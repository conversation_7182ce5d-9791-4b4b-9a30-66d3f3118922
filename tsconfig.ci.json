{"extends": "./tsconfig.json", "compilerOptions": {"skipLibCheck": true, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictPropertyInitialization": false, "strictBindCallApply": false, "noImplicitThis": false, "alwaysStrict": false}, "include": ["src"], "exclude": ["node_modules", "src/components/map/animation/AnimationManager.ts", "src/components/map/animation/CityDriveByManager.ts", "src/components/map/animation/ComponentInteractionManager.ts", "src/components/map/animation/POIDiscoveryManager.ts", "src/components/map/animation/VehicleManager.ts", "src/components/map/types/AnimationTypes.ts", "src/components/map/types/MapTypes.ts"]}