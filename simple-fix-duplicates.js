#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name using ES module syntax
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const filePath = path.join(process.cwd(), 'src/components/map/animation/VehicleManager.ts');
const backupPath = `${filePath}.backup`;

// Create a backup if it doesn't exist
if (!fs.existsSync(backupPath)) {
  console.log('Creating backup of VehicleManager.ts...');
  fs.copyFileSync(filePath, backupPath);
}

console.log('Reading VehicleManager.ts...');
let content = fs.readFileSync(filePath, 'utf8');

// Specific fix for the duplicate getVehicleDebugInfo method
console.log('Searching for duplicate getVehicleDebugInfo implementations...');

// These patterns will locate the private and public implementations
const privateVehicleDebugInfoPattern = /private\s+getVehicleDebugInfo\s*\(\s*\)\s*:\s*VehicleDebugInfo\s*{[\s\S]*?(?=\s+(?:private|public)\s+\w+)/g;
const publicVehicleDebugInfoPattern = /public\s+getVehicleDebugInfo\s*\(\s*\)\s*:\s*VehicleDebugInfo\s*{[\s\S]*?(?=\s+(?:private|public)\s+\w+)/g;

const privateMatches = content.match(privateVehicleDebugInfoPattern);
const publicMatches = content.match(publicVehicleDebugInfoPattern);

if (privateMatches && privateMatches.length > 0) {
  console.log(`Found ${privateMatches.length} private implementation(s) of getVehicleDebugInfo`);
  
  // Replace the private implementation with a comment
  content = content.replace(
    privateMatches[0],
    '  // NOTE: Removing the private implementation of getVehicleDebugInfo since it\'s duplicated in the public method\n\n  '
  );
}

if (publicMatches && publicMatches.length > 0) {
  console.log(`Found ${publicMatches.length} public implementation(s) of getVehicleDebugInfo`);
}

// Fix for duplicate isVehicleDOM
const privateVehicleDOMPattern = /private\s+isVehicleDOM\s*\(\s*[^)]*\)\s*:\s*[^{]*{[\s\S]*?(?=\s+(?:private|public)\s+\w+)/g;
const vehicleDOMMatches = content.match(privateVehicleDOMPattern);

if (vehicleDOMMatches && vehicleDOMMatches.length > 1) {
  console.log(`Found ${vehicleDOMMatches.length} implementations of isVehicleDOM, keeping only the first one`);
  
  // Keep the first implementation, replace others with comments
  for (let i = 1; i < vehicleDOMMatches.length; i++) {
    content = content.replace(
      vehicleDOMMatches[i],
      '  // NOTE: Removing duplicate implementation of isVehicleDOM\n\n  '
    );
  }
}

// Fix for duplicate isMapboxMarker
const privateMapboxMarkerPattern = /private\s+isMapboxMarker\s*\(\s*[^)]*\)\s*:\s*[^{]*{[\s\S]*?(?=\s+(?:private|public)\s+\w+)/g;
const mapboxMarkerMatches = content.match(privateMapboxMarkerPattern);

if (mapboxMarkerMatches && mapboxMarkerMatches.length > 1) {
  console.log(`Found ${mapboxMarkerMatches.length} implementations of isMapboxMarker, keeping only the first one`);
  
  // Keep the first implementation, replace others with comments
  for (let i = 1; i < mapboxMarkerMatches.length; i++) {
    content = content.replace(
      mapboxMarkerMatches[i],
      '  // NOTE: Removing duplicate implementation of isMapboxMarker\n\n  '
    );
  }
}

// Fix for the _recoveryTimeout property type
if (content.includes('private _recoveryTimeout:') && !content.includes('private _recoveryTimeout: NodeJS.Timeout | null')) {
  console.log('Fixing _recoveryTimeout property type...');
  content = content.replace(
    /private _recoveryTimeout:.*?=/,
    'private _recoveryTimeout: NodeJS.Timeout | null ='
  );
}

// Write the fixed content back to the file
console.log('Writing fixed content back to VehicleManager.ts...');
fs.writeFileSync(filePath, content);

console.log('Duplicates have been fixed in VehicleManager.ts!');
console.log('Run the TypeScript linter to check for remaining issues.'); 