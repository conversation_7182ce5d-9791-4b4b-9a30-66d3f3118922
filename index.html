<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Come to Morocco - Interactive Journey Planner</title>
    <link rel="manifest" href="/manifest.json">
    <meta name="description" content="Plan your perfect Morocco journey with interactive routes and points of interest" />
    <meta name="author" content="Come to Morocco" />

    <meta property="og:title" content="Come to Morocco - Interactive Journey Planner" />
    <meta property="og:description" content="Plan your perfect Morocco journey with interactive routes and points of interest" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@lovable_dev" />
    <meta name="twitter:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />
    
    <!-- Google Fonts for Moroccan Theme -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cinzel+Decorative:wght@400;700;900&family=Lato:wght@300;400;700&family=Marcellus&family=Scheherazade+New:wght@400;700&display=swap" rel="stylesheet">
    
    <!-- PWA Manifest and Theme Color -->
    <link rel="manifest" href="/manifest.webmanifest" />
    <meta name="theme-color" content="#8B1A18" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.svg" />
    <link rel="icon" sizes="192x192" href="/pwa-192x192.svg" />
    <link rel="icon" sizes="512x512" href="/pwa-512x512.svg" />
    
    <!-- Marker styles now handled by unified CSS system -->
  </head>

  <body>
    <div id="root"></div>
    <!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->
    <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>
    <script type="module" src="/src/main.tsx"></script>
    <!-- Service Worker Registration - Commented out for development -->
    <!--
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then(registration => {
              console.log('Service Worker registered with scope:', registration.scope);
            })
            .catch(error => {
              console.error('Service Worker registration failed:', error);
            });
        });
      }
    </script>
    -->
  </body>
</html>
