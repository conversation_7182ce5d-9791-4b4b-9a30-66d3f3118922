-- Initial database schema for Come to Morocco travel platform
-- This migration creates the core tables for multi-client travel platform

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- Clients table - for multi-tenant architecture
CREATE TABLE clients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    theme_config JSONB DEFAULT '{}',
    branding_config JSONB DEFAULT '{}',
    map_config JSONB DEFAULT '{}',
    contact_info JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Destinations table
CREATE TABLE destinations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    coordinates POINT NOT NULL, -- PostGIS point for lat/lng
    region VARCHAR(100),
    country VARCHAR(100) DEFAULT 'Morocco',
    tags TEXT[] DEFAULT '{}',
    images TEXT[] DEFAULT '{}',
    weather_info JSONB DEFAULT '{}',
    best_time_to_visit TEXT[],
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- POIs (Points of Interest) table
CREATE TABLE pois (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    destination_id UUID REFERENCES destinations(id) ON DELETE SET NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL,
    coordinates POINT NOT NULL,
    location VARCHAR(255),
    region VARCHAR(100),
    tags TEXT[] DEFAULT '{}',
    images TEXT[] DEFAULT '{}',
    price_range VARCHAR(50), -- 'free', 'budget', 'moderate', 'expensive', 'luxury'
    duration_minutes INTEGER DEFAULT 60,
    rating DECIMAL(3,2) DEFAULT 0,
    review_count INTEGER DEFAULT 0,
    opening_hours JSONB DEFAULT '{}',
    contact_info JSONB DEFAULT '{}',
    accessibility_info JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Journey templates table
CREATE TABLE journeys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    user_id UUID, -- Will reference auth.users when auth is set up
    name VARCHAR(255) NOT NULL,
    description TEXT,
    destinations UUID[] DEFAULT '{}', -- Array of destination IDs
    pois UUID[] DEFAULT '{}', -- Array of POI IDs
    duration_days INTEGER NOT NULL,
    total_distance_km DECIMAL(10,2),
    estimated_cost DECIMAL(10,2),
    difficulty_level VARCHAR(50) DEFAULT 'moderate', -- 'easy', 'moderate', 'challenging', 'expert'
    best_season TEXT[] DEFAULT '{}',
    tags TEXT[] DEFAULT '{}',
    route_data JSONB DEFAULT '{}', -- Store route coordinates and waypoints
    is_template BOOLEAN DEFAULT false,
    is_featured BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Detailed itineraries table
CREATE TABLE itineraries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    journey_id UUID REFERENCES journeys(id) ON DELETE CASCADE,
    user_id UUID, -- Will reference auth.users when auth is set up
    day_number INTEGER NOT NULL,
    destination_id UUID REFERENCES destinations(id),
    pois UUID[] DEFAULT '{}', -- Array of POI IDs for this day
    activities TEXT[] DEFAULT '{}',
    accommodation JSONB DEFAULT '{}',
    transportation JSONB DEFAULT '{}',
    meals JSONB DEFAULT '{}',
    notes TEXT,
    estimated_cost DECIMAL(10,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(journey_id, day_number)
);

-- Users table (extends Supabase auth.users)
CREATE TABLE users (
    id UUID PRIMARY KEY, -- References auth.users.id
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255),
    avatar_url TEXT,
    preferences JSONB DEFAULT '{}',
    travel_interests TEXT[] DEFAULT '{}',
    emergency_contact JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Bookings table
CREATE TABLE bookings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    journey_id UUID REFERENCES journeys(id),
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'confirmed', 'cancelled', 'completed'
    booking_date DATE NOT NULL,
    travel_date DATE NOT NULL,
    guest_count INTEGER DEFAULT 1,
    total_amount DECIMAL(10,2),
    payment_status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'paid', 'refunded'
    special_requests TEXT,
    contact_info JSONB NOT NULL,
    booking_reference VARCHAR(50) UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Reviews table
CREATE TABLE reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    poi_id UUID REFERENCES pois(id) ON DELETE CASCADE,
    journey_id UUID REFERENCES journeys(id) ON DELETE CASCADE,
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title VARCHAR(255),
    content TEXT NOT NULL,
    images TEXT[] DEFAULT '{}',
    is_verified BOOLEAN DEFAULT false,
    is_featured BOOLEAN DEFAULT false,
    helpful_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CHECK (poi_id IS NOT NULL OR journey_id IS NOT NULL)
);

-- Create indexes for better performance
CREATE INDEX idx_destinations_client_id ON destinations(client_id);
CREATE INDEX idx_destinations_coordinates ON destinations USING GIST(coordinates);
CREATE INDEX idx_pois_client_id ON pois(client_id);
CREATE INDEX idx_pois_destination_id ON pois(destination_id);
CREATE INDEX idx_pois_coordinates ON pois USING GIST(coordinates);
CREATE INDEX idx_pois_category ON pois(category);
CREATE INDEX idx_journeys_client_id ON journeys(client_id);
CREATE INDEX idx_journeys_is_template ON journeys(is_template);
CREATE INDEX idx_itineraries_journey_id ON itineraries(journey_id);
CREATE INDEX idx_bookings_user_id ON bookings(user_id);
CREATE INDEX idx_bookings_client_id ON bookings(client_id);
CREATE INDEX idx_reviews_poi_id ON reviews(poi_id);
CREATE INDEX idx_reviews_journey_id ON reviews(journey_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to all tables
CREATE TRIGGER update_clients_updated_at BEFORE UPDATE ON clients FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_destinations_updated_at BEFORE UPDATE ON destinations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_pois_updated_at BEFORE UPDATE ON pois FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_journeys_updated_at BEFORE UPDATE ON journeys FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_itineraries_updated_at BEFORE UPDATE ON itineraries FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_bookings_updated_at BEFORE UPDATE ON bookings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_reviews_updated_at BEFORE UPDATE ON reviews FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE destinations ENABLE ROW LEVEL SECURITY;
ALTER TABLE pois ENABLE ROW LEVEL SECURITY;
ALTER TABLE journeys ENABLE ROW LEVEL SECURITY;
ALTER TABLE itineraries ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies (will be expanded in future migrations)
CREATE POLICY "Public read access for active clients" ON clients FOR SELECT USING (is_active = true);
CREATE POLICY "Public read access for active destinations" ON destinations FOR SELECT USING (is_active = true);
CREATE POLICY "Public read access for active pois" ON pois FOR SELECT USING (is_active = true);
CREATE POLICY "Public read access for active journeys" ON journeys FOR SELECT USING (is_active = true);
CREATE POLICY "Public read access for verified reviews" ON reviews FOR SELECT USING (is_verified = true);
