-- Fixed seed data with proper UUIDs for Come to Morocco travel platform

-- Insert clients for different regions
INSERT INTO clients (id, name, slug, theme_config, branding_config, map_config) VALUES 
(
    'a1b2c3d4-e5f6-7890-abcd-ef1234567890'::uuid,
    'Morocco Travel',
    'morocco',
    '{"primaryColor": "#8B1A1A", "secondaryColor": "#0047AB", "accentColor": "#D1C4A9", "backgroundColor": "#F8F6F0"}',
    '{"logo": "/images/morocco-logo.png", "companyName": "Come to Morocco", "tagline": "Discover the Magic of Morocco"}',
    '{"center": [-6.8498, 33.9716], "zoom": 6, "bounds": [[-15, 25], [2, 37]], "style": "mapbox://styles/mapbox/outdoors-v12"}'
),
(
    'b2c3d4e5-f6a7-8901-bcde-f23456789012'::uuid,
    'Portugal Discoveries',
    'portugal',
    '{"primaryColor": "#006600", "secondaryColor": "#FF0000", "accentColor": "#FFD700", "backgroundColor": "#F0F8F0"}',
    '{"logo": "/images/portugal-logo.png", "companyName": "Portugal Discoveries", "tagline": "Explore the Soul of Portugal"}',
    '{"center": [-8.61, 39.6], "zoom": 6, "bounds": [[-10, 36], [-6, 42]], "style": "mapbox://styles/mapbox/outdoors-v12"}'
),
(
    'c3d4e5f6-a7b8-9012-cdef-************'::uuid,
    'Global Framework',
    'global',
    '{"primaryColor": "#2563EB", "secondaryColor": "#7C3AED", "accentColor": "#F59E0B", "backgroundColor": "#F8FAFC"}',
    '{"logo": "/images/global-logo.png", "companyName": "Global Travel Framework", "tagline": "Discover the World"}',
    '{"center": [0, 20], "zoom": 2, "bounds": [[-180, -85], [180, 85]], "style": "mapbox://styles/mapbox/outdoors-v12"}'
),
(
    'd4e5f6a7-b8c9-0123-def4-56789abcdef0'::uuid,
    'Neutral Framework',
    'neutral',
    '{"primaryColor": "#6B7280", "secondaryColor": "#9CA3AF", "accentColor": "#F59E0B", "backgroundColor": "#F9FAFB"}',
    '{"logo": "/images/neutral-logo.png", "companyName": "Neutral Travel Framework", "tagline": "Explore Anywhere"}',
    '{"center": [0, 20], "zoom": 2, "bounds": [[-180, -85], [180, 85]], "style": "mapbox://styles/mapbox/outdoors-v12"}'
);

-- Insert Morocco destinations with UUID IDs
INSERT INTO destinations (id, client_id, name, description, coordinates, region, tags, images, weather_info, best_time_to_visit) VALUES 
(
    '11111111-1111-1111-1111-111111111111'::uuid,
    'a1b2c3d4-e5f6-7890-abcd-ef1234567890'::uuid,
    'Marrakech',
    'The Red City known for its vibrant souks, historic medina, and stunning palaces. A UNESCO World Heritage site with incredible architecture and bustling markets.',
    POINT(-7.9811, 31.6295),
    'Marrakech-Safi',
    ARRAY['historic', 'cultural', 'shopping', 'architecture', 'unesco', 'imperial-city'],
    ARRAY['/images/marrakech-1.jpg', '/images/marrakech-2.jpg', '/images/marrakech-medina.jpg'],
    '{"averageTemp": {"winter": "18°C", "summer": "35°C"}, "rainfall": "low", "climate": "semi-arid"}',
    ARRAY['October', 'November', 'December', 'January', 'February', 'March', 'April']
),
(
    '*************-2222-2222-************'::uuid,
    'a1b2c3d4-e5f6-7890-abcd-ef1234567890'::uuid,
    'Casablanca',
    'Morocco''s largest city and economic center with modern architecture and the iconic Hassan II Mosque.',
    POINT(-7.5898, 33.5731),
    'Casablanca-Settat',
    ARRAY['modern', 'business', 'coastal', 'architecture', 'urban', 'mosque'],
    ARRAY['/images/casablanca-1.jpg', '/images/casablanca-2.jpg', '/images/hassan-ii-mosque.jpg'],
    '{"averageTemp": {"winter": "16°C", "summer": "26°C"}, "rainfall": "moderate", "climate": "mediterranean"}',
    ARRAY['April', 'May', 'June', 'September', 'October', 'November']
),
(
    '*************-3333-3333-************'::uuid,
    'a1b2c3d4-e5f6-7890-abcd-ef1234567890'::uuid,
    'Fez',
    'The spiritual and cultural heart of Morocco, home to the world''s oldest continuously operating university.',
    POINT(-5.0003, 34.0181),
    'Fès-Meknès',
    ARRAY['historic', 'cultural', 'spiritual', 'education', 'unesco', 'imperial-city', 'medieval'],
    ARRAY['/images/fez-1.jpg', '/images/fez-2.jpg', '/images/fez-medina.jpg'],
    '{"averageTemp": {"winter": "15°C", "summer": "35°C"}, "rainfall": "moderate", "climate": "mediterranean"}',
    ARRAY['March', 'April', 'May', 'October', 'November']
);

-- Insert sample POIs with UUID IDs
INSERT INTO pois (id, client_id, destination_id, name, description, category, coordinates, location, region, tags, price_range, duration_minutes, rating, opening_hours, contact_info) VALUES
(
    'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'::uuid,
    'a1b2c3d4-e5f6-7890-abcd-ef1234567890'::uuid,
    '11111111-1111-1111-1111-111111111111'::uuid,
    'Jardin Majorelle',
    'Beautiful botanical garden with exotic plants and the iconic blue villa, once owned by Yves Saint Laurent.',
    'Garden',
    POINT(-7.9826, 31.6417),
    'Gueliz, Marrakech',
    'Marrakech-Safi',
    ARRAY['garden', 'art', 'ysl', 'photography', 'peaceful'],
    'moderate',
    90,
    4.5,
    '{"daily": "8:00-18:00"}',
    '{"phone": "+212 524 301 852", "website": "https://jardinmajorelle.com"}'
),
(
    'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb'::uuid,
    'a1b2c3d4-e5f6-7890-abcd-ef1234567890'::uuid,
    '*************-2222-2222-************'::uuid,
    'Hassan II Mosque',
    'One of the largest mosques in the world, featuring stunning architecture and oceanfront location.',
    'Religious',
    POINT(-7.6327, 33.6084),
    'Casablanca Corniche',
    'Casablanca-Settat',
    ARRAY['mosque', 'architecture', 'religious', 'ocean', 'landmark'],
    'budget',
    120,
    4.7,
    '{"daily": "9:00-17:00", "closed": "Friday mornings"}',
    '{"phone": "+212 522 482 886"}'
);

-- Insert sample journeys with UUID IDs
INSERT INTO journeys (id, client_id, name, description, duration_days, difficulty_level, destinations, best_season, tags, is_template, is_featured) VALUES
(
    'aaaabbbb-cccc-dddd-eeee-ffffffffffff'::uuid,
    'a1b2c3d4-e5f6-7890-abcd-ef1234567890'::uuid,
    'Imperial Cities Classic',
    'Experience Morocco''s historic imperial cities with their rich culture and impressive architecture.',
    7,
    'moderate',
    ARRAY['11111111-1111-1111-1111-111111111111'::uuid, '*************-3333-3333-************'::uuid],
    ARRAY['October', 'November', 'December', 'January', 'February', 'March', 'April'],
    ARRAY['imperial-cities', 'cultural', 'historic', 'classic'],
    true,
    true
);

-- Insert sample reviews with UUID IDs
INSERT INTO reviews (id, user_id, poi_id, client_id, rating, title, content, is_verified, is_featured, helpful_count) VALUES
(
    'ddddeeee-ffff-aaaa-bbbb-cccccccccccc'::uuid,
    NULL,
    'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'::uuid,
    'a1b2c3d4-e5f6-7890-abcd-ef1234567890'::uuid,
    5,
    'Absolutely Stunning Garden',
    'The Jardin Majorelle is a true oasis in Marrakech. The vibrant blue colors and exotic plants create a magical atmosphere.',
    true,
    true,
    23
);
