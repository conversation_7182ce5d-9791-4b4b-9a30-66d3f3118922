-- Migration: Add Neutral Client
-- Description: Add neutral client for enhanced-neutral-demo page

-- Add neutral client if it doesn't exist
INSERT INTO clients (id, name, slug, theme_config, branding_config, map_config) VALUES 
(
    'd4e5f6a7-b8c9-0123-def4-56789abcdef0'::uuid,
    'Neutral Framework',
    'neutral',
    '{"primaryColor": "#6B7280", "secondaryColor": "#9CA3AF", "accentColor": "#F59E0B", "backgroundColor": "#F9FAFB"}',
    '{"logo": "/images/neutral-logo.png", "companyName": "Neutral Travel Framework", "tagline": "Explore Anywhere"}',
    '{"center": [0, 20], "zoom": 2, "bounds": [[-180, -85], [180, 85]], "style": "mapbox://styles/mapbox/outdoors-v12"}'
)
ON CONFLICT (id) DO NOTHING;

-- Add superadmin client that the app sometimes looks for
INSERT INTO clients (id, name, slug, theme_config, branding_config, map_config) VALUES 
(
    'e5f6a7b8-c9d0-1234-ef56-789abcdef012'::uuid,
    'Super Admin Framework',
    'superadmin',
    '{"primaryColor": "#1F2937", "secondaryColor": "#374151", "accentColor": "#F59E0B", "backgroundColor": "#F9FAFB"}',
    '{"logo": "/images/superadmin-logo.png", "companyName": "Super Admin Portal", "tagline": "Manage Everything"}',
    '{"center": [0, 20], "zoom": 2, "bounds": [[-180, -85], [180, 85]], "style": "mapbox://styles/mapbox/outdoors-v12"}'
)
ON CONFLICT (id) DO NOTHING;
