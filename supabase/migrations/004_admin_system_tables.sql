-- =====================================================
-- Migration: 004_admin_system_tables.sql
-- Description: Add missing tables for admin system
-- Date: January 2025
-- =====================================================

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- 1. USER ROLES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS user_roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    role VARCHAR(50) NOT NULL CHECK (role IN ('super_admin', 'client_admin', 'user')),
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    permissions JSONB DEFAULT '[]'::jsonb,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID,
    
    -- Constraints
    UNIQUE(user_id, client_id),
    
    -- Super admins don't need client_id, client_admins must have client_id
    CONSTRAINT role_client_constraint CHECK (
        (role = 'super_admin' AND client_id IS NULL) OR
        (role = 'client_admin' AND client_id IS NOT NULL) OR
        (role = 'user')
    )
);

-- Add indexes for performance
CREATE INDEX idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX idx_user_roles_client_id ON user_roles(client_id);
CREATE INDEX idx_user_roles_role ON user_roles(role);

-- =====================================================
-- 2. CLIENT THEMES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS client_themes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    theme_name VARCHAR(100) NOT NULL,
    
    -- Color Configuration
    primary_color VARCHAR(7) NOT NULL DEFAULT '#3B82F6',
    secondary_color VARCHAR(7) NOT NULL DEFAULT '#6B7280',
    accent_color VARCHAR(7) NOT NULL DEFAULT '#F59E0B',
    background_color VARCHAR(7) NOT NULL DEFAULT '#FFFFFF',
    text_color VARCHAR(7) NOT NULL DEFAULT '#1F2937',
    border_color VARCHAR(7) NOT NULL DEFAULT '#E5E7EB',
    success_color VARCHAR(7) NOT NULL DEFAULT '#10B981',
    warning_color VARCHAR(7) NOT NULL DEFAULT '#F59E0B',
    error_color VARCHAR(7) NOT NULL DEFAULT '#EF4444',
    
    -- Typography
    font_family VARCHAR(100) DEFAULT 'Inter, sans-serif',
    heading_font VARCHAR(100) DEFAULT 'Inter, sans-serif',
    
    -- Layout Configuration
    header_style VARCHAR(20) DEFAULT 'standard' CHECK (header_style IN ('minimal', 'standard', 'prominent')),
    footer_style VARCHAR(20) DEFAULT 'standard' CHECK (footer_style IN ('minimal', 'standard', 'detailed')),
    
    -- Custom CSS
    custom_css TEXT,
    
    -- Metadata
    is_active BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID,
    
    -- Constraints
    UNIQUE(client_id, theme_name),
    
    -- Only one default theme per client
    EXCLUDE (client_id WITH =) WHERE (is_default = true)
);

-- Add indexes
CREATE INDEX idx_client_themes_client_id ON client_themes(client_id);
CREATE INDEX idx_client_themes_active ON client_themes(client_id, is_active);

-- =====================================================
-- 3. CLIENT SETTINGS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS client_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    
    -- Feature Toggles
    enable_ai BOOLEAN DEFAULT true,
    enable_animations BOOLEAN DEFAULT true,
    enable_poi_discovery BOOLEAN DEFAULT true,
    enable_quote_requests BOOLEAN DEFAULT true,
    enable_user_registration BOOLEAN DEFAULT false,
    
    -- Map Configuration
    default_map_center POINT,
    default_map_zoom DECIMAL(4,2) DEFAULT 6.0,
    default_map_bounds JSONB,
    
    -- Content Settings
    max_destinations INTEGER DEFAULT 10,
    max_pois_per_destination INTEGER DEFAULT 50,
    max_journey_duration INTEGER DEFAULT 30, -- days
    
    -- Branding Settings
    company_name VARCHAR(255),
    company_tagline VARCHAR(500),
    logo_url TEXT,
    favicon_url TEXT,
    
    -- Contact Information
    contact_email VARCHAR(255),
    contact_phone VARCHAR(50),
    contact_address TEXT,
    website_url TEXT,
    
    -- Social Media
    social_media JSONB DEFAULT '{}'::jsonb,
    
    -- Analytics
    google_analytics_id VARCHAR(50),
    facebook_pixel_id VARCHAR(50),
    
    -- API Configuration
    api_settings JSONB DEFAULT '{}'::jsonb,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID,
    
    -- Constraints
    UNIQUE(client_id)
);

-- Add indexes
CREATE INDEX idx_client_settings_client_id ON client_settings(client_id);

-- =====================================================
-- 4. ADMIN PERMISSIONS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS admin_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    permission_name VARCHAR(100) NOT NULL UNIQUE,
    permission_description TEXT,
    permission_category VARCHAR(50) NOT NULL,
    is_system_permission BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default permissions
INSERT INTO admin_permissions (permission_name, permission_description, permission_category, is_system_permission) VALUES
-- Client Management
('clients.view', 'View client information', 'client_management', true),
('clients.create', 'Create new clients', 'client_management', true),
('clients.edit', 'Edit client information', 'client_management', true),
('clients.delete', 'Delete clients', 'client_management', true),

-- Content Management
('content.view', 'View content (destinations, POIs, journeys)', 'content_management', true),
('content.create', 'Create new content', 'content_management', true),
('content.edit', 'Edit existing content', 'content_management', true),
('content.delete', 'Delete content', 'content_management', true),

-- Theme Management
('themes.view', 'View theme configurations', 'theme_management', true),
('themes.create', 'Create new themes', 'theme_management', true),
('themes.edit', 'Edit theme configurations', 'theme_management', true),
('themes.delete', 'Delete themes', 'theme_management', true),

-- User Management
('users.view', 'View user information', 'user_management', true),
('users.create', 'Create new users', 'user_management', true),
('users.edit', 'Edit user information', 'user_management', true),
('users.delete', 'Delete users', 'user_management', true),
('users.assign_roles', 'Assign roles to users', 'user_management', true),

-- System Administration
('system.view_analytics', 'View system analytics', 'system_admin', true),
('system.manage_settings', 'Manage system settings', 'system_admin', true),
('system.view_logs', 'View system logs', 'system_admin', true),
('system.backup_restore', 'Backup and restore data', 'system_admin', true),

-- Quote Management
('quotes.view', 'View quote requests', 'quote_management', true),
('quotes.respond', 'Respond to quote requests', 'quote_management', true),
('quotes.delete', 'Delete quote requests', 'quote_management', true);

-- =====================================================
-- 5. ROLE PERMISSIONS JUNCTION TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS role_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_role_id UUID NOT NULL REFERENCES user_roles(id) ON DELETE CASCADE,
    permission_id UUID NOT NULL REFERENCES admin_permissions(id) ON DELETE CASCADE,
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    granted_by UUID,
    
    -- Constraints
    UNIQUE(user_role_id, permission_id)
);

-- Add indexes
CREATE INDEX idx_role_permissions_user_role ON role_permissions(user_role_id);
CREATE INDEX idx_role_permissions_permission ON role_permissions(permission_id);

-- =====================================================
-- 6. AUDIT LOG TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS admin_audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID,
    client_id UUID REFERENCES clients(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for audit log
CREATE INDEX idx_audit_log_user_id ON admin_audit_log(user_id);
CREATE INDEX idx_audit_log_client_id ON admin_audit_log(client_id);
CREATE INDEX idx_audit_log_action ON admin_audit_log(action);
CREATE INDEX idx_audit_log_created_at ON admin_audit_log(created_at);

-- =====================================================
-- 7. UPDATE TRIGGERS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add update triggers
CREATE TRIGGER update_user_roles_updated_at BEFORE UPDATE ON user_roles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_client_themes_updated_at BEFORE UPDATE ON client_themes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_client_settings_updated_at BEFORE UPDATE ON client_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 8. ROW LEVEL SECURITY (RLS)
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE user_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE client_themes ENABLE ROW LEVEL SECURITY;
ALTER TABLE client_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE role_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_audit_log ENABLE ROW LEVEL SECURITY;

-- RLS Policies will be added in a separate migration for clarity

-- =====================================================
-- 9. SEED DEFAULT DATA
-- =====================================================

-- Insert default client settings for existing clients
INSERT INTO client_settings (client_id, company_name, company_tagline, enable_ai, enable_animations)
SELECT 
    id,
    name,
    CASE 
        WHEN slug = 'morocco' THEN 'Discover the Magic of Morocco'
        WHEN slug = 'portugal' THEN 'Explore the Soul of Portugal'
        WHEN slug = 'global' THEN 'Discover the World'
        WHEN slug = 'neutral' THEN 'Explore Anywhere'
        ELSE 'Your Travel Adventure Awaits'
    END,
    true,
    true
FROM clients
WHERE NOT EXISTS (SELECT 1 FROM client_settings WHERE client_settings.client_id = clients.id);

-- Insert default themes for existing clients
INSERT INTO client_themes (client_id, theme_name, primary_color, secondary_color, is_default)
SELECT 
    id,
    'Default Theme',
    CASE 
        WHEN slug = 'morocco' THEN '#8B1A1A'
        WHEN slug = 'portugal' THEN '#006600'
        WHEN slug = 'global' THEN '#2563EB'
        WHEN slug = 'neutral' THEN '#6B7280'
        ELSE '#3B82F6'
    END,
    CASE 
        WHEN slug = 'morocco' THEN '#0047AB'
        WHEN slug = 'portugal' THEN '#FF0000'
        WHEN slug = 'global' THEN '#7C3AED'
        WHEN slug = 'neutral' THEN '#9CA3AF'
        ELSE '#6B7280'
    END,
    true
FROM clients
WHERE NOT EXISTS (SELECT 1 FROM client_themes WHERE client_themes.client_id = clients.id);

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

COMMENT ON TABLE user_roles IS 'User role assignments with client-specific permissions';
COMMENT ON TABLE client_themes IS 'Theme configurations for each client';
COMMENT ON TABLE client_settings IS 'Client-specific settings and configurations';
COMMENT ON TABLE admin_permissions IS 'System permissions for role-based access control';
COMMENT ON TABLE role_permissions IS 'Junction table linking user roles to permissions';
COMMENT ON TABLE admin_audit_log IS 'Audit trail for admin actions';
