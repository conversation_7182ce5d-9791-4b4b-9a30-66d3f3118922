# Memory Bank for travelz.ai

## Purpose

This Memory Bank serves as the comprehensive documentation and context repository for the travelz.ai application. It follows Cursor's Memory Bank structure to maintain perfect documentation between development sessions, ensuring that all team members and AI assistants can quickly understand the project's purpose, architecture, and current state.

## Memory Bank Structure

The Memory Bank consists of required core files in Markdown format, building upon each other in a clear hierarchy:

```mermaid
flowchart TD
    PB[projectbrief.md] --> PC[productContext.md]
    PB --> SP[systemPatterns.md]
    PB --> TC[techContext.md]
    
    PC --> AC[activeContext.md]
    SP --> AC
    TC --> AC
    
    AC --> P[progress.md]
```

### Core Files

1. `projectbrief.md`
   - Foundation document that shapes all other files
   - Defines core requirements and goals
   - Source of truth for project scope

2. `productContext.md`
   - Why this project exists
   - Problems it solves
   - How it should work
   - User experience goals

3. `activeContext.md`
   - Current work focus
   - Recent changes
   - Next steps
   - Active decisions and considerations

4. `systemPatterns.md`
   - System architecture
   - Key technical decisions
   - Design patterns in use
   - Component relationships

5. `techContext.md`
   - Technologies used
   - Development setup
   - Technical constraints
   - Dependencies

6. `progress.md`
   - What works
   - What's left to build
   - Current status
   - Known issues

## Usage Guidelines

### When to Update

Memory Bank updates should occur when:
1. Discovering new project patterns
2. After implementing significant changes
3. When requested with **update memory bank**
4. When context needs clarification

### How to Update

When updating the Memory Bank:
1. Review ALL files
2. Document the current state
3. Clarify next steps
4. Update .cursorrules if needed

### Reading Order

For new team members or when returning to the project after a break, read the files in this order:
1. projectbrief.md
2. productContext.md
3. systemPatterns.md
4. techContext.md
5. activeContext.md
6. progress.md

This ensures a logical progression from high-level understanding to current details.

## Maintenance Responsibility

All team members are responsible for keeping the Memory Bank updated. When making significant changes to the codebase, update the relevant Memory Bank files to reflect those changes.

Remember: The Memory Bank is the single source of truth for project context and documentation. Its accuracy and completeness are critical for effective development.