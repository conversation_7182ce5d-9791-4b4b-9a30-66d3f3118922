# travelz.ai - System Patterns

## System Architecture

The travelz.ai application follows a modular architecture with clear separation of concerns, particularly in its animation system. The application is built on React and uses Mapbox GL for map rendering.

### Core Architecture Principles

1. **Separation of Concerns**: Each component has a clearly defined responsibility and interface.
2. **Interface-Driven Design**: Components implement well-defined interfaces to ensure proper implementation.
3. **Event-Driven Communication**: Components communicate through a standardized event system.
4. **Dependency Injection**: Components support constructor injection for dependencies to facilitate testing.

## Key Technical Decisions

### Three-Tier Animation Architecture

The animation system follows a three-tier architecture:

1. **VehicleDOM**: Exclusively handles DOM operations and element manipulation
2. **VehicleManager**: Manages vehicle state and coordinates with VehicleDOM
3. **VehicleController**: Handles animations, smoothing, and user interactions

This separation allows for better performance, maintainability, and testability.

### Modular Map Components

The map system is divided into specialized modules:

- **Animation**: Handles route animation and vehicle movement
- **Markers**: Manages map markers for destinations and POIs
- **Layers**: Handles route line rendering and map styling
- **Overlays**: Manages UI overlays for the map
- **Controls**: Provides UI controls for map interaction

### Event System

The application uses a standardized event system with:

- Type-safe event handling with generics
- Consistent event flow up and down the component hierarchy
- Standardized event types through enums

## Design Patterns in Use

### Adapter Pattern

Used for integrating with third-party libraries like Mapbox GL to provide a consistent interface and insulate the application from changes in the library.

### Factory Pattern

Used for creating component instances with proper dependency injection, particularly in the animation system.

### Observer Pattern

Implemented through the event system to allow components to subscribe to and react to events without tight coupling.

### Strategy Pattern

Used for implementing different animation behaviors and camera controls that can be swapped at runtime.

## Component Relationships

### Animation System

```
AnimationManager
├── VehicleManager
│   └── VehicleDOM
├── RouteAnimator
├── POIDiscoveryManager
└── CameraBehavior
```

### Map Rendering System

```
MapInstance
├── LayerManager
│   └── RouteLayer
├── MarkerManager
│   ├── DestinationMarker
│   └── POIMarker
├── OverlayManager
│   ├── CityTagsOverlay
│   └── POINotification
└── ControlManager
    └── AnimationControls
```

This architecture provides a clear structure for the application while maintaining flexibility for future enhancements and extensions to other regions and clients.