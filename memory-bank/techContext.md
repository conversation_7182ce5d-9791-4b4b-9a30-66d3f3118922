# travelz.ai - Technical Context

## Technologies Used

### Frontend Framework
- **React**: Core UI framework for building component-based interfaces
- **TypeScript**: For type safety and improved developer experience

### Map Technology
- **Mapbox GL**: Primary mapping library for interactive maps
- **Custom Adapter Layer**: Wraps Mapbox GL to provide consistent interface and insulate from API changes

### Animation
- **Custom Animation System**: Built on requestAnimationFrame for smooth animations
- **DOM-based Vehicle Marker**: Custom implementation for vehicle animation

### State Management
- **React Context**: For sharing state between components
- **Custom Event System**: For component communication

### Styling
- **CSS Modules**: For component-scoped styling
- **Responsive Design**: For mobile and desktop compatibility

## Development Setup

### Required Tools
- Node.js and npm/yarn for package management
- Modern web browser with developer tools
- Code editor with TypeScript support

### Development Workflow
1. Clone the repository
2. Install dependencies with `npm install`
3. Start the development server with `npm start`
4. Access the application at `http://localhost:3000`

### Code Style Guidelines

- Follow TypeScript best practices for type safety
- Use interfaces to define component contracts
- Document public methods and interfaces with JSDoc comments
- Use consistent naming conventions
- Use `Date.now()` for timestamps instead of `new Date().toISOString()`
- Use the adapter pattern for third-party libraries

## Technical Constraints

### Performance Considerations
- Animation performance is critical for smooth user experience
- Mobile device compatibility requires optimization
- Map rendering performance affects overall application responsiveness

### Browser Compatibility
- Support for modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browser support for iOS and Android

### Map API Limitations
- Mapbox GL API version compatibility
- Rate limits for map tile loading
- Custom marker implementation constraints

## Dependencies

### Core Dependencies
- React and React DOM
- TypeScript
- Mapbox GL

### Development Dependencies
- ESLint for code linting
- Jest for unit testing
- Webpack for bundling

This technical context provides the foundation for understanding the technologies, setup, and constraints of the travelz.ai application.