import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name from the current URL
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Path to the file
const filePath = path.join(__dirname, 'src/components/map/animation/VehicleManager.ts');
const backupPath = `${filePath}.template-backup`;

// Create a backup of the original file if it doesn't exist
if (!fs.existsSync(backupPath)) {
  console.log(`Creating backup of VehicleManager.ts at ${backupPath}`);
  fs.copyFileSync(filePath, backupPath);
}

// Read the content of the file
console.log(`Reading ${filePath}`);
let content = fs.readFileSync(filePath, 'utf8');

// Fix template literals - convert all template literals to string concatenation
const fixTemplateLiterals = (content) => {
  // Regex pattern to match template literals
  const templatePattern = /`([^`]*?)\${([^}]*)}\s*([^`]*)`/g;
  
  // Replace template literals with string concatenation
  let fixedContent = content;
  let match;
  let numFixed = 0;
  
  // Keep replacing until no more matches are found
  while ((match = templatePattern.exec(content)) !== null) {
    const fullMatch = match[0];
    const before = match[1];
    const expression = match[2];
    const after = match[3];
    
    // Create the appropriate string concatenation
    let replacement = '';
    if (before.length === 0 && after.length === 0) {
      replacement = `(${expression})`;
    } else if (before.length === 0) {
      replacement = `(${expression}) + "${after}"`;
    } else if (after.length === 0) {
      replacement = `"${before}" + (${expression})`;
    } else {
      replacement = `"${before}" + (${expression}) + "${after}"`;
    }
    
    // Replace in the full content
    fixedContent = fixedContent.replace(fullMatch, replacement);
    numFixed++;
    
    // Reset the regex pattern to search from the beginning again
    templatePattern.lastIndex = 0;
  }
  
  console.log(`Fixed ${numFixed} template literals`);
  
  // Remove extra semicolons (replace double semicolons with single)
  fixedContent = fixedContent.replace(/;;/g, ';');
  
  return fixedContent;
};

// Fix template literals
const fixedContent = fixTemplateLiterals(content);

// Fix specific syntax issues with console.log
const fixConsoleLogs = (content) => {
  // Common pattern for console logs with date
  const dateLogPattern = /console\.(log|error|warn|info|debug)\(`(.*?)\$\{Date\.now\(\)\}(.*?)`(.*?)\)/g;
  let fixedContent = content;
  let numFixed = 0;
  
  // Replace console logs with fixed syntax
  fixedContent = fixedContent.replace(dateLogPattern, (match, method, prefix, suffix, params) => {
    numFixed++;
    if (params && params.trim().length > 0) {
      return `console.${method}("${prefix}" + Date.now() + "${suffix}"${params})`;
    } else {
      return `console.${method}("${prefix}" + Date.now() + "${suffix}")`;
    }
  });
  
  console.log(`Fixed ${numFixed} console logs with Date.now()`);
  return fixedContent;
};

// Apply console log fixes
const finalContent = fixConsoleLogs(fixedContent);

// Write the fixed content back to the file
console.log(`Writing fixed content back to ${filePath}`);
fs.writeFileSync(filePath, finalContent);
console.log(`Template literals fixed in VehicleManager.ts`);
console.log(`Run TypeScript linter to check for any remaining issues`); 