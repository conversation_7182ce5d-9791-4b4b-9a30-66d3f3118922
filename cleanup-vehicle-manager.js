#!/usr/bin/env node

/**
 * VehicleManager.ts Cleanup Script
 * 
 * This script helps clean up the VehicleManager.ts file to remove duplicate
 * method implementations and fix common type issues.
 * 
 * Usage:
 * 1. Make sure you have a backup of VehicleManager.ts
 * 2. Run: node cleanup-vehicle-manager.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name using ES module syntax
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const vehicleManagerPath = path.join(__dirname, 'src', 'components', 'map', 'animation', 'VehicleManager.ts');
const backupPath = `${vehicleManagerPath}.backup`;

// Create a backup if it doesn't exist
if (!fs.existsSync(backupPath)) {
  console.log('Creating backup of VehicleManager.ts...');
  fs.copyFileSync(vehicleManagerPath, backupPath);
}

console.log('Reading VehicleManager.ts...');
let content = fs.readFileSync(vehicleManagerPath, 'utf8');

// Function to find the implementation of a method in a class
function findMethodImplementation(content, methodName, isPrivate = false) {
  const regex = new RegExp(`(\\s+${isPrivate ? 'private' : 'public'}\\s+${methodName}\\s*\\([^\\)]*\\)[^{]*{[\\s\\S]*?\\n\\s+})(?=\\s+(?:private|public)\\s+\\w+|})`, 'g');
  const matches = [...content.matchAll(regex)];
  return matches.map(match => match[0]);
}

// Find duplicate method implementations
const methodsToCheck = [
  { name: 'isVehicleDOM', isPrivate: true },
  { name: 'isMapboxMarker', isPrivate: true },
  { name: 'getVehicleDebugInfo', isPrivate: false },
  { name: 'isHTMLElement', isPrivate: true },
  { name: 'getVehicleMarkerElement', isPrivate: false }
];

for (const method of methodsToCheck) {
  const implementations = findMethodImplementation(content, method.name, method.isPrivate);
  if (implementations.length > 1) {
    console.log(`Found ${implementations.length} implementations of ${method.isPrivate ? 'private' : 'public'} ${method.name}`);
    
    // Keep only the first implementation
    for (let i = 1; i < implementations.length; i++) {
      const implementation = implementations[i];
      content = content.replace(implementation, `\n  // Removed duplicate implementation of ${method.name}\n  `);
    }
  }
}

// Function to fix common syntax issues
function fixSyntaxIssues(content) {
  // Fix missing semicolons at the end of lines
  content = content.replace(/(\w+)\s*$/gm, '$1;');
  
  // Fix missing closing braces for methods
  content = content.replace(/(\s+(?:private|public)\s+\w+\s*\([^)]*\)[^{]*{[\s\S]*?)(?=\s+(?:private|public)\s+\w+)/g, 
    (match, p1) => {
      if (!p1.trim().endsWith('}')) {
        return p1 + '\n  }\n';
      }
      return match;
    });
  
  return content;
}

// Clean up duplicated class closing brackets
content = content.replace(/}\s*}\s*$/g, '}\n');

// Write the fixed content back to the file
console.log('Writing fixed content back to VehicleManager.ts...');
fs.writeFileSync(vehicleManagerPath, content);

console.log('VehicleManager.ts has been fixed.'); 