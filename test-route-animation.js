/**
 * test-route-animation.js
 * 
 * A simple utility script to test the route animation functionality using
 * hardcoded test routes and configurations.
 * 
 * Run this with: node test-route-animation.js
 */

// Sample route coordinates for testing (Casablanca to Marrakech)
const testRoute = [
  // Casablanca
  [-7.5898, 33.9716],
  [-7.5900, 33.9000],
  [-7.6200, 33.8500],
  [-7.6500, 33.7800],
  [-7.7000, 33.7000],
  // Route points
  [-7.7500, 33.6000],
  [-7.8000, 33.4000],
  [-7.8500, 33.2000],
  [-7.9000, 32.8000],
  [-7.9500, 32.6000],
  [-7.9700, 32.4000],
  [-7.9800, 32.2000],
  // Marrakech
  [-7.9731, 31.6295]
];

// Sample destinations
const testDestinations = [
  { name: "Casablanca", coordinates: [-7.5898, 33.9716] },
  { name: "Marrakech", coordinates: [-7.9731, 31.6295] },
];

console.log('=========================================');
console.log('Route Animation Test Utility');
console.log('=========================================');
console.log('Test route loaded with', testRoute.length, 'points:');
console.log('Starting point:', testRoute[0]);
console.log('Ending point:', testRoute[testRoute.length - 1]);
console.log('');
console.log('This is a simple test utility that:');
console.log('1. Verifies routes are properly formatted');
console.log('2. Validates destination coordinates');
console.log('3. Generates a test route for animation');
console.log('');
console.log('To test the actual animation:');
console.log('1. Open your React app');
console.log('2. Use these coordinates for testing:');
console.log(JSON.stringify(testRoute));
console.log('');
console.log('3. Check browser console for animation-related logs');
console.log('=========================================');

// Calculate the total distance of the route
let totalDistance = 0;
for (let i = 0; i < testRoute.length - 1; i++) {
  const [lon1, lat1] = testRoute[i];
  const [lon2, lat2] = testRoute[i + 1];
  
  // Simple Haversine formula for distance calculation
  const R = 6371; // Radius of the earth in km
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c; // Distance in km
  
  totalDistance += distance;
}

console.log('Calculated route distance:', totalDistance.toFixed(2), 'km');
console.log('Estimated animation duration:', (totalDistance * 100).toFixed(0), 'ms');
console.log('=========================================');

// Execute simple validation of the route structure
function validateRoute(route) {
  if (!Array.isArray(route)) {
    console.error('❌ Route is not an array');
    return false;
  }
  
  if (route.length < 2) {
    console.error('❌ Route needs at least 2 points');
    return false;
  }
  
  for (const point of route) {
    if (!Array.isArray(point) || point.length !== 2) {
      console.error('❌ Route point is not a valid [lng, lat] array:', point);
      return false;
    }
    
    const [lng, lat] = point;
    if (typeof lng !== 'number' || typeof lat !== 'number') {
      console.error('❌ Route coordinates are not numbers:', point);
      return false;
    }
    
    if (lng < -180 || lng > 180 || lat < -90 || lat > 90) {
      console.error('❌ Route coordinates out of range:', point);
      return false;
    }
  }
  
  console.log('✅ Route validation successful');
  return true;
}

// Run the validation
validateRoute(testRoute);
console.log('=========================================');

// Export the test data so it can be imported in other files if needed
module.exports = {
  testRoute,
  testDestinations
}; 