#!/usr/bin/env node

/**
 * Fix Console Logs and Emoji Issues in VehicleManager.ts
 * 
 * This script specifically targets and fixes:
 * 1. Console log statements with template literals
 * 2. Emoji characters in string literals
 * 3. Extra semicolons
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Path to the file
const filePath = path.join(process.cwd(), 'src/components/map/animation/VehicleManager.ts');
const backupPath = `${filePath}.console-backup`;

// Create a backup of the original file
if (!fs.existsSync(backupPath)) {
  console.log(`Creating backup of VehicleManager.ts at ${backupPath}`);
  fs.copyFileSync(filePath, backupPath);
}

// Read the content of the file
console.log(`Reading ${filePath}`);
let content = fs.readFileSync(filePath, 'utf8');

// Replace all emojis with safe characters
const replaceEmojis = (text) => {
  // Map of emojis to replace with text equivalents
  const emojiReplacements = {
    '🔄': '[REFRESH]',
    '👁️': '[OBSERVER]',
    '❌': '[ERROR]',
    '✅': '[SUCCESS]',
    '🔧': '[TOOL]',
    '🧹': '[CLEANUP]',
    '⚠️': '[WARNING]',
    '🔍': '[SEARCH]'
  };

  // Replace each emoji in the text
  let result = text;
  for (const [emoji, replacement] of Object.entries(emojiReplacements)) {
    result = result.replace(new RegExp(emoji, 'g'), replacement);
  }

  return result;
};

// Fix console log statements with template literals
const fixConsoleLogs = (content) => {
  // Pattern to match console log statements with template literals
  const consolePattern = /console\.(log|error|warn|info|debug)\(`(.*?)(?:\${([^}]*)})(.*?)`(.*?)\)/g;
  
  let result = content;
  let match;
  let count = 0;
  
  // Replace each matched console log statement
  while ((match = consolePattern.exec(content)) !== null) {
    const [fullMatch, method, prefix, expr, suffix, params] = match;
    
    // Create the replacement with string concatenation
    let replacement;
    if (params && params.trim()) {
      replacement = `console.${method}("${replaceEmojis(prefix)}" + ${expr} + "${replaceEmojis(suffix)}"${params})`;
    } else {
      replacement = `console.${method}("${replaceEmojis(prefix)}" + ${expr} + "${replaceEmojis(suffix)}")`;
    }
    
    // Replace the full match in the result
    result = result.replace(fullMatch, replacement);
    count++;
    
    // Reset the regex to search from the beginning
    consolePattern.lastIndex = 0;
  }
  
  console.log(`Fixed ${count} console log statements`);
  return result;
};

// Fix debug log statements with template literals
const fixDebugLogs = (content) => {
  // Pattern to match debug log statements with template literals
  const debugPattern = /this\.debugLog\(['"](\w+)['"],\s*`(.*?)(?:\${([^}]*)})(.*?)`(.*?)\)/g;
  
  let result = content;
  let match;
  let count = 0;
  
  // Replace each matched debug log statement
  while ((match = debugPattern.exec(content)) !== null) {
    const [fullMatch, level, prefix, expr, suffix, params] = match;
    
    // Create the replacement with string concatenation
    let replacement;
    if (params && params.trim()) {
      replacement = `this.debugLog('${level}', "${replaceEmojis(prefix)}" + ${expr} + "${replaceEmojis(suffix)}"${params})`;
    } else {
      replacement = `this.debugLog('${level}', "${replaceEmojis(prefix)}" + ${expr} + "${replaceEmojis(suffix)}")`;
    }
    
    // Replace the full match in the result
    result = result.replace(fullMatch, replacement);
    count++;
    
    // Reset the regex to search from the beginning
    debugPattern.lastIndex = 0;
  }
  
  console.log(`Fixed ${count} debug log statements`);
  return result;
};

// Fix CSS template literals
const fixCssTemplates = (content) => {
  // Pattern to match CSS template literals
  const cssPattern = /([a-zA-Z-]+):\s*`(.*?)\${([^}]*)}\s*(.*?)`/g;
  
  let result = content;
  let match;
  let count = 0;
  
  // Replace each matched CSS template literal
  while ((match = cssPattern.exec(content)) !== null) {
    const [fullMatch, property, prefix, expr, suffix] = match;
    
    // Create the replacement with string concatenation
    const replacement = `${property}: "${prefix}" + ${expr} + "${suffix}"`;
    
    // Replace the full match in the result
    result = result.replace(fullMatch, replacement);
    count++;
    
    // Reset the regex to search from the beginning
    cssPattern.lastIndex = 0;
  }
  
  console.log(`Fixed ${count} CSS template literals`);
  return result;
};

// Fix general template literals
const fixGeneralTemplates = (content) => {
  // Pattern to match general template literals
  const templatePattern = /`([^`\$]*)\${([^}]*)}\s*([^`]*)`/g;
  
  let result = content;
  let match;
  let count = 0;
  
  // Replace each matched template literal
  while ((match = templatePattern.exec(content)) !== null) {
    const [fullMatch, prefix, expr, suffix] = match;
    
    // Skip if already part of a fixed console or debug log
    if (result.substring(match.index - 20, match.index).includes('console.') ||
        result.substring(match.index - 20, match.index).includes('debugLog')) {
      continue;
    }
    
    // Create the replacement with string concatenation
    let replacement;
    if (prefix.length === 0 && suffix.length === 0) {
      replacement = `(${expr})`;
    } else if (prefix.length === 0) {
      replacement = `(${expr}) + "${replaceEmojis(suffix)}"`;
    } else if (suffix.length === 0) {
      replacement = `"${replaceEmojis(prefix)}" + (${expr})`;
    } else {
      replacement = `"${replaceEmojis(prefix)}" + (${expr}) + "${replaceEmojis(suffix)}"`;
    }
    
    // Replace the full match in the result
    result = result.replace(fullMatch, replacement);
    count++;
    
    // Reset the regex to search from the beginning
    templatePattern.lastIndex = 0;
  }
  
  console.log(`Fixed ${count} general template literals`);
  return result;
};

// Fix double semicolons
const fixDoubleSemicolons = (content) => {
  let result = content;
  const semicolonPattern = /;;/g;
  const count = (result.match(semicolonPattern) || []).length;
  
  result = result.replace(semicolonPattern, ';');
  console.log(`Fixed ${count} double semicolons`);
  
  return result;
};

// Apply all fixes
let fixedContent = content;
fixedContent = fixConsoleLogs(fixedContent);
fixedContent = fixDebugLogs(fixedContent);
fixedContent = fixCssTemplates(fixedContent);
fixedContent = fixGeneralTemplates(fixedContent);
fixedContent = fixDoubleSemicolons(fixedContent);

// Write the fixed content back to the file
console.log(`Writing fixed content back to ${filePath}`);
fs.writeFileSync(filePath, fixedContent);

console.log('Console logs and template literals fixed in VehicleManager.ts');
console.log('Run TypeScript linter to check for any remaining issues'); 