.
├── .cursor
│   ├── mcp.json
│   └── rules
│       ├── animation-architecture-rules.mdc
│       ├── animation-debugging-rules.mdc
│       ├── animation-performance-rules.mdc
│       ├── animation-system-rules.mdc
│       ├── apicalldebounceingrules.mdc
│       ├── consistency-senior-dev.mdc
│       ├── cursor_rules.mdc
│       ├── cursor-memory-bank-rules.md
│       ├── datehandlingrule.mdc
│       ├── dev_workflow.mdc
│       ├── direct_apply.mdc
│       ├── errorhandlingrule.mdc
│       ├── map-camera-rules.mdc
│       ├── mapbox-best-practices.mdc
│       ├── performancerule.mdc
│       ├── principle-of-least-surprise.mdc
│       ├── react-animation-patterns.mdc
│       ├── reactcomponentstructurerule.mdc
│       ├── self_improve.mdc
│       ├── statemanagementrule.mdc
│       ├── taskmaster.mdc
│       ├── typesafetyrule.mdc
│       ├── uicomponentpatternrule.mdc
│       └── vehicle-animation-rules.mdc
├── .env
├── .env.example
├── .gitignore
├── .npmrc
├── .windsurfrules
├── .zed
│   └── settings.json
├── bun.lockb
├── cleanup-vehicle-manager.js
├── components.json
├── docs
│   ├── Animation-Architecture-Rules.md
│   ├── Animation-Architecture.md
│   ├── Animation-Contextual-Controls.md
│   ├── Animation-Lifecycle-Simplification.md
│   ├── Animation-System-Refactoring-Progress.md
│   ├── Animation-System-Refactoring-Summary.md
│   ├── Animation-User-Experience-Flow.md
│   ├── AnimationModules.md
│   ├── App-concept.md
│   ├── ARCHITECTURE-CONSOLIDATED.md
│   ├── ARCHITECTURE-UPDATE.md
│   ├── ButtonArchitecture.md
│   ├── Client-Onboarding-Checklist.md
│   ├── code-style-guidelines.md
│   ├── CodeConsolidation.md
│   ├── Component-Architecture-Implementation-Plan.md
│   ├── Component-Responsibilities-Clarification.md
│   ├── examples
│   │   ├──  .html
│   │   ├── AnimationLifecycle-Example.ts
│   │   ├── ThemeableDiscoveryOverlay-Italy-Tuscany.tsx
│   │   ├── ThemeableDiscoveryOverlay-Morocco-Atlas.tsx
│   │   ├── ThemeableDiscoveryOverlay-Morocco-Cultural.tsx
│   │   ├── ThemeableDiscoveryOverlay-Portugal-DouroValley.tsx
│   │   ├── ThemeableDiscoveryOverlay-USA-NapaValley.tsx
│   │   └── VehicleAnimation-Example.ts
│   ├── Framework-Offering.md
│   ├── images
│   │   └── animation-component-architecture.md
│   ├── import-path-standardization.md
│   ├── Map-Marker-Implementation-Lessons.md
│   ├── Mapbox-Integration.md
│   ├── MapCameraRules.md
│   ├── Migration-Guide-Vehicle-Components.md
│   ├── Modularization-Progress.md
│   ├── Multi-Client-POI-Architecture.md
│   ├── MultiClientImplementation.md
│   ├── POIDiscoveryFrameworkEvents.md
│   ├── Portugal-Theme-Implementation.md
│   ├── Position-Migration-Plan.md
│   ├── Position-Type-System.md
│   ├── Pricing-Strategy.md
│   ├── ProjectStructure.md
│   ├── RoutePointInterface .md
│   ├── TravelAnimator-Enhancements-Roadmap.md
│   ├── TravelAnimator-UserStory.md
│   ├── UI-Control-Consolidation.md
│   ├── UI-UX-Enhancements.md
│   ├── UI-UX-Flow-Suggestions.md
│   └── VehicleAnimationRules.md
├── enhanced-vehicle-fix.js
├── eslint.config.js
├── fix-console-and-emojis.js
├── fix-summary.md
├── fix-syntax-structure.js
├── fix-template-literals.js
├── index.html
├── jest.config.js
├── logs
│   └── localhost-*************.log
├── memory-bank
│   ├── activeContext.md
│   ├── productContext.md
│   ├── progress.md
│   ├── projectbrief.md
│   ├── README.md
│   ├── systemPatterns.md
│   └── techContext.md
├── package-lock.json
├── package.json
├── playwright-report
│   ├── data
│   │   ├── 43d1c37c0166051c533334613e958c6796572424.zip
│   │   ├── b505a64b2ec1b523b58fed1155fa98768bfb5622.png
│   │   └── d380389ba303bbf85a0b9932f4254ebf6b4fbd6d.webm
│   ├── index.html
│   └── trace
│       ├── assets
│       │   ├── codeMirrorModule-DpJ-EmBQ.js
│       │   └── defaultSettingsView-DTenqiGw.js
│       ├── codeMirrorModule.C3UTv-Ge.css
│       ├── codicon.DCmgc-ay.ttf
│       ├── defaultSettingsView.5fN5lw10.css
│       ├── index.CFOW-Ezb.css
│       ├── index.CUq7VgrV.js
│       ├── index.html
│       ├── playwright-logo.svg
│       ├── snapshot.html
│       ├── sw.bundle.js
│       ├── uiMode.BatfzHMG.css
│       ├── uiMode.CHJSAD7F.js
│       ├── uiMode.html
│       └── xtermModule.Beg8tuEN.css
├── playwright.config.js
├── postcss.config.js
├── public
│   ├── ctm-logo.png
│   ├── ctm-logo2.png
│   ├── debug-vehicle
│   │   ├── enhanced-test.html
│   │   ├── index.html
│   │   ├── README.md
│   │   ├── test-enhanced.js
│   │   └── test.js
│   ├── favicon.ico
│   ├── inject-mapbox-token.js
│   ├── mapbox-config.js
│   ├── mapbox-test.html
│   ├── placeholder.svg
│   └── robots.txt
├── README-task-master.md
├── README.md
├── scripts
│   ├── dev.js
│   ├── example_prd.txt
│   ├── fix-project-structure.sh
│   ├── inject-env-to-html.js
│   ├── prd.txt
│   ├── README-task-master.md
│   ├── README.md
│   └── typescript-fix.js
├── simple-fix-duplicates.js
├── simple-fix-vehicle-manager.js
├── src
│   ├── App.multiclient.tsx
│   ├── App.tsx
│   ├── components
│   │   ├── admin
│   │   │   ├── AdminClients.tsx
│   │   │   ├── AdminDestinations.tsx
│   │   │   ├── AdminPOIs.tsx
│   │   │   ├── AdminQuotes.tsx
│   │   │   ├── AdminThemes.tsx
│   │   │   ├── AdminUsers.tsx
│   │   │   ├── client-components
│   │   │   │   ├── ClientFeatureFlagsForm.tsx
│   │   │   │   ├── ClientMapSettingsForm.tsx
│   │   │   │   ├── ClientPOISettingsForm.tsx
│   │   │   │   └── ClientThemeForm.tsx
│   │   │   ├── destination-components
│   │   │   │   ├── DestinationActions.tsx
│   │   │   │   ├── EmptyState.tsx
│   │   │   │   └── LoadingState.tsx
│   │   │   ├── DestinationFormDialog.tsx
│   │   │   ├── DestinationHeader.tsx
│   │   │   ├── DestinationList.tsx
│   │   │   ├── form-sections
│   │   │   │   ├── BasicInfoFields.tsx
│   │   │   │   ├── DestinationBasicInfoFields.tsx
│   │   │   │   ├── DestinationDetailsFields.tsx
│   │   │   │   ├── DestinationLocationFields.tsx
│   │   │   │   ├── DetailsFields.tsx
│   │   │   │   ├── FormActions.tsx
│   │   │   │   └── LocationFields.tsx
│   │   │   ├── POIForm.tsx
│   │   │   ├── POIFormDialog.tsx
│   │   │   ├── POIHeader.tsx
│   │   │   ├── POIList.tsx
│   │   │   └── schemas
│   │   │       └── destinationSchema.ts
│   │   ├── AppHeader.tsx
│   │   ├── archived
│   │   │   └── fullmap
│   │   ├── auth
│   │   │   ├── LoginForm.tsx
│   │   │   └── SignupForm.tsx
│   │   ├── ClientSelector.tsx
│   │   ├── DestinationCard.tsx
│   │   ├── EnhancedFooter.tsx
│   │   ├── EnhancedPointOfInterestCard.tsx
│   │   ├── explore
│   │   │   ├── ExploreFilters.tsx
│   │   │   ├── ExploreFooter.tsx
│   │   │   ├── ExplorePOIList.tsx
│   │   │   └── ExploreSidebar.tsx
│   │   ├── Footer.tsx
│   │   ├── home
│   │   │   ├── DestinationsList.tsx
│   │   │   ├── MapSection.tsx
│   │   │   └── RouteFooter.tsx
│   │   ├── HomePage.tsx
│   │   ├── ItineraryBalanceAnalysis.tsx
│   │   ├── ItineraryBuilder.tsx
│   │   ├── ItineraryPOIDisplay.tsx
│   │   ├── journey
│   │   │   ├── controls
│   │   │   ├── markers
│   │   │   ├── overlays
│   │   │   └── RightSidebar.tsx
│   │   ├── JourneyFooter.tsx
│   │   ├── MainHeader.tsx
│   │   ├── map
│   │   │   ├── animation
│   │   │   │   ├── __tests__
│   │   │   │   │   ├── README.md
│   │   │   │   │   ├── VehicleController.test.ts
│   │   │   │   │   ├── VehicleDOM.test.ts
│   │   │   │   │   ├── VehicleIntegration.test.ts
│   │   │   │   │   ├── VehicleManager.test.ts
│   │   │   │   │   └── VehiclePerformance.test.ts
│   │   │   │   ├── AnimationController.tsx
│   │   │   │   ├── AnimationDebugPanel.css
│   │   │   │   ├── AnimationDebugPanel.tsx
│   │   │   │   ├── AnimationDebugTools.ts
│   │   │   │   ├── AnimationErrorHandler.ts
│   │   │   │   ├── AnimationEventEmitter.ts
│   │   │   │   ├── AnimationFrameManager.ts
│   │   │   │   ├── AnimationHandler.ts
│   │   │   │   ├── AnimationIntegration.ts
│   │   │   │   ├── AnimationLifecycleManager.ts
│   │   │   │   ├── AnimationManager.ts
│   │   │   │   ├── AnimationManagerWrapper.ts
│   │   │   │   ├── AnimationMonitoring.ts
│   │   │   │   ├── AnimationPerformanceMonitor.ts
│   │   │   │   ├── AnimationStateValidator.ts
│   │   │   │   ├── AnimationStyles.tsx
│   │   │   │   ├── AnimationUIUtils.ts
│   │   │   │   ├── AnimationUtils.ts
│   │   │   │   ├── AwarenessIndicators.ts
│   │   │   │   ├── CameraJitterTest.ts
│   │   │   │   ├── CinematicController.ts
│   │   │   │   ├── CinematicControllerFactory.ts
│   │   │   │   ├── CityDriveByManager.ts
│   │   │   │   ├── ComponentInteractionManager.ts
│   │   │   │   ├── ContextualRhythm.ts
│   │   │   │   ├── ContextualSpeedController.ts
│   │   │   │   ├── DebugHelper.ts
│   │   │   │   ├── EmergencyVehicle.ts
│   │   │   │   ├── EventManager.ts
│   │   │   │   ├── fixes
│   │   │   │   │   └── InteractionFix.ts
│   │   │   │   ├── index.ts
│   │   │   │   ├── interfaces
│   │   │   │   │   ├── AnimationComponentInterface.ts
│   │   │   │   │   ├── AnimationIntegrationInterface.ts
│   │   │   │   │   ├── AnimationLifecycleManagerInterface.ts
│   │   │   │   │   ├── AnimationManagerInterface.ts
│   │   │   │   │   ├── CameraControllerInterface.ts
│   │   │   │   │   ├── CinematicControllerInterface.ts
│   │   │   │   │   ├── index.ts
│   │   │   │   │   ├── RouteAnimatorInterface.ts
│   │   │   │   │   ├── VehicleControllerInterface.ts
│   │   │   │   │   ├── VehicleDOMInterface.ts
│   │   │   │   │   └── VehicleManagerInterface.ts
│   │   │   │   ├── MarkerManager.ts
│   │   │   │   ├── OptimizedAnimationIntegration.ts
│   │   │   │   ├── POIDiscoveryFrameworkEvents.ts
│   │   │   │   ├── POIDiscoveryIntegration.ts
│   │   │   │   ├── POIDiscoveryManager.ts
│   │   │   │   ├── README.md
│   │   │   │   ├── RefactoredVehicleManager.ts
│   │   │   │   ├── RouteAnimator.ts
│   │   │   │   ├── RouteAnimatorAdapter.ts
│   │   │   │   ├── RouteDataLoader.ts
│   │   │   │   ├── SmoothVehicleController.ts
│   │   │   │   ├── UIComponentManager.ts
│   │   │   │   ├── useAnimationFrame.ts
│   │   │   │   ├── utils
│   │   │   │   │   ├── MapInstance.ts
│   │   │   │   │   └── types.ts
│   │   │   │   ├── vehicle
│   │   │   │   ├── VehicleController.ts
│   │   │   │   ├── VehicleDOM.ts
│   │   │   │   ├── VehicleManager.ts
│   │   │   │   ├── VehicleManager.ts.backup
│   │   │   │   ├── VehicleManager.ts.backup.syntax
│   │   │   │   ├── VehicleManager.ts.console-backup
│   │   │   │   └── VehicleManager.ts.template-backup
│   │   │   ├── AnimationControlPanel.tsx
│   │   │   ├── camera
│   │   │   │   └── CameraController.ts
│   │   │   ├── clusters
│   │   │   │   ├── ClusterGenerator.ts
│   │   │   │   ├── ClusterMarkers.tsx
│   │   │   │   ├── ClusterModal.tsx
│   │   │   │   └── SimpleClusterModal.tsx
│   │   │   ├── components
│   │   │   │   ├── AnimationControls.tsx
│   │   │   │   ├── AnimationDebugStatePanel.tsx
│   │   │   │   ├── JourneyButton.tsx
│   │   │   │   ├── JourneyProgressIndicator.tsx
│   │   │   │   └── ProgressBar.tsx
│   │   │   ├── consolidation-notes.md
│   │   │   ├── controls
│   │   │   │   └── MapControls.tsx
│   │   │   ├── DestinationMarker.tsx
│   │   │   ├── DirectDestinationMarkers.tsx
│   │   │   ├── DirectPOIMarkers.tsx
│   │   │   ├── ExploreMap.tsx
│   │   │   ├── JourneyStateRestorer.tsx
│   │   │   ├── JourneyUtils.ts
│   │   │   ├── layers
│   │   │   │   └── RouteLayer.tsx
│   │   │   ├── LeftPOIPanel.tsx
│   │   │   ├── MapComponent.tsx
│   │   │   ├── MapController.tsx
│   │   │   ├── MapControls.tsx
│   │   │   ├── MapPopups.tsx
│   │   │   ├── MapStyles.tsx
│   │   │   ├── MarkerLayer.tsx
│   │   │   ├── markers
│   │   │   │   ├── DestinationMarker.tsx
│   │   │   │   ├── EnhancedPOIMarker.tsx
│   │   │   │   ├── MarkerManager.tsx
│   │   │   │   └── POIMarker.tsx
│   │   │   ├── notifications
│   │   │   │   ├── NotificationPanel.css
│   │   │   │   ├── NotificationPanel.tsx
│   │   │   │   └── SimpleNotificationPanel.tsx
│   │   │   ├── overlays
│   │   │   │   ├── CityTagsOverlay.tsx
│   │   │   │   ├── FeedbackOverlay.tsx
│   │   │   │   ├── OnTheWayPOIOverlay.tsx
│   │   │   │   ├── POINotification.tsx
│   │   │   │   └── POIOverlay.tsx
│   │   │   ├── POIConnectionLine.tsx
│   │   │   ├── POIConnections.tsx
│   │   │   ├── POIMarker.tsx
│   │   │   ├── PointsOfInterestLayer.tsx.bak
│   │   │   ├── README.md
│   │   │   ├── RightSidebar.tsx
│   │   │   ├── RouteConnector.tsx
│   │   │   ├── src
│   │   │   │   └── components
│   │   │   │       └── map
│   │   │   │           └── animation
│   │   │   │               └── README.md
│   │   │   ├── TestAnimationComponent.tsx
│   │   │   ├── TravelAnimator.module.css
│   │   │   ├── TravelAnimator.tsx
│   │   │   ├── TravelAnimatorNew.module.css
│   │   │   ├── TravelAnimatorNew.tsx
│   │   │   ├── types
│   │   │   │   ├── AnimationTypes.ts
│   │   │   │   ├── EventTypes.ts
│   │   │   │   ├── MapTypes.ts
│   │   │   │   └── VehicleTypes.ts
│   │   │   ├── utils
│   │   │   │   ├── AnimationHelpers.ts
│   │   │   │   ├── CameraBehavior.ts
│   │   │   │   ├── cluster-types.ts
│   │   │   │   ├── ClusterGenerator.ts
│   │   │   │   ├── constants.ts
│   │   │   │   ├── DirectionIndicator.tsx
│   │   │   │   ├── EnhancedCameraBehavior.ts
│   │   │   │   ├── index.tsx
│   │   │   │   ├── JourneyManager.ts
│   │   │   │   ├── JourneyStateManager.ts
│   │   │   │   ├── MapHelpers.ts
│   │   │   │   ├── MapHelpers.tsx
│   │   │   │   ├── MapInstance.ts
│   │   │   │   ├── MathHelpers.ts
│   │   │   │   ├── NotificationManager.tsx
│   │   │   │   ├── PerformanceUtils.ts
│   │   │   │   ├── POIDiscovery.ts
│   │   │   │   ├── POIDiscovery.tsx
│   │   │   │   ├── POIDiscoveryManager.ts
│   │   │   │   ├── poiUtils.ts
│   │   │   │   ├── RoutePoiDiscovery.ts
│   │   │   │   ├── types.ts
│   │   │   │   ├── useAnimationState.ts
│   │   │   │   └── ZIndexManager.ts
│   │   │   └── vehicle
│   │   │       ├── DirectVehicleMarker.tsx
│   │   │       ├── VehicleController.ts
│   │   │       └── VehicleMarker.tsx
│   │   ├── MobileDestinationCard.tsx
│   │   ├── MobileNavigation.tsx
│   │   ├── PointOfInterestCard.tsx
│   │   ├── profile
│   │   │   ├── ProfileAvatar.tsx
│   │   │   └── ProfileForm.tsx
│   │   ├── ProgressExample.tsx
│   │   ├── QuoteFormModal.tsx
│   │   ├── RouteCard.tsx
│   │   ├── RouteConnector.tsx
│   │   ├── RouteList.tsx
│   │   ├── RouteSaveModal.tsx
│   │   ├── SearchFilters.tsx
│   │   ├── shared
│   │   │   ├── controls
│   │   │   ├── markers
│   │   │   └── overlays
│   │   ├── TripDetailsForm.tsx
│   │   ├── TripRecommendations.tsx
│   │   ├── ui
│   │   │   ├── accordion.tsx
│   │   │   ├── alert-dialog.tsx
│   │   │   ├── alert.tsx
│   │   │   ├── aspect-ratio.tsx
│   │   │   ├── avatar.tsx
│   │   │   ├── badge.tsx
│   │   │   ├── breadcrumb.tsx
│   │   │   ├── button.tsx
│   │   │   ├── calendar.tsx
│   │   │   ├── card.tsx
│   │   │   ├── carousel.tsx
│   │   │   ├── chart.tsx
│   │   │   ├── checkbox.tsx
│   │   │   ├── collapsible.tsx
│   │   │   ├── command.tsx
│   │   │   ├── context-menu.tsx
│   │   │   ├── controls
│   │   │   ├── CountdownLoadingIndicator.tsx
│   │   │   ├── dialog.tsx
│   │   │   ├── drawer.tsx
│   │   │   ├── dropdown-menu.tsx
│   │   │   ├── ErrorDisplay.module.css
│   │   │   ├── ErrorDisplay.tsx
│   │   │   ├── form.tsx
│   │   │   ├── hover-card.tsx
│   │   │   ├── input-otp.tsx
│   │   │   ├── input.tsx
│   │   │   ├── label.tsx
│   │   │   ├── LoadingIndicator.module.css
│   │   │   ├── LoadingIndicator.tsx
│   │   │   ├── markers
│   │   │   ├── menubar.tsx
│   │   │   ├── navigation-menu.tsx
│   │   │   ├── overlays
│   │   │   ├── pagination.tsx
│   │   │   ├── popover.tsx
│   │   │   ├── progress-indicator.tsx
│   │   │   ├── progress.tsx
│   │   │   ├── radio-group.tsx
│   │   │   ├── resizable.tsx
│   │   │   ├── scroll-area.tsx
│   │   │   ├── select.tsx
│   │   │   ├── separator.tsx
│   │   │   ├── sheet.tsx
│   │   │   ├── sidebar.tsx
│   │   │   ├── skeleton.tsx
│   │   │   ├── slider.tsx
│   │   │   ├── switch.tsx
│   │   │   ├── table.tsx
│   │   │   ├── tabs.tsx
│   │   │   ├── textarea.tsx
│   │   │   ├── ThemedButton.tsx
│   │   │   ├── ThemedOverlay.tsx
│   │   │   ├── toast.tsx
│   │   │   ├── toaster.tsx
│   │   │   ├── toggle-group.tsx
│   │   │   ├── toggle.tsx
│   │   │   ├── tooltip.tsx
│   │   │   └── use-toast.tsx
│   │   ├── UserAvatar.tsx
│   │   └── VehicleSelector.tsx
│   ├── config
│   │   ├── clientConfigs.ts
│   │   ├── paths.ts
│   │   ├── themes
│   │   │   ├── index.ts
│   │   │   ├── morocco.ts
│   │   │   └── portugal.ts
│   │   └── themes.ts
│   ├── contexts
│   │   ├── AuthContext.tsx
│   │   └── ClientContext.tsx
│   ├── data
│   │   ├── clientPOIs.ts
│   │   ├── destinations.ts
│   │   ├── douroValleyPOIs.ts
│   │   ├── mockData.ts
│   │   ├── mockDestinations.ts
│   │   ├── poiInitializer.ts
│   │   ├── pointsOfInterest.ts
│   │   ├── portugalPOIs.ts
│   │   └── vehicles.ts
│   ├── global.d.ts
│   ├── hooks
│   │   ├── map
│   │   ├── use-animation.tsx
│   │   ├── use-mapbox.tsx
│   │   ├── use-mobile.tsx
│   │   ├── useAnimationManager.ts
│   │   ├── useAnimationManager.tsx
│   │   ├── useAuthProvider.ts
│   │   ├── useClientPOI.tsx
│   │   ├── useDestinationManagement.tsx
│   │   ├── useMapControls.ts
│   │   ├── useMapMarkers.tsx
│   │   ├── useMapPositioning.ts
│   │   ├── usePOIManagement.tsx
│   │   ├── useRoutePOIs.tsx
│   │   ├── useTheme.ts
│   │   └── useTheme.tsx
│   ├── index.css
│   ├── index.html
│   ├── integrations
│   │   └── supabase
│   │       ├── client.ts
│   │       └── types.ts
│   ├── lib
│   │   ├── turf-helper.ts
│   │   └── utils.ts
│   ├── main.tsx
│   ├── pages
│   │   ├── Admin.tsx
│   │   ├── archived
│   │   │   └── Index.tsx
│   │   ├── Auth.tsx
│   │   ├── ClientDemo.tsx
│   │   ├── Contact.tsx
│   │   ├── Developers.tsx
│   │   ├── HomePage.tsx
│   │   ├── MapView.tsx
│   │   ├── ModernPOIPanelsDemo.tsx
│   │   ├── MyRoutes.tsx
│   │   ├── NotFound.tsx
│   │   ├── PaymentPlans.tsx
│   │   ├── Profile.tsx
│   │   ├── ProgressDemo.tsx
│   │   ├── QuoteRequests.tsx
│   │   └── test-animation.tsx
│   ├── providers
│   │   ├── MapReadyProvider.tsx
│   │   └── ThemeProvider.tsx
│   ├── services
│   │   ├── clientPOIService.ts
│   │   └── emailService.ts
│   ├── setupTests.ts
│   ├── styles
│   │   ├── base
│   │   │   └── reset.css
│   │   ├── components
│   │   │   ├── animation-controls.css
│   │   │   ├── city-tags.css
│   │   │   ├── journey-footer.css
│   │   │   ├── journey-sidebar.css
│   │   │   ├── map-controls.css
│   │   │   ├── markers.css
│   │   │   ├── notification-panel.css
│   │   │   ├── poi-list.css
│   │   │   └── travel-animator.css
│   │   ├── consolidated
│   │   │   └── markers.css
│   │   ├── enhanced-markers.css
│   │   ├── enhanced-poi-panels.css
│   │   ├── explore-map.css
│   │   ├── fixes
│   │   ├── globals.css
│   │   ├── index.css
│   │   ├── journey-sidebar.css
│   │   ├── layout
│   │   ├── map-markers.css
│   │   ├── map-overlays.css
│   │   ├── map.css
│   │   ├── modern-poi-panels.css
│   │   ├── poi-markers.css
│   │   ├── right-sidebar.css
│   │   └── utils
│   ├── types
│   │   ├── AnimationEventTypes.ts
│   │   ├── AnimationTypes.ts
│   │   ├── auth-types.ts
│   │   ├── City.ts
│   │   ├── ClientTypes.ts
│   │   ├── clusters.ts
│   │   ├── CoreAnimationTypes.ts
│   │   ├── cultural-regions.ts
│   │   ├── custom-declarations.d.ts
│   │   ├── destination.ts
│   │   ├── DestinationTypes.ts
│   │   ├── index.ts
│   │   ├── interface-methods-additions.md
│   │   ├── LogLevel.ts
│   │   ├── LogTypes.ts
│   │   ├── mapbox-declarations.d.ts
│   │   ├── mapbox-gl.d.ts
│   │   ├── mapbox-imports.d.ts
│   │   ├── mapbox-point-geometry.d.ts
│   │   ├── MapTypes.ts
│   │   ├── module-declarations.d.ts
│   │   ├── MultiClientAnimationTypes.ts
│   │   ├── patch-mapbox-gl.d.ts
│   │   ├── path-aliases.d.ts
│   │   ├── poi.ts
│   │   ├── POIAdapter.ts
│   │   ├── point-geometry.d.ts
│   │   ├── POITypes.d.ts
│   │   ├── POITypes.ts
│   │   ├── POITypes.updated.ts
│   │   ├── position-extended.d.ts
│   │   ├── Position.ts
│   │   ├── README.md
│   │   ├── RoutePoint.ts
│   │   ├── RoutePointAdapter.ts
│   │   ├── TerrainType.ts
│   │   ├── TravelAnimator.d.ts
│   │   ├── TravelAnimatorTypes.ts
│   │   └── VehicleTypes.ts
│   ├── ui-enhancements-proposal.md
│   ├── utils
│   │   ├── AnimationDebugTools.ts
│   │   ├── AnimationEventEmitter.ts
│   │   ├── animationLogger.ts
│   │   ├── cultural-regions.ts
│   │   ├── debugLogger.ts
│   │   ├── mapbox-adapter.ts
│   │   ├── mapbox-imports-fixed.ts
│   │   ├── mapbox-imports.d.ts
│   │   ├── mapbox-imports.ts
│   │   ├── MapHelpers.ts
│   │   ├── mapUtils.ts
│   │   ├── MarkerCluster.ts
│   │   ├── markerManager.ts
│   │   ├── poi-importance.ts
│   │   ├── poiUtils.ts
│   │   ├── positionUtils.ts
│   │   ├── README.md
│   │   ├── route-analysis.ts
│   │   ├── routeManager.ts
│   │   ├── routeUtils.ts
│   │   ├── supabaseHelpers.ts
│   │   ├── throttleUtils.ts
│   │   └── trip-templates.ts
│   └── vite-env.d.ts
├── supabase
│   └── config.toml
├── tailwind.config.ts
├── tasks
│   └── technical-configuration-fixes.md
├── test-route-animation.js
├── tests
│   ├── bearing-analysis.spec.js
│   ├── fix-vehicle-marker.spec.js
│   ├── vehicle-analysis.spec.js
│   └── vehicle-recording.spec.js
├── tmp
├── tree.txt
├── tsconfig.app.json
├── tsconfig.ci.json
├── tsconfig.json
├── tsconfig.node.json
├── vite.config.ts
└── website

90 directories, 585 files
