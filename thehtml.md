<div data-lov-id="src/pages/enhanced-neutral-demo.tsx:738:12" data-lov-name="div" data-component-path="src/pages/enhanced-neutral-demo.tsx" data-component-line="738" data-component-file="enhanced-neutral-demo.tsx" data-component-name="div" data-component-content="%7B%7D" class="h-full transition-all duration-500 ease-in-out flex-shrink-0 opacity-100 z-20" style="width: 480px; margin: 0px; padding: 0px;"><button data-lov-id="src/pages/enhanced-neutral-demo.tsx:759:16" data-lov-name="button" data-component-path="src/pages/enhanced-neutral-demo.tsx" data-component-line="759" data-component-file="enhanced-neutral-demo.tsx" data-component-name="button" data-component-content="%7B%22className%22%3A%22absolute%20-right-6%20top-1%2F2%20-translate-y-1%2F2%20bg-white%20p-1%20rounded-r%20shadow-lg%20z-50%20border%20border-gray-200%22%7D" class="absolute -right-6 top-1/2 -translate-y-1/2 bg-white p-1 rounded-r shadow-lg z-50 border border-gray-200" style="color: var(--theme-primary-color);"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left h-4 w-4" data-lov-id="src/pages/enhanced-neutral-demo.tsx:764:81" data-lov-name="ChevronLeft" data-component-path="src/pages/enhanced-neutral-demo.tsx" data-component-line="764" data-component-file="enhanced-neutral-demo.tsx" data-component-name="ChevronLeft" data-component-content="%7B%22className%22%3A%22h-4%20w-4%22%7D"><path d="m15 18-6-6 6-6"></path></svg></button><div data-lov-id="src/components/layout/LeftPane.tsx:350:4" data-lov-name="div" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="350" data-component-file="LeftPane.tsx" data-component-name="div" data-component-content="%7B%22className%22%3A%22left-pane%20morocco-pattern-2%22%7D" class="left-pane morocco-pattern-2" style="width: 320px; background-color: var(--morocco-sand-lightest); height: 100vh; border-right: 1px solid var(--border-light); display: flex; flex-direction: column; box-shadow: var(--shadow-soft); z-index: 10; position: relative;"><div data-lov-id="src/components/layout/LeftPane.tsx:556:6" data-lov-name="div" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="556" data-component-file="LeftPane.tsx" data-component-name="div" data-component-content="%7B%7D" style="padding: 0.5rem 1rem; border-bottom: 1px solid var(--border-light);"><div data-lov-id="src/components/layout/LeftPane.tsx:557:8" data-lov-name="div" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="557" data-component-file="LeftPane.tsx" data-component-name="div" data-component-content="%7B%7D" style="display: flex; gap: 0.25rem; background: rgba(209, 196, 169, 0.15); padding: 0.125rem; border-radius: var(--radius-full);"><button data-lov-id="src/components/layout/LeftPane.tsx:566:10" data-lov-name="button" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="566" data-component-file="LeftPane.tsx" data-component-name="button" data-component-content="%7B%22text%22%3A%22All%22%7D" style="flex: 1 1 0%; padding: 0.375rem 0.75rem; border: none; border-radius: var(--radius-full); font-family: var(--font-body); font-weight: 600; font-size: 0.75rem; cursor: pointer; background-color: white; color: var(--morocco-red); box-shadow: var(--shadow-soft); transition: 0.2s; text-transform: uppercase; letter-spacing: 0.5px;">All</button><button data-lov-id="src/components/layout/LeftPane.tsx:588:10" data-lov-name="button" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="588" data-component-file="LeftPane.tsx" data-component-name="button" data-component-content="%7B%22text%22%3A%22Top%22%7D" style="flex: 1 1 0%; padding: 0.375rem 0.75rem; border: none; border-radius: var(--radius-full); font-family: var(--font-body); font-weight: 600; font-size: 0.75rem; cursor: pointer; background-color: transparent; color: var(--text-secondary); box-shadow: none; transition: 0.2s; display: flex; align-items: center; justify-content: center; gap: 0.25rem; text-transform: uppercase; letter-spacing: 0.5px;"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-star" data-lov-id="src/components/layout/LeftPane.tsx:612:12" data-lov-name="Star" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="612" data-component-file="LeftPane.tsx" data-component-name="Star" data-component-content="%7B%7D"><path d="M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z"></path></svg>Top</button><button data-lov-id="src/components/layout/LeftPane.tsx:615:10" data-lov-name="button" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="615" data-component-file="LeftPane.tsx" data-component-name="button" data-component-content="%7B%22text%22%3A%22Near%22%7D" style="flex: 1 1 0%; padding: 0.375rem 0.75rem; border: none; border-radius: var(--radius-full); font-family: var(--font-body); font-weight: 600; font-size: 0.75rem; cursor: pointer; background-color: transparent; color: var(--text-secondary); box-shadow: none; transition: 0.2s; display: flex; align-items: center; justify-content: center; gap: 0.25rem; text-transform: uppercase; letter-spacing: 0.5px;"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-map-pin" data-lov-id="src/components/layout/LeftPane.tsx:639:12" data-lov-name="MapPin" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="639" data-component-file="LeftPane.tsx" data-component-name="MapPin" data-component-content="%7B%7D"><path d="M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0"></path><circle cx="12" cy="10" r="3"></circle></svg>Near</button></div></div><div data-lov-id="src/components/layout/LeftPane.tsx:646:6" data-lov-name="div" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="646" data-component-file="LeftPane.tsx" data-component-name="div" data-component-content="%7B%7D" style="border-bottom: 1px solid var(--border-light);"><div data-lov-id="src/components/layout/LeftPane.tsx:648:8" data-lov-name="div" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="648" data-component-file="LeftPane.tsx" data-component-name="div" data-component-content="%7B%7D" style="width: 100%; padding: 0.75rem 1rem; display: flex; align-items: center; justify-content: space-between;"><div data-lov-id="src/components/layout/LeftPane.tsx:656:10" data-lov-name="div" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="656" data-component-file="LeftPane.tsx" data-component-name="div" data-component-content="%7B%7D" style="display: flex; align-items: center; gap: 0.5rem; cursor: default; flex: 1 1 0%;"><h3 data-lov-id="src/components/layout/LeftPane.tsx:666:12" data-lov-name="h3" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="666" data-component-file="LeftPane.tsx" data-component-name="h3" data-component-content="%7B%22text%22%3A%22Travel%20Interests%22%7D" style="margin: 0px; font-size: 0.875rem; font-weight: 600; color: var(--text-secondary); font-family: var(--font-body); display: flex; align-items: center; gap: 0.5rem;"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="var(--morocco-red)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-heart" data-lov-id="src/components/layout/LeftPane.tsx:678:14" data-lov-name="Heart" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="678" data-component-file="LeftPane.tsx" data-component-name="Heart" data-component-content="%7B%7D"><path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"></path></svg>Travel Interests</h3></div><button data-lov-id="src/components/layout/LeftPane.tsx:688:12" data-lov-name="button" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="688" data-component-file="LeftPane.tsx" data-component-name="button" data-component-content="%7B%7D" style="display: flex; align-items: center; gap: 0.25rem; background: none; border: none; font-size: 0.75rem; color: var(--morocco-blue); cursor: pointer; font-family: var(--font-body); font-weight: 500;"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-filter" data-lov-id="src/components/layout/LeftPane.tsx:703:14" data-lov-name="Filter" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="703" data-component-file="LeftPane.tsx" data-component-name="Filter" data-component-content="%7B%7D"><polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon></svg>More Filters</button></div><div data-lov-id="src/components/layout/LeftPane.tsx:711:10" data-lov-name="div" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="711" data-component-file="LeftPane.tsx" data-component-name="div" data-component-content="%7B%7D" style="padding: 0px 1rem 0.75rem;"><div data-lov-id="src/components/layout/LeftPane.tsx:713:12" data-lov-name="div" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="713" data-component-file="LeftPane.tsx" data-component-name="div" data-component-content="%7B%7D" style="display: flex; flex-wrap: wrap; gap: 0.5rem; margin-bottom: 0.75rem;"><button data-lov-id="src/components/layout/LeftPane.tsx:715:16" data-lov-name="button" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="715" data-component-file="LeftPane.tsx" data-component-name="button" data-component-content="%7B%7D" style="padding: 0.25rem 0.75rem; border-radius: var(--radius-full); border: none; background-color: rgba(0, 71, 171, 0.1); color: var(--morocco-blue); font-family: var(--font-body); font-size: 0.75rem; font-weight: 500; cursor: pointer; transition: 0.2s; display: flex; align-items: center; gap: 0.25rem;"><span data-lov-id="src/components/layout/LeftPane.tsx:738:18" data-lov-name="span" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="738" data-component-file="LeftPane.tsx" data-component-name="span" data-component-content="%7B%7D">🏛️</span>Historic Landmarks</button><button data-lov-id="src/components/layout/LeftPane.tsx:715:16" data-lov-name="button" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="715" data-component-file="LeftPane.tsx" data-component-name="button" data-component-content="%7B%7D" style="padding: 0.25rem 0.75rem; border-radius: var(--radius-full); border: none; background-color: rgba(0, 71, 171, 0.1); color: var(--morocco-blue); font-family: var(--font-body); font-size: 0.75rem; font-weight: 500; cursor: pointer; transition: 0.2s; display: flex; align-items: center; gap: 0.25rem;"><span data-lov-id="src/components/layout/LeftPane.tsx:738:18" data-lov-name="span" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="738" data-component-file="LeftPane.tsx" data-component-name="span" data-component-content="%7B%7D">🏔️</span>Natural Wonders</button><button data-lov-id="src/components/layout/LeftPane.tsx:715:16" data-lov-name="button" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="715" data-component-file="LeftPane.tsx" data-component-name="button" data-component-content="%7B%7D" style="padding: 0.25rem 0.75rem; border-radius: var(--radius-full); border: none; background-color: rgba(0, 71, 171, 0.1); color: var(--morocco-blue); font-family: var(--font-body); font-size: 0.75rem; font-weight: 500; cursor: pointer; transition: 0.2s; display: flex; align-items: center; gap: 0.25rem;"><span data-lov-id="src/components/layout/LeftPane.tsx:738:18" data-lov-name="span" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="738" data-component-file="LeftPane.tsx" data-component-name="span" data-component-content="%7B%7D">🎭</span>Cultural Sites</button><button data-lov-id="src/components/layout/LeftPane.tsx:715:16" data-lov-name="button" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="715" data-component-file="LeftPane.tsx" data-component-name="button" data-component-content="%7B%7D" style="padding: 0.25rem 0.75rem; border-radius: var(--radius-full); border: none; background-color: rgba(0, 71, 171, 0.1); color: var(--morocco-blue); font-family: var(--font-body); font-size: 0.75rem; font-weight: 500; cursor: pointer; transition: 0.2s; display: flex; align-items: center; gap: 0.25rem;"><span data-lov-id="src/components/layout/LeftPane.tsx:738:18" data-lov-name="span" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="738" data-component-file="LeftPane.tsx" data-component-name="span" data-component-content="%7B%7D">🧗</span>Adventure</button><button data-lov-id="src/components/layout/LeftPane.tsx:715:16" data-lov-name="button" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="715" data-component-file="LeftPane.tsx" data-component-name="button" data-component-content="%7B%7D" style="padding: 0.25rem 0.75rem; border-radius: var(--radius-full); border: none; background-color: rgba(0, 71, 171, 0.1); color: var(--morocco-blue); font-family: var(--font-body); font-size: 0.75rem; font-weight: 500; cursor: pointer; transition: 0.2s; display: flex; align-items: center; gap: 0.25rem;"><span data-lov-id="src/components/layout/LeftPane.tsx:738:18" data-lov-name="span" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="738" data-component-file="LeftPane.tsx" data-component-name="span" data-component-content="%7B%7D">📸</span>Photography</button><button data-lov-id="src/components/layout/LeftPane.tsx:715:16" data-lov-name="button" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="715" data-component-file="LeftPane.tsx" data-component-name="button" data-component-content="%7B%7D" style="padding: 0.25rem 0.75rem; border-radius: var(--radius-full); border: none; background-color: rgba(0, 71, 171, 0.1); color: var(--morocco-blue); font-family: var(--font-body); font-size: 0.75rem; font-weight: 500; cursor: pointer; transition: 0.2s; display: flex; align-items: center; gap: 0.25rem;"><span data-lov-id="src/components/layout/LeftPane.tsx:738:18" data-lov-name="span" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="738" data-component-file="LeftPane.tsx" data-component-name="span" data-component-content="%7B%7D">🏗️</span>Architecture</button><button data-lov-id="src/components/layout/LeftPane.tsx:715:16" data-lov-name="button" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="715" data-component-file="LeftPane.tsx" data-component-name="button" data-component-content="%7B%7D" style="padding: 0.25rem 0.75rem; border-radius: var(--radius-full); border: none; background-color: rgba(0, 71, 171, 0.1); color: var(--morocco-blue); font-family: var(--font-body); font-size: 0.75rem; font-weight: 500; cursor: pointer; transition: 0.2s; display: flex; align-items: center; gap: 0.25rem;"><span data-lov-id="src/components/layout/LeftPane.tsx:738:18" data-lov-name="span" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="738" data-component-file="LeftPane.tsx" data-component-name="span" data-component-content="%7B%7D">🎪</span>Local Experiences</button><button data-lov-id="src/components/layout/LeftPane.tsx:715:16" data-lov-name="button" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="715" data-component-file="LeftPane.tsx" data-component-name="button" data-component-content="%7B%7D" style="padding: 0.25rem 0.75rem; border-radius: var(--radius-full); border: none; background-color: rgba(0, 71, 171, 0.1); color: var(--morocco-blue); font-family: var(--font-body); font-size: 0.75rem; font-weight: 500; cursor: pointer; transition: 0.2s; display: flex; align-items: center; gap: 0.25rem;"><span data-lov-id="src/components/layout/LeftPane.tsx:738:18" data-lov-name="span" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="738" data-component-file="LeftPane.tsx" data-component-name="span" data-component-content="%7B%7D">💎</span>Hidden Gems</button><button data-lov-id="src/components/layout/LeftPane.tsx:715:16" data-lov-name="button" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="715" data-component-file="LeftPane.tsx" data-component-name="button" data-component-content="%7B%7D" style="padding: 0.25rem 0.75rem; border-radius: var(--radius-full); border: none; background-color: rgba(0, 71, 171, 0.1); color: var(--morocco-blue); font-family: var(--font-body); font-size: 0.75rem; font-weight: 500; cursor: pointer; transition: 0.2s; display: flex; align-items: center; gap: 0.25rem;"><span data-lov-id="src/components/layout/LeftPane.tsx:738:18" data-lov-name="span" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="738" data-component-file="LeftPane.tsx" data-component-name="span" data-component-content="%7B%7D">🛣️</span>Scenic Drives</button><button data-lov-id="src/components/layout/LeftPane.tsx:715:16" data-lov-name="button" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="715" data-component-file="LeftPane.tsx" data-component-name="button" data-component-content="%7B%7D" style="padding: 0.25rem 0.75rem; border-radius: var(--radius-full); border: none; background-color: rgba(0, 71, 171, 0.1); color: var(--morocco-blue); font-family: var(--font-body); font-size: 0.75rem; font-weight: 500; cursor: pointer; transition: 0.2s; display: flex; align-items: center; gap: 0.25rem;"><span data-lov-id="src/components/layout/LeftPane.tsx:738:18" data-lov-name="span" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="738" data-component-file="LeftPane.tsx" data-component-name="span" data-component-content="%7B%7D">👁️</span>Viewpoints</button></div></div></div><div dir="ltr" data-lov-id="src/components/layout/LeftPane.tsx:832:6" data-lov-name="ScrollArea" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="832" data-component-file="LeftPane.tsx" data-component-name="ScrollArea" data-component-content="%7B%22className%22%3A%22morocco-scrollbar%22%7D" class="relative overflow-hidden morocco-scrollbar" style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; flex: 1 1 0%; overflow-y: auto; padding: 0.75rem 0px;"><style>
[data-radix-scroll-area-viewport] {
  scrollbar-width: none;
  -ms-overflow-style: none;
  -webkit-overflow-scrolling: touch;
}
[data-radix-scroll-area-viewport]::-webkit-scrollbar {
  display: none;
}
:where([data-radix-scroll-area-viewport]) {
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
:where([data-radix-scroll-area-content]) {
  flex-grow: 1;
}
</style><div data-radix-scroll-area-viewport="" data-lov-id="src/components/ui/scroll-area.tsx:15:4" data-lov-name="ScrollAreaPrimitive.Viewport" data-component-path="src/components/ui/scroll-area.tsx" data-component-line="15" data-component-file="scroll-area.tsx" data-component-name="ScrollAreaPrimitive.Viewport" data-component-content="%7B%22className%22%3A%22h-full%20w-full%20rounded-%5Binherit%5D%22%7D" class="h-full w-full rounded-[inherit]" style="overflow: hidden scroll;"><div data-radix-scroll-area-content=""><div data-lov-id="src/components/layout/LeftPane.tsx:840:8" data-lov-name="div" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="840" data-component-file="LeftPane.tsx" data-component-name="div" data-component-content="%7B%7D" style="display: flex; flex-direction: column; gap: 0.75rem; padding: 0px 1rem;"><div data-lov-id="src/components/layout/LeftPane.tsx:843:14" data-lov-name="div" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="843" data-component-file="LeftPane.tsx" data-component-name="div" data-component-content="%7B%22className%22%3A%22morocco-card%22%7D" class="morocco-card" style="padding: 0.5rem 0.75rem; cursor: pointer; transition: 0.2s; position: relative; overflow: hidden; border: 1px solid var(--border-light); background-color: white; display: flex; align-items: center; justify-content: space-between; gap: 0.75rem;"><div data-lov-id="src/components/layout/LeftPane.tsx:866:16" data-lov-name="div" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="866" data-component-file="LeftPane.tsx" data-component-name="div" data-component-content="%7B%7D" style="display: flex; align-items: center; gap: 0.75rem; flex: 1 1 0%; min-width: 0px;"><h3 data-lov-id="src/components/layout/LeftPane.tsx:874:18" data-lov-name="h3" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="874" data-component-file="LeftPane.tsx" data-component-name="h3" data-component-content="%7B%7D" style="margin: 0px; font-family: var(--font-display); font-size: 0.875rem; font-weight: 600; color: var(--text-primary); flex: 1 1 0%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">Central Museum</h3><div data-lov-id="src/components/layout/LeftPane.tsx:914:18" data-lov-name="div" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="914" data-component-file="LeftPane.tsx" data-component-name="div" data-component-content="%7B%7D" style="display: flex; align-items: center; gap: 0.25rem; font-size: 0.75rem; color: var(--morocco-yellow); flex-shrink: 0;"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-star" data-lov-id="src/components/layout/LeftPane.tsx:924:20" data-lov-name="Star" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="924" data-component-file="LeftPane.tsx" data-component-name="Star" data-component-content="%7B%7D"><path d="M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z"></path></svg><span data-lov-id="src/components/layout/LeftPane.tsx:925:20" data-lov-name="span" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="925" data-component-file="LeftPane.tsx" data-component-name="span" data-component-content="%7B%7D" style="color: var(--text-primary); font-weight: 600; font-size: 0.75rem;">4</span></div></div><button data-lov-id="src/components/layout/LeftPane.tsx:936:16" data-lov-name="button" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="936" data-component-file="LeftPane.tsx" data-component-name="button" data-component-content="%7B%7D" style="padding: 0.25rem 0.5rem; font-size: 0.75rem; font-weight: 600; border: 1px solid var(--morocco-blue); background-color: var(--morocco-blue); color: white; border-radius: var(--radius-sm); cursor: pointer; transition: 0.2s; min-width: 50px; flex-shrink: 0;">Add</button></div><div data-lov-id="src/components/layout/LeftPane.tsx:843:14" data-lov-name="div" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="843" data-component-file="LeftPane.tsx" data-component-name="div" data-component-content="%7B%22className%22%3A%22morocco-card%22%7D" class="morocco-card" style="padding: 0.5rem 0.75rem; cursor: pointer; transition: 0.2s; position: relative; overflow: hidden; border: 1px solid var(--border-light); background-color: white; display: flex; align-items: center; justify-content: space-between; gap: 0.75rem;"><div data-lov-id="src/components/layout/LeftPane.tsx:866:16" data-lov-name="div" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="866" data-component-file="LeftPane.tsx" data-component-name="div" data-component-content="%7B%7D" style="display: flex; align-items: center; gap: 0.75rem; flex: 1 1 0%; min-width: 0px;"><h3 data-lov-id="src/components/layout/LeftPane.tsx:874:18" data-lov-name="h3" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="874" data-component-file="LeftPane.tsx" data-component-name="h3" data-component-content="%7B%7D" style="margin: 0px; font-family: var(--font-display); font-size: 0.875rem; font-weight: 600; color: var(--text-primary); flex: 1 1 0%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">Waterfront Park</h3><div data-lov-id="src/components/layout/LeftPane.tsx:914:18" data-lov-name="div" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="914" data-component-file="LeftPane.tsx" data-component-name="div" data-component-content="%7B%7D" style="display: flex; align-items: center; gap: 0.25rem; font-size: 0.75rem; color: var(--morocco-yellow); flex-shrink: 0;"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-star" data-lov-id="src/components/layout/LeftPane.tsx:924:20" data-lov-name="Star" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="924" data-component-file="LeftPane.tsx" data-component-name="Star" data-component-content="%7B%7D"><path d="M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z"></path></svg><span data-lov-id="src/components/layout/LeftPane.tsx:925:20" data-lov-name="span" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="925" data-component-file="LeftPane.tsx" data-component-name="span" data-component-content="%7B%7D" style="color: var(--text-primary); font-weight: 600; font-size: 0.75rem;">4</span></div></div><button data-lov-id="src/components/layout/LeftPane.tsx:936:16" data-lov-name="button" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="936" data-component-file="LeftPane.tsx" data-component-name="button" data-component-content="%7B%7D" style="padding: 0.25rem 0.5rem; font-size: 0.75rem; font-weight: 600; border: 1px solid var(--morocco-blue); background-color: var(--morocco-blue); color: white; border-radius: var(--radius-sm); cursor: pointer; transition: 0.2s; min-width: 50px; flex-shrink: 0;">Add</button></div><div data-lov-id="src/components/layout/LeftPane.tsx:843:14" data-lov-name="div" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="843" data-component-file="LeftPane.tsx" data-component-name="div" data-component-content="%7B%22className%22%3A%22morocco-card%22%7D" class="morocco-card" style="padding: 0.5rem 0.75rem; cursor: pointer; transition: 0.2s; position: relative; overflow: hidden; border: 1px solid var(--border-light); background-color: white; display: flex; align-items: center; justify-content: space-between; gap: 0.75rem;"><div data-lov-id="src/components/layout/LeftPane.tsx:866:16" data-lov-name="div" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="866" data-component-file="LeftPane.tsx" data-component-name="div" data-component-content="%7B%7D" style="display: flex; align-items: center; gap: 0.75rem; flex: 1 1 0%; min-width: 0px;"><h3 data-lov-id="src/components/layout/LeftPane.tsx:874:18" data-lov-name="h3" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="874" data-component-file="LeftPane.tsx" data-component-name="h3" data-component-content="%7B%7D" style="margin: 0px; font-family: var(--font-display); font-size: 0.875rem; font-weight: 600; color: var(--text-primary); flex: 1 1 0%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">Gourmet Restaurant</h3><div data-lov-id="src/components/layout/LeftPane.tsx:914:18" data-lov-name="div" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="914" data-component-file="LeftPane.tsx" data-component-name="div" data-component-content="%7B%7D" style="display: flex; align-items: center; gap: 0.25rem; font-size: 0.75rem; color: var(--morocco-yellow); flex-shrink: 0;"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-star" data-lov-id="src/components/layout/LeftPane.tsx:924:20" data-lov-name="Star" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="924" data-component-file="LeftPane.tsx" data-component-name="Star" data-component-content="%7B%7D"><path d="M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z"></path></svg><span data-lov-id="src/components/layout/LeftPane.tsx:925:20" data-lov-name="span" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="925" data-component-file="LeftPane.tsx" data-component-name="span" data-component-content="%7B%7D" style="color: var(--text-primary); font-weight: 600; font-size: 0.75rem;">4</span></div></div><button data-lov-id="src/components/layout/LeftPane.tsx:936:16" data-lov-name="button" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="936" data-component-file="LeftPane.tsx" data-component-name="button" data-component-content="%7B%7D" style="padding: 0.25rem 0.5rem; font-size: 0.75rem; font-weight: 600; border: 1px solid var(--morocco-blue); background-color: var(--morocco-blue); color: white; border-radius: var(--radius-sm); cursor: pointer; transition: 0.2s; min-width: 50px; flex-shrink: 0;">Add</button></div><div data-lov-id="src/components/layout/LeftPane.tsx:843:14" data-lov-name="div" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="843" data-component-file="LeftPane.tsx" data-component-name="div" data-component-content="%7B%22className%22%3A%22morocco-card%22%7D" class="morocco-card" style="padding: 0.5rem 0.75rem; cursor: pointer; transition: 0.2s; position: relative; overflow: hidden; border: 1px solid var(--border-light); background-color: white; display: flex; align-items: center; justify-content: space-between; gap: 0.75rem;"><div data-lov-id="src/components/layout/LeftPane.tsx:866:16" data-lov-name="div" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="866" data-component-file="LeftPane.tsx" data-component-name="div" data-component-content="%7B%7D" style="display: flex; align-items: center; gap: 0.75rem; flex: 1 1 0%; min-width: 0px;"><h3 data-lov-id="src/components/layout/LeftPane.tsx:874:18" data-lov-name="h3" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="874" data-component-file="LeftPane.tsx" data-component-name="h3" data-component-content="%7B%7D" style="margin: 0px; font-family: var(--font-display); font-size: 0.875rem; font-weight: 600; color: var(--text-primary); flex: 1 1 0%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">Adventure Center</h3><div data-lov-id="src/components/layout/LeftPane.tsx:914:18" data-lov-name="div" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="914" data-component-file="LeftPane.tsx" data-component-name="div" data-component-content="%7B%7D" style="display: flex; align-items: center; gap: 0.25rem; font-size: 0.75rem; color: var(--morocco-yellow); flex-shrink: 0;"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-star" data-lov-id="src/components/layout/LeftPane.tsx:924:20" data-lov-name="Star" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="924" data-component-file="LeftPane.tsx" data-component-name="Star" data-component-content="%7B%7D"><path d="M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z"></path></svg><span data-lov-id="src/components/layout/LeftPane.tsx:925:20" data-lov-name="span" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="925" data-component-file="LeftPane.tsx" data-component-name="span" data-component-content="%7B%7D" style="color: var(--text-primary); font-weight: 600; font-size: 0.75rem;">4</span></div></div><button data-lov-id="src/components/layout/LeftPane.tsx:936:16" data-lov-name="button" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="936" data-component-file="LeftPane.tsx" data-component-name="button" data-component-content="%7B%7D" style="padding: 0.25rem 0.5rem; font-size: 0.75rem; font-weight: 600; border: 1px solid var(--morocco-blue); background-color: var(--morocco-blue); color: white; border-radius: var(--radius-sm); cursor: pointer; transition: 0.2s; min-width: 50px; flex-shrink: 0;">Add</button></div></div></div></div></div><div data-lov-id="src/components/layout/LeftPane.tsx:991:6" data-lov-name="div" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="991" data-component-file="LeftPane.tsx" data-component-name="div" data-component-content="%7B%7D" style="padding: 0.75rem 1rem; border-top: 1px solid var(--border-light); background-color: rgba(209, 196, 169, 0.1); display: flex; justify-content: space-between; align-items: center;"><p data-lov-id="src/components/layout/LeftPane.tsx:1001:8" data-lov-name="p" data-component-path="src/components/layout/LeftPane.tsx" data-component-line="1001" data-component-file="LeftPane.tsx" data-component-name="p" data-component-content="%7B%22text%22%3A%22item%20in%20itinerary%22%7D" style="font-size: 0.875rem; color: var(--text-secondary); margin: 0px; font-family: var(--font-body);">0 items in itinerary</p></div></div></div>