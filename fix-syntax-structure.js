#!/usr/bin/env node

/**
 * VehicleManager.ts Syntax Fix Script
 * 
 * This script focuses on fixing the severe syntax issues in VehicleManager.ts.
 * The file appears to be severely malformed with basic syntax issues that prevent
 * TypeScript from properly parsing it.
 * 
 * Usage:
 * node fix-syntax-structure.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name using ES module syntax
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const filePath = path.join(process.cwd(), 'src/components/map/animation/VehicleManager.ts');
const backupPath = `${filePath}.backup.syntax`;

// Create a backup if it doesn't exist
if (!fs.existsSync(backupPath)) {
  console.log('Creating backup of VehicleManager.ts...');
  fs.copyFileSync(filePath, backupPath);
}

console.log('Reading VehicleManager.ts...');
let content = fs.readFileSync(filePath, 'utf8');

/**
 * The TS errors suggest the file has fundamental structure issues:
 * 1. Missing or extra braces
 * 2. Unexpected characters in method signatures
 * 3. Invalid type predicates
 * 4. Missing semicolons at expected points
 */

// First, let's attempt to clean up the class structure - find class definition
const classMatch = content.match(/export\s+class\s+VehicleManager\s*{/);
if (!classMatch) {
  console.error('Could not find VehicleManager class definition!');
  process.exit(1);
}

const classStartIndex = classMatch.index;
const classEndIndex = content.lastIndexOf('}');

// Extract just the class content to work with
const classContent = content.substring(classStartIndex, classEndIndex + 1);

// Function to clean up method definitions - fix common syntax issues in method signatures
function cleanupMethodDefinitions(classText) {
  // Pattern to match method signatures with return types
  // This captures method headers like: private foo(): ReturnType {
  const methodSignaturePattern = /(private|public|protected|static)(\s+static)?(\s+\w+)\s*\(([^)]*)\)\s*:\s*([^{]*){/g;
  
  return classText.replace(methodSignaturePattern, (match, visibility, isStatic, name, params, returnType) => {
    // Fix spacing and ensure correct syntax
    const cleanVisibility = visibility.trim();
    const cleanStatic = isStatic ? ' static' : '';
    const cleanName = name.trim();
    const cleanParams = params.trim();
    const cleanReturnType = returnType.trim();
    
    return `  ${cleanVisibility}${cleanStatic} ${cleanName}(${cleanParams}): ${cleanReturnType} {`;
  });
}

// Fix type predicates which are common errors in the file
function fixTypePredicates(classText) {
  // Pattern to match type predicate methods with common syntax errors
  const typePredicatePattern = /(private|public)\s+(\w+)\s*\(([^)]*)\)\s*:\s*value\s+is\s+([^{]*){/g;
  
  return classText.replace(typePredicatePattern, (match, visibility, name, params, type) => {
    // Properly format the type predicate
    return `  ${visibility} ${name}(${params.trim()}): ${params.split(':')[0].trim()} is ${type.trim()} {`;
  });
}

// Fix event handler method signatures
function fixEventHandlerSignatures(classText) {
  // Fix arrow functions in parameter lists
  const arrowFunctionPattern = /\((.*?)\s*=>\s*(.*?)\)/g;
  
  return classText.replace(arrowFunctionPattern, (match, params, body) => {
    // Make sure arrow functions are properly formatted
    return `(${params.trim()} => ${body.trim()})`;
  });
}

// Fix unclosed or improperly closed methods
function fixMethodClosures(classText) {
  let fixedText = classText;
  const methodStartPattern = /(private|public|protected|static)(\s+static)?(\s+\w+)\s*\(([^)]*)\)\s*:/g;
  const methods = [...fixedText.matchAll(methodStartPattern)];
  
  // Check each method definition
  for (let i = 0; i < methods.length; i++) {
    const method = methods[i];
    const methodStart = method.index;
    const methodName = method[3].trim();
    
    // Find method body start
    const bodyStartIndex = fixedText.indexOf('{', methodStart);
    if (bodyStartIndex === -1) continue; // Skip if no body found
    
    // Find next method or end of class
    const nextMethodMatch = i < methods.length - 1 ? methods[i + 1] : null;
    const nextMethodStart = nextMethodMatch ? nextMethodMatch.index : fixedText.length;
    
    // Find closing brace for this method
    let openBraces = 1;
    let closePos = bodyStartIndex + 1;
    
    while (closePos < nextMethodStart && openBraces > 0) {
      if (fixedText[closePos] === '{') openBraces++;
      if (fixedText[closePos] === '}') openBraces--;
      closePos++;
    }
    
    // If method is not properly closed (missing closing brace)
    if (openBraces > 0) {
      console.log(`Fixing unclosed method: ${methodName}`);
      
      // Insert closing brace before next method
      const insertPoint = nextMethodMatch ? nextMethodMatch.index : fixedText.length;
      fixedText = fixedText.substring(0, insertPoint) + '\n  }\n\n' + fixedText.substring(insertPoint);
      
      // Update positions of subsequent methods after insertion
      for (let j = i + 1; j < methods.length; j++) {
        methods[j].index += 5; // Length of "\n  }\n\n"
      }
    }
  }
  
  return fixedText;
}

// Attempt to fix the syntax issues
let cleanedClass = cleanupMethodDefinitions(classContent);
cleanedClass = fixTypePredicates(cleanedClass);
cleanedClass = fixEventHandlerSignatures(cleanedClass);
cleanedClass = fixMethodClosures(cleanedClass);

// Fix missing semicolons in property assignments
cleanedClass = cleanedClass.replace(/(\w+)\s*=\s*([^;{][^\n]*)\n/g, '$1 = $2;\n');

// Fix class property type definitions
cleanedClass = cleanedClass.replace(/private\s+(\w+):\s*([^=;\n]*)(?=[=;])/g, 'private $1: $2 ');
cleanedClass = cleanedClass.replace(/public\s+(\w+):\s*([^=;\n]*)(?=[=;])/g, 'public $1: $2 ');

// Fix any remaining catch blocks that might be malformed
cleanedClass = cleanedClass.replace(/}\s*catch\s*\(([^)]*)\)\s*{(?!\s*[}\n])/g, '} catch ($1) {\n    ');

// Fix if statements with missing braces
cleanedClass = cleanedClass.replace(/if\s*\(([^)]*)\)\s*([^{;\n][^\n;]*);/g, 'if ($1) { $2; }');

// Reconstruct the file with the fixed class content
const fixedContent = content.substring(0, classStartIndex) + cleanedClass;

// Write the fixed content back to the file
console.log('Writing fixed content back to VehicleManager.ts...');
fs.writeFileSync(filePath, fixedContent, 'utf8');

console.log('Fixed basic syntax issues in VehicleManager.ts.');
console.log('Run the TypeScript linter to check for remaining issues.'); 