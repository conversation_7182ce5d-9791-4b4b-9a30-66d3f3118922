// Professional B2B SaaS Website JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeNavigation();
    initializeMobileMenu();
    initializeScrollEffects();
    initializeAnalytics();
    initializeTabs();
});

// Navigation functionality
function initializeNavigation() {
    const navLinks = document.querySelectorAll('.nav-link[href^="#"]');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                const navHeight = document.querySelector('.navbar').offsetHeight;
                const targetPosition = targetElement.offsetTop - navHeight;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
                
                // Close mobile menu if open
                closeMobileMenu();
            }
        });
    });
}

// Mobile menu functionality
function initializeMobileMenu() {
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.getElementById('nav-menu');
    
    if (navToggle && navMenu) {
        navToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            navToggle.classList.toggle('active');
            
            // Animate hamburger bars
            const bars = navToggle.querySelectorAll('.bar');
            bars.forEach((bar, index) => {
                bar.style.transform = navToggle.classList.contains('active') 
                    ? getBarTransform(index) 
                    : 'none';
            });
        });
        
        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!navToggle.contains(e.target) && !navMenu.contains(e.target)) {
                closeMobileMenu();
            }
        });
    }
}

function closeMobileMenu() {
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.getElementById('nav-menu');
    
    if (navToggle && navMenu) {
        navMenu.classList.remove('active');
        navToggle.classList.remove('active');
        
        const bars = navToggle.querySelectorAll('.bar');
        bars.forEach(bar => {
            bar.style.transform = 'none';
        });
    }
}

function getBarTransform(index) {
    switch(index) {
        case 0: return 'rotate(45deg) translate(5px, 5px)';
        case 1: return 'opacity(0)';
        case 2: return 'rotate(-45deg) translate(7px, -6px)';
        default: return 'none';
    }
}

// Scroll effects
function initializeScrollEffects() {
    const navbar = document.querySelector('.navbar');
    let lastScrollTop = 0;
    
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // Add/remove scrolled class
        if (scrollTop > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
        
        // Hide/show navbar on scroll (optional)
        if (scrollTop > lastScrollTop && scrollTop > 100) {
            navbar.style.transform = 'translateY(-100%)';
        } else {
            navbar.style.transform = 'translateY(0)';
        }
        
        lastScrollTop = scrollTop;
    });
    
    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe cards for animation
    const cards = document.querySelectorAll('.platform-card, .deployment-card');
    cards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
}

// Analytics and tracking
function initializeAnalytics() {
    // Track CTA clicks
    const ctaButtons = document.querySelectorAll('.btn-primary, .nav-cta');
    ctaButtons.forEach(button => {
        button.addEventListener('click', function() {
            const buttonText = this.textContent.trim();
            trackEvent('CTA Click', {
                button_text: buttonText,
                button_location: getButtonLocation(this)
            });
        });
    });
    
    // Track section views
    const sections = document.querySelectorAll('section[id]');
    const sectionObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                trackEvent('Section View', {
                    section: entry.target.id
                });
            }
        });
    }, { threshold: 0.5 });
    
    sections.forEach(section => {
        sectionObserver.observe(section);
    });
}

function getButtonLocation(button) {
    if (button.closest('.hero')) return 'hero';
    if (button.closest('.navbar')) return 'navbar';
    if (button.closest('.deployment')) return 'deployment';
    if (button.closest('.demo')) return 'demo';
    return 'other';
}

function trackEvent(eventName, properties = {}) {
    // Placeholder for analytics tracking
    console.log('Analytics Event:', eventName, properties);
    
    // Example integration with Google Analytics
    if (typeof gtag !== 'undefined') {
        gtag('event', eventName, properties);
    }
    
    // Example integration with other analytics platforms
    if (typeof analytics !== 'undefined') {
        analytics.track(eventName, properties);
    }
}

// Contact form handling (if added later)
function initializeContactForm() {
    const contactForm = document.querySelector('#contact-form');
    
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            // Track form submission
            trackEvent('Contact Form Submit', {
                form_type: 'contact',
                company: data.company || 'not_provided'
            });
            
            // Handle form submission
            submitContactForm(data);
        });
    }
}

function submitContactForm(data) {
    // Placeholder for form submission logic
    console.log('Contact form submitted:', data);
    
    // Show success message
    showFormMessage('Thank you for your interest! We\'ll be in touch within 24 hours.', 'success');
}

function showFormMessage(message, type = 'success') {
    const messageDiv = document.createElement('div');
    messageDiv.className = `form-message form-message-${type}`;
    messageDiv.textContent = message;
    
    const form = document.querySelector('#contact-form');
    if (form) {
        form.insertBefore(messageDiv, form.firstChild);
        
        setTimeout(() => {
            messageDiv.remove();
        }, 5000);
    }
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Performance optimization
const optimizedScrollHandler = debounce(function() {
    // Handle scroll events here if needed
}, 16); // ~60fps

// Keyboard navigation support
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeMobileMenu();
    }
});

// Page load tracking
window.addEventListener('load', function() {
    trackEvent('Page Load', {
        page: 'homepage',
        load_time: performance.now()
    });
});

// Exit intent tracking (desktop only)
if (!window.matchMedia('(max-width: 768px)').matches) {
    document.addEventListener('mouseleave', function(e) {
        if (e.clientY <= 0) {
            trackEvent('Exit Intent', {
                page: 'homepage'
            });
        }
    });
}

// Tab functionality
function initializeTabs() {
    // Target Markets tabs
    const marketTabBtns = document.querySelectorAll('.tab-btn');
    const marketTabContents = document.querySelectorAll('.tab-content');

    marketTabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const targetTab = btn.getAttribute('data-tab');

            // Remove active class from all buttons and contents
            marketTabBtns.forEach(b => b.classList.remove('active'));
            marketTabContents.forEach(content => content.classList.remove('active'));

            // Add active class to clicked button and corresponding content
            btn.classList.add('active');
            const targetContent = document.getElementById(targetTab);
            if (targetContent) {
                targetContent.classList.add('active');
            }

            // Track tab interaction
            trackEvent('Market Tab Click', {
                tab: targetTab
            });
        });
    });

    // Technology tabs
    const techTabBtns = document.querySelectorAll('.tech-tab-btn');
    const techTabContents = document.querySelectorAll('.tech-tab-content');

    techTabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const targetTab = btn.getAttribute('data-tab');

            // Remove active class from all buttons and contents
            techTabBtns.forEach(b => b.classList.remove('active'));
            techTabContents.forEach(content => content.classList.remove('active'));

            // Add active class to clicked button and corresponding content
            btn.classList.add('active');
            const targetContent = document.getElementById(targetTab);
            if (targetContent) {
                targetContent.classList.add('active');
            }

            // Track tab interaction
            trackEvent('Tech Tab Click', {
                tab: targetTab
            });
        });
    });
}
