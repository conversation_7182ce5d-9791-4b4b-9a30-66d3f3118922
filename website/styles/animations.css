/* ===== ANIMATIONS & KEYFRAMES ===== */

/* Fade In Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Slide In From Left */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Slide In From Right */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Scale In Animation */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Bounce Animation */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Pulse Animation */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Floating Animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Gradient Animation */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Rotate Animation */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Shimmer Effect */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* ===== ANIMATION CLASSES ===== */

/* Intersection Observer Animations */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.animate-on-scroll.animated {
  opacity: 1;
  transform: translateY(0);
}

.animate-slide-left {
  opacity: 0;
  transform: translateX(-50px);
  transition: all 0.6s ease;
}

.animate-slide-left.animated {
  opacity: 1;
  transform: translateX(0);
}

.animate-slide-right {
  opacity: 0;
  transform: translateX(50px);
  transition: all 0.6s ease;
}

.animate-slide-right.animated {
  opacity: 1;
  transform: translateX(0);
}

.animate-scale {
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.6s ease;
}

.animate-scale.animated {
  opacity: 1;
  transform: scale(1);
}

/* Staggered Animations */
.stagger-animation {
  animation-delay: var(--stagger-delay, 0s);
}

/* Hover Animations */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-rotate {
  transition: transform 0.3s ease;
}

.hover-rotate:hover {
  transform: rotate(5deg);
}

/* Loading Animations */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(226, 125, 96, 0.3);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: rotate 1s linear infinite;
}

.loading-dots {
  display: flex;
  gap: 4px;
}

.loading-dots .dot {
  width: 8px;
  height: 8px;
  background: var(--primary-color);
  border-radius: 50%;
  animation: pulse 1.4s ease-in-out infinite both;
}

.loading-dots .dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dots .dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dots .dot:nth-child(3) { animation-delay: 0s; }

/* Shimmer Loading Effect */
.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Parallax Effects */
.parallax-element {
  transform: translateZ(0);
  will-change: transform;
}

/* Smooth Scroll Behavior */
.smooth-scroll {
  scroll-behavior: smooth;
}

/* ===== SPECIFIC COMPONENT ANIMATIONS ===== */

/* Hero Title Animation */
.hero-title .title-line {
  opacity: 0;
  transform: translateY(50px);
  animation: fadeIn 1s ease forwards;
}

.hero-title .title-line:nth-child(1) {
  animation-delay: 0.2s;
}

.hero-title .title-line:nth-child(2) {
  animation-delay: 0.4s;
}

/* Hero Stats Animation */
.hero-stats .stat {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeIn 0.8s ease forwards;
}

.hero-stats .stat:nth-child(1) { animation-delay: 0.6s; }
.hero-stats .stat:nth-child(2) { animation-delay: 0.8s; }
.hero-stats .stat:nth-child(3) { animation-delay: 1s; }

/* Feature Cards Stagger */
.feature-card {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeIn 0.6s ease forwards;
}

.feature-card:nth-child(1) { animation-delay: 0.1s; }
.feature-card:nth-child(2) { animation-delay: 0.2s; }
.feature-card:nth-child(3) { animation-delay: 0.3s; }
.feature-card:nth-child(4) { animation-delay: 0.4s; }
.feature-card:nth-child(5) { animation-delay: 0.5s; }
.feature-card:nth-child(6) { animation-delay: 0.6s; }

/* Demo Preview Animation */
.demo-preview {
  animation: float 6s ease-in-out infinite;
}

/* Tech Stack Items Animation */
.tech-item {
  opacity: 0;
  transform: scale(0.8);
  animation: scaleIn 0.4s ease forwards;
}

.tech-item:nth-child(1) { animation-delay: 0.1s; }
.tech-item:nth-child(2) { animation-delay: 0.2s; }
.tech-item:nth-child(3) { animation-delay: 0.3s; }
.tech-item:nth-child(4) { animation-delay: 0.4s; }
.tech-item:nth-child(5) { animation-delay: 0.5s; }

/* Pricing Cards Animation */
.pricing-card {
  opacity: 0;
  transform: translateY(50px);
  animation: fadeIn 0.8s ease forwards;
}

.pricing-card:nth-child(1) { animation-delay: 0.2s; }
.pricing-card:nth-child(2) { animation-delay: 0.4s; }
.pricing-card:nth-child(3) { animation-delay: 0.6s; }

/* Navigation Animation */
.nav-link {
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(226, 125, 96, 0.2), transparent);
  transition: left 0.5s ease;
}

.nav-link:hover::before {
  left: 100%;
}

/* Button Ripple Effect */
.btn {
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
}

.btn:active::before {
  width: 300px;
  height: 300px;
}

/* ===== RESPONSIVE ANIMATIONS ===== */

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .hero-title .title-line {
    animation-duration: 0.8s;
  }
  
  .feature-card {
    animation-duration: 0.4s;
  }
  
  .demo-preview {
    animation: none; /* Disable floating on mobile for performance */
  }
}

/* ===== UTILITY ANIMATIONS ===== */

/* Fade utilities */
.fade-in { animation: fadeIn 0.6s ease forwards; }
.fade-in-slow { animation: fadeIn 1.2s ease forwards; }
.fade-in-fast { animation: fadeIn 0.3s ease forwards; }

/* Slide utilities */
.slide-in-left { animation: slideInLeft 0.6s ease forwards; }
.slide-in-right { animation: slideInRight 0.6s ease forwards; }

/* Scale utilities */
.scale-in { animation: scaleIn 0.6s ease forwards; }

/* Delay utilities */
.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }
.delay-500 { animation-delay: 0.5s; }
