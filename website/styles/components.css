/* ===== FEATURES SECTION ===== */
.features {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: var(--spacing-xxl) 0;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.feature-card {
  background: var(--white);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--primary-gradient);
  transform: scaleX(0);
  transition: var(--transition-normal);
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-lg);
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-icon {
  width: 60px;
  height: 60px;
  background: var(--primary-gradient);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-md);
  color: var(--white);
  font-size: 1.5rem;
}

.feature-title {
  color: var(--dark-color);
  margin-bottom: var(--spacing-sm);
  font-size: 1.25rem;
}

.feature-description {
  color: #6B7280;
  margin-bottom: var(--spacing-md);
  line-height: 1.6;
}

.feature-tech {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

.tech-tag {
  background: rgba(226, 125, 96, 0.1);
  color: var(--primary-color);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ===== DEMO SECTION ===== */
.demo-section {
  padding: var(--spacing-xxl) 0;
  background: var(--white);
}

.demo-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-xl);
  align-items: start;
  margin-top: var(--spacing-xl);
}

.demo-frame {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.demo-header {
  background: #f5f5f5;
  padding: var(--spacing-sm) var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.demo-controls {
  display: flex;
  gap: 8px;
}

.demo-controls .control-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.demo-controls .red { background: #ff5f56; }
.demo-controls .yellow { background: #ffbd2e; }
.demo-controls .green { background: #27ca3f; }

.demo-url {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.875rem;
  color: #666;
  background: var(--white);
  padding: 4px 12px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.demo-content {
  height: 500px;
  background: #f0f0f0;
}

.demo-content iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.demo-features {
  background: var(--white);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.demo-features h3 {
  color: var(--dark-color);
  margin-bottom: var(--spacing-md);
  font-size: 1.25rem;
}

.feature-list {
  list-style: none;
  margin-bottom: var(--spacing-lg);
}

.feature-list li {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  color: #6B7280;
}

.feature-list i {
  color: var(--accent-color);
  font-size: 0.875rem;
}

/* ===== USE CASES SECTION ===== */
.use-cases {
  background: linear-gradient(135deg, var(--dark-color) 0%, #34495e 100%);
  color: var(--white);
  padding: var(--spacing-xxl) 0;
}

.use-cases .section-title {
  color: var(--white);
}

.use-cases .section-subtitle {
  color: rgba(255, 255, 255, 0.8);
}

.use-cases-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.use-case-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: var(--transition-normal);
}

.use-case-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
}

.use-case-icon {
  width: 50px;
  height: 50px;
  background: var(--primary-gradient);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-md);
  font-size: 1.25rem;
}

.use-case-title {
  color: var(--white);
  margin-bottom: var(--spacing-sm);
  font-size: 1.25rem;
}

.use-case-description {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--spacing-md);
}

.use-case-examples {
  list-style: none;
}

.use-case-examples li {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 4px;
  font-size: 0.875rem;
}

.use-case-examples li::before {
  content: '→';
  margin-right: var(--spacing-xs);
  color: var(--accent-color);
}

/* ===== TECHNOLOGY SECTION ===== */
.technology {
  padding: var(--spacing-xxl) 0;
  background: var(--white);
}

.tech-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
}

.tech-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.tech-feature {
  margin-bottom: var(--spacing-lg);
}

.tech-feature h4 {
  color: var(--dark-color);
  margin-bottom: var(--spacing-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.tech-feature i {
  color: var(--primary-color);
}

.tech-feature p {
  color: #6B7280;
  margin-bottom: var(--spacing-sm);
}

.tech-stack {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.tech-item {
  background: rgba(0, 71, 171, 0.1);
  color: var(--secondary-color);
  padding: 6px 12px;
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  font-weight: 600;
}

.tech-visual {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.architecture-diagram {
  width: 100%;
  max-width: 400px;
  height: 300px;
  background: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.diagram-layer {
  width: 80%;
  height: 40px;
  margin: 8px 0;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--white);
  position: relative;
}

.layer-ui { background: var(--primary-gradient); }
.layer-animation { background: var(--secondary-gradient); }
.layer-data { background: var(--accent-color); }
.layer-map { background: linear-gradient(135deg, #6c757d 0%, #495057 100%); }

/* ===== PRICING SECTION ===== */
.pricing {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: var(--spacing-xxl) 0;
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.pricing-card {
  background: var(--white);
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(0, 0, 0, 0.05);
  text-align: center;
  position: relative;
  transition: var(--transition-normal);
}

.pricing-card.featured {
  border: 2px solid var(--primary-color);
  transform: scale(1.05);
}

.pricing-card.featured::before {
  content: 'Most Popular';
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--primary-gradient);
  color: var(--white);
  padding: 6px 20px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

.pricing-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.pricing-card.featured:hover {
  transform: scale(1.05) translateY(-5px);
}

.pricing-title {
  color: var(--dark-color);
  margin-bottom: var(--spacing-sm);
}

.pricing-price {
  font-size: 3rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
  font-family: var(--font-display);
}

.pricing-period {
  color: #6B7280;
  margin-bottom: var(--spacing-lg);
}

.pricing-features {
  list-style: none;
  margin-bottom: var(--spacing-lg);
  text-align: left;
}

.pricing-features li {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  color: #6B7280;
}

.pricing-features i {
  color: var(--accent-color);
  font-size: 0.875rem;
}

/* ===== CONTACT SECTION ===== */
.contact {
  background: var(--dark-color);
  color: var(--white);
  padding: var(--spacing-xxl) 0;
}

.contact .section-title {
  color: var(--white);
}

.contact .section-subtitle {
  color: rgba(255, 255, 255, 0.8);
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
}

.contact-info h4 {
  color: var(--white);
  margin-bottom: var(--spacing-md);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  color: rgba(255, 255, 255, 0.8);
}

.contact-item i {
  color: var(--primary-color);
  font-size: 1.25rem;
  width: 20px;
}

.contact-form {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-group {
  margin-bottom: var(--spacing-md);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  color: var(--white);
  font-weight: 500;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--border-radius);
  background: rgba(255, 255, 255, 0.1);
  color: var(--white);
  font-family: var(--font-primary);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  background: rgba(255, 255, 255, 0.15);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

/* ===== FOOTER ===== */
.footer {
  background: var(--dark-color);
  color: var(--white);
  padding: var(--spacing-xl) 0 var(--spacing-md);
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
}

.footer-brand p {
  color: rgba(255, 255, 255, 0.7);
  margin-top: var(--spacing-sm);
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);
}

.footer-column h5 {
  color: var(--white);
  margin-bottom: var(--spacing-md);
  font-size: 1rem;
  font-weight: 600;
}

.footer-column a {
  display: block;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: var(--spacing-xs);
  transition: var(--transition-fast);
}

.footer-column a:hover {
  color: var(--primary-color);
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: var(--spacing-md);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-bottom p {
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
}

.footer-social {
  display: flex;
  gap: var(--spacing-md);
}

.footer-social a {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  transition: var(--transition-fast);
}

.footer-social a:hover {
  background: var(--primary-color);
  color: var(--white);
  transform: translateY(-2px);
}
