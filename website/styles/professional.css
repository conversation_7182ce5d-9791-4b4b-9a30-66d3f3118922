/* ===== PROFESSIONAL B2B SAAS STYLES ===== */

/* CSS Variables */
:root {
  /* Professional Color Palette */
  --primary-blue: #0066CC;
  --primary-blue-dark: #004499;
  --secondary-blue: #E6F2FF;
  --accent-yellow: #FFCC00;
  --accent-yellow-light: #FFF8E1;
  --accent-green: #28A745;
  --neutral-dark: #1A1A1A;
  --neutral-medium: #666666;
  --neutral-light: #F8F9FA;
  --white: #FFFFFF;
  --border-color: #E1E5E9;
  
  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-secondary: 'IBM Plex Sans', sans-serif;
  
  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 2rem;
  --spacing-lg: 3rem;
  --spacing-xl: 4rem;
  --spacing-xxl: 6rem;
  
  /* Borders & Shadows */
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.08);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.12);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.16);
  
  /* Transitions */
  --transition: 0.3s ease;
}

/* Reset & Base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-primary);
  line-height: 1.6;
  color: var(--neutral-dark);
  background-color: var(--white);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-secondary);
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: var(--spacing-sm);
}

h1 { font-size: 3rem; }
h2 { font-size: 2.25rem; }
h3 { font-size: 1.5rem; }

p {
  margin-bottom: var(--spacing-sm);
  color: var(--neutral-medium);
}

/* Layout */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.section {
  padding: var(--spacing-xxl) 0;
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.section-title {
  color: var(--neutral-dark);
  margin-bottom: var(--spacing-sm);
}

.section-subtitle {
  font-size: 1.125rem;
  color: var(--neutral-medium);
  max-width: 600px;
  margin: 0 auto;
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #000000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  transition: var(--transition);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.nav-brand {
  display: flex;
  align-items: center;
}

.brand-text {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  font-size: 1.75rem;
  font-weight: 600;
  letter-spacing: -0.3px;
  color: var(--white);
}

.brand-accent {
  color: var(--accent-yellow);
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.nav-link {
  font-weight: 500;
  color: var(--white);
  text-decoration: none;
  transition: var(--transition);
  position: relative;
}

.nav-link:hover {
  color: var(--accent-yellow);
}

.nav-cta {
  background: var(--accent-yellow);
  color: var(--white);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--border-radius);
  font-weight: 600;
  text-decoration: none;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
}

.nav-cta:hover {
  background: #e91e63;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.bar {
  width: 25px;
  height: 3px;
  background: var(--white);
  transition: var(--transition);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius);
  font-family: var(--font-primary);
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  font-size: 1rem;
}

.btn-primary {
  background: var(--primary-blue);
  color: var(--white);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  background: var(--primary-blue-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: transparent;
  color: var(--white);
  border: 2px solid var(--white);
}

.btn-secondary:hover {
  background: var(--white);
  color: var(--primary-blue);
}

.btn-large {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: 1.125rem;
}

/* Hero Section */
.hero {
  padding: calc(70px + var(--spacing-xl)) 0 var(--spacing-xxl);
  background: var(--primary-blue);
  position: relative;
}

.hero-content {
  text-align: center;
  max-width: 900px;
  margin: 0 auto;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: rgba(255, 255, 255, 0.15);
  color: var(--white);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: 50px;
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-title {
  font-size: 3rem;
  font-weight: 700;
  color: var(--white);
  margin-bottom: var(--spacing-md);
  line-height: 1.1;
}

.highlight {
  color: var(--accent-yellow);
  font-size: 0.8em;
}

.hero-subtitle {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--spacing-xl);
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: var(--white);
  font-family: var(--font-secondary);
}

.stat-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

/* Hero Screenshot */
.hero-screenshot {
  margin: var(--spacing-xxl) 0;
  display: flex;
  justify-content: center;
}

.screenshot-container {
  position: relative;
  max-width: 900px;
  width: 100%;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-xl);
  transition: var(--transition);
}

.screenshot-container:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.app-screenshot {
  width: 100%;
  height: auto;
  display: block;
  border-radius: var(--border-radius-lg);
}

.screenshot-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.05) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition);
}

.screenshot-container:hover .screenshot-overlay {
  opacity: 1;
}

.screenshot-badge {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--primary-blue);
  font-weight: 600;
  font-size: 0.875rem;
  box-shadow: var(--shadow-md);
  transform: translateY(10px);
  transition: var(--transition);
}

.screenshot-container:hover .screenshot-badge {
  transform: translateY(0);
}

.screenshot-badge i {
  color: var(--accent-yellow);
}

.hero-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  margin-bottom: var(--spacing-xl);
  flex-wrap: wrap;
}

.hero-clients {
  margin-top: var(--spacing-xl);
}

.clients-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--spacing-md);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.client-types {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.client-type {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: var(--white);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  font-weight: 500;
  color: var(--neutral-dark);
  box-shadow: var(--shadow-sm);
  font-size: 0.875rem;
}

.client-type i {
  color: var(--primary-blue);
}

/* Platform Section */
.platform {
  padding: var(--spacing-xxl) 0;
  background: var(--white);
}

.platform-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.platform-card {
  background: var(--white);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  transition: var(--transition);
  position: relative;
}

.platform-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-blue);
}

.platform-icon {
  width: 60px;
  height: 60px;
  background: var(--secondary-blue);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-md);
  color: var(--primary-blue);
  font-size: 1.5rem;
}

.platform-title {
  color: var(--neutral-dark);
  margin-bottom: var(--spacing-sm);
  font-size: 1.25rem;
}

.platform-description {
  color: var(--neutral-medium);
  margin-bottom: var(--spacing-md);
  line-height: 1.6;
}

.platform-tech {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

.tech-tag {
  background: var(--secondary-blue);
  color: var(--primary-blue);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Deployment Section */
.deployment {
  padding: var(--spacing-xxl) 0;
  background: var(--neutral-light);
}

.deployment-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.deployment-card {
  background: var(--white);
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  transition: var(--transition);
  position: relative;
  text-align: center;
}

.deployment-card.featured {
  border: 2px solid var(--primary-blue);
  transform: scale(1.02);
}

.deployment-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--accent-yellow);
  color: var(--white);
  padding: 6px 20px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

.deployment-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.deployment-card.featured:hover {
  transform: scale(1.02) translateY(-4px);
}

.deployment-icon {
  width: 80px;
  height: 80px;
  background: var(--secondary-blue);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-md);
  color: var(--primary-blue);
  font-size: 2rem;
}

.deployment-title {
  color: var(--neutral-dark);
  margin-bottom: var(--spacing-sm);
  font-size: 1.5rem;
}

.deployment-description {
  color: var(--neutral-medium);
  margin-bottom: var(--spacing-lg);
  line-height: 1.6;
}

.deployment-features {
  text-align: left;
  margin-bottom: var(--spacing-lg);
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  color: var(--neutral-medium);
}

.feature-item i {
  color: var(--accent-green);
  font-size: 0.875rem;
}

.deployment-pricing {
  background: var(--secondary-blue);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-md);
}

.price {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-blue);
  font-family: var(--font-secondary);
}

.price-period {
  font-size: 0.875rem;
  color: var(--neutral-medium);
}

.deployment-ideal {
  font-size: 0.875rem;
  color: var(--neutral-medium);
  text-align: left;
  padding: var(--spacing-md);
  background: var(--neutral-light);
  border-radius: var(--border-radius);
}

/* Target Markets Section */
.target-markets {
  padding: var(--spacing-xxl) 0;
  background: var(--neutral-light);
}

/* Market Tabs */
.markets-tabs {
  margin-top: var(--spacing-xl);
}

.tab-navigation {
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xl);
  border-bottom: 2px solid var(--border-color);
  padding-bottom: var(--spacing-md);
}

.tab-btn {
  background: none;
  border: none;
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: 1rem;
  font-weight: 500;
  color: var(--neutral-medium);
  cursor: pointer;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  transition: var(--transition);
  position: relative;
}

.tab-btn:hover {
  color: var(--primary-blue);
  background: var(--secondary-blue);
}

.tab-btn.active {
  color: var(--primary-blue);
  background: var(--white);
  border: 1px solid var(--border-color);
  border-bottom: 2px solid var(--white);
  margin-bottom: -2px;
}

.tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 3px;
  background: var(--accent-yellow);
  border-radius: 2px;
}

.tab-content-container {
  position: relative;
  min-height: 400px;
}

.tab-content {
  display: none;
  animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.markets-container {
  margin-top: var(--spacing-xl);
}

.market-segment {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--spacing-xl);
  overflow: hidden;
  border: 1px solid var(--border-color);
  transition: var(--transition);
  position: relative;
}

.market-segment:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.market-segment.featured {
  border: 2px solid var(--primary-blue);
  position: relative;
}

.market-segment.featured::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-blue), var(--accent-coral));
  z-index: 1;
}

.market-intro {
  display: flex;
  align-items: center;
  padding: var(--spacing-xl);
  background: linear-gradient(135deg, var(--secondary-blue) 0%, var(--white) 100%);
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

.market-number {
  font-size: 3rem;
  font-weight: 700;
  color: var(--accent-yellow);
  font-family: var(--font-secondary);
  margin-right: var(--spacing-lg);
  line-height: 1;
  opacity: 0.8;
}

.market-info {
  flex: 1;
}

.market-title {
  font-size: 1.75rem;
  color: var(--neutral-dark);
  margin-bottom: var(--spacing-xs);
  font-weight: 600;
}

.market-subtitle {
  color: var(--neutral-medium);
  font-size: 1rem;
  margin-bottom: 0;
}

.market-icon {
  width: 60px;
  height: 60px;
  background: var(--primary-blue);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 1.5rem;
  margin-left: auto;
}

.featured-badge {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  background: var(--accent-yellow);
  color: var(--white);
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  z-index: 2;
}

.market-details {
  padding: var(--spacing-xl);
}

.market-description {
  margin-bottom: var(--spacing-lg);
}

.market-description h4 {
  font-size: 1.25rem;
  color: var(--neutral-dark);
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
}

.market-description p {
  color: var(--neutral-medium);
  line-height: 1.6;
  font-size: 1rem;
}

.market-features {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
}

.feature-column h5 {
  color: var(--neutral-dark);
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.feature-column h5::before {
  content: '';
  width: 4px;
  height: 20px;
  background: var(--accent-yellow);
  border-radius: 2px;
}

.feature-column ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.feature-column li {
  color: var(--neutral-medium);
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: var(--spacing-sm);
  position: relative;
  padding-left: var(--spacing-md);
}

.feature-column li::before {
  content: '→';
  position: absolute;
  left: 0;
  color: var(--primary-blue);
  font-weight: 600;
}

.platform-strength {
  background: linear-gradient(135deg, var(--accent-yellow-light) 0%, var(--secondary-blue) 100%);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--accent-yellow);
}

.strength-label {
  font-weight: 600;
  color: var(--neutral-dark);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.strength-text {
  color: var(--neutral-medium);
  font-size: 0.875rem;
  margin-left: var(--spacing-sm);
}

.value-proposition,
.key-benefits,
.customization-features {
  margin-bottom: var(--spacing-lg);
}

.value-proposition h4,
.key-benefits h4,
.customization-features h4 {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--neutral-dark);
  font-size: 1rem;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
}

.value-proposition h4 i {
  color: var(--accent-orange);
}

.key-benefits h4 i {
  color: var(--accent-green);
}

.customization-features h4 i {
  color: var(--primary-blue);
}

.benefits-list,
.customization-features ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.benefits-list li,
.customization-features li {
  margin-bottom: var(--spacing-xs);
  color: var(--neutral-medium);
  font-size: 0.875rem;
  line-height: 1.5;
}

.benefits-list li strong {
  color: var(--neutral-dark);
  font-weight: 600;
}

.customization-features li {
  position: relative;
  padding-left: var(--spacing-md);
}

.customization-features li::before {
  content: '→';
  position: absolute;
  left: 0;
  color: var(--primary-blue);
  font-weight: 600;
}

.value-proposition p {
  color: var(--neutral-medium);
  line-height: 1.6;
  font-size: 0.875rem;
}

.platform-strength {
  margin-top: var(--spacing-md);
}

.strength-highlight {
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-dark));
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  text-align: center;
  color: var(--white);
}

.strength-title {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--spacing-xs);
  opacity: 0.9;
}

.strength-description {
  font-size: 0.875rem;
  line-height: 1.4;
  font-weight: 500;
}

/* Technology Section */
.technology {
  padding: var(--spacing-xxl) 0;
  background: var(--white);
}

.tech-overview {
  margin-bottom: var(--spacing-xxl);
}

.tech-intro {
  text-align: center;
  max-width: 800px;
  margin: 0 auto var(--spacing-xl);
}

.tech-intro h3 {
  color: var(--neutral-dark);
  font-size: 1.75rem;
  margin-bottom: var(--spacing-md);
  font-weight: 600;
}

.tech-intro p {
  color: var(--neutral-medium);
  font-size: 1.125rem;
  line-height: 1.6;
}

.tech-visual {
  margin-top: var(--spacing-xl);
  text-align: center;
}

.tech-image-placeholder {
  background: linear-gradient(135deg, var(--secondary-blue) 0%, var(--accent-yellow-light) 100%);
  border: 2px dashed var(--accent-yellow);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xxl);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  color: var(--neutral-medium);
  max-width: 600px;
  margin: 0 auto;
}

.tech-image-placeholder i {
  font-size: 3rem;
  color: var(--accent-yellow);
  opacity: 0.7;
}

.tech-image-placeholder span {
  font-size: 1rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tech-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-lg);
  max-width: 600px;
  margin: var(--spacing-xl) auto;
  justify-content: center;
  width: 100%;
}

.tech-stat {
  text-align: center;
  padding: var(--spacing-md);
  background: var(--secondary-blue);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--accent-yellow);
}

.tech-stat .stat-number {
  display: block;
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--primary-blue);
  font-family: var(--font-secondary);
  margin-bottom: var(--spacing-xs);
}

.tech-stat .stat-label {
  font-size: 0.875rem;
  color: var(--neutral-medium);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Technology Tabs */
.tech-tabs {
  margin-bottom: var(--spacing-xxl);
  display: flex;
  gap: var(--spacing-xl);
  align-items: flex-start;
}

.tech-tab-navigation {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  min-width: 280px;
  flex-shrink: 0;
}

.tech-tab-btn {
  background: var(--white);
  border: 1px solid var(--border-color);
  padding: var(--spacing-md);
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--neutral-medium);
  cursor: pointer;
  border-radius: var(--border-radius);
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  text-align: left;
  width: 100%;
}

.tech-tab-btn i {
  font-size: 1rem;
  width: 20px;
  text-align: center;
  color: var(--accent-yellow);
}

.tech-tab-btn:hover {
  color: var(--primary-blue);
  border-color: var(--primary-blue);
  background: var(--secondary-blue);
  transform: translateX(4px);
}

.tech-tab-btn.active {
  color: var(--primary-blue);
  border-color: var(--accent-yellow);
  background: var(--secondary-blue);
  border-left: 4px solid var(--accent-yellow);
  transform: translateX(4px);
  box-shadow: var(--shadow-md);
}

.tech-tab-content-container {
  flex: 1;
  position: relative;
  min-height: 500px;
}

.tech-tab-content {
  display: none;
  animation: fadeIn 0.3s ease-in-out;
}

.tech-tab-content.active {
  display: block;
}

.tech-components {
  margin-bottom: var(--spacing-xxl);
}

.tech-component {
  background: var(--neutral-light);
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--spacing-xl);
  overflow: hidden;
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.tech-component:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.component-header {
  display: flex;
  align-items: center;
  padding: var(--spacing-xl);
  background: var(--white);
  border-bottom: 1px solid var(--border-color);
}

.component-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-blue), var(--accent-yellow));
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 1.5rem;
  margin-right: var(--spacing-lg);
  flex-shrink: 0;
}

.component-info h4 {
  color: var(--neutral-dark);
  font-size: 1.5rem;
  margin-bottom: var(--spacing-xs);
  font-weight: 600;
}

.component-subtitle {
  color: var(--neutral-medium);
  font-size: 1rem;
  margin: 0;
}

.component-details {
  padding: var(--spacing-xl);
  background: var(--white);
}

.tech-description {
  margin-bottom: var(--spacing-lg);
}

.tech-description p {
  color: var(--neutral-medium);
  font-size: 1rem;
  line-height: 1.6;
}

.tech-features {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
}

.feature-group h5 {
  color: var(--neutral-dark);
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.feature-group h5::before {
  content: '';
  width: 4px;
  height: 20px;
  background: var(--accent-coral);
  border-radius: 2px;
}

.feature-group ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.feature-group li {
  color: var(--neutral-medium);
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: var(--spacing-sm);
  position: relative;
  padding-left: var(--spacing-md);
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.feature-group li::before {
  content: '→';
  position: absolute;
  left: 0;
  color: var(--primary-blue);
  font-weight: 600;
}

.feature-group li strong {
  color: var(--neutral-dark);
  font-weight: 600;
}

/* Code Snippets */
.code-example {
  background: var(--neutral-dark);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  margin: var(--spacing-md) 0;
  position: relative;
  overflow-x: auto;
}

.code-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.code-title {
  color: var(--accent-yellow);
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.code-language {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.75rem;
  text-transform: uppercase;
}

.code-content {
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #E6E6E6;
  white-space: pre;
  overflow-x: auto;
}

.code-content .comment {
  color: #6A9955;
}

.code-content .keyword {
  color: #569CD6;
}

.code-content .string {
  color: #CE9178;
}

.code-content .property {
  color: #9CDCFE;
}

.code-content .value {
  color: #B5CEA8;
}

/* White-Label Section */
.white-label-section {
  margin-top: var(--spacing-xxl);
  padding: var(--spacing-xl);
  background: linear-gradient(135deg, var(--secondary-blue) 0%, var(--accent-yellow-light) 100%);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
}

.white-label-section .section-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.white-label-section .section-header h3 {
  color: var(--neutral-dark);
  font-size: 1.75rem;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
}

.white-label-section .section-header p {
  color: var(--neutral-medium);
  font-size: 1rem;
}

.architecture-summary {
  background: linear-gradient(135deg, var(--secondary-blue) 0%, var(--accent-yellow-light) 100%);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
}

.summary-content h3 {
  text-align: center;
  color: var(--neutral-dark);
  font-size: 1.75rem;
  margin-bottom: var(--spacing-xl);
  font-weight: 600;
}

.architecture-features {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-xl);
}

.arch-feature {
  background: var(--white);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  text-align: center;
  transition: var(--transition);
}

.arch-feature:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.arch-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-blue), var(--accent-yellow));
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 1.5rem;
  margin: 0 auto var(--spacing-md);
}

.arch-feature h4 {
  color: var(--neutral-dark);
  font-size: 1.125rem;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
}

.arch-feature p {
  color: var(--neutral-medium);
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Platform Summary */
.platform-summary {
  margin-top: var(--spacing-xxl);
  background: var(--white);
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  text-align: center;
}

.summary-header h3 {
  color: var(--neutral-dark);
  margin-bottom: var(--spacing-xs);
  font-size: 1.75rem;
}

.summary-header p {
  color: var(--neutral-medium);
  margin-bottom: var(--spacing-lg);
}

.summary-features {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-lg);
}

.summary-feature {
  text-align: center;
}

.summary-feature .feature-icon {
  width: 60px;
  height: 60px;
  background: var(--secondary-blue);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-md);
  color: var(--primary-blue);
  font-size: 1.5rem;
}

.summary-feature h4 {
  color: var(--neutral-dark);
  margin-bottom: var(--spacing-xs);
  font-size: 1rem;
  font-weight: 600;
}

.summary-feature p {
  color: var(--neutral-medium);
  font-size: 0.875rem;
  line-height: 1.4;
}

/* Pricing Section */
.pricing {
  padding: var(--spacing-xxl) 0;
  background: var(--white);
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.pricing-card {
  background: var(--white);
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  transition: var(--transition);
  position: relative;
  text-align: center;
}

.pricing-card.featured {
  border: 2px solid var(--primary-blue);
  transform: scale(1.05);
}

.pricing-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--primary-blue);
  color: var(--white);
  padding: 6px 20px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

.pricing-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.pricing-card.featured:hover {
  transform: scale(1.05) translateY(-4px);
}

.pricing-title {
  color: var(--neutral-dark);
  margin-bottom: var(--spacing-sm);
  font-size: 1.5rem;
}

.pricing-description {
  color: var(--neutral-medium);
  margin-bottom: var(--spacing-lg);
  font-size: 0.875rem;
  line-height: 1.5;
  padding: var(--spacing-sm);
  background: var(--secondary-blue);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--accent-yellow);
}

.pricing-features {
  list-style: none;
  margin-bottom: var(--spacing-lg);
  text-align: left;
}

.pricing-features li {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  color: var(--neutral-medium);
  font-size: 0.875rem;
}

.pricing-features i {
  color: var(--accent-green);
  font-size: 0.75rem;
}

.pricing-ideal {
  background: var(--secondary-blue);
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-lg);
  font-size: 0.875rem;
  color: var(--neutral-medium);
  font-style: italic;
}

/* Contact Section */
.contact {
  padding: var(--spacing-xxl) 0;
  background: var(--neutral-dark);
  color: var(--white);
}

.contact .section-title {
  color: var(--white);
}

.contact .section-subtitle {
  color: rgba(255, 255, 255, 0.8);
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
}

.contact-info h4 {
  color: var(--white);
  margin-bottom: var(--spacing-md);
  font-size: 1.25rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  color: rgba(255, 255, 255, 0.8);
}

.contact-item i {
  color: var(--primary-blue);
  font-size: 1.25rem;
  width: 20px;
}

.contact-cta {
  margin-top: var(--spacing-lg);
}

.contact-form-container {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
}

.form-group {
  margin-bottom: var(--spacing-md);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  color: var(--white);
  font-weight: 500;
  font-size: 0.875rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius);
  background: rgba(255, 255, 255, 0.1);
  color: var(--white);
  font-family: var(--font-primary);
  font-size: 0.875rem;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-blue);
  background: rgba(255, 255, 255, 0.15);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    top: 70px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 70px);
    background: #000000;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding: var(--spacing-lg) 0;
    transition: var(--transition);
    box-shadow: var(--shadow-lg);
  }
  
  .nav-menu.active {
    left: 0;
  }
  
  .brand-text {
    font-size: 1.5rem;
  }

  .nav-toggle {
    display: flex;
  }
  
  .hero-title {
    font-size: 2.2rem;
  }

  .hero-subtitle {
    font-size: 0.9rem;
  }
  
  .hero-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-sm);
    max-width: 400px;
  }
  
  .hero-screenshot {
    margin: var(--spacing-xl) 0;
  }

  .screenshot-container {
    max-width: 100%;
    margin: 0 var(--spacing-md);
  }

  .screenshot-container:hover {
    transform: translateY(-4px);
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .btn-large {
    width: 100%;
    max-width: 280px;
  }
  
  .client-types {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
  }
  
  .platform-grid,
  .deployment-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .deployment-card.featured {
    transform: none;
    order: -1;
  }

  /* Target Markets Mobile */
  .market-intro {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
  }

  .market-number {
    font-size: 2rem;
    margin-right: 0;
    margin-bottom: var(--spacing-sm);
  }

  .market-icon {
    margin-left: 0;
    margin-top: var(--spacing-md);
  }

  .market-features {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .market-details {
    padding: var(--spacing-md);
  }

  .summary-features {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }

  /* Technology Mobile */
  .tech-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }

  .component-header {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }

  .component-icon {
    margin-right: 0;
    margin-bottom: var(--spacing-md);
  }

  .component-details {
    padding: var(--spacing-md);
    overflow-x: hidden;
  }

  .component-details .tech-description p {
    font-size: 0.9rem;
    line-height: 1.5;
  }

  .tech-features {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .architecture-features {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .architecture-summary {
    padding: var(--spacing-lg);
  }

  /* Tabs Mobile */
  .tab-navigation {
    flex-wrap: wrap;
    gap: var(--spacing-xs);
  }

  .tab-btn {
    font-size: 0.875rem;
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .tech-tabs {
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  .tech-tab-navigation {
    min-width: 100%;
    flex-direction: row;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
  }

  .tech-tab-btn {
    flex: 1;
    min-width: 140px;
    font-size: 0.75rem;
    padding: var(--spacing-sm);
    transform: none !important;
  }

  .tech-tab-btn:hover {
    transform: none;
  }

  .tech-tab-btn.active {
    transform: none;
    border-left: 1px solid var(--accent-yellow);
    border-bottom: 3px solid var(--accent-yellow);
  }

  .tech-tab-btn span {
    display: block;
    font-size: 0.75rem;
    margin-top: 4px;
  }

  .tech-tab-btn i {
    margin: 0;
  }

  /* Pricing Mobile */
  .pricing-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .pricing-card.featured {
    transform: none;
    order: -1;
  }

  /* White-Label Section Mobile */
  .white-label-section {
    margin-left: 0;
    margin-right: 0;
    width: 100%;
    padding: var(--spacing-md);
    overflow-x: hidden;
  }

  .white-label-section .feature-group li {
    font-size: 0.8rem;
    line-height: 1.4;
    padding-right: var(--spacing-md);
    max-width: 100%;
    box-sizing: border-box;
  }

  .white-label-section .tech-features {
    padding: 0 var(--spacing-sm);
  }

  .white-label-section .feature-group {
    padding-right: var(--spacing-md);
  }

  /* Contact Mobile */
  .contact-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }
}
