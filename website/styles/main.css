/* ===== CSS VARIABLES ===== */
:root {
  /* Colors */
  --primary-color: #E27D60;
  --secondary-color: #0047AB;
  --accent-color: #41B3A3;
  --dark-color: #2C3E50;
  --light-color: #F8F9FA;
  --white: #FFFFFF;
  --black: #000000;
  
  /* Gradients */
  --primary-gradient: linear-gradient(135deg, #E27D60 0%, #C85A3A 100%);
  --secondary-gradient: linear-gradient(135deg, #0047AB 0%, #003080 100%);
  --hero-gradient: linear-gradient(135deg, rgba(226, 125, 96, 0.9) 0%, rgba(0, 71, 171, 0.9) 100%);
  
  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-display: 'Playfair Display', serif;
  
  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 2rem;
  --spacing-lg: 3rem;
  --spacing-xl: 4rem;
  --spacing-xxl: 6rem;
  
  /* Borders */
  --border-radius: 8px;
  --border-radius-lg: 16px;
  --border-radius-xl: 24px;
  
  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 16px 48px rgba(0, 0, 0, 0.25);
  
  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* ===== RESET & BASE ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-primary);
  line-height: 1.6;
  color: var(--dark-color);
  background-color: var(--white);
  overflow-x: hidden;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-display);
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: var(--spacing-sm);
}

h1 { font-size: 3.5rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 2rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
  margin-bottom: var(--spacing-sm);
  color: #6B7280;
}

a {
  text-decoration: none;
  color: inherit;
  transition: var(--transition-fast);
}

/* ===== LAYOUT ===== */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.section {
  padding: var(--spacing-xxl) 0;
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.section-title {
  color: var(--dark-color);
  margin-bottom: var(--spacing-sm);
}

.section-subtitle {
  font-size: 1.25rem;
  color: #6B7280;
  max-width: 600px;
  margin: 0 auto;
}

/* ===== NAVIGATION ===== */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  z-index: var(--z-fixed);
  transition: var(--transition-normal);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-family: var(--font-display);
  font-weight: 700;
  font-size: 1.5rem;
  color: var(--primary-color);
}

.nav-icon {
  font-size: 1.75rem;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.nav-link {
  font-weight: 500;
  color: var(--dark-color);
  transition: var(--transition-fast);
  position: relative;
}

.nav-link:hover {
  color: var(--primary-color);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-gradient);
  transition: var(--transition-fast);
}

.nav-link:hover::after {
  width: 100%;
}

.nav-cta {
  background: var(--primary-gradient);
  color: var(--white);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--border-radius);
  font-weight: 600;
  transition: var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.nav-cta:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.bar {
  width: 25px;
  height: 3px;
  background: var(--dark-color);
  transition: var(--transition-fast);
}

/* ===== BUTTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius);
  font-family: var(--font-primary);
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  transition: var(--transition-fast);
  text-decoration: none;
  font-size: 1rem;
}

.btn-primary {
  background: var(--primary-gradient);
  color: var(--white);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: transparent;
  color: var(--dark-color);
  border: 2px solid var(--dark-color);
}

.btn-secondary:hover {
  background: var(--dark-color);
  color: var(--white);
}

.btn-large {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: 1.125rem;
}

/* ===== HERO SECTION ===== */
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--hero-gradient);
  z-index: -1;
}

.hero-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  width: 100%;
  z-index: 1;
}

.hero-content .container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
  align-items: center;
}

.hero-title {
  font-size: 4rem;
  font-weight: 700;
  color: var(--white);
  margin-bottom: var(--spacing-md);
  line-height: 1.1;
}

.title-line {
  display: block;
}

.highlight {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--spacing-lg);
  max-width: 500px;
}

.hero-stats {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: var(--white);
  font-family: var(--font-display);
}

.stat-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.hero-actions {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.demo-preview {
  background: var(--white);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-sm);
  box-shadow: var(--shadow-xl);
  transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
  transition: var(--transition-slow);
}

.demo-preview:hover {
  transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
}

.preview-screen {
  width: 400px;
  height: 300px;
  border-radius: var(--border-radius);
  overflow: hidden;
  background: #f0f0f0;
}

.preview-screen iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.preview-controls {
  display: flex;
  justify-content: center;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-sm);
}

.control-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ddd;
  transition: var(--transition-fast);
}

.control-dot.active {
  background: var(--primary-color);
}

.hero-scroll {
  position: absolute;
  bottom: var(--spacing-lg);
  left: 50%;
  transform: translateX(-50%);
}

.scroll-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  color: rgba(255, 255, 255, 0.8);
  transition: var(--transition-fast);
  animation: bounce 2s infinite;
}

.scroll-indicator:hover {
  border-color: var(--white);
  color: var(--white);
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

/* ===== RESPONSIVE DESIGN ===== */

/* Large screens */
@media (min-width: 1200px) {
  .container {
    max-width: 1400px;
  }

  .hero-title {
    font-size: 5rem;
  }

  .section-title {
    font-size: 3rem;
  }
}

/* Tablet */
@media (max-width: 1024px) {
  .hero-content .container {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--spacing-lg);
  }

  .hero-title {
    font-size: 3rem;
  }

  .hero-stats {
    justify-content: center;
  }

  .demo-container {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .tech-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .contact-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    text-align: center;
  }
}

/* Mobile */
@media (max-width: 768px) {
  /* Navigation */
  .nav-menu {
    position: fixed;
    top: 70px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 70px);
    background: var(--white);
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding: var(--spacing-lg) 0;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-lg);
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-toggle {
    display: flex;
  }

  .nav-cta {
    margin-top: var(--spacing-md);
  }

  /* Typography */
  h1 { font-size: 2.5rem; }
  h2 { font-size: 2rem; }
  h3 { font-size: 1.5rem; }

  .hero-title {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .section-subtitle {
    font-size: 1rem;
  }

  /* Layout */
  .container {
    padding: 0 var(--spacing-sm);
  }

  .section {
    padding: var(--spacing-xl) 0;
  }

  /* Hero */
  .hero-stats {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .btn-large {
    width: 100%;
    max-width: 280px;
  }

  .demo-preview {
    transform: none;
    width: 100%;
    max-width: 300px;
  }

  .preview-screen {
    width: 100%;
    height: 200px;
  }

  /* Features */
  .features-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  /* Use Cases */
  .use-cases-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  /* Pricing */
  .pricing-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .pricing-card.featured {
    transform: none;
    order: -1;
  }

  /* Footer */
  .footer-links {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    text-align: center;
  }

  .footer-bottom {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }
}

/* Small mobile */
@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.75rem;
  }

  .hero-stats .stat {
    padding: var(--spacing-sm);
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .feature-card,
  .use-case-card,
  .pricing-card {
    padding: var(--spacing-md);
  }

  .demo-frame {
    height: 250px;
  }
}

/* Navbar scrolled state */
.navbar.scrolled {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-sm);
}
