<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TravelCinema Preview</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #E27D60 0%, #0047AB 100%);
            height: 100vh;
            overflow: hidden;
            position: relative;
        }
        
        .preview-container {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .map-preview {
            width: 90%;
            height: 80%;
            background: #2c3e50;
            border-radius: 12px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .map-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #34495e 0%, #2c3e50 100%);
            opacity: 0.8;
        }
        
        .route-line {
            position: absolute;
            top: 30%;
            left: 20%;
            width: 60%;
            height: 2px;
            background: linear-gradient(90deg, #E27D60, #41B3A3, #0047AB);
            border-radius: 2px;
            animation: drawRoute 3s ease-in-out infinite;
        }
        
        .city-marker {
            position: absolute;
            width: 12px;
            height: 12px;
            background: #E27D60;
            border: 2px solid white;
            border-radius: 50%;
            animation: pulse 2s ease-in-out infinite;
        }
        
        .city-marker.marrakech {
            top: 60%;
            left: 25%;
            animation-delay: 0s;
        }
        
        .city-marker.casablanca {
            top: 25%;
            right: 25%;
            animation-delay: 1s;
        }
        
        .vehicle-marker {
            position: absolute;
            top: 30%;
            left: 20%;
            width: 8px;
            height: 8px;
            background: #FFD700;
            border-radius: 50%;
            animation: moveVehicle 4s ease-in-out infinite;
            z-index: 10;
        }
        
        .poi-indicator {
            position: absolute;
            width: 6px;
            height: 6px;
            background: #41B3A3;
            border-radius: 50%;
            animation: poiPulse 3s ease-in-out infinite;
        }
        
        .poi-1 { top: 45%; left: 35%; animation-delay: 0.5s; }
        .poi-2 { top: 35%; left: 50%; animation-delay: 1.5s; }
        .poi-3 { top: 40%; left: 65%; animation-delay: 2.5s; }
        
        .ui-overlay {
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            pointer-events: none;
        }
        
        .left-panel {
            position: absolute;
            top: 0;
            left: 0;
            width: 25%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 8px;
            padding: 12px;
            backdrop-filter: blur(10px);
        }
        
        .right-panel {
            position: absolute;
            top: 0;
            right: 0;
            width: 25%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 8px;
            padding: 12px;
            backdrop-filter: blur(10px);
        }
        
        .panel-header {
            font-size: 10px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .filter-item, .itinerary-item {
            height: 4px;
            background: #e0e0e0;
            margin: 4px 0;
            border-radius: 2px;
            animation: shimmer 2s ease-in-out infinite;
        }
        
        .filter-item:nth-child(2) { width: 80%; }
        .filter-item:nth-child(3) { width: 60%; }
        .filter-item:nth-child(4) { width: 90%; }
        
        .itinerary-item:nth-child(2) { width: 70%; }
        .itinerary-item:nth-child(3) { width: 85%; }
        .itinerary-item:nth-child(4) { width: 65%; }
        
        .top-bar {
            position: absolute;
            top: 0;
            left: 25%;
            right: 25%;
            height: 40px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
        }
        
        .begin-journey-btn {
            background: linear-gradient(135deg, #E27D60, #C85A3A);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 8px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            animation: btnPulse 3s ease-in-out infinite;
        }
        
        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .floating-dot {
            position: absolute;
            width: 3px;
            height: 3px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .floating-dot:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
        .floating-dot:nth-child(2) { top: 70%; left: 80%; animation-delay: 2s; }
        .floating-dot:nth-child(3) { top: 40%; left: 90%; animation-delay: 4s; }
        
        @keyframes drawRoute {
            0% { width: 0%; }
            50% { width: 60%; }
            100% { width: 60%; }
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.3); opacity: 0.7; }
        }
        
        @keyframes moveVehicle {
            0% { left: 20%; }
            50% { left: 50%; }
            100% { left: 80%; }
        }
        
        @keyframes poiPulse {
            0%, 100% { transform: scale(1); opacity: 0.8; }
            50% { transform: scale(1.5); opacity: 1; }
        }
        
        @keyframes shimmer {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.8; }
        }
        
        @keyframes btnPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .preview-label {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 8px;
            font-weight: 500;
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <div class="map-preview">
            <div class="map-background"></div>
            
            <!-- Route and markers -->
            <div class="route-line"></div>
            <div class="city-marker marrakech"></div>
            <div class="city-marker casablanca"></div>
            <div class="vehicle-marker"></div>
            
            <!-- POI indicators -->
            <div class="poi-indicator poi-1"></div>
            <div class="poi-indicator poi-2"></div>
            <div class="poi-indicator poi-3"></div>
            
            <!-- UI Overlay -->
            <div class="ui-overlay">
                <!-- Left Panel -->
                <div class="left-panel">
                    <div class="panel-header">Filters</div>
                    <div class="filter-item"></div>
                    <div class="filter-item"></div>
                    <div class="filter-item"></div>
                    <div class="filter-item"></div>
                </div>
                
                <!-- Right Panel -->
                <div class="right-panel">
                    <div class="panel-header">Itinerary</div>
                    <div class="itinerary-item"></div>
                    <div class="itinerary-item"></div>
                    <div class="itinerary-item"></div>
                    <div class="itinerary-item"></div>
                </div>
                
                <!-- Top Bar -->
                <div class="top-bar">
                    <button class="begin-journey-btn">Begin Journey</button>
                </div>
            </div>
            
            <!-- Floating elements -->
            <div class="floating-elements">
                <div class="floating-dot"></div>
                <div class="floating-dot"></div>
                <div class="floating-dot"></div>
            </div>
            
            <div class="preview-label">Interactive Morocco Travel Demo</div>
        </div>
    </div>
</body>
</html>
