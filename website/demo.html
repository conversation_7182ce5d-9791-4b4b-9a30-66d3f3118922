<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Demo - TravelCinema Framework</title>
    <meta name="description" content="Experience the full TravelCinema framework with our interactive Morocco travel demo.">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Styles -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/animations.css">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        /* Demo-specific styles */
        .demo-header {
            background: var(--dark-color);
            color: var(--white);
            padding: var(--spacing-lg) 0;
            text-align: center;
        }
        
        .demo-container {
            position: relative;
            width: 100%;
            height: calc(100vh - 140px);
            background: #f5f5f5;
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-xl);
            margin: var(--spacing-md) auto;
        }
        
        .demo-frame {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: var(--border-radius-lg);
        }
        
        .demo-controls {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .demo-control-btn {
            background: rgba(0, 0, 0, 0.7);
            color: var(--white);
            border: none;
            padding: var(--spacing-sm);
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition-fast);
            backdrop-filter: blur(10px);
        }
        
        .demo-control-btn:hover {
            background: rgba(0, 0, 0, 0.9);
        }
        
        .demo-info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: var(--white);
            padding: var(--spacing-md);
            border-radius: var(--border-radius);
            backdrop-filter: blur(10px);
            transform: translateY(100%);
            transition: var(--transition-normal);
        }
        
        .demo-info.visible {
            transform: translateY(0);
        }
        
        .demo-features-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-top: var(--spacing-md);
        }
        
        .demo-feature {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: 0.875rem;
        }
        
        .demo-feature i {
            color: var(--accent-color);
        }
        
        .back-to-site {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: var(--primary-gradient);
            color: var(--white);
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--border-radius);
            text-decoration: none;
            font-weight: 600;
            box-shadow: var(--shadow-md);
            transition: var(--transition-fast);
        }
        
        .back-to-site:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--white);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 999;
            transition: opacity 0.5s ease;
        }
        
        .loading-overlay.hidden {
            opacity: 0;
            pointer-events: none;
        }
        
        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(226, 125, 96, 0.3);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: var(--spacing-md);
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            color: var(--dark-color);
            font-weight: 500;
        }
        
        /* Mobile optimizations */
        @media (max-width: 768px) {
            .demo-container {
                height: calc(100vh - 100px);
                margin: var(--spacing-sm);
                border-radius: var(--border-radius);
            }
            
            .demo-controls {
                top: 10px;
                right: 10px;
                gap: var(--spacing-xs);
            }
            
            .demo-control-btn {
                padding: 8px;
                font-size: 0.875rem;
            }
            
            .demo-info {
                bottom: 10px;
                left: 10px;
                right: 10px;
                padding: var(--spacing-sm);
            }
            
            .demo-features-list {
                grid-template-columns: 1fr;
                gap: var(--spacing-xs);
            }
            
            .back-to-site {
                top: 10px;
                left: 10px;
                padding: 8px 12px;
                font-size: 0.875rem;
            }
        }
    </style>
</head>
<body>
    <!-- Back to Site Button -->
    <a href="index.html" class="back-to-site">
        <i class="fas fa-arrow-left"></i>
        Back to Site
    </a>

    <!-- Demo Header -->
    <div class="demo-header">
        <div class="container">
            <h1>Live Demo: Morocco Travel Experience</h1>
            <p>Experience the full TravelCinema framework in action</p>
        </div>
    </div>

    <!-- Demo Container -->
    <div class="container">
        <div class="demo-container">
            <!-- Loading Overlay -->
            <div class="loading-overlay" id="loadingOverlay">
                <div class="loading-spinner"></div>
                <div class="loading-text">Loading Morocco Travel Demo...</div>
            </div>
            
            <!-- Demo Controls -->
            <div class="demo-controls">
                <button class="demo-control-btn" onclick="toggleInfo()" title="Show/Hide Info">
                    <i class="fas fa-info-circle"></i>
                </button>
                <button class="demo-control-btn" onclick="refreshDemo()" title="Refresh Demo">
                    <i class="fas fa-refresh"></i>
                </button>
                <button class="demo-control-btn" onclick="toggleFullscreen()" title="Toggle Fullscreen">
                    <i class="fas fa-expand"></i>
                </button>
            </div>
            
            <!-- Demo Frame -->
            <iframe 
                id="demoFrame"
                class="demo-frame" 
                src="../index.html"
                title="Morocco Travel Demo"
                onload="hideLoading()">
            </iframe>
            
            <!-- Demo Info Panel -->
            <div class="demo-info" id="demoInfo">
                <h3><i class="fas fa-lightbulb"></i> Try These Features</h3>
                <div class="demo-features-list">
                    <div class="demo-feature">
                        <i class="fas fa-mouse-pointer"></i>
                        <span>Select multiple cities</span>
                    </div>
                    <div class="demo-feature">
                        <i class="fas fa-play"></i>
                        <span>Click "Begin Journey"</span>
                    </div>
                    <div class="demo-feature">
                        <i class="fas fa-map-marked-alt"></i>
                        <span>Discover POIs along route</span>
                    </div>
                    <div class="demo-feature">
                        <i class="fas fa-calendar-alt"></i>
                        <span>Build day-by-day itinerary</span>
                    </div>
                    <div class="demo-feature">
                        <i class="fas fa-mobile-alt"></i>
                        <span>Test mobile responsiveness</span>
                    </div>
                    <div class="demo-feature">
                        <i class="fas fa-cog"></i>
                        <span>Explore filter options</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Demo functionality
        let infoVisible = false;
        let isFullscreen = false;
        
        function hideLoading() {
            const overlay = document.getElementById('loadingOverlay');
            setTimeout(() => {
                overlay.classList.add('hidden');
            }, 1000);
        }
        
        function toggleInfo() {
            const info = document.getElementById('demoInfo');
            infoVisible = !infoVisible;
            
            if (infoVisible) {
                info.classList.add('visible');
            } else {
                info.classList.remove('visible');
            }
        }
        
        function refreshDemo() {
            const frame = document.getElementById('demoFrame');
            const overlay = document.getElementById('loadingOverlay');
            
            overlay.classList.remove('hidden');
            frame.src = frame.src;
        }
        
        function toggleFullscreen() {
            const container = document.querySelector('.demo-container');
            const btn = event.target.closest('.demo-control-btn');
            const icon = btn.querySelector('i');
            
            if (!isFullscreen) {
                if (container.requestFullscreen) {
                    container.requestFullscreen();
                } else if (container.webkitRequestFullscreen) {
                    container.webkitRequestFullscreen();
                } else if (container.msRequestFullscreen) {
                    container.msRequestFullscreen();
                }
                icon.className = 'fas fa-compress';
                isFullscreen = true;
            } else {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.webkitExitFullscreen) {
                    document.webkitExitFullscreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
                icon.className = 'fas fa-expand';
                isFullscreen = false;
            }
        }
        
        // Handle fullscreen change events
        document.addEventListener('fullscreenchange', handleFullscreenChange);
        document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
        document.addEventListener('msfullscreenchange', handleFullscreenChange);
        
        function handleFullscreenChange() {
            const isCurrentlyFullscreen = !!(document.fullscreenElement || 
                document.webkitFullscreenElement || 
                document.msFullscreenElement);
            
            if (!isCurrentlyFullscreen && isFullscreen) {
                const icon = document.querySelector('.demo-control-btn:last-child i');
                icon.className = 'fas fa-expand';
                isFullscreen = false;
            }
        }
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && infoVisible) {
                toggleInfo();
            }
            if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                e.preventDefault();
                refreshDemo();
            }
            if (e.key === 'F11') {
                e.preventDefault();
                toggleFullscreen();
            }
            if (e.key === 'i' && e.ctrlKey) {
                e.preventDefault();
                toggleInfo();
            }
        });
        
        // Auto-show info panel after 3 seconds
        setTimeout(() => {
            if (!infoVisible) {
                toggleInfo();
                
                // Auto-hide after 10 seconds
                setTimeout(() => {
                    if (infoVisible) {
                        toggleInfo();
                    }
                }, 10000);
            }
        }, 3000);
        
        // Analytics tracking (placeholder)
        function trackDemoInteraction(action) {
            console.log('Demo interaction:', action);
            // Add your analytics tracking here
        }
        
        // Track demo usage
        window.addEventListener('load', () => trackDemoInteraction('demo_loaded'));
        document.addEventListener('click', (e) => {
            if (e.target.closest('.demo-control-btn')) {
                trackDemoInteraction('control_used');
            }
        });
    </script>
</body>
</html>
