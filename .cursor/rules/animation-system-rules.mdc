---
description: Core animation system implementation guidelines
globs: ["**/map/animation/AnimationManager.ts", "**/map/animation/VehicleManager.ts"]
alwaysApply: true
---

# Animation System Implementation Guidelines

## Singleton Pattern
- Implement all manager classes as singletons
- Use private constructors with public static getInstance() methods
- Initialize all dependencies within the constructor
- Provide clear cleanup methods to prevent memory leaks
- Implement proper type safety for all singleton instances

## Animation Loop
- Use requestAnimationFrame for the primary animation loop
- Maintain a constant animation frame rate when possible
- Implement delta-time based animations for consistency across devices
- Track and log frame rate performance in debug mode
- Provide methods to adjust animation speed dynamically
- Include mechanisms to detect and recover from animation freezes

## Context Awareness
- Implement terrain type detection based on geographical coordinates
- Adjust animation speed based on environment (city, rural, mountain)
- Modify camera parameters when approaching points of interest
- Update vehicle styling based on current terrain
- Track proximity to predefined locations for contextual adjustments
- Cache context detection results to improve performance

## Vehicle Management
- Create vehicle markers as DOM elements for optimal performance
- Implement automatic visibility checks with recovery mechanisms
- Use transitions for smooth vehicle movement
- Apply different vehicle styles based on context
- Implement fault tolerance for marker creation failures
- Include debug visualizations for vehicle position and bearing

## Position Calculation
- Use interpolation between route points for smooth movement
- Calculate proper bearing for vehicle orientation
- Adjust position calculation based on terrain complexity
- Implement easing functions for natural acceleration/deceleration
- Handle edge cases at route boundaries
- Maintain position history for debugging and state recovery

## Error Handling
- Implement comprehensive error boundaries for all animation operations
- Log detailed error information including context and state
- Provide fallback behaviors for all critical failures
- Include automatic recovery attempts for common issues
- Maintain circular debug logs with the most recent entries
- Create visual indicators for error states in debug mode

## Performance Optimization
- Batch DOM updates to minimize reflows
- Implement throttling for expensive calculations
- Cache computation results when appropriate
- Use efficient data structures for position tracking
- Minimize object creation during animation frames
- Implement progressive detail levels based on device capabilities

## Integration Interface
- Provide clear public methods for controlling animations
- Use strongly typed events for communication between systems
- Implement standardized callback patterns
- Include proper cleanup for all event listeners
- Design for composition with other animation systems
- Support both imperative and declarative usage patterns

## Animation Loop
- Use `requestAnimationFrame` for all animation loops
- Target 60fps for smooth animations (16.7ms per frame)
- Implement frame throttling to prevent excessive CPU usage
- Include timestamp delta calculations for consistent motion
- Store animation state in class properties, not React state
- Wrap all animation frame logic in try/catch blocks

## State Management
- Use singleton pattern for animation manager classes
- Maintain single source of truth for animation state
- Implement state change callbacks for external components
- Use event system for cross-component communication
- Store animation-critical values in refs when using React
- Ensure proper cleanup of animation loops on component unmount

## Performance Optimization
- Use transform properties (translate, rotate) for DOM manipulations
- Pre-calculate expensive operations where possible
- Implement position interpolation for smoother motion
- Use hardware acceleration with `will-change` and `transform3d`
- Reduce DOM manipulation frequency during animations
- Batch DOM updates to minimize reflows and repaints

## Route Data
- Pre-process route data before animation starts
- Cache route calculations to prevent recalculating on each frame
- Implement route segmentation for efficient distance calculations
- Store route metadata (distances, bearings) alongside coordinates
- Support both linear and curved path interpolation
- Provide methods for finding closest point on route

## Error Recovery
- Implement visibility checks at regular intervals (every 2 seconds)
- Add timeout detection for stalled animations (10 second threshold)
- Use fallback DOM manipulation if initial method fails
- Log detailed error information for debugging
- Maintain circular debug log with last 20 significant events
- Implement automatic recovery for visibility failures
- Add manual recovery methods for critical animation failures

## Vehicle Animation
- Store frametime delta for calculating position changes
- Use bearing calculations for proper vehicle orientation
- Implement easing functions for natural acceleration/deceleration
- Apply contextual speed modifiers based on environment
- Use CSS transitions for smooth style changes
- Implement appearance transitions between terrain types

## Camera Control
- Implement smooth easing for camera movements
- Calculate appropriate zoom levels based on context
- Support both tracking and cinematic camera modes
- Use pitch and bearing adjustments for immersive experience
- Implement camera transition sequences for cinematic moments
- Add recovery mechanisms for camera positioning failures

## Testing & Debugging
- Add visual indicators for animation state (dev mode only)
- Include detailed console logging for animation events
- Implement animation replay capability for testing edge cases
- Add performance tracking metrics for optimization
- Create testing utilities for simulating animation scenarios
- Support step-by-step animation playback for debugging

## Animation Flow
1. User Journey Phases:
   - Journey initialization
   - Route animation start
   - POI discovery during travel
   - City exploration transitions
   - Journey completion

2. Core Animation Process:
   - AnimationManager initializes state
   - RouteAnimator calculates positions
   - VehicleManager updates marker
   - Camera follows with appropriate zoom
   - POI discovery runs in parallel

## Component Separation
1. React Components:
   - Handle UI updates and user interaction
   - Manage component lifecycle
   - Control animation state transitions
   - Render debug interface elements

2. Animation Modules:
   - Perform core animation calculations
   - Manage vehicle marker updates
   - Control camera behavior
   - Handle POI discovery logic

3. State Management:
   - Use refs for animation-critical state
   - React state for UI updates only
   - Direct DOM manipulation for performance
   - Singleton managers for global state

## Performance Guidelines
1. Animation Timing:
   - Use 100ms frame delay for smooth movement
   - Implement proper easing functions
   - Coordinate camera and vehicle movement
   - Batch style updates when possible

2. Resource Management:
   - Clean up animation frames on unmount
   - Monitor memory usage
   - Track frame rate performance
   - Implement state recovery mechanisms

## Contextual Animation
1. Speed Adjustment:
   - Vary animation speed based on geographic context
   - Slow down near POIs and cities (use predefined multipliers)
   - Apply gradual transitions between speed changes
   - Provide visual feedback when speed changes significantly
   - Use standard multipliers for each context type:
     - Default (1.0) for general route travel
     - Near POI (0.5) for highlighting points of interest
     - POI Cluster (0.3) for significant discovery opportunities
     - Unselected City (0.4) when passing through cities
     - Cultural Regions (0.6) for areas of cultural significance
     - Scenic Routes (0.7) for visually impressive areas
     - Mountain Terrain (0.8) for challenging geography
     - City Centers (0.3) for dense urban environments

2. POI Discovery:
   - Implement user-controllable pausing at POIs
   - Show non-intrusive notifications for approaching POIs
   - Group nearby POIs into discoverable clusters
   - Provide clear user controls to explore or continue
   - Maintain history of discovered and passed POIs
   - Prioritize POIs based on importance and user preferences

3. City Drive-By Experience:
   - Gradually increase zoom when approaching unselected cities
   - Display city information panel with key stats
   - Show city POI markers during approach
   - Provide "Explore" and "Continue" options
   - Highlight city boundaries on the map
   - Adjust camera perspective to showcase the city

## Debug Support
1. Development Tools:
   - Use AnimationDebugTools panel
   - Monitor state transitions
   - Track performance metrics
   - Log animation events

2. Error Handling:
   - Implement recovery mechanisms
   - Log error states
   - Provide fallback behaviors
   - Monitor animation health

## Best Practices
1. Code Organization:
   - Keep modules focused and small
   - Use clear interfaces
   - Document complex logic
   - Follow singleton pattern for managers

2. Performance Optimization:
   - Minimize React renders
   - Batch DOM updates
   - Use appropriate animation timing
   - Monitor resource usage

## Core Animation Principles

- Always use `requestAnimationFrame` for animation loops, never `setTimeout` for the main animation loop
- Maintain separate refs for animation state instead of relying on React state for timing-critical operations
- Use direct DOM manipulation techniques for vehicle marker visibility when needed
- Implement animation state monitoring to recover from failed animations
- Add detailed logging for animation transitions and state changes

## Component Communication
- Use custom events for non-critical state communications
- Implement direct function calls for performance-critical operations
- Create a unified event system for animation state changes
- Use React context for UI state sharing
- Maintain clear interfaces between animation components

## Zoom Levels

- Route traveling: Always use zoom level 8 to provide geographic context
- City exploration: Always use zoom level 14 to show POIs clearly
- POI detail view: Always use zoom level 16 for street-level detail
- Keep the pitch at 45° for 3D effect during all camera transitions
- Keep the bearing at 0 (north-facing) during route traveling

## Animation Debugging

- Always wrap complex animation code in try/catch blocks
- Use `AnimationDebugTools.log()` for consistent logging
- Add specific log markers for animation start/stop events
- Create recovery mechanisms for animation state failures
- Maintain a clean separation between animation code and React rendering

## Vehicle Marker Handling

- Check for the existence of vehicle marker DOM element before manipulating
- Ensure vehicle is visible during animation with both React state AND DOM manipulation
- Clean up animation resources when component unmounts
- Reset animation state completely when restarting animations
- Use appropriate marker scale for different zoom levels

## Performance Guidelines

- Delay between animation frames should be at least 100ms for smooth movement
- Always cancel existing animation frames before starting new ones
- Avoid accessing DOM elements in tight animation loops
- Use point index incrementing approach rather than time-based calculations
- Keep DOM manipulations to a minimum, batch when possible 