---
description: 
globs: ["**/VehicleManager.ts", "**/animation/Vehicle*.ts", "**/animation/*Vehicle*.ts", "**/map/utils/*.ts"]
alwaysApply: false
---
---
description: Vehicle animation control standards for map interactions
globs: ["**/VehicleManager.ts", "**/animation/Vehicle*.ts", "**/animation/*Vehicle*.ts", "**/map/utils/*.ts"]
alwaysApply: true
---

# Vehicle Animation Rules

## Vehicle Appearance
- Use different vehicle markers based on terrain type (car, 4x4, camel, etc.)
- Change vehicle appearance when entering different terrain zones
- Apply smooth transitions between vehicle types (fade in/out)
- Maintain consistent scale based on zoom level
- Use realistic travel appearance for each vehicle type
- Apply cultural context styles when entering cultural regions
- Use city-appropriate vehicles when in urban environments

## Movement Mechanics
- Follow route lines precisely
- Interpolate between route coordinates for smooth movement
- Use natural acceleration and deceleration curves
- Apply inertia for more realistic movement
- Orient vehicle according to route direction
- Adjust movement speed contextually based on environment

## Speed Control
- Apply environment-specific modifiers to base speed:
  - City: 0.5x (slower to show details)
  - POI proximity: 0.4x (slowing gradually as approaching)
  - Scenic routes: 0.7x (moderate speed to appreciate views)
  - Cultural regions: 0.6x (moderate speed with cultural context)
  - Open roads: 1.0x (normal travel speed)
  - Desert/mountains: 0.8x (slightly reduced for terrain)
- Implement gradual speed transitions between contexts
- Allow user manual speed override via controls
- Pause movement when showcasing POIs (user configurable)
- Accelerate on long stretches with no POIs

## Vehicle Position
- Calculate exact position along route segments
- Apply curve interpolation for smoother turns
- Maintain route adherence within 1.5m tolerance
- Store position using latitude/longitude coordinates
- Update position at consistent frame rates (60fps target)
- Implement recovery mechanisms for positioning errors
- Log significant position updates for debugging

## Appearance Transitions
- Fade between vehicle types over 1200ms duration
- Maintain vehicle visibility during type transitions
- Apply terrain-appropriate vehicle scaling
- Make vehicle more prominent during POI discovery
- Use enhanced vehicle visuals during city drive-by
- Apply consistent vehicle rotation based on travel direction
- Use hover effects for user identification

## Integration
- Emit position update events for other components
- Subscribe to external speed control events
- Connect with camera tracking system
- Coordinate with POI discovery system
- Integrate with animation pause/resume controls
- Implement user interaction listeners for manual control
- Respond to context change events from the animation manager