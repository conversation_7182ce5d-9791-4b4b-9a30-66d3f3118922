---
description: 
globs: 
alwaysApply: true
---
# Mapbox Map Best Practices

## Map Initialization and Cleanup

- Always clean up map instance on component unmount
- Initialize map with essential properties and layers only
- Defer non-critical map operations until after map is fully loaded
- Always check map exists before performing operations
- Use the map's load event for reliable initialization

## Marker Management

- Prefer direct DOM manipulation for high-performance marker updates
- Batch marker operations for better performance
- Remove markers from DOM when not needed rather than hiding them
- Assign proper z-index values to ensure correct stacking order
- Use appropriate marker scale for different zoom levels

## Camera Controls

- Respect these standard zoom levels:
  - Route travel: Zoom level 8
  - City view: Zoom level 14
  - POI detail: Zoom level 16
- Maintain fixed north orientation (bearing: 0) during route travel
- Use 45° pitch for 3D perspective when appropriate
- Add transition duration of at least 500ms for smooth camera movements
- Implement contextual rhythm for camera behaviors during exploration

## Performance Optimization

- Use `map.triggerRepaint()` sparingly
- Mark non-interactive animations as `essential: true` to prevent interruption
- Use map `easeTo()` instead of `flyTo()` for small movements
- Limit the frequency of camera updates during animation
- Batch style updates using map.batch() when available

## Layer Management

- Organize layers in logical groups with consistent naming
- Add proper event handlers for map interaction
- Implement efficient layer filtering techniques
- Use appropriate layer opacity for background context
- Keep layer complexity appropriate for the current zoom level 