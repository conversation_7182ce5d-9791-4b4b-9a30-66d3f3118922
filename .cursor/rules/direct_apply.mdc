---
description: 
globs: 
alwaysApply: true
---
---
description: Always apply code changes directly when the user requests a fix, feature, or refactor.
globs: "**/*"
alwaysApply: true
---

- **Direct Code Application**
  - When the user requests a code change, fix, or feature, always make the change directly in the codebase using the appropriate edit tools.
  - Do not just describe the change—implement it unless the user explicitly says "show me" or "explain only".
  - If a change requires multiple files, apply all necessary edits in sequence.
  - If a linter or type error is introduced, attempt to fix it immediately (up to 3 times).
  - If a change is ambiguous, make a best-effort implementation and ask for clarification if needed.
  - Example:
    - User: "Add a dark mode toggle to the header."
    - AI: (edits the header component and supporting files to add the toggle, not just a code snippet)