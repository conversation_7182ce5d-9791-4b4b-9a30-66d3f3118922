---
description: Debugging rules for animation components
globs: ["**/AnimationDebugTools*.ts", "**/AnimationDebugTools*.tsx", "**/animation/debug*.ts"]
alwaysApply: false
---

# Animation Debugging Rules

## Core Logging Principles
- Use AnimationDebugTools.log() for consistent logging format throughout the codebase
- Include level, context, timestamp, and data in all structured logs
- Format log messages consistently with [timestamp] [level] [context] pattern
- Use appropriate log levels: 'info', 'warning', 'error', 'performance'
- Include specific state information in all animation logs

## Log Content Guidelines
- Include geographic coordinates when logging position-related events
- Add specific log markers for animation start/stop/recovery events
- Log speed changes with reason, previous value, and new value
- Record both timestamp and animation progress percentage in status logs
- Include component name and method in context for easier tracing
- Log camera state with zoom, center, bearing, and pitch values

## Visual Debug Tools
- Implement debug panels with animation state visualization for development
- Add control buttons for manual animation testing and recovery
- Display current vehicle position, animation state, and speed in debug UI
- Add visual indicators for camera target points and POI detection radius
- Show speed indicators when animation speed changes contextually
- Provide visual feedback when approaching POIs or unselected cities

## Monitoring & Recovery
- Implement stall detection with timeout-based checks
- Create monitoring tools to detect animation failures and visibility issues
- Add recovery mechanisms that log detailed state before recovery attempts
- Track point index changes to identify animation stalls
- Implement emergency fallback for critical failures
- Monitor vehicle visibility with multi-level checks and recovery options

## Performance Tracking
- Show animation statistics (FPS, frame time) in development mode
- Implement frame execution time tracking in development
- Track and log speed transition performance
- Monitor memory usage for animation-related components
- Record timing of DOM operations for optimization
- Log camera transition timing and completion

## Implementation Guidelines
- Make debug panels conditional on NODE_ENV === 'development'
- Maintain a clean separation between animation code and debug code
- Create helper functions for common debugging tasks
- Log animation lifecycle events (start, pause, resume, complete)
- Trace route point processing with interpolation details
- Make debug visuals toggleable through developer controls

## Contextual Debugging
- Log speed context changes with full context details
- Record POI detection and city approach events with distances
- Include terrain type and region context in relevant logs
- Track user interactions with discovery notifications
- Record camera behavior during city drive-by
- Log component interaction events with source and target