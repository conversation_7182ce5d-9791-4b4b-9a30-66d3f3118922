---
description: 
globs: 
alwaysApply: true
---
 ---
description: Performance optimization rules for animations
globs: ["**/animation/*.ts", "**/animation/*.tsx", "**/TravelAnimator*.tsx", "**/RouteAnimator*.ts"]
alwaysApply: true
---

# Animation Performance Rules

- Keep animation frame logic minimal and focused
- Avoid DOM operations in tight animation loops
- Batch DOM operations when possible
- Use requestAnimationFrame for smooth animations
- Implement frame delay with timestamp comparison not setTimeout
- Use transform for position updates (better performance than left/top)
- Minimize React state updates during animation
- Track animation performance with performance.now() in development
- Log warnings when frame execution exceeds 16ms budget
- Defer non-critical updates outside the animation loop
- Reduce animation complexity on lower-performance devices
- Store animation state in refs not React state for critical timing
- Minimize direct style manipulations to essential properties only
- Implement debouncing for state updates from animation frames
- Calculate and cache values outside animation loops
- Use CSS transitions for smoother visual effects
- Implement device performance detection to adjust animation complexity
- Combine transform operations into a single style change
- Ensure proper animation cleanup to prevent memory leaks
- Avoid layout thrashing by reading DOM values in batches then writing