---
description: Core architecture rules for animation implementation
globs: ["**/map/animation/*.ts", "**/map/animation/*.tsx"]
alwaysApply: true
---

# Animation Architecture Rules

## Component Structure
- Maintain strict separation between animation logic and visual rendering
- Follow the Manager/Controller pattern for all animation systems
- Implement all core animation systems as singletons
- Keep animation-related components stateless when possible
- Use composition over inheritance for animation functionality
- Structure animation systems as independent, pluggable modules

## React vs. DOM Integration
- For buttons and UI controls, stick with the React implementation in TravelAnimator.tsx
- For map manipulation and vehicle animation, continue using the DOM-based approach
- Use React for all user interface elements (buttons, overlays, panels, etc.)
- Use direct DOM manipulation for performance-critical map operations
- Avoid mixing approaches within the same component responsibility
- Keep clear boundaries between React-managed UI and DOM-managed map components
- Use custom hooks to bridge between React state and DOM-manipulated elements

## Responsibility Separation
- **AnimationManager**: Controls core animation loop and timing
- **VehicleManager**: Handles vehicle marker creation and styling
- **CameraBehavior**: Manages all camera movements and transitions
- **RouteAnimator**: Processes route data and determines vehicle position
- **AnimationIntegration**: Provides unified interface to animation systems
- **TravelAnimator**: Renders UI components and manages user interaction

## State Management
- Store animation state in dedicated state objects, not component state
- Use event-driven architecture for communication between animation modules
- Implement clear ownership of animation state variables
- Always initialize animation state with safe default values
- Provide explicit methods for state transitions (start, pause, resume, stop)
- Maintain a single source of truth for animation progress

## Performance Guidelines
- Optimize all animation loops for efficient execution
- Use requestAnimationFrame for animation timing
- Limit DOM manipulations in animation frames
- Implement throttling for expensive operations
- Batch updates to reduce render cycles
- Use worker threads for complex calculations when appropriate
- Implement progressive enhancement strategies for low-performance devices

## Error Handling
- Design animation systems to be resilient against missing data
- Implement automatic recovery from animation failures
- Always provide fallback behaviors for error states
- Log animation errors with contextual information
- Implement timeout detection for stuck animations
- Create explicit cancel/cleanup mechanisms for all animations

## Integration Points
- Define clear interfaces between animation modules
- Use typed events for cross-module communication
- Implement lifecycle hooks for animation start/progress/completion
- Provide configuration options for all animation behaviors
- Design for extensibility with plugin architecture
- Create standardized callback patterns for animation events

## Testing and Debugging
- Include explicit debugging modes for all animation systems
- Implement visual indicators for animation state
- Create deterministic animation playback for testing
- Log contextual information at key animation points
- Support step-by-step animation for debugging
- Include performance metrics in debug logs

## Core Principles
- Separation of concerns between animation logic and UI rendering
- Single source of truth for animation state
- Centralized animation control through manager classes
- Contextual awareness for environmental adaptations
- Graceful error recovery for animation failures
- Performance optimization for smooth animations

## Component Responsibilities

### AnimationManager
- Maintains animation state (playing, paused, stopped)
- Controls overall animation timing and progress
- Calculates positions along route
- Implements playback control (play, pause, stop)
- Emits events for animation progress
- Handles animation speed adjustments
- Provides contextual awareness of environment

### VehicleManager
- Creates and manages vehicle DOM elements
- Updates vehicle position and rotation
- Handles vehicle visibility and error recovery
- Applies contextual styling based on environment
- Implements vehicle transitions between terrain types
- Maintains reference to DOM elements for direct manipulation

### CameraBehavior
- Controls map camera positioning and movement
- Implements camera transitions between viewpoints
- Adjusts zoom levels based on context
- Handles cinematic camera sequences
- Maintains smooth tracking of moving vehicles
- Implements contextual camera positioning for POIs

### TravelAnimator
- Provides React component interface for animations 
- Manages UI state for animation controls
- Coordinates between animation systems
- Handles user interaction with animation
- Renders progress indicators and controls
- Initializes animation components

### AnimationIntegration
- Unifies animation interfaces for external components
- Coordinates between animation subsystems
- Exposes simplified API for animation control
- Manages lifecycle of animation components
- Implements high-level animation sequences
- Handles error conditions across systems

## Data Flow
- Animation progress events flow from AnimationManager to UI
- User control actions flow from UI to AnimationManager
- Position updates flow from AnimationManager to VehicleManager
- Context updates flow from AnimationManager to all systems
- Camera updates flow from CameraBehavior to map instance
- Error events flow to AnimationIntegration for recovery

## Error Handling
- Log all animation errors with descriptive context
- Implement visibility checks for critical elements
- Force element recreation on visibility failures
- Add timeouts for asynchronous operations
- Implement animation state recovery mechanisms
- Provide fallback for failed animations
- Maintain debug log for last 20 significant events

## Module Organization
- Follow singleton pattern for manager classes:
  - AnimationManager for core animation state
  - VehicleManager for marker management
  - POIDiscoveryManager for POI detection
  - CameraManager for view control
- TravelAnimator.tsx coordinates overall journey state and phases
- RouteAnimator.ts handles core animation calculations
- Keep animation logic separate from React components
- Create specialized utility files for animation calculations

## UI Components
- POIOverlay.tsx handles city exploration and POI selection before animation
- NotificationPanel.tsx manages POI discovery during animation
- Keep clear separation between these two components:
  - POIOverlay: Initial city exploration interface
  - NotificationPanel: In-journey discovery interface
- Use appropriate overlay for each journey phase

## State Management
- Store animation frame IDs in refs for proper cleanup
- Reset animation state completely when animations restart
- Use direct DOM manipulation for vehicle marker visibility
- Log animation state transitions with AnimationDebugTools.log()
- Implement proper cleanup in useEffect returns

## DOM Operations
- Check for marker existence before DOM manipulation
- Add defensive marker recreation functions for recovery
- Consistently use vehicle marker ID 'direct-vehicle-marker'
- Batch DOM operations when possible for better performance
- Use index-based point progression instead of time-based calculations

## Camera Control
- Mark camera animations as 'essential: true' during critical animations
- Follow standard zoom levels:
  - Route Travel: Zoom Level 8
  - City Exploration: Zoom Level 14
  - POI Detail: Zoom Level 16
- Maintain fixed north orientation during route travel
- Keep pitch at 45° for 3D perspective

## Debug Support
- Include comprehensive debug capabilities
- Use AnimationDebugTools for state visualization
- Implement performance monitoring
- Add proper error recovery mechanisms
- Monitor memory usage and frame rate

## File Imports
- Import utilities from consolidated source files (e.g., MapHelpers.ts, not MapHelpers.tsx)
- Follow "single source of truth" principle for all utility functions
- Prefer importing from .ts files rather than .tsx files for non-UI utilities 

# Core Animation Architecture Rules

## Component Organization

1. **Manager Classes**: Each major system component should be implemented as a separate manager class using the singleton pattern:
   - `AnimationManager` - Core animation loop and timing
   - `RouteAnimationController` - Route-specific animation control
   - `VehicleManager` - Vehicle marker creation and position updates
   - `ContextualSpeedController` - Dynamic speed adjustments
   - `POIDiscoveryManager` - POI discovery and interactions
   - `CityDriveByManager` - City proximity and camera management
   - `ComponentInteractionManager` - UI component coordination

2. **Interfaces**: Define clear interfaces for all manager classes with strict typing:
   ```typescript
   interface AnimationControllerInterface {
     prepareRoute(route: RouteData, options: RoutePreparationOptions): void;
     start(): void;
     stop(): void;
     pause(isPaused: boolean): void;
     dispose(): void;
     // ...
   }
   ```

3. **Configuration**: Use centralized configuration objects for system-wide settings:
   ```typescript
   const ANIMATION_CONFIG = {
     SPEEDS: {
       DEFAULT: 1.0,
       CITY: 0.5,
       HIGHWAY: 1.5,
       MOUNTAIN: 0.75,
       SCENIC: 0.6
     },
     CAMERA: {
       DEFAULT_ZOOM: 12,
       DEFAULT_PITCH: 60,
       DEFAULT_BEARING_OFFSET: 0
     }
   };
   ```

## State Management

1. **Single Source of Truth**: The `AnimationManager` should be the authoritative source for animation state.

2. **State Propagation**: Use events or callbacks to notify other components of state changes:
   ```typescript
   // In AnimationManager
   private notifyAnimationStateChange(state: AnimationState) {
     this.eventEmitter.emit('animationStateChange', state);
   }
   ```

3. **Immutable Updates**: When updating state, create new objects rather than mutating existing ones:
   ```typescript
   // Correct
   this.state = { ...this.state, isAnimating: true };
   
   // Avoid
   this.state.isAnimating = true;
   ```

## Animation Control Flow

1. **Two-Phase Animation**: Split animation into preparation and execution phases:
   ```typescript
   // Preparation phase - setup without starting
   public prepareRoute(route: [number, number][], options: RoutePreparationOptions): void {
     // Clean up any existing animation
     this.stop();
     
     // Store route and callbacks
     this.route = route;
     this.duration = options.duration || 10000;
     this.onProgress = (progress, position, bearing) => {
       options.onProgress(progress);
       options.onPosition(position, bearing);
     };
     this.onComplete = options.onComplete;
     
     // Initialize position at start of route
     if (this.onPosition && route.length > 0) {
       this.onPosition(route[0], 0);
     }
   }
   
   // Execution phase - start when ready
   public start(): void {
     // Reset animation state
     this.isAnimating = true;
     this.isPaused = false;
     this.startTime = null;
     this.totalPausedTime = 0;
     
     // Start animation loop
     this.animationFrameId = requestAnimationFrame(this.animateFrame);
   }
   ```

2. **Pause/Resume Mechanism**: Implement proper pause handling that maintains the animation loop:
   ```typescript
   // Pause state handling
   private isPaused: boolean = false;
   private pauseStartTime: number | null = null;
   private totalPausedTime: number = 0;
   
   public pause(isPaused: boolean): void {
     if (this.isPaused === isPaused) return; // No change
     
     this.isPaused = isPaused;
     
     if (isPaused) {
       // Mark when we started pausing
       this.pauseStartTime = performance.now();
     } else {
       // Add pause duration to total paused time when resuming
       this.totalPausedTime += (performance.now() - this.pauseStartTime!);
       this.pauseStartTime = null;
     }
   }
   
   // In animation frame handler
   private animateFrame = (timestamp: number): void => {
     if (!this.isAnimating) return;
     
     // Skip updates when paused but keep the loop running
     if (this.isPaused) {
       this.animationFrameId = requestAnimationFrame(this.animateFrame);
       return;
     }
     
     // Calculate elapsed time accounting for pauses
     const elapsed = timestamp - this.startTime! - this.totalPausedTime;
     const progress = Math.min(elapsed / this.duration, 1);
     
     // Rest of animation logic...
   }
   ```

3. **Clean Teardown**: Implement proper resource cleanup:
   ```typescript
   public dispose(): void {
     this.stop();
     this.vehicleManager.reset();
     this.route = [];
     this.onProgress = null;
     this.onComplete = null;
   }
   
   public stop(): void {
     this.isAnimating = false;
     this.isPaused = false;
     
     if (this.animationFrameId) {
       cancelAnimationFrame(this.animationFrameId);
       this.animationFrameId = null;
     }
   }
   ```

## Animation Loop

1. **RequestAnimationFrame**: Use `requestAnimationFrame` for the animation loop:
   ```typescript
   private animationFrameId: number | null = null;
   
   private startAnimationLoop() {
     const animate = () => {
       this.animateFrame();
       this.animationFrameId = requestAnimationFrame(animate);
     };
     this.animationFrameId = requestAnimationFrame(animate);
   }
   
   private stopAnimationLoop() {
     if (this.animationFrameId !== null) {
       cancelAnimationFrame(this.animationFrameId);
       this.animationFrameId = null;
     }
   }
   ```

2. **Time-Based Animation**: Use time deltas rather than frame counts for consistent speed:
   ```typescript
   private lastFrameTime: number = 0;
   
   private animateFrame() {
     const now = performance.now();
     const delta = now - this.lastFrameTime;
     this.lastFrameTime = now;
     
     // Use delta to calculate position changes
     const distanceToMove = this.speed * delta / 1000;
     // ...
   }
   ```

3. **Animation Recovery**: Implement mechanisms to detect and recover from stuck animations:
   ```typescript
   private detectStuckAnimation() {
     const currentTime = performance.now();
     if (currentTime - this.lastPositionUpdateTime > 5000) {
       console.warn('Animation appears stuck, attempting recovery');
       this.recoverAnimation();
     }
   }
   ```

## Contextual Awareness

1. **Position Analysis**: Continuously analyze the vehicle's position to determine environmental context:
   ```typescript
   private determineCurrentContext(position: Position): AnimationContext {
     const nearbyCity = this.getNearbyCity(position);
     const nearbyPOI = this.getNearbyPOI(position);
     const terrainType = this.determineTerrainType(position);
     
     return {
       inCity: !!nearbyCity,
       cityName: nearbyCity?.name,
       nearPOI: !!nearbyPOI,
       poiName: nearbyPOI?.name,
       terrain: terrainType
     };
   }
   ```

2. **Dynamic Adjustments**: Adjust animation parameters based on the current context:
   ```typescript
   private adjustAnimationForContext(context: AnimationContext) {
     // Adjust speed
     this.currentSpeed = this.baseSpeed * ANIMATION_CONFIG.SPEEDS[context.terrain];
     
     // Adjust camera
     if (context.inCity) {
       this.updateCamera({
         zoom: ANIMATION_CONFIG.CAMERA.CITY_ZOOM,
         pitch: ANIMATION_CONFIG.CAMERA.CITY_PITCH
       });
     }
     
     // Handle POI discovery
     if (context.nearPOI && !this.discoveredPOIs.includes(context.poiName)) {
       this.handlePOIDiscovery(context.poiName);
     }
   }
   ```

## Performance Optimization

1. **Throttling**: Throttle expensive operations like terrain analysis:
   ```typescript
   private throttledTerrainCheck = throttle((position: Position) => {
     return this.determineTerrainType(position);
   }, 2000);
   ```

2. **Memory Management**: Clean up resources when components are destroyed:
   ```typescript
   public destroy() {
     this.stopAnimationLoop();
     this.eventEmitter.removeAllListeners();
     this.vehicleManager.destroy();
     // Additional cleanup...
   }
   ```

3. **Batched Updates**: Batch DOM updates for better performance:
   ```typescript
   private batchedStyleUpdates: Array<StyleUpdate> = [];
   
   private scheduleStyleUpdate(element: HTMLElement, style: Partial<CSSStyleDeclaration>) {
     this.batchedStyleUpdates.push({ element, style });
     
     if (!this.isApplyingUpdates) {
       this.isApplyingUpdates = true;
       requestAnimationFrame(() => this.applyBatchedStyleUpdates());
     }
   }
   
   private applyBatchedStyleUpdates() {
     for (const update of this.batchedStyleUpdates) {
       Object.assign(update.element.style, update.style);
     }
     this.batchedStyleUpdates = [];
     this.isApplyingUpdates = false;
   }
   ```

## Error Handling

1. **Graceful Degradation**: Implement fallbacks for when components fail:
   ```typescript
   private updateVehicle(position: Position) {
     try {
       this.vehicleManager.updateVehiclePosition(position);
     } catch (error) {
       console.error('Failed to update vehicle position', error);
       this.attemptVehicleRecovery(position);
     }
   }
   ```

2. **Logging**: Implement comprehensive logging for debugging:
   ```typescript
   private log(level: LogLevel, message: string, data?: any) {
     if (this.debugMode || level === 'error') {
       const logMessage = `[AnimationManager] ${message}`;
       switch (level) {
         case 'debug': console.debug(logMessage, data); break;
         case 'info': console.info(logMessage, data); break;
         case 'warn': console.warn(logMessage, data); break;
         case 'error': console.error(logMessage, data); break;
       }
     }
   }
   ```

3. **State Validation**: Validate state changes to prevent invalid states:
   ```typescript
   private setAnimationState(newState: AnimationState) {
     // Validate state transition
     if (newState === 'playing' && !this.routeData) {
       this.log('error', 'Cannot start animation without route data');
       return false;
     }
     
     this.state = newState;
     this.notifyAnimationStateChange(newState);
     return true;
   }
   ```

## Integration Points

1. **Public API**: Provide a clear public API for external components:
   ```typescript
   // Public methods for React components
   public prepareRoute(route: RouteData, options: RoutePreparationOptions): void;
   public start(): void;
   public pause(isPaused: boolean): void;
   public stop(): void;
   public dispose(): void;
   public getProgress(): number;
   ```

2. **Event System**: Use events for loose coupling between components:
   ```typescript
   public initialize() {
     this.eventEmitter.on('vehiclePositionChange', this.handleVehiclePositionChange);
     this.eventEmitter.on('poiDiscovered', this.handlePOIDiscovered);
     this.eventEmitter.on('cityApproached', this.handleCityApproached);
   }
   ```

3. **Dependency Injection**: Use dependency injection for testing and flexibility:
   ```typescript
   constructor({
     vehicleManager = VehicleManager.getInstance(),
     mapInstance = MapInstance.getInstance(),
     eventEmitter = new EventEmitter()
   }: AnimationManagerDependencies = {}) {
     this.vehicleManager = vehicleManager;
     this.mapInstance = mapInstance;
     this.eventEmitter = eventEmitter;
   }
   ``` 