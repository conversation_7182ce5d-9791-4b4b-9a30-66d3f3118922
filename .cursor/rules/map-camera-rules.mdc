---
description: 
globs: 
alwaysApply: true
---
---
description: Map camera control standards for animations
globs: ["**/camera/*.ts", "**/utils/CameraBehavior.ts", "**/animation/*Camera*.ts", "**/map/utils/*.ts"]
alwaysApply: true
---

# Map Camera Rules

## Standard Zoom Levels
- Use zoom level 8 for route travel animations
- Use zoom level 14 for city exploration and POI discovery
- Use zoom level 16 when focusing on a specific POI
- Use variable zoom levels (10-14) for city drive-by experiences
- Use zoom level 6 for dramatic country overviews

## Camera Orientation
- Maintain fixed north orientation (bearing: 0) during route travel
- Keep pitch at 45° for 3D perspective during general route travel
- Use 50-60° pitch for city exploration to highlight buildings
- Adjust bearing for POI details to showcase the point of interest
- Use optimal bearing angles for city skylines during drive-by

## Transition Parameters
- Use duration of at least 500ms for all camera transitions
- Use 1500-2000ms durations for dramatic zoom changes
- Implement easing functions for smoother camera movement
- Use 'easeInOutCubic' as the standard easing function
- Mark critical camera animations as 'essential: true'
- Assign priority levels to camera transitions ('low', 'medium', 'high', 'critical')

## Contextual Camera Behavior
- Use progressive zoom approach that starts wider and gradually focuses
- Apply two-step zooming for dramatic zoom changes
- Track vehicle with slight look-ahead during animation
- Coordinate camera movement speed with animation speed
- Gradually increase zoom when approaching points of interest
- Implement adaptive bearing based on terrain and travel direction
- Show city skylines when approaching unselected cities
- Highlight POI clusters with adjusted camera position

## Technical Implementation
- Store camera state in refs, not React state
- Implement camera state recovery mechanisms for interrupted animations
- Batch camera operations for better performance
- Use map.easeTo() instead of map.flyTo() for small movements
- Calculate look-ahead points for smoother camera tracking
- Log camera transitions with zoom, center, bearing, and pitch
- Create camera presets for standard views (route travel, city exploration, POI detail)
- Use camera state interpolation for smooth transitions
- Import camera utilities from MapHelpers.ts (not MapHelpers.tsx) to follow the "single source of truth" principle

## Camera Transition Timing
- Camera transitions for route travel: 500ms
- Camera transitions for city highlights: 1500ms
- Camera transitions for POI focus: 1800ms
- Camera transitions for long distances: 1500-2000ms
- Camera transitions for city drive-by: 1200-2000ms depending on speed