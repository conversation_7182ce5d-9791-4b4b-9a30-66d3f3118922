---
description: Best practices for React animation integration
globs: ["**/components/**/*.tsx", "**/TravelAnimator*.tsx", "**/animation/*.tsx"]
alwaysApply: true
---
---
globs: ["**/map/TravelAnimator.tsx", "**/map/overlays/OnTheWayPOIOverlay.tsx", "**/map/animation/ComponentInteractionManager.ts"]
---

# Best Practices for React Animation Integration

## Component Architecture

- **Separation of Concerns**: Keep animation logic separate from UI rendering.
  ```typescript
  // Good - Clear separation between animation control and UI
  const TravelAnimator = ({ isAnimating, route, onAnimationComplete }) => {
    // Animation state management
    useEffect(() => {
      if (isAnimating) {
        animationManager.startAnimation(route);
      } else {
        animationManager.stopAnimation();
      }
    }, [isAnimating, route]);
    
    // UI rendering
    return <ProgressBar progress={progress} />;
  };
  ```

- **Component Composition**: Break down complex animation UIs into manageable pieces.
  ```typescript
  const JourneyAnimation = () => (
    <>
      <AnimationControls />
      <VehicleMarker />
      <RouteDisplay />
      <POIOverlay />
    </>
  );
  ```

## State Management

- **Animation State**: Store animation state (playing, paused, progress) in React state to keep UI in sync.
  ```typescript
  const [animationState, setAnimationState] = useState({
    isPlaying: false,
    progress: 0,
    phase: JourneyPhase.NOT_STARTED
  });
  ```

- **Context for Animation State**: For deeper component trees, use Context API for animation state.
  ```typescript
  const AnimationContext = createContext(null);
  
  export const AnimationProvider = ({ children }) => {
    const [state, dispatch] = useReducer(animationReducer, initialState);
    return (
      <AnimationContext.Provider value={{ state, dispatch }}>
        {children}
      </AnimationContext.Provider>
    );
  };
  ```

## React Hooks

- **Custom Animation Hooks**: Create reusable hooks for animation logic.
  ```typescript
  function useAnimationProgress(isAnimating, onProgress) {
    useEffect(() => {
      if (!isAnimating) return;
      
      const handleFrame = () => {
        const progress = AnimationManager.getInstance().getProgress();
        onProgress(progress);
        frameId = requestAnimationFrame(handleFrame);
      };
      
      let frameId = requestAnimationFrame(handleFrame);
      return () => cancelAnimationFrame(frameId);
    }, [isAnimating, onProgress]);
  }
  ```

- **Effect Cleanup**: Always clean up animation effects with useEffect returns.
  ```typescript
  useEffect(() => {
    const animationManager = AnimationManager.getInstance();
    animationManager.startAnimation(route);
    
    return () => {
      animationManager.stopAnimation();
    };
  }, [route]);
  ```

## Performance Optimization

- **Component Memoization**: Memoize components that render frequently during animations.
  ```typescript
  const AnimatedMarker = React.memo(({ position }) => {
    return <Marker position={position} />;
  });
  ```

- **Throttled Updates**: Throttle progress updates to prevent excessive re-renders.
  ```typescript
  const [progress, setProgress] = useState(0);
  
  useEffect(() => {
    const handleProgress = throttle((newProgress) => {
      setProgress(newProgress);
    }, 50); // Update at most every 50ms
    
    animationManager.onProgress(handleProgress);
    return () => animationManager.offProgress(handleProgress);
  }, []);
  ```

## Event System Integration

- **Event Subscriptions**: Use React's lifecycle to manage animation event subscriptions.
  ```typescript
  useEffect(() => {
    const interactionManager = ComponentInteractionManager.getInstance();
    
    const unsubscribe = interactionManager.addEventListener(
      InteractionEventType.POI_DISCOVERED,
      handlePOIDiscovery
    );
    
    return unsubscribe;
  }, []);
  ```

- **Custom Events**: Leverage custom DOM events for cross-component communication.
  ```typescript
  useEffect(() => {
    const handleAnimationEvent = (event) => {
      const { type, data } = event.detail;
      // Handle animation event
    };
    
    window.addEventListener('animation-event', handleAnimationEvent);
    return () => {
      window.removeEventListener('animation-event', handleAnimationEvent);
    };
  }, []);
  ```

## Conditional Rendering

- **Animation Phase-Based Rendering**: Conditionally render components based on animation phase.
  ```typescript
  const AnimationUI = ({ phase }) => {
    switch (phase) {
      case JourneyPhase.INITIAL_CITY:
        return <CityPanel />;
      case JourneyPhase.JOURNEY:
        return <JourneyControls />;
      case JourneyPhase.COMPLETED:
        return <JourneySummary />;
      default:
        return null;
    }
  };
  ```

- **Transition Components**: Use dedicated components for handling transitions between animation states.
  ```typescript
  const AnimationTransition = ({ phase, prevPhase, children }) => {
    const [isTransitioning, setIsTransitioning] = useState(false);
    
    useEffect(() => {
      if (phase !== prevPhase) {
        setIsTransitioning(true);
        const timer = setTimeout(() => setIsTransitioning(false), 1000);
        return () => clearTimeout(timer);
      }
    }, [phase, prevPhase]);
    
    return (
      <div className={isTransitioning ? 'fade-transition' : ''}>
        {children}
      </div>
    );
  };
  ```

## Error Handling

- **Animation Error Boundaries**: Implement error boundaries specifically for animation components.
  ```typescript
  class AnimationErrorBoundary extends React.Component {
    state = { hasError: false };
    
    static getDerivedStateFromError() {
      return { hasError: true };
    }
    
    componentDidCatch(error, info) {
      AnimationDebugTools.log('error', 'Animation component error', { error, info });
    }
    
    render() {
      if (this.state.hasError) {
        return <FallbackAnimation />;
      }
      return this.props.children;
    }
  }
  ```

- **Graceful Degradation**: Provide fallback animations when optimal ones fail.
  ```typescript
  const AnimatedRoute = ({ route }) => {
    const [useSimpleAnimation, setUseSimpleAnimation] = useState(false);
    
    const handleAnimationError = () => {
      setUseSimpleAnimation(true);
    };
    
    return useSimpleAnimation ? (
      <SimpleRouteAnimation route={route} />
    ) : (
      <ComplexRouteAnimation route={route} onError={handleAnimationError} />
    );
  };
  ```

## Testing

- **Mock Animation Managers**: Create lightweight mocks of animation managers for testing.
  ```typescript
  // In test file
  jest.mock('../animation/AnimationManager', () => ({
    getInstance: () => ({
      startAnimation: jest.fn(),
      stopAnimation: jest.fn(),
      getProgress: jest.fn().mockReturnValue(0.5)
    })
  }));
  
  test('TravelAnimator renders with correct progress', () => {
    render(<TravelAnimator isAnimating={true} />);
    expect(screen.getByTestId('progress-bar')).toHaveStyle('width: 50%');
  });
  ```

- **Animation Snapshot Testing**: Use snapshots for key animation states.
  ```typescript
  test('Animation renders correctly at different states', () => {
    const { rerender, asFragment } = render(
      <AnimationUI phase={JourneyPhase.NOT_STARTED} />
    );
    expect(asFragment()).toMatchSnapshot('not-started');
    
    rerender(<AnimationUI phase={JourneyPhase.JOURNEY} />);
    expect(asFragment()).toMatchSnapshot('journey');
  });
  ```