{"mcpServers": {"task-master-ai": {"command": "npx", "args": ["-y", "task-master-mcp"], "env": {"ANTHROPIC_API_KEY": "************************************************************************************************************", "PERPLEXITY_API_KEY": "YOUR_PERPLEXITY_API_KEY", "MODEL": "claude-3-7-sonnet-20250219", "PERPLEXITY_MODEL": "sonar-pro", "MAX_TOKENS": 64000, "TEMPERATURE": 0.2, "DEFAULT_SUBTASKS": 5, "DEFAULT_PRIORITY": "medium"}}, "UI Maker": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@21st-dev/magic-mcp", "--config", "{\"TWENTY_FIRST_API_KEY\":\"0ff6277f21e4ae1c11835893446c07827ce3c5dba81b7462b6728b8723f172e1\"}"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "interactive": {"command": "npx", "args": ["-y", "interactive-mcp"]}}}