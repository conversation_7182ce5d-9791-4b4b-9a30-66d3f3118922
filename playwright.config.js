import { defineConfig } from '@playwright/test';

export default defineConfig({
  testDir: './tests',
  timeout: 60000, // 60 seconds
  fullyParallel: false,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: 1,
  
  // Configure projects for major browsers
  projects: [
    {
      name: 'chromium',
      use: {
        // Browser options
        browserName: 'chromium',
        viewport: { width: 1280, height: 720 },
        
        // Collect trace and screenshots
        trace: 'on',
        screenshot: 'on',
        
        // Record video
        video: {
          mode: 'on',
          size: { width: 1280, height: 720 },
        },
      },
    },
  ],
  
  // Configure output directory
  outputDir: 'test-results/',
  
  // Reporter to use
  reporter: [
    ['html', { open: 'never' }],
    ['list']
  ],
  
  // Use the default baseURL
  use: {
    baseURL: 'http://localhost:8080',
    actionTimeout: 10000,
  },
}); 