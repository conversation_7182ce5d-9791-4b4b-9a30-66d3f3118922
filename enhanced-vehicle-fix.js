#!/usr/bin/env node

/**
 * Enhanced VehicleManager.ts Fix Script
 * 
 * This script detects and fixes a broader range of issues in VehicleManager.ts:
 * 1. Fixes _recoveryTimeout property type
 * 2. Removes duplicate method implementations
 * 3. Adds isHTMLElement type guard and replaces instanceof HTMLElement
 * 4. Fixes inconsistent method signatures
 * 5. Fixes mismatched brackets and syntax issues
 * 6. Corrects AnimationDebugTools usage
 * 
 * Usage:
 * node enhanced-vehicle-fix.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name using ES module syntax
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const filePath = path.join(process.cwd(), 'src/components/map/animation/VehicleManager.ts');
const backupPath = `${filePath}.backup`;

// Create a backup if it doesn't exist
if (!fs.existsSync(backupPath)) {
  console.log('Creating backup of VehicleManager.ts...');
  fs.copyFileSync(filePath, backupPath);
}

console.log('Reading VehicleManager.ts...');
let content = fs.readFileSync(filePath, 'utf8');

// -------------------- PROPERTY TYPE FIXES --------------------

// Fix 1: Ensure _recoveryTimeout has NodeJS.Timeout | null type
if (content.includes('private _recoveryTimeout:') && !content.includes('private _recoveryTimeout: NodeJS.Timeout | null')) {
  console.log('Fixing _recoveryTimeout property type...');
  content = content.replace(
    /private\s+_recoveryTimeout:.*?=/,
    'private _recoveryTimeout: NodeJS.Timeout | null ='
  );
}

// -------------------- DUPLICATE METHOD REMOVAL --------------------

// Generic function to find method implementations
function findMethodImplementations(methodName, isPrivate = false, accessibility = 'private|public') {
  const methodPattern = new RegExp(`(\\s+${isPrivate ? 'private' : accessibility}\\s+${methodName}\\s*\\([^)]*\\)[^{]*{[\\s\\S]*?)(?=\\s+(?:private|public)\\s+\\w+|})`, 'g');
  return [...content.matchAll(methodPattern)].map(match => ({
    fullMatch: match[0],
    implementation: match[1],
    startIndex: match.index,
    endIndex: match.index + match[0].length
  }));
}

// Function to deduplicate a method
function deduplicateMethod(methodName, isPrivate = false) {
  const implementations = findMethodImplementations(methodName, isPrivate);
  if (implementations.length > 1) {
    console.log(`Found ${implementations.length} implementations of ${isPrivate ? 'private' : 'public/private'} ${methodName}`);
    
    // Sort implementations by length (we'll keep the most complete one)
    implementations.sort((a, b) => b.fullMatch.length - a.fullMatch.length);
    
    // Keep the first (most complete) implementation
    const keepImpl = implementations[0];
    
    // Replace others with comments
    for (let i = 1; i < implementations.length; i++) {
      const impl = implementations[i];
      content = content.substring(0, impl.startIndex) + 
                `\n  // NOTE: Removing duplicate implementation of ${methodName}\n\n  ` + 
                content.substring(impl.endIndex);
    }
    return true;
  }
  return false;
}

// Methods to check for duplicates
const methodsToCheck = [
  'isVehicleDOM',
  'isMapboxMarker',
  'getVehicleDebugInfo',
  'isHTMLElement',
  'getVehicleMarkerElement',
  'updateDOMMarkerPosition',
  'getMarkerElement',
  'checkVehicleVisibility',
  'checkVisibilityWithDelay',
  'addDebugEntry',
  'debugLog',
  'emitVehicleStatus',
  'createVehicleMarker'
];

for (const method of methodsToCheck) {
  deduplicateMethod(method);
}

// -------------------- TYPE GUARD FIXES --------------------

// Fix 2: Add isHTMLElement type guard if missing
if (!content.includes('isHTMLElement(value:') && 
    (content.includes('instanceof HTMLElement') || content.includes('element instanceof HTMLElement'))) {
  console.log('Adding isHTMLElement type guard function...');
  
  // Look for a good place to insert the function (after a specific method)
  let insertLocation = content.indexOf('private cleanupVehicleMarker(): void {');
  if (insertLocation === -1) {
    // Fallback: after any private method
    const privateMethodMatch = content.match(/private\s+\w+\(\).*?{[\s\S]*?}/);
    if (privateMethodMatch) {
      insertLocation = privateMethodMatch.index + privateMethodMatch[0].length;
    } else {
      // Last resort: at the beginning of the class after first {
      insertLocation = content.indexOf('{', content.indexOf('class VehicleManager')) + 1;
    }
  }
  
  if (insertLocation !== -1) {
    const isHTMLElementFunction = `

  /**
   * Type guard to check if a value is an HTMLElement
   */
  private isHTMLElement(value: any): value is HTMLElement {
    return value && typeof value === 'object' && value.nodeType === 1 && typeof value.style === 'object';
  }
`;
    content = content.slice(0, insertLocation) + isHTMLElementFunction + content.slice(insertLocation);
  }
}

// Fix 3: Replace instanceof HTMLElement with isHTMLElement call
if (content.includes('instanceof HTMLElement')) {
  console.log('Fixing instanceof HTMLElement checks...');
  content = content.replace(
    /(\w+)\s+instanceof\s+HTMLElement/g,
    'this.isHTMLElement($1)'
  );
}

// -------------------- METHOD SIGNATURE FIXES --------------------

// Fix 4: Ensure consistent signature for createVehicleMarker
const createVehicleMarkerPattern = /createVehicleMarker\s*\(([^)]*)\)/g;
const markerMatches = [...content.matchAll(createVehicleMarkerPattern)];

if (markerMatches.length > 0) {
  console.log('Checking createVehicleMarker for consistent signatures...');
  
  // Get all unique parameter lists
  const paramSets = new Set(markerMatches.map(m => m[1].trim()));
  
  if (paramSets.size > 1) {
    console.log('Found inconsistent createVehicleMarker signatures, standardizing...');
    
    // Find the method definition
    const methodDef = content.match(/private\s+createVehicleMarker\s*\(([^)]*)\)\s*:/);
    
    if (methodDef) {
      const correctParams = methodDef[1].trim();
      
      // Replace method calls with incorrect signatures
      for (const match of markerMatches) {
        const params = match[1].trim();
        if (params !== correctParams) {
          // Only replace method calls, not the definition
          if (!content.substring(match.index - 20, match.index).includes('private')) {
            const callStart = match.index;
            const callEnd = match.index + match[0].length;
            
            // Extract just the position parameter if it exists
            let positionParam = '';
            if (params.includes('position:')) {
              const posMatch = params.match(/position:\s*([^,}]+)/);
              if (posMatch) positionParam = posMatch[1].trim();
            } else if (params.includes(',')) {
              positionParam = params.split(',')[0].trim();
            } else if (params.length > 0) {
              positionParam = params;
            }
            
            if (positionParam) {
              const replacement = `createVehicleMarker(${positionParam})`;
              content = content.substring(0, callStart) + replacement + content.substring(callEnd);
            }
          }
        }
      }
    }
  }
}

// Fix 5: Ensure consistent signature for addDebugEntry
const addDebugEntryPattern = /addDebugEntry\s*\(([^)]*)\)/g;
const debugEntryMatches = [...content.matchAll(addDebugEntryPattern)];

if (debugEntryMatches.length > 0) {
  console.log('Checking addDebugEntry for consistent signatures...');
  
  // Get all unique parameter lists
  const paramSets = new Set(debugEntryMatches.map(m => m[1].trim()));
  
  if (paramSets.size > 1) {
    console.log('Found inconsistent addDebugEntry signatures, standardizing...');
    
    // Standardize calls to match: addDebugEntry(message: string, data?: any)
    for (const match of debugEntryMatches) {
      const params = match[1].trim();
      
      // Skip the method definition
      if (!content.substring(match.index - 20, match.index).includes('private')) {
        // Check if it's an old format call (with type string)
        if (params.includes('"debug"') || params.includes('"error"') || params.includes('"info"')) {
          const callStart = match.index;
          const callEnd = match.index + match[0].length;
          
          const paramParts = params.split(',').map(p => p.trim());
          if (paramParts.length >= 2) {
            // Old format: addDebugEntry("debug", "message", data?)
            // New format: addDebugEntry("message", data?)
            const message = paramParts[1];
            const data = paramParts.length > 2 ? paramParts.slice(2).join(', ') : '';
            
            const replacement = `addDebugEntry(${message}${data ? ', ' + data : ''})`;
            content = content.substring(0, callStart) + replacement + content.substring(callEnd);
          }
        }
      }
    }
  }
}

// -------------------- ANIMATION DEBUG TOOLS FIXES --------------------

// Fix 6: Check and fix AnimationDebugTools usage
if (content.includes('AnimationDebugTools')) {
  console.log('Fixing AnimationDebugTools usage...');
  
  // Replace AnimationDebugTools?.debug with AnimationDebugTools?.log for consistency
  content = content.replace(/AnimationDebugTools\?\.debug\(/g, 'AnimationDebugTools?.log(');
  
  // Ensure proper debug level is passed
  content = content.replace(/AnimationDebugTools\?\.log\('([^']+)'(.*?)(?:,|\))/g, (match, p1, p2) => {
    if (p1 !== 'debug' && p1 !== 'info' && p1 !== 'warn' && p1 !== 'error') {
      return `AnimationDebugTools?.log('debug', '${p1}'${p2}`;
    }
    return match;
  });
}

// -------------------- SYNTAX FIXES --------------------

// Fix 7: Attempt to fix unclosed methods
const methodStartPattern = /(private|public)\s+(\w+)\s*\([^)]*\)\s*:\s*\w+(?:\s*\|\s*\w+)*\s*{/g;
const methodStarts = [...content.matchAll(methodStartPattern)];

console.log('Checking for unclosed method blocks...');
for (const methodStart of methodStarts) {
  const methodName = methodStart[2];
  const startPos = methodStart.index;
  
  // Find the closing bracket for this method
  let openBrackets = 1;
  let closePos = startPos + methodStart[0].length;
  let foundClose = false;
  
  while (closePos < content.length && openBrackets > 0) {
    if (content[closePos] === '{') openBrackets++;
    if (content[closePos] === '}') openBrackets--;
    closePos++;
    
    // If we found the closing bracket for this method
    if (openBrackets === 0) {
      foundClose = true;
      break;
    }
    
    // If we hit another method definition before finding our closing bracket, 
    // this method is likely unclosed
    if (content.substring(closePos, closePos + 20).match(/^\s*(private|public)\s+\w+/)) {
      break;
    }
  }
  
  if (!foundClose) {
    console.log(`Found potentially unclosed method: ${methodName}`);
    // This is a complex fix and might be risky to implement automatically
    // We'll just log it so the user knows there might be an issue
  }
}

// Fix 8: Ensure consistent event emitter implementation
if (content.includes('emitEvent(')) {
  const emitPattern = /this\.emitEvent\((['"])(\w+)(['"])\s*,\s*(.+?)\)/g;
  content = content.replace(emitPattern, (match, q1, eventType, q3, data) => {
    // Ensure consistent event handling with type checking
    return `this.emitEvent('${eventType}', ${data})`;
  });
}

// Write the fixed content back to the file
console.log('Writing fixed content back to VehicleManager.ts...');
fs.writeFileSync(filePath, content);

console.log('VehicleManager.ts has been enhanced!');
console.log('Run the TypeScript linter to check for remaining issues.'); 