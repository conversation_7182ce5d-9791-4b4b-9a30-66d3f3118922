/**
 * Mapbox Configuration Helper
 * 
 * This script provides a standardized way to load the Mapbox access token
 * from environment variables or the parent application.
 * 
 * Usage:
 * 1. Include this script before your mapbox-gl.js script
 * 2. Access the token using window.MAPBOX_ACCESS_TOKEN
 */

(function() {
  // Try multiple ways to get the token
  const getMapboxToken = () => {
    // Check for token in window object (set by Vite)
    if (window.VITE_MAPBOX_ACCESS_TOKEN) {
      return window.VITE_MAPBOX_ACCESS_TOKEN;
    }
    
    // Check for token in parent window (iframe case)
    if (window.parent && window.parent.VITE_MAPBOX_ACCESS_TOKEN) {
      return window.parent.VITE_MAPBOX_ACCESS_TOKEN;
    }
    
    // Try to get from meta tag (can be set by server)
    const metaToken = document.querySelector('meta[name="mapbox-token"]');
    if (metaToken && metaToken.content) {
      return metaToken.content;
    }
    
    // Return empty token if all else fails
    console.warn('Mapbox token not found. Please ensure VITE_MAPBOX_ACCESS_TOKEN is set in your environment.');
    return '';
  };
  
  // Make token available globally
  window.MAPBOX_ACCESS_TOKEN = getMapboxToken();
  
  // If mapboxgl is already loaded, set the token
  if (window.mapboxgl) {
    window.mapboxgl.accessToken = window.MAPBOX_ACCESS_TOKEN;
  }
})(); 