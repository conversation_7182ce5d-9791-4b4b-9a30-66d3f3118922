/**
 * This script injects the Mapbox token from .env file to the window object
 * to ensure all debug and test pages use the correct token.
 * 
 * To use: include this script in the <head> of your HTML files
 * <script src="/inject-mapbox-token.js"></script>
 */

// This placeholder will be replaced at build time by Vite
window.VITE_MAPBOX_ACCESS_TOKEN = 'pk.eyJ1Ijoid3dtcyIsImEiOiJjbHdrYWQ0eXAxNGM1MmptbTd4YXg2NGxqIn0.tAM-9pPFtZHoVAzuDuLkUg';

// If mapboxgl is already loaded, also set its access token
if (window.mapboxgl) {
  window.mapboxgl.accessToken = window.VITE_MAPBOX_ACCESS_TOKEN;
}

console.log('Mapbox token injected from environment: ' + 
            (window.VITE_MAPBOX_ACCESS_TOKEN ? 'Success' : 'Failed')); 