<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Mapbox Test</title>
    <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no">
    <!-- Add meta tag that can be populated by server -->
    <meta name="mapbox-token" content="" />
    <!-- Include our helper scripts before mapbox -->
    <script src="./mapbox-config.js"></script>
    <script src="./inject-mapbox-token.js"></script>
    <link href="https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css" rel="stylesheet">
    <script src="https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js"></script>
    <style>
        body { margin: 0; padding: 0; }
        #map { position: absolute; top: 0; bottom: 0; width: 100%; }
    </style>
</head>
<body>
    <div id="map"></div>
    <script>
        // No need to set token - it's already set by the helper scripts
        
        // Initialize the map
        const map = new mapboxgl.Map({
            container: 'map',
            style: 'mapbox://styles/mapbox/streets-v11',
            center: [-6.8498, 31.7917], // Morocco
            zoom: 5.5
        });
        
        // Log errors
        map.on('error', function(e) {
            console.error('Mapbox Error:', e.error);
        });
        
        // Log success
        map.on('load', function() {
            console.log('Map loaded successfully!');
            // Log the token source for debugging (masked for security)
            const tokenSource = window.MAPBOX_ACCESS_TOKEN ? 'Environment' : 'Not found';
            const tokenPrefix = window.mapboxgl.accessToken.substring(0, 8);
            const tokenSuffix = window.mapboxgl.accessToken.substring(window.mapboxgl.accessToken.length - 4);
            console.log(`Token source: ${tokenSource}, Token: ${tokenPrefix}...${tokenSuffix}`);
        });
    </script>
</body>
</html> 