/**
 * VehicleMarker Debug Script
 * Run this script in the browser console to debug the vehicle marker
 */

(function() {
  console.log('🚀 Vehicle Marker Debug Script');
  
  // Helper to find all vehicle markers in the DOM
  function findVehicleMarkers() {
    const markers = document.querySelectorAll('.vehicle-marker, .mapboxgl-marker');
    console.log(`Found ${markers.length} marker elements:`, markers);
    
    // Log details of each marker
    markers.forEach((marker, index) => {
      const computed = window.getComputedStyle(marker);
      console.log(`Marker #${index}:`, {
        id: marker.id,
        className: marker.className,
        display: computed.display,
        visibility: computed.visibility,
        opacity: computed.opacity,
        zIndex: computed.zIndex,
        position: computed.position,
        transform: computed.transform
      });
    });
    
    return markers;
  }
  
  // Helper to force show a specific marker
  function forceShowMarker(marker) {
    if (!marker) {
      console.error('No marker provided to forceShowMarker');
      return;
    }
    
    marker.style.display = 'block';
    marker.style.visibility = 'visible';
    marker.style.opacity = '1';
    marker.style.zIndex = '10000';
    
    console.log('Forced marker visibility:', marker);
    return marker;
  }
  
  // Helper to fix all vehicle markers
  function fixAllMarkers() {
    const markers = findVehicleMarkers();
    markers.forEach(forceShowMarker);
    return markers.length;
  }
  
  // Helper to check TravelAnimator state
  function checkTravelAnimatorState() {
    // Find all matching React component instances
    const instances = Array.from(document.querySelectorAll('[data-testid]'))
      .filter(el => el.getAttribute('data-testid')?.includes('travel-animator'));
    
    console.log('Found TravelAnimator instances:', instances);
    
    // Try to get React component info
    if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
      console.log('React DevTools hook available, could extract component data');
    }
    
    return instances;
  }
  
  // Helper to check the map
  function checkMap() {
    // Look for the Mapbox container
    const mapContainer = document.querySelector('.mapboxgl-map');
    if (!mapContainer) {
      console.error('No Mapbox map found on page');
      return null;
    }
    
    console.log('Map container found:', mapContainer);
    
    // Check for canvas
    const canvas = mapContainer.querySelector('canvas');
    console.log('Map canvas:', canvas);
    
    // Check for map instance in window
    if (window.map) {
      console.log('Map instance found in window.map:', window.map);
      return window.map;
    }
    
    // Look for other possible map variables
    const possibleMapVars = ['map', '_map', 'mapInstance', 'mapRef'];
    for (const varName of possibleMapVars) {
      if (window[varName]) {
        console.log(`Map found in window.${varName}:`, window[varName]);
        return window[varName];
      }
    }
    
    console.warn('No map instance found in window variables');
    return mapContainer;
  }
  
  // Helper to create a test vehicle marker
  function createTestMarker() {
    const map = checkMap();
    if (!map || !map.addLayer) {
      console.error('Cannot create test marker - valid map not found');
      return;
    }
    
    // Create marker element
    const el = document.createElement('div');
    el.className = 'test-vehicle-marker';
    el.style.width = '30px';
    el.style.height = '30px';
    el.style.borderRadius = '50%';
    el.style.backgroundColor = 'red';
    el.style.border = '3px solid white';
    el.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.5)';
    el.style.zIndex = '10000';
    
    // Add text label
    const label = document.createElement('div');
    label.innerText = 'TEST';
    label.style.position = 'absolute';
    label.style.top = '-20px';
    label.style.left = '50%';
    label.style.transform = 'translateX(-50%)';
    label.style.color = 'white';
    label.style.backgroundColor = 'black';
    label.style.padding = '2px 4px';
    label.style.borderRadius = '3px';
    label.style.fontSize = '10px';
    
    el.appendChild(label);
    
    // Get center position of map
    const center = map.getCenter ? map.getCenter() : { lng: -8.008, lat: 31.63 };
    
    try {
      // Create and add the marker using Mapbox GL
      const marker = new mapboxgl.Marker({
        element: el,
        anchor: 'center'
      })
        .setLngLat([center.lng, center.lat])
        .addTo(map);
      
      console.log('Test marker created at center:', [center.lng, center.lat]);
      
      window.testMarker = marker;
      return marker;
    } catch (error) {
      console.error('Error creating test marker:', error);
      
      // Fallback approach - inject directly into DOM
      el.style.position = 'absolute';
      el.style.top = '50%';
      el.style.left = '50%';
      el.style.transform = 'translate(-50%, -50%)';
      
      const mapContainer = document.querySelector('.mapboxgl-map');
      if (mapContainer) {
        mapContainer.appendChild(el);
        console.log('Fallback test marker added to map container');
        window.testMarkerElement = el;
        return el;
      }
    }
    
    return null;
  }
  
  // Interface for vehicle debugging
  window.vehicleDebug = {
    findVehicleMarkers,
    forceShowMarker,
    fixAllMarkers,
    checkTravelAnimatorState,
    checkMap,
    createTestMarker,
    
    // Get access to the vehicle marker from TravelAnimator
    getVehiclePosition: function() {
      try {
        if (window.lastVehicleMarker) {
          const pos = window.lastVehicleMarker.getLngLat();
          console.log('Vehicle position from window.lastVehicleMarker:', pos);
          return pos;
        }
        
        // Look for vehicle position state in any React components
        console.warn('Cannot find vehicle position - window.lastVehicleMarker not available');
        return null;
      } catch (error) {
        console.error('Error getting vehicle position:', error);
        return null;
      }
    },
    
    // Force recreate vehicle marker
    recreateVehicleMarker: function() {
      // Find vehicle marker reference in TravelAnimator
      if (window.lastVehicleMarker) {
        // Remove old marker
        window.lastVehicleMarker.remove();
        
        // Get position
        const pos = this.getVehiclePosition() || { lng: -8.008, lat: 31.63 };
        
        // Create marker element
        const el = document.createElement('div');
        el.className = 'vehicle-marker-debug';
        el.style.width = '25px';
        el.style.height = '25px';
        el.style.borderRadius = '50%';
        el.style.backgroundColor = '#00FF00';
        el.style.border = '3px solid white';
        el.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.5)';
        el.style.zIndex = '10000';
        
        // Add text label
        const label = document.createElement('div');
        label.innerText = 'FIX';
        label.style.position = 'absolute';
        label.style.top = '-20px';
        label.style.left = '50%';
        label.style.transform = 'translateX(-50%)';
        label.style.color = 'white';
        label.style.backgroundColor = 'black';
        label.style.padding = '2px 4px';
        label.style.borderRadius = '3px';
        label.style.fontSize = '10px';
        
        el.appendChild(label);
        
        // Create and add the marker using Mapbox GL
        const map = checkMap();
        if (map && map.addLayer) {
          const marker = new mapboxgl.Marker({
            element: el,
            anchor: 'center'
          })
            .setLngLat([pos.lng, pos.lat])
            .addTo(map);
          
          console.log('Debug vehicle marker recreated at:', [pos.lng, pos.lat]);
          
          window.debugVehicleMarker = marker;
          return marker;
        }
      }
      
      console.error('Cannot recreate vehicle marker - no reference available');
      return null;
    }
  };
  
  console.log('✅ Vehicle debug tools loaded. Access via window.vehicleDebug');
  console.log('Available methods:');
  console.log('- vehicleDebug.findVehicleMarkers()');
  console.log('- vehicleDebug.fixAllMarkers()');
  console.log('- vehicleDebug.checkMap()');
  console.log('- vehicleDebug.createTestMarker()');
  console.log('- vehicleDebug.getVehiclePosition()');
  console.log('- vehicleDebug.recreateVehicleMarker()');
})(); 