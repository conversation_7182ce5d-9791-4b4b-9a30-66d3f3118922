/**
 * Enhanced Vehicle Marker Test
 * This script tests the enhanced vehicle marker implementation with 3D effects and terrain-specific features.
 */

// Configuration
const config = {
  mapboxToken: 'pk.your-mapbox-token-here', // Replace with your actual token
  startingPoint: [-7.9811, 31.6295], // Marrakech
  mountainPoint: [-5.5501, 31.1296], // Atlas Mountains
  desertPoint: [-4.0214, 31.1577], // Desert region
  cityPoint: [-9.5981, 30.4235], // Essaouira
  animationSpeed: 1000, // ms between points
  // Define routes through different terrain types
  routes: {
    mountain: [
      [-7.9811, 31.6295], // Marrakech
      [-7.7500, 31.6000],
      [-7.5000, 31.5500],
      [-7.2500, 31.5000],
      [-6.9000, 31.4500],
      [-6.6500, 31.4000],
      [-6.4000, 31.3500],
      [-6.1500, 31.3000],
      [-5.9000, 31.2500],
      [-5.5501, 31.1296]  // Atlas Mountains
    ],
    desert: [
      [-7.9811, 31.6295], // Marrakech
      [-7.5000, 31.5000],
      [-7.0000, 31.4000],
      [-6.5000, 31.3000],
      [-6.0000, 31.2000],
      [-5.5000, 31.1500],
      [-5.0000, 31.1400],
      [-4.5000, 31.1450],
      [-4.0214, 31.1577]  // Desert
    ],
    city: [
      [-7.9811, 31.6295], // Marrakech
      [-8.1000, 31.4000],
      [-8.3000, 31.2000],
      [-8.5000, 31.0000],
      [-8.7000, 30.8000],
      [-9.0000, 30.6000],
      [-9.2000, 30.5000],
      [-9.4000, 30.4500],
      [-9.5981, 30.4235]  // Essaouira
    ]
  }
};

// State variables
let map;
let vehicleMarker;
let currentRoute = 'mountain';
let currentIndex = 0;
let animationInterval;
let isAnimating = false;
let markerContainer;
let effectsContainer;

// Initialize map on page load
document.addEventListener('DOMContentLoaded', () => {
  initializeMap();
  setupControls();
});

// Initialize Mapbox map
function initializeMap() {
  mapboxgl.accessToken = config.mapboxToken;
  
  map = new mapboxgl.Map({
    container: 'map',
    style: 'mapbox://styles/mapbox/outdoors-v11',
    center: config.startingPoint,
    zoom: 8,
    pitch: 40,
    bearing: 0
  });
  
  map.on('load', () => {
    // Create marker containers
    createContainers();
    
    // Add route lines
    addRouteLines();
    
    // Display the routes 
    displayRoutesOnMap();
    
    console.log('Map initialized and ready for testing');
  });
}

// Create DOM containers for markers and effects
function createContainers() {
  // Create marker container
  markerContainer = document.createElement('div');
  markerContainer.id = 'vehicle-marker-container';
  markerContainer.style.position = 'absolute';
  markerContainer.style.top = '0';
  markerContainer.style.left = '0';
  markerContainer.style.width = '100%';
  markerContainer.style.height = '100%';
  markerContainer.style.pointerEvents = 'none';
  markerContainer.style.zIndex = '3000';
  map.getContainer().appendChild(markerContainer);
  
  // Create effects container
  effectsContainer = document.createElement('div');
  effectsContainer.id = 'vehicle-effects-container';
  effectsContainer.style.position = 'absolute';
  effectsContainer.style.top = '0';
  effectsContainer.style.left = '0';
  effectsContainer.style.width = '100%';
  effectsContainer.style.height = '100%';
  effectsContainer.style.pointerEvents = 'none';
  effectsContainer.style.zIndex = '2900';
  map.getContainer().appendChild(effectsContainer);
  
  // Add animation styles
  const styleEl = document.createElement('style');
  styleEl.id = 'vehicle-marker-style';
  styleEl.textContent = `
    @keyframes vehicle-pulse {
      0% { transform: scale(1); opacity: 0.7; }
      50% { transform: scale(1.5); opacity: 0.3; }
      100% { transform: scale(1); opacity: 0.7; }
    }
    .vehicle-pulse {
      animation: vehicle-pulse 1.5s ease-out infinite;
    }
    
    @keyframes dust-particle {
      0% { transform: translate(0, 0) scale(1); opacity: 0.7; }
      100% { transform: translate(var(--x, -10px), var(--y, 10px)) scale(0); opacity: 0; }
    }
    
    @keyframes vehicle-hover {
      0% { transform: translateY(0); }
      50% { transform: translateY(-3px); }
      100% { transform: translateY(0); }
    }
    
    .vehicle-hover {
      animation: vehicle-hover 2s ease-in-out infinite;
    }
    
    @keyframes mountain-rock {
      0% { transform: translate(0, 0) rotate(0deg); opacity: 0.8; }
      100% { transform: translate(var(--x, -15px), var(--y, 15px)) rotate(var(--r, 45deg)); opacity: 0; }
    }
  `;
  document.head.appendChild(styleEl);
}

// Add route lines to the map
function addRouteLines() {
  // Add mountain route
  map.addSource('mountain-route', {
    type: 'geojson',
    data: {
      type: 'Feature',
      properties: {},
      geometry: {
        type: 'LineString',
        coordinates: config.routes.mountain
      }
    }
  });
  
  map.addLayer({
    id: 'mountain-route',
    type: 'line',
    source: 'mountain-route',
    layout: {
      'line-join': 'round',
      'line-cap': 'round'
    },
    paint: {
      'line-color': '#c0392b',
      'line-width': 3
    }
  });
  
  // Add desert route
  map.addSource('desert-route', {
    type: 'geojson',
    data: {
      type: 'Feature',
      properties: {},
      geometry: {
        type: 'LineString',
        coordinates: config.routes.desert
      }
    }
  });
  
  map.addLayer({
    id: 'desert-route',
    type: 'line',
    source: 'desert-route',
    layout: {
      'line-join': 'round',
      'line-cap': 'round'
    },
    paint: {
      'line-color': '#e67e22',
      'line-width': 3
    }
  });
  
  // Add city route
  map.addSource('city-route', {
    type: 'geojson',
    data: {
      type: 'Feature',
      properties: {},
      geometry: {
        type: 'LineString',
        coordinates: config.routes.city
      }
    }
  });
  
  map.addLayer({
    id: 'city-route',
    type: 'line',
    source: 'city-route',
    layout: {
      'line-join': 'round',
      'line-cap': 'round'
    },
    paint: {
      'line-color': '#2980b9',
      'line-width': 3
    }
  });
}

// Display route endpoints on the map
function displayRoutesOnMap() {
  // Add Marrakech marker
  new mapboxgl.Marker({ color: '#333' })
    .setLngLat(config.startingPoint)
    .setPopup(new mapboxgl.Popup().setHTML('<h3>Marrakech</h3><p>Starting point</p>'))
    .addTo(map);
  
  // Add Mountain destination
  new mapboxgl.Marker({ color: '#c0392b' })
    .setLngLat(config.mountainPoint)
    .setPopup(new mapboxgl.Popup().setHTML('<h3>Atlas Mountains</h3><p>Mountain terrain</p>'))
    .addTo(map);
  
  // Add Desert destination
  new mapboxgl.Marker({ color: '#e67e22' })
    .setLngLat(config.desertPoint)
    .setPopup(new mapboxgl.Popup().setHTML('<h3>Sahara Desert</h3><p>Desert terrain</p>'))
    .addTo(map);
  
  // Add City destination
  new mapboxgl.Marker({ color: '#2980b9' })
    .setLngLat(config.cityPoint)
    .setPopup(new mapboxgl.Popup().setHTML('<h3>Essaouira</h3><p>City terrain</p>'))
    .addTo(map);
}

// Setup UI controls
function setupControls() {
  // Route selection
  document.getElementById('mountain-route-btn').addEventListener('click', () => {
    currentRoute = 'mountain';
    resetAnimation();
    map.flyTo({
      center: config.routes.mountain[0],
      zoom: 8,
      pitch: 40,
      bearing: 0,
      duration: 1000
    });
    document.getElementById('status').innerText = 'Selected mountain route';
  });
  
  document.getElementById('desert-route-btn').addEventListener('click', () => {
    currentRoute = 'desert';
    resetAnimation();
    map.flyTo({
      center: config.routes.desert[0],
      zoom: 8,
      pitch: 40,
      bearing: 0,
      duration: 1000
    });
    document.getElementById('status').innerText = 'Selected desert route';
  });
  
  document.getElementById('city-route-btn').addEventListener('click', () => {
    currentRoute = 'city';
    resetAnimation();
    map.flyTo({
      center: config.routes.city[0],
      zoom: 8,
      pitch: 40,
      bearing: 0,
      duration: 1000
    });
    document.getElementById('status').innerText = 'Selected city route';
  });
  
  // Animation controls
  document.getElementById('start-btn').addEventListener('click', startAnimation);
  document.getElementById('stop-btn').addEventListener('click', stopAnimation);
  document.getElementById('reset-btn').addEventListener('click', resetAnimation);
  document.getElementById('show-3d-btn').addEventListener('click', toggle3DView);
  
  // Display initial status
  document.getElementById('status').innerText = 'Ready to test. Select a route and press Start.';
}

// Create or update the vehicle marker
function createOrUpdateVehicleMarker(position, bearing = 0, terrain = 'default', speed = 1.0) {
  // Remove existing marker
  const existingMarker = document.getElementById('direct-vehicle-marker');
  if (existingMarker && existingMarker.parentNode) {
    existingMarker.parentNode.removeChild(existingMarker);
  }
  
  // Project geographic coordinates to pixel coordinates
  const point = map.project(position);
  
  // Create marker element
  const markerEl = document.createElement('div');
  markerEl.id = 'direct-vehicle-marker';
  markerEl.className = 'vehicle-marker';
  
  // Style the marker
  markerEl.style.position = 'absolute';
  markerEl.style.left = `${point.x}px`;
  markerEl.style.top = `${point.y}px`;
  markerEl.style.transform = 'translate(-50%, -50%)';
  markerEl.style.width = '25px';
  markerEl.style.height = '25px';
  markerEl.style.borderRadius = '50%';
  markerEl.style.backgroundColor = getVehicleColor(terrain);
  markerEl.style.border = '3px solid white';
  markerEl.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.5)';
  markerEl.style.zIndex = '3000';
  markerEl.style.pointerEvents = 'none';
  markerEl.style.visibility = 'visible';
  markerEl.style.display = 'block';
  markerEl.style.opacity = '1';
  
  // Add transition for smooth movement
  markerEl.style.transition = 'left 0.8s cubic-bezier(0.33, 1, 0.68, 1), top 0.8s cubic-bezier(0.33, 1, 0.68, 1)';
  
  // Add a visible label
  const label = document.createElement('div');
  label.innerText = `${terrain.toUpperCase()}`;
  label.style.position = 'absolute';
  label.style.top = '-20px';
  label.style.left = '50%';
  label.style.transform = 'translateX(-50%)';
  label.style.color = 'white';
  label.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
  label.style.padding = '2px 4px';
  label.style.borderRadius = '3px';
  label.style.fontSize = '10px';
  label.style.fontWeight = 'bold';
  label.style.whiteSpace = 'nowrap';
  markerEl.appendChild(label);
  
  // Add direction indicator
  const indicator = document.createElement('div');
  indicator.className = 'vehicle-direction-indicator';
  indicator.style.position = 'absolute';
  indicator.style.width = '0';
  indicator.style.height = '0';
  indicator.style.borderLeft = '8px solid transparent';
  indicator.style.borderRight = '8px solid transparent';
  indicator.style.borderBottom = '16px solid ' + getVehicleColor(terrain);
  indicator.style.top = '-16px';
  indicator.style.left = '50%';
  indicator.style.transform = `translateX(-50%) rotate(${bearing}deg)`;
  indicator.style.transformOrigin = 'bottom center';
  indicator.style.transition = 'transform 0.3s ease-out';
  markerEl.appendChild(indicator);
  
  // Add 3D perspective shadow
  const shadow = document.createElement('div');
  shadow.className = 'vehicle-shadow';
  shadow.style.position = 'absolute';
  shadow.style.width = '25px';
  shadow.style.height = '8px';
  shadow.style.borderRadius = '50%';
  shadow.style.backgroundColor = 'rgba(0, 0, 0, 0.3)';
  shadow.style.bottom = '-8px';
  shadow.style.left = '50%';
  shadow.style.transform = 'translateX(-50%)';
  shadow.style.filter = 'blur(2px)';
  markerEl.appendChild(shadow);
  
  // Add pulse effect
  const pulse = document.createElement('div');
  pulse.className = 'vehicle-pulse';
  pulse.style.position = 'absolute';
  pulse.style.top = '0';
  pulse.style.left = '0';
  pulse.style.right = '0';
  pulse.style.bottom = '0';
  pulse.style.borderRadius = '50%';
  pulse.style.backgroundColor = getVehicleColor(terrain);
  pulse.style.opacity = '0.6';
  pulse.style.animation = 'vehicle-pulse 1.5s ease-out infinite';
  markerEl.appendChild(pulse);
  
  // Add hover effect
  markerEl.style.animation = 'vehicle-hover 2s ease-in-out infinite';
  
  // Add to container
  markerContainer.appendChild(markerEl);
  
  // Create terrain effects
  if (speed > 0.2) {
    createMovementEffects(position, [0, 0], bearing, terrain, speed);
  }
  
  // Store reference in window for debugging
  window.vehicleMarkerDirect = markerEl;
  
  // Update marker position when map moves
  function updateMarkerPosition() {
    if (!markerEl) return;
    
    const point = map.project(position);
    markerEl.style.left = `${point.x}px`;
    markerEl.style.top = `${point.y}px`;
  }
  
  // Add map move listener
  map.on('move', updateMarkerPosition);
}

// Helper function to get vehicle color based on terrain
function getVehicleColor(terrain) {
  switch (terrain) {
    case 'mountain':
      return '#c0392b'; // Red for mountain terrain
    case 'desert':
      return '#e67e22'; // Orange for desert
    case 'city':
      return '#2980b9'; // Blue for city
    default:
      return '#ff6b6b'; // Default red
  }
}

// Create terrain-specific movement effects
function createMovementEffects(position, previousPosition, bearing, terrain, speed) {
  // Clear existing effects
  clearEffects();
  
  // Project positions to pixels
  const currentPoint = map.project(position);
  
  // Determine the number of particles based on speed
  const particleCount = Math.floor(speed * 5);
  
  // Create terrain-specific particles
  switch (terrain) {
    case 'desert':
      // Create dust particles
      for (let i = 0; i < particleCount; i++) {
        createDustParticle(currentPoint.x, currentPoint.y, bearing);
      }
      break;
      
    case 'mountain':
      // Create rock particles
      for (let i = 0; i < particleCount; i++) {
        createRockParticle(currentPoint.x, currentPoint.y, bearing);
      }
      break;
      
    case 'city':
      // No specific particles for city, just a subtle glow effect
      break;
      
    default:
      break;
  }
}

// Clear existing effects
function clearEffects() {
  while (effectsContainer.firstChild) {
    effectsContainer.removeChild(effectsContainer.firstChild);
  }
}

// Helper function to create a dust particle
function createDustParticle(x, y, bearing) {
  const particle = document.createElement('div');
  particle.className = 'dust-particle';
  
  // Calculate position behind the vehicle
  const angle = ((bearing + 180) % 360) * (Math.PI / 180);
  const offsetX = Math.sin(angle) * 10 * (Math.random() * 0.5 + 0.5);
  const offsetY = -Math.cos(angle) * 10 * (Math.random() * 0.5 + 0.5);
  
  // Random spread
  const spread = 10;
  const spreadX = (Math.random() - 0.5) * spread;
  const spreadY = (Math.random() - 0.5) * spread;
  
  // Set styles
  particle.style.position = 'absolute';
  particle.style.width = `${2 + Math.random() * 3}px`;
  particle.style.height = `${2 + Math.random() * 3}px`;
  particle.style.borderRadius = '50%';
  particle.style.backgroundColor = 'rgba(244, 208, 163, 0.8)';
  particle.style.left = `${x + offsetX + spreadX}px`;
  particle.style.top = `${y + offsetY + spreadY}px`;
  
  // Random animation direction
  const moveX = -30 + Math.random() * 60;
  const moveY = -30 + Math.random() * 60;
  
  // Set CSS variables for animation
  particle.style.setProperty('--x', `${moveX}px`);
  particle.style.setProperty('--y', `${moveY}px`);
  
  // Set animation
  particle.style.animation = `dust-particle ${0.5 + Math.random() * 1}s linear forwards`;
  
  // Add to container
  effectsContainer.appendChild(particle);
  
  // Remove after animation completes
  setTimeout(() => {
    if (particle.parentNode === effectsContainer) {
      effectsContainer.removeChild(particle);
    }
  }, 2000);
}

// Helper function to create a rock particle
function createRockParticle(x, y, bearing) {
  const particle = document.createElement('div');
  particle.className = 'rock-particle';
  
  // Calculate position behind the vehicle
  const angle = ((bearing + 180) % 360) * (Math.PI / 180);
  const offsetX = Math.sin(angle) * 12 * (Math.random() * 0.5 + 0.5);
  const offsetY = -Math.cos(angle) * 12 * (Math.random() * 0.5 + 0.5);
  
  // Random spread
  const spread = 8;
  const spreadX = (Math.random() - 0.5) * spread;
  const spreadY = (Math.random() - 0.5) * spread;
  
  // Set styles
  particle.style.position = 'absolute';
  particle.style.width = `${3 + Math.random() * 2}px`;
  particle.style.height = `${3 + Math.random() * 2}px`;
  particle.style.backgroundColor = 'rgba(120, 110, 100, 0.9)';
  particle.style.left = `${x + offsetX + spreadX}px`;
  particle.style.top = `${y + offsetY + spreadY}px`;
  
  // Random animation direction
  const moveX = -20 + Math.random() * 40;
  const moveY = -20 + Math.random() * 40;
  const rotation = Math.random() * 360;
  
  // Set CSS variables for animation
  particle.style.setProperty('--x', `${moveX}px`);
  particle.style.setProperty('--y', `${moveY}px`);
  particle.style.setProperty('--r', `${rotation}deg`);
  
  // Set animation
  particle.style.animation = `mountain-rock ${0.4 + Math.random() * 0.6}s linear forwards`;
  
  // Add to container
  effectsContainer.appendChild(particle);
  
  // Remove after animation completes
  setTimeout(() => {
    if (particle.parentNode === effectsContainer) {
      effectsContainer.removeChild(particle);
    }
  }, 1000);
}

// Start the animation along the selected route
function startAnimation() {
  if (isAnimating) return;
  
  const route = config.routes[currentRoute];
  if (!route || route.length === 0) {
    console.error('No route selected or route is empty');
    return;
  }
  
  isAnimating = true;
  document.getElementById('status').innerText = `Animating along ${currentRoute} route...`;
  
  // Create initial marker at the first point
  const firstPoint = route[0];
  const bearing = 0;
  createOrUpdateVehicleMarker(firstPoint, bearing, currentRoute, 1.0);
  
  // Update camera for animation start
  map.flyTo({
    center: firstPoint,
    zoom: 10,
    pitch: 45,
    bearing: 0,
    duration: 1000
  });
  
  // Start animation interval
  animationInterval = setInterval(() => {
    // Get current point
    const currentPoint = route[currentIndex];
    
    // Get next point
    const nextIndex = (currentIndex + 1) % route.length;
    const nextPoint = route[nextIndex];
    
    // Calculate bearing between points
    const bearing = calculateBearing(currentPoint, nextPoint);
    
    // Create marker
    createOrUpdateVehicleMarker(currentPoint, bearing, currentRoute, 1.0);
    
    // Update camera to follow with some perspective
    map.easeTo({
      center: currentPoint,
      bearing: bearing,
      pitch: 50,
      duration: config.animationSpeed * 0.8
    });
    
    // Move to next point
    currentIndex = nextIndex;
    
    // Check if route is completed
    if (currentIndex === 0) {
      document.getElementById('status').innerText = `Completed ${currentRoute} route. Restarting...`;
    }
  }, config.animationSpeed);
}

// Stop the animation
function stopAnimation() {
  if (!isAnimating) return;
  
  clearInterval(animationInterval);
  isAnimating = false;
  document.getElementById('status').innerText = `Animation stopped at point ${currentIndex}`;
}

// Reset the animation
function resetAnimation() {
  stopAnimation();
  currentIndex = 0;
  document.getElementById('status').innerText = 'Animation reset to start';
  
  // Remove existing marker
  const existingMarker = document.getElementById('direct-vehicle-marker');
  if (existingMarker && existingMarker.parentNode) {
    existingMarker.parentNode.removeChild(existingMarker);
  }
  
  // Clear effects
  clearEffects();
}

// Toggle 3D view
function toggle3DView() {
  const current = map.getPitch();
  const newPitch = current > 0 ? 0 : 60;
  
  map.easeTo({
    pitch: newPitch,
    duration: 1000
  });
  
  document.getElementById('status').innerText = newPitch > 0 ? '3D view enabled' : '2D view enabled';
}

// Calculate bearing between two points
function calculateBearing(point1, point2) {
  const lng1 = point1[0] * Math.PI / 180;
  const lng2 = point2[0] * Math.PI / 180;
  const lat1 = point1[1] * Math.PI / 180;
  const lat2 = point2[1] * Math.PI / 180;
  
  const y = Math.sin(lng2 - lng1) * Math.cos(lat2);
  const x = Math.cos(lat1) * Math.sin(lat2) -
          Math.sin(lat1) * Math.cos(lat2) * Math.cos(lng2 - lng1);
  const bearing = Math.atan2(y, x) * 180 / Math.PI;
  
  return (bearing + 360) % 360;
} 