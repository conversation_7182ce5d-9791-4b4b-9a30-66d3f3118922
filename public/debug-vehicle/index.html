<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vehicle Marker Debug</title>
  <!-- Add meta tag that can be populated by server -->
  <meta name="mapbox-token" content="" />
  <!-- Include our helper script before mapbox -->
  <script src="../mapbox-config.js"></script>
  <script src='https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js'></script>
  <link href='https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css' rel='stylesheet' />
  <style>
    body { margin: 0; padding: 0; }
    #map { position: absolute; top: 0; bottom: 0; width: 100%; }
    .controls {
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 1000;
      background: white;
      padding: 10px;
      border-radius: 4px;
      box-shadow: 0 0 10px rgba(0,0,0,0.2);
    }
    button {
      margin: 5px;
      padding: 5px 10px;
      background: #3B82F6;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background: #2563EB;
    }
    .vehicle-marker {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #FF5500;
      border: 4px solid white;
      box-shadow: 0 0 15px 5px rgba(255, 85, 0, 0.7);
      z-index: 10000;
      position: absolute;
      top: 0;
      left: 0;
      pointer-events: none;
      opacity: 1;
      visibility: visible;
      display: block;
    }
    .vehicle-pulse {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background-color: rgba(255, 85, 0, 0.5);
      animation: pulse 1.5s infinite;
      z-index: 9999;
      opacity: 1;
      visibility: visible;
    }
    @keyframes pulse {
      0% { transform: translate(-50%, -50%) scale(1); opacity: 0.8; }
      50% { transform: translate(-50%, -50%) scale(1.4); opacity: 0.4; }
      100% { transform: translate(-50%, -50%) scale(1); opacity: 0.8; }
    }
  </style>
</head>
<body>
  <div id="map"></div>
  <div class="controls">
    <h3>Vehicle Marker Debug</h3>
    <button id="add-marker">Add Vehicle Marker</button>
    <button id="animate-marker">Animate Vehicle</button>
    <button id="reset-map">Reset Map</button>
    <div id="status">Status: Ready</div>
  </div>

  <script>
    // Token is already set by mapbox-config.js
    // No need to set it again here
    
    // Initialize map
    const map = new mapboxgl.Map({
      container: 'map',
      style: 'mapbox://styles/mapbox/light-v11',
      center: [-8.00833, 31.629499], // Marrakech
      zoom: 10
    });
    
    let vehicleMarker = null;
    let animationId = null;
    const route = [
      [-8.00833, 31.629499], // Marrakech
      [-7.9812, 31.7337],
      [-7.8, 31.8],
      [-7.5, 32.0],
      [-7.2, 32.2],
      [-6.8, 32.5],
      [-6.5, 32.8],
      [-6.0, 33.0],
      [-5.5, 33.5],
      [-5.0, 34.0],
      [-5.81283, 35.78978] // Tangier
    ];
    
    // Create vehicle marker
    function createVehicleMarker(position) {
      // Remove existing marker if any
      if (vehicleMarker) {
        vehicleMarker.remove();
      }
      
      // Create DOM element
      const el = document.createElement('div');
      el.className = 'vehicle-marker';
      el.id = 'vehicle-marker-debug';
      
      // Add pulse effect
      const pulseCircle = document.createElement('div');
      pulseCircle.className = 'vehicle-pulse';
      el.appendChild(pulseCircle);
      
      // Add direction arrow
      const directionIndicator = document.createElement('div');
      directionIndicator.style.position = 'absolute';
      directionIndicator.style.top = '50%';
      directionIndicator.style.left = '50%';
      directionIndicator.style.transform = 'translate(-50%, -50%)';
      directionIndicator.style.borderLeft = '10px solid transparent';
      directionIndicator.style.borderRight = '10px solid transparent';
      directionIndicator.style.borderBottom = '16px solid white';
      directionIndicator.style.width = '0';
      directionIndicator.style.height = '0';
      directionIndicator.style.transformOrigin = 'center';
      directionIndicator.style.zIndex = '10001';
      el.appendChild(directionIndicator);
      
      // Create Mapbox marker
      vehicleMarker = new mapboxgl.Marker({
        element: el,
        anchor: 'center',
        rotationAlignment: 'map',
        pitchAlignment: 'map'
      })
      .setLngLat(position)
      .addTo(map);
      
      // Log success
      console.log('Vehicle marker created at', position);
      document.getElementById('status').textContent = `Status: Marker added at ${position[0].toFixed(4)}, ${position[1].toFixed(4)}`;
      
      return vehicleMarker;
    }
    
    // Animate vehicle along route
    function animateVehicle() {
      let step = 0;
      let progress = 0;
      const duration = 10000; // 10 seconds for the whole route
      const start = performance.now();
      
      function frame(timestamp) {
        const elapsed = timestamp - start;
        progress = Math.min(elapsed / duration, 1);
        
        // Get current position along route
        const pointIndex = Math.min(Math.floor(progress * route.length), route.length - 1);
        const currentPoint = route[pointIndex];
        const nextPoint = pointIndex < route.length - 1 ? route[pointIndex + 1] : null;
        
        // Update marker position
        if (vehicleMarker) {
          vehicleMarker.setLngLat(currentPoint);
          
          // Calculate bearing if next point exists
          if (nextPoint) {
            const bearing = calculateBearing(currentPoint, nextPoint);
            const arrow = vehicleMarker.getElement().querySelector('div:nth-child(2)');
            if (arrow) {
              arrow.style.transform = `translate(-50%, -50%) rotate(${bearing}deg)`;
            }
          }
          
          // Update map view
          map.easeTo({
            center: currentPoint,
            duration: 100
          });
          
          document.getElementById('status').textContent = `Status: Animating - ${Math.round(progress * 100)}% complete`;
        }
        
        // Continue animation if not complete
        if (progress < 1) {
          animationId = requestAnimationFrame(frame);
        } else {
          document.getElementById('status').textContent = 'Status: Animation complete';
        }
      }
      
      // Start animation
      cancelAnimationFrame(animationId);
      animationId = requestAnimationFrame(frame);
    }
    
    // Calculate bearing between two points
    function calculateBearing(start, end) {
      const startLng = start[0] * Math.PI / 180;
      const startLat = start[1] * Math.PI / 180;
      const endLng = end[0] * Math.PI / 180;
      const endLat = end[1] * Math.PI / 180;
      
      const y = Math.sin(endLng - startLng) * Math.cos(endLat);
      const x = Math.cos(startLat) * Math.sin(endLat) - 
                Math.sin(startLat) * Math.cos(endLat) * Math.cos(endLng - startLng);
      
      const bearing = Math.atan2(y, x) * 180 / Math.PI;
      return bearing;
    }
    
    // Add event listeners
    document.getElementById('add-marker').addEventListener('click', () => {
      createVehicleMarker([-8.00833, 31.629499]);
    });
    
    document.getElementById('animate-marker').addEventListener('click', () => {
      if (!vehicleMarker) {
        createVehicleMarker(route[0]);
      }
      animateVehicle();
    });
    
    document.getElementById('reset-map').addEventListener('click', () => {
      if (vehicleMarker) {
        vehicleMarker.remove();
        vehicleMarker = null;
      }
      cancelAnimationFrame(animationId);
      map.flyTo({
        center: [-8.00833, 31.629499],
        zoom: 10
      });
      document.getElementById('status').textContent = 'Status: Reset complete';
    });
    
    // When map loads, add marker automatically
    map.on('load', () => {
      document.getElementById('status').textContent = 'Status: Map loaded';
      createVehicleMarker([-8.00833, 31.629499]);
    });
  </script>
</body>
</html> 