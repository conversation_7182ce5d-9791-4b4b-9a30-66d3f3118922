<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Enhanced Vehicle Marker Test</title>
  <script src='https://api.mapbox.com/mapbox-gl-js/v2.14.1/mapbox-gl.js'></script>
  <link href='https://api.mapbox.com/mapbox-gl-js/v2.14.1/mapbox-gl.css' rel='stylesheet' />
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    }
    
    #app {
      display: flex;
      flex-direction: column;
      height: 100vh;
    }
    
    #map {
      flex: 1;
      position: relative;
    }
    
    #controls {
      background: #fff;
      border-top: 1px solid #ddd;
      padding: 10px 20px;
      display: flex;
      gap: 15px;
      align-items: center;
    }
    
    button {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      background: #3498db;
      color: white;
      font-weight: bold;
      cursor: pointer;
      transition: background 0.2s;
    }
    
    button:hover {
      background: #2980b9;
    }
    
    .route-btn {
      display: flex;
      align-items: center;
      gap: 5px;
    }
    
    .route-btn::before {
      content: '';
      display: block;
      width: 12px;
      height: 12px;
      border-radius: 50%;
    }
    
    #mountain-route-btn::before {
      background-color: #c0392b;
    }
    
    #desert-route-btn::before {
      background-color: #e67e22;
    }
    
    #city-route-btn::before {
      background-color: #2980b9;
    }
    
    #status {
      margin-left: auto;
      font-size: 14px;
      color: #333;
      max-width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  </style>
</head>
<body>
  <div id="app">
    <div id="map"></div>
    <div id="controls">
      <div class="route-selector">
        <button id="mountain-route-btn" class="route-btn">Mountain</button>
        <button id="desert-route-btn" class="route-btn">Desert</button>
        <button id="city-route-btn" class="route-btn">City</button>
      </div>
      <div class="animation-controls">
        <button id="start-btn">Start</button>
        <button id="stop-btn">Stop</button>
        <button id="reset-btn">Reset</button>
        <button id="show-3d-btn">Toggle 3D</button>
      </div>
      <div id="status">Ready to test</div>
    </div>
  </div>
  
  <script src="test-enhanced.js"></script>
</body>
</html> 