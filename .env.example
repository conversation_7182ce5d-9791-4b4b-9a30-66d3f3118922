# Required
ANTHROPIC_API_KEY=your-api-key-here  # For most AI ops -- Format: sk-ant-api03-... (Required)
PERPLEXITY_API_KEY=pplx-abcde        # For research -- Format: pplx-abcde (Optional, Highly Recommended)

# Optional - defaults shown
MODEL=claude-3-7-sonnet-20250219  # Recommended models: claude-3-7-sonnet-20250219, claude-3-opus-20240229 (Required)
PERPLEXITY_MODEL=sonar-pro        # Make sure you have access to sonar-pro otherwise you can use sonar regular (Optional)
MAX_TOKENS=64000                   # Maximum tokens for model responses (Required)
TEMPERATURE=0.2                   # Temperature for model responses (0.0-1.0) - lower = less creativity and follow your prompt closely (Required)
DEBUG=false                       # Enable debug logging (true/false)
LOG_LEVEL=info                    # Log level (debug, info, warn, error)

# ========================================
# TRAVEL APP CONFIGURATION
# ========================================

# Logging Configuration
REACT_APP_DEBUG=false            # Enable verbose logging in development
REACT_APP_LOG_LEVEL=info         # Log level for travel app (error, warn, info, debug, verbose)

# Map Configuration
REACT_APP_MAPBOX_TOKEN=your_mapbox_token_here

# Theme Configuration
REACT_APP_DEFAULT_THEME=morocco  # Default theme (morocco, portugal, white-label)
REACT_APP_CLIENT_THEME=          # Client-specific theme override

# Feature Flags
REACT_APP_ENABLE_ANIMATIONS=true
REACT_APP_ENABLE_POI_DISCOVERY=true
REACT_APP_ENABLE_JOURNEY_TEMPLATES=false
DEFAULT_SUBTASKS=5                # Default number of subtasks when expanding
DEFAULT_PRIORITY=medium           # Default priority for generated tasks (high, medium, low)
PROJECT_NAME=Animation System Verification      # Project name for tasks.json metadata