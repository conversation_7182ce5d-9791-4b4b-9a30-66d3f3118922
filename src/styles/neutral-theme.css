/* Neutral Base Theme 
   Clean, professional styling for client demos */

:root {
  /* Primary Colors */
  --neutral-blue: #2563eb;      /* Professional blue */
  --neutral-gray: #64748b;      /* Neutral gray */
  --neutral-light-blue: #0ea5e9; /* Light blue accent */
  
  /* Neutral Colors */
  --neutral-white: #ffffff;
  --neutral-gray-50: #f8fafc;
  --neutral-gray-100: #f1f5f9;
  --neutral-gray-200: #e2e8f0;
  --neutral-gray-300: #cbd5e1;
  --neutral-gray-400: #94a3b8;
  --neutral-gray-500: #64748b;
  --neutral-gray-600: #475569;
  --neutral-gray-700: #334155;
  --neutral-gray-800: #1e293b;
  --neutral-gray-900: #0f172a;
  
  /* UI Colors */
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --text-light: #ffffff;
  --background-primary: #ffffff;
  --background-secondary: #f8fafc;
  --border-light: #e2e8f0;
  --shadow-soft: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
  
  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  
  /* Font Families */
  --font-display: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-heading: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-body: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Base Layout */
.neutral-layout {
  font-family: var(--font-body);
  color: var(--text-primary);
  background-color: var(--background-primary);
}

/* Typography */
.neutral-layout h1,
.neutral-layout h2,
.neutral-layout h3,
.neutral-layout h4,
.neutral-layout h5,
.neutral-layout h6 {
  font-family: var(--font-heading);
  color: var(--text-primary);
  font-weight: 600;
}

.neutral-layout h1 {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.neutral-layout h2 {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.neutral-layout h3 {
  font-size: 1.5rem;
  line-height: 2rem;
}

/* Buttons */
.neutral-layout .btn-primary {
  background-color: var(--neutral-blue);
  color: var(--neutral-white);
  border: 1px solid var(--neutral-blue);
  border-radius: var(--radius-md);
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.neutral-layout .btn-primary:hover {
  background-color: #1d4ed8;
  border-color: #1d4ed8;
}

.neutral-layout .btn-secondary {
  background-color: var(--neutral-gray-50);
  color: var(--neutral-blue);
  border: 1px solid var(--neutral-gray-200);
  border-radius: var(--radius-md);
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.neutral-layout .btn-secondary:hover {
  background-color: var(--neutral-gray-100);
}

/* Cards and Panels */
.neutral-layout .card {
  background-color: var(--background-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-soft);
}

.neutral-layout .panel {
  background-color: var(--background-primary);
  border-right: 1px solid var(--border-light);
}

/* Navigation */
.neutral-layout .navbar {
  background-color: var(--background-primary);
  border-bottom: 1px solid var(--border-light);
  box-shadow: var(--shadow-soft);
}

/* Map Markers */
.neutral-layout .marker-destination {
  border-color: var(--neutral-blue);
  background-color: var(--neutral-white);
}

.neutral-layout .marker-poi {
  border-color: var(--neutral-gray);
  background-color: var(--neutral-white);
}

.neutral-layout .marker-selected {
  border-color: var(--neutral-light-blue);
  background-color: var(--neutral-light-blue);
  color: var(--neutral-white);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .neutral-layout {
    --radius-sm: 3px;
    --radius-md: 4px;
    --radius-lg: 6px;
  }
  
  .neutral-layout h1 {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
  
  .neutral-layout h2 {
    font-size: 1.5rem;
    line-height: 2rem;
  }
}
