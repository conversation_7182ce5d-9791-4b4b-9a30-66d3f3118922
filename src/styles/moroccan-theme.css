/* Moroccan-Inspired Theme 
   Created for Come To Morocco travel app */

:root {
  /* Primary Colors */
  --morocco-red: #8B1A18; /* Deep terracotta red */
  --morocco-blue: #0047AB; /* Cobalt blue */
  --morocco-yellow: #F2C037; /* Saffron yellow */
  
  /* Neutral Colors */
  --morocco-sand-lightest: #F9F6EE;
  --morocco-sand-light: #E6DFD1;
  --morocco-sand: #D1C4A9;
  --morocco-sand-dark: #B19B7B;
  --morocco-sand-darkest: #8D7966;
  
  /* Accent Colors */
  --morocco-teal: #41B3A3; /* Mediterranean teal */
  --morocco-orange: #E27D60; /* Terracotta orange */
  --morocco-green: #437A16; /* Atlas Mountains green */
  
  /* UI Colors */
  --text-primary: #1A1A1A;
  --text-secondary: #555555;
  --text-light: #FFFFFF;
  --background-primary: var(--morocco-sand-lightest);
  --background-secondary: #FFFFFF;
  --border-light: #E0E0E0;
  --shadow-soft: 0 4px 12px rgba(0, 0, 0, 0.05);
  --shadow-medium: 0 6px 16px rgba(0, 0, 0, 0.1);
  
  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  
  /* Font Families */
  --font-display: 'Cinzel Decorative', Georgia, serif;
  --font-heading: 'Marcellus', Georgia, serif;
  --font-body: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-decorative: 'Scheherazade New', serif;
}

/* Decorative Moroccan Patterns */
.moroccan-pattern-1 {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M54.627 0l.83.828-1.415 1.415L51.8 0h2.827zM5.373 0l-.83.828L5.96 2.243 8.2 0H5.374zM48.97 0l3.657 3.657-1.414 1.414L46.143 0h2.828zM11.03 0L7.372 3.657 8.787 5.07 13.857 0H11.03zm32.284 0L49.8 6.485 48.384 7.9l-7.9-7.9h2.83zM16.686 0L10.2 6.485 11.616 7.9l7.9-7.9h-2.83zm20.97 0l9.315 9.314-1.414 1.414L34.828 0h2.83zM22.344 0L13.03 9.314l1.414 1.414L25.172 0h-2.83zM32 0l12.142 12.142-1.414 1.414L30 .828 17.272 13.556l-1.414-1.414L28 0h4zM.284 0l28 28-1.414 1.414L0 2.544v2.83L26.272 32.97l-1.414 1.414L0 8.685v2.83L22.97 37.556l-1.414 1.414L0 14.828v2.83L19.67 42.142l-1.414 1.414L0 20.97v2.83L16.37 46.726l-1.414 1.414L0 27.113v2.83l12.143 12.142L0 55.23v2.313l13.557-13.557L15.9 45.4 0 61.3v-2.828l13.856-13.856L15.27 46.03 0 61.3v-5.657l15.485-15.485 1.414 1.414L0 61.3v-8.485l14.04-14.04L15.457 40.2 0 55.656v-2.83L15.856 35.97l1.415 1.414L0 55.657v-5.657l12.142-12.142L13.556 39.3 0 52.828v-2.83l8.485-8.485L9.9 42.926 0 52.828v-5.657l5.657-5.657L7.07 42.93 0 50v-2.83l5.356-5.355L6.77 43.23 0 50v-5.657l2.828-2.83L4.243 42.93 0 47.172v-2.83l3.2-3.2 1.414 1.415L0 47.172V50l.343-.343L1.757 51.07 0 52.828v-2.83l.443-.442L1.857 51 0 52.828v-5.657l2.728-2.728L4.143 45.9 0 50v-8.485l5.657-5.657L7.07 37.272 0 44.343v-5.657l8.485-8.485L9.9 31.615 0 41.515v-5.657l12.142-12.142L13.556 25.1 0 38.667v-5.657l15.485-15.485 1.414 1.414L0 35.798v-5.657l18.556-18.557 1.415 1.415L0 32.97v-5.657L21.97 8.454l1.415 1.415L0 30.142v-5.657L25.556 1.028 26.97 2.443 0 27.314v-5.657L28.97-5.857l1.415 1.415-30 30 1.414 1.414L32-2.543l1.414 1.414-30 30 1.414 1.414L35-2.857l1.414 1.414-30 30 1.414 1.414L38-3.17l1.414 1.415-30 30 1.414 1.414L41-3.485l1.414 1.414-30 30 1.414 1.414L44-3.8l1.414 1.414-30 30 1.414 1.414L47-4.115l1.414 1.414-30 30 1.414 1.414L50-4.428l1.414 1.414-30 30 1.414 1.414L53-4.742l1.414 1.414-30 30 1.414 1.414L56-5.056l1.414 1.414-30 30 1.414 1.414L59-5.37l1.414 1.414-30 30 1.414 1.414L60-6v2.828l-30 30 1.414 1.414L60-2.828v5.657l-30 30 1.414 1.414L60 .828v5.657l-30 30 1.414 1.414L60 4.487v5.657l-30 30 1.414 1.414L60 8.145v5.657l-30 30 1.414 1.414L60 11.803v5.657l-30 30 1.414 1.414L60 15.46v5.657l-30 30 1.414 1.414L60 19.12v5.657l-30 30 1.414 1.414L60 22.778v5.657l-30 30 1.414 1.414L60 26.436v5.657l-30 30 1.414 1.414L60 30.094v5.657l-30 30 1.414 1.414L60 33.752v5.657l-30 30 1.414 1.414L60 37.41v5.657l-30 30 1.414 1.414L60 41.068v5.657l-30 30 1.414 1.414L60 44.726v5.657l-30 30 1.414 1.414L60 48.383v5.657l-30 30 1.414 1.414L60 52.04v5.658l-30 30 1.414 1.414L60 55.7v5.657l-30 30 1.414 1.414L60 59.356v5.657l-30 30 1.414 1.414L60 63.014v5.657l-30 30 1.414 1.414L60 66.672v5.657l-30 30 1.414 1.414L60 70.33v5.657l-30 30 1.414 1.414L60 73.987v5.657l-30 30 1.414 1.414L60 77.645v-2.828l-30 30 1.414 1.414L60 74.816v-2.828l-30 30 1.414 1.414L60 71.988v-2.828l-30 30 1.414 1.414L60 69.16v-2.828l-30 30 1.414 1.414L60 66.33v-2.828l-30 30 1.414 1.414L60 63.503v-2.828l-30 30 1.414 1.414L60 60.675v-2.828l-30 30 1.414 1.414L60 57.847v-2.828l-30 30 1.414 1.414L60 55.02v-2.83l-30 30 1.414 1.415L60 52.19v-2.83l-30 30 1.414 1.415L60 49.36v-2.83l-30 30 1.414 1.415 28.59-28.587L60 52.142V49.36L33.784 75.576 32.37 74.162 60 46.533v-2.83L30.428 73.275 29.014 71.86 60 40.875v-2.83l-32.485 32.485-1.414-1.414L60 35.218v-2.83L24.657 71.276l-1.414-1.414L60 29.56v-2.83L20.743 70.09l-1.414-1.414L60 23.904v-2.83l-44.485 44.485-1.414-1.414L60 18.246v-2.83L12.828 68.904l-1.414-1.414L60 12.59v-2.83L8.9 67.718l-1.415-1.414L60 6.93v-2.83L4.97 66.533 3.557 65.12 60 1.272v-2.83L1.04 65.35-.374 63.936 58.284.828 59.7 2.243 30 31.94 1.613 3.556 3.027 2.142 30 29.112 58.686.425 60.1 1.84 30 31.94 27.55 29.488 28.964 28.073 30 29.112 32.45 26.663 33.864 28.077 30 31.94 36.364 25.577 37.778 26.99 30 34.77 33.657 31.113 35.07 32.527 30 37.6 40.97 26.628 42.383 28.042 30 40.425 42.778 27.647 44.192 29.06 30 43.254 46.485 26.77 47.9 28.183 30 46.083 48.97 27.113 50.383 28.526 30 48.91 55.364 23.547 56.778 24.96 30 51.74 52.657 29.082 54.07 30.496 30 54.567 53.536 31.03 54.95 32.444 30 57.395 52.657 34.74 54.07 36.154 30 60.225 54.828 35.397 56.242 36.81 30 63.053 52.657 40.396 54.07 41.81 30 65.882 54.828 41.054 56.242 42.468 30 68.71 52.657 46.053 54.07 47.467 30 71.54 54.828 46.71 56.242 48.126 30 74.368 57 47.682 58.414 49.095 30 77.51L60 47.51l1.414 1.413L30 80.338 58.172 52.166l1.414 1.414L30 83.166l32.485-32.485 1.414 1.414L30 86 57.828 58.17l1.414 1.415L30 88.828l30-30 1.414 1.414L30 91.657l30-30 1.414 1.414L30 94.485l30-30 1.414 1.414L30 97.314l30-30 1.414 1.414L30 100.142l30-30 1.414 1.414L30 102.97l30-30 1.414 1.414L30 105.8l30-30 1.414 1.414L30 108.627l30-30 1.414 1.414L30 111.455l30-30 1.414 1.414L30 114.283l30-30 1.414 1.414L30 117.11l30-30 1.414 1.415L30 119.94l30-30 1.414 1.414L30 122.77 57.828 94.94l1.414 1.414L30 125.597l30-30 1.414 1.414L30 128.425l30-30 1.414 1.414L30 131.254 57.828 103.425l1.414 1.414L30 134.082l30-30 1.414 1.414L30 136.91l28.889-28.889 1.414 1.414L30 139.738l27.778-27.778 1.414 1.414L30 142.566l26.667-26.667 1.414 1.414L30 145.394l25.556-25.556 1.414 1.414L30 148.222l24.444-24.444 1.414 1.414L30 151.05l23.333-23.333 1.414 1.414L30 153.88l22.222-22.222 1.414 1.414L30 156.706l21.11-21.11 1.415 1.414L30 159.534l20-20 1.414 1.414L30 162.362l18.89-18.89 1.413 1.416L30 165.19l17.778-17.778 1.414 1.414L30 168.02l16.667-16.668 1.414 1.414L30 170.846l15.556-15.557 1.414 1.414L30 173.674l14.444-14.444 1.414 1.414L30 176.503l13.333-13.333 1.414 1.414L30 179.33l12.222-12.22 1.414 1.413L30 182.158l11.11-11.11 1.415 1.413L30 184.986l10-10 1.414 1.414L30 187.815l8.89-8.89 1.413 1.414L30 190.643l7.778-7.778 1.414 1.414L30 193.47l6.667-6.667 1.414 1.414L30 196.3l5.556-5.557 1.414 1.414L30 199.127l4.444-4.445 1.414 1.414L30 201.956l3.333-3.334 1.414 1.414L30 204.784l2.222-2.223 1.414 1.414L30 207.612l1.11-1.11 1.415 1.412L30 210.44' fill='%23E27D60' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
}

.moroccan-pattern-2 {
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%230047AB' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E");
}

/* Card styles */
.morocco-card {
  background-color: var(--background-secondary);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-soft);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.morocco-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

/* Button styles */
.morocco-button {
  background-color: var(--morocco-red);
  color: white;
  font-family: var(--font-body);
  font-weight: 600;
  padding: 0.5rem 1.25rem;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease;
}

.morocco-button:hover {
  background-color: #7A1614;
  transform: translateY(-1px);
}

.morocco-button:active {
  transform: translateY(1px);
}

.morocco-button.secondary {
  background-color: var(--morocco-blue);
}

.morocco-button.secondary:hover {
  background-color: #003D91;
}

.morocco-button.outline {
  background-color: transparent;
  color: var(--morocco-red);
  border: 2px solid var(--morocco-red);
}

.morocco-button.outline:hover {
  background-color: rgba(139, 26, 24, 0.05);
}

/* Typography */
.morocco-title {
  font-family: var(--font-display);
  color: var(--text-primary);
  font-weight: 700;
  text-shadow: 1px 1px 2px rgba(139, 26, 24, 0.1);
}

.morocco-subtitle {
  font-family: var(--font-heading);
  color: var(--text-secondary);
  font-weight: 500;
}

.morocco-body {
  font-family: var(--font-body);
  color: var(--text-primary);
}

.morocco-accent {
  font-family: var(--font-decorative);
}

/* Dividers */
.morocco-divider {
  height: 1px;
  background-color: var(--border-light);
  margin: 1rem 0;
  position: relative;
}

.morocco-divider::after {
  content: "";
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 20%;
  height: 3px;
  background-color: var(--morocco-red);
  bottom: -1px;
}

/* Badge */
.morocco-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.morocco-badge.red {
  background-color: rgba(139, 26, 24, 0.1);
  color: var(--morocco-red);
}

.morocco-badge.blue {
  background-color: rgba(0, 71, 171, 0.1);
  color: var(--morocco-blue);
}

.morocco-badge.yellow {
  background-color: rgba(242, 192, 55, 0.1);
  color: #B58700;
}

/* Card with image */
.morocco-image-card {
  position: relative;
  border-radius: var(--radius-md);
  overflow: hidden;
  height: 200px;
}

.morocco-image-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.morocco-image-card:hover img {
  transform: scale(1.05);
}

.morocco-image-card-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 1rem;
  background: linear-gradient(to top, rgba(0,0,0,0.8), rgba(0,0,0,0));
  color: white;
}

/* Scrollbar styling */
.morocco-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: var(--morocco-sand-dark) var(--morocco-sand-lightest);
}

.morocco-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.morocco-scrollbar::-webkit-scrollbar-track {
  background: var(--morocco-sand-lightest);
}

.morocco-scrollbar::-webkit-scrollbar-thumb {
  background-color: var(--morocco-sand-dark);
  border-radius: 20px;
  border: 2px solid var(--morocco-sand-lightest);
}

/* Animation utility classes */
.fade-in {
  animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.slide-up {
  animation: slideUp 0.5s ease forwards;
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Loading indicators */
.morocco-loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(139, 26, 24, 0.2);
  border-radius: 50%;
  border-top-color: var(--morocco-red);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive Design for Mobile */
@media (max-width: 768px) {
  /* Hide panes by default on mobile */
  .left-pane-wrapper,
  .right-pane-wrapper {
    position: fixed;
    top: 0;
    bottom: 0;
    z-index: 30;
    transition: transform 0.3s ease;
    background-color: white;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
  }

  .left-pane-wrapper {
    left: 0;
    transform: translateX(-100%);
  }

  .left-pane-wrapper.visible {
    transform: translateX(0);
  }

  .right-pane-wrapper {
    right: 0;
    transform: translateX(100%);
  }

  .right-pane-wrapper.visible {
    transform: translateX(0);
  }

  .left-pane, .right-pane {
    width: 320px !important;
    height: 100vh;
    max-width: 90vw;
  }

  /* Improve mobile navigation */
  .moroccan-layout .top-bar {
    padding: 0.5rem 1rem;
    height: auto;
    min-height: 60px;
  }

  /* Better mobile city selection */
  .city-selection-overlay {
    flex-wrap: wrap;
    gap: 0.5rem !important;
    padding: 0.75rem !important;
    top: 0.5rem !important;
    left: 0.5rem !important;
    right: 0.5rem !important;
  }

  .city-selection-overlay > div {
    font-size: 0.75rem !important;
    padding: 0.25rem 0.5rem !important;
    min-width: auto !important;
  }

  /* Mobile city buttons */
  .city-selection-overlay button {
    font-size: 0.7rem !important;
    padding: 0.375rem 0.75rem !important;
  }

  /* Mobile day controls */
  .city-selection-overlay .day-controls {
    transform: scale(0.9) !important;
  }

  /* Mobile-friendly POI cards */
  .morocco-card {
    margin-bottom: 0.5rem !important;
    padding: 0.75rem !important;
  }

  /* Adjust mobile menu button visibility */
  .mobile-menu-button {
    display: flex !important;
  }

  /* Hide desktop-only elements on mobile */
  .desktop-only {
    display: none !important;
  }

  /* Mobile journey controls */
  .journey-controls-mobile {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 25;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 0.75rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(242, 192, 55, 0.2);
  }

  /* Mobile overlay for when panes are open */
  .mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 25;
    display: none;
  }

  .mobile-overlay.show {
    display: block;
  }

  /* Adjust main content for mobile */
  .moroccan-layout {
    flex-direction: column;
  }

  .moroccan-layout main {
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
  }

  /* Mobile toggle buttons */
  .pane-toggle-button {
    display: block !important;
  }

  /* Mobile floating buttons */
  .mobile-floating-button {
    width: 56px !important;
    height: 56px !important;
    border-radius: 50% !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    transition: all 0.3s ease !important;
  }

  .mobile-floating-button:active {
    transform: scale(0.95) !important;
  }

  /* Mobile button labels */
  .mobile-button-label {
    font-size: 0.75rem !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 3px rgba(255, 255, 255, 0.8) !important;
  }

  /* Adjust POI modals for mobile */
  .poi-modal {
    width: 95vw !important;
    height: 90vh !important;
    max-width: none !important;
    max-height: none !important;
  }

  /* Adjust POI hover cards for mobile */
  .poi-hover-card {
    max-width: 280px !important;
    font-size: 0.875rem !important;
  }

  /* Mobile-friendly buttons */
  .morocco-journey-button {
    padding: 12px 24px !important;
    font-size: 1.1rem !important;
    min-height: 48px;
  }

  /* Adjust map markers for touch */
  .morocco-marker {
    width: 48px !important;
    height: 48px !important;
  }

  .morocco-marker .marker-pin {
    width: 36px !important;
    height: 36px !important;
  }
}

/* Map markers */
.morocco-marker {
  position: relative;
  width: 40px;
  height: 40px;
  cursor: pointer;
  z-index: 10;
  transform-origin: center bottom;
  animation: pulse 2s infinite;
  transition: transform 0.3s ease;
  filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.5));
}

.morocco-marker .marker-pin {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 30px;
  background-color: #41B3A3;
  border: 4px solid white;
  border-radius: 50%;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.35);
  transition: all 0.3s ease;
}

.morocco-marker:hover {
  transform: scale(1.2);
  z-index: 100;
}

.morocco-marker:hover .marker-pin {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  transform: translate(-50%, -50%) scale(1.1);
}

.destination-marker {
  z-index: 20;
}

.destination-marker .marker-pin {
  background-color: var(--morocco-blue);
  width: 40px;
  height: 40px;
  border: 4px solid white;
}

/* POI Markers */
.poi-marker {
  z-index: 30;
}

.poi-marker .marker-pin {
  background-color: var(--morocco-teal);
  width: 35px;
  height: 35px;
  border: 4px solid white;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.poi-marker:hover {
  transform: scale(1.3);
}

.selected-marker .marker-pin {
  background-color: var(--morocco-blue);
  transform: translate(-50%, -50%) scale(1.1);
  border-color: var(--morocco-yellow);
  border-width: 3px;
}

.marker-label {
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
  text-align: center;
  font-size: 14px;
  font-weight: bold;
  color: var(--text-primary);
  text-shadow: 0 0 5px white, 0 0 5px white, 0 0 5px white;
  pointer-events: none;
  z-index: 5;
}

/* Vehicle marker styles */
.morocco-vehicle-marker {
  width: 40px;
  height: 40px;
  background-color: var(--morocco-blue);
  border-radius: 50%;
  border: 3px solid white;
  box-shadow: var(--shadow-medium);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  transition: transform 0.3s ease;
  z-index: 100;
}

/* Journey button styles */
.morocco-journey-button {
  background-color: var(--morocco-red);
  color: white;
  border: none;
  border-radius: 30px;
  padding: 10px 20px;
  font-family: var(--font-heading);
  font-weight: 600;
  font-size: 1rem;
  box-shadow: var(--shadow-medium);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.morocco-journey-button:hover {
  background-color: var(--morocco-red-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.morocco-journey-button.paused {
  background-color: var(--morocco-orange);
}

.morocco-journey-button.completed {
  background-color: var(--morocco-green);
}

/* Animation notification styles */
.morocco-notification {
  background-color: white;
  border-radius: var(--radius-md);
  padding: 15px;
  box-shadow: var(--shadow-lg);
  border-left: 4px solid var(--morocco-blue);
  max-width: 350px;
  margin: 10px;
  animation: slideIn 0.5s ease;
}

@keyframes slideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.morocco-notification-title {
  font-family: var(--font-heading);
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--morocco-blue);
  margin: 0 0 8px 0;
}

.morocco-notification-content {
  font-family: var(--font-body);
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin: 0 0 12px 0;
}

.morocco-notification-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

/* Make sure panes and map are properly arranged */
.main-content {
  display: flex;
  height: calc(100vh - 80px);
  width: 100%;
  position: relative;
}

.map-container {
  flex: 1;
  position: relative;
  height: 100%;
  z-index: 5;
}

.left-pane-wrapper,
.right-pane-wrapper {
  position: relative;
  height: 100%;
  transition: transform 0.3s ease;
  z-index: 10;
}

.left-pane-wrapper.hidden {
  transform: translateX(-100%);
}

.right-pane-wrapper.hidden {
  transform: translateX(100%);
}

.left-pane,
.right-pane {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.pane-toggle-button {
  transition: all 0.2s ease;
}

.pane-toggle-button:hover {
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 3px 6px rgba(0,0,0,0.15);
}

.pane-toggle-button.left:hover {
  background-color: var(--morocco-red);
  color: white;
}

.pane-toggle-button.right:hover {
  background-color: var(--morocco-blue);
  color: white;
}

/* POI Overlay specific styles */
.poi-overlay {
  position: fixed;
  left: 0;
  top: 80px;
  width: 350px;
  height: calc(100vh - 80px);
  background-color: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  border-radius: 0 12px 12px 0;
  z-index: 50;
  overflow: auto;
  border: 2px solid var(--morocco-blue);
  transform: translateX(0);
  display: block;
  transition: transform 0.3s ease;
}

/* City Selection Box */
.city-selection {
  position: fixed;
  top: 100px;
  left: 50%;
  transform: translateX(-50%);
  width: 600px;
  max-width: 90%;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
  padding: 20px;
  z-index: 50;
  border: 2px solid var(--morocco-blue);
}

/* Make the POI button more visible */
.poi-select-button {
  background-color: var(--morocco-red);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-weight: bold;
  margin-top: 10px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.poi-select-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  background-color: #7A1614;
}

.poi-select-button.selected {
  background-color: var(--morocco-green);
}

/* Ensure map container doesn't hide overlays */
.moroccan-map-container {
  z-index: 1;
  position: relative;
} 