/* Modern POI Panel Styles - Moroccan Theme */

/* ===== Variables ===== */
:root {
  --morocco-terracotta: #E27D60;
  --morocco-blue: #3B82F6;
  --morocco-teal: #2A9D8F;
  --morocco-sand: #F8F0E3;
  --morocco-gold: #F2C94C;
  --morocco-dark: #2D3748;
  --morocco-light: #F9FAFB;
  --panel-radius: 12px;
  --panel-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

/* ===== Common Panel Styles ===== */
.modern-panel-base {
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.97), rgba(255, 255, 255, 0.92));
  backdrop-filter: blur(10px);
  border-radius: var(--panel-radius);
  box-shadow: var(--panel-shadow);
  border: 1px solid rgba(229, 231, 235, 0.8);
  transition: all 0.3s ease;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  z-index: 250; /* Keep z-index for potential desktop overlap needs if not flexed properly */
  pointer-events: auto; /* Ensure panels are interactive */

  /* Desktop default styles (participate in flex) */
  position: relative;
  height: 100%; 
  width: 380px; /* Default width for desktop */
}

/* ===== Left POI Selection Panel ===== */
.modern-selection-panel {
  /* Desktop specific additions if any - mostly width is handled by modern-panel-base now */
}

/* Mobile-specific overrides for fixed positioning */
@media (max-width: 768px) {
  .modern-panel-base {
    position: fixed;
    top: 64px; /* Assuming header is 64px on mobile */
    height: calc(100vh - 64px); /* Full height minus mobile header */
    /* width will be controlled by framer motion or specific mobile panel class if needed */
  }

  .modern-selection-panel {
    /* Framer motion might handle x-translation for slide-in/out */
    /* Ensure width is appropriate for mobile if not set by modern-panel-base or animation */
    width: 320px; /* Or a more mobile-friendly width, adjust as needed */
    left: 0; /* For mobile fixed positioning */
     /* opacity, visibility, transform might be controlled by framer-motion for open/close */
  }
}

.modern-panel-header {
  padding: 20px;
  border-bottom: 1px solid rgba(229, 231, 235, 0.8);
  position: relative;
  background: linear-gradient(135deg, var(--morocco-light), white);
}

.modern-panel-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60%;
  height: 2px;
  background: linear-gradient(to right, transparent, var(--morocco-terracotta), transparent);
}

.modern-panel-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--morocco-dark);
}

.modern-panel-title-icon {
  color: var(--morocco-terracotta);
}

.modern-panel-subtitle {
  font-size: 0.9rem;
  color: var(--morocco-dark);
  opacity: 0.8;
}

/* Search Bar */
.modern-search-bar {
  position: relative;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
}

.modern-search-input {
  width: 100%;
  padding: 10px 15px 10px 40px;
  border-radius: 8px;
  border: 1px solid rgba(229, 231, 235, 0.8);
  background-color: white;
  font-size: 0.95rem;
  transition: all 0.2s ease;
}

.modern-search-input:focus {
  outline: none;
  border-color: var(--morocco-teal);
  box-shadow: 0 0 0 2px rgba(42, 157, 143, 0.2);
}

.modern-search-icon {
  position: absolute;
  left: 35px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--morocco-dark);
  opacity: 0.5;
  width: 16px;
  height: 16px;
}

/* Filter Section */
.modern-filter-section {
  padding: 10px 20px;
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
  max-height: 35%;
  overflow-y: auto;
}

.modern-filter-group {
  margin-bottom: 15px;
}

.modern-filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 8px 0;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--morocco-dark);
  transition: color 0.2s ease;
}

.modern-filter-header:hover {
  color: var(--morocco-terracotta);
}

.modern-filter-label {
  font-weight: 600;
  color: var(--morocco-dark);
}

.modern-filter-content {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}

.modern-filter-chip {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 6px 12px;
  background-color: white;
  border: 1px solid rgba(229, 231, 235, 0.8);
  border-radius: 20px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modern-filter-chip:hover {
  border-color: var(--morocco-teal);
  background-color: rgba(42, 157, 143, 0.05);
}

.modern-filter-chip.active {
  background-color: var(--morocco-teal);
  color: white;
  border-color: var(--morocco-teal);
}

.modern-sort-select {
  width: 100%;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid rgba(229, 231, 235, 0.8);
  background-color: white;
  font-size: 0.9rem;
  margin-top: 8px;
  color: var(--morocco-dark);
}

.modern-sort-select:focus {
  outline: none;
  border-color: var(--morocco-teal);
}

/* POI List */
.modern-poi-list {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.modern-poi-card {
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 15px;
  border: 1px solid rgba(229, 231, 235, 0.8);
  transition: all 0.2s ease;
}

.modern-poi-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
  border-color: var(--morocco-teal);
}

.modern-poi-card.selected {
  border-color: var(--morocco-terracotta);
  box-shadow: 0 0 0 2px rgba(226, 125, 96, 0.2);
}

.modern-poi-card-content {
  display: flex;
  padding: 12px;
}

.modern-poi-card-image-container {
  position: relative;
  width: 100px;
  height: 100px;
  flex-shrink: 0;
  border-radius: 6px;
  overflow: hidden;
}

.modern-poi-card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.modern-poi-card:hover .modern-poi-card-image {
  transform: scale(1.05);
}

.modern-poi-card-rating {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 3px;
}

.modern-poi-card-details {
  margin-left: 12px;
  flex: 1;
}

.modern-poi-card-title {
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 4px;
  color: var(--morocco-dark);
}

.modern-poi-card-location {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.8rem;
  color: var(--morocco-dark);
  opacity: 0.7;
  margin-bottom: 6px;
}

.modern-poi-card-description {
  font-size: 0.85rem;
  color: var(--morocco-dark);
  opacity: 0.8;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.modern-poi-card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.modern-poi-tag {
  font-size: 0.7rem;
  padding: 2px 8px;
  background-color: var(--morocco-sand);
  color: var(--morocco-dark);
  border-radius: 4px;
}

.modern-poi-card-actions {
  padding: 10px 12px;
  border-top: 1px solid rgba(229, 231, 235, 0.5);
  display: flex;
  justify-content: flex-end;
}

.modern-action-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.modern-action-primary {
  background-color: var(--morocco-teal);
  color: white;
}

.modern-action-primary:hover {
  background-color: #249184;
}

.modern-action-secondary {
  background-color: var(--morocco-terracotta);
  color: white;
}

.modern-action-secondary:hover {
  background-color: #d06a50;
}

.modern-panel-footer {
  padding: 15px 20px;
  border-top: 1px solid rgba(229, 231, 235, 0.8);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(248, 240, 227, 0.2);
}

.modern-selection-count {
  font-weight: 600;
  color: var(--morocco-terracotta);
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Empty State */
.modern-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--morocco-dark);
  opacity: 0.7;
  text-align: center;
}

.modern-empty-state svg {
  margin-bottom: 15px;
  color: var(--morocco-terracotta);
}

.modern-empty-subtitle {
  font-size: 0.85rem;
  margin-top: 5px;
}

/* ===== Right POI Journey Panel ===== */
.modern-journey-panel {
  width: 350px;
  right: 0;
}

.modern-journey-header {
  background: linear-gradient(135deg, var(--morocco-terracotta), #d06a50);
  color: white;
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-journey-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modern-journey-subtitle {
  font-size: 0.9rem;
  opacity: 0.9;
  margin-bottom: 15px;
}

.modern-journey-progress {
  margin-bottom: 15px;
}

.modern-progress-bar {
  height: 6px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  overflow: hidden;
  position: relative;
}

.modern-progress-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: white;
  border-radius: 3px;
}

.modern-journey-stats {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
  font-size: 0.85rem;
}

.modern-journey-metrics {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
}

.modern-metric {
  display: flex;
  align-items: center;
  gap: 8px;
}

.modern-metric-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modern-metric-content {
  display: flex;
  flex-direction: column;
}

.modern-metric-value {
  font-weight: 600;
  font-size: 0.95rem;
}

.modern-metric-label {
  font-size: 0.75rem;
  opacity: 0.9;
}

/* Journey Content */
.modern-journey-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px 20px;
}

.modern-day-section {
  margin-bottom: 25px;
}

.modern-day-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.modern-day-badge {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: var(--morocco-blue);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.85rem;
}

.modern-day-title {
  font-weight: 600;
  color: var(--morocco-dark);
}

.modern-timeline {
  position: relative;
  padding-left: 20px;
}

.modern-timeline::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 2px;
  background-color: var(--morocco-blue);
  opacity: 0.3;
}

.modern-timeline-item {
  position: relative;
  padding-bottom: 20px;
  padding-left: 15px;
}

.modern-timeline-item:last-child {
  padding-bottom: 0;
}

.modern-timeline-connector {
  position: absolute;
  top: 10px;
  left: -20px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--morocco-blue);
  border: 2px solid white;
  z-index: 1;
}

.modern-timeline-content {
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(229, 231, 235, 0.8);
  position: relative;
}

.modern-timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 5px;
}

.modern-timeline-title {
  font-weight: 600;
  font-size: 1rem;
  color: var(--morocco-dark);
}

.modern-timeline-time {
  font-size: 0.8rem;
  color: var(--morocco-blue);
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
}

.modern-timeline-location {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.8rem;
  color: var(--morocco-dark);
  opacity: 0.7;
  margin-bottom: 8px;
}

.modern-timeline-description {
  font-size: 0.85rem;
  color: var(--morocco-dark);
  opacity: 0.8;
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.modern-timeline-footer {
  display: flex;
  gap: 12px;
  font-size: 0.75rem;
}

.modern-timeline-weather,
.modern-timeline-duration,
.modern-timeline-cost {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--morocco-dark);
  opacity: 0.8;
}

.weather-icon {
  width: 14px;
  height: 14px;
}

.weather-icon.sunny {
  color: var(--morocco-gold);
}

.weather-icon.cloudy {
  color: #94a3b8;
}

.weather-icon.rainy {
  color: var(--morocco-blue);
}

.weather-icon.windy {
  color: #94a3b8;
}

.modern-timeline-remove {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(229, 231, 235, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s ease;
}

.modern-timeline-content:hover .modern-timeline-remove {
  opacity: 1;
}

.modern-timeline-remove:hover {
  background-color: var(--morocco-terracotta);
  color: white;
}

.modern-timeline-item.drag-over .modern-timeline-content {
  border-color: var(--morocco-blue);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* Journey Summary */
.modern-journey-summary {
  padding: 15px 20px;
  border-top: 1px solid rgba(229, 231, 235, 0.8);
  background-color: rgba(248, 240, 227, 0.2);
}

.modern-summary-title {
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 10px;
  color: var(--morocco-dark);
}

.modern-summary-stats {
  display: flex;
  justify-content: space-between;
}

.modern-summary-stat {
  text-align: center;
}

.modern-stat-value {
  font-weight: 700;
  font-size: 1.2rem;
  color: var(--morocco-terracotta);
}

.modern-stat-label {
  font-size: 0.8rem;
  color: var(--morocco-dark);
  opacity: 0.8;
}

/* Panel Toggle Buttons */
.modern-panel-toggle {
  position: absolute;
  width: 24px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  border: 1px solid rgba(229, 231, 235, 0.8);
  cursor: pointer;
  z-index: 10;
  transition: all 0.2s ease;
}

.modern-panel-toggle:hover {
  background-color: var(--morocco-sand);
}

.modern-panel-toggle-left {
  right: -24px;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 0 6px 6px 0;
  border-left: none;
}

.modern-panel-toggle-right {
  left: -24px;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 6px 0 0 6px;
  border-right: none;
}

.modern-panel-toggle-show-left {
  position: fixed;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 0 6px 6px 0;
  border-left: none;
  z-index: 250;
}

.modern-panel-toggle-show-right {
  position: fixed;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 6px 0 0 6px;
  border-right: none;
  z-index: 250;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .modern-selection-panel,
  .modern-journey-panel {
    width: 100%;
    max-width: 380px;
  }
  
  .modern-panel-toggle-left,
  .modern-panel-toggle-right {
    display: none;
  }
  
  .modern-panel-toggle-show-left,
  .modern-panel-toggle-show-right {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--morocco-teal);
    color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
  
  .modern-panel-toggle-show-left {
    left: 15px;
  }
  
  .modern-panel-toggle-show-right {
    right: 15px;
  }
}

/* Add styling for the selected city badge */
.modern-selected-city-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background-color: var(--primary-color-light);
  color: var(--primary-color);
  padding: 4px 10px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  margin-top: 8px;
}

.modern-selected-city-badge svg {
  color: var(--primary-color);
}