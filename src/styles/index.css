@import './base/reset.css';
@import './components/poi-list.css';
@import './components/city-tags.css';
@import './components/journey-footer.css';
@import './components/journey-sidebar.css';
@import './components/map-controls.css';
@import './components/notification-panel.css';
@import './components/animation-controls.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global styles */
:root {
  --primary: #41B3A3;
  --primary-dark: #3a9f91;
  --secondary: #E27D60;
  --accent: #85CDCA;
  --text-primary: #343a40;
  --text-secondary: #6c757d;
  --bg-light: #f8f9fa;
  --border-light: #e9ecef;
}

body {
  font-family: 'Inter', sans-serif;
  color: var(--text-primary);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Utility classes */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .city-tags-overlay {
    left: 10px;
    right: 10px;
  }
  
  .journey-footer-container {
    padding: 12px 16px;
  }
  
  .journey-stats {
    gap: 16px;
  }
} 