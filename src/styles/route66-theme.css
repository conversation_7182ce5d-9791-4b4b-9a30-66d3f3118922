/**
 * Route 66 Adventure Theme CSS
 * 
 * Classic Americana styling with red, white, blue and vintage road trip aesthetics
 */

/* Import bold Americana fonts */
@import url('https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Oswald:wght@400;500;600;700&family=Russo+One&display=swap');

/* Route 66 Color Palette */
:root {
  /* Classic Americana colors */
  --route66-red: #dc2626;
  --route66-red-light: #ef4444;
  --route66-red-dark: #b91c1c;
  
  /* Patriotic blue */
  --route66-blue: #1e40af;
  --route66-blue-light: #3b82f6;
  --route66-blue-dark: #1e3a8a;
  
  /* Classic white and cream */
  --route66-white: #ffffff;
  --route66-cream: #fef7ed;
  --route66-off-white: #f8fafc;
  
  /* Vintage yellows */
  --route66-yellow: #fbbf24;
  --route66-yellow-light: #fcd34d;
  --route66-yellow-dark: #f59e0b;
  
  /* Road and asphalt colors */
  --route66-asphalt: #374151;
  --route66-asphalt-light: #4b5563;
  --route66-asphalt-dark: #1f2937;
  
  /* Desert and rust colors */
  --route66-rust: #c2410c;
  --route66-desert: #d97706;
  --route66-sand: #fbbf24;
  
  /* Text colors */
  --route66-text-primary: #1f2937;
  --route66-text-secondary: #374151;
  --route66-text-muted: #6b7280;
  
  /* Bold typography */
  --route66-font-display: 'Bebas Neue', cursive;
  --route66-font-heading: 'Russo One', sans-serif;
  --route66-font-body: 'Oswald', sans-serif;
}

/* Route 66 Layout Styling */
.route66-demo {
  font-family: var(--route66-font-body);
  background: linear-gradient(135deg, var(--route66-cream) 0%, var(--route66-off-white) 100%);
  color: var(--route66-text-primary);
  position: relative;
}

/* Add vintage road texture overlay */
.route66-demo::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 98px,
      rgba(220, 38, 38, 0.1) 100px
    );
  pointer-events: none;
  z-index: 1;
}

/* Navigation styling for Route 66 theme */
.route66-demo .neutral-layout header {
  background: linear-gradient(90deg, var(--route66-red) 0%, var(--route66-blue) 50%, var(--route66-red) 100%);
  backdrop-filter: blur(10px);
  border-bottom: 4px solid var(--route66-yellow);
  position: relative;
  z-index: 100;
}

.route66-demo .neutral-layout h1 {
  font-family: var(--route66-font-display);
  color: var(--route66-white);
  font-weight: 400;
  letter-spacing: 2px;
  text-transform: uppercase;
  font-size: 2rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* Button styling */
.route66-demo button {
  font-family: var(--route66-font-heading);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
}

.route66-demo .primary-button {
  background: linear-gradient(135deg, var(--route66-red) 0%, var(--route66-red-light) 100%);
  color: var(--route66-white);
  border: 3px solid var(--route66-yellow);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.route66-demo .primary-button:hover {
  background: linear-gradient(135deg, var(--route66-red-light) 0%, var(--route66-red) 100%);
  box-shadow: 0 6px 16px rgba(220, 38, 38, 0.6);
  transform: translateY(-3px);
  border-color: var(--route66-yellow-light);
}

.route66-demo .secondary-button {
  background: var(--route66-white);
  color: var(--route66-blue);
  border: 3px solid var(--route66-blue);
  font-weight: 700;
}

.route66-demo .secondary-button:hover {
  background: var(--route66-blue);
  color: var(--route66-white);
  border-color: var(--route66-yellow);
}

/* Panel styling */
.route66-demo .left-pane,
.route66-demo .right-pane {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border: 3px solid var(--route66-red);
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(220, 38, 38, 0.2);
  position: relative;
  z-index: 10;
}

.route66-demo .left-pane h2,
.route66-demo .right-pane h2 {
  font-family: var(--route66-font-display);
  color: var(--route66-red);
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 2px;
  border-bottom: 3px solid var(--route66-yellow);
  padding-bottom: 0.5rem;
  font-size: 1.5rem;
}

/* POI Card styling */
.route66-demo .poi-card {
  background: var(--route66-white);
  border: 2px solid var(--route66-blue);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.2);
  transition: all 0.3s ease;
  position: relative;
}

.route66-demo .poi-card:hover {
  box-shadow: 0 8px 24px rgba(30, 64, 175, 0.3);
  transform: translateY(-4px);
  border-color: var(--route66-red);
}

.route66-demo .poi-card h3 {
  font-family: var(--route66-font-heading);
  color: var(--route66-blue);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.route66-demo .poi-card .poi-type {
  background: var(--route66-red);
  color: var(--route66-white);
  font-family: var(--route66-font-display);
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  border: 1px solid var(--route66-yellow);
}

/* Route 66-specific POI types */
.route66-demo .poi-type.landmark {
  background: var(--route66-blue);
  border-color: var(--route66-white);
}

.route66-demo .poi-type.restaurant {
  background: var(--route66-red);
  border-color: var(--route66-yellow);
}

.route66-demo .poi-type.attraction {
  background: var(--route66-yellow);
  color: var(--route66-asphalt);
  border-color: var(--route66-red);
}

.route66-demo .poi-type.accommodation {
  background: var(--route66-asphalt);
  border-color: var(--route66-yellow);
}

.route66-demo .poi-type.nature {
  background: var(--route66-desert);
  border-color: var(--route66-white);
}

/* Map styling */
.route66-demo .mapboxgl-popup-content {
  background: var(--route66-white);
  border: 3px solid var(--route66-red);
  border-radius: 8px;
  font-family: var(--route66-font-body);
  color: var(--route66-text-primary);
}

.route66-demo .mapboxgl-popup-tip {
  border-top-color: var(--route66-red);
}

/* Custom markers for Route 66 theme */
.route66-demo .marker-landmark {
  background: var(--route66-blue);
  border: 3px solid var(--route66-white);
  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.5);
}

.route66-demo .marker-restaurant {
  background: var(--route66-red);
  border: 3px solid var(--route66-yellow);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.5);
}

.route66-demo .marker-attraction {
  background: var(--route66-yellow);
  border: 3px solid var(--route66-red);
  box-shadow: 0 4px 12px rgba(251, 191, 36, 0.5);
}

/* Journey controls */
.route66-demo .journey-controls {
  background: rgba(255, 255, 255, 0.95);
  border: 3px solid var(--route66-blue);
  border-radius: 8px;
  backdrop-filter: blur(8px);
}

.route66-demo .begin-journey-button {
  background: linear-gradient(135deg, var(--route66-red) 0%, var(--route66-blue) 100%);
  color: var(--route66-white);
  font-family: var(--route66-font-display);
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-size: 1.25rem;
  border: 3px solid var(--route66-yellow);
  box-shadow: 0 6px 16px rgba(220, 38, 38, 0.4);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.route66-demo .begin-journey-button:hover {
  box-shadow: 0 8px 20px rgba(220, 38, 38, 0.6);
  transform: translateY(-3px);
  border-color: var(--route66-yellow-light);
}

/* Progress indicators */
.route66-demo .progress-bar {
  background: var(--route66-off-white);
  border: 2px solid var(--route66-asphalt);
}

.route66-demo .progress-fill {
  background: linear-gradient(90deg, var(--route66-red) 0%, var(--route66-blue) 50%, var(--route66-red) 100%);
}

/* Vintage road signs styling */
.route66-demo .road-sign {
  background: var(--route66-white);
  border: 4px solid var(--route66-red);
  border-radius: 8px;
  font-family: var(--route66-font-display);
  color: var(--route66-blue);
  text-transform: uppercase;
  letter-spacing: 2px;
  padding: 1rem;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Scrollbar styling */
.route66-demo ::-webkit-scrollbar {
  width: 12px;
}

.route66-demo ::-webkit-scrollbar-track {
  background: var(--route66-asphalt);
}

.route66-demo ::-webkit-scrollbar-thumb {
  background: var(--route66-red);
  border-radius: 6px;
  border: 2px solid var(--route66-yellow);
}

.route66-demo ::-webkit-scrollbar-thumb:hover {
  background: var(--route66-blue);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .route66-demo .neutral-layout h1 {
    font-size: 1.5rem;
    letter-spacing: 1px;
  }
  
  .route66-demo .poi-card {
    margin-bottom: 1rem;
  }
  
  .route66-demo .left-pane,
  .route66-demo .right-pane {
    margin: 0.5rem;
    border-width: 2px;
  }
  
  .route66-demo .begin-journey-button {
    font-size: 1rem;
    letter-spacing: 1px;
  }
}
