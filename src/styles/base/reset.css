/* MapboxGL Resets */
.mapboxgl-map {
  font-family: 'Inter', sans-serif;
}

/* Reset all Mapbox native markers and location dots */
.mapboxgl-user-location-dot,
.mapboxgl-user-location-dot::before,
.mapboxgl-user-location-dot::after,
.mapboxgl-user-location-accuracy-circle,
.mapboxgl-marker-anchor-center {
  animation: none !important;
  background: none !important;
  border: none !important;
  transition: none !important;
  visibility: hidden !important;
  display: none !important;
  opacity: 0 !important;
}

/* Reset any dynamically created styles */
.mapboxgl-canvas-container style {
  display: none !important;
}

/* Base container styles */
#explore-map-container {
  overflow: visible;
}

/* Base popup styles */
.mapboxgl-popup {
  z-index: 1000;
}

.mapboxgl-popup-content {
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
} 