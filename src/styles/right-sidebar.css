/* Right Sidebar Styling */
.right-sidebar {
  width: 350px;
  height: 100%;
  background-color: rgba(255,255,255,0.92); /* Light mode default */
  border-left: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: absolute;
  right: 0;
  top: 0;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  z-index: 30;
  backdrop-filter: blur(8px);
}

/* Optional: Add dark mode support if you use a dark theme class on body or root */
body.dark .right-sidebar {
  background-color: rgba(24,24,27,0.92);
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid var(--color-border);
}

.sidebar-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 16px;
}

.stats-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-text-primary);
}

.stat-label {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  margin-top: 4px;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.section-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--color-text-secondary);
  margin-bottom: 16px;
  letter-spacing: 0.05em;
}

.destination-card {
  background-color: var(--color-card-background);
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--color-border);
}

.destination-card.drag-over {
  border: 2px dashed var(--color-primary);
}

.destination-content {
  padding: 16px;
  display: flex;
  gap: 12px;
}

.destination-order {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: var(--color-primary);
  color: var(--color-primary-contrast);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.destination-details {
  flex: 1;
}

.destination-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.destination-name {
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 4px;
}

.destination-weather {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--color-text-secondary);
  font-size: 0.875rem;
}

.weather-icon {
  width: 16px;
  height: 16px;
}

.weather-icon.sunny {
  color: #f59e0b;
}

.weather-icon.cloudy {
  color: #94a3b8;
}

.weather-icon.rainy {
  color: #38bdf8;
}

.weather-icon.windy {
  color: #a3a3a3;
}

.days-controls {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.days-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.days-button {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 1px solid var(--color-border);
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.days-button:hover {
  background-color: var(--color-hover);
}

.days-count {
  padding: 0 8px;
  font-weight: 600;
  font-size: 0.875rem;
}

.cost-per-day {
  color: var(--color-text-secondary);
  font-size: 0.75rem;
  margin-top: 4px;
}

.destination-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.activity-badge {
  background-color: var(--color-background-secondary);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  color: var(--color-text-secondary);
}

.action-buttons {
  display: flex;
  gap: 6px;
}

.icon-button {
  width: 28px;
  height: 28px;
  border-radius: 4px;
  background-color: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.icon-button:hover {
  background-color: var(--color-hover);
}

.icon-button.remove:hover {
  color: var(--color-danger);
}

.icon-small {
  width: 16px;
  height: 16px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: var(--color-text-secondary);
}

.empty-icon {
  width: 40px;
  height: 40px;
  opacity: 0.2;
  margin-bottom: 12px;
}

.section-divider {
  height: 1px;
  background-color: var(--color-border);
  margin: 24px 0;
}

.recommended-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.recommended-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.recommended-item:hover {
  background-color: var(--color-hover);
}

.recommended-image {
  width: 48px;
  height: 48px;
  border-radius: 6px;
  background-size: cover;
  background-position: center;
  flex-shrink: 0;
}

.recommended-details {
  flex: 1;
}

.recommended-name {
  font-weight: 500;
  color: var(--color-text-primary);
  margin-bottom: 2px;
  font-size: 0.875rem;
}

.recommended-distance {
  color: var(--color-text-secondary);
  font-size: 0.75rem;
}

.add-icon {
  width: 16px;
  height: 16px;
  opacity: 0;
  transition: opacity 0.2s;
}

.recommended-item:hover .add-icon {
  opacity: 1;
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid var(--color-border);
}

.journey-summary {
  margin-bottom: 16px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 0.875rem;
}

.summary-label {
  color: var(--color-text-secondary);
}

.summary-value {
  font-weight: 500;
  color: var(--color-text-primary);
}

.continue-button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px;
  font-weight: 500;
} 