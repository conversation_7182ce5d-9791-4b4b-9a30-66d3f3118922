/**
 * CORE ANIMATION STYLES - SCOPED AND CONFLICT-FREE
 * Replaces scattered marker styles with centralized, scoped styles
 */

/* Core vehicle marker - highest priority to override conflicts */
#core-vehicle-marker {
  position: absolute !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 999999 !important;
  pointer-events: none !important;
  
  /* Ensure no CSS can hide this */
  width: 16px !important;
  height: 16px !important;
  background: #FF0000 !important;
  border: 2px solid #FFFFFF !important;
  border-radius: 50% !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
  
  /* Transform properties */
  transform-origin: center center !important;
  transition: none !important; /* Disable transitions for smooth animation */
}

/* Ensure no global styles can interfere */
.mapboxgl-map #core-vehicle-marker {
  display: block !important;
}

/* Override any professional.css conflicts */
.mapboxgl-map * #core-vehicle-marker {
  display: block !important;
}

/* Animation performance optimizations */
#core-vehicle-marker {
  will-change: transform, left, top;
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

/* Ensure marker is always above other map elements */
.mapboxgl-marker#core-vehicle-marker {
  z-index: 999999 !important;
}

/* Debug styles for development */
.core-animation-debug #core-vehicle-marker {
  border: 3px solid #00FF00 !important;
  box-shadow: 0 0 10px rgba(0,255,0,0.5) !important;
}
