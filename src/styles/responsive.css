/* src/styles/responsive.css */

/* Hide sidebar by default on smaller screens */
@media (max-width: 768px) { /* Adjust breakpoint as needed, 768px is md */
  .right-sidebar-mobile-hidden {
    display: none;
  }

  .right-sidebar-mobile-overlay {
    position: fixed; /* Or absolute if container is relative */
    top: 0;
    right: 0;
    bottom: 0;
    width: 80%; /* Or a fixed width like 300px */
    max-width: 320px;
    background-color: white; /* Or your theme's background */
    z-index: 1000; /* Ensure it's above the map */
    box-shadow: -2px 0 5px rgba(0,0,0,0.1);
    overflow-y: auto;
    /* Add transition for smooth slide-in/out */
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
  }

  .right-sidebar-mobile-overlay.show {
    transform: translateX(0);
  }

  .explore-map-mobile-full {
    width: 100% !important;
  }
} 