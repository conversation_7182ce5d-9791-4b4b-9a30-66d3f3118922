/**
 * PORTUGAL THEME OVERRIDES
 * Douro Valley inspired color palette and styling
 */

:root {
  /* ========================================
     PORTUGAL BRAND COLORS
     ======================================== */
  
  --primary-color: #1B4332;      /* Deep Forest Green */
  --primary-dark: #081C15;       /* Darker Forest */
  --primary-light: #2D5A3D;      /* Lighter Forest */
  
  --secondary-color: #52796F;    /* Sage Green */
  --secondary-dark: #354F52;     /* Darker Sage */
  --secondary-light: #6A8A82;    /* Lighter Sage */
  
  --accent-color: #DAD7CD;       /* Warm Cream */
  --accent-dark: #A3B18A;        /* Olive Green */
  --accent-light: #F6F3E7;       /* Light Cream */
  
  --success-color: #588157;      /* Natural Green */
  --warning-color: #F77F00;      /* Warm Orange */
  --error-color: #D62828;        /* Deep Red */

  /* ========================================
     PORTUGAL SPECIFIC MARKERS
     ======================================== */
  
  --marker-border-default: #52796F;
  --marker-border-selected: #588157;
  --marker-border-vehicle: #F77F00;
  --marker-bg-default: rgba(246, 243, 231, 0.9);
  --marker-bg-selected: rgba(88, 129, 87, 0.1);

  /* ========================================
     TYPOGRAPHY ADJUSTMENTS
     ======================================== */
  
  --font-family-heading: 'Crimson Text', Georgia, serif;
  
  /* ========================================
     PORTUGAL SPECIFIC SHADOWS
     ======================================== */
  
  --shadow-portugal: 0 4px 12px rgba(27, 67, 50, 0.15);
  --shadow-portugal-lg: 0 8px 24px rgba(27, 67, 50, 0.2);
}

/* ========================================
   PORTUGAL COMPONENT OVERRIDES
   ======================================== */

/* Header styling for Portugal */
.header-portugal {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  border-bottom: 2px solid var(--accent-dark);
}

/* Button styling for Portugal */
.btn-primary-portugal {
  background: var(--primary-color);
  border-color: var(--primary-dark);
  color: var(--accent-color);
}

.btn-primary-portugal:hover {
  background: var(--primary-dark);
  border-color: var(--primary-color);
}

/* Panel styling for Portugal */
.panel-portugal {
  background: rgba(246, 243, 231, 0.95);
  border: 1px solid var(--secondary-light);
  box-shadow: var(--shadow-portugal);
}

/* Map controls for Portugal */
.map-controls-portugal {
  background: var(--accent-color);
  border: 1px solid var(--accent-dark);
}

/* POI cards for Portugal */
.poi-card-portugal {
  background: var(--accent-color);
  border: 1px solid var(--secondary-light);
  box-shadow: var(--shadow-portugal);
}

.poi-card-portugal:hover {
  box-shadow: var(--shadow-portugal-lg);
  transform: translateY(-2px);
}

/* ========================================
   PORTUGAL SPECIFIC ANIMATIONS
   ======================================== */

@keyframes portugal-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.portugal-animate-in {
  animation: portugal-fade-in 0.4s ease-out;
}

/* ========================================
   PORTUGAL RESPONSIVE ADJUSTMENTS
   ======================================== */

@media (max-width: 768px) {
  .panel-portugal {
    background: rgba(246, 243, 231, 0.98);
  }
}

/* ========================================
   PORTUGAL ACCESSIBILITY
   ======================================== */

.portugal-focus:focus {
  outline: 2px solid var(--success-color);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --primary-color: #000000;
    --secondary-color: #333333;
    --accent-color: #FFFFFF;
  }
}
