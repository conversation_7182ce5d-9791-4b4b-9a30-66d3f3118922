/**
 * WHITE-LABEL THEME
 * Generic, customizable theme for client branding
 * Easy to override with client-specific colors
 */

:root {
  /* ========================================
     WHITE-LABEL BRAND COLORS (NEUTRAL)
     ======================================== */
  
  --primary-color: #2563EB;      /* Professional Blue */
  --primary-dark: #1D4ED8;       /* Darker Blue */
  --primary-light: #3B82F6;      /* Lighter Blue */
  
  --secondary-color: #64748B;    /* Slate Gray */
  --secondary-dark: #475569;     /* Darker Slate */
  --secondary-light: #94A3B8;    /* Lighter Slate */
  
  --accent-color: #F59E0B;       /* Amber */
  --accent-dark: #D97706;        /* Darker Amber */
  --accent-light: #FCD34D;       /* Lighter Amber */
  
  --success-color: #10B981;      /* Emerald */
  --warning-color: #F59E0B;      /* Amber */
  --error-color: #EF4444;        /* Red */

  /* ========================================
     WHITE-LABEL MARKERS
     ======================================== */
  
  --marker-border-default: #64748B;
  --marker-border-selected: #10B981;
  --marker-border-vehicle: #F59E0B;
  --marker-bg-default: rgba(255, 255, 255, 0.9);
  --marker-bg-selected: rgba(16, 185, 129, 0.1);

  /* ========================================
     CUSTOMIZABLE BRAND VARIABLES
     Client can override these easily
     ======================================== */
  
  --brand-primary: var(--primary-color);
  --brand-secondary: var(--secondary-color);
  --brand-accent: var(--accent-color);
  
  --brand-font-primary: var(--font-family-primary);
  --brand-font-heading: var(--font-family-primary);
  
  --brand-radius: var(--radius-md);
  --brand-shadow: var(--shadow-md);
}

/* ========================================
   CLIENT CUSTOMIZATION HOOKS
   These can be easily overridden
   ======================================== */

/* Example client override:
:root {
  --brand-primary: #FF6B35;     // Client orange
  --brand-secondary: #004E89;   // Client navy
  --brand-accent: #1A936F;      // Client green
  --brand-font-heading: 'Montserrat', sans-serif;
}
*/

/* ========================================
   WHITE-LABEL COMPONENT OVERRIDES
   ======================================== */

/* Header styling */
.header-white-label {
  background: var(--brand-primary);
  border-bottom: 1px solid var(--brand-secondary);
}

/* Button styling */
.btn-primary-white-label {
  background: var(--brand-primary);
  border-color: var(--brand-primary);
  color: var(--white);
}

.btn-primary-white-label:hover {
  background: var(--primary-dark);
  border-color: var(--primary-dark);
}

.btn-secondary-white-label {
  background: var(--brand-secondary);
  border-color: var(--brand-secondary);
  color: var(--white);
}

/* Panel styling */
.panel-white-label {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--brand-radius);
  box-shadow: var(--brand-shadow);
}

.panel-white-label .panel-header {
  background: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
}

/* POI cards */
.poi-card-white-label {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--brand-radius);
  box-shadow: var(--brand-shadow);
}

.poi-card-white-label:hover {
  border-color: var(--brand-primary);
  box-shadow: var(--shadow-lg);
}

/* Map controls */
.map-controls-white-label {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--brand-radius);
  box-shadow: var(--brand-shadow);
}

/* ========================================
   TYPOGRAPHY OVERRIDES
   ======================================== */

.white-label h1,
.white-label .h1 {
  font-family: var(--brand-font-heading);
  color: var(--brand-primary);
}

.white-label h2,
.white-label .h2 {
  font-family: var(--brand-font-heading);
  color: var(--brand-primary);
}

.white-label .brand-title {
  font-family: var(--brand-font-heading);
  color: var(--brand-primary);
}

/* ========================================
   NEUTRAL ANIMATIONS
   ======================================== */

@keyframes white-label-fade-in {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.white-label-animate-in {
  animation: white-label-fade-in 0.3s ease-out;
}

/* ========================================
   ACCESSIBILITY ENHANCEMENTS
   ======================================== */

.white-label-focus:focus {
  outline: 2px solid var(--brand-primary);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --brand-primary: #000000;
    --brand-secondary: #333333;
    --brand-accent: #666666;
  }
  
  .white-label .panel,
  .white-label .btn {
    border-width: 2px;
  }
}

/* ========================================
   RESPONSIVE ADJUSTMENTS
   ======================================== */

@media (max-width: 768px) {
  .panel-white-label {
    border-radius: var(--radius-sm);
    box-shadow: var(--shadow-sm);
  }
}

/* ========================================
   PRINT STYLES
   ======================================== */

@media print {
  .white-label * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
}

/* ========================================
   CLIENT CUSTOMIZATION EXAMPLES
   ======================================== */

/*
Example 1: Tech Startup Theme
.tech-startup {
  --brand-primary: #6366F1;     // Indigo
  --brand-secondary: #8B5CF6;   // Purple  
  --brand-accent: #06B6D4;      // Cyan
  --brand-font-heading: 'Space Grotesk', sans-serif;
}

Example 2: Nature/Eco Theme  
.eco-theme {
  --brand-primary: #059669;     // Emerald
  --brand-secondary: #0D9488;   // Teal
  --brand-accent: #84CC16;      // Lime
  --brand-font-heading: 'Merriweather', serif;
}

Example 3: Luxury Travel Theme
.luxury-theme {
  --brand-primary: #7C2D12;     // Brown
  --brand-secondary: #A16207;   // Amber
  --brand-accent: #DC2626;      // Red
  --brand-font-heading: 'Playfair Display', serif;
}

Example 4: Corporate Theme
.corporate-theme {
  --brand-primary: #1E40AF;     // Blue
  --brand-secondary: #374151;   // Gray
  --brand-accent: #DC2626;      // Red
  --brand-font-heading: 'Inter', sans-serif;
}
*/
