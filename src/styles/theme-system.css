/**
 * Theme System CSS
 * 
 * Basic theme-aware styling that can be expanded later
 * Provides foundation for multi-theme support
 */

/* ===== BASE THEME VARIABLES ===== */
:root {
  /* Neutral/Default Theme */
  --theme-primary: #2563eb;
  --theme-secondary: #ec4899;
  --theme-accent: #10b981;
  --theme-background: #ffffff;
  --theme-surface: #f8fafc;
  --theme-text: #1e293b;
  --theme-text-muted: #64748b;
  --theme-border: #e2e8f0;
  
  /* Layout */
  --pane-width: 320px;
  --topbar-height: 64px;
  --border-radius: 8px;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

/* ===== MOROCCO THEME ===== */
.theme-morocco {
  --theme-primary: #8b1a18;
  --theme-secondary: #d97706;
  --theme-accent: #059669;
  --theme-background: #fefefe;
  --theme-surface: #faf8f5;
  --theme-text: #1c1917;
  --theme-text-muted: #78716c;
  --theme-border: #e7e5e4;
}

/* ===== NAPA VALLEY THEME ===== */
.theme-napa-valley {
  --theme-primary: #7c2d12;
  --theme-secondary: #a16207;
  --theme-accent: #166534;
  --theme-background: #fefefe;
  --theme-surface: #fefdf8;
  --theme-text: #1c1917;
  --theme-text-muted: #78716c;
  --theme-border: #e7e5e4;
}

/* ===== ROUTE 66 THEME ===== */
.theme-route66 {
  --theme-primary: #dc2626;
  --theme-secondary: #2563eb;
  --theme-accent: #7c2d12;
  --theme-background: #ffffff;
  --theme-surface: #f8fafc;
  --theme-text: #1e293b;
  --theme-text-muted: #64748b;
  --theme-border: #e2e8f0;
}

/* ===== NEUTRAL THEME ===== */
.theme-neutral {
  --theme-primary: #2563eb;
  --theme-secondary: #ec4899;
  --theme-accent: #10b981;
  --theme-background: #ffffff;
  --theme-surface: #f8fafc;
  --theme-text: #1e293b;
  --theme-text-muted: #64748b;
  --theme-border: #e2e8f0;
}

/* ===== LAYOUT COMPONENTS ===== */
.neutral-layout {
  background-color: var(--theme-background);
  color: var(--theme-text);
}

.main-content {
  height: calc(100vh - var(--topbar-height));
}

.map-container {
  background-color: var(--theme-surface);
}

/* ===== MOBILE CONTROLS ===== */
.mobile-pane-controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 12px;
  z-index: 1000;
}

.pane-toggle-btn {
  padding: 8px 16px;
  background-color: var(--theme-primary);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  box-shadow: var(--shadow-md);
  transition: all 0.2s ease;
}

.pane-toggle-btn:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

.pane-toggle-btn:active {
  transform: translateY(0);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  :root {
    --pane-width: 280px;
    --topbar-height: 56px;
  }
  
  .mobile-pane-controls {
    display: flex;
  }
}

@media (min-width: 769px) {
  .mobile-pane-controls {
    display: none;
  }
}

/* ===== DARK THEME SUPPORT ===== */
.dark-theme {
  --theme-background: #0f172a;
  --theme-surface: #1e293b;
  --theme-text: #f1f5f9;
  --theme-text-muted: #94a3b8;
  --theme-border: #334155;
}

/* ===== COMPONENT THEMING HOOKS ===== */
/* These classes can be expanded later for detailed component styling */

.theme-aware-button {
  background-color: var(--theme-primary);
  color: white;
  border: 1px solid var(--theme-primary);
}

.theme-aware-surface {
  background-color: var(--theme-surface);
  border: 1px solid var(--theme-border);
}

.theme-aware-text {
  color: var(--theme-text);
}

.theme-aware-text-muted {
  color: var(--theme-text-muted);
}

/* ===== ANIMATION SUPPORT ===== */
.theme-transition {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* ===== UTILITY CLASSES ===== */
.theme-primary-bg { background-color: var(--theme-primary); }
.theme-secondary-bg { background-color: var(--theme-secondary); }
.theme-accent-bg { background-color: var(--theme-accent); }
.theme-primary-text { color: var(--theme-primary); }
.theme-secondary-text { color: var(--theme-secondary); }
.theme-accent-text { color: var(--theme-accent); }
