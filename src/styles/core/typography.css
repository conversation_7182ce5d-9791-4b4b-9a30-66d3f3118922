/**
 * TYPOGRAPHY SYSTEM
 * Business-aligned typography for multi-client deployment
 */

/* ========================================
   FONT LOADING & OPTIMIZATION
   ======================================== */

/* Preload critical fonts */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: local('Inter Regular'), local('Inter-Regular');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: local('Inter SemiBold'), local('Inter-SemiBold');
}

/* ========================================
   BASE TYPOGRAPHY
   ======================================== */

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--gray-900);
  font-weight: var(--font-weight-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ========================================
   HEADINGS
   ======================================== */

h1, .h1 {
  font-family: var(--font-family-heading);
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--primary-color);
  margin-bottom: var(--space-6);
}

h2, .h2 {
  font-family: var(--font-family-heading);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--primary-color);
  margin-bottom: var(--space-5);
}

h3, .h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--gray-800);
  margin-bottom: var(--space-4);
}

h4, .h4 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  color: var(--gray-800);
  margin-bottom: var(--space-3);
}

h5, .h5 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  color: var(--gray-700);
  margin-bottom: var(--space-3);
}

h6, .h6 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  color: var(--gray-700);
  margin-bottom: var(--space-2);
}

/* ========================================
   BODY TEXT
   ======================================== */

p, .text-body {
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--gray-700);
  margin-bottom: var(--space-4);
}

.text-large {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
}

.text-small {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

.text-xs {
  font-size: var(--font-size-xs);
  line-height: var(--line-height-normal);
}

/* ========================================
   TEXT UTILITIES
   ======================================== */

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

.italic { font-style: italic; }
.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }

/* ========================================
   COLOR UTILITIES
   ======================================== */

.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--secondary-color); }
.text-accent { color: var(--accent-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }

.text-gray-50 { color: var(--gray-50); }
.text-gray-100 { color: var(--gray-100); }
.text-gray-200 { color: var(--gray-200); }
.text-gray-300 { color: var(--gray-300); }
.text-gray-400 { color: var(--gray-400); }
.text-gray-500 { color: var(--gray-500); }
.text-gray-600 { color: var(--gray-600); }
.text-gray-700 { color: var(--gray-700); }
.text-gray-800 { color: var(--gray-800); }
.text-gray-900 { color: var(--gray-900); }

/* ========================================
   BUSINESS SPECIFIC TYPOGRAPHY
   ======================================== */

.brand-title {
  font-family: var(--font-family-heading);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  letter-spacing: -0.025em;
}

.poi-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  line-height: var(--line-height-tight);
}

.poi-description {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  line-height: var(--line-height-normal);
}

.journey-step {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--primary-color);
}

.notification-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
}

/* ========================================
   RESPONSIVE TYPOGRAPHY
   ======================================== */

@media (max-width: 768px) {
  h1, .h1 {
    font-size: var(--font-size-3xl);
  }
  
  h2, .h2 {
    font-size: var(--font-size-2xl);
  }
  
  .brand-title {
    font-size: var(--font-size-2xl);
  }
}

@media (max-width: 480px) {
  h1, .h1 {
    font-size: var(--font-size-2xl);
  }
  
  h2, .h2 {
    font-size: var(--font-size-xl);
  }
  
  .brand-title {
    font-size: var(--font-size-xl);
  }
}

/* ========================================
   ACCESSIBILITY
   ======================================== */

/* Ensure sufficient contrast */
@media (prefers-contrast: high) {
  .text-gray-400,
  .text-gray-500,
  .text-gray-600 {
    color: var(--gray-800);
  }
}

/* Respect user font size preferences */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
  }
}
