/**
 * UNIFIED MARKER SYSTEM - SINGLE SOURCE OF TRUTH
 * Consolidates all marker styles to eliminate conflicts
 * Replaces: consolidated/markers.css, globals.css markers, index.html styles
 */

/* ========================================
   BASE MAPBOX MARKER STYLES
   ======================================== */

.mapboxgl-marker {
  position: absolute !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  cursor: pointer;
  pointer-events: auto !important;
  will-change: transform !important;
  z-index: var(--z-index-poi-markers);
  transform-origin: center center;
}

.mapboxgl-marker:hover {
  z-index: var(--z-index-selected-markers);
}

/* ========================================
   POI MARKERS
   ======================================== */

.poi-marker {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #3B82F6;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: var(--z-index-poi-markers);
  transform: translate(-50%, -50%);
  transition: all 0.2s ease;
}

.poi-marker:hover {
  transform: translate(-50%, -50%) scale(1.1);
  z-index: var(--z-index-selected-markers);
}

.poi-marker.selected {
  background-color: #F59E0B;
  transform: translate(-50%, -50%) scale(1.2);
  z-index: var(--z-index-selected-markers);
  box-shadow: 0 4px 8px rgba(245, 158, 11, 0.4);
}

/* ========================================
   DESTINATION MARKERS
   ======================================== */

.destination-marker {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #10B981;
  border: 3px solid white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  z-index: var(--z-index-destination-markers);
  transform: translate(-50%, -50%);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 12px;
}

.destination-marker:hover {
  transform: translate(-50%, -50%) scale(1.1);
  z-index: var(--z-index-selected-markers);
}

.destination-marker.selected {
  background-color: #41B3A3;
  transform: translate(-50%, -50%) scale(1.1);
  z-index: var(--z-index-selected-markers);
  box-shadow: 0 6px 12px rgba(65, 179, 163, 0.4);
}

/* ========================================
   VEHICLE MARKER - HIGHEST PRIORITY
   ======================================== */

.vehicle-marker {
  /* DO NOT set position, left, top, or transform here - Mapbox handles positioning */
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #FF0000;
  border: 3px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  z-index: 99999;
  pointer-events: none;
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
  /* Let Mapbox handle all positioning - no transform here */
  transition: background-color 0.3s ease, border-color 0.3s ease;
  will-change: auto; /* Don't force transform changes */
}

.vehicle-marker.active {
  z-index: var(--z-index-vehicle-marker-active);
  animation: vehicle-pulse 2s infinite;
}

/* ========================================
   VEHICLE CONTEXTUAL STYLES
   ======================================== */

.vehicle-marker--default {
  border-color: #4A90E2;
  background-color: #4A90E2;
}

.vehicle-marker--city-center {
  border-color: #F5A623;
  background-color: #F5A623;
  box-shadow: 0 0 15px rgba(245, 166, 35, 0.7);
}

.vehicle-marker--city-approach {
  border-color: #F8E71C;
  background-color: #F8E71C;
}

.vehicle-marker--poi-discovery {
  border-color: #7ED321;
  background-color: #7ED321;
  animation: pulse-green 1.5s infinite;
}

.vehicle-marker--poi-nearby {
  border-color: #50E3C2;
  background-color: #50E3C2;
}

.vehicle-marker--terrain-mountain {
  background-color: #A9A9A9;
}

.vehicle-marker--terrain-desert {
  background-color: #FADDAF;
}

.vehicle-marker--terrain-coastal {
  border-style: dashed;
  border-color: #4A90E2;
  background-color: #ADD8E6;
}

.vehicle-marker--scenic {
  box-shadow: 0 0 12px rgba(74, 144, 226, 0.9);
  border-width: 3px;
  background-color: #E6F3FF;
}

/* ========================================
   MARKER ANIMATIONS
   ======================================== */

@keyframes vehicle-pulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); }
  50% { transform: translate(-50%, -50%) scale(1.1); }
}

@keyframes pulse-green {
  0%, 100% { box-shadow: 0 0 5px rgba(126, 211, 33, 0.5); }
  50% { box-shadow: 0 0 20px rgba(126, 211, 33, 0.8); }
}

/* ========================================
   ORDER BADGES
   ======================================== */

.order-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  background-color: #EF4444;
  color: white;
  border-radius: 50%;
  font-size: 10px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: var(--z-index-selected-markers);
}

/* ========================================
   MARKER CONTAINERS
   ======================================== */

.marker-container,
.marker-wrapper,
.mapboxgl-marker-container {
  position: relative !important;
}

/* ========================================
   RESPONSIVE ADJUSTMENTS
   ======================================== */

@media (max-width: 768px) {
  .destination-marker {
    width: 28px;
    height: 28px;
    font-size: 10px;
  }
  
  .vehicle-marker {
    width: 36px;
    height: 36px;
  }
  
  .poi-marker {
    width: 14px;
    height: 14px;
  }
}
