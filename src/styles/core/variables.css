/**
 * CORE CSS VARIABLES
 * Business-aligned design system for multi-client deployment
 * Supports Morocco, Portugal, and white-label themes
 */

:root {
  /* ========================================
     Z-INDEX HIERARCHY (BUSINESS PRIORITY)
     CONSOLIDATED - SINGLE SOURCE OF TRUTH
     ======================================== */

  /* Base layers */
  --z-index-base: 1;
  --z-index-map-base: 10;

  /* Marker layers (ordered by importance) */
  --z-index-poi-markers: 100;
  --z-index-destination-markers: 200;
  --z-index-selected-markers: 300;
  --z-index-vehicle-marker: 1000;
  --z-index-vehicle-marker-active: 1100;

  /* UI layers */
  --z-index-popups: 2000;
  --z-index-controls: 3000;
  --z-index-overlays: 4000;
  --z-index-notifications: 5000;
  --z-index-modals: 6000;
  --z-index-debug: 9999;
  
  --z-debug: 9999;           /* Development debugging */
  --z-modal: 5000;           /* Critical business interactions */
  --z-overlay: 4000;         /* Full-screen overlays */
  --z-dropdown: 3000;        /* Dropdown menus */
  --z-header: 2000;          /* Navigation header */
  --z-sidebar: 1200;         /* Side panels */
  --z-notification: 1000;    /* User feedback */
  --z-controls: 800;         /* UI controls & buttons */
  --z-popups: 500;          /* Contextual information */
  --z-vehicle: 200;         /* Journey visualization */
  --z-markers-selected: 15; /* Selected POI discovery */
  --z-markers: 10;          /* POI discovery */
  --z-base: 1;              /* Map foundation */

  /* ========================================
     BRAND COLORS - MOROCCO (DEFAULT)
     ======================================== */
  
  --primary-color: #0047AB;      /* Morocco Blue */
  --primary-dark: #003080;       /* Darker Morocco Blue */
  --primary-light: #4A90E2;      /* Lighter Morocco Blue */
  
  --secondary-color: #41B3A3;    /* Teal */
  --secondary-dark: #2E8B7A;     /* Darker Teal */
  --secondary-light: #5FCFC0;    /* Lighter Teal */
  
  --accent-color: #FF6B35;       /* Orange */
  --accent-dark: #E55A2B;        /* Darker Orange */
  --accent-light: #FF8A5C;       /* Lighter Orange */
  
  --success-color: #1DE9B6;      /* Success Green */
  --warning-color: #FFB74D;      /* Warning Orange */
  --error-color: #F44336;        /* Error Red */
  
  /* ========================================
     NEUTRAL COLORS
     ======================================== */
  
  --white: #FFFFFF;
  --gray-50: #FAFAFA;
  --gray-100: #F5F5F5;
  --gray-200: #EEEEEE;
  --gray-300: #E0E0E0;
  --gray-400: #BDBDBD;
  --gray-500: #9E9E9E;
  --gray-600: #757575;
  --gray-700: #616161;
  --gray-800: #424242;
  --gray-900: #212121;
  --black: #000000;

  /* ========================================
     TYPOGRAPHY
     ======================================== */
  
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-heading: 'Playfair Display', Georgia, serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', monospace;
  
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* ========================================
     SPACING SYSTEM
     ======================================== */
  
  --space-0: 0;
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */

  /* ========================================
     BORDER RADIUS
     ======================================== */
  
  --radius-none: 0;
  --radius-sm: 0.25rem;   /* 4px */
  --radius-md: 0.375rem;  /* 6px */
  --radius-lg: 0.5rem;    /* 8px */
  --radius-xl: 0.75rem;   /* 12px */
  --radius-2xl: 1rem;     /* 16px */
  --radius-full: 9999px;

  /* ========================================
     SHADOWS
     ======================================== */
  
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* ========================================
     TRANSITIONS
     ======================================== */
  
  --transition-fast: 150ms ease;
  --transition-normal: 250ms ease;
  --transition-slow: 350ms ease;
  
  --transition-all: all var(--transition-normal);
  --transition-colors: color var(--transition-fast), background-color var(--transition-fast), border-color var(--transition-fast);
  --transition-transform: transform var(--transition-normal);

  /* ========================================
     BREAKPOINTS (for CSS custom media)
     ======================================== */
  
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /* ========================================
     COMPONENT SPECIFIC
     ======================================== */
  
  /* Markers */
  --marker-size-sm: 30px;
  --marker-size-md: 40px;
  --marker-size-lg: 50px;
  --marker-size-xl: 60px;
  
  /* Panels */
  --panel-width-sm: 300px;
  --panel-width-md: 400px;
  --panel-width-lg: 500px;
  
  /* Header */
  --header-height: 60px;
  --header-height-mobile: 50px;

  /* ========================================
     BUSINESS CONFIGURATION
     ======================================== */
  
  /* Feature Flags (can be overridden per client) */
  --enable-animations: 1;
  --enable-shadows: 1;
  --enable-gradients: 1;
  
  /* Performance Settings */
  --gpu-acceleration: translateZ(0);
  --will-change-transform: transform;
  --will-change-auto: auto;
}
