/**
 * MODERN CSS RESET
 * Business-optimized reset for consistent cross-browser rendering
 */

/* ========================================
   BOX SIZING & FUNDAMENTALS
   ======================================== */

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* ========================================
   ROOT & DOCUMENT
   ======================================== */

html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  margin: 0;
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--gray-900);
  background-color: var(--white);
  overflow-x: hidden;
}

/* ========================================
   TYPOGRAPHY RESET
   ======================================== */

h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
}

p, blockquote, dl, dd, ol, ul, figure {
  margin: 0;
}

/* ========================================
   INTERACTIVE ELEMENTS
   ======================================== */

button, input, optgroup, select, textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.15;
  margin: 0;
}

button, [type="button"], [type="reset"], [type="submit"] {
  -webkit-appearance: button;
  cursor: pointer;
}

button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring {
  outline: 1px dotted ButtonText;
}

/* ========================================
   MEDIA ELEMENTS
   ======================================== */

img, picture, video, canvas, svg {
  display: block;
  max-width: 100%;
  height: auto;
}

img {
  border-style: none;
}

/* ========================================
   TABLES
   ======================================== */

table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* ========================================
   LISTS
   ======================================== */

ol, ul {
  list-style: none;
}

/* ========================================
   LINKS
   ======================================== */

a {
  background-color: transparent;
  color: inherit;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* ========================================
   ACCESSIBILITY
   ======================================== */

/* Remove all animations and transitions for users who prefer reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Improve focus visibility */
:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Hide content from screen readers when marked as decorative */
[aria-hidden="true"] {
  display: none !important;
}

/* ========================================
   BUSINESS SPECIFIC RESETS
   ======================================== */

/* Ensure consistent rendering across map libraries */
.mapboxgl-map {
  font-family: var(--font-family-primary);
}

/* Reset for third-party components */
.mapboxgl-ctrl {
  font-family: var(--font-family-primary);
}

/* Ensure proper stacking context for business components */
.business-component {
  position: relative;
  z-index: var(--z-base);
}
