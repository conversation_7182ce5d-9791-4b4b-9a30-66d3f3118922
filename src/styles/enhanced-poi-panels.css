/* Enhanced POI Panel Styles - Moroccan Theme */

/* ===== Common Panel Styles ===== */
.poi-panel-base {
  --morocco-terracotta: #E27D60;
  --morocco-blue: #3B82F6;
  --morocco-teal: #2A9D8F;
  --morocco-sand: #F8F0E3;
  --morocco-gold: #F2C94C;
  --morocco-dark: #2D3748;
  --morocco-light: #F9FAFB;
  --panel-radius: 12px;
  --panel-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
  backdrop-filter: blur(10px);
  border-radius: var(--panel-radius);
  box-shadow: var(--panel-shadow);
  border: 1px solid rgba(229, 231, 235, 0.8);
  transition: all 0.3s ease;
  overflow: hidden;
}

/* ===== Left POI Selection Panel ===== */
.poi-selection-panel {
  width: 380px;
  height: calc(100vh - 80px);
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 80px;
  z-index: 250;
  transform: translateX(0);
  transition: transform 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

.poi-selection-panel.hidden {
  transform: translateX(-100%);
}

.poi-panel-header {
  padding: 20px;
  border-bottom: 1px solid rgba(229, 231, 235, 0.8);
  position: relative;
  background: linear-gradient(135deg, var(--morocco-light), white);
}

.poi-panel-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60%;
  height: 2px;
  background: linear-gradient(to right, transparent, var(--morocco-terracotta), transparent);
}

.poi-panel-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--morocco-dark);
  display: flex;
  align-items: center;
  gap: 8px;
}

.poi-panel-title-icon {
  color: var(--morocco-terracotta);
}

.poi-panel-subtitle {
  font-size: 0.9rem;
  color: #6B7280;
  margin-top: 4px;
}

.poi-filter-section {
  padding: 15px 20px;
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  background-color: rgba(248, 240, 227, 0.3);
}

.poi-filter-chip {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.poi-filter-chip:hover {
  background-color: rgba(226, 125, 96, 0.1);
  border-color: var(--morocco-terracotta);
  transform: translateY(-1px);
}

.poi-filter-chip.active {
  background-color: var(--morocco-terracotta);
  color: white;
  border-color: var(--morocco-terracotta);
  box-shadow: 0 2px 5px rgba(226, 125, 96, 0.3);
}

.poi-search-bar {
  margin: 15px 20px;
  position: relative;
}

.poi-search-input {
  width: 100%;
  padding: 12px 15px 12px 40px;
  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background-color: white;
  font-size: 0.95rem;
  transition: all 0.2s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.poi-search-input:focus {
  outline: none;
  border-color: var(--morocco-terracotta);
  box-shadow: 0 0 0 3px rgba(226, 125, 96, 0.2);
}

.poi-search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9CA3AF;
}

.poi-list-container {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  background-color: rgba(255, 255, 255, 0.5);
  scrollbar-width: thin;
  scrollbar-color: var(--morocco-terracotta) transparent;
}

.poi-list-container::-webkit-scrollbar {
  width: 6px;
}

.poi-list-container::-webkit-scrollbar-track {
  background: transparent;
}

.poi-list-container::-webkit-scrollbar-thumb {
  background-color: rgba(226, 125, 96, 0.5);
  border-radius: 3px;
}

.poi-card {
  margin-bottom: 18px;
  border-radius: 12px;
  background-color: white;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
}

.poi-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

.poi-card.selected {
  border-left: 4px solid var(--morocco-terracotta);
  background-color: rgba(248, 240, 227, 0.3);
}

.poi-card-content {
  padding: 15px;
  display: flex;
  gap: 15px;
}

.poi-card-image {
  width: 90px;
  height: 90px;
  border-radius: 10px;
  object-fit: cover;
  flex-shrink: 0;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.poi-card-details {
  flex: 1;
}

.poi-card-title {
  font-weight: 600;
  font-size: 1.05rem;
  margin-bottom: 5px;
  color: var(--morocco-dark);
  line-height: 1.3;
}

.poi-card-location {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.8rem;
  color: #6B7280;
  margin-bottom: 6px;
}

.poi-card-description {
  font-size: 0.85rem;
  color: #4B5563;
  margin-bottom: 8px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.poi-card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-top: 8px;
}

.poi-tag {
  padding: 3px 8px;
  border-radius: 6px;
  font-size: 0.7rem;
  background-color: rgba(0, 0, 0, 0.05);
  color: #4B5563;
  transition: all 0.2s ease;
}

.poi-tag:hover {
  background-color: rgba(226, 125, 96, 0.1);
  color: var(--morocco-terracotta);
}

.poi-card-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 10px;
}

.poi-action-button {
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.poi-action-primary {
  background-color: var(--morocco-terracotta);
  color: white;
  box-shadow: 0 2px 5px rgba(226, 125, 96, 0.3);
}

.poi-action-primary:hover {
  background-color: #d06a50;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(226, 125, 96, 0.4);
}

.poi-action-secondary {
  background-color: white;
  border: 1px solid var(--morocco-terracotta);
  color: var(--morocco-terracotta);
}

.poi-action-secondary:hover {
  background-color: rgba(226, 125, 96, 0.1);
}

.poi-panel-footer {
  padding: 15px 20px;
  border-top: 1px solid rgba(229, 231, 235, 0.8);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(248, 240, 227, 0.2);
}

.poi-selection-count {
  font-weight: 600;
  color: var(--morocco-terracotta);
  display: flex;
  align-items: center;
  gap: 5px;
}

.poi-footer-actions {
  display: flex;
  gap: 10px;
}

/* ===== Right POI Journey Panel ===== */
.poi-journey-panel {
  width: 350px;
  height: calc(100vh - 80px);
  display: flex;
  flex-direction: column;
  position: fixed;
  right: 0;
  top: 80px;
  z-index: 250;
  transform: translateX(0);
  transition: transform 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

.poi-journey-panel.hidden {
  transform: translateX(100%);
}

.journey-header {
  padding: 20px;
  border-bottom: 1px solid rgba(229, 231, 235, 0.8);
  background: linear-gradient(135deg, var(--morocco-light), white);
  position: relative;
}

.journey-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60%;
  height: 2px;
  background: linear-gradient(to right, transparent, var(--morocco-blue), transparent);
}

.journey-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--morocco-dark);
}

.journey-title svg {
  color: var(--morocco-blue);
}

.journey-subtitle {
  font-size: 0.9rem;
  color: #6B7280;
  margin-bottom: 10px;
}

.journey-progress {
  margin-top: 15px;
}

.journey-progress-bar {
  height: 6px;
  background-color: #E5E7EB;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.journey-progress-fill {
  height: 100%;
  background: linear-gradient(to right, var(--morocco-blue), var(--morocco-teal));
  border-radius: 3px;
  transition: width 0.3s ease;
}

.journey-stats {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  font-size: 0.8rem;
}

.journey-poi-list {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  background-color: rgba(255, 255, 255, 0.5);
  scrollbar-width: thin;
  scrollbar-color: var(--morocco-terracotta) transparent;
}

.journey-poi-list::-webkit-scrollbar {
  width: 6px;
}

.journey-poi-list::-webkit-scrollbar-track {
  background: transparent;
}

.journey-poi-list::-webkit-scrollbar-thumb {
  background-color: rgba(226, 125, 96, 0.5);
  border-radius: 3px;
}

.journey-day-section {
  margin-bottom: 25px;
}

.journey-day-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.journey-day-badge {
  background-color: var(--morocco-blue);
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  box-shadow: 0 3px 6px rgba(59, 130, 246, 0.3);
}

.journey-day-title {
  font-weight: 600;
  color: var(--morocco-dark);
  font-size: 1.05rem;
}

.journey-timeline {
  position: relative;
  padding-left: 30px;
}

.journey-timeline::before {
  content: '';
  position: absolute;
  top: 0;
  left: 15px;
  width: 2px;
  height: 100%;
  background: linear-gradient(to bottom, rgba(59, 130, 246, 0.7), rgba(59, 130, 246, 0.2));
  transform: translateX(-50%);
}

.journey-poi-item {
  position: relative;
  padding: 12px 15px;
  background-color: white;
  border-radius: 10px;
  margin-bottom: 15px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.journey-poi-item::before {
  content: '';
  position: absolute;
  top: 50%;
  left: -30px;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: var(--morocco-blue);
  transform: translateY(-50%);
  border: 3px solid white;
  z-index: 1;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.journey-poi-item:hover {
  transform: translateX(5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.journey-poi-title {
  font-weight: 600;
  font-size: 0.95rem;
  margin-bottom: 5px;
  color: var(--morocco-dark);
}

.journey-poi-time {
  font-size: 0.8rem;
  color: #6B7280;
  display: flex;
  align-items: center;
  gap: 4px;
}

.journey-poi-duration {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 6px;
  font-size: 0.7rem;
  background-color: rgba(42, 157, 143, 0.1);
  color: var(--morocco-teal);
  margin-left: auto;
  font-weight: 500;
}

.journey-poi-description {
  font-size: 0.8rem;
  color: #4B5563;
  margin-top: 6px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.journey-summary {
  padding: 18px 20px;
  border-top: 1px solid rgba(229, 231, 235, 0.8);
  background-color: var(--morocco-sand);
}

.journey-summary-title {
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 12px;
  color: var(--morocco-dark);
}

.journey-summary-stats {
  display: flex;
  justify-content: space-between;
}

.journey-stat {
  text-align: center;
  padding: 10px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  flex: 1;
  margin: 0 5px;
}

.journey-stat-value {
  font-weight: 700;
  font-size: 1.2rem;
  color: var(--morocco-terracotta);
}

.journey-stat-label {
  font-size: 0.8rem;
  color: #6B7280;
  margin-top: 3px;
}

/* Journey Metrics (Weather, Budget, Activities) */
.journey-metrics {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px dashed #E5E7EB;
}

.journey-metric {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.journey-metric-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: linear-gradient(135deg, var(--morocco-sand), white);
  border-radius: 50%;
  margin-right: 8px;
  color: var(--morocco-blue);
}

.journey-metric-content {
  display: flex;
  flex-direction: column;
}

.journey-metric-value {
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--morocco-dark);
}

.journey-metric-label {
  font-size: 0.7rem;
  color: #6B7280;
}

/* Collapsible Sections */
.journey-collapsible-section {
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
}

.journey-section-header {
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.journey-section-header:hover {
  background-color: rgba(248, 240, 227, 0.3);
}

.journey-section-title {
  font-weight: 600;
  font-size: 0.95rem;
  color: var(--morocco-dark);
}

.journey-section-content {
  padding: 0 20px 15px 20px;
  background-color: rgba(248, 240, 227, 0.1);
}

/* Weather Forecast */
.journey-weather-forecast {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
}

.journey-forecast-day {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  flex: 1;
  margin: 0 5px;
}

.journey-forecast-date {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--morocco-dark);
  margin-bottom: 5px;
}

.journey-forecast-icon {
  color: var(--morocco-gold);
  margin-bottom: 5px;
}

.journey-forecast-temp {
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--morocco-dark);
}

.journey-forecast-desc {
  font-size: 0.7rem;
  color: #6B7280;
}

/* Budget Breakdown */
.journey-budget-breakdown {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.journey-budget-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: white;
  border-radius: 6px;
  font-size: 0.85rem;
}

.journey-budget-item span:first-child {
  color: #6B7280;
}

.journey-budget-item span:last-child {
  font-weight: 500;
  color: var(--morocco-dark);
}

.journey-budget-total {
  display: flex;
  justify-content: space-between;
  padding: 10px 12px;
  margin-top: 5px;
  background-color: var(--morocco-blue);
  color: white;
  border-radius: 6px;
  font-weight: 600;
}

/* Journey Timeline Enhancements */
.journey-timeline-connector {
  position: absolute;
  top: 0;
  left: -30px;
  width: 2px;
  height: 100%;
  background: linear-gradient(to bottom, var(--morocco-blue), rgba(59, 130, 246, 0.2));
}

.journey-poi-content {
  position: relative;
}

.journey-poi-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.journey-poi-location {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.8rem;
  color: #6B7280;
  margin-bottom: 6px;
}

.journey-poi-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  font-size: 0.8rem;
}

.journey-poi-weather,
.journey-poi-cost {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 3px 8px;
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.05);
}

.journey-poi-weather .weather-icon {
  color: var(--morocco-gold);
}

.journey-poi-weather .weather-icon.cloudy {
  color: #9CA3AF;
}

.journey-poi-weather .weather-icon.rainy {
  color: var(--morocco-blue);
}

.journey-poi-weather .weather-icon.windy {
  color: #6B7280;
}

.journey-poi-remove {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.05);
  color: #6B7280;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.journey-poi-remove:hover {
  background-color: var(--morocco-terracotta);
  color: white;
}

.journey-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #6B7280;
  text-align: center;
}

.journey-empty-state svg {
  color: #9CA3AF;
  margin-bottom: 15px;
  opacity: 0.7;
}

.journey-empty-subtitle {
  font-size: 0.85rem;
  margin-top: 5px;
  opacity: 0.8;
}

/* ===== Enhanced Marker Tooltip ===== */
.enhanced-marker-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-5px);
  background-color: white;
  color: var(--morocco-dark);
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 0.85rem;
  font-weight: 500;
  white-space: nowrap;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  pointer-events: none;
  opacity: 0;
  transition: all 0.2s ease;
  margin-bottom: 10px;
  z-index: 10;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.enhanced-marker-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -8px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid white;
}

.poi-marker:hover .enhanced-marker-tooltip {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

/* ===== Animations ===== */
@keyframes fadeSlideIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes pulseHighlight {
  0%, 100% { box-shadow: 0 0 0 0 rgba(226, 125, 96, 0); }
  50% { box-shadow: 0 0 0 8px rgba(226, 125, 96, 0.3); }
}

.poi-card-new {
  animation: fadeSlideIn 0.5s ease-out, pulseHighlight 2s infinite;
}

/* ===== Drag and Drop Styles ===== */
.poi-draggable {
  cursor: grab;
}

.poi-draggable:active {
  cursor: grabbing;
}

.poi-drag-over {
  border: 2px dashed var(--morocco-terracotta);
  background-color: rgba(226, 125, 96, 0.05);
}

/* ===== Toggle Panel Controls ===== */
.panel-toggle-control {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 50px;
  background-color: white;
  border: 1px solid rgba(229, 231, 235, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 200;
  transition: all 0.2s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.panel-toggle-control:hover {
  background-color: var(--morocco-sand);
}

.panel-toggle-left {
  right: -24px;
  border-radius: 0 8px 8px 0;
}

.panel-toggle-right {
  left: -24px;
  border-radius: 8px 0 0 8px;
}

/* ===== Responsive Adjustments ===== */
@media (max-width: 768px) {
  .poi-selection-panel,
  .poi-journey-panel {
    width: 100%;
    border-radius: 0;
  }
  
  .poi-card-content {
    flex-direction: column;
  }
  
  .poi-card-image {
    width: 100%;
    height: 120px;
  }
  
  .journey-summary-stats {
    flex-wrap: wrap;
  }
  
  .journey-stat {
    flex-basis: calc(50% - 10px);
    margin-bottom: 10px;
  }
}

@media (max-width: 480px) {
  .poi-filter-section {
    overflow-x: auto;
    padding-bottom: 10px;
  }
  
  .poi-filter-chip {
    flex-shrink: 0;
  }
  
  .journey-day-badge {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }
  
  .journey-poi-item::before {
    width: 12px;
    height: 12px;
  }
}