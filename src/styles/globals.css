@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
    transform: scale(1.03);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    transform: scale(1);
  }
}

/* Add button-pulse animation for the Continue Journey button */
@keyframes button-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
    transform: scale(1.03);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    transform: scale(1);
  }
}

/* Vehicle marker styles moved to markers-unified.css to avoid conflicts */
/* DO NOT add vehicle-marker styles here - they conflict with Mapbox positioning */

.vehicle-pulse, .vehicle-pulse-outer {
  pointer-events: none;
  will-change: transform, opacity;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  transform-style: preserve-3d;
  -webkit-transform-style: preserve-3d;
}

/* Z-index variables now handled by core/variables.css */

/* Apply z-index to components */
.mapboxgl-popup {
  z-index: var(--z-index-popups) !important;
}

.notification-panel {
  z-index: var(--z-index-notifications) !important;
}

/* Make popups more user-friendly */
.mapboxgl-popup-close-button {
  font-size: 16px !important;
  padding: 5px !important;
  right: 5px !important;
  top: 5px !important;
  width: 24px !important;
  height: 24px !important;
  background: rgba(255, 255, 255, 0.8) !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: 1 !important;
}

/* Fix for POI popup handling */
.map-announcement-container {
  z-index: var(--z-index-notifications) !important;
}

/* Make the journey controls always visible */
.journey-controls {
  z-index: var(--z-index-notifications) !important;
}

/* Import component-specific stylesheets */
@import './components/notification-panel.css'; 