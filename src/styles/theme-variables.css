/**
 * Universal Theme Variables System
 *
 * Dynamic CSS variables that change based on the active theme
 */

/* Import Google Fonts for theme-specific typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Crimson+Text:ital,wght@0,400;0,600;1,400;1,600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Bebas+Neue&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Russo+One&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Oswald:wght@300;400;500;600;700&display=swap');

/* Base/Default Theme Variables (Neutral - Professional Corporate) */
:root {
  /* Primary theme colors - Clean corporate blue/gray palette */
  --theme-primary-color: #1e40af;
  --theme-secondary-color: #475569;
  --theme-accent-color: #06b6d4;
  --theme-background-color: #f8fafc;
  --theme-text-color: #0f172a;

  /* Header and navigation - Professional blue */
  --theme-header-bg: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
  --theme-header-text: #ffffff;
  --theme-border-color: rgba(30, 64, 175, 0.15);

  /* Typography - Clean, professional fonts */
  --theme-font-display: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --theme-font-heading: "Inter", sans-serif;
  --theme-font-body: "Inter", sans-serif;

  /* Buttons - Professional styling */
  --theme-button-primary-bg: #1e40af;
  --theme-button-primary-text: #ffffff;
  --theme-button-primary-hover: #1d4ed8;
  --theme-button-secondary-bg: #f1f5f9;
  --theme-button-secondary-text: #1e40af;
  --theme-button-secondary-hover: #e2e8f0;

  /* Stats and indicators - Professional color scheme */
  --theme-stat-duration-color: #1e40af;
  --theme-stat-cities-color: #475569;
  --theme-stat-pois-color: #06b6d4;
  --theme-stat-pace-color: #64748b;

  /* Progress and journey */
  --theme-progress-bg: #e2e8f0;
  --theme-progress-fill: linear-gradient(90deg, #1e40af 0%, #06b6d4 100%);
  --theme-journey-button-bg: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
}

/* Morocco Theme Variables */
.theme-morocco,
[data-theme="morocco"] {
  /* Primary theme colors */
  --theme-primary-color: #8B1A18;
  --theme-secondary-color: #D1C4A9;
  --theme-accent-color: #41B3A3;
  --theme-background-color: #ffffff;
  --theme-text-color: #3c2415;
  
  /* Header and navigation */
  --theme-header-bg: rgba(139, 26, 24, 0.9);
  --theme-header-text: #ffffff;
  --theme-border-color: rgba(177, 155, 123, 0.3);
  
  /* Typography */
  --theme-font-display: "Amiri", serif;
  --theme-font-heading: "Playfair Display", serif;
  --theme-font-body: "Source Sans Pro", sans-serif;
  
  /* Buttons */
  --theme-button-primary-bg: #8B1A18;
  --theme-button-primary-text: #ffffff;
  --theme-button-primary-hover: #a91e1c;
  --theme-button-secondary-bg: #f9f6ee;
  --theme-button-secondary-text: #8B1A18;
  --theme-button-secondary-hover: #f0ead6;
  
  /* Stats and indicators */
  --theme-stat-duration-color: #41B3A3;
  --theme-stat-cities-color: #8B1A18;
  --theme-stat-pois-color: #D1C4A9;
  --theme-stat-pace-color: #41B3A3;
  
  /* Progress and journey */
  --theme-progress-bg: #f0ead6;
  --theme-progress-fill: #8B1A18;
  --theme-journey-button-bg: #8B1A18;
}

/* Napa Valley Wine Theme Variables - Elegant Wine Country */
.theme-napa-valley,
[data-theme="napa-valley"] {
  /* Primary theme colors - Rich wine country palette */
  --theme-primary-color: #722f37;
  --theme-secondary-color: #b8860b;
  --theme-accent-color: #cd853f;
  --theme-background-color: #faf8f3;
  --theme-text-color: #2d1b1e;

  /* Header and navigation - Deep wine gradient */
  --theme-header-bg: linear-gradient(135deg, #722f37 0%, #8b4513 50%, #b8860b 100%);
  --theme-header-text: #faf8f3;
  --theme-border-color: rgba(184, 134, 11, 0.25);

  /* Typography - Elegant serif fonts */
  --theme-font-display: "Playfair Display", "Times New Roman", serif;
  --theme-font-heading: "Playfair Display", serif;
  --theme-font-body: "Crimson Text", "Georgia", serif;

  /* Buttons - Wine-inspired styling */
  --theme-button-primary-bg: linear-gradient(135deg, #722f37 0%, #8b4513 100%);
  --theme-button-primary-text: #faf8f3;
  --theme-button-primary-hover: #5d252a;
  --theme-button-secondary-bg: #f5f2e8;
  --theme-button-secondary-text: #722f37;
  --theme-button-secondary-hover: #ede7d3;

  /* Stats and indicators - Wine country colors */
  --theme-stat-duration-color: #b8860b;
  --theme-stat-cities-color: #722f37;
  --theme-stat-pois-color: #cd853f;
  --theme-stat-pace-color: #8b4513;

  /* Progress and journey - Elegant wine gradients */
  --theme-progress-bg: #f5f2e8;
  --theme-progress-fill: linear-gradient(90deg, #722f37 0%, #b8860b 50%, #cd853f 100%);
  --theme-journey-button-bg: linear-gradient(135deg, #722f37 0%, #8b4513 50%, #b8860b 100%);
}

/* Route 66 Adventure Theme Variables - Bold Americana Vintage */
.theme-route66,
[data-theme="route66"] {
  /* Primary theme colors - Classic Americana red, white, blue */
  --theme-primary-color: #c41e3a;
  --theme-secondary-color: #002868;
  --theme-accent-color: #ffcc00;
  --theme-background-color: #f7f3e9;
  --theme-text-color: #1a1a1a;

  /* Header and navigation - Bold patriotic stripes */
  --theme-header-bg: linear-gradient(90deg, #c41e3a 0%, #ffffff 25%, #002868 50%, #ffffff 75%, #c41e3a 100%);
  --theme-header-text: #1a1a1a;
  --theme-border-color: #ffcc00;

  /* Typography - Bold vintage American fonts */
  --theme-font-display: "Bebas Neue", "Impact", "Arial Black", sans-serif;
  --theme-font-heading: "Russo One", "Impact", sans-serif;
  --theme-font-body: "Oswald", "Arial Narrow", sans-serif;

  /* Buttons - Vintage Americana styling */
  --theme-button-primary-bg: linear-gradient(135deg, #c41e3a 0%, #8b0000 100%);
  --theme-button-primary-text: #ffffff;
  --theme-button-primary-hover: #a01729;
  --theme-button-secondary-bg: #ffcc00;
  --theme-button-secondary-text: #002868;
  --theme-button-secondary-hover: #e6b800;

  /* Stats and indicators - Patriotic color scheme */
  --theme-stat-duration-color: #002868;
  --theme-stat-cities-color: #c41e3a;
  --theme-stat-pois-color: #ffcc00;
  --theme-stat-pace-color: #4a4a4a;

  /* Progress and journey - Vintage road trip styling */
  --theme-progress-bg: #e6e6e6;
  --theme-progress-fill: linear-gradient(90deg, #c41e3a 0%, #ffcc00 50%, #002868 100%);
  --theme-journey-button-bg: linear-gradient(135deg, #c41e3a 0%, #002868 100%);
}

/* Theme-specific patterns and backgrounds */
.theme-pattern-backdrop.theme-neutral {
  background-image:
    linear-gradient(135deg, rgba(30, 64, 175, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.05) 0%, transparent 50%);
}

.theme-pattern-backdrop.theme-napa-valley {
  background-image:
    radial-gradient(ellipse at 30% 70%, rgba(114, 47, 55, 0.08) 0%, transparent 60%),
    linear-gradient(45deg, rgba(184, 134, 11, 0.04) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(139, 69, 19, 0.04) 25%, transparent 25%);
  background-size: 100% 100%, 30px 30px, 30px 30px;
}

.theme-pattern-backdrop.theme-route66 {
  background-image:
    repeating-linear-gradient(
      90deg,
      rgba(196, 30, 58, 0.05) 0px,
      rgba(196, 30, 58, 0.05) 2px,
      transparent 2px,
      transparent 20px,
      rgba(0, 40, 104, 0.05) 20px,
      rgba(0, 40, 104, 0.05) 22px,
      transparent 22px,
      transparent 40px
    );
}

/* Theme-specific title styling */
.theme-title.theme-neutral {
  text-shadow: 1px 1px 2px rgba(30, 64, 175, 0.1);
  font-weight: 600;
  letter-spacing: -0.5px;
}

.theme-title.theme-napa-valley {
  text-shadow: 2px 2px 4px rgba(114, 47, 55, 0.3);
  font-style: italic;
  font-weight: 400;
  letter-spacing: 0.5px;
}

.theme-title.theme-route66 {
  text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.6);
  text-transform: uppercase;
  letter-spacing: 3px;
  font-weight: 900;
  -webkit-text-stroke: 1px rgba(255, 204, 0, 0.3);
}

/* Responsive theme adjustments */
@media (max-width: 768px) {
  .theme-title.theme-route66 {
    letter-spacing: 1.5px;
    -webkit-text-stroke: 0.5px rgba(255, 204, 0, 0.3);
  }

  .theme-title.theme-napa-valley {
    font-style: normal;
    letter-spacing: 0px;
  }

  .theme-title.theme-neutral {
    letter-spacing: -0.25px;
  }
}
