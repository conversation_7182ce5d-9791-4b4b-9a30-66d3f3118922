/* Map Container Styles */
.explore-map-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: visible;
}

/* Map Element */
#explore-map-container {
  width: 100%;
  height: 100%;
}

/* Map Controls */
.map-controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 10;
}

.map-control-button {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  color: #333;
  transition: all 0.2s ease;
}

.map-control-button:hover {
  background-color: #f8f8f8;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Destination Markers */
.destination-marker {
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.destination-marker .marker-icon {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.destination-marker .marker-label {
  background-color: rgba(255, 255, 255, 0.9);
  color: #333;
  padding: 3px 8px;
  border-radius: 10px;
  margin-top: 5px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}

/* POI Clusters */
.cluster-marker {
  background-color: #FF9800;
  color: white;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  cursor: pointer;
}

/* Vehicle Marker styles moved to markers-unified.css to avoid conflicts */
/* DO NOT add vehicle-marker styles here */

/* Animation Controls */
.animation-controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  background-color: white;
  padding: 8px 12px;
  border-radius: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
}

.animation-control-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  color: #333;
  transition: all 0.2s ease;
}

.animation-control-button:hover {
  background-color: #e0e0e0;
}

.animation-control-button.active {
  background-color: #3B82F6;
  color: white;
}

/* Map Mode Toggles */
.map-mode-toggles {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 10;
}

.map-mode-toggle {
  background-color: white;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  user-select: none;
}

.map-mode-toggle.active {
  background-color: #3B82F6;
  color: white;
}

/* City Panel Toggle buttons visibility */
.modern-panel-toggle-show-right,
.modern-panel-toggle-show-left {
  position: absolute;
  z-index: 10;
  background-color: white;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  border: none;
  cursor: pointer;
}

.modern-panel-toggle-show-left {
  top: 50%;
  left: 10px;
  transform: translateY(-50%);
}

.modern-panel-toggle-show-right {
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
} 