#journey-sidebar-component {
  background: white !important;
  border-right: 1px solid #e5e7eb !important;
  width: 400px !important;
  height: 100vh !important;
  overflow-y: auto !important;
  padding: 24px !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 24px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
  position: relative !important;
  z-index: 1000 !important; /* Ensure sidebar is above map elements */
}

#journey-sidebar-component .journey-header {
  padding-bottom: 16px !important;
  border-bottom: 1px solid #e5e7eb !important;
}

#journey-sidebar-component .journey-header h2 {
  font-size: 24px !important;
  font-weight: 600 !important;
  color: #111827 !important;
  margin-bottom: 16px !important;
}

.journey-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 16px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #41B3A3;
}

.stat-label {
  font-size: 12px;
  text-transform: uppercase;
  color: #6c757d;
  margin-top: 4px;
}

.destinations-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.destination-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.destination-card:hover {
  border-color: #2A9D8F;
  box-shadow: 0 4px 12px rgba(42, 157, 143, 0.1);
}

.destination-header {
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.destination-order {
  width: 32px;
  height: 32px;
  background: #2A9D8F;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.destination-info {
  flex: 1;
}

.destination-info h3 {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.destination-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  font-size: 14px;
}

.days-control {
  display: flex;
  align-items: center;
  margin-left: 36px;
  font-size: 14px;
  color: #6c757d;
}

.days-control button {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  cursor: pointer;
}

.days-control button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.days-text {
  margin: 0 8px;
}

.destination-actions {
  display: flex;
  gap: 4px;
}

.action-button {
  width: 28px;
  height: 28px;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
  background: white;
  color: #4b5563;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-button.remove:hover {
  background: #fee2e2;
  border-color: #fecaca;
  color: #dc2626;
}

.destination-pois {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.poi-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border-radius: 8px;
  background: #f9fafb;
  transition: all 0.2s ease;
}

.poi-item:hover {
  background: #f3f4f6;
}

.poi-type-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #2A9D8F;
}

.poi-type-indicator[data-type="historical"] { background: #E27D60; }
.poi-type-indicator[data-type="cultural"] { background: #41B3A3; }
.poi-type-indicator[data-type="natural"] { background: #85CDCA; }
.poi-type-indicator[data-type="adventure"] { background: #E8A87C; }
.poi-type-indicator[data-type="food"] { background: #C38D9E; }
.poi-type-indicator[data-type="shopping"] { background: #41B3A3; }
.poi-type-indicator[data-type="restaurant"] { background: #F59E0B; }
.poi-type-indicator[data-type="landmark"] { background: #6366F1; }
.poi-type-indicator[data-type="activity"] { background: #EC4899; }
.poi-type-indicator[data-type="hotel"] { background: #8B5CF6; }

.poi-info {
  flex: 1;
}

.poi-info h4 {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
  margin-bottom: 2px;
}

.poi-type {
  font-size: 12px;
  color: #6b7280;
}

.add-activities-button {
  margin-left: 36px;
  margin-top: 8px;
  color: #41B3A3;
  font-size: 14px;
  display: flex;
  align-items: center;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
}

.add-activities-button:hover {
  color: #2d7d71;
}

.add-activities-button svg {
  margin-right: 4px;
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 48px 24px;
  text-align: center;
  color: #6b7280;
}

.empty-state .icon {
  color: #2A9D8F;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.empty-state p {
  font-size: 14px;
  max-width: 240px;
  line-height: 1.5;
}

/* New bottom section styles */
.journey-footer {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 16px;
  border-top: 1px solid #e5e7eb;
  margin: 0 -24px -24px -24px;
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.05);
  z-index: 1001; /* Above the sidebar */
}

.journey-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.summary-stats {
  display: flex;
  gap: 24px;
}

.summary-stat {
  text-align: center;
}

.summary-stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #2A9D8F;
  line-height: 1;
  margin-bottom: 4px;
}

.summary-stat-label {
  font-size: 12px;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.journey-actions {
  display: flex;
  gap: 12px;
}

.action-primary {
  flex: 1;
  padding: 12px 24px;
  background: #2A9D8F;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.action-primary:hover {
  background: #238579;
}

.action-secondary {
  padding: 12px;
  background: white;
  color: #4b5563;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-secondary:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.journey-progress {
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  margin-bottom: 12px;
  overflow: hidden;
}

.journey-progress-bar {
  height: 100%;
  background: #2A9D8F;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.destination-recommendations {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.recommendation-title {
  font-size: 14px;
  font-weight: 600;
  color: #4b5563;
  margin-bottom: 12px;
}

.recommendation-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.recommendation-chip {
  padding: 6px 12px;
  background: #f3f4f6;
  border-radius: 16px;
  font-size: 13px;
  color: #4b5563;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.recommendation-chip:hover {
  background: #2A9D8F;
  color: white;
}

.recommendation-chip .icon {
  width: 14px;
  height: 14px;
}

/* City icon container styling */
.city-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Destination item styling */
.destination-item {
  border-bottom: 1px solid #e9ecef;
  padding: 12px 0;
}

.destination-item:last-child {
  border-bottom: none;
}

.destination-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.order-badge {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #41B3A3;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 12px;
  font-size: 12px;
}

.destination-name {
  font-weight: 500;
  flex-grow: 1;
}

.remove-button {
  color: #adb5bd;
  cursor: pointer;
}

.remove-button:hover {
  color: #dc3545;
}

/* Reordering controls */
.reorder-controls {
  display: flex;
  margin-left: 36px;
  margin-top: 8px;
}

.reorder-controls button {
  color: #adb5bd;
  padding: 4px;
  cursor: pointer;
}

.reorder-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.reorder-controls button:hover:not(:disabled) {
  color: #6c757d;
}

/* Continue button */
.continue-button {
  width: 100%;
  padding: 10px;
  border-radius: 4px;
  background-color: #41B3A3;
  color: white;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: none;
}

.continue-button:hover {
  background-color: #2d7d71;
}

.continue-button:disabled {
  background-color: #dee2e6;
  cursor: not-allowed;
}

.continue-button svg {
  margin-left: 8px;
}

/* Recommended stops */
.recommended-stops {
  margin-top: 16px;
}

.recommended-stops h4 {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #495057;
}

.stop-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.stop-button {
  background-color: #f8f9fa;
  border: none;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.stop-button:hover {
  background-color: #e9ecef;
}

.stop-button svg {
  margin-right: 4px;
}

.poi-list {
  margin-left: 36px;
  margin-top: 8px;
}

.poi-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  padding: 4px 0;
}

.poi-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #41B3A3;
  margin-right: 8px;
}

.poi-name {
  flex-grow: 1;
}

.empty-poi-message {
  font-size: 14px;
  color: #adb5bd;
  margin-left: 36px;
  margin-top: 8px;
} 