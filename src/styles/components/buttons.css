/**
 * BUTTON COMPONENT SYSTEM
 * Consolidated styling for all button components
 */

/* ========================================
   BASE BUTTON STYLES
   ======================================== */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: var(--transition-colors);
  user-select: none;
  white-space: nowrap;
  position: relative;
  z-index: var(--z-base);
}

.btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* ========================================
   BUTTON VARIANTS
   ======================================== */

/* Primary Button */
.btn-primary {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--white);
}

.btn-primary:hover {
  background: var(--primary-dark);
  border-color: var(--primary-dark);
}

.btn-primary:active {
  background: var(--primary-dark);
  transform: translateY(1px);
}

/* Secondary Button */
.btn-secondary {
  background: var(--secondary-color);
  border-color: var(--secondary-color);
  color: var(--white);
}

.btn-secondary:hover {
  background: var(--secondary-dark);
  border-color: var(--secondary-dark);
}

/* Outline Button */
.btn-outline {
  background: transparent;
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn-outline:hover {
  background: var(--primary-color);
  color: var(--white);
}

/* Ghost Button */
.btn-ghost {
  background: transparent;
  border-color: transparent;
  color: var(--primary-color);
}

.btn-ghost:hover {
  background: var(--gray-100);
}

/* Accent Button */
.btn-accent {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: var(--gray-900);
}

.btn-accent:hover {
  background: var(--accent-dark);
  border-color: var(--accent-dark);
}

/* Success Button */
.btn-success {
  background: var(--success-color);
  border-color: var(--success-color);
  color: var(--white);
}

.btn-success:hover {
  background: var(--success-color);
  opacity: 0.9;
}

/* Warning Button */
.btn-warning {
  background: var(--warning-color);
  border-color: var(--warning-color);
  color: var(--gray-900);
}

.btn-warning:hover {
  background: var(--warning-color);
  opacity: 0.9;
}

/* Error Button */
.btn-error {
  background: var(--error-color);
  border-color: var(--error-color);
  color: var(--white);
}

.btn-error:hover {
  background: var(--error-color);
  opacity: 0.9;
}

/* ========================================
   BUTTON SIZES
   ======================================== */

.btn-xs {
  padding: var(--space-1) var(--space-2);
  font-size: var(--font-size-xs);
}

.btn-sm {
  padding: var(--space-2) var(--space-3);
  font-size: var(--font-size-sm);
}

.btn-md {
  padding: var(--space-3) var(--space-4);
  font-size: var(--font-size-sm);
}

.btn-lg {
  padding: var(--space-4) var(--space-6);
  font-size: var(--font-size-base);
}

.btn-xl {
  padding: var(--space-5) var(--space-8);
  font-size: var(--font-size-lg);
}

/* ========================================
   BUSINESS SPECIFIC BUTTONS
   ======================================== */

/* Begin Journey Button */
.btn-begin-journey {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  border: none;
  color: var(--white);
  font-weight: var(--font-weight-semibold);
  padding: var(--space-4) var(--space-8);
  font-size: var(--font-size-base);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-controls);
}

.btn-begin-journey:hover {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--secondary-dark) 100%);
  box-shadow: var(--shadow-xl);
  transform: translateY(-2px);
}

.btn-begin-journey:disabled {
  background: var(--gray-400);
  box-shadow: none;
  transform: none;
}

/* Add POI Button */
.btn-add-poi {
  background: var(--success-color);
  border-color: var(--success-color);
  color: var(--white);
  font-size: var(--font-size-xs);
  padding: var(--space-2) var(--space-3);
}

.btn-add-poi:hover {
  background: var(--success-color);
  opacity: 0.9;
  transform: scale(1.05);
}

/* Remove POI Button */
.btn-remove-poi {
  background: var(--error-color);
  border-color: var(--error-color);
  color: var(--white);
  font-size: var(--font-size-xs);
  padding: var(--space-1) var(--space-2);
}

/* Filter Button */
.btn-filter {
  background: var(--gray-100);
  border-color: var(--gray-300);
  color: var(--gray-700);
  font-size: var(--font-size-sm);
}

.btn-filter:hover {
  background: var(--gray-200);
}

.btn-filter.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--white);
}

/* ========================================
   BUTTON GROUPS
   ======================================== */

.btn-group {
  display: inline-flex;
  border-radius: var(--radius-md);
  overflow: hidden;
}

.btn-group .btn {
  border-radius: 0;
  border-right-width: 0;
}

.btn-group .btn:first-child {
  border-top-left-radius: var(--radius-md);
  border-bottom-left-radius: var(--radius-md);
}

.btn-group .btn:last-child {
  border-top-right-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
  border-right-width: 1px;
}

/* ========================================
   FLOATING ACTION BUTTONS
   ======================================== */

.btn-fab {
  position: fixed;
  bottom: var(--space-6);
  right: var(--space-6);
  width: 56px;
  height: 56px;
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-controls);
  padding: 0;
}

.btn-fab:hover {
  box-shadow: var(--shadow-xl);
  transform: scale(1.1);
}

/* ========================================
   LOADING STATES
   ======================================== */

.btn-loading {
  position: relative;
  color: transparent;
}

.btn-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-top-color: transparent;
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* ========================================
   RESPONSIVE DESIGN
   ======================================== */

@media (max-width: 768px) {
  .btn-begin-journey {
    padding: var(--space-3) var(--space-6);
    font-size: var(--font-size-sm);
  }
  
  .btn-fab {
    bottom: var(--space-4);
    right: var(--space-4);
    width: 48px;
    height: 48px;
  }
}

/* ========================================
   ACCESSIBILITY
   ======================================== */

@media (prefers-reduced-motion: reduce) {
  .btn {
    transition: none;
  }
  
  .btn:hover {
    transform: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .btn {
    border-width: 2px;
  }
  
  .btn-ghost {
    border-color: currentColor;
  }
}
