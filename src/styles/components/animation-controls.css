/* Animation Controls - Styling for animation buttons and controls */

.animation-controls {
  position: absolute;
  bottom: 3rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  z-index: 100;
}

/* Simulate Journey Button */
.simulate-journey-button {
  background-color: #10b981;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: bold;
  box-shadow: 0 4px 14px rgba(0,0,0,0.25);
  border: 2px solid white;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-width: 200px;
  cursor: pointer;
  font-size: 16px;
  animation: pulse 2s infinite;
  transition: all 0.3s ease;
}

.simulate-journey-button:hover {
  background-color: #0ea271;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

.simulate-journey-button.loading {
  background-color: #64748b;
  cursor: wait;
  animation: none;
}

.simulate-journey-button.error {
  background-color: #ef4444;
}

/* Begin Adventure Button */
.begin-adventure-button {
  background-color: #3b82f6;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: bold;
  box-shadow: 0 4px 14px rgba(0,0,0,0.25);
  border: 2px solid white;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-width: 200px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
}

.begin-adventure-button:hover {
  background-color: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

/* Pause Button */
.pause-button {
  background-color: #f59e0b;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: bold;
  box-shadow: 0 4px 14px rgba(0,0,0,0.25);
  border: 2px solid white;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-width: 200px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
}

.pause-button:hover {
  background-color: #d97706;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

/* Button Spinner */
.button-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 3px solid rgba(255,255,255,0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
}

/* Error Tooltip */
.button-error-tooltip {
  position: absolute;
  top: calc(100% + 10px);
  left: 50%;
  transform: translateX(-50%);
  background-color: #fee2e2;
  color: #ef4444;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: normal;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  white-space: nowrap;
  pointer-events: none;
}

/* Loading Indicator */
.loading-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 6px solid rgba(255,255,255,0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

.loading-text {
  color: white;
  font-size: 24px;
  font-weight: bold;
}

/* Journey Progress Indicator */
.journey-progress-indicator {
  position: absolute;
  top: 1rem;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  max-width: 600px;
  z-index: 100;
}

/* Animations */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
    transform: translateY(0) scale(1);
  }
  
  70% {
    box-shadow: 0 0 0 15px rgba(16, 185, 129, 0);
    transform: translateY(-2px) scale(1.05);
  }
  
  100% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
    transform: translateY(0) scale(1);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
} 