/* POI List Container */
.poi-list-container {
  position: absolute;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, transform 0.3s ease;
  transform: translateY(10px);
  pointer-events: none;
}

.poi-list-container.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  pointer-events: all;
}

/* POI List */
.poi-list {
  width: 260px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-height: 350px;
  display: flex;
  flex-direction: column;
}

.poi-list-header {
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.poi-list-title {
  font-weight: 600;
  color: #343a40;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.poi-list-content {
  overflow-y: auto;
  padding: 8px 0;
}

/* POI List Items */
.poi-list-item {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s ease;
}

.poi-list-item:hover {
  background-color: #f8f9fa;
}

.poi-list-item.selected {
  background-color: #e9ecef;
}

.poi-item-info {
  flex: 1;
  margin-right: 12px;
}

.poi-item-title {
  font-weight: 500;
  color: #343a40;
  margin-bottom: 4px;
}

.poi-item-type {
  font-size: 12px;
  color: #6c757d;
}

.poi-duration {
  font-size: 12px;
  color: #495057;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Scrollbar Styling */
.poi-list-content::-webkit-scrollbar {
  width: 8px;
}

.poi-list-content::-webkit-scrollbar-track {
  background: #f1f3f5;
  border-radius: 4px;
}

.poi-list-content::-webkit-scrollbar-thumb {
  background: #ced4da;
  border-radius: 4px;
}

.poi-list-content::-webkit-scrollbar-thumb:hover {
  background: #adb5bd;
} 