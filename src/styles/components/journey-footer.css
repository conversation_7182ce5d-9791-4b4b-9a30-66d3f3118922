/* Journey Footer */
.journey-footer-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 16px 24px;
}

.journey-footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Journey Stats */
.journey-stats {
  display: flex;
  gap: 24px;
}

.journey-stat {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #495057;
  font-size: 14px;
}

.journey-stat-icon {
  width: 20px;
  height: 20px;
  color: #6c757d;
}

.journey-stat.recommended {
  color: #41B3A3;
}

.journey-stat.recommended .journey-stat-icon {
  color: #41B3A3;
}

/* Journey Actions */
.journey-actions {
  display: flex;
  gap: 12px;
}

.save-route-btn {
  padding: 8px 16px;
  border-radius: 6px;
  background-color: #f8f9fa;
  color: #495057;
  font-weight: 500;
  transition: all 0.2s ease;
}

.save-route-btn:hover {
  background-color: #e9ecef;
}

.proceed-btn {
  padding: 8px 24px;
  border-radius: 6px;
  background-color: #41B3A3;
  color: white;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.proceed-btn:hover {
  background-color: #3a9f91;
  transform: translateY(-1px);
} 