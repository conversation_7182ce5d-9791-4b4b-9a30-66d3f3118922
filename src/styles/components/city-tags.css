/* City Tags Container */
.city-tags-overlay {
  position: absolute;
  top: 80px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  display: flex;
  flex-direction: column;
  gap: 12px;
  pointer-events: none;
  width: auto;
  max-width: 90%;
}

.city-tags-primary,
.city-tags-secondary {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  pointer-events: auto;
}

/* City Tag */
.city-tag {
  background-color: white;
  border-radius: 20px;
  padding: 6px 12px;
  font-size: 14px;
  font-weight: 500;
  color: #343a40;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.city-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.city-tag.selected {
  background-color: #41B3A3;
  color: white;
}

.city-tag-number {
  background-color: #e9ecef;
  color: #495057;
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 600;
}

.city-tag.selected .city-tag-number {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Secondary Tags */
.city-tags-secondary .city-tag {
  font-size: 12px;
  padding: 4px 10px;
  background-color: rgba(255, 255, 255, 0.9);
} 