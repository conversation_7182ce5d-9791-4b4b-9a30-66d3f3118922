/**
 * PANELS COMPONENT SYSTEM
 * Consolidated styling for all panel components
 */

/* ========================================
   BASE PANEL STYLES
   ======================================== */

.panel {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  z-index: var(--z-controls);
  position: relative;
}

.panel-header {
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.panel-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  margin: 0;
}

.panel-content {
  padding: var(--space-6);
}

.panel-footer {
  padding: var(--space-4) var(--space-6);
  border-top: 1px solid var(--gray-200);
  background: var(--gray-50);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--space-3);
}

/* ========================================
   PANEL VARIANTS
   ======================================== */

/* Left Sidebar Panel */
.panel-left {
  position: fixed;
  top: var(--header-height);
  left: 0;
  width: var(--panel-width-md);
  height: calc(100vh - var(--header-height));
  z-index: var(--z-sidebar);
  border-radius: 0;
  border-left: none;
  border-top: none;
  border-bottom: none;
  overflow-y: auto;
}

/* Right Sidebar Panel */
.panel-right {
  position: fixed;
  top: var(--header-height);
  right: 0;
  width: var(--panel-width-md);
  height: calc(100vh - var(--header-height));
  z-index: var(--z-sidebar);
  border-radius: 0;
  border-right: none;
  border-top: none;
  border-bottom: none;
  overflow-y: auto;
}

/* Modal Panel */
.panel-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  z-index: var(--z-modal);
  box-shadow: var(--shadow-2xl);
}

/* Floating Panel */
.panel-floating {
  position: absolute;
  z-index: var(--z-popups);
  box-shadow: var(--shadow-lg);
  max-width: 400px;
}

/* ========================================
   PANEL STATES
   ======================================== */

.panel-collapsed {
  transform: translateX(-100%);
  transition: transform var(--transition-normal);
}

.panel-expanded {
  transform: translateX(0);
  transition: transform var(--transition-normal);
}

.panel-loading {
  opacity: 0.6;
  pointer-events: none;
}

.panel-error {
  border-color: var(--error-color);
  background: rgba(244, 67, 54, 0.05);
}

/* ========================================
   BUSINESS SPECIFIC PANELS
   ======================================== */

/* Trip Planner Panel */
.trip-planner-panel {
  background: linear-gradient(135deg, var(--white) 0%, var(--gray-50) 100%);
}

.trip-planner-panel .panel-header {
  background: var(--primary-color);
  color: var(--white);
  border-bottom-color: var(--primary-dark);
}

.trip-planner-panel .panel-title {
  color: var(--white);
}

/* POI Discovery Panel */
.poi-panel {
  background: var(--white);
}

.poi-panel .panel-header {
  background: var(--secondary-color);
  color: var(--white);
}

.poi-panel .panel-title {
  color: var(--white);
}

/* Notification Panel */
.notification-panel {
  background: var(--accent-color);
  border-color: var(--accent-dark);
  z-index: var(--z-notification);
}

.notification-panel.success {
  background: var(--success-color);
  border-color: var(--success-color);
  color: var(--white);
}

.notification-panel.warning {
  background: var(--warning-color);
  border-color: var(--warning-color);
  color: var(--gray-900);
}

.notification-panel.error {
  background: var(--error-color);
  border-color: var(--error-color);
  color: var(--white);
}

/* ========================================
   PANEL ANIMATIONS
   ======================================== */

.panel-slide-in {
  animation: slideIn var(--transition-normal) ease-out;
}

.panel-fade-in {
  animation: fadeIn var(--transition-normal) ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* ========================================
   RESPONSIVE DESIGN
   ======================================== */

@media (max-width: 768px) {
  .panel-left,
  .panel-right {
    width: 100%;
    transform: translateX(-100%);
  }
  
  .panel-left.expanded,
  .panel-right.expanded {
    transform: translateX(0);
  }
  
  .panel-modal {
    width: 95%;
    max-height: 90vh;
  }
  
  .panel-content {
    padding: var(--space-4);
  }
  
  .panel-header,
  .panel-footer {
    padding: var(--space-3) var(--space-4);
  }
}

/* ========================================
   ACCESSIBILITY
   ======================================== */

.panel[aria-hidden="true"] {
  display: none;
}

.panel:focus-within {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .panel {
    border-width: 2px;
    border-color: var(--gray-900);
  }
  
  .panel-header {
    background: var(--gray-900);
    color: var(--white);
  }
}
