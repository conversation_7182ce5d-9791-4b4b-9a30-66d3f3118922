/**
 * MAP CONTROLS COMPONENT SYSTEM
 * Consolidated styling for all map control elements
 */

/* ========================================
   BASE MAP CONTROL STYLES
   ======================================== */

.map-control {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  padding: var(--space-2);
  z-index: var(--z-controls);
  position: relative;
}

.map-controls {
  position: absolute;
  bottom: var(--space-6);
  right: var(--space-6);
  z-index: var(--z-controls);
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.map-control-button {
  background-color: var(--white);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  padding: var(--space-2) var(--space-4);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  transition: var(--transition-all);
  color: var(--gray-700);
}

.map-control-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  background: var(--gray-50);
  border-color: var(--gray-400);
}

.map-control-button:active {
  transform: translateY(0);
  box-shadow: var(--shadow-md);
}

.map-control-button.discover-button {
  background-color: var(--success-color);
  border-color: var(--success-color);
  color: var(--white);
}

.map-control-button.discover-button:hover {
  background-color: var(--success-color);
  opacity: 0.9;
}

.map-control-button.discover-button[data-active="true"] {
  background-color: var(--error-color);
  border-color: var(--error-color);
}

.map-control-button.discover-button[data-active="true"]:hover {
  background-color: var(--error-color);
  opacity: 0.9;
}

.map-control-button svg {
  width: 16px;
  height: 16px;
}

/* ========================================
   RESPONSIVE DESIGN
   ======================================== */

@media (max-width: 768px) {
  .map-controls {
    bottom: var(--space-4);
    right: var(--space-4);
    gap: var(--space-1);
  }

  .map-control-button {
    padding: var(--space-2) var(--space-3);
    font-size: var(--font-size-xs);
  }
}