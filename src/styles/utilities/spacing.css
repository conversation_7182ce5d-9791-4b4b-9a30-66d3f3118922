/**
 * SPACING UTILITIES
 * Consistent spacing system for layout and components
 */

/* ========================================
   MARGIN UTILITIES
   ======================================== */

/* All sides */
.m-0 { margin: var(--space-0); }
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-5 { margin: var(--space-5); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }
.m-10 { margin: var(--space-10); }
.m-12 { margin: var(--space-12); }
.m-16 { margin: var(--space-16); }
.m-20 { margin: var(--space-20); }
.m-24 { margin: var(--space-24); }
.m-auto { margin: auto; }

/* Top */
.mt-0 { margin-top: var(--space-0); }
.mt-1 { margin-top: var(--space-1); }
.mt-2 { margin-top: var(--space-2); }
.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }
.mt-5 { margin-top: var(--space-5); }
.mt-6 { margin-top: var(--space-6); }
.mt-8 { margin-top: var(--space-8); }
.mt-10 { margin-top: var(--space-10); }
.mt-12 { margin-top: var(--space-12); }
.mt-16 { margin-top: var(--space-16); }
.mt-20 { margin-top: var(--space-20); }
.mt-24 { margin-top: var(--space-24); }
.mt-auto { margin-top: auto; }

/* Right */
.mr-0 { margin-right: var(--space-0); }
.mr-1 { margin-right: var(--space-1); }
.mr-2 { margin-right: var(--space-2); }
.mr-3 { margin-right: var(--space-3); }
.mr-4 { margin-right: var(--space-4); }
.mr-5 { margin-right: var(--space-5); }
.mr-6 { margin-right: var(--space-6); }
.mr-8 { margin-right: var(--space-8); }
.mr-10 { margin-right: var(--space-10); }
.mr-12 { margin-right: var(--space-12); }
.mr-16 { margin-right: var(--space-16); }
.mr-20 { margin-right: var(--space-20); }
.mr-24 { margin-right: var(--space-24); }
.mr-auto { margin-right: auto; }

/* Bottom */
.mb-0 { margin-bottom: var(--space-0); }
.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-5 { margin-bottom: var(--space-5); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }
.mb-10 { margin-bottom: var(--space-10); }
.mb-12 { margin-bottom: var(--space-12); }
.mb-16 { margin-bottom: var(--space-16); }
.mb-20 { margin-bottom: var(--space-20); }
.mb-24 { margin-bottom: var(--space-24); }
.mb-auto { margin-bottom: auto; }

/* Left */
.ml-0 { margin-left: var(--space-0); }
.ml-1 { margin-left: var(--space-1); }
.ml-2 { margin-left: var(--space-2); }
.ml-3 { margin-left: var(--space-3); }
.ml-4 { margin-left: var(--space-4); }
.ml-5 { margin-left: var(--space-5); }
.ml-6 { margin-left: var(--space-6); }
.ml-8 { margin-left: var(--space-8); }
.ml-10 { margin-left: var(--space-10); }
.ml-12 { margin-left: var(--space-12); }
.ml-16 { margin-left: var(--space-16); }
.ml-20 { margin-left: var(--space-20); }
.ml-24 { margin-left: var(--space-24); }
.ml-auto { margin-left: auto; }

/* Horizontal */
.mx-0 { margin-left: var(--space-0); margin-right: var(--space-0); }
.mx-1 { margin-left: var(--space-1); margin-right: var(--space-1); }
.mx-2 { margin-left: var(--space-2); margin-right: var(--space-2); }
.mx-3 { margin-left: var(--space-3); margin-right: var(--space-3); }
.mx-4 { margin-left: var(--space-4); margin-right: var(--space-4); }
.mx-5 { margin-left: var(--space-5); margin-right: var(--space-5); }
.mx-6 { margin-left: var(--space-6); margin-right: var(--space-6); }
.mx-8 { margin-left: var(--space-8); margin-right: var(--space-8); }
.mx-10 { margin-left: var(--space-10); margin-right: var(--space-10); }
.mx-12 { margin-left: var(--space-12); margin-right: var(--space-12); }
.mx-16 { margin-left: var(--space-16); margin-right: var(--space-16); }
.mx-20 { margin-left: var(--space-20); margin-right: var(--space-20); }
.mx-24 { margin-left: var(--space-24); margin-right: var(--space-24); }
.mx-auto { margin-left: auto; margin-right: auto; }

/* Vertical */
.my-0 { margin-top: var(--space-0); margin-bottom: var(--space-0); }
.my-1 { margin-top: var(--space-1); margin-bottom: var(--space-1); }
.my-2 { margin-top: var(--space-2); margin-bottom: var(--space-2); }
.my-3 { margin-top: var(--space-3); margin-bottom: var(--space-3); }
.my-4 { margin-top: var(--space-4); margin-bottom: var(--space-4); }
.my-5 { margin-top: var(--space-5); margin-bottom: var(--space-5); }
.my-6 { margin-top: var(--space-6); margin-bottom: var(--space-6); }
.my-8 { margin-top: var(--space-8); margin-bottom: var(--space-8); }
.my-10 { margin-top: var(--space-10); margin-bottom: var(--space-10); }
.my-12 { margin-top: var(--space-12); margin-bottom: var(--space-12); }
.my-16 { margin-top: var(--space-16); margin-bottom: var(--space-16); }
.my-20 { margin-top: var(--space-20); margin-bottom: var(--space-20); }
.my-24 { margin-top: var(--space-24); margin-bottom: var(--space-24); }
.my-auto { margin-top: auto; margin-bottom: auto; }

/* ========================================
   PADDING UTILITIES
   ======================================== */

/* All sides */
.p-0 { padding: var(--space-0); }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-5 { padding: var(--space-5); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }
.p-10 { padding: var(--space-10); }
.p-12 { padding: var(--space-12); }
.p-16 { padding: var(--space-16); }
.p-20 { padding: var(--space-20); }
.p-24 { padding: var(--space-24); }

/* Top */
.pt-0 { padding-top: var(--space-0); }
.pt-1 { padding-top: var(--space-1); }
.pt-2 { padding-top: var(--space-2); }
.pt-3 { padding-top: var(--space-3); }
.pt-4 { padding-top: var(--space-4); }
.pt-5 { padding-top: var(--space-5); }
.pt-6 { padding-top: var(--space-6); }
.pt-8 { padding-top: var(--space-8); }
.pt-10 { padding-top: var(--space-10); }
.pt-12 { padding-top: var(--space-12); }
.pt-16 { padding-top: var(--space-16); }
.pt-20 { padding-top: var(--space-20); }
.pt-24 { padding-top: var(--space-24); }

/* Right */
.pr-0 { padding-right: var(--space-0); }
.pr-1 { padding-right: var(--space-1); }
.pr-2 { padding-right: var(--space-2); }
.pr-3 { padding-right: var(--space-3); }
.pr-4 { padding-right: var(--space-4); }
.pr-5 { padding-right: var(--space-5); }
.pr-6 { padding-right: var(--space-6); }
.pr-8 { padding-right: var(--space-8); }
.pr-10 { padding-right: var(--space-10); }
.pr-12 { padding-right: var(--space-12); }
.pr-16 { padding-right: var(--space-16); }
.pr-20 { padding-right: var(--space-20); }
.pr-24 { padding-right: var(--space-24); }

/* Bottom */
.pb-0 { padding-bottom: var(--space-0); }
.pb-1 { padding-bottom: var(--space-1); }
.pb-2 { padding-bottom: var(--space-2); }
.pb-3 { padding-bottom: var(--space-3); }
.pb-4 { padding-bottom: var(--space-4); }
.pb-5 { padding-bottom: var(--space-5); }
.pb-6 { padding-bottom: var(--space-6); }
.pb-8 { padding-bottom: var(--space-8); }
.pb-10 { padding-bottom: var(--space-10); }
.pb-12 { padding-bottom: var(--space-12); }
.pb-16 { padding-bottom: var(--space-16); }
.pb-20 { padding-bottom: var(--space-20); }
.pb-24 { padding-bottom: var(--space-24); }

/* Left */
.pl-0 { padding-left: var(--space-0); }
.pl-1 { padding-left: var(--space-1); }
.pl-2 { padding-left: var(--space-2); }
.pl-3 { padding-left: var(--space-3); }
.pl-4 { padding-left: var(--space-4); }
.pl-5 { padding-left: var(--space-5); }
.pl-6 { padding-left: var(--space-6); }
.pl-8 { padding-left: var(--space-8); }
.pl-10 { padding-left: var(--space-10); }
.pl-12 { padding-left: var(--space-12); }
.pl-16 { padding-left: var(--space-16); }
.pl-20 { padding-left: var(--space-20); }
.pl-24 { padding-left: var(--space-24); }

/* Horizontal */
.px-0 { padding-left: var(--space-0); padding-right: var(--space-0); }
.px-1 { padding-left: var(--space-1); padding-right: var(--space-1); }
.px-2 { padding-left: var(--space-2); padding-right: var(--space-2); }
.px-3 { padding-left: var(--space-3); padding-right: var(--space-3); }
.px-4 { padding-left: var(--space-4); padding-right: var(--space-4); }
.px-5 { padding-left: var(--space-5); padding-right: var(--space-5); }
.px-6 { padding-left: var(--space-6); padding-right: var(--space-6); }
.px-8 { padding-left: var(--space-8); padding-right: var(--space-8); }
.px-10 { padding-left: var(--space-10); padding-right: var(--space-10); }
.px-12 { padding-left: var(--space-12); padding-right: var(--space-12); }
.px-16 { padding-left: var(--space-16); padding-right: var(--space-16); }
.px-20 { padding-left: var(--space-20); padding-right: var(--space-20); }
.px-24 { padding-left: var(--space-24); padding-right: var(--space-24); }

/* Vertical */
.py-0 { padding-top: var(--space-0); padding-bottom: var(--space-0); }
.py-1 { padding-top: var(--space-1); padding-bottom: var(--space-1); }
.py-2 { padding-top: var(--space-2); padding-bottom: var(--space-2); }
.py-3 { padding-top: var(--space-3); padding-bottom: var(--space-3); }
.py-4 { padding-top: var(--space-4); padding-bottom: var(--space-4); }
.py-5 { padding-top: var(--space-5); padding-bottom: var(--space-5); }
.py-6 { padding-top: var(--space-6); padding-bottom: var(--space-6); }
.py-8 { padding-top: var(--space-8); padding-bottom: var(--space-8); }
.py-10 { padding-top: var(--space-10); padding-bottom: var(--space-10); }
.py-12 { padding-top: var(--space-12); padding-bottom: var(--space-12); }
.py-16 { padding-top: var(--space-16); padding-bottom: var(--space-16); }
.py-20 { padding-top: var(--space-20); padding-bottom: var(--space-20); }
.py-24 { padding-top: var(--space-24); padding-bottom: var(--space-24); }

/* ========================================
   GAP UTILITIES (for flexbox/grid)
   ======================================== */

.gap-0 { gap: var(--space-0); }
.gap-1 { gap: var(--space-1); }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-5 { gap: var(--space-5); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }
.gap-10 { gap: var(--space-10); }
.gap-12 { gap: var(--space-12); }
.gap-16 { gap: var(--space-16); }
.gap-20 { gap: var(--space-20); }
.gap-24 { gap: var(--space-24); }
