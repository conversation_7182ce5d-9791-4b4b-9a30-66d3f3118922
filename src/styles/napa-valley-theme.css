/**
 * Napa Valley Wine Theme CSS
 * 
 * Elegant wine country styling with deep wine reds, golden ambers, and vineyard greens
 */

/* Import elegant serif font for wine country aesthetic */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Crimson+Text:wght@400;500;600&display=swap');

/* Napa Valley Color Palette */
:root {
  /* Primary wine colors */
  --napa-wine-red: #7c2d12;
  --napa-wine-red-light: #991b1b;
  --napa-wine-red-dark: #5b1a0f;
  
  /* Golden amber accents */
  --napa-amber: #a16207;
  --napa-amber-light: #ca8a04;
  --napa-amber-dark: #78350f;
  
  /* Vineyard greens */
  --napa-vineyard-green: #166534;
  --napa-vineyard-green-light: #15803d;
  --napa-vineyard-green-dark: #14532d;
  
  /* Neutral earth tones */
  --napa-cream: #fef7ed;
  --napa-sand: #f5f5dc;
  --napa-earth: #8b7355;
  --napa-stone: #6b5b73;
  
  /* Text colors */
  --napa-text-primary: #3c2415;
  --napa-text-secondary: #6b5b73;
  --napa-text-muted: #8b7355;
  
  /* Elegant typography */
  --napa-font-display: 'Playfair Display', serif;
  --napa-font-body: 'Crimson Text', serif;
  --napa-font-accent: 'Playfair Display', serif;
}

/* Napa Valley Layout Styling */
.napa-valley-demo {
  font-family: var(--napa-font-body);
  background: linear-gradient(135deg, var(--napa-cream) 0%, var(--napa-sand) 100%);
  color: var(--napa-text-primary);
}

/* Navigation styling for wine theme */
.napa-valley-demo .neutral-layout header {
  background: linear-gradient(90deg, var(--napa-wine-red) 0%, var(--napa-wine-red-dark) 100%);
  backdrop-filter: blur(10px);
  border-bottom: 2px solid var(--napa-amber);
}

.napa-valley-demo .neutral-layout h1 {
  font-family: var(--napa-font-display);
  color: var(--napa-cream);
  font-weight: 700;
  letter-spacing: 1px;
}

/* Button styling */
.napa-valley-demo button {
  font-family: var(--napa-font-accent);
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.napa-valley-demo .primary-button {
  background: linear-gradient(135deg, var(--napa-wine-red) 0%, var(--napa-wine-red-light) 100%);
  color: var(--napa-cream);
  border: 1px solid var(--napa-amber);
  box-shadow: 0 4px 12px rgba(124, 45, 18, 0.3);
}

.napa-valley-demo .primary-button:hover {
  background: linear-gradient(135deg, var(--napa-wine-red-light) 0%, var(--napa-wine-red) 100%);
  box-shadow: 0 6px 16px rgba(124, 45, 18, 0.4);
  transform: translateY(-2px);
}

.napa-valley-demo .secondary-button {
  background: var(--napa-cream);
  color: var(--napa-wine-red);
  border: 2px solid var(--napa-amber);
}

.napa-valley-demo .secondary-button:hover {
  background: var(--napa-amber);
  color: var(--napa-cream);
}

/* Panel styling */
.napa-valley-demo .left-pane,
.napa-valley-demo .right-pane {
  background: rgba(254, 247, 237, 0.95);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(161, 98, 7, 0.2);
  box-shadow: 0 8px 24px rgba(124, 45, 18, 0.15);
}

.napa-valley-demo .left-pane h2,
.napa-valley-demo .right-pane h2 {
  font-family: var(--napa-font-display);
  color: var(--napa-wine-red);
  font-weight: 600;
  border-bottom: 2px solid var(--napa-amber);
  padding-bottom: 0.5rem;
}

/* POI Card styling */
.napa-valley-demo .poi-card {
  background: var(--napa-cream);
  border: 1px solid var(--napa-amber);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(124, 45, 18, 0.1);
  transition: all 0.3s ease;
}

.napa-valley-demo .poi-card:hover {
  box-shadow: 0 8px 24px rgba(124, 45, 18, 0.2);
  transform: translateY(-4px);
  border-color: var(--napa-wine-red);
}

.napa-valley-demo .poi-card h3 {
  font-family: var(--napa-font-display);
  color: var(--napa-wine-red);
  font-weight: 600;
}

.napa-valley-demo .poi-card .poi-type {
  background: var(--napa-amber);
  color: var(--napa-cream);
  font-family: var(--napa-font-accent);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
}

/* Wine-specific POI types */
.napa-valley-demo .poi-type.winery {
  background: var(--napa-wine-red);
}

.napa-valley-demo .poi-type.restaurant {
  background: var(--napa-amber);
}

.napa-valley-demo .poi-type.resort {
  background: var(--napa-vineyard-green);
}

.napa-valley-demo .poi-type.experience {
  background: var(--napa-stone);
}

/* Map styling */
.napa-valley-demo .mapboxgl-popup-content {
  background: var(--napa-cream);
  border: 2px solid var(--napa-amber);
  border-radius: 12px;
  font-family: var(--napa-font-body);
  color: var(--napa-text-primary);
}

.napa-valley-demo .mapboxgl-popup-tip {
  border-top-color: var(--napa-amber);
}

/* Custom markers for wine theme */
.napa-valley-demo .marker-winery {
  background: var(--napa-wine-red);
  border: 3px solid var(--napa-amber);
  box-shadow: 0 4px 12px rgba(124, 45, 18, 0.4);
}

.napa-valley-demo .marker-restaurant {
  background: var(--napa-amber);
  border: 3px solid var(--napa-wine-red);
  box-shadow: 0 4px 12px rgba(161, 98, 7, 0.4);
}

.napa-valley-demo .marker-resort {
  background: var(--napa-vineyard-green);
  border: 3px solid var(--napa-cream);
  box-shadow: 0 4px 12px rgba(22, 101, 52, 0.4);
}

/* Journey controls */
.napa-valley-demo .journey-controls {
  background: rgba(254, 247, 237, 0.95);
  border: 2px solid var(--napa-amber);
  border-radius: 12px;
  backdrop-filter: blur(8px);
}

.napa-valley-demo .begin-journey-button {
  background: linear-gradient(135deg, var(--napa-wine-red) 0%, var(--napa-amber) 100%);
  color: var(--napa-cream);
  font-family: var(--napa-font-display);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 6px 16px rgba(124, 45, 18, 0.3);
}

.napa-valley-demo .begin-journey-button:hover {
  box-shadow: 0 8px 20px rgba(124, 45, 18, 0.4);
  transform: translateY(-2px);
}

/* Progress indicators */
.napa-valley-demo .progress-bar {
  background: var(--napa-sand);
  border: 1px solid var(--napa-amber);
}

.napa-valley-demo .progress-fill {
  background: linear-gradient(90deg, var(--napa-wine-red) 0%, var(--napa-amber) 100%);
}

/* Scrollbar styling */
.napa-valley-demo ::-webkit-scrollbar {
  width: 8px;
}

.napa-valley-demo ::-webkit-scrollbar-track {
  background: var(--napa-sand);
}

.napa-valley-demo ::-webkit-scrollbar-thumb {
  background: var(--napa-amber);
  border-radius: 4px;
}

.napa-valley-demo ::-webkit-scrollbar-thumb:hover {
  background: var(--napa-wine-red);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .napa-valley-demo .neutral-layout h1 {
    font-size: 1.25rem;
  }
  
  .napa-valley-demo .poi-card {
    margin-bottom: 1rem;
  }
  
  .napa-valley-demo .left-pane,
  .napa-valley-demo .right-pane {
    margin: 0.5rem;
  }
}
