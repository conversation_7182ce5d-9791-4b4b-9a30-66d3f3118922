.cluster-marker {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  text-align: center;
  line-height: 30px;
  font-weight: bold;
  font-size: 14px;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.cluster-marker:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.route-cluster {
  background-color: #3B82F6;
  color: white;
}

.city-cluster {
  background-color: #10B981;
  color: white;
}

.button-tooltip {
  position: relative;
  display: inline-block;
}

/* Position tooltip above the button when at the bottom of the screen */
.button-tooltip:hover::after {
  content: attr(title);
  position: absolute;
  top: auto;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  padding: 8px 12px;
  background-color: rgba(0, 0, 0, 0.9);
  color: white;
  border-radius: 6px;
  font-size: 14px;
  white-space: normal;
  max-width: 300px;
  z-index: 1000;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  text-align: center;
  line-height: 1.4;
}

/* Position tooltip arrow */
.button-tooltip:hover::before {
  content: '';
  position: absolute;
  top: auto;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 6px;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.9) transparent transparent transparent;
  margin-bottom: 2px;
  z-index: 1000;
}

/* Add hover effect for buttons */
.button-tooltip {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.button-tooltip:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
} 