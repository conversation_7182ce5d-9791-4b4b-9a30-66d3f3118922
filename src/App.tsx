import React, { useEffect } from "react";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider, useAuth } from "./contexts/AuthContext";
import { ClientProvider } from "./contexts/ClientContext";
import { ThemeProvider } from "./providers/ThemeProvider";
import { MapReadyProvider } from './providers/MapReadyProvider';
import { Toaster } from "@/components/ui/toaster";

import HomePage from "./pages/HomePage";
import MapView from "./pages/MapView";
import Admin from "./pages/Admin";
import Auth from "./pages/Auth";
import NotFound from "./pages/NotFound";
import Profile from "./pages/Profile";
import ClientDemo from "./pages/ClientDemo";
import MyRoutes from "./pages/MyRoutes";
import QuoteRequests from "./pages/QuoteRequests";
import PaymentPlans from "./pages/PaymentPlans";
import Developers from "./pages/Developers";
import Contact from "./pages/Contact";
import ProgressDemo from "./pages/ProgressDemo";
import MoroccanDemo from "./pages/moroccan-demo";
import TestMoroccan from "./pages/test-moroccan";
import MoroccanTest from "./MoroccanTest";
import SimpleMorocco from "./pages/simple-morocco";
import NeutralDemo from "./pages/neutral-demo";
import EnhancedNeutralDemo from "./pages/enhanced-neutral-demo";
import ThemeShowcase from "./pages/theme-showcase";
import NapaValleyDemo from "./pages/napa-valley-demo";
import Route66Demo from "./pages/route66-demo";
import PortugalDemo from "./pages/portugal-demo";
// New Framework Demos
import NeutralFrameworkDemo from "./pages/framework-demos/NeutralFrameworkDemo";
import MoroccoFrameworkDemo from "./pages/framework-demos/MoroccoFrameworkDemo";
// Admin Portal
import AdminDashboard from "./pages/admin/AdminDashboard";
import ClientManagement from "./pages/admin/ClientManagement";
import ContentManagement from "./pages/admin/ContentManagement";
import ThemeCustomization from "./pages/admin/ThemeCustomization";
import { AdminAuthProvider } from "./contexts/AdminAuthContext";
import 'mapbox-gl/dist/mapbox-gl.css';
import './styles/theme-variables.css';
import './styles/core-animation.css';
// import AppUpdater from "./components/AppUpdater"; // Commented out to resolve linter error

// Extend Window interface to include our custom properties
declare global {
  interface Window {
    __markerHandlingInitialized?: boolean;
  }
}

// Get client ID from URL or environment
const getInitialClientId = (): string => {
  const pathname = window.location.pathname;

  // Map specific demo pages to their client IDs
  if (pathname === '/moroccan-demo') return 'morocco';
  if (pathname === '/portugal-demo') return 'portugal';
  if (pathname === '/napa-valley-demo') return 'napa-valley';
  if (pathname === '/route66-demo') return 'route66';
  if (pathname === '/neutral-demo') return 'neutral';

  // Check for client ID in URL path (e.g., /morocco/...)
  const pathSegments = pathname.split('/');
  const clientIdFromPath = pathSegments[1];

  // Check for client ID in URL query parameter
  const urlParams = new URLSearchParams(window.location.search);
  const clientIdFromQuery = urlParams.get('client');

  // Check for client ID in localStorage (for persistence)
  const clientIdFromStorage = localStorage.getItem('clientId');

  // Return the first available client ID or default to 'neutral'
  return clientIdFromPath || clientIdFromQuery || clientIdFromStorage || 'neutral';
};

// Log that we're relying on index.html for critical CSS
console.log('Using index.html for critical marker CSS');

// Initialize marker handling once
if (typeof window !== 'undefined' && !window.__markerHandlingInitialized) {
  window.__markerHandlingInitialized = true;
  console.log('Setting up marker handling (once)');
  
  // Add CSS and global fixes for marker visibility 
  // These are now expected to be in index.html or a global CSS file
  // injectMapboxMarkerCSS(); 
  // setupGlobalMarkerPositionFixes();
  
  // Log initialization for debugging
  window.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded - Marker handling initialized');
  });
}

const queryClient = new QueryClient();

// Define the HomePage element outside the App component to keep it stable
const homeElement = <HomePage />;

// New component to contain the logic that uses useAuth
const AppContent = () => {
  // Debug logging disabled for production readiness
  // console.log('[AppContent RENDER]');
  const { user /*, loading */ } = useAuth(); // loading commented out earlier
  // const isMobile = useIsMobile(); // Not needed here if not directly controlling UI based on it

  /* Loading check commented out earlier
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-100">
        <div className="text-xl font-semibold text-gray-700">Loading...</div>
      </div>
    );
  }
  */

  return (
    <Router>
      <Routes>
        <Route path="/" element={<EnhancedNeutralDemo />} />
        <Route path="/map" element={<MapView />} />
        <Route path="/dashboard" element={user ? <MyRoutes /> : <Navigate to="/auth" />} />
        <Route path="/admin" element={
          <AdminAuthProvider>
            <AdminDashboard />
          </AdminAuthProvider>
        } />
        <Route path="/admin/dashboard" element={
          <AdminAuthProvider>
            <AdminDashboard />
          </AdminAuthProvider>
        } />
        <Route path="/admin/legacy" element={
          <AdminAuthProvider>
            <Admin />
          </AdminAuthProvider>
        } />
        <Route path="/admin/clients" element={
          <AdminAuthProvider>
            <ClientManagement />
          </AdminAuthProvider>
        } />
        <Route path="/admin/content" element={
          <AdminAuthProvider>
            <ContentManagement />
          </AdminAuthProvider>
        } />
        <Route path="/admin/themes" element={
          <AdminAuthProvider>
            <ThemeCustomization />
          </AdminAuthProvider>
        } />
        <Route path="/admin/destinations" element={
          <AdminAuthProvider>
            <Admin />
          </AdminAuthProvider>
        } />
        <Route path="/admin/pois" element={
          <AdminAuthProvider>
            <Admin />
          </AdminAuthProvider>
        } />
        <Route path="/admin/users" element={
          <AdminAuthProvider>
            <Admin />
          </AdminAuthProvider>
        } />
        <Route path="/admin/quotes" element={
          <AdminAuthProvider>
            <Admin />
          </AdminAuthProvider>
        } />
        <Route path="/profile" element={user ? <Profile /> : <Navigate to="/auth" />} />
        <Route path="/auth" element={<Auth />} />
        <Route path="/client-demo" element={<ClientDemo />} />
        <Route path="/my-routes" element={user ? <MyRoutes /> : <Navigate to="/auth" />} />
        <Route path="/quote-requests" element={user ? <QuoteRequests /> : <Navigate to="/auth" />} />
        <Route path="/payment-plans" element={<PaymentPlans />} />
        <Route path="/developers" element={<Developers />} />
        <Route path="/contact" element={<Contact />} />
        <Route path="/progress-demo" element={<ProgressDemo />} />
        <Route path="/neutral-demo" element={<NeutralDemo />} />
        <Route path="/enhanced-neutral-demo" element={<EnhancedNeutralDemo />} />
        <Route path="/theme-showcase" element={<ThemeShowcase />} />
        <Route path="/napa-valley-demo" element={<NapaValleyDemo />} />
        <Route path="/route66-demo" element={<Route66Demo />} />
        <Route path="/portugal-demo" element={<PortugalDemo />} />
        <Route path="/moroccan-demo"
          element={(() => {
            console.log('Rendering MoroccanDemo component');
            return <MoroccanDemo />;
          })()}
        />
        <Route path="/test-moroccan" element={<TestMoroccan />} />
        <Route path="/morocco-test" element={<MoroccanTest />} />
        <Route path="/morocco-simple" element={<div>Simple Morocco Test</div>} />
        <Route path="/simple-morocco" element={<SimpleMorocco />} />
        <Route path="*" element={<NotFound />} />
      </Routes>
      <Toaster />
      {/* <AppUpdater /> */}
    </Router>
  );
};

const App = () => {
  // Debug logging disabled for production readiness
  // console.log('[App RENDER Shell]');
  const initialClientId = getInitialClientId();

  useEffect(() => {
    console.log('[App MOUNTED Shell]');
    localStorage.setItem('clientId', initialClientId);
    return () => {
      console.log('[App UNMOUNTING Shell]');
    };
  }, [initialClientId]);

  return (
    <QueryClientProvider client={queryClient}>
      <ClientProvider initialClientId={initialClientId}>
        <ThemeProvider>
          <AuthProvider> {/* AuthProvider now wraps AppContent */}
            <MapReadyProvider>
              <TooltipProvider>
                <AppContent /> {/* AppContent calls useAuth and renders Router/Routes */}
              </TooltipProvider>
            </MapReadyProvider>
          </AuthProvider>
        </ThemeProvider>
      </ClientProvider>
    </QueryClientProvider>
  );
};

export default App;
