
/* Import core system first */
@import './styles/core/variables.css';
@import './styles/core/markers-unified.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 40 33% 98%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 18 71% 62%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 18 71% 62%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }
  
  /* Added responsive font sizing */
  h1 {
    @apply text-2xl md:text-3xl lg:text-4xl font-bold;
  }
  
  h2 {
    @apply text-xl md:text-2xl lg:text-3xl font-bold;
  }
  
  h3 {
    @apply text-lg md:text-xl font-bold;
  }
}

@layer components {
  .map-container {
    @apply h-full w-full rounded-lg overflow-hidden;
  }
  
  .morocco-pattern {
    background-color: #f8f9fa;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e07a5f' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  }
  
  .destination-card {
    @apply bg-white rounded-lg shadow-md overflow-hidden transition-all hover:shadow-lg;
  }
  
  .destination-card img {
    @apply w-full h-48 object-cover;
  }
  
  .destination-card:hover {
    @apply transform -translate-y-1;
  }
  
  .route-line {
    @apply stroke-morocco-blue stroke-2 fill-none;
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
  }
  
  .route-line.animated {
    @apply animate-route-draw;
  }
  
  .location-marker {
    @apply text-morocco-terracotta fill-current;
  }
  
  .vehicle-option {
    @apply border-2 border-transparent rounded-lg p-4 cursor-pointer transition-all;
  }
  
  .vehicle-option.selected {
    @apply border-morocco-terracotta bg-morocco-terracotta/10;
  }
  
  .btn-morocco {
    @apply bg-morocco-terracotta text-white hover:bg-morocco-terracotta/90 font-medium rounded-lg px-5 py-2.5 text-center transition-colors;
  }
  
  .btn-outline-morocco {
    @apply border border-morocco-terracotta text-morocco-terracotta hover:bg-morocco-terracotta/10 font-medium rounded-lg px-5 py-2.5 text-center transition-colors;
  }
  
  /* Added mobile optimized classes */
  .mobile-container {
    @apply px-4 py-3 md:px-6 md:py-4;
  }
  
  .mobile-card {
    @apply rounded-lg shadow-md p-3 md:p-5;
  }
  
  .mobile-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4;
  }
}

/* Added animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.8); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-slide-in {
  animation: slideIn 0.5s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out forwards;
}

/* Add staggered animation delays for list items */
.stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-item:nth-child(4) { animation-delay: 0.4s; }
.stagger-item:nth-child(5) { animation-delay: 0.5s; }

/* Added mobile navigation */
.mobile-nav {
  @apply fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-10 py-2 px-4;
  display: flex;
  justify-content: space-around;
}

.mobile-nav-item {
  @apply flex flex-col items-center text-xs text-gray-600;
}

.mobile-nav-item.active {
  @apply text-morocco-terracotta;
}

/* Added custom scrollbar */
@media (min-width: 768px) {
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #e07a5f80;
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: #e07a5f;
  }
}
