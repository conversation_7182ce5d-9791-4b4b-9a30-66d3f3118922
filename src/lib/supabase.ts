/**
 * Supabase Configuration and Client Setup
 * Handles database connections and authentication
 */

import { createClient } from '@supabase/supabase-js';

// Supabase configuration with fallback
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Check if Supabase is configured
const isSupabaseConfigured = supabaseUrl && supabaseAnonKey &&
  supabaseUrl !== 'your_supabase_project_url_here' &&
  supabaseAnonKey !== 'your_supabase_anon_key_here';

if (!isSupabaseConfigured) {
  console.warn('⚠️ Supabase not configured. Using mock data fallback.');
  console.log('To enable database features:');
  console.log('1. Create a Supabase project at https://supabase.com');
  console.log('2. Add VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY to your .env file');
  console.log('3. Follow the migration instructions in docs/MIGRATION_INSTRUCTIONS.md');
}

// Create Supabase client with fallback
export const supabase = isSupabaseConfigured
  ? createClient(supabaseUrl!, supabaseAnonKey!, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true
      }
    })
  : null; // Fallback to null when not configured

// Database table names
export const TABLES = {
  CLIENTS: 'clients',
  DESTINATIONS: 'destinations', 
  POIS: 'pois',
  JOURNEYS: 'journeys',
  ITINERARIES: 'itineraries',
  USERS: 'users',
  BOOKINGS: 'bookings',
  REVIEWS: 'reviews'
} as const;

// Type definitions for database tables
export interface DatabaseClient {
  id: string;
  name: string;
  slug: string;
  theme_config: any;
  branding_config: any;
  map_config: any;
  created_at: string;
  updated_at: string;
  is_active: boolean;
}

export interface DatabaseDestination {
  id: string;
  client_id: string;
  name: string;
  description: string;
  coordinates: [number, number];
  region: string;
  country: string;
  tags: string[];
  images: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface DatabasePOI {
  id: string;
  client_id: string;
  destination_id?: string;
  name: string;
  description: string;
  category: string;
  coordinates: [number, number];
  location: string;
  region: string;
  tags: string[];
  images: string[];
  price_range: string;
  duration_minutes: number;
  rating: number;
  review_count: number;
  opening_hours: any;
  contact_info: any;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface DatabaseJourney {
  id: string;
  client_id: string;
  user_id?: string;
  name: string;
  description: string;
  destinations: string[]; // Array of destination IDs
  pois: string[]; // Array of POI IDs
  duration_days: number;
  total_distance_km: number;
  estimated_cost: number;
  difficulty_level: string;
  best_season: string[];
  tags: string[];
  is_template: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface DatabaseItinerary {
  id: string;
  journey_id: string;
  user_id?: string;
  day_number: number;
  destination_id: string;
  pois: string[]; // Array of POI IDs for this day
  activities: string[];
  accommodation?: any;
  transportation?: any;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface DatabaseUser {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  preferences: any;
  travel_interests: string[];
  created_at: string;
  updated_at: string;
}

export interface DatabaseBooking {
  id: string;
  user_id: string;
  journey_id: string;
  client_id: string;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  booking_date: string;
  travel_date: string;
  guest_count: number;
  total_amount: number;
  payment_status: 'pending' | 'paid' | 'refunded';
  special_requests?: string;
  contact_info: any;
  created_at: string;
  updated_at: string;
}

export interface DatabaseReview {
  id: string;
  user_id: string;
  poi_id?: string;
  journey_id?: string;
  client_id: string;
  rating: number;
  title?: string;
  content: string;
  images?: string[];
  is_verified: boolean;
  is_featured: boolean;
  created_at: string;
  updated_at: string;
}

// Helper functions for common database operations
export class DatabaseHelper {
  /**
   * Check if Supabase is available
   */
  private static checkSupabaseAvailable(): boolean {
    if (!supabase) {
      console.warn('Supabase not configured. Database operations unavailable.');
      return false;
    }
    return true;
  }

  /**
   * Get client configuration by slug
   */
  static async getClientBySlug(slug: string): Promise<DatabaseClient | null> {
    if (!this.checkSupabaseAvailable()) return null;

    const { data, error } = await supabase!
      .from(TABLES.CLIENTS)
      .select('*')
      .eq('slug', slug)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error('Error fetching client:', error);
      return null;
    }

    return data;
  }

  /**
   * Get all clients
   */
  static async getAllClients(): Promise<DatabaseClient[]> {
    if (!this.checkSupabaseAvailable()) return [];

    const { data, error } = await supabase!
      .from(TABLES.CLIENTS)
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching all clients:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Get destinations for a client
   */
  static async getDestinations(clientId: string): Promise<DatabaseDestination[]> {
    if (!this.checkSupabaseAvailable()) return [];

    const { data, error } = await supabase!
      .from(TABLES.DESTINATIONS)
      .select('*')
      .eq('client_id', clientId)
      .eq('is_active', true)
      .order('name');

    if (error) {
      console.error('Error fetching destinations:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Get POIs for a client
   */
  static async getPOIs(clientId: string, destinationId?: string): Promise<DatabasePOI[]> {
    if (!this.checkSupabaseAvailable()) return [];

    let query = supabase!
      .from(TABLES.POIS)
      .select('*')
      .eq('client_id', clientId)
      .eq('is_active', true);

    if (destinationId) {
      query = query.eq('destination_id', destinationId);
    }

    const { data, error } = await query.order('name');

    if (error) {
      console.error('Error fetching POIs:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Get journey templates for a client
   */
  static async getJourneyTemplates(clientId: string): Promise<DatabaseJourney[]> {
    if (!this.checkSupabaseAvailable()) return [];

    const { data, error } = await supabase!
      .from(TABLES.JOURNEYS)
      .select('*')
      .eq('client_id', clientId)
      .eq('is_template', true)
      .eq('is_active', true)
      .order('name');

    if (error) {
      console.error('Error fetching journey templates:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Create a new user booking
   */
  static async createBooking(booking: Omit<DatabaseBooking, 'id' | 'created_at' | 'updated_at'>): Promise<DatabaseBooking | null> {
    if (!this.checkSupabaseAvailable()) return null;

    const { data, error } = await supabase!
      .from(TABLES.BOOKINGS)
      .insert(booking)
      .select()
      .single();

    if (error) {
      console.error('Error creating booking:', error);
      return null;
    }

    return data;
  }

  /**
   * Add a review
   */
  static async createReview(review: Omit<DatabaseReview, 'id' | 'created_at' | 'updated_at'>): Promise<DatabaseReview | null> {
    if (!this.checkSupabaseAvailable()) return null;

    const { data, error } = await supabase!
      .from(TABLES.REVIEWS)
      .insert(review)
      .select()
      .single();

    if (error) {
      console.error('Error creating review:', error);
      return null;
    }

    return data;
  }

  /**
   * Get reviews for a POI or Journey
   */
  static async getReviews(poiId?: string, journeyId?: string): Promise<DatabaseReview[]> {
    if (!this.checkSupabaseAvailable()) return [];

    let query = supabase!
      .from(TABLES.REVIEWS)
      .select('*')
      .eq('is_verified', true);

    if (poiId) {
      query = query.eq('poi_id', poiId);
    } else if (journeyId) {
      query = query.eq('journey_id', journeyId);
    }

    const { data, error } = await query
      .order('created_at', { ascending: false })
      .limit(50);

    if (error) {
      console.error('Error fetching reviews:', error);
      return [];
    }

    return data || [];
  }
}

export default supabase;
