import * as turf from '@turf/turf';

// Export the turf library as a single object
export { turf };

/**
 * Simplified interface for working with turf.js in this application
 * 
 * This helper provides easier access to commonly used Turf.js functions
 * for route calculations, distance measurements, and geographic operations.
 * 
 * Example usage:
 * ```
 * import { turf } from '@/lib/turf-helper';
 * 
 * // Create a point
 * const point = turf.point([longitude, latitude]);
 * 
 * // Calculate distance between points
 * const distance = turf.distance(pointA, pointB, { units: 'kilometers' });
 * ```
 */ 