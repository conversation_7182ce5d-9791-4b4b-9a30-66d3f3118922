/**
 * Types barrel file - UNIFIED EXPORTS
 *
 * Central export point for all type definitions
 * Consolidates imports from the new unified type system
 */

// ========================================
// CORE TYPES
// ========================================

// Position types
export type { Position, PositionObject, PositionTuple } from './Position';
export { isPosition, isPositionObject, toPositionObject, toPositionTuple, calculateDistance, isValidPosition } from './Position';

// ========================================
// POI AND DESTINATION TYPES
// ========================================

// POI types (unified)
export type {
  PointOfInterest,
  Destination,
  POICategory,
  POIProperties,
  POIReview
} from './poi';
export {
  normalizePOI,
  normalizeDestination,
  isPointOfInterest,
  isDestination,
  getPOICategories,
  getCategoryDisplayName,
  isValidCoordinates
} from './poi';

// ========================================
// ANIMATION TYPES
// ========================================

// Animation types (unified)
export type {
  AnimationState,
  AnimationConfig,
  RoutePoint,
  RouteData,
  VehicleConfig,
  AnimationProgressInfo,
  CityDriveByInfo,
  POIDiscoveryInfo,
  ProgressCallback,
  CompleteCallback,
  ErrorCallback,
  PositionUpdateCallback,
  AnimationManagerInterface,
  SimpleAnimationOptions,
  UIAnimationState
} from './animation';

export {
  AnimationPhase,
  AnimationEventType,
  ANIMATION_SPEEDS,
  ANIMATION_TIMING,
  isRoutePoint,
  isRouteData
} from './animation';

// ========================================
// CLIENT AND THEME TYPES
// ========================================

// Client types
export type { ClientConfig, ThemeConfig } from './ClientTypes';

// ========================================
// MAP AND VEHICLE TYPES
// ========================================

// Map types
export type { MapFeatureOptions } from './MapTypes';

// Vehicle types
export type { VehicleType } from './VehicleTypes';

// ========================================
// TERRAIN AND ENVIRONMENT TYPES
// ========================================

// Terrain types
export type { TerrainType } from './TerrainType';
export { TERRAIN_SPEED_MULTIPLIERS } from './TerrainType';

// ========================================
// ITINERARY AND TRAVEL TYPES
// ========================================

// Itinerary types
export type {
  TravelPace,
  JourneyStyle,
  TravelInterest,
  PreArrangedJourney
} from './ItineraryParameters';

// ========================================
// LEGACY COMPATIBILITY
// ========================================

// Keep some legacy exports for backward compatibility during transition
export { AnimationEventType as LegacyAnimationEventType } from './AnimationEventTypes';
export type { EventData, AnimationEventData, AnimationEventCallback, CityProximityStage } from './AnimationEventTypes';