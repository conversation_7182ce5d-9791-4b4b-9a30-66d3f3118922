/**
 * TravelAnimator.ts
 * 
 * TRAVEL ANIMATOR TYPES
 * Types specific to the TravelAnimator component
 */

import { Position } from './Position';

// ========================================
// JOURNEY PHASE ENUM
// ========================================

/**
 * Journey phase enum for animation state tracking
 */
export enum JourneyPhase {
  IDLE = 'idle',
  PREPARING = 'preparing',
  READY = 'ready',
  ANIMATING = 'animating',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  ERROR = 'error'
}

// ========================================
// BUTTON STATE ENUM
// ========================================

/**
 * Button state enum for UI controls
 */
export enum ButtonState {
  PLAY = 'play',
  PAUSE = 'pause',
  STOP = 'stop',
  DISABLED = 'disabled'
}

// ========================================
// TRAVEL ANIMATOR INTERFACES
// ========================================

/**
 * Travel animator handles interface for imperative API
 */
export interface TravelAnimatorHandles {
  getActualIsPaused: () => boolean;
  toggleActualPause: () => void;
  startAnimation: (externalRoutePoints: any, options?: any) => Promise<boolean>;
}

/**
 * Travel animator props interface
 */
export interface TravelAnimatorProps {
  map?: any | null;
  routePoints: any;
  animationDuration?: number;
  autoPlay?: boolean;
  onAnimationStart?: () => void;
  onAnimationPause?: () => void;
  onAnimationResume?: () => void;
  onAnimationComplete?: () => void;
  onProgressUpdate?: (progress: number, currentPosition: Position, bearing: number) => void;
  onError?: (error: Error) => void;
}

// ========================================
// ANIMATION STATE INTERFACES
// ========================================

/**
 * Animation state interface for component state
 */
export interface AnimationComponentState {
  currentJourneyPhase: JourneyPhase;
  buttonState: ButtonState;
  progress: number;
  isInitialized: boolean;
  error?: string | null;
}

/**
 * Animation controls interface
 */
export interface AnimationControls {
  play: () => void;
  pause: () => void;
  stop: () => void;
  resume: () => void;
  reset: () => void;
}

// ========================================
// EVENT INTERFACES
// ========================================

/**
 * Animation event data interface
 */
export interface AnimationEventData {
  phase: JourneyPhase;
  progress: number;
  position?: Position;
  bearing?: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

/**
 * Animation event callback type
 */
export type AnimationEventCallback = (data: AnimationEventData) => void;

// ========================================
// CONFIGURATION INTERFACES
// ========================================

/**
 * Travel animator configuration
 */
export interface TravelAnimatorConfig {
  duration: number;
  autoPlay: boolean;
  loop: boolean;
  speed: number;
  easing: string;
  followCamera: boolean;
  showTrail: boolean;
}

/**
 * Default travel animator configuration
 */
export const DEFAULT_TRAVEL_ANIMATOR_CONFIG: TravelAnimatorConfig = {
  duration: 30000,
  autoPlay: false,
  loop: false,
  speed: 1.0,
  easing: 'linear',
  followCamera: true,
  showTrail: false
};

// ========================================
// UTILITY TYPES
// ========================================

/**
 * Partial travel animator config for overrides
 */
export type PartialTravelAnimatorConfig = Partial<TravelAnimatorConfig>;

/**
 * Travel animator status type
 */
export type TravelAnimatorStatus = 'idle' | 'ready' | 'playing' | 'paused' | 'completed' | 'error';

/**
 * Travel animator mode type
 */
export type TravelAnimatorMode = 'manual' | 'auto' | 'guided';

// ========================================
// TYPE GUARDS
// ========================================

/**
 * Type guard for JourneyPhase
 */
export function isJourneyPhase(value: any): value is JourneyPhase {
  return Object.values(JourneyPhase).includes(value);
}

/**
 * Type guard for ButtonState
 */
export function isButtonState(value: any): value is ButtonState {
  return Object.values(ButtonState).includes(value);
}

/**
 * Type guard for TravelAnimatorHandles
 */
export function isTravelAnimatorHandles(value: any): value is TravelAnimatorHandles {
  return (
    value &&
    typeof value === 'object' &&
    typeof value.getActualIsPaused === 'function' &&
    typeof value.toggleActualPause === 'function' &&
    typeof value.startAnimation === 'function'
  );
}

// ========================================
// UTILITY FUNCTIONS
// ========================================

/**
 * Get button state from journey phase
 */
export function getButtonStateFromPhase(phase: JourneyPhase): ButtonState {
  switch (phase) {
    case JourneyPhase.IDLE:
    case JourneyPhase.READY:
    case JourneyPhase.COMPLETED:
      return ButtonState.PLAY;
    case JourneyPhase.ANIMATING:
      return ButtonState.PAUSE;
    case JourneyPhase.PAUSED:
      return ButtonState.PLAY;
    case JourneyPhase.PREPARING:
    case JourneyPhase.ERROR:
      return ButtonState.DISABLED;
    default:
      return ButtonState.DISABLED;
  }
}

/**
 * Check if phase allows animation start
 */
export function canStartAnimation(phase: JourneyPhase): boolean {
  return [
    JourneyPhase.IDLE,
    JourneyPhase.READY,
    JourneyPhase.COMPLETED,
    JourneyPhase.ERROR
  ].includes(phase);
}

/**
 * Check if phase allows animation pause
 */
export function canPauseAnimation(phase: JourneyPhase): boolean {
  return phase === JourneyPhase.ANIMATING;
}

/**
 * Check if phase allows animation resume
 */
export function canResumeAnimation(phase: JourneyPhase): boolean {
  return phase === JourneyPhase.PAUSED;
}

/**
 * Check if phase indicates animation is active
 */
export function isAnimationActive(phase: JourneyPhase): boolean {
  return [
    JourneyPhase.PREPARING,
    JourneyPhase.ANIMATING,
    JourneyPhase.PAUSED
  ].includes(phase);
}
