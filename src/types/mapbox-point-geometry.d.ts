/**
 * Declaration file for @mapbox/point-geometry which is used by mapbox-gl
 */
declare module '@mapbox/point-geometry' {
  class Point {
    x: number;
    y: number;
    
    constructor(x: number, y: number);
    
    clone(): Point;
    add(p: Point): Point;
    sub(p: Point): Point;
    mult(k: number): Point;
    div(k: number): Point;
    rotate(a: number): Point;
    matMult(m: number[]): Point;
    distSqr(p: Point): number;
    dist(p: Point): number;
    mag(): number;
    equals(p: Point): boolean;
    toString(): string;
    convert(a: any): Point;
  }
  
  export = Point;
} 