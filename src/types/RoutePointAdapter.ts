/**
 * RoutePointAdapter.ts
 * Utility functions for converting between different RoutePoint type definitions
 * This adapter resolves type compatibility issues between different RoutePoint interfaces
 */

import { Position, isPosition } from './Position';
import { RoutePoint as DetailedRoutePoint } from './RoutePoint';
import { TerrainType } from './TerrainType';

// Use interface duplication for the SimpleRoutePoint to avoid path alias issues
interface SimpleRoutePoint {
  lat: number;
  lng: number;
}

/**
 * Type guard to check if a value is the detailed RoutePoint from RoutePoint.ts
 */
export function isDetailedRoutePoint(point: any): point is DetailedRoutePoint {
  return (
    point &&
    typeof point === 'object' &&
    'position' in point &&
    'distance' in point &&
    'bearing' in point &&
    'index' in point &&
    isPosition(point.position)
  );
}

/**
 * Type guard to check if a value is the simple RoutePoint from TravelAnimatorTypes.ts
 */
export function isSimpleRoutePoint(point: any): point is SimpleRoutePoint {
  return (
    point &&
    typeof point === 'object' &&
    'lat' in point &&
    'lng' in point &&
    typeof point.lat === 'number' &&
    typeof point.lng === 'number' &&
    !('position' in point) &&
    !('distance' in point)
  );
}

/**
 * Safely get the position from any RoutePoint type
 * @returns Position tuple [lng, lat]
 */
export function getRoutePointPosition(point: DetailedRoutePoint | SimpleRoutePoint): Position {
  if (isDetailedRoutePoint(point)) {
    // RoutePoint from RoutePoint.ts - position is already a Position tuple
    return point.position;
  } else if (isSimpleRoutePoint(point)) {
    // RoutePoint from TravelAnimatorTypes.ts - needs conversion
    return [point.lng, point.lat];
  }
  
  // Fallback to a default position if type cannot be determined
  console.error('Invalid RoutePoint type in getRoutePointPosition:', point);
  return [0, 0]; // Default to [0,0] as fallback
}

/**
 * Convert from simple RoutePoint to detailed RoutePoint
 */
export function convertSimpleToDetailedRoutePoint(
  point: SimpleRoutePoint,
  index: number = 0,
  bearing: number = 0,
  distance: number = 0
): DetailedRoutePoint {
  return {
    position: [point.lng, point.lat],
    index,
    bearing,
    distance,
    // Optional fields are left undefined
    terrain: undefined,
    elevation: undefined,
    nearestLocation: undefined,
    pois: undefined,
    metadata: undefined
  };
}

/**
 * Convert from detailed RoutePoint to simple RoutePoint
 */
export function convertDetailedToSimpleRoutePoint(point: DetailedRoutePoint): SimpleRoutePoint {
  return {
    lng: point.position[0],
    lat: point.position[1]
  };
}

/**
 * Create a standard detailed RoutePoint from a Position
 */
export function createRoutePointFromPosition(
  position: Position,
  index: number = 0,
  options: {
    bearing?: number;
    distance?: number;
    terrain?: TerrainType;
    elevation?: number;
    nearestLocation?: string;
  } = {}
): DetailedRoutePoint {
  return {
    position,
    index,
    bearing: options.bearing || 0,
    distance: options.distance || 0,
    terrain: options.terrain,
    elevation: options.elevation,
    nearestLocation: options.nearestLocation
  };
}

/**
 * Safe method to extract a standard Position from any RoutePoint type
 */
export function safeGetRoutePointPosition(point: any): Position | null {
  try {
    if (isDetailedRoutePoint(point)) {
      return point.position;
    }
    
    if (isSimpleRoutePoint(point)) {
      return [point.lng, point.lat];
    }
    
    // Try with Position type guard
    if (isPosition(point)) {
      return point;
    }
    
    // Check if it's an array-like with at least 2 elements that are numbers
    if (Array.isArray(point) && point.length >= 2 && 
        typeof point[0] === 'number' && typeof point[1] === 'number') {
      return [point[0], point[1]];
    }
    
    // Check for common position-like objects
    if (point && typeof point === 'object') {
      if ('lng' in point && 'lat' in point && 
          typeof point.lng === 'number' && typeof point.lat === 'number') {
        return [point.lng, point.lat];
      }
      
      if ('x' in point && 'y' in point && 
          typeof point.x === 'number' && typeof point.y === 'number') {
        return [point.x, point.y];
      }
    }
    
    return null;
  } catch (error) {
    console.error('Error getting position from route point:', error);
    return null;
  }
} 