/**
 * Declaration file for TravelAnimator component
 */

import { Map } from 'mapbox-gl';
import { Position } from './Position';
import { PointOfInterest as CorePointOfInterest, Destination as CoreDestination } from './POITypes';

// Extend canonical types for local use
export interface PointOfInterest extends CorePointOfInterest {
  type?: string; // for legacy compatibility
  image?: string;
  imageUrl?: string;
  position?: { lat: number; lng: number; name?: string };
}

export interface Destination extends CoreDestination {
  imageUrl?: string;
  position?: { lat: number; lng: number };
}

/**
 * Animation phase for journey
 */
export enum JourneyPhase {
  NOT_STARTED = 'not_started',
  INITIAL_CITY = 'initial_city',
  JOURNEY = 'journey',
  DISCOVERY = 'discovery',
  CITY_APPROACH = 'city_approach',
  COMPLETED = 'completed'
}

/**
 * The main props interface for the TravelAnimator component
 */
export interface TravelAnimatorProps {
  onAnimationComplete?: () => void;
  onProgressUpdate?: (progress: number) => void;
  map?: Map;
  destinations: Destination[];
  allDestinations?: Destination[]; // All available destinations including unselected ones
  route?: Array<[number, number]>;
  isPaused?: boolean;
  onPOIDiscovered?: (poi: PointOfInterest) => void;
  discoveredPOIs?: string[];
  selectedCategories?: string[];
  userInterests?: string[];
  pois?: PointOfInterest[];
  onAnimationEvent?: (event: { type: string; success: boolean; message: string }) => void;
}

/**
 * Button state options
 */
export type ButtonState = 'idle' | 'active' | 'ready' | 'paused' | 'loading' | 'error'; 