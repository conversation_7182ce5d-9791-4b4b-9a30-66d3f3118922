/**
 * Custom type declarations for mapbox-gl to bypass the default import issues
 */

declare module 'mapbox-gl' {
  // Mock Point from '@mapbox/point-geometry'
  class Point {
    x: number;
    y: number;
    
    constructor(x: number, y: number);
    
    clone(): Point;
    add(p: Point): Point;
    sub(p: Point): Point;
    mult(k: number): Point;
    div(k: number): Point;
    rotate(a: number): Point;
    matMult(m: number[]): Point;
    distSqr(p: Point): number;
    dist(p: Point): number;
    mag(): number;
    equals(p: Point): boolean;
    toString(): string;
  }
  
  // LngLat and other core types
  export class LngLat {
    lng: number;
    lat: number;
    
    constructor(lng: number, lat: number);
    
    wrap(): LngLat;
    toArray(): [number, number];
    toString(): string;
    toBounds(radius: number): LngLatBounds;
    static convert(input: LngLatLike): LngLat;
  }

  export class LngLatBounds {
    constructor(sw?: LngLatLike, ne?: LngLatLike);
    
    setNorthEast(ne: LngLatLike): this;
    setSouthWest(sw: LngLatLike): this;
    extend(obj: LngLatLike | LngLatBoundsLike): this;
    getCenter(): LngLat;
    getSouthWest(): LngLat;
    getNorthEast(): LngLat;
    getNorthWest(): LngLat;
    getSouthEast(): LngLat;
    getWest(): number;
    getSouth(): number;
    getEast(): number;
    getNorth(): number;
    toArray(): [[number, number], [number, number]];
    toString(): string;
    isEmpty(): boolean;
    static convert(input: LngLatBoundsLike): LngLatBounds;
  }
  
  // Common mapbox types
  export type LngLatLike = LngLat | [number, number] | { lng: number; lat: number } | { lon: number; lat: number };
  export type LngLatBoundsLike = LngLatBounds | [LngLatLike, LngLatLike] | [number, number, number, number];
  export type PointLike = Point | [number, number];
  
  // Map and marker classes
  export class Map {
    constructor(options: any);
    
    addControl(control: Control, position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'): this;
    removeControl(control: Control): this;
    resize(eventData?: any): this;
    getBounds(): LngLatBounds;
    getCenter(): LngLat;
    getZoom(): number;
    getBearing(): number;
    getPitch(): number;
    getContainer(): HTMLElement;
    getCanvasContainer(): HTMLElement;
    getCanvas(): HTMLCanvasElement;
    getStyle(): any;
    isMoving(): boolean;
    isZooming(): boolean;
    isRotating(): boolean;
    project(lnglat: LngLatLike): Point;
    unproject(point: PointLike): LngLat;
    on(type: string, listener: Function): this;
    off(type: string, listener: Function): this;
    once(type: string, listener: Function): this;
    flyTo(options: any): this;
    easeTo(options: any): this;
    jumpTo(options: any): this;
    setZoom(zoom: number): this;
    setCenter(center: LngLatLike): this;
    setPitch(pitch: number): this;
    setBearing(bearing: number): this;
    setFeatureState(feature: any, state: any): void;
    getFeatureState(feature: any): any;
    triggerRepaint(): void;
    remove(): void;
  }
  
  export class Marker {
    constructor(options?: any);
    
    setLngLat(lnglat: LngLatLike): this;
    getLngLat(): LngLat;
    setPopup(popup?: Popup): this;
    getPopup(): Popup;
    addTo(map: Map): this;
    remove(): this;
    getElement(): HTMLElement;
    setDraggable(draggable: boolean): this;
    isDraggable(): boolean;
    setRotation(rotation: number): this;
    getRotation(): number;
  }
  
  export class Popup {
    constructor(options?: any);
    
    addTo(map: Map): this;
    isOpen(): boolean;
    remove(): this;
    getLngLat(): LngLat;
    setLngLat(lnglat: LngLatLike): this;
    setText(text: string): this;
    setHTML(html: string): this;
    setMaxWidth(maxWidth: string): this;
    setDOMContent(htmlNode: Node): this;
  }
  
  export class Control {
    addTo(map: Map): this;
    remove(): this;
    getDefaultPosition(): string;
  }
  
  export class NavigationControl extends Control {}
  export class GeolocateControl extends Control {}
  export class ScaleControl extends Control {}
  export class FullscreenControl extends Control {}
  
  export class GeoJSONSource {
    setData(data: any): this;
    getClusterExpansionZoom(clusterId: number, callback: Function): void;
    getClusterLeaves(clusterId: number, limit: number, offset: number, callback: Function): void;
  }
  
  // Export mapboxgl namespace for compatibility
  namespace mapboxgl {
    export { Map, LngLat, LngLatBounds, Marker, Popup, Control, NavigationControl, GeolocateControl, ScaleControl, FullscreenControl, GeoJSONSource };
    export type { LngLatLike, LngLatBoundsLike, PointLike };
    
    export let accessToken: string;
    export function setRTLTextPlugin(pluginURL: string, callback: Function, deferred?: boolean): void;
    export function supported(options?: { failIfMajorPerformanceCaveat?: boolean }): boolean;
    export let workerCount: number;
    export function clearStorage(callback?: Function): void;
  }
  
  // Default export for import mapboxgl from 'mapbox-gl'
  export default mapboxgl;
} 