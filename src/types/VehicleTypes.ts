/**
 * Vehicle Types
 * 
 * Type definitions for vehicle markers and styling
 */

import { Position } from './Position';
import { TerrainType } from './TerrainType';

/**
 * Vehicle marker options
 */
export interface VehicleMarkerOptions {
  position: Position;
  bearing?: number;
  elementId?: string;
  className?: string;
  draggable?: boolean;
  style?: Partial<VehicleStyle>;
}

/**
 * Vehicle style properties
 */
export interface VehicleStyle {
  color?: string;
  size?: number;
  opacity?: number;
  borderColor?: string;
  borderWidth?: number;
  shadowColor?: string;
  shadowBlur?: number;
  zIndex?: number;
  icon?: string; // Could be a URL or an identifier for a predefined SVG
  terrain?: TerrainType | string; // Specific terrain type or 'default'
  width?: number; // Optional explicit width
  height?: number; // Optional explicit height
  image?: string; // URL to an image for the vehicle
  display?: 'block' | 'inline-block' | 'none' | string; // Added display property
}

/**
 * Vehicle debug information
 */
export interface VehicleDebugInfo {
  id: string;
  exists: boolean;
  visible: boolean;
  isVisible?: boolean; // Alias for visible, for backward compatibility
  position: Position | null;
  bearing: number;
  elementAttached: boolean;
  domElement: boolean;
  style: Partial<VehicleStyle>;
  timestamp: number;
  lastUpdateDelta: number;
  terrainType: string; // Use string type for terrain to avoid enum reference issues
  terrain?: string; // Additional terrain property for compatibility
}

/**
 * Local vehicle debug info interface for internal use
 */
export interface LocalVehicleDebugInfo extends Omit<VehicleDebugInfo, 'isVisible'> {
  visible: boolean;
}

/**
 * Extended vehicle debug info with additional properties
 */
export interface ExtendedVehicleDebugInfo extends LocalVehicleDebugInfo {
  terrain: string;
  speed?: number;
  smoothingEnabled?: boolean;
}

/**
 * Default vehicle style
 */
export const DEFAULT_VEHICLE_STYLE: VehicleStyle = {
  color: '#3388ff',
  size: 24,
  opacity: 0.9,
  borderColor: '#ffffff',
  borderWidth: 2,
  shadowColor: 'rgba(0, 0, 0, 0.3)',
  shadowBlur: 5,
  zIndex: 5,
  icon: 'car',
  terrain: 'default' // Use string value directly
};

/**
 * Default recovery options
 */
export const DEFAULT_RECOVERY_OPTIONS = {
  maxAttempts: 3,
  interval: 500,
  timeout: 5000
};