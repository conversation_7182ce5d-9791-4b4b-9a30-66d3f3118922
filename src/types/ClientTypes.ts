/**
 * ClientTypes.ts
 * 
 * Type definitions for multi-client architecture
 */

import { Position } from './Position';

/**
 * Client configuration interface
 */
export interface ClientConfig {
  id: string;                 // Unique client identifier
  name: string;               // Display name
  theme: ThemeConfig;         // Visual theming
  mapSettings: MapConfig;     // Map-specific settings
  poiSettings: POIConfig;     // POI-related configurations
  featureFlags: FeatureFlags; // Client-specific feature toggles
}

/**
 * Theme configuration for client-specific styling
 */
export interface ThemeConfig {
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  backgroundColor: string;
  textColor: string;
  fontFamily: string;
  logoUrl: string;
  
  // UI elements theming
  buttons?: {
    primary: {
      background: string;
      text: string;
      hover: string;
      active: string;
    };
    secondary: {
      background: string;
      text: string;
      hover: string;
      active: string;
    };
  };
  
  // Map elements theming
  map?: {
    markerColors: {
      destination: string;
      poi: string;
      selectedPoi: string;
      vehicle: string;
    };
    routeColor: string;
    poiClusterColor: string;
  };
  
  // Animation elements theming
  animation?: {
    countdownColor: string;
    progressBarColor: string;
    vehicleColor: string;
  };
  
  // Overlay elements theming
  overlays?: {
    background: string;
    headerBackground: string;
    borderColor: string;
    tagBackground: string;
    tagTextColor: string;
  };
  
  // Text elements theming
  text?: {
    heading: string;
    paragraph: string;
    muted: string;
    highlight: string;
  };
}

/**
 * Map configuration for client-specific map settings
 */
export interface MapConfig {
  initialBounds: [[number, number], [number, number]];
  defaultCenter: [number, number];
  defaultZoom: number;
  minZoom: number;
  maxZoom: number;
  style: string;              // Mapbox style URL
  padding?: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
}

/**
 * POI configuration for client-specific POI settings
 */
export interface POIConfig {
  categories: POICategory[];
  filters: POIFilter[];
  clustering: ClusteringOptions;
  displayRules: POIDisplayRules;
}

/**
 * POI category definition
 */
export interface POICategory {
  id: string;
  name: string;
  icon: string;
  color: string;
}

/**
 * POI filter definition
 */
export interface POIFilter {
  id: string;
  name: string;
  field: string;
  options: FilterOption[];
}

/**
 * Filter option definition
 */
export interface FilterOption {
  value: string;
  label: string;
}

/**
 * Clustering options for POI display
 */
export interface ClusteringOptions {
  enabled: boolean;
  radius: number;
  maxZoom: number;
  minPoints: number;
}

/**
 * POI display rules for controlling visibility
 */
export interface POIDisplayRules {
  minZoom: number;
  maxDistance: number;
  priorityField: string;
}

/**
 * Feature flags for enabling/disabling client-specific features
 */
export interface FeatureFlags {
  routeAnimation: boolean;
  poiDiscovery: boolean;
  itineraryBuilder: boolean;
  userAccounts: boolean;
  routeSaving: boolean;
  analytics: boolean;
}

/**
 * Client-specific metadata for POIs
 */
export interface ClientMetadata {
  clientId: string;
  priority: number;
  customFields: Record<string, any>;
}