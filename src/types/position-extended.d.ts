// This file is deprecated. All position types are now in Position.ts
// Import from '../types/Position' instead.

/**
 * Extended Position type declarations
 * Contains types referenced in config/paths.ts
 */

import { Position } from './Position';

// Different position type formats used in the application
export type MapPosition = [number, number];
export type GeoPosition = { longitude: number; latitude: number };
export type LatLngPosition = { lng: number; lat: number };

// Type guard functions
export function isPosition(value: unknown): value is Position;
export function isMapPosition(value: unknown): value is MapPosition;
export function isGeoPosition(value: unknown): value is GeoPosition;
export function isLatLngPosition(value: unknown): value is LatLngPosition;
export function isAnyPosition(value: unknown): value is Position | MapPosition | GeoPosition | LatLngPosition;

// Conversion functions
export function toPosition(value: unknown): Position;
export function safeToPosition(value: unknown): Position | null;
export function createPosition(x: number, y: number): Position;
export function safeCreatePosition(x: number | undefined, y: number | undefined): Position | null;

// Transformation functions
export function positionToMapPosition(position: Position): MapPosition;
export function positionToGeoPosition(position: Position): GeoPosition;
export function positionToLatLngPosition(position: Position): LatLngPosition;
export function mapPositionToPosition(mapPosition: MapPosition): Position;
export function geoPositionToPosition(geoPosition: GeoPosition): Position;
export function latLngPositionToPosition(latLngPosition: LatLngPosition): Position; 