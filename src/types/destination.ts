/**
 * destination.ts
 * 
 * Type definitions for Destination data
 */

// import { Position } from './Position'; // Position is also in POITypes.ts usually

// Commenting out local definition to prefer re-export from POITypes
/*
export interface Destination {
  id: string;
  name: string;
  coordinates?: [number, number]; // [longitude, latitude]
  position?: {
    lat: number;
    lng: number;
  };
  image?: string;
  description?: string;
  days?: number;
  budget?: number;
  distance?: string;
  activities?: string[];
  weather?: {
    condition: 'sunny' | 'cloudy' | 'rainy' | 'windy';
    temperature: number;
  };
}

export interface DestinationWithDetails extends Destination {
  details?: {
    culture?: string;
    cuisine?: string;
    attractions?: string[];
    bestTimeToVisit?: string;
  };
}
*/

// Re-export Destination from the canonical source
export type { Destination } from './POITypes';