/**
 * Terrain Types
 * 
 * CANONICAL SOURCE OF TRUTH FOR TERRAIN TYPES
 * 
 * This file defines the terrain types used throughout the application
 * for contextual adaptations like vehicle styling and animation speeds.
 * 
 * All other files should import from this file rather than defining their own terrain types.
 */

/**
 * String union type for different terrain types
 */
export type TerrainType = 'urban' | 'mountain' | 'desert' | 'coastal' | 'standard' | 'city' | 'rural' | 'scenic' | 'highway' | 'forest' | 'suburban' | 'cultural_region' | 'unknown';

/**
 * Static TerrainType object for use in place of an enum
 * This allows for TerrainType.CITY syntax instead of string literals
 */
export const TerrainType = {
  URBAN: 'urban' as TerrainType,
  MOUNTAIN: 'mountain' as TerrainType,
  DESERT: 'desert' as TerrainType,
  COASTAL: 'coastal' as TerrainType,
  STANDARD: 'standard' as TerrainType,
  CITY: 'city' as TerrainType,
  RURAL: 'rural' as TerrainType,
  SCENIC: 'scenic' as TerrainType,
  HIGHWAY: 'highway' as TerrainType,
  FOREST: 'forest' as TerrainType,
  SUBURBAN: 'suburban' as TerrainType,
  CULTURAL_REGION: 'cultural_region' as TerrainType,
  UNKNOWN: 'unknown' as TerrainType,
  DEFAULT: 'standard' as TerrainType // Alias for STANDARD
};

/**
 * Legacy enum for terrain types 
 * Used in older components for backward compatibility
 */
export enum TerrainTypeEnum {
  DEFAULT = 'DEFAULT',
  CITY = 'CITY',
  MOUNTAIN = 'MOUNTAIN',
  DESERT = 'DESERT',
  COASTAL = 'COASTAL',
  RURAL = 'RURAL',
  SCENIC = 'SCENIC',
  HIGHWAY = 'HIGHWAY',
  FOREST = 'FOREST',
  SUBURBAN = 'SUBURBAN',
  CULTURAL_REGION = 'CULTURAL_REGION',
  UNKNOWN = 'UNKNOWN'
}

/**
 * Maps string terrain types to their enum equivalents
 */
export const terrainTypeToEnum: Record<TerrainType, TerrainTypeEnum> = {
  'standard': TerrainTypeEnum.DEFAULT,
  'urban': TerrainTypeEnum.CITY,
  'city': TerrainTypeEnum.CITY,
  'mountain': TerrainTypeEnum.MOUNTAIN,
  'desert': TerrainTypeEnum.DESERT,
  'coastal': TerrainTypeEnum.COASTAL,
  'rural': TerrainTypeEnum.RURAL,
  'scenic': TerrainTypeEnum.SCENIC,
  'highway': TerrainTypeEnum.HIGHWAY,
  'forest': TerrainTypeEnum.FOREST,
  'suburban': TerrainTypeEnum.SUBURBAN,
  'cultural_region': TerrainTypeEnum.CULTURAL_REGION,
  'unknown': TerrainTypeEnum.UNKNOWN
};

/**
 * Maps enum terrain types to their string equivalents
 */
export const enumToTerrainType: Record<TerrainTypeEnum, TerrainType> = {
  [TerrainTypeEnum.DEFAULT]: 'standard',
  [TerrainTypeEnum.CITY]: 'city',
  [TerrainTypeEnum.MOUNTAIN]: 'mountain',
  [TerrainTypeEnum.DESERT]: 'desert',
  [TerrainTypeEnum.COASTAL]: 'coastal',
  [TerrainTypeEnum.RURAL]: 'rural',
  [TerrainTypeEnum.SCENIC]: 'scenic',
  [TerrainTypeEnum.HIGHWAY]: 'highway',
  [TerrainTypeEnum.FOREST]: 'forest',
  [TerrainTypeEnum.SUBURBAN]: 'suburban',
  [TerrainTypeEnum.CULTURAL_REGION]: 'cultural_region',
  [TerrainTypeEnum.UNKNOWN]: 'unknown'
};

/**
 * Animation speed multipliers for different terrain types
 */
export const TERRAIN_SPEED_MULTIPLIERS = {
  DEFAULT: 1.0,
  CITY: 0.5,      // Slower in cities (50% normal speed)
  MOUNTAIN: 0.6,  // Slower in mountains (60% normal speed)
  DESERT: 1.2,    // Faster in deserts (120% normal speed)
  COASTAL: 1.0,   // Default speed on coastal routes
  RURAL: 0.8,     // Slightly slower in rural areas
  SCENIC: 0.3,    // Very slow for scenic areas (30% normal speed)
  HIGHWAY: 1.5,   // Faster on highways (150% normal speed)
  FOREST: 0.7,    // Slower in forests (70% normal speed)
  SUBURBAN: 0.7,  // Slower in suburban areas (70% normal speed)
  CULTURAL_REGION: 0.6, // Speed for cultural regions
  UNKNOWN: 1.0    // Default speed for unknown terrain
}; 