/**
 * Animation Event Types
 * 
 * This file defines the event types and callback signatures used
 * throughout the animation system for communication between components.
 */

/**
 * Base event data interface
 */
export interface EventData {
  timestamp: number;
  type: string;
}

/**
 * Animation events for the system to react to
 */
export enum AnimationEventType {
  ANIMATION_START = 'animation-start',
  ANIMATION_PAUSE = 'animation-pause',
  ANIMATION_RESUME = 'animation-resume',
  ANIMATION_STOP = 'animation-stop',
  ANIMATION_COMPLETE = 'animation-complete',
  ANIMATION_PROGRESS = 'animation-progress',
  ANIMATION_ERROR = 'animation-error',
  
  // Vehicle events
  VEHICLE_MARKER_CREATED = 'vehicle-marker-created',
  VEHICLE_POSITION_UPDATED = 'vehicle-position-updated',
  VEHICLE_MARKER_ERROR = 'vehicle-marker-error',
  VEHICLE_STYLE_CHANGE = 'vehicle-style-change',
  
  // Context events
  POI_DISCOVERED = 'poi-discovered',
  POI_DISCOVERY = 'poi-discovery', // Alias for backward compatibility
  CITY_APPROACHED = 'city-approached',
  CITY_APPROACH = 'city-approach', // Alias for backward compatibility
  TERRAIN_CHANGED = 'terrain-changed',
  
  // Cinematic events
  CINEMATIC_SEQUENCE_START = 'cinematic-sequence-start',
  CINEMATIC_SEQUENCE_END = 'cinematic-sequence-end',
  CINEMATIC_SEQUENCE_STEP = 'cinematic-sequence-step',
  
  // Camera events
  CAMERA_MOVEMENT_START = 'camera-movement-start',
  CAMERA_MOVEMENT_END = 'camera-movement-end',

  // Add these for animation system event bus
  PROGRESS_UPDATE = 'progress-update',
  STATE_CHANGE = 'state-change'
}

/**
 * Animation event callback type
 */
export type AnimationEventCallback = (data: EventData) => void;

/**
 * Interaction event types (alias to AnimationEventType for backward compatibility)
 */
export const InteractionEventType = AnimationEventType;

/**
 * City proximity stages
 */
export enum CityProximityStage {
  NONE = 'NONE',
  APPROACHING = 'APPROACHING',
  ENTERING = 'ENTERING',
  INSIDE = 'INSIDE',
  LEAVING = 'LEAVING'
}

/**
 * Animation event data interface
 */
export interface AnimationEventData extends EventData {
  eventType: AnimationEventType;
  data?: any;
}

/**
 * Event callback type
 */
export type EventCallback<T = AnimationEventData> = (event: T) => void;

/**
 * Discovery event payload interface
 */
export interface DiscoveryEventPayload {
  poi: {
    id: string;
    name: string;
    position: [number, number];
  };
  distance: number;
  timestamp: number;
  position: [number, number];
}

/**
 * City drive-by event payload interface
 */
export interface CityDriveByPayload {
  nearbyCity: {
    name: string;
  };
  distance: number;
  stage: CityProximityStage;
  pois: Array<{
    id: string;
    name: string;
    position: [number, number];
  }>;
  timestamp: number;
  position: [number, number];
}

/**
 * Discovery event interface
 */
export interface DiscoveryEvent extends AnimationEventData {
  eventType: AnimationEventType.POI_DISCOVERY | AnimationEventType.POI_DISCOVERED;
  data: DiscoveryEventPayload;
  payload?: DiscoveryEventPayload; // Alternative name for data for backward compatibility
}

/**
 * City drive-by event interface
 */
export interface CityDriveByEvent extends AnimationEventData {
  eventType: AnimationEventType.CITY_APPROACH | AnimationEventType.CITY_APPROACHED;
  data: CityDriveByPayload;
  city?: any; // City property for backward compatibility
  distanceToCity?: number; // Distance property for backward compatibility
} 