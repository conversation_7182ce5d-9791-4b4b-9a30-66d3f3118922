/**
 * poi.ts
 *
 * UNIFIED POI TYPES - SINGLE SOURCE OF TRUTH
 * Consolidates POITypes.ts, POITypes.updated.ts, and related files
 */

import { Position, PositionObject, isPositionObject } from './Position';

// ========================================
// POI CATEGORIES
// ========================================

/**
 * POI Category type - comprehensive list for discovery/exploration
 */
export type POICategory =
  | 'landmark'           // Historic monuments, iconic sites
  | 'nature'            // Natural wonders, viewpoints, geological formations
  | 'cultural'          // Museums, traditional villages, artisan workshops
  | 'adventure'         // Hiking trails, outdoor activities, sports
  | 'scenic'            // Scenic routes, panoramic viewpoints, photo spots
  | 'hidden-gem'        // Off-the-beaten-path discoveries
  | 'photography'       // Instagram-worthy locations, sunset points
  | 'local-experience'  // Markets, festivals, authentic encounters
  | 'architecture'      // Historic buildings, unique structures
  | 'viewpoint'         // Observation decks, mountain passes
  | 'activity'          // General activities and experiences
  | 'accommodation'     // Hotels, riads, guesthouses
  | 'dining'           // Restaurants, cafes, food experiences
  | 'shopping'         // Markets, souks, artisan shops
  | 'transport'        // Transport hubs, stations
  | 'other';

// ========================================
// CORE POI INTERFACE
// ========================================

/**
 * Point of Interest (POI) interface - unified definition
 */
export interface PointOfInterest {
  id: string;
  name: string;
  description: string;
  position: PositionObject;
  coordinates: [number, number]; // Always required for map display
  category: POICategory;
  images: string[]; // Always present, can be empty
  tags: string[]; // Always present, can be empty

  // Optional metadata
  rating?: number;
  reviews?: number;
  priceLevel?: number;
  openingHours?: string;
  website?: string;
  phone?: string;
  address?: string;
  location?: string;
  duration?: number; // Recommended visit duration in minutes
  cost?: number; // Entry cost

  // Multi-client support
  clientId?: string;
  region?: string;
  country?: string;
  city?: string;

  // Discovery metadata
  importance?: number; // 1-10 scale for discovery priority
  accessibility?: string;
  bestTimeToVisit?: string;
  seasonality?: string[];

  // Additional properties
  properties?: POIProperties;
}

/**
 * Destination interface - represents a city or major location
 */
export interface Destination {
  id: string;
  name: string;
  position: PositionObject;
  coordinates: [number, number]; // Always required
  images: string[]; // Always present, can be empty
  tags: string[]; // Always present, can be empty

  // Optional metadata
  description?: string;
  rating?: number;
  reviews?: number;
  priceLevel?: number;
  openingHours?: string;
  website?: string;
  phone?: string;
  address?: string;

  // Travel-specific information
  visitDuration?: number; // Recommended stay in days
  bestTimeToVisit?: string;
  travelTime?: number; // Travel time from previous destination
  distance?: number; // Distance from previous destination
  isHighlight?: boolean; // Featured destination

  // Multi-client support
  clientId?: string;
  region?: string;
  country?: string;
}

/**
 * POI Properties interface for additional data
 */
export interface POIProperties {
  address?: string;
  website?: string;
  phone?: string;
  openingHours?: string;
  accessibility?: string;
  entranceFee?: number;
  restrictions?: string[];
  amenities?: string[];
  reviews?: POIReview[];
  socialMedia?: {
    instagram?: string;
    facebook?: string;
    twitter?: string;
  };
}

/**
 * POI Review interface
 */
export interface POIReview {
  id: string;
  author: string;
  rating: number;
  comment: string;
  date: string;
  helpful?: number;
}

// ========================================
// NORMALIZATION UTILITIES
// ========================================

/**
 * Normalize POI data to ensure type compliance
 */
export function normalizePOI(poi: any): PointOfInterest {
  let coordinates: [number, number];
  let positionObj: PositionObject;

  // Handle coordinates
  if (Array.isArray(poi.coordinates) && poi.coordinates.length >= 2) {
    coordinates = [Number(poi.coordinates[0]), Number(poi.coordinates[1])];
  } else if (Array.isArray(poi.position) && poi.position.length >= 2) {
    coordinates = [Number(poi.position[0]), Number(poi.position[1])];
  } else if (poi.lng !== undefined && poi.lat !== undefined) {
    coordinates = [Number(poi.lng), Number(poi.lat)];
  } else if (poi.longitude !== undefined && poi.latitude !== undefined) {
    coordinates = [Number(poi.longitude), Number(poi.latitude)];
  } else {
    throw new Error(`Invalid POI coordinates for ${poi.id || 'unknown'}`);
  }

  // Handle position object
  if (isPositionObject(poi.position)) {
    positionObj = poi.position;
  } else {
    positionObj = {
      lng: coordinates[0],
      lat: coordinates[1]
    };
  }

  // Handle images
  const finalImages = Array.isArray(poi.images) ? poi.images : [];

  return {
    id: String(poi.id),
    name: String(poi.name),
    description: String(poi.description || ''),
    position: positionObj,
    coordinates,
    category: (poi.category as POICategory) || mapStringToPOICategory(poi.type as string) || 'other',
    images: finalImages,
    rating: typeof poi.rating === 'number' ? poi.rating : undefined,
    reviews: typeof poi.reviews === 'number' ? poi.reviews : undefined,
    priceLevel: typeof poi.priceLevel === 'number' ? poi.priceLevel : undefined,
    openingHours: typeof poi.openingHours === 'string' ? poi.openingHours : undefined,
    website: typeof poi.website === 'string' ? poi.website : undefined,
    phone: typeof poi.phone === 'string' ? poi.phone : undefined,
    address: typeof poi.address === 'string' ? poi.address : undefined,
    location: typeof poi.location === 'string' ? poi.location : undefined,
    tags: Array.isArray(poi.tags) ? poi.tags : [],
    duration: typeof poi.duration === 'number' ? poi.duration : undefined,
    cost: typeof poi.cost === 'number' ? poi.cost : undefined,
    clientId: typeof poi.clientId === 'string' ? poi.clientId : undefined,
    region: typeof poi.region === 'string' ? poi.region : undefined,
    country: typeof poi.country === 'string' ? poi.country : undefined,
    city: typeof poi.city === 'string' ? poi.city : undefined,
    importance: typeof poi.importance === 'number' ? poi.importance : undefined,
    accessibility: typeof poi.accessibility === 'string' ? poi.accessibility : undefined,
    bestTimeToVisit: typeof poi.bestTimeToVisit === 'string' ? poi.bestTimeToVisit : undefined,
    seasonality: Array.isArray(poi.seasonality) ? poi.seasonality : undefined,
    properties: poi.properties || undefined
  };
}

/**
 * Normalize destination data
 */
export function normalizeDestination(dest: any): Destination {
  let coordinates: [number, number];
  let positionObj: PositionObject;

  // Handle coordinates (same logic as POI)
  if (Array.isArray(dest.coordinates) && dest.coordinates.length >= 2) {
    coordinates = [Number(dest.coordinates[0]), Number(dest.coordinates[1])];
  } else if (Array.isArray(dest.position) && dest.position.length >= 2) {
    coordinates = [Number(dest.position[0]), Number(dest.position[1])];
  } else if (dest.lng !== undefined && dest.lat !== undefined) {
    coordinates = [Number(dest.lng), Number(dest.lat)];
  } else if (dest.longitude !== undefined && dest.latitude !== undefined) {
    coordinates = [Number(dest.longitude), Number(dest.latitude)];
  } else {
    throw new Error(`Invalid destination coordinates for ${dest.id || 'unknown'}`);
  }

  // Handle position object
  if (isPositionObject(dest.position)) {
    positionObj = dest.position;
  } else {
    positionObj = {
      lng: coordinates[0],
      lat: coordinates[1]
    };
  }

  return {
    id: String(dest.id),
    name: String(dest.name),
    position: positionObj,
    coordinates,
    images: Array.isArray(dest.images) ? dest.images : [],
    tags: Array.isArray(dest.tags) ? dest.tags : [],
    description: typeof dest.description === 'string' ? dest.description : undefined,
    rating: typeof dest.rating === 'number' ? dest.rating : undefined,
    reviews: typeof dest.reviews === 'number' ? dest.reviews : undefined,
    priceLevel: typeof dest.priceLevel === 'number' ? dest.priceLevel : undefined,
    openingHours: typeof dest.openingHours === 'string' ? dest.openingHours : undefined,
    website: typeof dest.website === 'string' ? dest.website : undefined,
    phone: typeof dest.phone === 'string' ? dest.phone : undefined,
    address: typeof dest.address === 'string' ? dest.address : undefined,
    visitDuration: typeof dest.visitDuration === 'number' ? dest.visitDuration : undefined,
    bestTimeToVisit: typeof dest.bestTimeToVisit === 'string' ? dest.bestTimeToVisit : undefined,
    travelTime: typeof dest.travelTime === 'number' ? dest.travelTime : undefined,
    distance: typeof dest.distance === 'number' ? dest.distance : undefined,
    isHighlight: typeof dest.isHighlight === 'boolean' ? dest.isHighlight : undefined,
    clientId: typeof dest.clientId === 'string' ? dest.clientId : undefined,
    region: typeof dest.region === 'string' ? dest.region : undefined,
    country: typeof dest.country === 'string' ? dest.country : undefined
  };
}

/**
 * Map string type to POI category
 */
function mapStringToPOICategory(type: string): POICategory | null {
  if (!type) return null;

  const typeMap: Record<string, POICategory> = {
    'monument': 'landmark',
    'museum': 'cultural',
    'park': 'nature',
    'restaurant': 'dining',
    'hotel': 'accommodation',
    'shop': 'shopping',
    'market': 'local-experience',
    'mosque': 'cultural',
    'palace': 'architecture',
    'garden': 'nature',
    'beach': 'nature',
    'mountain': 'nature',
    'desert': 'nature',
    'oasis': 'hidden-gem',
    'kasbah': 'architecture',
    'medina': 'cultural',
    'souk': 'shopping',
    'riad': 'accommodation',
    'hammam': 'local-experience',
    'viewpoint': 'viewpoint',
    'photo': 'photography',
    'instagram': 'photography',
    'hike': 'adventure',
    'trek': 'adventure',
    'climb': 'adventure'
  };

  return typeMap[type.toLowerCase()] || null;
}

// ========================================
// TYPE GUARDS
// ========================================

/**
 * Type guard for PointOfInterest
 */
export function isPointOfInterest(value: any): value is PointOfInterest {
  return (
    value &&
    typeof value === 'object' &&
    typeof value.id === 'string' &&
    typeof value.name === 'string' &&
    Array.isArray(value.coordinates) &&
    value.coordinates.length === 2 &&
    typeof value.coordinates[0] === 'number' &&
    typeof value.coordinates[1] === 'number'
  );
}

/**
 * Type guard for Destination
 */
export function isDestination(value: any): value is Destination {
  return (
    value &&
    typeof value === 'object' &&
    typeof value.id === 'string' &&
    typeof value.name === 'string' &&
    Array.isArray(value.coordinates) &&
    value.coordinates.length === 2 &&
    typeof value.coordinates[0] === 'number' &&
    typeof value.coordinates[1] === 'number'
  );
}

// ========================================
// UTILITY FUNCTIONS
// ========================================

/**
 * Get POI categories as array
 */
export function getPOICategories(): POICategory[] {
  return [
    'landmark', 'nature', 'cultural', 'adventure', 'scenic', 'hidden-gem',
    'photography', 'local-experience', 'architecture', 'viewpoint', 'activity',
    'accommodation', 'dining', 'shopping', 'transport', 'other'
  ];
}

/**
 * Get category display name
 */
export function getCategoryDisplayName(category: POICategory): string {
  const displayNames: Record<POICategory, string> = {
    'landmark': 'Landmarks',
    'nature': 'Nature',
    'cultural': 'Cultural',
    'adventure': 'Adventure',
    'scenic': 'Scenic Views',
    'hidden-gem': 'Hidden Gems',
    'photography': 'Photography',
    'local-experience': 'Local Experiences',
    'architecture': 'Architecture',
    'viewpoint': 'Viewpoints',
    'activity': 'Activities',
    'accommodation': 'Accommodation',
    'dining': 'Dining',
    'shopping': 'Shopping',
    'transport': 'Transport',
    'other': 'Other'
  };

  return displayNames[category] || category;
}

/**
 * Validate coordinates array
 */
export function isValidCoordinates(coordinates: [number, number]): boolean {
  return (
    Array.isArray(coordinates) &&
    coordinates.length === 2 &&
    typeof coordinates[0] === 'number' &&
    typeof coordinates[1] === 'number' &&
    !isNaN(coordinates[0]) &&
    !isNaN(coordinates[1]) &&
    coordinates[0] >= -180 &&
    coordinates[0] <= 180 &&
    coordinates[1] >= -90 &&
    coordinates[1] <= 90
  );
}