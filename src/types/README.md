# Type System Architecture

This directory contains type definitions used throughout the application. It follows a specific organization to maintain type safety, avoid circular dependencies, and provide a clear structure for imports.

## Core Type Files

### Single Source of Truth Files

These files are the canonical sources for their respective type domains:

- **`Position.ts`**: Geographic position types and utilities
- **`TerrainType.ts`**: Terrain type definitions and terrain-related constants
- **`AnimationEventTypes.ts`**: Event types and event data interfaces for the animation system
- **`VehicleTypes.ts`**: Vehicle-related types for markers and styling

### Aggregator Files

These files import and re-export types from multiple files to provide consolidated import points:

- **`AnimationTypes.ts`**: Main entry point for animation-related types
- **`CoreAnimationTypes.ts`**: Basic animation state and configuration types

## Import Guidelines

1. **Prefer the aggregator files** when importing multiple related types:
   ```typescript
   // Good
   import { AnimationState, AnimationConfig, TerrainType } from '../types/AnimationTypes';
   
   // Avoid multiple imports
   import { AnimationState, AnimationConfig } from '../types/CoreAnimationTypes';
   import { TerrainType } from '../types/TerrainType';
   ```

2. **Go directly to the source** when importing only one or two types:
   ```typescript
   // Good for single type
   import { Position } from '../types/Position';
   ```

3. **Avoid circular dependencies** by using aggregator files at the right level.

## Declaration Files

TypeScript declaration files (`.d.ts`) serve specific purposes:

- **`mapbox-declarations.d.ts`**: Enhances Mapbox types for better IDE support
- **`module-declarations.d.ts`**: Provides type support for external modules
- **`custom-declarations.d.ts`**: Declares custom global types

## Type Evolution Strategy

When extending or modifying types:

1. **Maintain backward compatibility** by using interfaces that extend existing ones
2. **Use type re-exports** for renamed or relocated types
3. **Add JSDoc comments** to deprecated types and properties
4. **Update aggregator files** when adding new canonical source files

## Architecture Decisions

- **Interface vs Type**: Prefer `interface` for object types that might be extended, `type` for unions and primitives
- **Enum vs String Literal Types**: Use string literal union types with companion objects for better type safety
- **Import Path Aliases**: Use path aliases (`@/types/...`) when configured in tsconfig

## Type Validation and Guards

Prefer creating type guards when working with complex types:

```typescript
// Example from Position.ts
export function isPosition(value: unknown): value is Position {
  return (
    Array.isArray(value) &&
    value.length === 2 &&
    typeof value[0] === 'number' &&
    typeof value[1] === 'number'
  );
}
```

## Module Declarations

When adding module declarations for third-party libraries:

1. Check if types are already available from DefinitelyTyped (`@types/...`)
2. Add minimal declarations for just what you need
3. Place module declarations in appropriate `.d.ts` files

# Types Guide

## Overview

This project contains multiple type definitions for similar concepts, which can sometimes lead to compatibility issues. This guide outlines how to properly use the types and avoid common problems.

## Position Types

The application uses several position types:

- `Position` from `Position.ts`: A tuple of `[number, number]` (longitude, latitude)
- `MapPosition` from `Position.ts`: An object with `{ lng: number, lat: number }`
- `GeoPosition` from `Position.ts`: An object with `{ longitude: number, latitude: number }`
- `LatLngPosition` from `Position.ts`: An object with `{ lat: number, lng: number }`

### Best Practices

1. **Always use the standard `Position` type ([lng, lat] tuple) for internal calculations**
2. Use the conversion utilities provided in `Position.ts`:
   - `toPosition`: Converts any position format to the standard Position tuple
   - `positionToMapPosition`, `positionToGeoPosition`, etc.: Convert between formats
3. Import position types from the central path alias:
   ```typescript
   import { Position, MapPosition, toPosition } from '@config/paths';
   ```

## Point of Interest Types

We have two separate POI types:

- `PointOfInterest` from `POITypes.ts`: Uses `position: Position | MapPosition`
- `PointOfInterest` from `poi.ts`: Uses `coordinates: Position`

### How to Handle Type Compatibility

1. **Prefer importing from the central path export**:
   ```typescript
   // For POITypes.ts version
   import { PointOfInterest } from '@config/paths';
   // For poi.ts version
   import { POIBase } from '@config/paths';
   ```

2. **Use the conversion utility**:
   ```typescript
   import { convertPoiTypes } from '@config/paths';
   
   // Convert between types
   const convertedPoi = convertPoiTypes(originalPoi);
   ```

3. **When working with both types, use type guards**:
   ```typescript
   function handlePoi(poi: any) {
     const hasPosition = 'position' in poi;
     const hasCoordinates = 'coordinates' in poi;
     
     if (hasPosition) {
       // Handle POITypes.PointOfInterest
     } else if (hasCoordinates) {
       // Handle poi.PointOfInterest
     }
   }
   ```

## Common Error Scenarios and Solutions

### "Property 'position' does not exist on type 'PointOfInterest'"

This usually means you're importing `PointOfInterest` from `poi.ts` but trying to access the `position` property from `POITypes.ts`.

Solution:
```typescript
import { PointOfInterest, POIBase, convertPoiTypes } from '@config/paths';

// If you're not sure which type you have:
const poi = convertPoiTypes(originalPoi);
```

### "Property 'coordinates' does not exist on type 'PointOfInterest'"

This means you're importing `PointOfInterest` from `POITypes.ts` but trying to access the `coordinates` property from `poi.ts`.

Solution: Similar to above, use the conversion utility.

## Technical Configuration Changes

In our vite.config.ts, we've configured path aliases to make imports more consistent:

- `@` points to `./src`
- `@types` points to `./src/types`
- `@config` points to `./src/config`

Always prefer these aliases over relative imports to ensure consistent type resolution. 