/**
 * Type declarations for @/utils/mapbox-imports
 */

declare module '@/utils/mapbox-imports' {
  import * as _mapboxgl from 'mapbox-gl';
  
  // Export the namespace with the same structure as real mapboxgl
  const mapboxgl: typeof _mapboxgl & {
    Map: typeof _mapboxgl.Map;
    Marker: typeof _mapboxgl.Marker;
    LngLat: typeof _mapboxgl.LngLat;
    LngLatBounds: typeof _mapboxgl.LngLatBounds;
    GeoJSONSource: typeof _mapboxgl.GeoJSONSource;
    NavigationControl: typeof _mapboxgl.NavigationControl;
    ScaleControl: typeof _mapboxgl.ScaleControl;
    GeolocateControl: typeof _mapboxgl.GeolocateControl;
    Popup: typeof _mapboxgl.Popup;
  };

  // Utility functions
  export function createMap(options: any): mapboxgl.Map;
  export function destroyMap(map: mapboxgl.Map): void;
  
  // Default export
  export default mapboxgl;
} 