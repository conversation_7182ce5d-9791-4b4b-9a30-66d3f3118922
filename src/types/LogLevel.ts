/**
 * LogLevel.ts
 * 
 * Centralized definition of log levels used throughout the application.
 * This is the single source of truth for log level types.
 */

export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'performance';

// Ordered log levels from least to most severe
export const LOG_LEVELS: LogLevel[] = ['debug', 'info', 'warn', 'error', 'performance'];

// Helper to check if a log level is valid
export const isValidLogLevel = (level: string): level is LogLevel => {
  return LOG_LEVELS.includes(level as LogLevel);
};

// Helper to get numeric severity of a log level (higher = more severe)
export const getLogLevelSeverity = (level: LogLevel): number => {
  return LOG_LEVELS.indexOf(level);
}; 