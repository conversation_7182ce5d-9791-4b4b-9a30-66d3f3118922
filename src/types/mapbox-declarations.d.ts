/**
 * Mapbox GL and related type declarations
 * This file provides TypeScript declarations for mapbox-gl and related modules to resolve import errors
 */

/**
 * Declarations for @mapbox/point-geometry
 * Resolves the 'export =' issue by providing a class declaration
 */
declare module '@mapbox/point-geometry' {
  export default class Point {
    x: number;
    y: number;
    constructor(x: number, y: number);
    clone(): Point;
    add(point: Point): Point;
    sub(point: Point): Point;
    mult(factor: number): Point;
    div(factor: number): Point;
    rotate(angle: number): Point;
    rotateAround(angle: number, center: Point): Point;
    matMult(matrix: [number, number, number, number]): Point;
    unit(): Point;
    perp(): Point;
    round(): Point;
    mag(): number;
    equals(point: Point): boolean;
    dist(point: Point): number;
    distSqr(point: Point): number;
    angle(): number;
    angleTo(point: Point): number;
    angleWidth(point: Point): number;
    angleWithSep(x: number, y: number): number;
    convert(a: any): Point;
  }
}

/**
 * Declarations for mapbox-gl
 * Re-exports all types from the module to resolve TypeScript import issues
 */
declare module 'mapbox-gl' {
  import Point from '@mapbox/point-geometry';
  import { Position } from './Position';

  // Export everything from the mapbox-gl module
  export * from 'mapbox-gl';

  // Explicitly re-export key types that might be used in imports
  export type MapboxOptions = any;
  export type MapMouseEvent = any;
  export type MapLayerMouseEvent = any;
  export type AnyLayer = any;
  export type AnySourceData = any;
  export type LngLat = any;
  export type LngLatLike = any;
  export type LngLatBounds = any;
  export type Map = any;
  export type Marker = any;
  export type Popup = any;
  export type CameraOptions = any;
  export type AnimationOptions = any;
  export type EventData = any;
  export type MapboxEvent = any;
  export type PointLike = any;
  export type Expression = any;
  export type StyleSpecification = any;
  export type GeoJSONSource = any;
  export type GeoJSONSourceRaw = any;
  export type ImageSource = any;
  export type VideoSource = any;
  export type CanvasSource = any;
  export type Layer = any;
  export type CustomLayerInterface = any;
  export type FillExtrusionPaint = any;
  export type FillPaint = any;
  export type LinePaint = any;
  export type CirclePaint = any;
  export type SymbolPaint = any;
  export type RasterPaint = any;
  export type BackgroundPaint = any;
  export type FillLayout = any;
  export type LineLayout = any;
  export type CircleLayout = any;
  export type SymbolLayout = any;
  export type RasterLayout = any;
  export type BackgroundLayout = any;
  export type TransitionOptions = any;
  export type IControl = any;
  export type NavigationControl = any;
  export type GeolocateControl = any;
  export type AttributionControl = any;
  export type ScaleControl = any;
  export type FullscreenControl = any;
  export type Bounds = any;
  export type MapboxGeoJSONFeature = any;
  export type FitBoundsOptions = any;
  export type PaddingOptions = any;
  export type RequestParameters = any;
  export type RequestTransformFunction = any;
  export type ResponseCallback = any;
  export type Cancelable = any;
  export type VectorSource = any;
  export type VectorSourceImpl = any;
  export type RasterSource = any;
  export type RasterSourceImpl = any;
  export type ElevationData = any;
  export type TerrainSpecification = any;
  export type SkyType = any;
  export type SkyPaint = any;
  export type SkyLayer = any;
  export type Fog = any;

  // Define default export as the mapboxgl namespace
  const mapboxgl: any;
  export default mapboxgl;
}

/**
 * Declarations for Position module with additional utility functions
 * Exact path match for imports from '@/types/Position'
 */
declare module '../../../types/Position' {
  export * from './Position';
}

// Also provide a version for absolute imports
declare module '@/types/Position' {
  export * from './Position';
}

/**
 * Declarations for TerrainType module with enum extensions
 * Exact path match for imports from '@/types/TerrainType'
 */
declare module '../../../types/TerrainType' {
  export type TerrainType = 'urban' | 'mountain' | 'desert' | 'coastal' | 'standard' | 'city' | 'rural' | 'scenic' | 'highway';
  
  export enum TerrainTypeEnum {
    DEFAULT = 'DEFAULT',
    CITY = 'CITY',
    MOUNTAIN = 'MOUNTAIN',
    DESERT = 'DESERT',
    COASTAL = 'COASTAL',
    RURAL = 'RURAL',
    SCENIC = 'SCENIC',
    HIGHWAY = 'HIGHWAY'
  }
  
  // Adding static properties to the TerrainType type
  export const TerrainType: {
    DEFAULT: TerrainType;
    CITY: TerrainType;
    MOUNTAIN: TerrainType;
    DESERT: TerrainType;
    COASTAL: TerrainType;
    RURAL: TerrainType;
    SCENIC: TerrainType;
    HIGHWAY: TerrainType;
  };
  
  export const terrainTypeToEnum: Record<TerrainType, TerrainTypeEnum>;
  export const enumToTerrainType: Record<TerrainTypeEnum, TerrainType>;
}

// Also provide a version for absolute imports
declare module '@/types/TerrainType' {
  export * from './TerrainType';
}

/**
 * Declarations for AnimationEventTypes with additional event types
 * Exact path match for imports from '@/types/AnimationEventTypes'
 */
declare module '../../../types/AnimationEventTypes' {
  export interface EventData {
    timestamp: number;
    type: string;
  }
  
  export enum AnimationEventType {
    // Core animation events
    INITIALIZED = 'initialized',
    START = 'start',
    PAUSE = 'pause',
    RESUME = 'resume',
    STOP = 'stop',
    COMPLETE = 'complete',
    RESET = 'reset',
    ERROR = 'error',
    
    // Position and progress events
    POSITION_CHANGE = 'positionChange',
    PROGRESS_UPDATE = 'progressUpdate',
    BEARING_CHANGE = 'bearingChange',
    
    // Context events
    POI_APPROACH = 'poiApproach',
    POI_DISCOVERY = 'poiDiscovery',
    CITY_APPROACH = 'cityApproach',
    TERRAIN_CHANGE = 'terrainChange',
    
    // State events
    STATE_CHANGE = 'stateChange',
    SPEED_CHANGE = 'speedChange',
    
    // Camera events
    CAMERA_MOVE = 'cameraMove',
    CAMERA_MODE_CHANGE = 'cameraModeChange',
    
    // Vehicle events
    VEHICLE_VISIBLE = 'vehicleVisible',
    VEHICLE_HIDDEN = 'vehicleHidden',
    VEHICLE_STYLE_CHANGE = 'vehicleStyleChange',
    VEHICLE_VISIBILITY_CHANGE = 'vehicleVisibilityChange',
    VEHICLE_POSITION_CHANGE = 'vehiclePositionChange',
    VEHICLE_MARKER_CREATED = 'vehicleMarkerCreated',
    
    // UI events
    UI_INTERACTION = 'uiInteraction',
    
    // Additional events referenced in code
    ANIMATION_START = 'animationStart',
    ANIMATION_PAUSE = 'animationPause',
    ANIMATION_RESUME = 'animationResume',
    ANIMATION_COMPLETE = 'animationComplete',
    ANIMATION_ERROR = 'animationError',
    ANIMATION_PROGRESS = 'animationProgress'
  }
  
  export type AnimationEventCallback = (data: EventData) => void;
  
  // Additional types referenced in imports
  export enum InteractionEventType {
    // Core animation events
    INITIALIZED = 'initialized',
    START = 'start',
    PAUSE = 'pause',
    RESUME = 'resume',
    STOP = 'stop',
    COMPLETE = 'complete',
    RESET = 'reset',
    ERROR = 'error',
    
    // Position and progress events
    POSITION_CHANGE = 'positionChange',
    PROGRESS_UPDATE = 'progressUpdate',
    BEARING_CHANGE = 'bearingChange',
    
    // Context events
    POI_APPROACH = 'poiApproach',
    POI_DISCOVERY = 'poiDiscovery',
    CITY_APPROACH = 'cityApproach',
    TERRAIN_CHANGE = 'terrainChange',
    
    // State events
    STATE_CHANGE = 'stateChange',
    SPEED_CHANGE = 'speedChange',
    
    // Camera events
    CAMERA_MOVE = 'cameraMove',
    CAMERA_MODE_CHANGE = 'cameraModeChange',
    
    // Vehicle events
    VEHICLE_VISIBLE = 'vehicleVisible',
    VEHICLE_HIDDEN = 'vehicleHidden',
    VEHICLE_STYLE_CHANGE = 'vehicleStyleChange',
    VEHICLE_VISIBILITY_CHANGE = 'vehicleVisibilityChange',
    VEHICLE_POSITION_CHANGE = 'vehiclePositionChange',
    VEHICLE_MARKER_CREATED = 'vehicleMarkerCreated',
    
    // UI events
    UI_INTERACTION = 'uiInteraction',
    
    // Additional events referenced in code
    ANIMATION_START = 'animationStart',
    ANIMATION_PAUSE = 'animationPause',
    ANIMATION_RESUME = 'animationResume',
    ANIMATION_COMPLETE = 'animationComplete',
    ANIMATION_ERROR = 'animationError',
    ANIMATION_PROGRESS = 'animationProgress'
  }
  
  export enum CityProximityStage {
    APPROACHING = 'approaching',
    PASSING = 'passing',
    PASSED = 'passed',
    NONE = 'none'
  }
  
  export interface AnimationEventData extends EventData {
    eventType: AnimationEventType;
    data?: any;
  }
  
  export type EventCallback = (event: AnimationEventData) => void;
  
  export interface DiscoveryEventPayload {
    poi: {
      id: string;
      name: string;
      position: [number, number];
    };
    distance: number;
    timestamp: number;
    position: [number, number];
  }
  
  export interface CityDriveByPayload {
    nearbyCity: {
      name: string;
    };
    distance: number;
    stage: CityProximityStage;
    pois: Array<{
      id: string;
      name: string;
      position: [number, number];
    }>;
    timestamp: number;
    position: [number, number];
  }
  
  export interface DiscoveryEvent extends AnimationEventData {
    eventType: AnimationEventType.POI_DISCOVERY;
    data: DiscoveryEventPayload;
  }
  
  export interface CityDriveByEvent extends AnimationEventData {
    eventType: AnimationEventType.CITY_APPROACH;
    data: CityDriveByPayload;
  }
}

// Also provide a version for absolute imports
declare module '@/types/AnimationEventTypes' {
  export * from '@/types/AnimationEventTypes';
}

/**
 * Declarations for CoreAnimationTypes
 * Exact path match for imports from '@/types/CoreAnimationTypes'
 */
declare module '../../../types/CoreAnimationTypes' {
  export interface AnimationConfig {
    routePoints: [number, number][];
    duration?: number;
    loop?: boolean;
    onProgress: (progress: number, position?: [number, number], bearing?: number) => void;
    onComplete: () => void;
  }
  
  export const ANIMATION_SPEEDS: {
    DEFAULT: number;
    CITY: number;
    MOUNTAIN: number;
    DESERT: number;
    COASTAL: number;
    RURAL: number;
    SCENIC: number;
    HIGHWAY: number;
  };
}

// Also provide a version for absolute imports
declare module '@/types/CoreAnimationTypes' {
  export * from '@/types/CoreAnimationTypes';
}

/**
 * Declarations for VehicleTypes
 * Exact path match for imports from '@/types/VehicleTypes'
 */
declare module '../../../types/VehicleTypes' {
  export interface VehicleDebugInfo {
    exists: boolean;
    visible: boolean;
    isVisible?: boolean; // Alias for visible, used in some components
    position?: [number, number];
    bearing?: number;
    lastUpdated: number;
    elementId: string;
    terrain?: string; // Added for extended vehicle debug info
  }
  
  export interface LocalVehicleDebugInfo extends VehicleDebugInfo {
    visible: boolean;
  }
  
  export interface ExtendedVehicleDebugInfo extends LocalVehicleDebugInfo {
    terrain?: string;
    visible: boolean; // Making visible required to satisfy the extension
  }
}

// Also provide a version for absolute imports
declare module '@/types/VehicleTypes' {
  export * from '@/types/VehicleTypes';
}

/**
 * Declarations for the RoutePoint interface
 * Exact path match for imports from '@/types/RoutePoint'
 */
declare module '../../../types/RoutePoint' {
  import { Position } from '@/types/Position';
  import { TerrainType } from '@/types/TerrainType';

  /**
   * Detailed RoutePoint interface with full metadata
   */
  export interface RoutePoint {
    position: Position;
    distance: number;
    bearing: number;
    terrain?: TerrainType;
    elevation?: number;
    nearestLocation?: string;
    pois?: {
      id: string;
      distance: number;
    }[];
    index: number;
    metadata?: Record<string, any>;
  }
}

// Also provide a version for absolute imports
declare module '@/types/RoutePoint' {
  export * from '@/types/RoutePoint';
}

/**
 * Declarations for the simple RoutePoint interface from TravelAnimatorTypes
 * Exact path match for imports from '@/types/TravelAnimatorTypes'
 */
declare module '../../../types/TravelAnimatorTypes' {
  export interface RoutePoint {
    lat: number;
    lng: number;
  }
  
  export const VEHICLE_MARKER_ID: string;
  export const DIRECT_VEHICLE_MARKER_ID: string;
  export const BASE_ANIMATION_DURATION_MS: number;
  
  export interface TravelAnimatorProps {
    onAnimationComplete?: () => void;
    onProgressUpdate?: (progress: number) => void;
    map?: any;
    destinations: any[];
    allDestinations?: any[];
    route?: Array<[number, number]>;
    isPaused?: boolean;
    onPOIDiscovered?: (poi: any) => void;
    discoveredPOIs?: string[];
    selectedCategories?: string[];
    userInterests?: string[];
    pois?: any[];
    onAnimationEvent?: (event: { type: string; success: boolean; message: string }) => void;
  }
  
  export type ButtonState = 'idle' | 'ready' | 'loading' | 'error';
  export type LocalJourneyPhase = 'not_started' | 'initial_city' | 'selecting_pois' | 'ready_to_start' 
                          | 'journey' | 'approaching_city' | 'new_city' | 'completed' | 'idle';
  
  export interface AnimationConfig {
    routePoints: [number, number][];
    duration?: number;
    loop?: boolean;
    onProgress: (progress: number, position?: [number, number], bearing?: number) => void;
    onComplete: () => void;
  }
  
  export interface AnimationState {
    isAnimating: boolean;
    progress: number;
    vehiclePosition: [number, number] | null;
    vehicleBearing: number;
    journeyPhase: any;
  }
  
  export interface AnimationControllers {
    vehicleManager: any | null;
    animationManager: any | null;
    cameraController: any | null;
    cityDriveByManager: any | null;
  }
  
  export interface CityDriveByResult {
    nearbyCity: {
      name: string;
    };
    distance: number;
    stage: 'APPROACHING' | 'PASSING' | 'PASSED' | 'NONE';
    pois: Array<{
      id: string;
      name: string;
      position: [number, number];
    }>;
    timestamp: number;
    position: [number, number];
  }
}

// Also provide a version for absolute imports
declare module '@/types/TravelAnimatorTypes' {
  export * from '@/types/TravelAnimatorTypes';
}

/**
 * Declarations for the RoutePointAdapter
 * Exact path match for imports from '@/types/RoutePointAdapter'
 */
declare module '../../../types/RoutePointAdapter' {
  import { Position, isPosition } from '@/types/Position';
  import { RoutePoint as DetailedRoutePoint } from '@/types/RoutePoint';
  import { TerrainType } from '@/types/TerrainType';

  interface SimpleRoutePoint {
    lat: number;
    lng: number;
  }

  export function isDetailedRoutePoint(point: any): point is DetailedRoutePoint;
  export function isSimpleRoutePoint(point: any): point is SimpleRoutePoint;
  export function getRoutePointPosition(point: DetailedRoutePoint | SimpleRoutePoint): Position;
  export function convertSimpleToDetailedRoutePoint(
    point: SimpleRoutePoint,
    index?: number,
    bearing?: number,
    distance?: number
  ): DetailedRoutePoint;
  export function convertDetailedToSimpleRoutePoint(point: DetailedRoutePoint): SimpleRoutePoint;
  export function createRoutePointFromPosition(
    position: Position,
    index?: number,
    options?: {
      bearing?: number;
      distance?: number;
      terrain?: TerrainType;
      elevation?: number;
      nearestLocation?: string;
    }
  ): DetailedRoutePoint;
  export function safeGetRoutePointPosition(point: any): Position | null;
}

// Also provide a version for absolute imports
declare module '@/types/RoutePointAdapter' {
  export * from '@/types/RoutePointAdapter';
}

// Optional: Add RoutePoint interface for better integration
declare module './RoutePoint' {
  import { Position } from './Position';
  import { TerrainType } from './TerrainType';
  
  export interface RoutePoint {
    position: Position;
    distance: number;
    bearing: number;
    terrain?: TerrainType;
    elevation?: number;
    nearestLocation?: string;
    pois?: Array<{id: string; distance: number}>;
    index: number;
    metadata?: Record<string, any>;
  }
}

// Position type declaration
declare module './Position' {
  export type Position = [number, number];
  
  export function isPosition(value: unknown): value is Position;
  export function toPosition(value: unknown): Position;
}

// TerrainType declaration
declare module './TerrainType' {
  export type TerrainType = 'urban' | 'mountain' | 'desert' | 'coastal' | 'standard' | 'city' | 'rural' | 'scenic' | 'highway';
  
  export enum TerrainTypeEnum {
    DEFAULT = 'DEFAULT',
    CITY = 'CITY',
    MOUNTAIN = 'MOUNTAIN',
    DESERT = 'DESERT',
    COASTAL = 'COASTAL',
    RURAL = 'RURAL',
    SCENIC = 'SCENIC',
    HIGHWAY = 'HIGHWAY'
  }
  
  export const terrainTypeToEnum: Record<TerrainType, TerrainTypeEnum>;
  export const enumToTerrainType: Record<TerrainTypeEnum, TerrainType>;
} 