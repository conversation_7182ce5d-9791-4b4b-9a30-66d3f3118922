/**
 * POITypes.ts
 * 
 * Type definitions for Points of Interest and Destinations
 */

// CANONICAL TYPE DEFINITIONS FOR ALL POI/DESTINATION DATA IN THE APP
// All code should use these types for Destination and PointOfInterest.
// 'coordinates' is always required and must be [number, number].
// Use the normalization utilities below to ensure compliance.

import { Position, PositionObject, isPositionObject } from './Position';

/**
 * POI Category type - Updated for Discovery/Exploration Focus
 */
export type POICategory =
  | 'landmark'           // Historic monuments, iconic sites
  | 'nature'            // Natural wonders, viewpoints, geological formations
  | 'cultural'          // Museums, traditional villages, artisan workshops
  | 'adventure'         // Hiking trails, outdoor activities, sports
  | 'scenic'            // Scenic routes, panoramic viewpoints, photo spots
  | 'hidden-gem'        // Off-the-beaten-path discoveries
  | 'photography'       // Instagram-worthy locations, sunset points
  | 'local-experience'  // Markets, festivals, authentic encounters
  | 'architecture'      // Historic buildings, unique structures
  | 'viewpoint'         // Observation decks, mountain passes
  | 'activity'          // General activities and experiences
  | 'other';

/**
 * Point of Interest (POI) interface
 */
export interface PointOfInterest {
  id: string;
  name: string;
  description: string;
  position: PositionObject;
  coordinates: [number, number]; // Always required
  category: POICategory;
  images: string[]; // Always present, can be empty
  rating?: number;
  reviews?: number;
  priceLevel?: number;
  openingHours?: string;
  website?: string;
  phone?: string;
  address?: string;
  location?: string;
  tags: string[]; // Always present, can be empty
  duration?: number;
  cost?: number;
}

/**
 * Destination interface - represents a city or location that can be visited
 */
export interface Destination {
  id: string;
  name: string;
  position: PositionObject;
  coordinates: [number, number]; // Always required
  description?: string;
  images: string[]; // Always present, can be empty
  rating?: number;
  reviews?: number;
  priceLevel?: number;
  openingHours?: string;
  website?: string;
  phone?: string;
  address?: string;
  tags: string[]; // Always present, can be empty
}

// --- Normalization Utilities ---

function normalizePOI(poi: any): PointOfInterest {
  let coordinates: [number, number];
  let positionObj: PositionObject;

  if (Array.isArray(poi.coordinates) && poi.coordinates.length === 2 && typeof poi.coordinates[0] === 'number' && typeof poi.coordinates[1] === 'number') {
    coordinates = [Number(poi.coordinates[0]), Number(poi.coordinates[1])];
    positionObj = { lng: coordinates[0], lat: coordinates[1] };
  } else if (poi.position && isPositionObject(poi.position)) {
    coordinates = [Number(poi.position.lng), Number(poi.position.lat)];
    positionObj = { lng: coordinates[0], lat: coordinates[1] };
  } else if (typeof poi.latitude === 'number' && typeof poi.longitude === 'number') {
    coordinates = [Number(poi.longitude), Number(poi.latitude)]; // lng, lat
    positionObj = { lng: coordinates[0], lat: coordinates[1] };
  } else {
    throw new Error(`POI missing valid coordinates or position: ${poi.id || poi.name} - Raw: ${JSON.stringify(poi)}`);
  }

  // Debug logging disabled for production readiness
  // if (poi.name === 'Ouzoud Waterfalls') {
  //   console.log('[normalizePOI] Processing Ouzoud Waterfalls. Raw poi.images:', poi.images, 'Raw poi.image:', poi.image);
  // }

  const fallbackImage = '/images/placeholder.svg';
  let finalImages: string[] = [];

  if (Array.isArray(poi.images)) {
    finalImages = poi.images.filter((img: string) => typeof img === 'string' && img.trim() !== '');
  }

  if (finalImages.length === 0 && typeof poi.image === 'string' && poi.image.trim() !== '') {
    finalImages = [poi.image.trim()];
  }

  if (finalImages.length === 0) {
    finalImages = [fallbackImage];
  }

  // Debug logging disabled for production readiness
  // if (poi.name === 'Ouzoud Waterfalls') {
  //   console.log('[normalizePOI] Ouzoud Waterfalls. Final images array:', finalImages);
  // }

  return {
    id: String(poi.id),
    name: String(poi.name),
    description: String(poi.description || ''),
    position: positionObj,
    coordinates,
    category: (poi.category as POICategory) || (mapStringToPOICategory(poi.type as string)) || 'other',
    images: finalImages,
    rating: typeof poi.rating === 'number' ? poi.rating : undefined,
    reviews: typeof poi.reviews === 'number' ? poi.reviews : undefined,
    priceLevel: typeof poi.priceLevel === 'number' ? poi.priceLevel : undefined,
    openingHours: typeof poi.openingHours === 'string' ? poi.openingHours : undefined,
    website: typeof poi.website === 'string' ? poi.website : undefined,
    phone: typeof poi.phone === 'string' ? poi.phone : undefined,
    address: typeof poi.address === 'string' ? poi.address : undefined,
    location: typeof poi.location === 'string' ? poi.location : undefined,
    tags: Array.isArray(poi.tags) ? poi.tags : [],
    duration: typeof poi.duration === 'number' ? poi.duration : undefined,
    cost: typeof poi.cost === 'number' ? poi.cost : undefined,
  };
}

function normalizeDestination(dest: any): Destination {
  let coordinates: [number, number];
  if (Array.isArray(dest.coordinates) && dest.coordinates.length === 2) {
    coordinates = [Number(dest.coordinates[0]), Number(dest.coordinates[1])];
  } else if (dest.position && isPositionObject(dest.position)) {
    coordinates = [Number(dest.position.lng), Number(dest.position.lat)];
  } else {
    throw new Error(`Destination missing valid coordinates or position: ${dest.id || dest.name}`);
  }
  return {
    id: String(dest.id),
    name: String(dest.name),
    position: dest.position,
    coordinates,
    description: typeof dest.description === 'string' ? dest.description : undefined,
    images: Array.isArray(dest.images) ? dest.images : [],
    rating: typeof dest.rating === 'number' ? dest.rating : undefined,
    reviews: typeof dest.reviews === 'number' ? dest.reviews : undefined,
    priceLevel: typeof dest.priceLevel === 'number' ? dest.priceLevel : undefined,
    openingHours: typeof dest.openingHours === 'string' ? dest.openingHours : undefined,
    website: typeof dest.website === 'string' ? dest.website : undefined,
    phone: typeof dest.phone === 'string' ? dest.phone : undefined,
    address: typeof dest.address === 'string' ? dest.address : undefined,
    tags: Array.isArray(dest.tags) ? dest.tags : [],
  };
}

function isValidCoordinates(coordinates: [number, number]): boolean {
  return (
    Array.isArray(coordinates) &&
    coordinates.length === 2 &&
    typeof coordinates[0] === 'number' &&
    typeof coordinates[1] === 'number' &&
    coordinates[0] >= -180 &&
    coordinates[0] <= 180 &&
    coordinates[1] >= -90 &&
    coordinates[1] <= 90
  );
}

/**
 * Helper function to validate a Point of Interest
 */
function isValidPOI(poi: any): poi is PointOfInterest {
  return (
    poi &&
    typeof poi.id === 'string' &&
    typeof poi.name === 'string' &&
    typeof poi.description === 'string' &&
    isValidCoordinates(poi.coordinates) &&
    typeof poi.category === 'string'
  );
}

/**
 * Helper function to validate a Destination
 */
function isValidDestination(dest: any): dest is Destination {
  return (
    dest &&
    typeof dest.id === 'string' &&
    typeof dest.name === 'string' &&
    isValidCoordinates(dest.coordinates)
  );
}

// Helper function to map string types to POICategory - Updated for Discovery/Exploration Focus
function mapStringToPOICategory(typeStr: string | undefined): POICategory {
  if (!typeStr) return 'other';
  const lowerType = typeStr.toLowerCase();
  switch (lowerType) {
    case 'landmark': case 'monument': case 'palace': case 'historic site': case 'fortress': case 'kasbah': return 'landmark';
    case 'nature': case 'park': case 'beach': case 'mountain': case 'waterfall': case 'oasis': case 'gorge': case 'valley': case 'garden': case 'lake': case 'desert': return 'nature';
    case 'cultural': case 'museum': case 'madrasa': case 'tannery': case 'traditional village': case 'artisan workshop': return 'cultural';
    case 'adventure': case 'hiking': case 'trekking': case 'climbing': case 'water sports': case 'outdoor activity': return 'adventure';
    case 'scenic': case 'scenic route': case 'panoramic road': case 'mountain pass': case 'coastal drive': return 'scenic';
    case 'hidden-gem': case 'off-the-beaten-path': case 'secret spot': case 'local secret': return 'hidden-gem';
    case 'photography': case 'photo spot': case 'instagram spot': case 'sunset point': return 'photography';
    case 'local-experience': case 'market': case 'souk': case 'festival': case 'authentic experience': return 'local-experience';
    case 'architecture': case 'historic building': case 'unique structure': case 'architectural site': return 'architecture';
    case 'viewpoint': case 'observation deck': case 'lookout': case 'vista point': return 'viewpoint';
    case 'activity': case 'experience': case 'tour': return 'activity';
    // Legacy mappings for backward compatibility
    case 'attraction': case 'tourist attraction': return 'landmark';
    case 'restaurant': case 'cafe': case 'food': case 'dining': return 'local-experience';
    case 'hotel': case 'accommodation': case 'riad': case 'lodging': return 'other';
    case 'shopping': case 'bazaar': case 'store': return 'local-experience';
    case 'entertainment': case 'nightlife': case 'bar': case 'club': return 'local-experience';
    default: return 'other';
  }
}

export { 
  isValidCoordinates, 
  normalizePOI, 
  normalizeDestination, 
  isValidPOI, 
  isValidDestination
};