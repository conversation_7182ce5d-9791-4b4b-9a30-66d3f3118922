/**
 * POIAdapter.ts
 * Utility functions for converting between different PointOfInterest type definitions
 * This adapter resolves type compatibility issues between src/types/poi.ts and src/types/POITypes.ts
 */

import { PointOfInterest as PoiTSType } from './poi';
import { PointOfInterest as POITypesType } from './POITypes';
import { Position, isPositionTuple, isPositionObject, toPosition } from './Position';

// Valid POI types from POITypes.ts
const validPOITypes = ['accommodation', 'activity', 'restaurant', 'landmark', 'shopping', 'other'] as const;
type ValidPOIType = typeof validPOITypes[number];

/**
 * Safely determine a valid POI type from any string input
 * @param input The input string to convert to a valid POI type
 * @returns A valid POI type or 'landmark' as fallback
 */
export function safelyGetPOIType(input: string | undefined): ValidPOIType {
  if (!input) return 'landmark';
  
  // Check if the input is already a valid POI type
  if (validPOITypes.includes(input as ValidPOIType)) {
    return input as ValidPOIType;
  }
  
  // Try with lowercase
  const inputLower = input.toLowerCase();
  if (validPOITypes.includes(inputLower as ValidPOIType)) {
    return inputLower as ValidPOIType;
  }
  
  // Default fallback
  return 'landmark';
}

/**
 * Type guard to check if the object is from src/types/poi.ts
 */
export function isPoiTSType(poi: any): poi is PoiTSType {
  return poi && 'coordinates' in poi && !('position' in poi);
}

/**
 * Type guard to check if the object is from src/types/POITypes.ts
 */
export function isPOITypesType(poi: any): poi is POITypesType {
  return poi && 'position' in poi && 'coordinates' in poi;
}

/**
 * Safely get the position from any POI type
 * @returns Position tuple [lng, lat]
 */
export function getPOIPosition(poi: PoiTSType | POITypesType): Position {
  if (isPoiTSType(poi)) {
    // For PoiTSType, use coordinates
    const coords = isPositionTuple(poi.coordinates) ? poi.coordinates : toPosition(poi.coordinates);
    return coords || [0, 0];
  } else if (isPOITypesType(poi)) {
    // For POITypesType, use position
    const pos = (poi as POITypesType).position;
    return [pos.lng, pos.lat];
  }
  
  // Fallback to a default position if type cannot be determined
  console.error('Invalid POI type in getPOIPosition:', poi);
  return [0, 0]; // Default to [0,0] as fallback
}

/**
 * Convert from POITypes.PointOfInterest to poi.PointOfInterest
 */
export function convertPOITypesToPoi(poi: POITypesType): PoiTSType {
  try {
    const poiType = poi.type ? safelyGetPOIType(poi.type) : 'landmark';
    const [lng, lat] = [poi.position.lng, poi.position.lat];
    
    return {
      id: poi.id,
      name: poi.name,
      description: poi.description || '',
      type: poiType,
      coordinates: [lng, lat],
      tags: poi.tags || [],
      image: poi.image || '',
      duration: poi.duration || 0,
      cost: poi.cost || 0,
      location: poi.location || '',
      categories: poi.categories || [poiType],
      rating: poi.rating,
      price: poi.price,
      city: poi.city,
      position: { lat, lng }
    };
  } catch (error) {
    console.error('Error converting POITypesType to PoiTSType:', error);
    throw new Error(`Failed to convert POI: ${error}`);
  }
}

/**
 * Convert from poi.PointOfInterest to POITypes.PointOfInterest
 */
export function convertPoiToPOITypes(poi: PoiTSType): POITypesType {
  try {
    const coords = isPositionTuple(poi.coordinates) ? poi.coordinates : toPosition(poi.coordinates);
    if (!coords) {
      throw new Error('Invalid coordinates in POI');
    }
    const [lng, lat] = coords;
    
    return {
      id: poi.id,
      name: poi.name,
      description: poi.description,
      position: { lat, lng },
      coordinates: [lng, lat],
      type: safelyGetPOIType(poi.type),
      tags: poi.tags || [],
      image: poi.image,
      rating: poi.rating,
      price: poi.price,
      city: poi.city,
      location: poi.location
    };
  } catch (error) {
    console.error('Error converting PoiTSType to POITypesType:', error);
    throw new Error(`Failed to convert POI: ${error}`);
  }
}

/**
 * Get a standardized POI ID regardless of type
 */
export function getPOIId(poi: PoiTSType | POITypesType): string {
  return poi.id;
} 