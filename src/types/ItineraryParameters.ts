// Travel pace preferences - Updated for universal framework
export type TravelPace =
  | 'slow-immersive'     // 2-3 POIs per day, longer stays, deep exploration
  | 'balanced-explorer'  // 4-5 POIs per day, moderate pace, good balance
  | 'maximum-discovery'; // 6+ POIs per day, fast-paced, see everything

// Journey style preferences - Updated for discovery focus
export type JourneyStyle =
  | 'scenic-routes'      // Prioritize beautiful drives and panoramic roads
  | 'cultural-deep-dive' // Focus on authentic cultural experiences
  | 'adventure-seeker'   // Outdoor activities and adventure sports priority
  | 'photography-tour'   // Best viewpoints and Instagram-worthy spots
  | 'hidden-gems'        // Off-the-beaten-path discoveries
  | 'local-immersion';   // Authentic local experiences and traditions

// Travel interests/themes - Updated for exploration focus
export type TravelInterest =
  | 'landmarks'          // Historic monuments and iconic sites
  | 'nature'            // Natural wonders and landscapes
  | 'culture'           // Museums, traditions, local customs
  | 'adventure'         // Outdoor activities and sports
  | 'photography'       // Scenic viewpoints and photo opportunities
  | 'architecture'      // Historic buildings and unique structures
  | 'local-experience'  // Markets, festivals, authentic encounters
  | 'hidden-gems'       // Secret spots and off-the-beaten-path
  | 'scenic-drives'     // Beautiful routes and mountain passes
  | 'viewpoints';       // Observation decks and panoramic vistas

// Legacy type for backward compatibility
export type TravelStyle = TravelPace;

// Pre-arranged journey template interface
export interface PreArrangedJourney {
  id: string;
  name: string;
  description: string;
  duration: number;           // in days
  difficulty: 'easy' | 'moderate' | 'challenging';
  pace: TravelPace;
  style: JourneyStyle;
  cities: string[];          // City names in order
  highlights: string[];      // Key POI names
  imageUrl?: string;
  price?: {
    from: number;
    currency: string;
  };
  seasonality: string[];     // Best seasons
  tags: string[];           // Additional tags for filtering
}