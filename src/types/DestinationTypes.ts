export interface Destination {
  id: string;
  name: string;
  coordinates?: [number, number]; // [longitude, latitude]
  image?: string;
  description?: string;
  days?: number;
  budget?: number;
  distance?: string;
  activities?: string[];
  weather?: {
    condition: 'sunny' | 'cloudy' | 'rainy' | 'windy';
    temperature: number;
  };
}

export interface DestinationWithDetails extends Destination {
  details?: {
    culture?: string;
    cuisine?: string;
    attractions?: string[];
    bestTimeToVisit?: string;
  };
} 