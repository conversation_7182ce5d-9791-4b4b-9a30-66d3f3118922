/**
 * POITypes declaration file
 * These types are referenced in config/paths.ts
 */

import { Position } from './Position';

export interface Destination {
  id: string;
  name: string;
  description: string;
  coordinates: Position;
  image?: string;
  tags?: string[];
  daysToSpend?: number;
  nearby?: PointOfInterest[];
  regionId?: string;
  region?: string;
  popularity?: number;
  type?: string;
  featured?: boolean;
}

export interface PointOfInterest {
  id: string;
  name: string;
  type: string;
  description: string;
  image: string;
  duration?: number;
  cost?: number;
  location: string;
  coordinates: Position;
  tags: string[];
  imageUrl?: string;
  categories?: string[];
} 