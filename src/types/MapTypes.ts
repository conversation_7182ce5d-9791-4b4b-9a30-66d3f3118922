/**
 * Map type definitions
 * 
 * This file defines map-specific types while importing the core Position type
 * from the central Position type system.
 */
import type { Position, MapPosition } from './Position';

// Re-export Position types for backward compatibility
export type { Position, MapPosition };

// Map-specific types that extend or use Position
export interface MapViewport {
  center: Position;
  zoom: number;
  bearing?: number;
  pitch?: number;
}

export interface MapBounds {
  northeast: Position;
  southwest: Position;
}

export interface MapMarker {
  id: string;
  position: Position;
  type: string;
  icon?: string;
  label?: string;
}

/**
 * Map view state
 */
export interface MapViewState {
  longitude: number;
  latitude: number;
  zoom: number;
  pitch: number;
  bearing: number;
}

/**
 * Map bounds
 */
export interface MapBounds {
  north: number;
  south: number;
  east: number;
  west: number;
} 