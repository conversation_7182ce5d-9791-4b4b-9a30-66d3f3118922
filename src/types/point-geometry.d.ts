
/**
 * Type declaration for @mapbox/point-geometry
 * Added to fix TypeScript import issues
 */
declare module '@mapbox/point-geometry' {
  export default class Point {
    x: number;
    y: number;
    
    constructor(x: number, y: number);
    
    clone(): Point;
    add(p: Point): Point;
    sub(p: Point): Point;
    mult(k: number): Point;
    div(k: number): Point;
    rotate(a: number): Point;
    matMult(m: number[]): Point;
    distSqr(p: Point): number;
    dist(p: Point): number;
    mag(): number;
    equals(p: Point): boolean;
    toString(): string;
    convert(a: any): Point;
  }
}
