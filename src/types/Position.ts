/**
 * Basic Position type used across the application
 * Represents a geographic position as either a tuple [lng, lat] or an object { lat, lng }.
 */

/**
 * Position type definitions
 * Single source of truth for all position-related types in the application
 */

// Core position types
export type PositionTuple = [number, number]; // [longitude, latitude]
export type PositionObject = { lat: number; lng: number };
export type Position = PositionTuple | PositionObject;

// Map-specific position types
export interface MapPosition {
  latitude: number;
  longitude: number;
  zoom?: number;
  bearing?: number;
  pitch?: number;
}

export interface GeoPosition {
  longitude: number;
  latitude: number;
}

export interface LatLngPosition {
  lat: number;
  lng: number;
}

// Type guards
export function isPositionTuple(position: unknown): position is PositionTuple {
  return (
    Array.isArray(position) &&
    position.length === 2 &&
    typeof position[0] === 'number' &&
    typeof position[1] === 'number'
  );
}

export function isPositionObject(position: unknown): position is PositionObject {
  return (
    typeof position === 'object' &&
    position !== null &&
    'lat' in position &&
    'lng' in position &&
    typeof (position as PositionObject).lat === 'number' &&
    typeof (position as PositionObject).lng === 'number'
  );
}

export function isPosition(pos: unknown): pos is Position {
  return isPositionTuple(pos) || isPositionObject(pos);
}

export function isMapPosition(pos: unknown): pos is MapPosition {
  return !Array.isArray(pos) && 
    pos !== null && 
    typeof pos === 'object' && 
    'latitude' in pos && 
    'longitude' in pos &&
    typeof pos.latitude === 'number' &&
    typeof pos.longitude === 'number';
}

export function isGeoPosition(pos: unknown): pos is GeoPosition {
  return !Array.isArray(pos) && 
    pos !== null && 
    typeof pos === 'object' && 
    'latitude' in pos && 
    'longitude' in pos &&
    typeof pos.latitude === 'number' &&
    typeof pos.longitude === 'number';
}

export function isLatLngPosition(pos: unknown): pos is LatLngPosition {
  return !Array.isArray(pos) && 
    pos !== null && 
    typeof pos === 'object' && 
    'lat' in pos && 
    'lng' in pos &&
    typeof pos.lat === 'number' &&
    typeof pos.lng === 'number';
}

export function isAnyPosition(pos: unknown): pos is Position | MapPosition | GeoPosition | LatLngPosition {
  return isPosition(pos) || isMapPosition(pos) || isGeoPosition(pos) || isLatLngPosition(pos);
}

// Conversion functions
export function toPosition(position: Position): PositionObject {
  if (isPositionTuple(position)) {
    return { lat: position[1], lng: position[0] };
  }
  return position;
}

export function toPositionTuple(position: Position): PositionTuple {
  if (isPositionObject(position)) {
    return [position.lng, position.lat];
  }
  return position;
}

export function toPositionObject(pos: Position | PositionObject | null | undefined): PositionObject | null {
  if (!pos) {
    return null;
  }
  if (isPositionObject(pos)) {
    return pos;
  }
  if (isPositionTuple(pos)) {
    return { lng: pos[0], lat: pos[1] };
  }
  return null;
}

export function toMapPosition(pos: Position): MapPosition {
  const tuple = toPositionTuple(pos);
  if (!tuple) {
    throw new Error('Invalid position for map conversion');
  }
  return {
    longitude: tuple[0],
    latitude: tuple[1]
  };
}

export function fromMapPosition(pos: MapPosition): PositionTuple {
  return [pos.longitude, pos.latitude];
}

export function positionToMapPosition(pos: Position): MapPosition {
  return toMapPosition(pos);
}

export function positionToGeoPosition(pos: Position): GeoPosition {
  const tuple = toPositionTuple(pos);
  if (!tuple) {
    throw new Error('Invalid position for geo conversion');
  }
  return {
    longitude: tuple[0],
    latitude: tuple[1]
  };
}

export function positionToLatLngPosition(pos: Position): LatLngPosition {
  const tuple = toPositionTuple(pos);
  if (!tuple) {
    throw new Error('Invalid position for latlng conversion');
  }
  return {
    lat: tuple[1],
    lng: tuple[0]
  };
}

// Safe conversion functions
export function safeToPosition(position: unknown): PositionObject | null {
  if (!isValidPosition(position)) {
    return null;
  }
  return toPosition(position as Position);
}

export function safeToPositionTuple(position: unknown): PositionTuple | null {
  if (!isValidPosition(position)) {
    return null;
  }
  return toPositionTuple(position as Position);
}

export function createPosition(lat: number, lng: number): Position {
  return { lat, lng };
}

export function safeCreatePosition(lng: number, lat: number, fallback: Position = [0, 0]): Position {
  try {
    return createPosition(lat, lng);
  } catch (error) {
    console.warn('Failed to create position, using fallback:', error);
    return fallback;
  }
}

// Validation functions
export function isValidLongitude(lng: number): boolean {
  return typeof lng === 'number' && 
    !isNaN(lng) && 
    isFinite(lng) && 
    lng >= -180 && 
    lng <= 180;
}

export function isValidLatitude(lat: number): boolean {
  return typeof lat === 'number' && 
    !isNaN(lat) && 
    isFinite(lat) && 
    lat >= -90 && 
    lat <= 90;
}

export function isValidPosition(position: unknown): position is Position {
  return isPositionTuple(position) || isPositionObject(position);
}

// Utility functions
export function arePositionsEqual(pos1: Position, pos2: Position): boolean {
  const tuple1 = toPositionTuple(pos1);
  const tuple2 = toPositionTuple(pos2);
  if (!tuple1 || !tuple2) return false;
  return tuple1[0] === tuple2[0] && tuple1[1] === tuple2[1];
}

export function calculateDistance(pos1: Position | null | undefined, pos2: Position | null | undefined): number | null {
  if (!pos1 || !pos2) {
    return null;
  }
  const tuple1 = toPositionTuple(pos1);
  const tuple2 = toPositionTuple(pos2);
  if (!tuple1 || !tuple2) {
    return null;
  }
  const R = 6371; // Earth's radius in kilometers
  const dLat = (tuple2[1] - tuple1[1]) * Math.PI / 180;
  const dLon = (tuple2[0] - tuple1[0]) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(tuple1[1] * Math.PI / 180) * Math.cos(tuple2[1] * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
} 

// Export everything as a namespace
export const PositionUtils = {
  isPositionTuple,
  isPositionObject,
  isPosition,
  isMapPosition,
  isGeoPosition,
  isLatLngPosition,
  isAnyPosition,
  toPosition,
  toPositionTuple,
  toPositionObject,
  toMapPosition,
  fromMapPosition,
  positionToMapPosition,
  positionToGeoPosition,
  positionToLatLngPosition,
  safeToPosition,
  safeToPositionTuple,
  createPosition,
  safeCreatePosition,
  isValidLongitude,
  isValidLatitude,
  isValidPosition,
  arePositionsEqual,
  calculateDistance
}; 