/**
 * This file patches mapbox-gl module to work around the TypeScript error with @mapbox/point-geometry import
 * 
 * By patching the specific mapbox-gl module imports to use our own versions that are 
 * compatible with TypeScript's module system regardless of esModuleInterop setting
 */

// First, patch the point-geometry module
declare module '@mapbox/point-geometry' {
  class Point {
    x: number;
    y: number;
    constructor(x: number, y: number);
    
    // Basic operations
    clone(): Point;
    add(point: Point): Point;
    sub(point: Point): Point;
    mult(factor: number): Point;
    div(factor: number): Point;
    
    // Geometric operations
    rotate(angle: number): Point;
    matMult(matrix: [number, number, number, number]): Point;
    
    // Comparison
    equals(point: Point): boolean;
    
    // Distance calculations
    dist(point: Point): number;
    distSqr(point: Point): number;
    
    // Angle calculations
    angle(): number;
    angleTo(point: Point): number;
    angleWidth(point: Point): number;
    angleWithSep(x: number, y: number): number;
    
    // Other operations
    perp(): Point;
    round(): Point;
    mag(): number;
    
    // Static method
    convert(arrays: any): Array<Point>;
  }
  
  // Match how it's exported in the original module
  export = Point;
}

// Then provide a complete patched version of the mapbox-gl module
declare module 'mapbox-gl' {
  // Import the patched Point
  import Point = require('@mapbox/point-geometry');
  
  // Re-export all the types from the original mapbox-gl
  export * from 'mapbox-gl';
  
  // Add the default export that matches how the project uses it
  const mapboxgl: any;
  export default mapboxgl;
} 