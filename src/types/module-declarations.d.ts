/**
 * Declaration overrides for third-party modules
 * This file provides TypeScript declaration fixes for various module import issues
 */

// Fix for mapbox-gl and its dependencies
declare module 'mapbox-gl' {
  const mapboxgl: any;
  export = mapboxgl;
  export as namespace mapboxgl;
}

// Fix for @mapbox/point-geometry which is imported by mapbox-gl
declare module '@mapbox/point-geometry' {
  class Point {
    x: number;
    y: number;
    
    constructor(x: number, y: number);
    
    clone(): Point;
    add(p: Point): Point;
    sub(p: Point): Point;
    mult(k: number): Point;
    div(k: number): Point;
    rotate(a: number): Point;
    matMult(m: number[]): Point;
    distSqr(p: Point): number;
    dist(p: Point): number;
    mag(): number;
    equals(p: Point): boolean;
    toString(): string;
    convert(a: any): Point;
  }
  
  export = Point;
}

// Additional module declarations as needed 

/**
 * Global module declarations to help with TypeScript errors
 */

// Make Animation event types compatible
declare module '@/types' {
  export const AnimationEventType: any;
  export interface EventData {}
  export interface AnimationEventData {}
  export type AnimationEventCallback = (data: any) => void;
  export interface CityProximityStage {}
  export type Position = [number, number] | { lat: number; lng: number };
  
  // Add POI types
  export interface PointOfInterest {
    id: string;
    name: string;
    coordinates: [number, number]; 
    type?: string;
    description?: string;
    image?: string;
    position: { lat: number; lng: number; name?: string };
  }
  
  export interface Destination {
    id: string;
    name: string;
    coordinates: [number, number];
    position?: { lat: number; lng: number };
    description?: string;
    imageUrl?: string;
    image?: string;
    distance?: string | number;
    visitDuration?: number;
    bestTimeToVisit?: string;
    travelTime?: number;
    isHighlight?: boolean;
    days?: number;
    budget?: number;
    activities?: string[];
    weather?: {
      condition?: 'sunny' | 'cloudy' | 'rainy' | 'windy';
      temperature?: number;
    };
    type?: string;
    region?: string;
    tags?: string[];
    pois?: PointOfInterest[];
    suggestedDuration?: number;
    cost?: number;
  }
  
  export function isValidCoordinates(coords: any): coords is [number, number];
}

// Add AnimationManager compatibility
declare module '@/components/map/animation/AnimationManagerWrapper' {
  export default class AnimationManagerWrapper {
    static getInstance(): AnimationManagerWrapper;
    initialize(map: any, ...args: any[]): void;
    isReady(): boolean;
    isInitialized(): boolean;
    startAnimation(route: any[], options?: any): void;
    pauseAnimation(): void;
    resumeAnimation(): void;
    stopAnimation(): void;
    getProgress(): number;
    getPosition(): [number, number];
    isAnimating: boolean;
    isPaused: boolean;
    progress: number;
    addEventListener(eventType: any, callback: (data: any) => void): () => void;
    removeEventListener(eventType: any, callback: (data: any) => void): void;
    getPOIDiscoveryManager(): any;
    getVehicleManager(): any;
    startCoordinatedAnimation(route: any[], options?: any): Promise<boolean>;
  }
}

// Ensure compatibility for animation component props
declare module '@/components/map/AnimationControlPanel' {
  import { FC } from 'react';
  
  export interface AnimationControlPanelProps {
    isAnimating?: boolean;
    isPaused?: boolean;
    progress?: number;
    onPause?: () => void;
    onStop?: () => void;
    onRestart?: () => Promise<void>;
  }
  
  const AnimationControlPanel: FC<AnimationControlPanelProps>;
  export default AnimationControlPanel;
}

// Animation event types definitions
declare module '@/types/MultiClientAnimationTypes' {
  export enum MultiClientAnimationEvent {
    START = 'start',
    PAUSE = 'pause',
    RESUME = 'resume',
    STOP = 'stop',
    COMPLETE = 'complete',
    PROGRESS = 'progress',
    ERROR = 'error',
    POI_DISCOVERED = 'poi_discovered',
    CITY_APPROACHED = 'city_approached'
  }
  
  export interface MultiClientAnimationOptions {
    duration?: number;
    onComplete?: () => void;
  }
  
  export interface AnimationProgressInfo {
    progress: number;
    position: any;
    bearing: number;
  }
  
  export interface CityDriveByInfo {
    cityName: string;
    position: Position;
    description?: string;
    imageUrl?: string;
    city?: string;
    distance?: number;
    pois?: Array<{
      id: string;
      name: string;
      position: [number, number];
    }>;
  }
  
  export type AnimationEventCallback = (data: any) => void;
}

// Add declarations for AwarenessIndicators
declare module '../clusters/ClusterGenerator' {
  export interface ExtendedPointOfInterest {
    id: string;
    name: string;
    coordinates: [number, number];
    type: string;
    city?: string;
    position?: { lat: number; lng: number; name?: string };
    imageUrl?: string;
    description?: string;
    image?: string;
    images?: string[];
    tags?: string[];
    suggestedDuration?: number;
    cost?: number;
    pois?: ExtendedPointOfInterest[];
  }
}

// Add more TravelAnimator and Destination related types
declare module '@/types/POITypes' {
  interface Destination {
    id: string;
    name: string;
    coordinates: [number, number];
    description?: string;
    imageUrl?: string;
    position?: { lat: number; lng: number };
    type?: string;
    region?: string;
    image?: string;
    images?: string[];
    tags?: string[];
    suggestedDuration?: number;
    cost?: number;
    pois?: any[];
  }
}

// Add compatibility for hooks
declare module '@/hooks/useAnimationManager' {
  import { default as AnimationManager } from '@/components/map/animation/AnimationManagerWrapper';
  
  interface AnimationHookResult {
    isAnimating: boolean;
    isPaused: boolean;
    progress: number;
    currentPosition: any;
    currentBearing: number;
    startAnimation: (route: any[], options?: any) => void;
    togglePause: () => void;
    stopAnimation: () => void;
    addEventListener: (eventType: any, callback: (data: any) => void) => () => void;
    
    // Add missing properties used in TravelAnimator.tsx
    isInitialized: () => boolean;
    initialize: (map: any, ...args: any[]) => Promise<void>;
    getVehicleManager: () => any;
    startCoordinatedAnimation: (route: any[], options?: any) => Promise<boolean>;
  }
  
  export default function useAnimationManager(map?: any): AnimationHookResult;
}

// Add declarations for ContextualSpeedController
declare module '@/components/map/animation/ContextualSpeedController' {
  export default class ContextualSpeedController {
    constructor(options?: any);
    adjustSpeed(context: any): number;
    calculateSpeedMultiplier(context: any): number;
    getTerrainAdjustment(terrain: string): number;
  }
}

// Add declarations for POIDiscoveryManager
declare module '@/components/map/animation/POIDiscoveryManager' {
  export default class POIDiscoveryManager {
    constructor(map?: any);
    configure(options: any): void;
    addPOI(poi: any): void;
    getContextUpdate(position: any): any;
    getNearbyPOIs(position: any): any[];
  }
}

// Add declarations for AwarenessIndicators to fix ExtendedPointOfInterest
declare module '@/components/map/animation/AwarenessIndicators' {
  export interface ExtendedPointOfInterest {
    id: string;
    name: string;
    coordinates: [number, number];
    type?: string;
    position?: { lat: number; lng: number; name?: string };
    imageUrl?: string;
    description?: string;
    image?: string;
    images?: string[];
    tags?: string[];
    suggestedDuration?: number;
    cost?: number;
    pois?: ExtendedPointOfInterest[];
  }
  
  export interface POINotification {
    poi: ExtendedPointOfInterest;
    distance: number;
    direction: string;
    type: 'approaching' | 'passing';
  }
}

// Fix CityDriveByManager related types
declare module '@/components/map/animation/CityDriveByManager' {
  export interface CityDriveByEvent {
    city: string;
    distance: number;
    position: [number, number];
    type?: string;
    pois?: any[];
  }
  
  export interface DriveByDestination {
    id: string;
    name: string;
    position: [number, number];
    type?: string;
  }
}

// Add declarations for missing Animation types to reduce AnimationEventType errors
declare module '@/types/AnimationEventTypes' {
  export enum AnimationEventType {
    ANIMATION_START = 'animation_start',
    ANIMATION_PAUSE = 'animation_pause',
    ANIMATION_RESUME = 'animation_resume',
    ANIMATION_STOP = 'animation_stop',
    ANIMATION_COMPLETE = 'animation_complete',
    ANIMATION_PROGRESS = 'animation_progress',
    ANIMATION_ERROR = 'animation_error',
    POI_DISCOVERED = 'poi_discovered',
    CITY_APPROACHED = 'city_approached',
    STATE_CHANGE = 'state_change',
    PROGRESS_UPDATE = 'progress_update'
  }
} 