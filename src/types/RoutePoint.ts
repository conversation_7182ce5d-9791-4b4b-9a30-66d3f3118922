/**
 * RoutePoint.ts
 * 
 * Represents a point along a route with position and additional metadata
 */

import { Position } from './Position';
import { TerrainType } from './TerrainType';

/**
 * Represents a point along a route with position and contextual metadata
 */
export interface RoutePoint {
  /**
   * Geographic position as [longitude, latitude]
   */
  position: Position;
  
  /**
   * Distance along the route in meters
   */
  distance: number;
  
  /**
   * Bearing (direction) in degrees at this point
   */
  bearing: number;
  
  /**
   * Terrain type at this location
   */
  terrain?: TerrainType;
  
  /**
   * Elevation in meters (if available)
   */
  elevation?: number;
  
  /**
   * Nearest city or location name (if available)
   */
  nearestLocation?: string;
  
  /**
   * Distance to nearest city or point of interest in meters (if available)
   */
  pois?: {
    id: string;
    distance: number;
  }[];
  
  /**
   * Index of this point in the route array
   */
  index: number;

  /**
   * Any additional metadata for this route point
   */
  metadata?: Record<string, any>;
} 