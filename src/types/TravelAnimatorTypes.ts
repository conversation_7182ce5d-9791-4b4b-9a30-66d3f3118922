import { Map } from 'mapbox-gl';
import { Position } from './Position';
import { PointOfInterest } from './POITypes';

// Import the JourneyPhase enum from utils/types
import { JourneyPhase as ImportedJourneyPhase } from '@/components/map/utils/types';

// Re-export the JourneyPhase enum for consumers of this file
export type JourneyPhase = ImportedJourneyPhase;

/**
 * Button state type for journey controls
 */
export type ButtonState = 'default' | 'loading' | 'error' | 'disabled';

/**
 * The main props interface for the TravelAnimator component
 */
export interface TravelAnimatorProps {
  onAnimationComplete?: () => void;
  onProgressUpdate?: (progress: number) => void;
  map: mapboxgl.Map;
  destinations: [number, number][];
  allDestinations?: Destination[];
  route?: Array<[number, number]>;
  isPaused?: boolean;
  onPOIDiscovered?: (poi: PointOfInterest) => void;
  discoveredPOIs?: string[];
  selectedCategories?: string[];
  userInterests?: string[];
  pois?: PointOfInterest[];
  onAnimationEvent?: (event: { type: string; success: boolean; message: string }) => void;
  isAnimating: boolean;
  vehicleManager: any;
  onProgress: (progress: number, position: [number, number], bearing: number) => void;
  onComplete: () => void;
  setVehiclePosition: (position: [number, number]) => void;
  setShowVehicle: (show: boolean) => void;
  setProgress: (progress: number) => void;
  setIsAnimating: (isAnimating: boolean) => void;
}

/**
 * Animation state interface
 */
export interface AnimationState {
  isAnimating: boolean;
  isPaused: boolean;
  progress: number;
  currentPosition: [number, number];
  currentBearing: number;
  currentDestination: number;
  currentSegment: number;
  currentSegmentProgress: number;
}

/**
 * Journey state interface
 */
export interface JourneyState {
  isActive: boolean;
  isPaused: boolean;
  progress: number;
  currentPosition: [number, number];
  currentBearing: number;
  currentDestination: number;
  currentSegment: number;
  currentSegmentProgress: number;
}

/**
 * Animation configuration interface
 */
export interface AnimationConfig {
  duration: number;
  easing: string;
  vehicle: VehicleConfig;
}

/**
 * Vehicle configuration interface
 */
export interface VehicleConfig {
  type: 'car' | 'bike' | 'walk';
  speed: number;
  icon: string;
  size: number;
  color: string;
}

/**
 * Animation controllers interface
 */
export interface AnimationControllers {
  vehicleManager: any | null;
  animationManager: any | null;
  cameraController: any | null;
  cityDriveByManager: any | null;
}

/**
 * City drive-by result interface
 */
export interface CityDriveByResult {
  nearbyCity: {
    name: string;
  };
  distance: number;
  stage: 'APPROACHING' | 'PASSING' | 'PASSED' | 'NONE';
  pois: Array<{
    id: string;
    name: string;
    position: [number, number];
  }>;
  timestamp: number;
  position: [number, number];
}

/**
 * Route point interface
 */
export interface RoutePoint {
  lat: number;
  lng: number;
}

/**
 * Vehicle marker constants
 */
export const VEHICLE_MARKER_ID = 'vehicle-marker';
export const DIRECT_VEHICLE_MARKER_ID = 'direct-vehicle-marker';

/**
 * Animation constants
 */
export const BASE_ANIMATION_DURATION_MS = 60000; // 60 seconds default duration

/**
 * Extend Window interface
 */
declare global {
  interface Window {
    vehicleMarkerController: {
      cleanup: () => void;
      updatePosition: (position: [number, number], bearing: number) => void;
    } | null;
  }
}

/**
 * Configuration options for travel animation playback
 */
export interface TravelAnimationOptions {
  duration?: number;
  speedFactor?: number;
  easing?: (t: number) => number;
  followVehicle?: boolean;
  zoomLevel?: number;
  cinematic?: boolean;
  onProgress?: (progress: number) => void;
  onPositionUpdate?: (position: Position, bearing: number) => void;
  onComplete?: () => void;
  onPOIDiscovered?: (poi: PointOfInterest) => void;
  contextualSpeed?: boolean;
}

/**
 * Animation progress update interface
 */
export interface AnimationProgressUpdate {
  progress: number;
  position: Position;
  bearing: number;
}

/**
 * Destination interface
 */
export interface Destination {
  id: string;
  name: string;
  position: Position;
  description?: string;
  imageUrl?: string;
  [key: string]: any;
}

/**
 * City drive-by data interface
 */
export interface CityDriveByData {
  city: string;
  position: [number, number];
  bearing: number;
  zoom: number;
  duration: number;
}

export interface AnimationProgress {
  progress: number;
  position: [number, number];
  bearing: number;
  destination: number;
  segment: number;
  segmentProgress: number;
} 