/**
 * animation.ts
 * 
 * UNIFIED ANIMATION TYPES - SINGLE SOURCE OF TRUTH
 * Consolidates AnimationTypes.ts, CoreAnimationTypes.ts, MultiClientAnimationTypes.ts
 */

import { Position } from './Position';
import { TerrainType } from './TerrainType';

// ========================================
// CORE ANIMATION STATE
// ========================================

/**
 * Animation state interface - unified from all previous definitions
 */
export interface AnimationState {
  isPlaying: boolean;
  isPaused: boolean;
  progress: number;
  currentPosition: Position | null;
  currentBearing: number;
  speed: number;
  error?: string | null;
}

/**
 * Animation phase enum
 */
export enum AnimationPhase {
  IDLE = 'idle',
  INITIALIZING = 'initializing',
  READY = 'ready',
  PLAYING = 'playing',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  ERROR = 'error'
}

// ========================================
// ROUTE AND POSITION TYPES
// ========================================

/**
 * Route point with optional metadata
 */
export interface RoutePoint {
  position: Position;
  bearing?: number;
  speed?: number;
  terrain?: TerrainType;
  metadata?: Record<string, any>;
}

/**
 * Route data structure
 */
export interface RouteData {
  points: RoutePoint[];
  totalDistance?: number;
  estimatedDuration?: number;
  metadata?: Record<string, any>;
}

// ========================================
// ANIMATION CONFIGURATION
// ========================================

/**
 * Animation configuration options
 */
export interface AnimationConfig {
  duration: number;
  speedFactor?: number;
  easing?: (t: number) => number;
  followVehicle?: boolean;
  zoomLevel?: number;
  cinematic?: boolean;
  contextualSpeed?: boolean;
  onProgress?: (state: AnimationState) => void;
  onComplete?: () => void;
  onError?: (error: Error) => void;
  onPositionUpdate?: (position: Position, bearing: number) => void;
  onPOIDiscovered?: (poi: any) => void;
}

/**
 * Vehicle marker configuration
 */
export interface VehicleConfig {
  elementId?: string;
  className?: string;
  size?: number;
  color?: string;
  showTrail?: boolean;
}

// ========================================
// ANIMATION SPEEDS AND CONSTANTS
// ========================================

/**
 * Speed multipliers for different contexts
 */
export const ANIMATION_SPEEDS = {
  DEFAULT: 1.0,
  CITY: 0.5,
  MOUNTAIN: 0.6,
  DESERT: 1.2,
  COASTAL: 1.0,
  RURAL: 0.8,
  SCENIC: 0.3,
  HIGHWAY: 1.5,
  SLOW: 0.3,
  NORMAL: 1.0,
  FAST: 1.5,
  VERY_FAST: 2.0
} as const;

/**
 * Animation timing constants
 */
export const ANIMATION_TIMING = {
  FRAME_RATE: 60,
  MAX_DELTA: 100,
  MIN_DELTA: 10,
  POSITION_CHECK_INTERVAL: 500,
  TIMEOUT: 10000,
  RECOVERY_ATTEMPTS: 3
} as const;

// ========================================
// EVENT TYPES
// ========================================

/**
 * Animation event types
 */
export enum AnimationEventType {
  ANIMATION_START = 'animation_start',
  ANIMATION_PAUSE = 'animation_pause',
  ANIMATION_RESUME = 'animation_resume',
  ANIMATION_COMPLETE = 'animation_complete',
  ANIMATION_ERROR = 'animation_error',
  ANIMATION_PROGRESS = 'animation_progress',
  POSITION_UPDATE = 'position_update',
  POI_DISCOVERED = 'poi_discovered',
  CITY_APPROACHED = 'city_approached',
  TERRAIN_CHANGED = 'terrain_changed'
}

/**
 * Animation event data
 */
export interface AnimationEventData {
  type: AnimationEventType;
  timestamp: number;
  data?: any;
}

/**
 * Animation event callback
 */
export type AnimationEventCallback = (event: AnimationEventData) => void;

// ========================================
// PROGRESS AND COMPLETION TYPES
// ========================================

/**
 * Animation progress information
 */
export interface AnimationProgressInfo {
  progress: number;
  position: Position;
  bearing: number;
  speed: number;
  phase: AnimationPhase;
  timestamp: number;
}

/**
 * City drive-by information
 */
export interface CityDriveByInfo {
  cityName: string;
  position: Position;
  description?: string;
  imageUrl?: string;
  distance?: number;
}

/**
 * POI discovery information
 */
export interface POIDiscoveryInfo {
  poi: any; // Will be typed more specifically when POI types are consolidated
  position: Position;
  distance: number;
  discoveryType: 'nearby' | 'route' | 'destination';
}

// ========================================
// CALLBACK TYPES
// ========================================

/**
 * Progress callback type
 */
export type ProgressCallback = (info: AnimationProgressInfo) => void;

/**
 * Completion callback type
 */
export type CompleteCallback = () => void;

/**
 * Error callback type
 */
export type ErrorCallback = (error: Error) => void;

/**
 * Position update callback type
 */
export type PositionUpdateCallback = (position: Position, bearing: number) => void;

// ========================================
// MANAGER INTERFACE
// ========================================

/**
 * Animation manager interface
 */
export interface AnimationManagerInterface {
  initialize(map: any, config?: Partial<VehicleConfig>): void;
  startAnimation(route: RouteData | RoutePoint[], config: AnimationConfig): Promise<boolean>;
  pauseAnimation(shouldPause?: boolean): void;
  stopAnimation(): void;
  getState(): AnimationState;
  dispose(): void;
}

// ========================================
// TYPE GUARDS
// ========================================

/**
 * Type guard for RoutePoint
 */
export function isRoutePoint(value: any): value is RoutePoint {
  return (
    value &&
    typeof value === 'object' &&
    Array.isArray(value.position) &&
    value.position.length === 2 &&
    typeof value.position[0] === 'number' &&
    typeof value.position[1] === 'number'
  );
}

/**
 * Type guard for RouteData
 */
export function isRouteData(value: any): value is RouteData {
  return (
    value &&
    typeof value === 'object' &&
    Array.isArray(value.points) &&
    value.points.every(isRoutePoint)
  );
}

// ========================================
// UTILITY TYPES
// ========================================

/**
 * Animation options for simplified usage
 */
export type SimpleAnimationOptions = Pick<
  AnimationConfig,
  'duration' | 'speedFactor' | 'onProgress' | 'onComplete' | 'onError'
>;

/**
 * Animation state subset for UI components
 */
export type UIAnimationState = Pick<
  AnimationState,
  'isPlaying' | 'isPaused' | 'progress'
>;
