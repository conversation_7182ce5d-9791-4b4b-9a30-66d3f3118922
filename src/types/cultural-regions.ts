/**
 * Interface representing a cultural or geographical region on the map.
 * Used for contextual animation and vehicle marker appearance.
 */
export interface CulturalRegion {
  /** Name of the cultural or geographical region */
  name: string;
  
  /** Type of region, affects camera behavior and vehicle appearance */
  type: 'mountain' | 'desert' | 'city' | 'default';
  
  /** 
   * Geographical bounds of the region as [[minLng, minLat], [maxLng, maxLat]]
   * Coordinates are in the format [longitude, latitude]
   */
  bounds: [[number, number], [number, number]];
  
  /** Optional camera settings overrides for this region */
  cameraSettings?: {
    pitch?: number;
    bearing?: number;
    zoom?: number;
    duration?: number;
  };
} 