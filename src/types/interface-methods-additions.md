# Missing Method Declarations for Animation System Interfaces

This document outlines method declarations that need to be added to our animation system interfaces for improved type safety and better IDE support.

## VehicleManagerInterface

```typescript
/**
 * Sets the vehicle visibility state directly
 * @param visible Whether the vehicle should be visible
 * @returns Whether the operation was successful
 */
setVehicleVisible(visible: boolean): boolean;

/**
 * Updates the position and terrain of the vehicle
 * @param position New position
 * @param bearing Optional bearing in degrees
 * @param terrain Optional terrain type
 */
updatePosition(position: Position, bearing?: number, terrain?: TerrainType): void;

/**
 * Sets the Mapbox map instance for the vehicle manager
 * @param map The Mapbox map instance
 */
setMap(map: mapboxgl.Map): void;

/**
 * Attempts to recover vehicle visibility when it becomes hidden
 * @returns Whether recovery was successful
 */
recoverVehicleVisibility(): boolean;

/**
 * Forces an attempt to recover the vehicle marker in case of errors
 * @param position Position to place the recovered vehicle
 * @param bearing Optional bearing for the vehicle
 */
attemptRecovery(position: Position, bearing?: number): void;
```

## AnimationManagerInterface

```typescript
/**
 * Gets the current vehicle position
 * @returns The current position or null if not animating
 */
getCurrentPosition(): Position | null;

/**
 * Gets the current vehicle bearing
 * @returns The current bearing in degrees
 */
getCurrentBearing(): number;

/**
 * Checks if animation is currently running
 * @returns True if animation is in progress
 */
isCurrentlyAnimating(): boolean;

/**
 * Sets contextual animation rhythm handler function
 * @param rhythmHandler Function that modifies animation based on context
 */
setContextualRhythm(rhythmHandler: (position: Position, elapsedTime: number, isNearPOI?: boolean) => any): void;

/**
 * Enables or disables contextual speed adjustments
 * @param enabled Whether to adjust speed based on context
 */
enableContextualSpeed(enabled: boolean): void;
```

## CameraControllerInterface

```typescript
/**
 * Sets the current camera mode and behavior
 * @param mode Camera mode identifier
 * @param options Additional options for the mode
 */
setCameraMode(mode: string, options?: Record<string, any>): void;

/**
 * Enables or disables smooth transitions
 * @param enabled Whether smooth transitions should be used
 */
enableSmoothTransitions(enabled: boolean): void;

/**
 * Handles map viewport changes
 * @param viewport Map viewport properties
 */
handleViewportChange(viewport: {center?: Position, zoom?: number, bearing?: number, pitch?: number}): void;

/**
 * Sets camera padding for frame calculations
 * @param padding Padding values or single number
 */
setPadding(padding: number | {top?: number, right?: number, bottom?: number, left?: number}): void;
```

## CinematicControllerInterface

```typescript
/**
 * Enables or disables debugging visualizations
 * @param enabled Whether debug mode is enabled
 */
setDebugMode(enabled: boolean): void;

/**
 * Registers custom handlers for cinematic events
 * @param eventType Type of cinematic event
 * @param handler Handler function
 */
registerEventHandler(eventType: string, handler: (data: any) => void): () => void;

/**
 * Sets options for all cinematic sequences
 * @param options Global cinematic sequence options
 */
setGlobalOptions(options: Partial<CinematicSequenceOptions>): void;
```

## Additional Interface Improvements

We should also ensure all interfaces inherit from a common base interface where appropriate to ensure consistency:

```typescript
/**
 * Base interface for all animation controllers
 */
export interface AnimationComponentInterface {
  /**
   * Initializes the component
   */
  initialize(config?: Record<string, any>): void;
  
  /**
   * Cleans up resources
   */
  dispose(): void;
  
  /**
   * Returns debug information about the component state
   */
  getDebugInfo(): Record<string, any>;
}
```

These missing method declarations should be added to their respective interface files to ensure type safety and provide better developer experience with IDE auto-completion and documentation. 