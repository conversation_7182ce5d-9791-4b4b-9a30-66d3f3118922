/**
 * Admin Authentication Context
 * Provides role-based access control for admin interfaces
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AdminService, { UserRole } from '../services/database/AdminService';

// =====================================================
// TYPE DEFINITIONS
// =====================================================

export interface AdminUser {
  id: string;
  email?: string;
  role: 'super_admin' | 'client_admin' | 'user';
  clientId?: string;
  permissions: string[];
  isActive: boolean;
}

export interface AdminAuthContextType {
  user: AdminUser | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isSuperAdmin: boolean;
  isClientAdmin: boolean;
  hasPermission: (permission: string) => boolean;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
  switchClient: (clientId: string) => Promise<boolean>;
}

// =====================================================
// CONTEXT CREATION
// =====================================================

const AdminAuthContext = createContext<AdminAuthContextType | undefined>(undefined);

// =====================================================
// PROVIDER COMPONENT
// =====================================================

interface AdminAuthProviderProps {
  children: ReactNode;
}

export const AdminAuthProvider: React.FC<AdminAuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<AdminUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize auth state
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      setIsLoading(true);
      
      // Check for existing session
      const savedUser = localStorage.getItem('admin_user');
      if (savedUser) {
        const userData = JSON.parse(savedUser);
        
        // Validate session and load current permissions
        const userRoles = await AdminService.getUserRoles(userData.id);
        if (userRoles.length > 0) {
          const activeRole = userRoles.find(role => role.is_active) || userRoles[0];
          
          setUser({
            id: userData.id,
            email: userData.email,
            role: activeRole.role,
            clientId: activeRole.client_id,
            permissions: activeRole.permissions,
            isActive: activeRole.is_active
          });
        }
      } else {
        // For demo purposes, create a default super admin user
        // In production, this would be handled by proper authentication
        const demoUser: AdminUser = {
          id: 'demo-super-admin',
          email: '<EMAIL>',
          role: 'super_admin',
          permissions: [
            'clients.view', 'clients.create', 'clients.edit', 'clients.delete',
            'content.view', 'content.create', 'content.edit', 'content.delete',
            'themes.view', 'themes.create', 'themes.edit', 'themes.delete',
            'users.view', 'users.create', 'users.edit', 'users.delete',
            'system.view_analytics', 'system.manage_settings'
          ],
          isActive: true
        };
        
        setUser(demoUser);
        localStorage.setItem('admin_user', JSON.stringify(demoUser));
      }
    } catch (error) {
      console.error('Error initializing admin auth:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      
      // TODO: Implement proper authentication with Supabase Auth
      // For now, simulate login based on email
      
      let role: 'super_admin' | 'client_admin' = 'client_admin';
      let clientId: string | undefined;
      let permissions: string[] = [];

      if (email === '<EMAIL>' || email === '<EMAIL>') {
        role = 'super_admin';
        permissions = [
          'clients.view', 'clients.create', 'clients.edit', 'clients.delete',
          'content.view', 'content.create', 'content.edit', 'content.delete',
          'themes.view', 'themes.create', 'themes.edit', 'themes.delete',
          'users.view', 'users.create', 'users.edit', 'users.delete',
          'system.view_analytics', 'system.manage_settings'
        ];
      } else if (email.includes('morocco')) {
        role = 'client_admin';
        clientId = 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'; // Morocco client ID
        permissions = [
          'content.view', 'content.create', 'content.edit', 'content.delete',
          'themes.view', 'themes.edit',
          'quotes.view', 'quotes.respond'
        ];
      } else if (email.includes('portugal')) {
        role = 'client_admin';
        clientId = 'b2c3d4e5-f6a7-8901-bcde-f23456789012'; // Portugal client ID
        permissions = [
          'content.view', 'content.create', 'content.edit', 'content.delete',
          'themes.view', 'themes.edit',
          'quotes.view', 'quotes.respond'
        ];
      }

      const adminUser: AdminUser = {
        id: `user-${Date.now()}`,
        email,
        role,
        clientId,
        permissions,
        isActive: true
      };

      setUser(adminUser);
      localStorage.setItem('admin_user', JSON.stringify(adminUser));

      // Log the login action
      await AdminService.logAdminAction({
        user_id: adminUser.id,
        client_id: adminUser.clientId,
        action: 'admin_login',
        resource_type: 'auth',
        new_values: { email, role }
      });

      return true;
    } catch (error) {
      console.error('Error during admin login:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('admin_user');
  };

  const switchClient = async (clientId: string): Promise<boolean> => {
    if (!user || user.role !== 'super_admin') {
      return false;
    }

    try {
      const updatedUser = {
        ...user,
        clientId
      };

      setUser(updatedUser);
      localStorage.setItem('admin_user', JSON.stringify(updatedUser));

      // Log the client switch action
      await AdminService.logAdminAction({
        user_id: user.id,
        client_id: clientId,
        action: 'client_switch',
        resource_type: 'auth',
        new_values: { clientId }
      });

      return true;
    } catch (error) {
      console.error('Error switching client:', error);
      return false;
    }
  };

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    return user.permissions.includes(permission);
  };

  // Computed properties
  const isAuthenticated = !!user;
  const isSuperAdmin = user?.role === 'super_admin';
  const isClientAdmin = user?.role === 'client_admin';

  const value: AdminAuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    isSuperAdmin,
    isClientAdmin,
    hasPermission,
    login,
    logout,
    switchClient
  };

  return (
    <AdminAuthContext.Provider value={value}>
      {children}
    </AdminAuthContext.Provider>
  );
};

// =====================================================
// HOOK FOR USING CONTEXT
// =====================================================

export const useAdminAuth = (): AdminAuthContextType => {
  const context = useContext(AdminAuthContext);
  if (context === undefined) {
    throw new Error('useAdminAuth must be used within an AdminAuthProvider');
  }
  return context;
};

// =====================================================
// PERMISSION CONSTANTS
// =====================================================

export const ADMIN_PERMISSIONS = {
  // Client Management
  CLIENTS_VIEW: 'clients.view',
  CLIENTS_CREATE: 'clients.create',
  CLIENTS_EDIT: 'clients.edit',
  CLIENTS_DELETE: 'clients.delete',

  // Content Management
  CONTENT_VIEW: 'content.view',
  CONTENT_CREATE: 'content.create',
  CONTENT_EDIT: 'content.edit',
  CONTENT_DELETE: 'content.delete',

  // Theme Management
  THEMES_VIEW: 'themes.view',
  THEMES_CREATE: 'themes.create',
  THEMES_EDIT: 'themes.edit',
  THEMES_DELETE: 'themes.delete',

  // User Management
  USERS_VIEW: 'users.view',
  USERS_CREATE: 'users.create',
  USERS_EDIT: 'users.edit',
  USERS_DELETE: 'users.delete',

  // System Administration
  SYSTEM_ANALYTICS: 'system.view_analytics',
  SYSTEM_SETTINGS: 'system.manage_settings',

  // Quote Management
  QUOTES_VIEW: 'quotes.view',
  QUOTES_RESPOND: 'quotes.respond',
  QUOTES_DELETE: 'quotes.delete'
} as const;

export default AdminAuthContext;
