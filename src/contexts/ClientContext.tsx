/**
 * ClientContext.tsx
 * 
 * Context provider for managing client-specific state and configuration
 */

import React, { createContext, useContext, useState, useEffect, useMemo } from 'react';
import { ClientConfig, ThemeConfig } from '../types/ClientTypes';

/**
 * Client context value interface
 */
interface ClientContextValue {
  clientId: string;
  config: ClientConfig | null;
  theme: ThemeConfig | null;
  isLoaded: boolean;
  error: Error | null;
  setClientId: (id: string) => void;
}

// Create the context with default values
const ClientContext = createContext<ClientContextValue>({
  clientId: '',
  config: null,
  theme: null,
  isLoaded: false,
  error: null,
  setClientId: () => {}
});

/**
 * Default client configuration for fallback
 */
const DEFAULT_CLIENT_ID = 'morocco';

/**
 * Props for the ClientProvider component
 */
interface ClientProviderProps {
  initialClientId?: string;
  children: React.ReactNode;
}

/**
 * Client Provider component for managing client-specific state
 */
export const ClientProvider: React.FC<ClientProviderProps> = ({ 
  initialClientId = DEFAULT_CLIENT_ID,
  children 
}) => {
  const [clientId, setClientId] = useState<string>(initialClientId);
  const [config, setConfig] = useState<ClientConfig | null>(null);
  const [isLoaded, setIsLoaded] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  // Load client configuration when clientId changes
  useEffect(() => {
    const loadClientConfig = async () => {
      try {
        setIsLoaded(false);
        setError(null);
        
        // In a real implementation, this would fetch from an API or config store
        // For now, we'll simulate loading with a timeout
        const configResponse = await fetch(`/api/clients/${clientId}/config`)
          .catch(() => {
            // Fallback for development - simulate API response
            return new Promise<Response>((resolve) => {
              setTimeout(() => {
                // Mock client configuration
                const mockConfig: ClientConfig = {
                  id: clientId,
                  name: clientId === 'morocco' ? 'Morocco' : 'Portugal',
                  theme: {
                    primaryColor: clientId === 'morocco' ? '#c53030' : '#2b6cb0',
                    secondaryColor: clientId === 'morocco' ? '#dd6b20' : '#4299e1',
                    accentColor: clientId === 'morocco' ? '#f6ad55' : '#63b3ed',
                    backgroundColor: '#ffffff',
                    textColor: '#1a202c',
                    fontFamily: '"Roboto", sans-serif',
                    logoUrl: `/assets/${clientId}/logo.svg`
                  },
                  mapSettings: {
                    initialBounds: [
                      [clientId === 'morocco' ? -13.168555 : -9.5, clientId === 'morocco' ? 27.666667 : 36.8],
                      [clientId === 'morocco' ? -1.030511 : -6.2, clientId === 'morocco' ? 35.922284 : 42.1]
                    ],
                    defaultCenter: clientId === 'morocco' ? [-7.09, 31.79] : [-8.61, 39.6],
                    defaultZoom: 6,
                    minZoom: 5,
                    maxZoom: 18,
                    style: 'mapbox://styles/mapbox/streets-v11',
                    padding: {
                      top: 50,
                      bottom: 50,
                      left: 50,
                      right: 50
                    }
                  },
                  poiSettings: {
                    categories: [
                      {
                        id: 'landmark',
                        name: 'Landmarks',
                        icon: 'landmark',
                        color: '#4a5568'
                      },
                      {
                        id: 'restaurant',
                        name: 'Restaurants',
                        icon: 'restaurant',
                        color: '#e53e3e'
                      },
                      {
                        id: 'accommodation',
                        name: 'Accommodations',
                        icon: 'hotel',
                        color: '#38a169'
                      },
                      {
                        id: 'activity',
                        name: 'Activities',
                        icon: 'hiking',
                        color: '#d69e2e'
                      }
                    ],
                    filters: [
                      {
                        id: 'type',
                        name: 'Type',
                        field: 'type',
                        options: [
                          { value: 'landmark', label: 'Landmarks' },
                          { value: 'restaurant', label: 'Restaurants' },
                          { value: 'accommodation', label: 'Accommodations' },
                          { value: 'activity', label: 'Activities' }
                        ]
                      },
                      {
                        id: 'cost',
                        name: 'Price Range',
                        field: 'cost',
                        options: [
                          { value: 'low', label: 'Budget' },
                          { value: 'medium', label: 'Mid-range' },
                          { value: 'high', label: 'Luxury' }
                        ]
                      }
                    ],
                    clustering: {
                      enabled: true,
                      radius: 50,
                      maxZoom: 14,
                      minPoints: 2
                    },
                    displayRules: {
                      minZoom: 10,
                      maxDistance: 5000, // meters
                      priorityField: 'priority'
                    }
                  },
                  featureFlags: {
                    routeAnimation: true,
                    poiDiscovery: true,
                    itineraryBuilder: true,
                    userAccounts: clientId === 'morocco',
                    routeSaving: true,
                    analytics: true
                  }
                };
                
                const mockResponse = new Response(
                  JSON.stringify({ data: mockConfig }),
                  { status: 200, headers: { 'Content-Type': 'application/json' } }
                );
                
                resolve(mockResponse);
              }, 500);
            });
          });

        const data = await configResponse.json();
        setConfig(data.data);
        setIsLoaded(true);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to load client configuration'));
        setIsLoaded(true);
      }
    };

    loadClientConfig();
  }, [clientId]);

  const value = useMemo((): ClientContextValue => ({
    clientId,
    config,
    theme: config?.theme || null,
    isLoaded,
    error,
    setClientId
  }), [clientId, config, isLoaded, error]);

  return (
    <ClientContext.Provider value={value}>
      {children}
    </ClientContext.Provider>
  );
};

/**
 * Hook for accessing the client context
 */
export const useClient = () => {
  const context = useContext(ClientContext);
  if (context === undefined) {
    throw new Error('useClient must be used within a ClientProvider');
  }
  return context;
};

export default ClientContext;