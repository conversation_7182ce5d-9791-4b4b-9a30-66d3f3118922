<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/morocco.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Destinations</title>
    <link rel="preconnect" href="https://api.mapbox.com">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=DM+Sans:opsz,wght@9..40,400;9..40,700&family=Playfair+Display:wght@400;700&display=swap" rel="stylesheet">
    <style>
      /* Reset basic styles */
      html, body, #root {
        height: 100%;
        margin: 0;
        padding: 0;
        overflow: hidden;
      }
      
      /* Ensure the mapboxgl-map container fills its parent */
      .mapboxgl-map {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
      }
      
      /* Make the map container take up the full space */
      #explore-map-container {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
      }

      /* Ensure map container parent expands properly in flex layout */
      .explore-map-container {
        flex: 1;
        min-width: 0;
        position: relative;
      }
      
      /* MARKER STYLING - Only visual properties, no positioning */
      .destination-marker {
        width: 36px;
        height: 36px;
        background-color: #E27D60;
        border: 2px solid white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        cursor: pointer;
      }
      
      /* Selected marker state */
      .destination-marker.selected {
        background-color: #d64a33;
        border-width: 3px;
      }
      
      /* Number inside marker */
      .destination-marker span {
        color: white;
        font-weight: bold;
        font-size: 16px;
      }
    </style>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html> 