/**
 * DevProvider.tsx
 * 
 * DEVELOPMENT PROVIDER
 * Provides development tools and debugging capabilities
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import DevDashboard from '@/components/dev/DevDashboard';
import { ErrorBoundary } from '@/components/shared/ErrorBoundary';
import { performanceMonitor } from '@/utils/performance';

// ========================================
// TYPES AND INTERFACES
// ========================================

interface DevContextValue {
  isDevelopment: boolean;
  isDevDashboardVisible: boolean;
  toggleDevDashboard: () => void;
  enablePerformanceMonitoring: boolean;
  setEnablePerformanceMonitoring: (enabled: boolean) => void;
  debugMode: boolean;
  setDebugMode: (enabled: boolean) => void;
}

interface DevProviderProps {
  children: ReactNode;
  enableInProduction?: boolean;
}

// ========================================
// CONTEXT CREATION
// ========================================

const DevContext = createContext<DevContextValue | undefined>(undefined);

export const useDevTools = (): DevContextValue => {
  const context = useContext(DevContext);
  if (!context) {
    throw new Error('useDevTools must be used within a DevProvider');
  }
  return context;
};

// ========================================
// DEVELOPMENT UTILITIES
// ========================================

/**
 * Add global debug utilities to window object
 */
const addGlobalDebugUtils = () => {
  if (typeof window === 'undefined') return;

  // Add debug utilities to window for console access
  (window as any).__DEV_TOOLS__ = {
    // Performance monitoring
    performance: {
      start: (name: string) => performanceMonitor.start(name),
      end: (name: string) => performanceMonitor.end(name),
      getMetrics: () => performanceMonitor.getMetrics(),
      clear: () => performanceMonitor.clear()
    },
    
    // Component debugging
    components: {
      highlightRenders: () => {
        // Add visual indicators for component re-renders
        const style = document.createElement('style');
        style.textContent = `
          * {
            animation: highlight-render 0.3s ease-in-out;
          }
          @keyframes highlight-render {
            0% { outline: 2px solid red; }
            100% { outline: none; }
          }
        `;
        document.head.appendChild(style);
        setTimeout(() => document.head.removeChild(style), 1000);
      },
      
      logProps: (component: any) => {
        console.log('Component props:', component);
      }
    },
    
    // State debugging
    state: {
      logAll: () => {
        console.log('Application state debugging not implemented');
      }
    },
    
    // Network debugging
    network: {
      logRequests: () => {
        // Intercept fetch requests for debugging
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
          console.log('🌐 Fetch request:', args);
          const response = await originalFetch(...args);
          console.log('🌐 Fetch response:', response);
          return response;
        };
      }
    },
    
    // Error simulation
    errors: {
      throwError: (message: string = 'Test error') => {
        throw new Error(message);
      },
      
      throwAsync: async (message: string = 'Test async error') => {
        await new Promise(resolve => setTimeout(resolve, 100));
        throw new Error(message);
      }
    }
  };

  console.log('🛠️ Dev tools available at window.__DEV_TOOLS__');
};

/**
 * Add keyboard shortcuts for development
 */
const addKeyboardShortcuts = (toggleDevDashboard: () => void) => {
  const handleKeyDown = (event: KeyboardEvent) => {
    // Ctrl/Cmd + Shift + D to toggle dev dashboard
    if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'D') {
      event.preventDefault();
      toggleDevDashboard();
    }
    
    // Ctrl/Cmd + Shift + P to log performance metrics
    if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'P') {
      event.preventDefault();
      console.table(performanceMonitor.getMetrics());
    }
    
    // Ctrl/Cmd + Shift + C to clear console
    if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'C') {
      event.preventDefault();
      console.clear();
    }
  };

  document.addEventListener('keydown', handleKeyDown);
  return () => document.removeEventListener('keydown', handleKeyDown);
};

// ========================================
// MAIN PROVIDER COMPONENT
// ========================================

export const DevProvider: React.FC<DevProviderProps> = ({ 
  children, 
  enableInProduction = false 
}) => {
  const isDevelopment = process.env.NODE_ENV === 'development' || enableInProduction;
  
  const [isDevDashboardVisible, setIsDevDashboardVisible] = useState(false);
  const [enablePerformanceMonitoring, setEnablePerformanceMonitoring] = useState(isDevelopment);
  const [debugMode, setDebugMode] = useState(isDevelopment);

  // Initialize development tools
  useEffect(() => {
    if (!isDevelopment) return;

    // Add global debug utilities
    addGlobalDebugUtils();

    // Add keyboard shortcuts
    const removeKeyboardShortcuts = addKeyboardShortcuts(() => 
      setIsDevDashboardVisible(prev => !prev)
    );

    // Performance monitoring
    if (enablePerformanceMonitoring) {
      performanceMonitor.start('app-initialization');
      
      // Monitor React render performance
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'measure' && entry.name.includes('React')) {
            console.log(`⚡ React performance: ${entry.name} took ${entry.duration.toFixed(2)}ms`);
          }
        });
      });
      
      try {
        observer.observe({ entryTypes: ['measure'] });
      } catch (error) {
        console.warn('Performance observer not supported');
      }

      return () => {
        removeKeyboardShortcuts();
        observer.disconnect();
        performanceMonitor.end('app-initialization');
      };
    }

    return removeKeyboardShortcuts;
  }, [isDevelopment, enablePerformanceMonitoring]);

  // Debug mode effects
  useEffect(() => {
    if (!isDevelopment || !debugMode) return;

    // Add debug styling
    const debugStyle = document.createElement('style');
    debugStyle.id = 'dev-debug-styles';
    debugStyle.textContent = `
      [data-debug="true"] {
        outline: 1px dashed rgba(255, 0, 0, 0.3) !important;
      }
      [data-debug="true"]:hover {
        outline: 2px solid rgba(255, 0, 0, 0.6) !important;
      }
    `;
    document.head.appendChild(debugStyle);

    return () => {
      const existingStyle = document.getElementById('dev-debug-styles');
      if (existingStyle) {
        document.head.removeChild(existingStyle);
      }
    };
  }, [isDevelopment, debugMode]);

  const toggleDevDashboard = () => {
    setIsDevDashboardVisible(prev => !prev);
  };

  const contextValue: DevContextValue = {
    isDevelopment,
    isDevDashboardVisible,
    toggleDevDashboard,
    enablePerformanceMonitoring,
    setEnablePerformanceMonitoring,
    debugMode,
    setDebugMode
  };

  // Don't render dev tools in production unless explicitly enabled
  if (!isDevelopment) {
    return <>{children}</>;
  }

  return (
    <DevContext.Provider value={contextValue}>
      <ErrorBoundary level="critical" showDetails={true}>
        {children}
        
        {/* Development Dashboard */}
        <DevDashboard
          isVisible={isDevDashboardVisible}
          onToggle={toggleDevDashboard}
        />
        
        {/* Development Notifications */}
        {isDevelopment && (
          <div className="fixed top-4 left-4 z-40">
            <div className="bg-yellow-100 border border-yellow-400 text-yellow-800 px-3 py-2 rounded-md text-sm">
              🛠️ Development Mode
              <div className="text-xs mt-1">
                Press Ctrl+Shift+D for dev tools
              </div>
            </div>
          </div>
        )}
      </ErrorBoundary>
    </DevContext.Provider>
  );
};

// ========================================
// DEVELOPMENT HOOKS
// ========================================

/**
 * Hook for performance monitoring in components
 */
export const usePerformanceMonitor = (componentName: string) => {
  const { enablePerformanceMonitoring } = useDevTools();

  useEffect(() => {
    if (!enablePerformanceMonitoring) return;

    const metricName = `component-${componentName}`;
    performanceMonitor.start(metricName);

    return () => {
      performanceMonitor.end(metricName);
    };
  }, [componentName, enablePerformanceMonitoring]);
};

/**
 * Hook for debugging component renders
 */
export const useRenderDebug = (componentName: string, props?: any) => {
  const { debugMode } = useDevTools();

  useEffect(() => {
    if (!debugMode) return;

    console.log(`🔄 ${componentName} rendered`, {
      timestamp: new Date().toISOString(),
      props: props ? Object.keys(props) : 'no props'
    });
  });

  useEffect(() => {
    if (!debugMode) return;

    console.log(`🎯 ${componentName} mounted`);
    return () => {
      console.log(`💀 ${componentName} unmounted`);
    };
  }, [componentName, debugMode]);
};

export default DevProvider;
