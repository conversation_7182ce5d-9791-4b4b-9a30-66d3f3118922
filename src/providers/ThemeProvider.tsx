/**
 * ThemeProvider.tsx
 * 
 * Provider component for applying client-specific theming
 */

import React, { createContext, useContext, useMemo, useEffect } from 'react';
import { useClient } from '../contexts/ClientContext';
import { ThemeConfig } from '../types/ClientTypes';
import { getThemeByClientId, defaultTheme } from '../config/themes';

/**
 * Theme context value interface
 */
interface ThemeContextValue {
  theme: ThemeConfig;
  isDarkMode: boolean;
  toggleDarkMode: () => void;
}

// Create the context with default values
const ThemeContext = createContext<ThemeContextValue>({
  theme: defaultTheme,
  isDarkMode: false,
  toggleDarkMode: () => {}
});

/**
 * Props for the ThemeProvider component
 */
interface ThemeProviderProps {
  children: React.ReactNode;
}

/**
 * Theme Provider component for applying client-specific styling
 */
export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const { clientId, isLoaded } = useClient();
  const [isDarkMode, setIsDarkMode] = React.useState(false);

  // Toggle dark mode function
  const toggleDarkMode = () => {
    setIsDarkMode(prev => !prev);
  };

  // Get theme based on client ID
  const theme = useMemo(() => {
    return isLoaded ? getThemeByClientId(clientId) : defaultTheme;
  }, [clientId, isLoaded]);

  // Apply theme to document root
  useEffect(() => {
    if (!theme) return;

    // Apply CSS variables to :root
    const root = document.documentElement;
    
    // Apply base theme variables
    root.style.setProperty('--primary-color', theme.primaryColor);
    root.style.setProperty('--secondary-color', theme.secondaryColor);
    root.style.setProperty('--accent-color', theme.accentColor);
    root.style.setProperty('--background-color', theme.backgroundColor);
    root.style.setProperty('--text-color', theme.textColor);
    root.style.setProperty('--font-family', theme.fontFamily);
    
    // Apply extended theme variables
    if (theme.buttons) {
      root.style.setProperty('--button-primary-bg', theme.buttons.primary.background);
      root.style.setProperty('--button-primary-text', theme.buttons.primary.text);
      root.style.setProperty('--button-primary-hover', theme.buttons.primary.hover);
      root.style.setProperty('--button-primary-active', theme.buttons.primary.active);
      
      root.style.setProperty('--button-secondary-bg', theme.buttons.secondary.background);
      root.style.setProperty('--button-secondary-text', theme.buttons.secondary.text);
      root.style.setProperty('--button-secondary-hover', theme.buttons.secondary.hover);
      root.style.setProperty('--button-secondary-active', theme.buttons.secondary.active);
    }
    
    if (theme.map) {
      root.style.setProperty('--map-marker-destination', theme.map.markerColors.destination);
      root.style.setProperty('--map-marker-poi', theme.map.markerColors.poi);
      root.style.setProperty('--map-marker-selectedpoi', theme.map.markerColors.selectedPoi);
      root.style.setProperty('--map-marker-vehicle', theme.map.markerColors.vehicle);
      root.style.setProperty('--map-route-color', theme.map.routeColor);
      root.style.setProperty('--map-poi-cluster', theme.map.poiClusterColor);
    }
    
    if (theme.animation) {
      root.style.setProperty('--animation-countdown', theme.animation.countdownColor);
      root.style.setProperty('--animation-progress', theme.animation.progressBarColor);
      root.style.setProperty('--animation-vehicle', theme.animation.vehicleColor);
    }
    
    if (theme.overlays) {
      root.style.setProperty('--overlay-background', theme.overlays.background);
      root.style.setProperty('--overlay-header', theme.overlays.headerBackground);
      root.style.setProperty('--overlay-border', theme.overlays.borderColor);
      root.style.setProperty('--overlay-tag-bg', theme.overlays.tagBackground);
      root.style.setProperty('--overlay-tag-text', theme.overlays.tagTextColor);
    }
    
    if (theme.text) {
      root.style.setProperty('--text-heading', theme.text.heading);
      root.style.setProperty('--text-paragraph', theme.text.paragraph);
      root.style.setProperty('--text-muted', theme.text.muted);
      root.style.setProperty('--text-highlight', theme.text.highlight);
    }

    // Apply dark mode overrides if enabled
    if (isDarkMode) {
      root.style.setProperty('--background-color', '#1a202c');
      root.style.setProperty('--text-color', '#f7fafc');
      root.classList.add('dark-mode');
    } else {
      root.classList.remove('dark-mode');
    }

    // Set font family
    document.body.style.fontFamily = theme.fontFamily;
    
  }, [theme, isDarkMode]);

  // Memoize context value to prevent unnecessary re-renders
  const value = useMemo(() => ({
    theme,
    isDarkMode,
    toggleDarkMode
  }), [theme, isDarkMode]);

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

/**
 * Hook for accessing the theme context
 */
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export default ThemeContext;