import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { MapReadinessState } from '../hooks/types/mapboxTypes';
import mapboxgl from 'mapbox-gl';

interface MapReadyContextType {
  map: mapboxgl.Map | null;
  isMapReady: boolean;
  isMapFullyLoaded: boolean;
  mapReadinessState: MapReadinessState;
  readinessError: Error | null;
  waitForMap: () => Promise<mapboxgl.Map>;
}

const defaultMapReadyContext: MapReadyContextType = {
  map: null,
  isMapReady: false,
  isMapFullyLoaded: false,
  mapReadinessState: MapReadinessState.NOT_INITIALIZED,
  readinessError: null,
  waitForMap: () => new Promise((_, reject) => reject(new Error('MapReadyContext not initialized'))),
};

const MapReadyContext = createContext<MapReadyContextType>(defaultMapReadyContext);

export const useMapReady = () => useContext(MapReadyContext);

interface MapReadyProviderProps {
  children: ReactNode;
}

const MAP_READY_TIMEOUT = 10000; // 10 seconds timeout for map to be ready

export function MapReadyProvider({ children }: MapReadyProviderProps) {
  const [map, setMap] = useState<mapboxgl.Map | null>(null);
  const [isMapReady, setIsMapReady] = useState(false);
  const [isMapFullyLoaded, setIsMapFullyLoaded] = useState(false);
  const [mapReadinessState, setMapReadinessState] = useState<MapReadinessState>(MapReadinessState.NOT_INITIALIZED);
  const [readinessError, setReadinessError] = useState<Error | null>(null);

  useEffect(() => {
    // Listen for basic map load events
    const handleMapLoaded = (event: Event) => {
      console.log('MapReadyProvider: mapbox-loaded event received');
      setMapReadinessState(MapReadinessState.LOADED);
    };

    // Listen for the fully ready custom event
    const handleMapFullyReady = (event: CustomEvent) => {
      // console.log('MapReadyProvider: mapbox-fully-ready event received', event); // Commented out to reduce noise
      if (event.detail && event.detail.map) {
        setMap(event.detail.map);
        setIsMapReady(true); 
        setIsMapFullyLoaded(true);
        setMapReadinessState(MapReadinessState.FULLY_READY);
      }
    };

    // Add event listeners
    window.addEventListener('mapbox-loaded', handleMapLoaded);
    window.addEventListener('mapbox-fully-ready', handleMapFullyReady as EventListener);

    // Set up timeout for map readiness
    const readinessTimeout = setTimeout(() => {
      if (!isMapFullyLoaded) {
        console.warn('MapReadyProvider: Map readiness timeout reached');
        setReadinessError(new Error('Map failed to initialize within the expected timeframe'));
      }
    }, MAP_READY_TIMEOUT);

    // Clean up event listeners and timeout
    return () => {
      window.removeEventListener('mapbox-loaded', handleMapLoaded);
      window.removeEventListener('mapbox-fully-ready', handleMapFullyReady as EventListener);
      clearTimeout(readinessTimeout);
    };
  }, [isMapFullyLoaded]);

  // Function to wait for the map to be fully ready
  const waitForMap = (): Promise<mapboxgl.Map> => {
    return new Promise((resolve, reject) => {
      if (isMapFullyLoaded && map) {
        resolve(map);
        return;
      }

      // If there's already an error, reject immediately
      if (readinessError) {
        reject(readinessError);
        return;
      }

      // Set up listeners for map ready and timeout
      const handleMapReady = (event: CustomEvent) => {
        if (event.detail && event.detail.map) {
          cleanupListeners();
          resolve(event.detail.map);
        }
      };

      const handleTimeout = () => {
        cleanupListeners();
        reject(new Error('Timeout waiting for map to be ready'));
      };

      // Add event listeners
      window.addEventListener('mapbox-fully-ready', handleMapReady as EventListener);
      const timeoutId = setTimeout(handleTimeout, 5000); // 5 second timeout

      // Cleanup function
      const cleanupListeners = () => {
        window.removeEventListener('mapbox-fully-ready', handleMapReady as EventListener);
        clearTimeout(timeoutId);
      };
    });
  };

  const contextValue: MapReadyContextType = {
    map,
    isMapReady,
    isMapFullyLoaded,
    mapReadinessState,
    readinessError,
    waitForMap,
  };

  return (
    <MapReadyContext.Provider value={contextValue}>
      {children}
    </MapReadyContext.Provider>
  );
}

// HOC to ensure a component only renders when the map is ready
export function withMapReady<P extends object>(Component: React.ComponentType<P>) {
  return function WithMapReady(props: P) {
    const { isMapReady, map } = useMapReady();

    if (!isMapReady || !map) {
      return null; // Or a loading indicator
    }

    return <Component {...props} map={map} />;
  };
}

// Custom hook to get the map when it's ready (async)
export function useMapWhenReady() {
  const { map, isMapReady, waitForMap } = useMapReady();
  const [error, setError] = useState<Error | null>(null);
  const [isLoading, setIsLoading] = useState(!isMapReady);

  useEffect(() => {
    if (isMapReady && map) {
      setIsLoading(false);
      return;
    }

    let isMounted = true;
    
    setIsLoading(true);
    waitForMap()
      .then(() => {
        if (isMounted) {
          setIsLoading(false);
        }
      })
      .catch((err) => {
        if (isMounted) {
          setError(err);
          setIsLoading(false);
        }
      });

    return () => {
      isMounted = false;
    };
  }, [map, isMapReady, waitForMap]);

  return { map, isLoading, error };
}

// Component to render children only when map is ready
export function MapReadyGuard({ children }: { children: ReactNode }) {
  const { isMapReady } = useMapReady();

  if (!isMapReady) {
    return null; // Or a loading indicator
  }

  return <>{children}</>;
} 