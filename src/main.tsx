import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import 'mapbox-gl/dist/mapbox-gl.css';
import './styles/index.css';
import './styles/enhanced-poi-panels.css';
import './styles/modern-poi-panels.css';
import { HeroUIProvider } from '@heroui/react';

console.log('main.tsx loaded');

ReactDOM.createRoot(document.getElementById('root')!).render(
  <HeroUIProvider>
    <App />
  </HeroUIProvider>
);

// Clear service worker cache and unregister for development
if ('serviceWorker' in navigator && import.meta.env.DEV) {
  navigator.serviceWorker.getRegistrations().then(function(registrations) {
    for(let registration of registrations) {
      registration.unregister()
        .then(function() { console.log('Service Worker UNREGISTERED:', registration.scope); })
        .catch(function(err) { console.error('Service Worker UNREGISTRATION FAILED:', err); });
    }
    if (registrations.length === 0) {
      console.log('No active service workers found to unregister.');
    }
  }).catch(function(err) {
    console.error('Error getting service worker registrations:', err);
  });

  // Clear all caches in development
  if ('caches' in window) {
    caches.keys().then(function(names) {
      for (let name of names) {
        caches.delete(name).then(function() {
          console.log('Cache cleared:', name);
        });
      }
    });
  }
}

// Service Worker Registration - Commented out for now to prevent re-registration
/*
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js') // Path is relative to the root
      .then((registration) => {
        console.log('Service Worker registered with scope:', registration.scope);
      })
      .catch((error) => {
        console.error('Service Worker registration failed:', error);
      });
  });
}
*/
