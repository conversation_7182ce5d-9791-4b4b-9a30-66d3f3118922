export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      clients: {
        Row: {
          id: string
          name: string
          slug: string
          logo_url: string | null
          status: string
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          name: string
          slug: string
          logo_url?: string | null
          status?: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          logo_url?: string | null
          status?: string
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      client_destinations: {
        Row: {
          id: string
          client_id: string
          name: string
          description: string | null
          coordinates: number[]
          type: string
          priority: number
          is_capital: boolean
          region: string | null
          population: number | null
          image_url: string | null
          tags: string[]
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          client_id: string
          name: string
          description?: string | null
          coordinates: number[]
          type: string
          priority?: number
          is_capital?: boolean
          region?: string | null
          population?: number | null
          image_url?: string | null
          tags?: string[]
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          client_id?: string
          name?: string
          description?: string | null
          coordinates?: number[]
          type?: string
          priority?: number
          is_capital?: boolean
          region?: string | null
          population?: number | null
          image_url?: string | null
          tags?: string[]
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "client_destinations_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          }
        ]
      }
      client_journeys: {
        Row: {
          id: string
          client_id: string
          name: string
          description: string | null
          duration: number
          difficulty: string
          pace: string
          style: string
          cities: string[]
          highlights: string[]
          image_url: string | null
          price: number | null
          seasonality: string[]
          tags: string[]
          is_featured: boolean
          is_active: boolean
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          client_id: string
          name: string
          description?: string | null
          duration: number
          difficulty?: string
          pace?: string
          style?: string
          cities: string[]
          highlights?: string[]
          image_url?: string | null
          price?: number | null
          seasonality?: string[]
          tags?: string[]
          is_featured?: boolean
          is_active?: boolean
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          client_id?: string
          name?: string
          description?: string | null
          duration?: number
          difficulty?: string
          pace?: string
          style?: string
          cities?: string[]
          highlights?: string[]
          image_url?: string | null
          price?: number | null
          seasonality?: string[]
          tags?: string[]
          is_featured?: boolean
          is_active?: boolean
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "client_journeys_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          }
        ]
      }
      client_poi_categories: {
        Row: {
          id: string
          client_id: string
          category_id: string
          name: string
          icon: string
          color: string
          description: string | null
          is_active: boolean
          sort_order: number
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          client_id: string
          category_id: string
          name: string
          icon: string
          color: string
          description?: string | null
          is_active?: boolean
          sort_order?: number
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          client_id?: string
          category_id?: string
          name?: string
          icon?: string
          color?: string
          description?: string | null
          is_active?: boolean
          sort_order?: number
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "client_poi_categories_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          }
        ]
      }
      client_seo: {
        Row: {
          id: string
          client_id: string
          title: string | null
          description: string | null
          keywords: string[]
          favicon_url: string | null
          og_image_url: string | null
          og_title: string | null
          og_description: string | null
          twitter_card: string
          twitter_title: string | null
          twitter_description: string | null
          twitter_image_url: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          client_id: string
          title?: string | null
          description?: string | null
          keywords?: string[]
          favicon_url?: string | null
          og_image_url?: string | null
          og_title?: string | null
          og_description?: string | null
          twitter_card?: string
          twitter_title?: string | null
          twitter_description?: string | null
          twitter_image_url?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          client_id?: string
          title?: string | null
          description?: string | null
          keywords?: string[]
          favicon_url?: string | null
          og_image_url?: string | null
          og_title?: string | null
          og_description?: string | null
          twitter_card?: string
          twitter_title?: string | null
          twitter_description?: string | null
          twitter_image_url?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "client_seo_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: true
            referencedRelation: "clients"
            referencedColumns: ["id"]
          }
        ]
      }
      client_settings: {
        Row: {
          id: string
          client_id: string
          settings_config: Json
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          client_id: string
          settings_config: Json
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          client_id?: string
          settings_config?: Json
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "client_settings_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: true
            referencedRelation: "clients"
            referencedColumns: ["id"]
          }
        ]
      }
      client_themes: {
        Row: {
          id: string
          client_id: string
          theme_config: Json
          is_active: boolean
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          client_id: string
          theme_config: Json
          is_active?: boolean
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          client_id?: string
          theme_config?: Json
          is_active?: boolean
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "client_themes_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          }
        ]
      }
      destinations: {
        Row: {
          coordinates: number[]
          cost: number
          created_at: string | null
          description: string
          id: string
          image: string
          name: string
          slug: string
          suggested_duration: number
          type: string
          updated_at: string | null
          client_id: string | null
        }
        Insert: {
          coordinates: number[]
          cost: number
          created_at?: string | null
          description: string
          id?: string
          image: string
          name: string
          slug: string
          suggested_duration: number
          type: string
          updated_at?: string | null
          client_id?: string | null
        }
        Update: {
          coordinates?: number[]
          cost?: number
          created_at?: string | null
          description?: string
          id?: string
          image?: string
          name?: string
          slug?: string
          suggested_duration?: number
          type?: string
          updated_at?: string | null
          client_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "destinations_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          }
        ]
      }
      points_of_interest: {
        Row: {
          coordinates: number[]
          cost: number | null
          created_at: string | null
          description: string
          duration: number | null
          id: string
          image: string | null
          location: string
          name: string
          tags: string[] | null
          type: string
          updated_at: string | null
          client_id: string | null
        }
        Insert: {
          coordinates: number[]
          cost?: number | null
          created_at?: string | null
          description: string
          duration?: number | null
          id?: string
          image?: string | null
          location: string
          name: string
          tags?: string[] | null
          type: string
          updated_at?: string | null
          client_id?: string | null
        }
        Update: {
          coordinates?: number[]
          cost?: number | null
          created_at?: string | null
          description?: string
          duration?: number | null
          id?: string
          image?: string | null
          location?: string
          name?: string
          tags?: string[] | null
          type?: string
          updated_at?: string | null
          client_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "points_of_interest_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          }
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string | null
          email: string
          full_name: string | null
          id: string
          is_admin: boolean | null
          updated_at: string | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string | null
          email: string
          full_name?: string | null
          id: string
          is_admin?: boolean | null
          updated_at?: string | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string | null
          email?: string
          full_name?: string | null
          id?: string
          is_admin?: boolean | null
          updated_at?: string | null
        }
        Relationships: []
      }
      quote_requests: {
        Row: {
          created_at: string | null
          email: string
          full_name: string
          id: string
          num_travelers: number
          phone: string | null
          route_details: Json
          special_requests: string | null
          status: string | null
          travel_date: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          email: string
          full_name: string
          id?: string
          num_travelers: number
          phone?: string | null
          route_details: Json
          special_requests?: string | null
          status?: string | null
          travel_date?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          email?: string
          full_name?: string
          id?: string
          num_travelers?: number
          phone?: string | null
          route_details?: Json
          special_requests?: string | null
          status?: string | null
          travel_date?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      saved_routes: {
        Row: {
          created_at: string | null
          destinations: Json
          distance: number | null
          duration: number | null
          id: string
          name: string
          points_of_interest: Json | null
          recommended_days: number | null
          updated_at: string | null
          user_id: string
          vehicle: Json | null
        }
        Insert: {
          created_at?: string | null
          destinations: Json
          distance?: number | null
          duration?: number | null
          id?: string
          name: string
          points_of_interest?: Json | null
          recommended_days?: number | null
          updated_at?: string | null
          user_id: string
          vehicle?: Json | null
        }
        Update: {
          created_at?: string | null
          destinations?: Json
          distance?: number | null
          duration?: number | null
          id?: string
          name?: string
          points_of_interest?: Json | null
          recommended_days?: number | null
          updated_at?: string | null
          user_id?: string
          vehicle?: Json | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
    ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never
