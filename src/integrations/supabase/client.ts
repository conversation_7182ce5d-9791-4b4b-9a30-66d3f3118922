// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://xelahnudkuyuhvyhaiqv.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhlbGFobnVka3V5dWh2eWhhaXF2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMyODU2MTksImV4cCI6MjA1ODg2MTYxOX0.8hQrLH4OHmeAu5X8-j06xY7xk0EBd3n9c-17B9okfq0";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);