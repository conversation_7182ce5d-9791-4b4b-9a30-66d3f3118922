import React from 'react';
import { Clock, MapPin, Plus, MoreHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { PointOfInterest } from '@/types/POITypes';

interface EnhancedPointOfInterestCardProps {
  poi: PointOfInterest;
  onAdd: () => void;
  isAdded?: boolean;
}

const EnhancedPointOfInterestCard: React.FC<EnhancedPointOfInterestCardProps> = ({ 
  poi, 
  onAdd,
  isAdded = false 
}) => {
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'accommodation':
        return 'bg-blue-100 text-blue-800';
      case 'activity':
        return 'bg-green-100 text-green-800';
      case 'restaurant':
        return 'bg-yellow-100 text-yellow-800';
      case 'landmark':
        return 'bg-purple-100 text-purple-800';
      case 'monument':
        return 'bg-indigo-100 text-indigo-800';
      case 'nature':
        return 'bg-emerald-100 text-emerald-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCost = (cost: number) => {
    if (cost === 0) return 'Free';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(cost);
  };
  
  const formatDuration = (hours?: number) => {
    if (!hours) return 'Flexible';
    if (hours < 1) return `${Math.round(hours * 60)} min`;
    return `${hours} hr${hours !== 1 ? 's' : ''}`;
  };

  // Helper function to render star rating based on cost (for demo purposes)
  const renderRating = (cost: number) => {
    // For demo purposes, we'll convert cost to a rating between 1-5
    // In a real app, you'd use an actual rating property
    const rating = Math.min(5, Math.max(1, Math.round(cost / 20)));
    
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map(star => (
          <svg 
            key={star} 
            className={`w-3 h-3 ${star <= rating ? 'text-yellow-400' : 'text-gray-300'}`} 
            fill="currentColor" 
            viewBox="0 0 20 20"
          >
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        ))}
      </div>
    );
  };

  return (
    <div className="bg-white rounded-lg border border-gray-100 shadow-sm hover:shadow-md hover:translate-y-[-2px] transition-all duration-300 overflow-hidden animate-fade-in">
      <div className="relative h-40">
        <img 
          src={poi.image || poi.images?.[0] || 'https://images.unsplash.com/photo-1539635278303-d4002c07eae3'} 
          alt={poi.name} 
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
        <div className="absolute bottom-3 left-3">
          <h3 className="text-lg font-semibold text-white drop-shadow-md line-clamp-1">{poi.name}</h3>
        </div>
        <div className="absolute top-3 right-3">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(poi.type)}`}>
            {poi.type}
          </span>
        </div>
      </div>
      
      <div className="p-4">
        <div className="flex justify-between items-start mb-3">
          <div className="flex items-center text-sm text-gray-500">
            <MapPin className="w-3 h-3 mr-1 text-morocco-terracotta" />
            <span className="line-clamp-1">{poi.location}</span>
          </div>
          <div className="text-sm font-medium text-morocco-terracotta bg-morocco-terracotta/10 px-2 py-1 rounded-full">
            {formatCost(poi.cost || 0)}
          </div>
        </div>
        
        <p className="text-sm text-gray-600 mb-3 line-clamp-2">
          {poi.description}
        </p>
        
        <div className="bg-gray-50 p-3 rounded-md mb-3">
          <div className="flex justify-between items-center">
            <div className="flex items-center text-sm text-gray-500">
              <Clock className="w-3 h-3 mr-1 text-morocco-terracotta" />
              <span>{formatDuration(poi.duration)}</span>
            </div>
            {renderRating(poi.cost || 0)}
          </div>
        </div>
        
        <div className="flex flex-wrap gap-1 mt-3">
          {poi.tags?.slice(0, 3).map((tag: string, index: number) => (
            <span 
              key={index}
              className="px-2 py-0.5 bg-gray-100 hover:bg-gray-200 rounded-full text-xs text-gray-600 cursor-pointer transition-colors"
            >
              {tag}
            </span>
          ))}
          {poi.tags && poi.tags.length > 3 && (
            <span className="px-2 py-0.5 bg-gray-100 hover:bg-gray-200 rounded-full text-xs text-gray-600 cursor-pointer transition-colors">
              +{poi.tags.length - 3}
            </span>
          )}
        </div>
      </div>
      
      <div className="flex border-t border-gray-100">
        <Button
          onClick={onAdd}
          disabled={isAdded}
          className={`flex-1 rounded-none py-3 ${isAdded ? 'bg-green-500 hover:bg-green-600' : 'bg-morocco-terracotta hover:bg-morocco-terracotta/90'}`}
          size="sm"
        >
          {isAdded ? 'Added to Itinerary' : (
            <span className="flex items-center justify-center">
              <Plus className="w-4 h-4 mr-1" />
              Add to Itinerary
            </span>
          )}
        </Button>
        <Button
          className="bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-none py-3 px-3"
          size="sm"
        >
          <MoreHorizontal className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
};

export default EnhancedPointOfInterestCard;