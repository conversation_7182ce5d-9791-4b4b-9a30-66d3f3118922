import React from 'react';
import { ItineraryBalance, analyzeItineraryBalance } from '@/utils/trip-templates';
import { Destination, PointOfInterest } from '@/types';
import { AlertTriangle, Check, Info, Compass } from 'lucide-react';

interface ItineraryBalanceAnalysisProps {
  selectedCities: Destination[];
  selectedPOIs: PointOfInterest[];
}

const ItineraryBalanceAnalysis: React.FC<ItineraryBalanceAnalysisProps> = ({
  selectedCities,
  selectedPOIs
}) => {
  // No analysis needed if no cities selected
  if (selectedCities.length === 0) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md text-center">
        <Info className="h-10 w-10 text-gray-400 mx-auto mb-2" />
        <h3 className="text-lg font-semibold mb-2">No Itinerary Analysis</h3>
        <p className="text-gray-600">
          Select destinations to see itinerary balance analysis.
        </p>
      </div>
    );
  }
  
  // Get itinerary balance analysis
  const analysis = analyzeItineraryBalance(selectedCities, selectedPOIs);
  
  // Determine overall balance status
  const isWellBalanced = 
    analysis.recommendations.length <= 1 &&
    Math.abs(analysis.urbanRuralBalance - 50) < 30 &&
    analysis.relaxationScore > 20;
    
  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold">Itinerary Balance Analysis</h2>
        {isWellBalanced ? (
          <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium flex items-center">
            <Check className="w-4 h-4 mr-1" />
            Well Balanced
          </span>
        ) : (
          <span className="px-3 py-1 bg-amber-100 text-amber-800 rounded-full text-sm font-medium flex items-center">
            <AlertTriangle className="w-4 h-4 mr-1" />
            Needs Adjustment
          </span>
        )}
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        {/* Cultural Score */}
        <div className="p-4 border rounded-lg">
          <h3 className="text-sm font-medium text-gray-500 mb-1">Cultural Experience</h3>
          <div className="flex items-center justify-between">
            <span className="text-2xl font-bold">{analysis.culturalScore}%</span>
            <ScoreIndicator score={analysis.culturalScore} />
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
            <div 
              className="bg-indigo-600 h-2 rounded-full" 
              style={{ width: `${analysis.culturalScore}%` }}
            ></div>
          </div>
        </div>
        
        {/* Adventure Score */}
        <div className="p-4 border rounded-lg">
          <h3 className="text-sm font-medium text-gray-500 mb-1">Adventure Experience</h3>
          <div className="flex items-center justify-between">
            <span className="text-2xl font-bold">{analysis.adventureScore}%</span>
            <ScoreIndicator score={analysis.adventureScore} />
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
            <div 
              className="bg-green-600 h-2 rounded-full" 
              style={{ width: `${analysis.adventureScore}%` }}
            ></div>
          </div>
        </div>
        
        {/* Relaxation Score */}
        <div className="p-4 border rounded-lg">
          <h3 className="text-sm font-medium text-gray-500 mb-1">Relaxation</h3>
          <div className="flex items-center justify-between">
            <span className="text-2xl font-bold">{analysis.relaxationScore}%</span>
            <ScoreIndicator score={analysis.relaxationScore} />
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
            <div 
              className="bg-blue-500 h-2 rounded-full" 
              style={{ width: `${analysis.relaxationScore}%` }}
            ></div>
          </div>
        </div>
      </div>
      
      {/* Urban/Rural Balance */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-gray-500 mb-2">Urban / Rural Balance</h3>
        <div className="relative w-full h-6 bg-gray-200 rounded-full overflow-hidden">
          <div 
            className="absolute left-0 top-0 h-6 bg-morocco-terracotta opacity-20" 
            style={{ width: '100%' }}
          ></div>
          <div 
            className="absolute left-0 top-0 h-6 bg-gradient-to-r from-blue-500 to-green-500" 
            style={{ width: `${analysis.urbanRuralBalance}%` }}
          ></div>
          <div 
            className="absolute h-full w-0.5 bg-white left-1/2 transform -translate-x-1/2"
          ></div>
          <div className="absolute inset-0 flex justify-between items-center px-2 text-xs text-white font-medium">
            <span>Urban</span>
            <span>Rural</span>
          </div>
          <div 
            className="absolute h-4 w-4 rounded-full bg-white border-2 border-morocco-terracotta top-1 transform -translate-x-1/2"
            style={{ left: `${analysis.urbanRuralBalance}%` }}
          ></div>
        </div>
      </div>
      
      {/* Pace Analysis */}
      <div className="mb-6 p-4 border rounded-lg bg-gray-50">
        <div className="flex items-start">
          <Compass className="w-5 h-5 text-morocco-terracotta mt-0.5 mr-2 flex-shrink-0" />
          <div>
            <h3 className="text-sm font-medium text-gray-700">Travel Pace Analysis</h3>
            <p className="text-sm text-gray-600 mt-1">{analysis.paceAnalysis}</p>
          </div>
        </div>
      </div>
      
      {/* Recommendations */}
      {analysis.recommendations.length > 0 && (
        <div className="border-t pt-4">
          <h3 className="text-sm font-medium text-gray-700 mb-2">Recommendations for Better Balance</h3>
          <ul className="space-y-2">
            {analysis.recommendations.map((recommendation, index) => (
              <li key={index} className="flex items-start">
                <AlertTriangle className="w-4 h-4 text-amber-500 mt-0.5 mr-2 flex-shrink-0" />
                <span className="text-sm text-gray-600">{recommendation}</span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

// Helper component for score indicator
const ScoreIndicator: React.FC<{ score: number }> = ({ score }) => {
  if (score >= 70) {
    return <span className="text-xs px-2 py-0.5 bg-green-100 text-green-800 rounded">High</span>;
  } else if (score >= 30) {
    return <span className="text-xs px-2 py-0.5 bg-blue-100 text-blue-800 rounded">Balanced</span>;
  } else {
    return <span className="text-xs px-2 py-0.5 bg-amber-100 text-amber-800 rounded">Low</span>;
  }
};

export default ItineraryBalanceAnalysis; 