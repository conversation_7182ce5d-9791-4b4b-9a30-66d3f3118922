
import React from 'react';
import { useForm } from 'react-hook-form';
import { Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';

export interface TripDetails {
  duration: string;
  travelers: string;
  budget: string;
  interests: string[];
  startDate: string;
}

interface TripDetailsFormProps {
  onSubmit: (data: TripDetails) => void;
  className?: string;
}

const TripDetailsForm: React.FC<TripDetailsFormProps> = ({ onSubmit, className }) => {
  const form = useForm<TripDetails>({
    defaultValues: {
      duration: '4-7',
      travelers: '2',
      budget: 'mid',
      interests: ['cultural'],
      startDate: '',
    }
  });

  const handleSubmit = (data: TripDetails) => {
    onSubmit(data);
  };

  return (
    <div className={cn('bg-white rounded-lg shadow-md p-6', className)}>
      <div className="flex items-center mb-4">
        <Calendar className="h-6 w-6 text-morocco-terracotta mr-2" />
        <h2 className="text-xl font-bold text-morocco-dark">Plan Your Moroccan Adventure</h2>
      </div>
      
      <p className="text-gray-600 mb-6">
        Tell us about your trip so we can tailor the perfect Moroccan journey for you.
      </p>
      
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="duration"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Trip Duration</FormLabel>
                <Select 
                  onValueChange={field.onChange} 
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select trip duration" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="1-3">1-3 days (Short Trip)</SelectItem>
                    <SelectItem value="4-7">4-7 days (Standard)</SelectItem>
                    <SelectItem value="8-14">8-14 days (Extended)</SelectItem>
                    <SelectItem value="15+">15+ days (Comprehensive)</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  How many days are you planning to stay?
                </FormDescription>
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="travelers"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Number of Travelers</FormLabel>
                <FormControl>
                  <Input type="number" min="1" max="20" {...field} />
                </FormControl>
                <FormDescription>
                  How many people will be traveling?
                </FormDescription>
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="budget"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Budget Range</FormLabel>
                <Select 
                  onValueChange={field.onChange} 
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select budget range" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="budget">Budget-friendly</SelectItem>
                    <SelectItem value="mid">Mid-range</SelectItem>
                    <SelectItem value="luxury">Luxury</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  What's your approximate budget for this trip?
                </FormDescription>
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="startDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Tentative Start Date</FormLabel>
                <FormControl>
                  <Input type="date" {...field} />
                </FormControl>
                <FormDescription>
                  When do you plan to start your Moroccan adventure?
                </FormDescription>
              </FormItem>
            )}
          />
          
          <div className="pt-4">
            <Button type="submit" className="w-full bg-morocco-terracotta hover:bg-morocco-terracotta/90">
              Start Planning Your Route
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

// Mobile version of the form inside a slide-up sheet
export const MobileTripDetailsForm: React.FC<TripDetailsFormProps> = (props) => {
  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button className="w-full bg-morocco-terracotta hover:bg-morocco-terracotta/90">
          Plan Your Moroccan Adventure
        </Button>
      </SheetTrigger>
      <SheetContent side="bottom" className="h-[90vh]">
        <SheetHeader>
          <SheetTitle className="flex items-center">
            <Calendar className="h-5 w-5 text-morocco-terracotta mr-2" />
            Plan Your Moroccan Adventure
          </SheetTitle>
          <SheetDescription>
            Tell us about your trip so we can tailor the perfect Moroccan journey for you.
          </SheetDescription>
        </SheetHeader>
        <div className="py-6">
          <TripDetailsForm {...props} />
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default TripDetailsForm;
