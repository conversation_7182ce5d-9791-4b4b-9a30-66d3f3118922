import React, { useState } from 'react';
import { ProgressIndicator, CompactProgressIndicator } from './ui/progress-indicator';
import { Button } from './ui/button';

const ProgressExample: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 4;
  
  const tripSteps = [
    "Select Cities", 
    "Add Points of Interest", 
    "Choose Vehicle", 
    "Get Quote"
  ];
  
  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(prevStep => prevStep + 1);
    }
  };
  
  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(prevStep => prevStep - 1);
    }
  };
  
  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-8 text-center">Morocco Trip Planner</h1>
      
      <section className="mb-12">
        <h2 className="text-xl font-semibold mb-4">Desktop Progress Indicator</h2>
        <div className="bg-white rounded-lg shadow-md p-6 mb-4">
          <ProgressIndicator 
            currentStep={currentStep} 
            totalSteps={totalSteps} 
            labels={tripSteps} 
          />
        </div>
        
        <div className="mt-8 flex justify-center gap-4">
          <Button
            onClick={handlePrevious}
            disabled={currentStep === 1}
            variant="outline"
          >
            Previous Step
          </Button>
          <Button
            onClick={handleNext}
            disabled={currentStep === totalSteps}
          >
            Next Step
          </Button>
        </div>
      </section>
      
      <section className="mb-12">
        <h2 className="text-xl font-semibold mb-4">Mobile Progress Indicator</h2>
        <div className="bg-slate-100 rounded-lg p-6 flex justify-center">
          <div className="bg-white rounded-lg shadow-md p-4 max-w-xs w-full">
            <div className="mb-4 flex justify-center">
              <CompactProgressIndicator 
                currentStep={currentStep} 
                totalSteps={totalSteps} 
              />
            </div>
            
            <h3 className="text-lg font-medium text-center mb-4">
              Step {currentStep}: {tripSteps[currentStep - 1]}
            </h3>
            
            <p className="text-sm text-gray-600 text-center">
              {currentStep === 1 && "Choose the cities you want to visit during your trip."}
              {currentStep === 2 && "Select interesting places and activities to add to your itinerary."}
              {currentStep === 3 && "Pick the right vehicle for your journey."}
              {currentStep === 4 && "Review your selections and get a quote for your trip."}
            </p>
          </div>
        </div>
      </section>
      
      <section>
        <h2 className="text-xl font-semibold mb-4">No Labels Variation</h2>
        <div className="bg-white rounded-lg shadow-md p-6">
          <ProgressIndicator 
            currentStep={currentStep} 
            totalSteps={totalSteps}
          />
          <div className="mt-4 text-center text-lg font-medium">
            Step {currentStep} of {totalSteps}
          </div>
        </div>
      </section>
    </div>
  );
};

export default ProgressExample; 