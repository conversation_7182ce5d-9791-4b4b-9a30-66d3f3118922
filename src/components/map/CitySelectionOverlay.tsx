import React from 'react';
import { MapPin, Plus, Minus } from 'lucide-react';
import { Destination } from '@/types/POITypes';
import { TravelPace, JourneyStyle } from '@/types/ItineraryParameters';
import JourneyParametersForm from './JourneyParametersForm';

interface CitySelectionOverlayProps {
  availableCities: Destination[];
  selectedCities: Destination[];
  onCitySelect: (city: Destination) => void;
  onCityDeselect: (city: Destination) => void;
  currentRegion?: string;
  // Journey parameters
  numberOfTravelDays: number;
  onNumberOfTravelDaysChange: (days: number) => void;
  travelStyle: TravelPace;
  onTravelStyleChange: (style: TravelPace) => void;
  journeyStyle: JourneyStyle;
  onJourneyStyleChange: (style: JourneyStyle) => void;
  // City day allocations
  cityDayAllocations?: Record<string, number>;
  onCityDayAllocationChange?: (cityName: string, days: number) => void;
}

const CitySelectionOverlay: React.FC<CitySelectionOverlayProps> = ({
  availableCities,
  selectedCities,
  onCitySelect,
  onCityDeselect,
  currentRegion = 'morocco',
  numberOfTravelDays,
  onNumberOfTravelDaysChange,
  travelStyle,
  onTravelStyleChange,
  journeyStyle,
  onJourneyStyleChange,
  cityDayAllocations = {},
  onCityDayAllocationChange
}) => {

  // Define main cities for different regions
  const getMainCities = (region: string): string[] => {
    switch (region.toLowerCase()) {
      case 'morocco':
        return ['Marrakech', 'Casablanca', 'Fez', 'Rabat', 'Chefchaouen', 'Essaouira'];
      case 'portugal':
        return ['Lisbon', 'Porto', 'Coimbra', 'Braga', 'Aveiro', 'Óbidos'];
      default:
        return [];
    }
  };

  const mainCityNames = getMainCities(currentRegion);

  // Separate main cities from others based on current region
  const mainCities = availableCities.filter(city =>
    mainCityNames.includes(city.name)
  );
  const otherCities = availableCities.filter(city =>
    !mainCityNames.includes(city.name)
  );

  return (
    <div
      style={{
        position: 'absolute',
        top: '1rem',
        left: '1rem',
        right: '1rem',
        zIndex: 30,
        backgroundColor: 'rgba(255, 255, 255, 0.85)',
        backdropFilter: 'blur(12px)',
        borderRadius: '12px',
        border: '1px solid rgba(0, 0, 0, 0.1)',
        boxShadow: '0 2px 12px rgba(0, 0, 0, 0.1)',
        padding: '0.75rem',
        display: 'flex',
        flexDirection: 'column',
        gap: '0.5rem',
        maxWidth: '100%',
        overflow: 'hidden'
      }}
      className="city-selection-overlay"
    >
      {/* Journey Parameters Section - Ultra Compact */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '0.5rem',
        flexWrap: 'wrap',
        padding: '0.5rem',
        backgroundColor: 'rgba(59, 130, 246, 0.08)',
        borderRadius: '8px',
        border: '1px solid rgba(59, 130, 246, 0.15)',
        minHeight: 'auto',
        overflow: 'hidden'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem',
          flexWrap: 'wrap',
          width: '100%',
          justifyContent: 'space-between'
        }}>
          <JourneyParametersForm
            travelStyle={travelStyle}
            onTravelStyleChange={onTravelStyleChange}
            availableTravelStyles={[
              { value: 'slow-immersive', label: 'Slow & Immersive' },
              { value: 'balanced-explorer', label: 'Balanced Explorer' },
              { value: 'maximum-discovery', label: 'Maximum Discovery' }
            ]}
            selectedTravelInterests={[]}
            onTravelInterestChange={() => {}}
            availableTravelInterests={[]}
            compact={true}
            journeyStyle={journeyStyle}
            onJourneyStyleChange={(style: string) => onJourneyStyleChange(style as JourneyStyle)}
            availableJourneyStyles={[
              { value: 'scenic-routes', label: 'Scenic Routes' },
              { value: 'cultural-deep-dive', label: 'Cultural Deep-dive' },
              { value: 'adventure-seeker', label: 'Adventure Seeker' },
              { value: 'photography-tour', label: 'Photography Tour' },
              { value: 'hidden-gems', label: 'Hidden Gems' },
              { value: 'local-immersion', label: 'Local Immersion' }
            ]}
            calculatedTotalDays={numberOfTravelDays}
          />
        </div>
      </div>

      {/* City Selection Section */}
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem',
          flexWrap: 'wrap',
          padding: '0.5rem',
          backgroundColor: 'rgba(139, 26, 24, 0.08)',
          borderRadius: '8px',
          border: '1px solid rgba(139, 26, 24, 0.15)',
          overflow: 'hidden'
        }}
      >
      {/* Cities Label */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '0.5rem',
        flexShrink: 0
      }}>
        <MapPin size={16} style={{ color: 'var(--morocco-blue)' }} />
        <span style={{
          fontSize: '0.875rem',
          fontWeight: 600,
          color: 'var(--morocco-blue)',
          textTransform: 'uppercase',
          letterSpacing: '0.5px'
        }}>
          Cities:
        </span>
      </div>

      {/* Main Cities - Larger Swatches with Day Controls */}
      {mainCities.map(city => {
        const isSelected = selectedCities.some(c => c.id === city.id);
        const selectionOrder = selectedCities.findIndex(c => c.id === city.id) + 1;
        const cityDays = cityDayAllocations[city.name] || Math.ceil(numberOfTravelDays / Math.max(selectedCities.length, 1));

        return (
          <div key={city.id} style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '0.25rem',
            alignItems: 'center'
          }}>
            <button
              onClick={() => {
                if (isSelected) {
                  onCityDeselect(city);
                } else {
                  onCitySelect(city);
                }
              }}
              style={{
                padding: '0.5rem 1rem',
                border: isSelected ? '2px solid var(--morocco-blue)' : '1px solid rgba(0, 71, 171, 0.2)',
                borderRadius: 'var(--radius-full)',
                background: isSelected ? 'rgba(0, 71, 171, 0.1)' : 'rgba(255, 255, 255, 0.9)',
                cursor: 'pointer',
                fontSize: '0.8rem',
                fontWeight: isSelected ? 700 : 600,
                color: isSelected ? 'var(--morocco-blue)' : 'var(--text-primary)',
                textAlign: 'center',
                transition: 'all 0.2s ease',
                position: 'relative',
                textTransform: 'uppercase',
                letterSpacing: '0.5px',
                flexShrink: 0,
                whiteSpace: 'nowrap'
              }}
              onMouseEnter={(e) => {
                if (!isSelected) {
                  e.currentTarget.style.borderColor = 'var(--morocco-blue)';
                  e.currentTarget.style.backgroundColor = 'rgba(0, 71, 171, 0.1)';
                  e.currentTarget.style.transform = 'translateY(-1px)';
                }
              }}
              onMouseLeave={(e) => {
                if (!isSelected) {
                  e.currentTarget.style.borderColor = 'rgba(0, 71, 171, 0.2)';
                  e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
                  e.currentTarget.style.transform = 'translateY(0)';
                }
              }}
            >
              {city.name}
              {isSelected && (
                <div style={{
                  position: 'absolute',
                  top: '-6px',
                  right: '-6px',
                  width: '20px',
                  height: '20px',
                  backgroundColor: 'var(--morocco-red)',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: '2px solid white',
                  boxShadow: '0 2px 6px rgba(0,0,0,0.2)'
                }}>
                  <span style={{
                    color: 'white',
                    fontSize: '0.75rem',
                    fontWeight: 700,
                    fontFamily: 'var(--font-heading)'
                  }}>
                    {selectionOrder}
                  </span>
                </div>
              )}
            </button>

            {/* Day Allocation Controls - Only show for selected cities */}
            {isSelected && onCityDayAllocationChange && (
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.25rem',
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                borderRadius: 'var(--radius-sm)',
                padding: '0.25rem 0.5rem',
                border: '1px solid rgba(0, 71, 171, 0.2)'
              }}>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    if (cityDays > 1) {
                      onCityDayAllocationChange(city.name, cityDays - 1);
                    }
                  }}
                  style={{
                    width: '16px',
                    height: '16px',
                    borderRadius: '50%',
                    border: '1px solid var(--morocco-blue)',
                    backgroundColor: 'white',
                    color: 'var(--morocco-blue)',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '10px'
                  }}
                >
                  <Minus size={8} />
                </button>
                <span style={{
                  fontSize: '0.7rem',
                  fontWeight: 600,
                  color: 'var(--morocco-blue)',
                  minWidth: '20px',
                  textAlign: 'center'
                }}>
                  {cityDays}d
                </span>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    if (cityDays < 15) { // Allow up to 15 days per city
                      onCityDayAllocationChange(city.name, cityDays + 1);
                    }
                  }}
                  style={{
                    width: '16px',
                    height: '16px',
                    borderRadius: '50%',
                    border: '1px solid var(--morocco-blue)',
                    backgroundColor: 'white',
                    color: 'var(--morocco-blue)',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '10px'
                  }}
                >
                  <Plus size={8} />
                </button>
              </div>
            )}
          </div>
        );
      })}

      {/* Other Cities - Smaller Swatches */}
      {otherCities.map(city => {
        const isSelected = selectedCities.some(c => c.id === city.id);
        const selectionOrder = selectedCities.findIndex(c => c.id === city.id) + 1;
        return (
          <button
            key={city.id}
            onClick={() => {
              if (isSelected) {
                onCityDeselect(city);
              } else {
                onCitySelect(city);
              }
            }}
            style={{
              padding: '0.375rem 0.75rem',
              border: isSelected ? '2px solid var(--morocco-blue)' : '1px solid rgba(0, 71, 171, 0.2)',
              borderRadius: 'var(--radius-full)',
              background: isSelected ? 'rgba(0, 71, 171, 0.1)' : 'rgba(255, 255, 255, 0.8)',
              cursor: 'pointer',
              fontSize: '0.7rem',
              fontWeight: isSelected ? 600 : 500,
              color: isSelected ? 'var(--morocco-blue)' : 'var(--text-secondary)',
              textAlign: 'center',
              transition: 'all 0.2s ease',
              position: 'relative',
              textTransform: 'uppercase',
              letterSpacing: '0.5px',
              flexShrink: 0,
              whiteSpace: 'nowrap'
            }}
            onMouseEnter={(e) => {
              if (!isSelected) {
                e.currentTarget.style.borderColor = 'var(--morocco-blue)';
                e.currentTarget.style.backgroundColor = 'rgba(0, 71, 171, 0.1)';
                e.currentTarget.style.color = 'var(--morocco-blue)';
                e.currentTarget.style.transform = 'translateY(-1px)';
              }
            }}
            onMouseLeave={(e) => {
              if (!isSelected) {
                e.currentTarget.style.borderColor = 'rgba(0, 71, 171, 0.2)';
                e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
                e.currentTarget.style.color = 'var(--text-secondary)';
                e.currentTarget.style.transform = 'translateY(0)';
              }
            }}
          >
            {city.name}
            {isSelected && (
              <div style={{
                position: 'absolute',
                top: '-4px',
                right: '-4px',
                width: '16px',
                height: '16px',
                backgroundColor: 'var(--morocco-red)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                border: '2px solid white',
                boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
              }}>
                <span style={{
                  color: 'white',
                  fontSize: '0.625rem',
                  fontWeight: 700,
                  fontFamily: 'var(--font-heading)'
                }}>
                  {selectionOrder}
                </span>
              </div>
            )}
          </button>
        );
      })}
      </div>
    </div>
  );
};

export default CitySelectionOverlay;
