import React from 'react';

// Updated to avoid any styling conflicts with index.html
const MapStyles: React.FC = () => {
  return (
    <style>
      {`
        /* Map container styles */
        .mapboxgl-map {
          position: relative;
          z-index: 1;
        }

        /* Popup styles */
        .mapboxgl-popup {
          max-width: 280px;
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
          z-index: 6000 !important; /* Above all markers */
        }
        
        .mapboxgl-popup-content {
          padding: 12px;
          border-radius: 8px;
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
          border: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        /* Marker tooltip */
        .marker-tooltip {
          position: absolute;
          bottom: calc(100% + 8px);
          left: 50%;
          transform: translateX(-50%);
          z-index: 7000 !important; /* Above markers and popups */
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          gap: 6px;
          max-width: 90%;
          background-color: rgba(255, 255, 255, 0.9);
          padding: 8px 12px;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          pointer-events: auto;
        }
        
        /* City tags */
        .city-tag {
          padding: 4px 10px;
          cursor: pointer;
        }
        
        .city-tag.selected {
          background-color: #41B3A3;
          color: white;
        }
        
        .city-tag-number {
          background-color: #e9ecef;
          color: #495057;
          border-radius: 12px;
          padding: 2px 8px;
          font-size: 12px;
          font-weight: 600;
        }
        
        .city-tag.selected .city-tag-number {
          background-color: rgba(255, 255, 255, 0.2);
          color: white;
        }
      `}
    </style>
  );
};

export default MapStyles;
