import React, { useEffect, useState } from 'react';
import mapboxgl from 'mapbox-gl';
import { Destination } from '@/data/destinations';
import { logger } from '@/utils/debugLogger';

interface POIConnectionLineProps {
  map: mapboxgl.Map;
  poiId: string;
  poiCoordinates: [number, number];
  destinationCoordinates: [number, number] | Destination;
  safeRemoveLayer: (layerId: string) => boolean;
  safeRemoveSource: (sourceId: string) => boolean;
}

const POIConnectionLine: React.FC<POIConnectionLineProps> = ({
  map,
  poiId,
  poiCoordinates,
  destinationCoordinates,
  safeRemoveLayer,
  safeRemoveSource
}) => {
  const [isMapReady, setIsMapReady] = useState(false);

  // Listen for map ready state
  useEffect(() => {
    if (!map) return;
    
    // Check if map is already ready
    const checkMapReady = () => {
      const isLoaded = map.loaded();
      const hasCenter = map.getCenter() && map.getCenter().lng && map.getCenter().lat;
      const hasZoom = map.getZoom() > 0;
      const hasStyle = map.isStyleLoaded();
      
      if (isLoaded && hasCenter && hasZoom && hasStyle) {
        setIsMapReady(true);
      } else {
        setTimeout(checkMapReady, 200);
      }
    };
    
    // Handler for the custom event
    const handleMapReady = (e: CustomEvent) => {
      setIsMapReady(true);
    };

    // Add event listener for map ready event
    document.addEventListener('mapFullyReady', handleMapReady as EventListener);
    
    // Initial check
    checkMapReady();
    
    // Cleanup
    return () => {
      document.removeEventListener('mapFullyReady', handleMapReady as EventListener);
    };
  }, [map]);

  useEffect(() => {
    // Early return if map is not ready
    if (!map || !isMapReady) {
      logger.logMapEvent(`POIConnectionLine: Skipping line creation for ${poiId} - map not ready`);
      return;
    }
    
    const sourceId = `poi-line-${poiId}`;
    const layerId = `poi-line-layer-${poiId}`;
    
    // Safely remove any existing layers and sources
    safeRemoveLayer(layerId);
    safeRemoveSource(sourceId);

    // Get the actual coordinates from the destination if it's an object
    const destCoords: [number, number] = Array.isArray(destinationCoordinates) 
      ? destinationCoordinates 
      : destinationCoordinates.coordinates;
    
    // Validate coordinates are within expected ranges
    if (
      poiCoordinates[0] < -180 || poiCoordinates[0] > 180 || 
      poiCoordinates[1] < -90 || poiCoordinates[1] > 90 ||
      destCoords[0] < -180 || destCoords[0] > 180 || 
      destCoords[1] < -90 || destCoords[1] > 90
    ) {
      logger.logMapEvent('POIConnectionLine: Invalid coordinates', {
        poiId,
        poiCoordinates,
        destCoords
      });
      return;
    }
    
    try {
      logger.logMapEvent(`POIConnectionLine: Creating line for ${poiId}`, {
        from: poiCoordinates,
        to: destCoords
      });
      
      // Add source
      map.addSource(sourceId, {
        type: 'geojson',
        data: {
          type: 'Feature',
          properties: {},
          geometry: {
            type: 'LineString',
            coordinates: [poiCoordinates, destCoords]
          }
        }
      });
      
      // Add visible layer
      map.addLayer({
        id: layerId,
        type: 'line',
        source: sourceId,
        layout: {
          'line-join': 'round',
          'line-cap': 'round'
        },
        paint: {
          'line-color': '#D84727',
          'line-width': 2,
          'line-dasharray': [0.5, 1.5]
        }
      });
      
      logger.logMapEvent(`POIConnectionLine: Successfully created line for ${poiId}`);
    } catch (error) {
      logger.logMapEvent(`POIConnectionLine: Error creating line for ${poiId}`, {
        error: String(error)
      });
    }
    
    // Cleanup on unmount
    return () => {
      logger.logMapEvent(`POIConnectionLine: Removing line for ${poiId}`);
      safeRemoveLayer(layerId);
      safeRemoveSource(sourceId);
    };
  }, [map, poiId, poiCoordinates, destinationCoordinates, safeRemoveLayer, safeRemoveSource, isMapReady]);

  return null;
};

export default POIConnectionLine;
