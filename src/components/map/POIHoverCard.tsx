import React, { useState, useEffect } from 'react';
import { PointOfInterest } from '@/types/POITypes';
import { MapPin, Clock, Star, DollarSign, ChevronLeft, ChevronRight } from 'lucide-react';

interface POIHoverCardProps {
  poi: PointOfInterest | null;
  isVisible: boolean;
  position: { x: number; y: number };
  onClose?: () => void;
  onViewDetails?: (poi: PointOfInterest) => void;
}

const POIHoverCard: React.FC<POIHoverCardProps> = ({
  poi,
  isVisible,
  position,
  onClose,
  onViewDetails
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isImageLoading, setIsImageLoading] = useState(true);

  useEffect(() => {
    if (isVisible && poi) {
      setCurrentImageIndex(0);
      setIsImageLoading(true);
    }
  }, [isVisible, poi]);

  if (!isVisible || !poi) {
    return null;
  }

  const images = poi.images || [];
  const hasMultipleImages = images.length > 1;
  const currentImage = images[currentImageIndex] || '/images/placeholder.svg';

  const nextImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (hasMultipleImages) {
      setCurrentImageIndex((prev) => (prev + 1) % images.length);
      setIsImageLoading(true);
    }
  };

  const prevImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (hasMultipleImages) {
      setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
      setIsImageLoading(true);
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'landmark': return 'var(--morocco-yellow)';
      case 'cultural': return 'var(--morocco-blue)';
      case 'nature': return 'var(--morocco-green)';
      case 'restaurant': return 'var(--morocco-red)';
      case 'accommodation': return 'var(--morocco-purple)';
      default: return 'var(--morocco-blue)';
    }
  };

  // Calculate position to keep card on screen
  const cardWidth = 280;
  const cardHeight = 200;
  const adjustedX = Math.min(position.x, window.innerWidth - cardWidth - 20);
  const adjustedY = Math.min(position.y, window.innerHeight - cardHeight - 20);

  return (
    <div
      className="poi-hover-card"
      style={{
        position: 'fixed',
        left: `${adjustedX}px`,
        top: `${adjustedY}px`,
        width: `${cardWidth}px`,
        backgroundColor: 'white',
        borderRadius: 'var(--radius-lg)',
        boxShadow: '0 10px 40px rgba(0, 0, 0, 0.15)',
        border: '1px solid var(--border-light)',
        zIndex: 500,
        overflow: 'hidden',
        transform: 'translateY(-10px)',
        animation: 'slideIn 0.2s ease-out forwards',
        pointerEvents: 'auto'
      }}
      onMouseLeave={onClose}
    >
      {/* Image section */}
      <div style={{ position: 'relative', height: '120px', overflow: 'hidden' }}>
        <img
          src={currentImage}
          alt={poi.name}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            opacity: isImageLoading ? 0 : 1,
            transition: 'opacity 0.3s ease'
          }}
          onLoad={() => setIsImageLoading(false)}
          onError={(e) => {
            e.currentTarget.src = '/images/placeholder.svg';
            setIsImageLoading(false);
          }}
        />
        
        {/* Loading overlay */}
        {isImageLoading && (
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 71, 171, 0.1)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <div style={{
              width: '20px',
              height: '20px',
              border: '2px solid rgba(0, 71, 171, 0.3)',
              borderTop: '2px solid var(--morocco-blue)',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite'
            }} />
          </div>
        )}

        {/* Image navigation for multiple images */}
        {hasMultipleImages && !isImageLoading && (
          <>
            <button
              onClick={prevImage}
              style={{
                position: 'absolute',
                left: '0.5rem',
                top: '50%',
                transform: 'translateY(-50%)',
                backgroundColor: 'rgba(0, 0, 0, 0.6)',
                color: 'white',
                border: 'none',
                borderRadius: '50%',
                width: '24px',
                height: '24px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '12px'
              }}
            >
              <ChevronLeft size={14} />
            </button>
            <button
              onClick={nextImage}
              style={{
                position: 'absolute',
                right: '0.5rem',
                top: '50%',
                transform: 'translateY(-50%)',
                backgroundColor: 'rgba(0, 0, 0, 0.6)',
                color: 'white',
                border: 'none',
                borderRadius: '50%',
                width: '24px',
                height: '24px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '12px'
              }}
            >
              <ChevronRight size={14} />
            </button>
            
            {/* Image indicators */}
            <div style={{
              position: 'absolute',
              bottom: '0.5rem',
              left: '50%',
              transform: 'translateX(-50%)',
              display: 'flex',
              gap: '0.25rem'
            }}>
              {images.map((_, index) => (
                <div
                  key={index}
                  style={{
                    width: '4px',
                    height: '4px',
                    borderRadius: '50%',
                    backgroundColor: index === currentImageIndex ? 'white' : 'rgba(255, 255, 255, 0.5)'
                  }}
                />
              ))}
            </div>
          </>
        )}

        {/* Category badge */}
        <div style={{
          position: 'absolute',
          top: '0.5rem',
          left: '0.5rem',
          backgroundColor: getCategoryColor(poi.category || 'other'),
          color: 'white',
          padding: '0.25rem 0.5rem',
          borderRadius: 'var(--radius-sm)',
          fontSize: '0.75rem',
          fontWeight: 600,
          textTransform: 'capitalize'
        }}>
          {poi.category || 'POI'}
        </div>
      </div>

      {/* Content section */}
      <div style={{ padding: '1rem' }}>
        <h3 style={{
          margin: '0 0 0.5rem',
          fontSize: '1rem',
          fontWeight: 600,
          color: 'var(--text-primary)',
          fontFamily: 'var(--font-heading)',
          lineHeight: 1.2
        }}>
          {poi.name}
        </h3>
        
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem', marginBottom: '0.5rem' }}>
          <MapPin size={12} color="var(--text-secondary)" />
          <span style={{ fontSize: '0.75rem', color: 'var(--text-secondary)' }}>
            {poi.location || 'Morocco'}
          </span>
        </div>

        {poi.description && (
          <p style={{
            margin: '0 0 0.75rem',
            fontSize: '0.75rem',
            lineHeight: 1.4,
            color: 'var(--text-secondary)',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden'
          }}>
            {poi.description}
          </p>
        )}

        {/* Quick info */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', fontSize: '0.75rem' }}>
          {(poi as any).duration && (
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
              <Clock size={12} color="var(--morocco-blue)" />
              <span>{(poi as any).duration}h</span>
            </div>
          )}
          
          {(poi as any).cost && (
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
              <DollarSign size={12} color="var(--morocco-red)" />
              <span>${(poi as any).cost}</span>
            </div>
          )}
          
          {(poi as any).rating && (
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
              <Star size={12} color="var(--morocco-yellow)" />
              <span>{(poi as any).rating}</span>
            </div>
          )}
        </div>

        {/* View Details Button */}
        <button
          onClick={(e) => {
            e.stopPropagation();
            if (onViewDetails && poi) {
              onViewDetails(poi);
            }
          }}
          style={{
            marginTop: '0.75rem',
            width: '100%',
            padding: '0.5rem',
            backgroundColor: 'var(--morocco-blue)',
            color: 'white',
            border: 'none',
            borderRadius: 'var(--radius-sm)',
            fontSize: '0.75rem',
            fontWeight: 500,
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = 'var(--morocco-blue-dark)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'var(--morocco-blue)';
          }}
        >
          View Details
        </button>
      </div>

      <style>{`
        @keyframes slideIn {
          from {
            opacity: 0;
            transform: translateY(-5px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default POIHoverCard;
