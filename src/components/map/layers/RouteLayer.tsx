import React, { useEffect, useState } from 'react';
import mapboxgl from 'mapbox-gl';
// import { Destination } from '@/data/destinations'; // Commented out incorrect import
import { Destination } from '../../../types'; // Corrected import path

interface RouteLayerProps {
  map: mapboxgl.Map | null;
  destinations: Destination[];
  routeColor?: string;
  onRouteReady?: (routeCoordinates: [number, number][]) => void;
}

const RouteLayer: React.FC<RouteLayerProps> = ({
  map,
  destinations,
  routeColor = '#E27D60',
  onRouteReady
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const routeSourceId = 'morocco-route';
  const routeLayerId = 'morocco-route-layer';
  const routeShadowLayerId = 'morocco-route-shadow-layer';

  // Log every render and the destinations prop
  console.log('[RouteLayer] Rendered. destinations prop:', destinations);

  // Fetch driving route between two points
  const fetchRoute = async (start: [number, number], end: [number, number]) => {
    try {
      const query = await fetch(
        `https://api.mapbox.com/directions/v5/mapbox/driving/${start[0]},${start[1]};${end[0]},${end[1]}?geometries=geojson&overview=full&access_token=${mapboxgl.accessToken}`
      );
      const json = await query.json();
      if (!json.routes || !json.routes[0]) {
        throw new Error('No route found');
      }
      return json.routes[0].geometry;
    } catch (error) {
      console.error('Error fetching route:', error);
      return null;
    }
  };

  useEffect(() => {
    console.log('[RouteLayer] useEffect running. destinations:', destinations);
    if (!map || destinations.length < 2) {
      if (map && map.isStyleLoaded()) {
        if (map.getLayer(routeLayerId)) map.removeLayer(routeLayerId);
        if (map.getLayer(routeShadowLayerId)) map.removeLayer(routeShadowLayerId);
        if (map.getSource(routeSourceId)) map.removeSource(routeSourceId);
      }
      console.log('[RouteLayer] Not enough destinations or map not ready. Skipping route draw.');
      return;
    }

    const fetchRoutes = async () => {
      console.log('[RouteLayer] fetchRoutes called. Destinations:', destinations);
      // Wait for map style to be loaded
      if (!map.isStyleLoaded()) {
        console.log('[RouteLayer] Map style not loaded, waiting for styledata event.');
        await new Promise<void>(resolve => {
          map.once('styledata', () => {
            console.log('[RouteLayer] styledata event fired.');
            resolve();
          });
        });
      }

      setIsLoading(true);
      try {
        const validDestinationsInput = destinations.filter(
          dest => dest.coordinates && dest.coordinates.length === 2
        );
        console.log('[RouteLayer] validDestinationsInput:', validDestinationsInput.map(d => d.name), validDestinationsInput.map(d => d.coordinates));

        if (validDestinationsInput.length < 2) {
          console.warn('[RouteLayer] Not enough valid coordinates to draw a route. Early exit from fetchRoutes.');
          setIsLoading(false); // Ensure loading state is reset
          onRouteReady?.([]); // Call with empty array if route cannot be formed
          return;
        }

        // De-duplicate consecutive destinations
        let validDestinations: Destination[] = [];
        if (validDestinationsInput.length > 0) {
          validDestinations.push(validDestinationsInput[0]);
          for (let i = 1; i < validDestinationsInput.length; i++) {
            if (validDestinationsInput[i].id !== validDestinationsInput[i-1].id) {
              validDestinations.push(validDestinationsInput[i]);
            }
          }
        }
        console.log('[RouteLayer] De-duplicated validDestinations:', validDestinations.map(d => d.name), validDestinations.map(d => d.coordinates));

        // Check again after de-duplication
        if (validDestinations.length < 2) {
          console.warn('[RouteLayer] Not enough unique consecutive destinations to draw a route. Early exit from fetchRoutes.');
          setIsLoading(false); // Ensure loading state is reset
          // Optionally, clear any existing route here if necessary
          if (map.getSource(routeSourceId)) {
            if (map.getLayer(routeLayerId)) map.removeLayer(routeLayerId);
            if (map.getLayer(routeShadowLayerId)) map.removeLayer(routeShadowLayerId);
            map.removeSource(routeSourceId);
          }
          onRouteReady?.([]); // Call with empty array if route cannot be formed
          return;
        }

        // Fetch routes between consecutive points
        const routeGeometries = [];
        for (let i = 0; i < validDestinations.length - 1; i++) {
          const start = validDestinations[i].coordinates as [number, number];
          const end = validDestinations[i + 1].coordinates as [number, number];
          console.log(`[RouteLayer] Attempting to fetch route segment: ${validDestinations[i].name} -> ${validDestinations[i+1].name}`, { start, end });
          const geometry = await fetchRoute(start, end);
          if (geometry) {
            routeGeometries.push(geometry);
            console.log(`[RouteLayer] Successfully fetched segment: ${validDestinations[i].name} -> ${validDestinations[i+1].name}. Coordinates count:`, geometry.coordinates.length);
          } else {
            console.warn(`[RouteLayer] Failed to fetch segment: ${validDestinations[i].name} -> ${validDestinations[i+1].name}. fetchRoute returned null.`);
          }
        }

        if (routeGeometries.length === 0) {
          console.warn('[RouteLayer] No valid route segments fetched. Early exit from fetchRoutes.');
          setIsLoading(false); // Ensure loading state is reset
          onRouteReady?.([]); // Call with empty array if no route segments
          return;
        }

        // Combine all route geometries
        const combinedCoordinates = routeGeometries.reduce((acc, geometry) => {
          return acc.concat(geometry.coordinates);
        }, [] as [number, number][]);
        console.log('[RouteLayer] Combined route coordinates count:', combinedCoordinates.length);

        // Remove existing layers and source
        if (map.getSource(routeSourceId)) {
          if (map.getLayer(routeLayerId)) map.removeLayer(routeLayerId);
          if (map.getLayer(routeShadowLayerId)) map.removeLayer(routeShadowLayerId);
          map.removeSource(routeSourceId);
          console.log('[RouteLayer] Removed previous route layers and source');
        }

        // Add new source with combined route
        map.addSource(routeSourceId, {
          type: 'geojson',
          data: {
            type: 'Feature',
            properties: {},
            geometry: {
              type: 'LineString',
              coordinates: combinedCoordinates
            }
          }
        });
        console.log('[RouteLayer] Added new route source');

        // Add shadow/border layer
        map.addLayer({
          id: routeShadowLayerId,
          type: 'line',
          source: routeSourceId,
          layout: {
            'line-join': 'round',
            'line-cap': 'round',
            visibility: 'visible'
          },
          paint: {
            'line-color': '#ffffff',
            'line-width': 6,
            'line-opacity': 0.7
          }
        });
        console.log('[RouteLayer] Added route shadow layer');

        // Add main route layer
        map.addLayer({
          id: routeLayerId,
          type: 'line',
          source: routeSourceId,
          layout: {
            'line-join': 'round',
            'line-cap': 'round',
            visibility: 'visible'
          },
          paint: {
            'line-color': routeColor,
            'line-width': 3,
            'line-opacity': 0.9,
            'line-dasharray': [2, 2]
          }
        });
        console.log('[RouteLayer] Added main route layer');

        // Validate coordinates before calling onRouteReady
        const validatedCoordinates = combinedCoordinates.filter((coord: any) => {
          if (Array.isArray(coord) && coord.length === 2) {
            const [lng, lat] = coord;
            return !isNaN(lng) && !isNaN(lat) && isFinite(lng) && isFinite(lat);
          }
          return false;
        });

        // Call the onRouteReady callback with validated coordinates
        if (onRouteReady) {
          console.log('[RouteLayer] PREPARING to call onRouteReady with validated coordinates:', validatedCoordinates.length);
          onRouteReady(validatedCoordinates);
          console.log('[RouteLayer] SUCCESSFULLY CALLED onRouteReady.');
        }

        // Fit map to the route bounds with padding - with validation
        const validCoordinates = combinedCoordinates.filter((coord: any) => {
          if (Array.isArray(coord) && coord.length === 2) {
            const [lng, lat] = coord;
            return !isNaN(lng) && !isNaN(lat) && isFinite(lng) && isFinite(lat);
          }
          return false;
        });

        if (validCoordinates.length > 0) {
          const bounds = validCoordinates.reduce((accBounds: mapboxgl.LngLatBounds, currentCoord: mapboxgl.LngLatLike) => {
            return accBounds.extend(currentCoord);
          }, new mapboxgl.LngLatBounds(validCoordinates[0] as mapboxgl.LngLatLike, validCoordinates[0] as mapboxgl.LngLatLike));

          map.fitBounds(bounds, {
            padding: {
              top: 100,
              bottom: 100,
              left: 100,
              right: 100
            },
            maxZoom: 7.5,
            duration: 1000
          });
        } else {
          console.warn('[RouteLayer] No valid coordinates found for bounds calculation');
        }
        console.log('[RouteLayer] Route drawn on map.');
      } catch (error) {
        console.error('[RouteLayer] Error creating route visualization:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRoutes();

    return () => {
      if (map && map.isStyleLoaded()) {
        if (map.getLayer(routeLayerId)) map.removeLayer(routeLayerId);
        if (map.getLayer(routeShadowLayerId)) map.removeLayer(routeShadowLayerId);
        if (map.getSource(routeSourceId)) map.removeSource(routeSourceId);
        console.log('[RouteLayer] Cleaned up route layers and source on unmount');
      }
    };
  }, [map, destinations, onRouteReady, routeColor]);

  return null;
};

export default RouteLayer; 