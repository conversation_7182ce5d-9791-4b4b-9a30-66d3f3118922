import React from 'react';
import { TravelStyle, TravelInterest } from '@/types/ItineraryParameters';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Calendar, Clock } from 'lucide-react';

interface JourneyParametersFormProps {
  // Remove numberOfTravelDays - will be calculated from city allocations
  travelStyle: TravelStyle;
  onTravelStyleChange: (style: TravelStyle) => void;
  availableTravelStyles: { value: TravelStyle; label: string }[];
  selectedTravelInterests: TravelInterest[];
  onTravelInterestChange: (interest: TravelInterest, selected: boolean) => void;
  availableTravelInterests: { value: TravelInterest; label: string }[];
  compact?: boolean;
  journeyStyle?: string;
  onJourneyStyleChange?: (style: string) => void;
  availableJourneyStyles?: { value: string; label: string }[];
  // Add calculated total days for display
  calculatedTotalDays?: number;
}

const JourneyParametersForm: React.FC<JourneyParametersFormProps> = ({
  travelStyle,
  onTravelStyleChange,
  availableTravelStyles,
  selectedTravelInterests,
  onTravelInterestChange,
  availableTravelInterests,
  compact = false,
  journeyStyle = 'cultural-deep-dive',
  onJourneyStyleChange,
  availableJourneyStyles = [
    { value: 'scenic-routes', label: 'Scenic Routes' },
    { value: 'cultural-deep-dive', label: 'Cultural Deep-dive' },
    { value: 'adventure-seeker', label: 'Adventure Seeker' },
    { value: 'photography-tour', label: 'Photography Tour' },
    { value: 'hidden-gems', label: 'Hidden Gems' },
    { value: 'local-immersion', label: 'Local Immersion' }
  ],
  calculatedTotalDays = 0
}) => {

  // Duration handlers removed - now calculated from city allocations

  if (compact) {
    return (
      <div
        className="morocco-card"
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '1.5rem',
          padding: '0.75rem 1rem',
          backgroundColor: 'white',
          border: '1px solid var(--border-light)',
          fontFamily: 'var(--font-body)'
        }}
      >
        {/* Total Days Display - Calculated from city allocations */}
        {calculatedTotalDays > 0 && (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            fontFamily: 'var(--font-body)',
            fontSize: '0.875rem'
          }}>
            <Calendar size={16} style={{ color: 'var(--morocco-blue)' }} />
            <span style={{
              color: 'var(--text-primary)',
              fontWeight: 500
            }}>Total</span>
            <span style={{
              fontSize: '1rem',
              fontWeight: 600,
              color: 'var(--morocco-red)',
              fontFamily: 'var(--font-heading)'
            }}>
              {calculatedTotalDays} {calculatedTotalDays === 1 ? 'day' : 'days'}
            </span>
          </div>
        )}

        {/* Moroccan Pace Selector */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem',
          fontFamily: 'var(--font-body)',
          fontSize: '0.875rem'
        }}>
          <Clock size={16} style={{ color: 'var(--morocco-yellow)' }} />
          <span style={{
            color: 'var(--text-primary)',
            fontWeight: 500
          }}>Pace</span>

          <select
            value={travelStyle}
            onChange={(e) => onTravelStyleChange(e.target.value as TravelStyle)}
            style={{
              padding: '0.25rem 0.5rem',
              borderRadius: 'var(--radius-sm)',
              border: '1px solid var(--border-light)',
              backgroundColor: 'var(--background-secondary)',
              color: 'var(--text-primary)',
              fontSize: '0.875rem',
              fontFamily: 'var(--font-body)',
              cursor: 'pointer',
              outline: 'none',
              minWidth: '140px'
            }}
          >
            {availableTravelStyles.map((style) => (
              <option key={style.value} value={style.value}>
                {style.label}
              </option>
            ))}
          </select>
        </div>

        {/* Moroccan Style Selector */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem',
          fontFamily: 'var(--font-body)',
          fontSize: '0.875rem'
        }}>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="var(--morocco-teal)" strokeWidth="2">
            <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
            <polyline points="3.27,6.96 12,12.01 20.73,6.96"/>
            <line x1="12" y1="22.08" x2="12" y2="12"/>
          </svg>
          <span style={{
            color: 'var(--text-primary)',
            fontWeight: 500
          }}>Style</span>

          <select
            value={journeyStyle}
            onChange={(e) => onJourneyStyleChange && onJourneyStyleChange(e.target.value)}
            style={{
              padding: '0.25rem 0.5rem',
              borderRadius: 'var(--radius-sm)',
              border: '1px solid var(--border-light)',
              backgroundColor: 'var(--background-secondary)',
              color: 'var(--text-primary)',
              fontSize: '0.875rem',
              fontFamily: 'var(--font-body)',
              cursor: 'pointer',
              outline: 'none',
              minWidth: '160px'
            }}
          >
            {availableJourneyStyles.map((style) => (
              <option key={style.value} value={style.value}>
                {style.label}
              </option>
            ))}
          </select>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 space-y-6 bg-white rounded-lg shadow-xl border border-gray-200">
      <h3 className="text-xl font-semibold text-gray-800 border-b border-gray-200 pb-3 mb-4">Plan Your Journey</h3>

      {/* Total Days Display - Calculated from city allocations */}
      {calculatedTotalDays > 0 && (
        <div className="space-y-2">
          <Label className="text-gray-700 font-medium">Total Trip Duration</Label>
          <div className="flex items-center gap-2">
            <Calendar size={16} className="text-blue-600" />
            <span className="text-lg font-semibold text-blue-600">
              {calculatedTotalDays} {calculatedTotalDays === 1 ? 'day' : 'days'}
            </span>
            <span className="text-sm text-gray-500">(calculated from city allocations)</span>
          </div>
        </div>
      )}

      {/* Travel Style */}
      <div className="space-y-2">
        <Label htmlFor="travel-style" className="text-gray-700 font-medium">Travel Style/Pace</Label>
        <Select value={travelStyle} onValueChange={onTravelStyleChange}>
          <SelectTrigger id="travel-style" className="bg-white border-gray-300 text-gray-800">
            <SelectValue placeholder="Select travel style" />
          </SelectTrigger>
          <SelectContent className="bg-white border-gray-300">
            {availableTravelStyles.map((style) => (
              <SelectItem key={style.value} value={style.value} className="hover:bg-gray-50">
                {style.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Travel Interests */}
      <div className="space-y-2">
        <Label className="text-gray-700 font-medium">Travel Interests</Label>
        <div className="grid grid-cols-2 gap-3 pt-2">
          {availableTravelInterests.map((interest) => (
            <div key={interest.value} className="flex items-center space-x-2 bg-gray-50 p-2 rounded-md border border-gray-200">
              <Checkbox
                id={`interest-${interest.value}`}
                checked={(selectedTravelInterests || []).includes(interest.value)}
                onCheckedChange={(checked) => onTravelInterestChange(interest.value, !!checked)}
                className="text-blue-500 border-gray-400"
              />
              <Label htmlFor={`interest-${interest.value}`} className="text-gray-700 text-sm font-normal cursor-pointer">
                {interest.label}
              </Label>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default JourneyParametersForm; 