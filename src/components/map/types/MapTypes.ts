/**
 * Type definitions for map-related components
 */
import type { LogLevel } from '@/types/LogLevel';
import { Position, MapPosition, LatLngPosition } from '@/types/Position';

export type { Position, MapPosition, LatLngPosition };

export interface MapBounds {
  north: number;
  south: number;
  east: number;
  west: number;
}

export interface MapViewport {
  center: Position;
  zoom: number;
  bearing: number;
  pitch: number;
}

export type LngLatTuple = [number, number];

// Re-export LogLevel for backwards compatibility 
export type { LogLevel }; 