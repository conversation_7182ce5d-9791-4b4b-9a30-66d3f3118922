import { Position, LogLevel } from './MapTypes';
import { 
  AnimationEventType, 
  AnimationStateEventData,
  EventData,
  InteractionEventType
} from '@/types/AnimationEventTypes';

export interface BaseEventData {
  timestamp: number;
  level: LogLevel;
  message: string;
  data?: any;
}

export interface VehiclePositionEventData extends BaseEventData {
  position: Position;
  bearing: number;
}

export interface POIDiscoveryEventData extends BaseEventData {
  poiId: string;
  position: Position;
  name: string;
}

export interface CityApproachEventData extends BaseEventData {
  cityId: string;
  position: Position;
  name: string;
  distance: number;
}

export interface EventEmitter {
  emit(eventName: string, data: BaseEventData): void;
  on(eventName: string, handler: (data: BaseEventData) => void): void;
  off(eventName: string, handler: (data: BaseEventData) => void): void;
}

export type EventHandler = (data: BaseEventData) => void;

export enum EventType {
  VEHICLE_POSITION = 'vehiclePosition',
  ANIMATION_STATE = 'animationState',
  POI_DISCOVERED = 'poiDiscovered',
  CITY_APPROACHED = 'cityApproached'
}

export { AnimationEventType, InteractionEventType };
export type { AnimationStateEventData, EventData }; 