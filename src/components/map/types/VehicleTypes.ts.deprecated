import { Position } from './MapTypes';
import { TerrainType } from '../../../types/CoreAnimationTypes';

export interface VehicleDebugInfo {
  terrain: TerrainType;
  isVisible: boolean;
  lastUpdateTime: number;
  recoveryAttempts: number;
  message: string;
  timestamp?: number;
  position?: Position;
  bearing?: number;
}

export interface VehicleMarkerOptions {
  style?: VehicleStyle;
  recoveryOptions?: VehicleRecoveryOptions;
}

export interface VehicleStyle {
  color: string;
  scale: number;
  opacity: number;
  zIndex: number;
  terrainStyles?: Partial<Record<TerrainType, Partial<VehicleStyle>>>;
}

export interface VehicleRecoveryOptions {
  maxAttempts: number;
  delayBetweenAttempts: number;
  onRecoveryAttempt?: (attempt: number) => void;
  onRecoverySuccess?: () => void;
  onRecoveryFailure?: () => void;
}

export const DEFAULT_VEHICLE_STYLE: VehicleStyle = {
  color: '#2196F3',
  scale: 1,
  opacity: 1,
  zIndex: 100,
  terrainStyles: {
    [TerrainType.CITY]: {
      scale: 0.8,
      color: '#1976D2'
    },
    [TerrainType.HIGHWAY]: {
      scale: 1.2,
      color: '#2196F3'
    },
    [TerrainType.MOUNTAIN]: {
      scale: 0.9,
      color: '#1565C0'
    },
    [TerrainType.SCENIC]: {
      scale: 0.85,
      color: '#0D47A1'
    }
  }
};

export const DEFAULT_RECOVERY_OPTIONS: VehicleRecoveryOptions = {
  maxAttempts: 3,
  delayBetweenAttempts: 1000,
  onRecoveryAttempt: (attempt) => console.debug(`Vehicle recovery attempt ${attempt}`),
  onRecoverySuccess: () => console.debug('Vehicle recovery successful'),
  onRecoveryFailure: () => console.error('Vehicle recovery failed')
}; 