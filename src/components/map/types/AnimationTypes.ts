import { Position } from './MapTypes';
import { AnimationEventType } from '@/types/AnimationEventTypes';
import { 
  AnimationConfig as CoreAnimationConfig,
  AnimationState as CoreAnimationState,
  TerrainType,
  ANIMATION_SPEEDS
} from '@/types/CoreAnimationTypes';

/**
 * Map-specific Animation Types
 * 
 * This file contains animation types specific to map-based animations.
 * It extends the core animation types from CoreAnimationTypes.ts with
 * map-specific functionality.
 */

// Extend the core animation state with map-specific properties
export interface MapAnimationState extends CoreAnimationState {
  currentPosition: Position;
  currentBearing: number;
  isInCity: boolean;
  nearPOI: boolean;
  terrain: TerrainType;
}

// Map-specific animation configuration
export interface MapAnimationConfig extends CoreAnimationConfig {
  defaultZoom: number;
  defaultPitch: number;
  defaultBearing: number;
  terrainZoomLevels: {
    [key in TerrainType]: number;
  };
  terrainPitchLevels: {
    [key in TerrainType]: number;
  };
}

// Map-specific route preparation options
export interface RoutePreparationOptions {
  duration?: number;
  onProgress?: (progress: number) => void;
  onPosition?: (position: Position, bearing: number) => void;
  onComplete?: () => void;
  onError?: (error: Error) => void;
}

// Re-export types
export { AnimationEventType, TerrainType };
export type { CoreAnimationConfig, ANIMATION_SPEEDS };

// Default zoom levels for different contexts
export const DEFAULT_ZOOM_LEVELS = {
  ROUTE_TRAVEL: 8,
  CITY_EXPLORATION: 14,
  POI_DETAIL: 16,
  CITY_DRIVE_BY: 12
};

// Default pitch levels for different contexts
export const DEFAULT_PITCH_LEVELS = {
  ROUTE_TRAVEL: 45,
  CITY_EXPLORATION: 60,
  POI_DETAIL: 50,
  CITY_DRIVE_BY: 55
}; 