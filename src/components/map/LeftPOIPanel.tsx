import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { motion, AnimatePresence, Variants } from 'framer-motion';
import {
  MapPin,
  Search,
  Filter,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  X,
  Plus,
  Check,
  Clock,
  Calendar,
  DollarSign,
  Sun,
  Cloud,
  CloudRain,
  Wind,
  Star
} from 'lucide-react';
import { PointOfInterest } from '@/types/POITypes';
import { ComponentInteractionManager } from './animation/ComponentInteractionManager';
import '../../styles/modern-poi-panels.css';
import { Button } from '@/components/ui/button';

// Panel animation variants
const panelVariants: Variants = {
  leftClosed: {
    x: '-100%',
    transition: {
      duration: 0.3,
      ease: 'easeInOut'
    }
  },
  leftOpen: {
    x: '0%',
    transition: {
      duration: 0.3,
      ease: 'easeInOut'
    }
  },
  rightOpen: {
    x: 0,
    opacity: 1,
    transition: {
      type: 'spring',
      stiffness: 300,
      damping: 30,
    },
  },
  rightClosed: {
    x: 350,
    opacity: 0,
    transition: {
      type: 'spring',
      stiffness: 300,
      damping: 30,
    },
  },
};

export interface LeftPOIPanelProps {
  pointsOfInterest: PointOfInterest[];
  selectedPOIs: PointOfInterest[];
  onAddPOI: (poi: PointOfInterest) => void;
  onRemovePOI: (poi: PointOfInterest) => void;
  onReorderPOIs?: (reorderedPOIs: PointOfInterest[]) => void;
  initialLeftPanelVisible?: boolean;
  initialRightPanelVisible?: boolean;
  selectedCity?: string | null;
  onPOIClick: (poi: PointOfInterest) => void;
  isMobile?: boolean;
  onClosePanel?: () => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

const LeftPOIPanel: React.FC<LeftPOIPanelProps> = ({
  pointsOfInterest,
  selectedPOIs,
  onAddPOI,
  onRemovePOI,
  onReorderPOIs,
  initialLeftPanelVisible = true,
  initialRightPanelVisible = true,
  selectedCity = null,
  onPOIClick,
  isMobile = false,
  onClosePanel,
  isCollapsed = false,
  onToggleCollapse
}) => {
  // State for panel visibility
  const [rightPanelVisible, setRightPanelVisible] = useState(initialRightPanelVisible && !isMobile);
  
  // State for filtering and search
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const [selectedDurationFilter, setSelectedDurationFilter] = useState<string>('');
  const [selectedPricing, setSelectedPricing] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'recommended' | 'name' | 'distance'>('recommended');
  
  // Collapsible sections state - default to open on desktop, closed on mobile
  const [categoryFilterSectionOpen, setCategoryFilterSectionOpen] = useState(!isMobile);
  const [durationFilterSectionOpen, setDurationFilterSectionOpen] = useState(!isMobile);
  const [pricingFilterSectionOpen, setPricingFilterSectionOpen] = useState(!isMobile);
  // Sort by is a select, usually not collapsible like this, but we can if needed.
  
  // Add debug state to monitor POIs
  const [debugInfo, setDebugInfo] = useState<{
    totalPOIs: number,
    filteredCount: number,
    cityName: string | null
  }>({
    totalPOIs: 0,
    filteredCount: 0,
    cityName: null
  });
  
  // Update debug info whenever pointsOfInterest changes
  useEffect(() => {
    setDebugInfo(prev => ({
      ...prev,
      totalPOIs: pointsOfInterest.length,
      cityName: selectedCity
    }));
    console.log(`[LeftPOIPanel] Received ${pointsOfInterest.length} POIs for city: ${selectedCity || 'none'}`);
    // Log first few POIs to help with debugging
    if (pointsOfInterest.length > 0) {
      console.log('[LeftPOIPanel] Sample POIs:', pointsOfInterest.slice(0, 2));
    }
  }, [pointsOfInterest, selectedCity]);
  
  // Consolidated getImageUrl - rely on normalizePOI to provide the correct image or fallback
  const getImageUrl = useCallback((poi: PointOfInterest) => {
    // After normalization, poi.images is guaranteed to be an array.
    // If it was originally empty or invalid, normalizePOI sets it to [fallbackImage].
    // If it had content, it uses that.
    return poi.images[0]; 
  }, []);
  
  // Normalize location display
  const getLocationDisplay = useCallback((poi: PointOfInterest) => {
    return poi.location || selectedCity || 'Morocco';
  }, [selectedCity]);
  
  // Drag and drop state
  const [draggedPOI, setDraggedPOI] = useState<PointOfInterest | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);
  
  // Extract unique POI types for filter chips (safely handling undefined types)
  const poiCategories = [...new Set(pointsOfInterest
    .map(poi => poi.category)
    .filter(category => category !== undefined) as string[])];
  
  // Filter POIs based on search term, selected types, and selected city
  const filteredPOIs = useMemo(() => {
    // Start with provided POIs
    let filteredList = pointsOfInterest;
    
    // Apply search filter
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filteredList = filteredList.filter(poi => {
        return (
          (poi.name && poi.name.toLowerCase().includes(searchLower)) ||
          (poi.description && poi.description.toLowerCase().includes(searchLower))
        );
      });
    }
    
    // Apply type filter
    if (selectedTypes.length > 0) {
      filteredList = filteredList.filter(poi => {
        return poi.category ? selectedTypes.includes(poi.category) : false;
      });
    }
    
    // Apply duration filter
    if (selectedDurationFilter) {
      filteredList = filteredList.filter(poi => {
        const duration = poi.duration || 0;
        if (selectedDurationFilter === 'quick') return duration < 1;
        if (selectedDurationFilter === 'medium') return duration >= 1 && duration <= 2;
        if (selectedDurationFilter === 'long') return duration > 2;
        return true;
      });
    }
    
    // Apply pricing filter
    if (selectedPricing !== 'all') {
      filteredList = filteredList.filter(poi => {
        const cost = poi.cost || 0;
        if (selectedPricing === 'free') return cost === 0;
        if (selectedPricing === 'paid') return cost > 0;
        return true;
      });
    }
    
    // Sort POIs
    if (sortBy === 'name') {
      filteredList = [...filteredList].sort((a, b) => a.name.localeCompare(b.name));
    } else if (sortBy === 'distance') {
      // Sort by distance if position information is available
      if (selectedCity) {
        filteredList = [...filteredList].sort((a, b) => {
          // Default to recommended order if positions aren't available
          return 0; // Placeholder - would implement distance sorting if we had coordinates
        });
      }
    }
    
    // Update debug info
    setDebugInfo(prev => ({
      ...prev,
      filteredCount: filteredList.length
    }));
    
    return filteredList;
  }, [pointsOfInterest, searchTerm, selectedTypes, selectedDurationFilter, selectedPricing, sortBy, selectedCity]);
  
  // Group selected POIs by day (for journey panel)
  const poiDays = selectedPOIs.reduce((acc, poi, index) => {
    // For demo purposes, assign POIs to days (1 POI per day)
    const day = Math.floor(index / 3) + 1;
    if (!acc[day]) acc[day] = [];
    acc[day].push(poi);
    return acc;
  }, {} as Record<number, PointOfInterest[]>);
  
  // Handle filter chip click
  const handleFilterToggle = (type: string) => {
    setSelectedTypes(prev => 
      prev.includes(type) 
        ? prev.filter(t => t !== type) 
        : [...prev, type]
    );
  };
  
  // Handle drag and drop
  const handleDragStart = (poi: PointOfInterest) => {
    setDraggedPOI(poi);
  };
  
  const handleDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault();
    setDragOverIndex(index);
  };
  
  const handleDrop = (e: React.DragEvent, targetIndex: number) => {
    e.preventDefault();
    if (!draggedPOI) return;
    const sourceIndex = selectedPOIs.findIndex((poi: PointOfInterest) => poi.id === draggedPOI.id);
    if (sourceIndex === -1) return;
    const reordered = [...selectedPOIs];
    const [removed] = reordered.splice(sourceIndex, 1);
    reordered.splice(targetIndex, 0, removed);
    if (onReorderPOIs) {
      onReorderPOIs(reordered);
    }
    setDraggedPOI(null);
    setDragOverIndex(null);
  };
  
  const handleDragEnd = () => {
    setDraggedPOI(null);
    setDragOverIndex(null);
  };
  
  // Calculate journey stats
  const journeyStats = {
    totalPOIs: selectedPOIs.length,
    totalDays: Object.keys(poiDays).length,
    estimatedCost: selectedPOIs.reduce((sum, poi) => sum + (poi.cost || 0), 0),
  };
  
  // Weather icons based on condition
  const getWeatherIcon = (condition: string) => {
    switch (condition) {
      case 'sunny':
        return <Sun className="weather-icon sunny" />;
      case 'cloudy':
        return <Cloud className="weather-icon cloudy" />;
      case 'rainy':
        return <CloudRain className="weather-icon rainy" />;
      case 'windy':
        return <Wind className="weather-icon windy" />;
      default:
        return <Sun className="weather-icon sunny" />;
    }
  };

  useEffect(() => {
    // We'll implement a simplified approach to POI event handling
    // This will be properly implemented when we have the correct types
    console.log('LeftPOIPanel mounted - will implement event handling later');
    
    return () => {
      // Cleanup function
    };
  }, []);

  // Panel animation variants - can be enhanced for width later
  const leftPanelWrapperVariants: Variants = {
    open: { 
      width: isMobile ? '100%' : '380px', // Full width on mobile, 380px on desktop
      opacity: 1, 
      transition: { duration: 0.3, ease: 'easeInOut' } 
    },
    collapsed: { 
      width: '50px', // Collapsed width for desktop
      opacity: 1, // Keep it opaque to show the button
      transition: { duration: 0.3, ease: 'easeInOut' } 
    },
    hidden: { // For mobile slide-out
        x: '-100%',
        transition: { duration: 0.3, ease: 'easeInOut' }
    },
    visible: { // For mobile slide-in
        x: '0%',
        transition: { duration: 0.3, ease: 'easeInOut' }
    }
  };
  
  // Determine animation state based on props
  const animateState = isCollapsed ? 'collapsed' : 'open';

  return (
      <motion.div 
      className={`modern-panel-base modern-poi-panel bg-gray-50 dark:bg-gray-900 shadow-xl flex flex-col modern-left-panel`}
      variants={leftPanelWrapperVariants}
      initial={isCollapsed ? 'collapsed' : 'open'} // Use 'collapsed' if initially collapsed
      animate={animateState}
      style={{
        position: isMobile ? 'fixed' : 'relative',
        top: isMobile ? '64px' : undefined, // Position below header on mobile
        left: 0,
        bottom: isMobile ? 0 : undefined,
        height: isMobile ? 'calc(100vh - 64px)' : '100%', // Full height below header on mobile
        // width: isCollapsed ? '50px' : (isMobile ? '80%' : '384px'), // w-96, or 80% on mobile when open
        zIndex: isMobile ? 1000 : undefined, // Ensure it's above map on mobile
      }}
    >
      {/* Panel Header */}
      <div className={`flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700 ${isCollapsed ? 'hidden' : ''}`}>
        <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Explore</h2>
        {isMobile && onClosePanel && (
          <Button variant="ghost" size="icon" onClick={onClosePanel} className="text-gray-700 dark:text-gray-200">
            <X size={20} />
          </Button>
          )}
        </div>
        
      {/* Collapse/Expand Button for Desktop */}
      {!isMobile && onToggleCollapse && (
        <button 
          onClick={onToggleCollapse} 
          className="absolute top-1/2 -right-3 transform -translate-y-1/2 bg-gray-200 dark:bg-gray-700 p-1 rounded-full shadow-md z-10 hover:bg-gray-300 dark:hover:bg-gray-600"
          aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          {isCollapsed ? <ChevronRight size={18} /> : <ChevronLeft size={18} />}
        </button>
      )}

      {/* Content Area (Scrollable) */}
      {!isCollapsed && (
        <div className="flex-grow overflow-y-auto p-3 space-y-4">
          {/* Search Input */}
          <div className="relative">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input 
            type="text" 
              placeholder="Search POIs..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-8 pr-2 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-sm text-gray-700 dark:text-gray-200"
          />
        </div>
        
          {/* Filter Sections ... (Categories, Duration, Pricing) */}
          {/* Category Filter */}
          <FilterSection 
            title="Categories" 
            isOpen={categoryFilterSectionOpen} 
            setIsOpen={setCategoryFilterSectionOpen}
          >
            <div className="flex flex-wrap gap-1.5">
              {poiCategories.map(type => (
                <Button 
                  key={type}
                  variant={selectedTypes.includes(type) ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleFilterToggle(type)}
                  className={`text-xs px-2 py-0.5 rounded-full ${selectedTypes.includes(type) ? 'bg-blue-500 text-white hover:bg-blue-600' : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'}`}
                >
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </Button>
                ))}
              </div>
          </FilterSection>
          
          {/* Duration Filter */}
          <FilterSection 
            title="Duration" 
            isOpen={durationFilterSectionOpen} 
            setIsOpen={setDurationFilterSectionOpen}
          >
            <div className="flex flex-col space-y-1.5">
              {[ {label: 'Any', value: ''}, {label: 'Quick (<1 hr)', value: 'quick'}, {label: 'Medium (1-2 hrs)', value: 'medium'}, {label: 'Long (>2 hrs)', value: 'long'}].map(opt => (
                <label key={opt.value} className="flex items-center text-xs text-gray-600 dark:text-gray-300">
                  <input 
                    type="radio" 
                    name="durationFilter" 
                    value={opt.value} 
                    checked={selectedDurationFilter === opt.value} 
                    onChange={(e) => setSelectedDurationFilter(e.target.value)} 
                    className="mr-1.5 h-3 w-3 text-blue-500 focus:ring-blue-400 border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-700"
                  />
                  {opt.label}
                </label>
              ))}
              </div>
          </FilterSection>
          
          {/* Pricing Filter */}
          <FilterSection 
            title="Pricing" 
            isOpen={pricingFilterSectionOpen} 
            setIsOpen={setPricingFilterSectionOpen}
          >
            <div className="flex flex-col space-y-1.5">
              {[ {label: 'All', value: 'all'}, {label: 'Free', value: 'free'}, {label: 'Paid', value: 'paid'}].map(opt => (
                <label key={opt.value} className="flex items-center text-xs text-gray-600 dark:text-gray-300">
                  <input 
                    type="radio" 
                    name="pricingFilter" 
                    value={opt.value} 
                    checked={selectedPricing === opt.value} 
                    onChange={(e) => setSelectedPricing(e.target.value)} 
                    className="mr-1.5 h-3 w-3 text-blue-500 focus:ring-blue-400 border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-700"
                  />
                  {opt.label}
                </label>
              ))}
            </div>
          </FilterSection>

          {/* POI List */}
          <div className="space-y-2">
            {filteredPOIs.map((poi, index) => (
              <POICard 
                key={poi.id} 
                poi={poi} 
                onAdd={() => onAddPOI(poi)} 
                onRemove={() => onRemovePOI(poi)} 
                onClick={() => onPOIClick(poi)}
                isSelected={selectedPOIs.some(p => p.id === poi.id)}
                isMobile={isMobile}
                getImageUrl={getImageUrl}
                getLocationDisplay={getLocationDisplay}
              />
            ))}
            {filteredPOIs.length === 0 && (
              <p className="text-xs text-gray-400 dark:text-gray-500 text-center py-3">No points of interest match your filters.</p>
            )}
          </div>
          
          {/* REMOVED "Your Journey" section
          {selectedPOIs.length > 0 && (
            <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-200 mb-2">Your Journey</h3>
              {Object.entries(poiDays).map(([day, pois]) => (
                <div key={day} className="mb-2.5">
                  <h4 className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Day {day}</h4>
                  {pois.map(poi => (
                    <div key={poi.id} className="flex items-center justify-between py-1 border-b border-gray-100 dark:border-gray-800 last:border-b-0">
                      <span className="text-xs text-gray-600 dark:text-gray-300 truncate pr-1" title={poi.name}>{poi.name}</span>
                      <Button variant="ghost" size="icon" onClick={() => onRemovePOI(poi)} className="text-red-400 hover:text-red-500 p-0.5 h-auto w-auto min-w-0">
                        <X size={10} />
                      </Button>
                    </div>
                  ))}
                </div>
              ))}
            </div>
          )}
          */}
        </div>
      )}
    </motion.div>
  );
};

// Helper component for collapsible filter sections
interface FilterSectionProps {
  title: string;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  children: React.ReactNode;
}

const FilterSection: React.FC<FilterSectionProps> = ({ title, isOpen, setIsOpen, children }) => (
  <div className="py-2 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
        <button 
      onClick={() => setIsOpen(!isOpen)}
      className="flex items-center justify-between w-full text-left text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100 mb-1"
        >
      <span>{title}</span>
      {isOpen ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
        </button>
    {isOpen && <div className="mt-1.5">{children}</div>}
  </div>
);

// POI Card component
interface POICardProps {
  poi: PointOfInterest;
  onAdd: () => void;
  onRemove: () => void;
  onClick: () => void;
  isSelected: boolean;
  isMobile?: boolean;
  getImageUrl: (poi: PointOfInterest) => string;
  getLocationDisplay: (poi: PointOfInterest) => string;
}

const POICard: React.FC<POICardProps> = ({ poi, onAdd, onRemove, onClick, isSelected, getImageUrl, getLocationDisplay, isMobile }) => {
  const imageUrl = getImageUrl(poi);
  if (imageUrl && imageUrl.includes('via.placeholder.com')) {
    console.warn(`[LeftPOIPanel POICard] WARNING: Attempting to load via.placeholder.com for ${poi.name}. POI Image URL: ${imageUrl}, POI Object:`, JSON.stringify(poi, null, 2));
  }

  return (
    <motion.div
      className={`poi-item p-2.5 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm cursor-pointer hover:shadow-md dark:hover:border-gray-600 transition-all duration-150 ease-in-out ${isSelected ? 'ring-2 ring-blue-500 border-transparent' : 'bg-white dark:bg-gray-800'}`}
      onClick={onClick}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => e.key === 'Enter' && onClick()}
    >
      <div className="flex items-start space-x-2.5">
        <img
          src={imageUrl}
          alt={poi.name}
          className="w-16 h-16 object-cover rounded-md flex-shrink-0"
          data-load-attempt={imageUrl === '/images/placeholder.svg' ? 'placeholder' : 'primary'} // Set initial attempt type
          onError={(e) => {
            const target = e.currentTarget as HTMLImageElement;
            if (target.getAttribute('data-load-attempt') === 'placeholder') {
              // Local placeholder itself failed
              console.warn(`[LeftPOIPanel POICard] LOCAL PLACEHOLDER /images/placeholder.svg FAILED for ${poi.name}. Hiding image.`);
              target.style.display = 'none';
            } else {
              // Primary image failed, try local placeholder
              console.warn(`[LeftPOIPanel POICard] Primary image failed for ${poi.name} (src: ${target.src}). Attempting local placeholder.`);
              target.setAttribute('data-load-attempt', 'placeholder');
              target.src = '/images/placeholder.svg';
            }
          }}
        />
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-semibold text-gray-800 dark:text-gray-100 truncate" title={poi.name}>{poi.name}</h4>
          <p className="text-xs text-gray-500 dark:text-gray-400 truncate">{getLocationDisplay(poi)}</p>
          <div className="flex items-center mt-0.5">
            {poi.rating && (
              <div className="flex items-center mr-1.5">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} size={10} className={i < poi.rating! ? "text-yellow-400 fill-yellow-400" : "text-gray-300 dark:text-gray-500"} />
                ))}
              </div>
            )}
            {/* {poi.duration && <p className="text-xs text-gray-400 dark:text-gray-500">{poi.duration}h</p>} */}
          </div>
          <p className="text-xs text-gray-600 dark:text-gray-300 mt-1 line-clamp-2">{poi.description}</p>
        </div>
        <div className="flex flex-col items-end space-y-1 ml-1">
          {isSelected ? (
            <Button variant="outline" size="sm" onClick={(e) => { e.stopPropagation(); onRemove(); }} className="text-red-500 border-red-400 hover:bg-red-50 dark:text-red-400 dark:border-red-500 dark:hover:bg-red-900/30 h-auto px-2 py-1 text-xs">
              <X size={12} className="mr-0.5" /> Remove
            </Button>
          ) : (
            <Button variant="outline" size="sm" onClick={(e) => { e.stopPropagation(); onAdd(); }} className="text-blue-500 border-blue-400 hover:bg-blue-50 dark:text-blue-400 dark:border-blue-500 dark:hover:bg-blue-900/30 h-auto px-2 py-1 text-xs">
              <Plus size={12} className="mr-0.5" /> Add
            </Button>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default LeftPOIPanel;