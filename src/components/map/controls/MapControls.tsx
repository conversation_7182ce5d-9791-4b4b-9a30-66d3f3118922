import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ZoomIn, ZoomOut, RotateCcw } from 'lucide-react';

interface MapControlsProps {
  onResetView?: () => void;
  onZoomIn?: () => void;
  onZoomOut?: () => void;
  // onToggleLeftPanel?: () => void; // Removed
  // isAnimating?: boolean; // Removed
  // onPauseAnimation?: () => void; // Removed
  // animationPaused?: boolean; // Removed
}

const MapControls: React.FC<MapControlsProps> = ({ 
  onResetView, 
  onZoomIn, 
  onZoomOut 
}) => {
  return (
    <div className="absolute bottom-4 right-4 z-10 flex flex-col gap-2">
      {onZoomIn && (
        <Button 
          variant="outline" 
          size="sm" 
          className="h-10 w-10 p-0 bg-white border-gray-300 text-gray-700 shadow-md hover:bg-gray-100 transition-colors"
          onClick={onZoomIn}
          title="Zoom in"
        >
          <ZoomIn size={20} className="h-5 w-5" />
        </Button>
      )}
      
      {onZoomOut && (
        <Button 
          variant="outline" 
          size="sm" 
          className="h-10 w-10 p-0 bg-white border-gray-300 text-gray-700 shadow-md hover:bg-gray-100 transition-colors"
          onClick={onZoomOut}
          title="Zoom out"
        >
          <ZoomOut size={20} className="h-5 w-5" />
        </Button>
      )}
      
      {onResetView && (
        <Button 
          variant="outline" 
          size="sm" 
          className="h-10 w-10 p-0 bg-white border-gray-300 text-gray-700 shadow-md hover:bg-gray-100 transition-colors"
          onClick={onResetView}
          title="Reset view"
        >
          <RotateCcw size={20} className="h-5 w-5" />
        </Button>
      )}
    </div>
  );
};

export default MapControls; 