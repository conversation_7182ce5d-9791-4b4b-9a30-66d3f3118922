import React, { useState, useEffect } from 'react';
import { PointOfInterest } from '@/types/POITypes';
import { MapPin, X, Star, Clock, Info } from 'lucide-react';

interface POIDiscoveryNotificationProps {
  discoveredPOI: PointOfInterest | null;
  isVisible: boolean;
  onClose: () => void;
  onAddToItinerary?: (poi: PointOfInterest) => void;
  onViewDetails?: (poi: PointOfInterest) => void;
}

const POIDiscoveryNotification: React.FC<POIDiscoveryNotificationProps> = ({
  discoveredPOI,
  isVisible,
  onClose,
  onAddToItinerary,
  onViewDetails
}) => {
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (isVisible && discoveredPOI) {
      setIsAnimating(true);
      // Auto-hide after 8 seconds
      const timer = setTimeout(() => {
        onClose();
      }, 8000);
      return () => clearTimeout(timer);
    }
  }, [isVisible, discoveredPOI, onClose]);

  useEffect(() => {
    if (!isVisible) {
      setIsAnimating(false);
    }
  }, [isVisible]);

  if (!isVisible || !discoveredPOI) {
    return null;
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'landmark':
        return <Star size={16} color="var(--morocco-yellow)" />;
      case 'cultural':
        return <MapPin size={16} color="var(--morocco-blue)" />;
      case 'nature':
        return <MapPin size={16} color="var(--morocco-green)" />;
      case 'restaurant':
        return <MapPin size={16} color="var(--morocco-red)" />;
      default:
        return <MapPin size={16} color="var(--morocco-blue)" />;
    }
  };

  return (
    <div
      style={{
        position: 'fixed',
        top: '100px',
        right: '20px',
        width: '320px',
        backgroundColor: 'white',
        borderRadius: 'var(--radius-lg)',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',
        border: '1px solid var(--border-light)',
        zIndex: 1000,
        transform: isAnimating ? 'translateX(0)' : 'translateX(100%)',
        transition: 'transform 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
        overflow: 'hidden'
      }}
    >
      {/* Header */}
      <div
        style={{
          padding: '1rem',
          background: 'linear-gradient(135deg, rgba(0, 71, 171, 0.1), rgba(139, 26, 24, 0.05))',
          borderBottom: '1px solid var(--border-light)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
          <div
            style={{
              width: '32px',
              height: '32px',
              borderRadius: '50%',
              backgroundColor: 'var(--morocco-blue)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              animation: 'pulse 2s infinite'
            }}
          >
            <MapPin size={16} color="white" />
          </div>
          <div>
            <h3
              style={{
                margin: 0,
                fontSize: '1rem',
                fontWeight: 600,
                color: 'var(--morocco-blue)',
                fontFamily: 'var(--font-heading)'
              }}
            >
              POI Discovered!
            </h3>
            <p
              style={{
                margin: 0,
                fontSize: '0.75rem',
                color: 'var(--text-secondary)'
              }}
            >
              Nearby point of interest
            </p>
          </div>
        </div>
        <button
          onClick={onClose}
          style={{
            background: 'none',
            border: 'none',
            cursor: 'pointer',
            padding: '0.25rem',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
          className="hover:bg-gray-100"
        >
          <X size={16} color="var(--text-secondary)" />
        </button>
      </div>

      {/* Content */}
      <div style={{ padding: '1rem' }}>
        <div style={{ display: 'flex', alignItems: 'flex-start', gap: '0.75rem', marginBottom: '1rem' }}>
          <div style={{ flexShrink: 0 }}>
            {getCategoryIcon(discoveredPOI.category || 'other')}
          </div>
          <div style={{ flex: 1 }}>
            <h4
              style={{
                margin: '0 0 0.25rem',
                fontSize: '1.1rem',
                fontWeight: 600,
                color: 'var(--text-primary)',
                fontFamily: 'var(--font-heading)'
              }}
            >
              {discoveredPOI.name}
            </h4>
            <p
              style={{
                margin: 0,
                fontSize: '0.875rem',
                color: 'var(--text-secondary)',
                lineHeight: 1.4
              }}
            >
              {discoveredPOI.description || 'Interesting location worth exploring'}
            </p>
          </div>
        </div>

        {/* Category badge */}
        <div style={{ marginBottom: '1rem' }}>
          <span
            style={{
              display: 'inline-flex',
              alignItems: 'center',
              gap: '0.25rem',
              padding: '0.25rem 0.5rem',
              backgroundColor: 'rgba(0, 71, 171, 0.1)',
              color: 'var(--morocco-blue)',
              borderRadius: 'var(--radius-full)',
              fontSize: '0.75rem',
              fontWeight: 500,
              textTransform: 'capitalize'
            }}
          >
            {discoveredPOI.category || 'Point of Interest'}
          </span>
        </div>

        {/* Action buttons */}
        <div style={{ display: 'flex', gap: '0.5rem' }}>
          {onAddToItinerary && (
            <button
              onClick={() => onAddToItinerary(discoveredPOI)}
              style={{
                flex: 1,
                padding: '0.5rem 1rem',
                backgroundColor: 'var(--morocco-red)',
                color: 'white',
                border: 'none',
                borderRadius: 'var(--radius-md)',
                cursor: 'pointer',
                fontFamily: 'var(--font-body)',
                fontWeight: 500,
                fontSize: '0.875rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '0.25rem',
                transition: 'all 0.2s ease'
              }}
              className="hover:bg-morocco-red-dark"
            >
              <Star size={14} />
              Add to Trip
            </button>
          )}
          {onViewDetails && (
            <button
              onClick={() => onViewDetails(discoveredPOI)}
              style={{
                flex: 1,
                padding: '0.5rem 1rem',
                backgroundColor: 'transparent',
                color: 'var(--morocco-blue)',
                border: '1px solid var(--morocco-blue)',
                borderRadius: 'var(--radius-md)',
                cursor: 'pointer',
                fontFamily: 'var(--font-body)',
                fontWeight: 500,
                fontSize: '0.875rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '0.25rem',
                transition: 'all 0.2s ease'
              }}
              className="hover:bg-morocco-blue hover:text-white"
            >
              <Info size={14} />
              Details
            </button>
          )}
        </div>
      </div>

      {/* Progress bar */}
      <div
        style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          height: '3px',
          backgroundColor: 'rgba(0, 71, 171, 0.1)'
        }}
      >
        <div
          style={{
            height: '100%',
            backgroundColor: 'var(--morocco-blue)',
            animation: 'shrink 8s linear forwards'
          }}
        />
      </div>

      <style jsx>{`
        @keyframes pulse {
          0%, 100% {
            opacity: 1;
            transform: scale(1);
          }
          50% {
            opacity: 0.7;
            transform: scale(1.05);
          }
        }
        
        @keyframes shrink {
          from {
            width: 100%;
          }
          to {
            width: 0%;
          }
        }
      `}</style>
    </div>
  );
};

export default POIDiscoveryNotification;
