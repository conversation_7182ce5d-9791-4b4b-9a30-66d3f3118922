.travelAnimator {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  padding: 12px;
  z-index: 10;
  width: 300px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progressContainer {
  width: 100%;
  margin-bottom: 8px;
}

.progressBarWrapper {
  width: 100%;
  height: 8px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.progressBar {
  height: 100%;
  background-color: #3182ce;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progressText {
  font-size: 12px;
  color: white;
  text-align: center;
  margin-top: 4px;
}

.controls {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 14px;
}

.playButton {
  background-color: #3182ce;
  color: white;
}

.controlButton {
  background-color: #3182ce;
  color: white;
}

.stopButton {
  background-color: #e53e3e;
  color: white;
}

.button:hover {
  opacity: 0.9;
}

.button:active {
  transform: scale(0.95);
} 