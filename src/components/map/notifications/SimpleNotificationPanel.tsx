/**
 * SimpleNotificationPanel.tsx
 * 
 * A simplified version of the NotificationPanel that requires minimal props.
 * This is used by the refactored TravelAnimator component.
 */

import React, { useState, useEffect, CSSProperties } from 'react';
import { PointOfInterest } from '@/types/poi';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  FaCompass, 
  FaHiking, 
  FaMonument, 
  FaLandmark,
  FaMapMarkedAlt,
  FaChevronRight,
  FaPlay
} from 'react-icons/fa';

// Simplified interface with only required props
interface NotificationPanelProps {
  title: string;
  pois: PointOfInterest[];
  onSelect: (poi: PointOfInterest) => void;
}

// Category mappings for icons
const categoryIcons: Record<string, React.ReactElement> = {
  'monument': <FaMonument className="text-purple-500" />,
  'landmark': <FaLandmark className="text-blue-500" />,
  'activity': <FaHiking className="text-green-500" />,
  'nature': <FaCompass className="text-orange-500" />
};

export const NotificationPanel: React.FC<NotificationPanelProps> = ({
  title,
  pois,
  onSelect
}) => {
  const [isVisible, setIsVisible] = useState(false);

  // Add animation for panel entry
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 100);
    
    return () => {
      clearTimeout(timer);
    };
  }, []);

  // Panel styles
  const panelStyles: CSSProperties = {
    position: 'fixed',
    left: '20px',
    top: '160px',
    width: '300px',
    height: 'calc(100vh - 180px)',
    backgroundColor: 'rgba(255, 255, 255, 0.98)',
    boxShadow: '0 6px 24px rgba(0, 0, 0, 0.15)',
    backdropFilter: 'blur(10px)',
    zIndex: 1000,
    borderRadius: '16px',
    border: '1px solid rgba(229, 231, 235, 0.8)',
    overflow: 'hidden',
    padding: '0',
    transition: 'transform 0.3s ease-in-out, opacity 0.3s ease-in-out',
    opacity: isVisible ? 1 : 0,
    transform: isVisible ? 'translateX(0)' : 'translateX(-20px)'
  };
  
  return (
    <div style={panelStyles} className="notification-panel">
      {/* Panel header */}
      <div 
        className="panel-header"
        style={{
          padding: '16px',
          background: 'linear-gradient(135deg, #ff8800 0%, #ff6600 100%)',
          color: 'white',
          fontWeight: 'bold',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderBottom: '1px solid #e5e7eb'
        }}
      >
        <div className="flex items-center gap-2">
          <FaMapMarkedAlt />
          <span className="text-lg">{title}</span>
        </div>
      </div>
      
      {/* POI list */}
      <ScrollArea className="h-full py-2">
        <div className="px-4 py-2">
          <h3 className="text-sm font-medium text-gray-500 mb-3">Points of Interest</h3>
          
          {pois.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              No points of interest found in this area.
            </div>
          ) : (
            <div className="space-y-3">
              {pois.map(poi => (
                <div 
                  key={poi.id}
                  className="bg-white rounded-lg shadow-sm border border-gray-100 p-3 hover:bg-gray-50 cursor-pointer"
                  onClick={() => onSelect(poi)}
                >
                  <div className="flex items-start gap-3">
                    <div className="rounded-full bg-gray-100 p-2 mt-1">
                      {categoryIcons[poi.type] || <FaCompass className="text-gray-500" />}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium">{poi.name}</h4>
                      {poi.description && (
                        <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                          {poi.description}
                        </p>
                      )}
                      <div className="mt-2 flex items-center">
                        <Badge variant="outline" className="text-xs">
                          {poi.type}
                        </Badge>
                      </div>
                    </div>
                    <FaChevronRight className="text-gray-400 self-center" />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
};

export default NotificationPanel; 