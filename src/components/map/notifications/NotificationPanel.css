/* NotificationPanel.css */
.notification-panel {
  position: fixed;
  left: 0;
  top: 0;
  width: 320px;
  height: 100vh;
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  z-index: 1000;
  border-right: 4px solid #10b981;
  overflow: hidden;
  padding: 0;
  transform: translateX(0);
  transition: transform 0.5s ease-in-out, opacity 0.5s ease-in-out;
  opacity: 1;
}

.notification-panel.hidden {
  transform: translateX(-120%);
  opacity: 0;
}

/* Enhance the panel aesthetics */
.notification-panel .badge {
  transition: all 0.2s ease;
  background-color: #f97316; /* Orange background */
  color: white;
  font-weight: bold;
}

.notification-panel .badge:hover {
  transform: scale(1.05);
  background-color: #ea580c; /* Darker orange on hover */
}

/* POI Card Animation */
.notification-panel .poi-card {
  padding: 12px;
  border-radius: 8px;
  background-color: white;
  margin-bottom: 12px;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.notification-panel .poi-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.notification-panel .poi-card.selected {
  border-color: #4A90E2;
  background-color: #f0f7ff;
}

/* Category badges */
.notification-panel .category-badge {
  display: inline-block;
  padding: 4px 8px;
  margin-right: 6px;
  margin-bottom: 6px;
  border-radius: 16px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: #f0f0f0;
  color: #333;
}

.notification-panel .category-badge:hover {
  background-color: #e0e0e0;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.notification-panel .category-badge.selected {
  background-color: #4A90E2;
  color: white;
}

/* Continue Journey Button Pulse Animation */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.notification-panel .continue-journey-btn {
  background-color: #4A90E2;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  width: 100%;
  margin-top: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;
}

.notification-panel .continue-journey-btn:hover {
  background-color: #3A80D2;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.notification-panel .discovery-toggle {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  cursor: pointer;
  user-select: none;
}
