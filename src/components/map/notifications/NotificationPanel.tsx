/**
 * NotificationPanel.tsx
 * 
 * Purpose: Renders the panel used for discovering and selecting Points of Interest (POIs)
 *          *during* the journey animation phases managed by TravelAnimator. This panel
 *          typically appears when the user is exploring the initial city or when 
 *          the POI discovery feature is active along the route.
 * 
 * Features: Includes POI filtering, category selection, and a toggle for enabling/
 *           disabling POI discovery.
 * 
 * Usage: Rendered conditionally by TravelAnimator.tsx based on the `journeyPhase` 
 *        (e.g., 'initial_city', 'selecting_pois') and the `currentArea`.
 */
import React, { useState, useEffect, CSSProperties } from 'react';
import { PointOfInterest } from '@/types/poi';
import { JourneyPhase } from '../utils/types';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Slider } from '@/components/ui/slider';
import { 
  FaCompass, 
  FaHiking, 
  FaMonument, 
  FaLandmark,
  FaFilter,
  FaClock,
  FaDollarSign,
  FaMapMarkedAlt,
  FaChevronRight,
  FaPlay,
  FaCheck,
  FaStar,
  FaMountain,
  FaTree,
  FaMapMarked,
  FaSearchLocation
} from 'react-icons/fa';
import { Switch } from '@/components/ui/switch';
import '@/styles/components/notification-panel.css';
import * as turf from '@turf/turf';
import { ANIMATION_CONSTANTS } from '../utils/constants';

interface NotificationPanelProps {
  currentArea: string | null;
  discoveredPOIs: PointOfInterest[];
  onPOISelect: (poi: PointOfInterest) => void;
  selectedPOIs: Set<string>;
  journeyPhase: JourneyPhase;
  selectedCategories: string[];
  onCategoryChange: (categories: string[]) => void;
  isDiscoveryEnabled: boolean;
  onToggleDiscovery: () => void;
  onContinueJourney: () => void;
  // New props for map-based filtering
  mapBounds?: [[number, number], [number, number]] | null; // SW and NE corners of visible map area
  currentZoom?: number; // Current map zoom level
  focusedCity?: [number, number] | null; // Center coordinates of focused city
}

// Updated categories focused on road trips and chauffeur experiences
const CATEGORIES = [
  { id: 'monument', icon: FaMonument, label: 'Monuments & Culture' },
  { id: 'landmark', icon: FaLandmark, label: 'Historic Sites' },
  { id: 'nature', icon: FaMountain, label: 'Nature & Landscapes' },
  { id: 'activity', icon: FaCompass, label: 'Activities & Experiences' }
];

// Distance settings for POI filtering based on city and zoom
const FILTER_DISTANCES = {
  CITY_RADIUS: 64, // 40 miles ~= 64km around city when zoomed in (default)
  VISIBLE_RADIUS: 100, // 100km max when viewing map area (zoomed out)
  MIN_ZOOM_FOR_CITY_ONLY: 9, // Below this zoom level, show all visible POIs
  MAX_ZOOM_FOR_FULL_VIEW: 5, // At or below this zoom, show everything within VISIBLE_RADIUS
};

export const NotificationPanel: React.FC<NotificationPanelProps> = ({
  currentArea,
  discoveredPOIs,
  onPOISelect,
  selectedPOIs,
  journeyPhase,
  selectedCategories,
  onCategoryChange,
  isDiscoveryEnabled,
  onToggleDiscovery,
  onContinueJourney,
  mapBounds = null,
  currentZoom = 12,
  focusedCity = null
}) => {
  console.log('📋 NotificationPanel rendering:', {
    currentArea,
    discoveredPOIsCount: discoveredPOIs.length,
    selectedPOIsCount: selectedPOIs.size,
    journeyPhase,
    isDiscoveryEnabled,
    mapBounds,
    currentZoom
  });

  // Add detailed logging to track component rendering and props
  useEffect(() => {
    console.log('NotificationPanel rendered with:', {
      currentArea,
      discoveredPOIsCount: discoveredPOIs?.length || 0,
      selectedPOIsCount: selectedPOIs?.size || 0,
      journeyPhase,
      isDiscoveryEnabled,
      selectedCategories,
      mapBounds,
      currentZoom,
      focusedCity
    });
  }, [currentArea, discoveredPOIs, selectedPOIs, journeyPhase, isDiscoveryEnabled, selectedCategories, mapBounds, currentZoom, focusedCity]);

  const [showFilters, setShowFilters] = useState(false);
  const [maxDuration, setMaxDuration] = useState(8); // in hours
  const [isPaidOnly, setIsPaidOnly] = useState(false);
  const [isFreeOnly, setIsFreeOnly] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [filterByMapView, setFilterByMapView] = useState(true); // New state for toggling map-based filtering

  // Add animation for panel entry
  useEffect(() => {
    // Slight delay to ensure proper animation
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 100);
    
    return () => {
      clearTimeout(timer);
    };
  }, []);

  // Function to check if POI is within the visible map area or city radius
  const isPOIVisible = (poi: PointOfInterest): boolean => {
    if (!filterByMapView) return true; // Skip map-based filtering if disabled
    
    if (!poi.coordinates) return false;
    
    // Check if POI is within city radius when a city is focused
    if (focusedCity && currentZoom >= FILTER_DISTANCES.MIN_ZOOM_FOR_CITY_ONLY) {
      try {
        const distance = turf.distance(
          turf.point(focusedCity),
          turf.point(poi.coordinates),
          { units: 'kilometers' }
        );
        return distance <= FILTER_DISTANCES.CITY_RADIUS;
      } catch (error) {
        console.error('Error calculating distance from city:', error);
        return true; // Include in case of error
      }
    }
    
    // When zoomed out, check if POI is within visible map bounds
    if (mapBounds) {
      try {
        // Create a bounding box from the map bounds
        const boundingBox = turf.bboxPolygon([
          mapBounds[0][0], // SW longitude
          mapBounds[0][1], // SW latitude
          mapBounds[1][0], // NE longitude
          mapBounds[1][1]  // NE latitude
        ]);
        
        // Check if POI is within the bounding box
        return turf.booleanPointInPolygon(
          turf.point(poi.coordinates),
          boundingBox
        );
      } catch (error) {
        console.error('Error checking if POI is in bounds:', error);
        return true; // Include in case of error
      }
    }
    
    // If no map bounds or city focus is available, include all POIs
    return true;
  };

  // Filter POIs based on all criteria
  const filteredPOIs = discoveredPOIs.filter(poi => {
    // First filter by map visibility
    if (!isPOIVisible(poi)) return false;
    
    // Map legacy categories to new ones
    const poiCategory = poi.type === 'restaurant' ? 'activity' : 
                        poi.type === 'accommodation' ? 'activity' : 
                        poi.type;
    
    const matchesCategory = selectedCategories.length === 0 || 
                           selectedCategories.includes(poiCategory) || 
                           (poi.tags && poi.tags.some(tag => selectedCategories.includes(tag)));
    const matchesDuration = !poi.duration || poi.duration <= maxDuration;
    
    // Handle new pricing filter
    let matchesPricing = true;
    if (isPaidOnly && poi.cost === 0) matchesPricing = false;
    if (isFreeOnly && poi.cost > 0) matchesPricing = false;
    
    return matchesCategory && matchesDuration && matchesPricing;
  });

  // Deduplicate POIs to prevent duplicate key issues
  const uniquePOIs = Array.from(
    new Map(filteredPOIs.map(poi => [poi.id, poi])).values()
  );

  // Handle price filter changes
  const handlePriceFilterChange = (type: 'free' | 'paid') => {
    if (type === 'free') {
      // If free is being toggled on, turn off paid
      if (!isFreeOnly) setIsPaidOnly(false);
      setIsFreeOnly(!isFreeOnly);
    } else {
      // If paid is being toggled on, turn off free
      if (!isPaidOnly) setIsFreeOnly(false); 
      setIsPaidOnly(!isPaidOnly);
    }
  };

  // Calculate visible POI count vs total
  const visibleCount = uniquePOIs.length;
  const totalCount = discoveredPOIs.length;
  const filteredOutCount = totalCount - visibleCount;

  // Add some custom inline styles to ensure panel visibility
  const panelStyles: CSSProperties = {
    position: 'fixed',
    left: '20px',
    top: '160px', // Increased to account for the taller orange header
    width: '300px',
    height: 'calc(100vh - 180px)', // Adjusted height accordingly
    backgroundColor: 'rgba(255, 255, 255, 0.98)',
    boxShadow: '0 6px 24px rgba(0, 0, 0, 0.15)',
    backdropFilter: 'blur(10px)',
    zIndex: ANIMATION_CONSTANTS.Z_INDEX.NOTIFICATION_PANEL, // Use consistent z-index from constants
    borderRadius: '16px',
    border: '1px solid rgba(229, 231, 235, 0.8)',
    overflow: 'hidden',
    padding: '0',
    transition: 'transform 0.3s ease-in-out, opacity 0.3s ease-in-out',
    opacity: 0,
    transform: 'translateX(-20px)'
  };
  
  const contentStyles: CSSProperties = {
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    gap: '12px',
    padding: '0'
  };

  const headerStyles: CSSProperties = {
    padding: '16px',
    borderBottom: '1px solid rgba(229, 231, 235, 0.5)',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    backdropFilter: 'blur(4px)'
  };

  const bodyStyles: CSSProperties = {
    padding: '0 16px',
    flex: '1',
    overflowY: 'auto'
  };

  const footerStyles: CSSProperties = {
    padding: '16px',
    borderTop: '1px solid rgba(229, 231, 235, 0.5)',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    backdropFilter: 'blur(4px)'
  };

  return (
    <div 
      className="notification-panel"
      style={{
        ...panelStyles,
        opacity: isVisible ? 1 : 0,
        transform: isVisible ? 'translateX(0)' : 'translateX(-20px)'
      }}
      data-testid="notification-panel"
    >
      <div style={contentStyles}>
        {/* Panel header */}
        <div style={headerStyles}>
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-lg font-bold flex items-center gap-2">
              <FaMapMarkedAlt className="text-green-600" />
              {currentArea && `Discover ${currentArea}`}
              {!currentArea && "Points of Interest"}
            </h3>
            {journeyPhase === 'initial_city' && (
              <Button 
                size="sm" 
                variant="ghost"
                className="flex items-center justify-center p-2 h-8 w-8 rounded-full"
                onClick={() => setShowFilters(!showFilters)}
              >
                <FaFilter className={showFilters ? "text-green-600" : "text-gray-500"} />
              </Button>
            )}
          </div>

          {/* Filter chips visible in all modes */}
          <div className="flex gap-2 flex-wrap">
            {CATEGORIES.map((category) => {
              const Icon = category.icon;
              const isSelected = selectedCategories.includes(category.id);
              
              return (
                <Badge
                  key={category.id}
                  variant={isSelected ? "default" : "outline"}
                  className={`cursor-pointer flex items-center gap-1 ${
                    isSelected ? 'bg-green-600 hover:bg-green-700' : 'hover:bg-green-100'
                  }`}
                  onClick={() => {
                    if (isSelected) {
                      onCategoryChange(selectedCategories.filter(c => c !== category.id));
                    } else {
                      onCategoryChange([...selectedCategories, category.id]);
                    }
                  }}
                >
                  <Icon className="w-3 h-3" />
                  {category.label}
                </Badge>
              );
            })}
          </div>
        </div>

        <div style={bodyStyles}>
          {/* Map View Filter Toggle */}
          <div className="px-2 py-3 flex items-center justify-between border-b border-gray-200 mb-3">
            <div className="flex items-center gap-2">
              <FaMapMarked className="text-blue-600 w-4 h-4" />
              <span className="text-sm font-medium text-blue-800">Filter by Map View</span>
            </div>
            <input
              type="checkbox"
              id="map-view-toggle"
              checked={filterByMapView}
              onChange={() => setFilterByMapView(!filterByMapView)}
              className="sr-only"
            />
            <label
              htmlFor="map-view-toggle"
              className={`relative inline-flex items-center cursor-pointer h-6 w-11 rounded-full transition-colors ${
                filterByMapView ? 'bg-blue-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`absolute left-1 right-1 h-4 w-4 rounded-full bg-white transform transition-transform ${
                  filterByMapView ? 'translate-x-5' : 'translate-x-0'
                }`}
              />
            </label>
          </div>

          {/* Filter Toggle with animation */}
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="w-full flex items-center justify-between gap-2 bg-gray-50/80 hover:bg-gray-100/80 transition-all duration-300"
          >
            <span className="flex items-center gap-2">
              <FaFilter className="w-4 h-4 text-gray-500" />
              <span>Filters</span>
            </span>
            <span className={`transition-transform duration-300 ${showFilters ? 'rotate-90' : ''}`}>
              <FaChevronRight className="w-3 h-3" />
            </span>
          </Button>

          {/* Enhanced Filters Section with animation */}
          <div className={`space-y-4 p-3 bg-gray-50/80 rounded-lg border border-gray-100 transition-all duration-300 ease-in-out overflow-hidden ${showFilters ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0 p-0 border-0'}`}>
            {/* Category Filters */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Categories</label>
              <div className="flex flex-wrap gap-2">
                {CATEGORIES.map(({ id, icon: Icon, label }) => (
                  <Badge
                    key={id}
                    variant={selectedCategories.includes(id) ? "default" : "outline"}
                    className={`cursor-pointer flex items-center gap-1 px-3 py-1 transition-all duration-200 ${
                      selectedCategories.includes(id) 
                        ? 'bg-primary text-primary-foreground shadow-md transform hover:scale-105' 
                        : 'hover:border-primary/50'
                    }`}
                    onClick={() => {
                      const newCategories = selectedCategories.includes(id)
                        ? selectedCategories.filter(c => c !== id)
                        : [...selectedCategories, id];
                      onCategoryChange(newCategories);
                    }}
                  >
                    <Icon className="w-3 h-3" />
                    {label}
                  </Badge>
                ))}
               {selectedCategories.length > 0 && (
                  <button onClick={() => onCategoryChange([])} className="text-xs text-morocco-blue hover:underline">Clear</button>
               )}
              </div>
            </div>

            {/* Duration Filter */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700 flex items-center gap-1">
                  <FaClock className="w-3 h-3 text-gray-500" />
                  Max Duration
                </label>
                <span className="text-xs font-medium text-gray-500">{maxDuration} hours</span>
              </div>
              <Slider
                value={[maxDuration]}
                min={1}
                max={8}
                step={1}
                onValueChange={(value) => setMaxDuration(value[0])}
                className="py-2"
              />
            </div>

            {/* Pricing Filters */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 flex items-center gap-1">
                <FaDollarSign className="w-3 h-3 text-gray-500" />
                Pricing
              </label>
              <div className="flex flex-wrap gap-2">
                <Badge
                  variant={isFreeOnly ? "default" : "outline"}
                  className={`cursor-pointer flex items-center gap-1 px-3 py-1 transition-all duration-200 ${
                    isFreeOnly ? 'bg-primary text-primary-foreground shadow-md' : ''
                  }`}
                  onClick={() => handlePriceFilterChange('free')}
                >
                  Free Only
                </Badge>
                <Badge
                  variant={isPaidOnly ? "default" : "outline"}
                  className={`cursor-pointer flex items-center gap-1 px-3 py-1 transition-all duration-200 ${
                    isPaidOnly ? 'bg-primary text-primary-foreground shadow-md' : ''
                  }`}
                  onClick={() => handlePriceFilterChange('paid')}
                >
                  Paid Only
                </Badge>
                <Badge
                  variant={!isFreeOnly && !isPaidOnly ? "default" : "outline"}
                  className={`cursor-pointer flex items-center gap-1 px-3 py-1 transition-all duration-200 ${
                    !isFreeOnly && !isPaidOnly ? 'bg-primary text-primary-foreground shadow-md' : ''
                  }`}
                  onClick={() => {
                    setIsFreeOnly(false);
                    setIsPaidOnly(false);
                  }}
                >
                  All
                </Badge>
              </div>
            </div>
          </div>
          
          {/* POI List Title */}
          <div className="px-4 py-2 bg-gray-50/80 border-y border-gray-200 flex items-center justify-between">
            <span className="font-medium text-gray-700">
              {visibleCount} {visibleCount === 1 ? 'Attraction' : 'Attractions'}
              {filterByMapView && filteredOutCount > 0 && (
                <span className="ml-1 text-xs font-normal text-gray-500">
                  (of {totalCount} total)
                </span>
              )}
            </span>
            {selectedPOIs.size > 0 && (
              <Badge className="bg-primary-light text-primary">
                {selectedPOIs.size} Selected
              </Badge>
            )}
          </div>

          {/* Map Filtering Info - Only show when filtering by map */}
          {filterByMapView && focusedCity && (
            <div className="px-4 py-2 bg-blue-50/80 border-b border-blue-100 text-xs text-blue-700 flex items-center gap-2">
              <FaSearchLocation className="w-3 h-3 flex-shrink-0" />
              <span>
                {currentZoom >= FILTER_DISTANCES.MIN_ZOOM_FOR_CITY_ONLY 
                  ? `Showing attractions within 40 miles of ${currentArea || 'selected city'}`
                  : 'Showing attractions in the visible map area'}
              </span>
            </div>
          )}

          {/* POI List */}
          <ScrollArea className="flex-1 h-[calc(100vh-400px)]">
            {uniquePOIs.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-40 text-center p-4">
                <FaMapMarkedAlt className="w-8 h-8 text-gray-300 mb-2" />
                <p className="text-gray-500">
                  {filterByMapView && discoveredPOIs.length > 0
                    ? 'No attractions in the current map view.'
                    : 'No attractions discovered yet in this area.'}
                </p>
                {filterByMapView && discoveredPOIs.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => setFilterByMapView(false)}
                  >
                    Show All Attractions
                  </Button>
                )}
                {!isDiscoveryEnabled && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={onToggleDiscovery}
                  >
                    Enable Discovery
                  </Button>
                )}
              </div>
            ) : (
              <div className="space-y-2 pb-4">
                {uniquePOIs.map((poi) => {
                  const isSelected = selectedPOIs.has(poi.id);
                  const IconComponent = 
                    poi.type === 'monument' || poi.type === 'landmark' ? FaLandmark :
                    poi.type === 'nature' ? FaMountain :
                    poi.type === 'activity' ? FaCompass :
                    FaMapMarkedAlt;

                  return (
                    <div 
                      key={poi.id}
                      onClick={() => onPOISelect(poi)}
                      className={`group flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-all 
                        ${isSelected 
                          ? 'bg-green-50 border-l-4 border-green-600' 
                          : 'border border-gray-100 bg-white hover:border-gray-300 hover:shadow-sm'
                        }`}
                      role="button"
                      tabIndex={0}
                      onKeyDown={(e) => (e.key === 'Enter' || e.key === ' ') && onPOISelect(poi)}
                      aria-pressed={isSelected}
                    >
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <IconComponent className={`w-4 h-4 ${isSelected ? 'text-green-600' : 'text-gray-500'}`} />
                          <h3 className="font-medium text-gray-900 truncate">
                            {poi.name}
                          </h3>
                          {poi.cost > 0 && (
                            <FaDollarSign className="w-3 h-3 text-gray-500" title="Paid attraction" />
                          )}
                        </div>
                        <div className="flex items-center gap-3 mt-1 text-xs text-gray-500">
                          {poi.duration && (
                            <span className="flex items-center gap-1">
                              <FaClock className="w-3 h-3" /> 
                              {poi.duration}h
                            </span>
                          )}
                          {poi.type && (
                            <span className="capitalize flex items-center gap-1">
                              {poi.type.replace(/_/g, ' ')}
                            </span>
                          )}
                        </div>
                      </div>
                      <Button 
                        size="sm"
                        variant={isSelected ? "ghost" : "outline"}
                        className={`min-w-[2.5rem] h-8 px-2 transition-all ${
                          isSelected 
                            ? 'bg-green-100 text-green-700 hover:bg-green-200' 
                            : 'text-gray-600 hover:bg-gray-100'
                        }`}
                        onClick={(e) => {
                          e.stopPropagation();
                          onPOISelect(poi);
                        }}
                        aria-label={isSelected ? `Remove ${poi.name} from itinerary` : `Add ${poi.name} to itinerary`}
                      >
                        {isSelected ? <FaCheck className="w-3 h-3" /> : 'Add'}
                      </Button>
                    </div>
                  );
                })}
              </div>
            )}
          </ScrollArea>
        </div>

        {/* Trip Planning Tips */}
        <div className="p-3 bg-blue-50 border-t border-blue-100 text-sm text-blue-700">
          <h4 className="font-semibold mb-1">Road Trip Tips:</h4>
          <ul className="list-disc list-inside space-y-1 text-xs">
            <li>Select attractions that fit your interests and time constraints</li>
            <li>We'll build your customized journey based on your selections</li>
            <li>Your private driver will guide you between destinations</li>
            <li>Entrance fees for paid attractions are not included in quotes</li>
          </ul>
        </div>
      </div>
    </div>
  );
}; 