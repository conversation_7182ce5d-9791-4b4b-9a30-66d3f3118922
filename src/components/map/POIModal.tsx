import React, { useState, useEffect } from 'react';
import { PointOfInterest } from '@/types/POITypes';
import { X, MapPin, Clock, Star, DollarSign, Calendar, Users, Camera, ExternalLink, Heart, Share2, ChevronLeft, ChevronRight } from 'lucide-react';

interface POIModalProps {
  poi: PointOfInterest | null;
  isVisible: boolean;
  onClose: () => void;
  onAddToItinerary?: (poi: PointOfInterest) => void;
  onBooking?: (poi: PointOfInterest) => void;
  isInItinerary?: boolean;
}

const POIModal: React.FC<POIModalProps> = ({
  poi,
  isVisible,
  onClose,
  onAddToItinerary,
  onBooking,
  isInItinerary = false
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isImageLoading, setIsImageLoading] = useState(true);

  useEffect(() => {
    if (isVisible && poi) {
      setCurrentImageIndex(0);
      setIsImageLoading(true);
    }
  }, [isVisible, poi]);

  if (!isVisible || !poi) {
    return null;
  }

  const images = poi.images || [];
  const hasMultipleImages = images.length > 1;

  const nextImage = () => {
    if (hasMultipleImages) {
      setCurrentImageIndex((prev) => (prev + 1) % images.length);
      setIsImageLoading(true);
    }
  };

  const prevImage = () => {
    if (hasMultipleImages) {
      setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
      setIsImageLoading(true);
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'landmark': return 'var(--morocco-yellow)';
      case 'cultural': return 'var(--morocco-blue)';
      case 'nature': return 'var(--morocco-green)';
      case 'restaurant': return 'var(--morocco-red)';
      case 'accommodation': return 'var(--morocco-purple)';
      default: return 'var(--morocco-blue)';
    }
  };

  const currentImage = images[currentImageIndex] || '/images/placeholder.svg';

  return (
    <>
      {/* Backdrop */}
      <div
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          zIndex: 1000,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '2rem'
        }}
        onClick={onClose}
      >
        {/* Modal */}
        <div
          className="poi-modal"
          style={{
            backgroundColor: 'white',
            borderRadius: 'var(--radius-lg)',
            maxWidth: '600px',
            width: '100%',
            maxHeight: '90vh',
            overflow: 'hidden',
            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
            position: 'relative'
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header with Image */}
          <div style={{ position: 'relative', height: '300px', overflow: 'hidden' }}>
            <img
              src={currentImage}
              alt={poi.name}
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover',
                opacity: isImageLoading ? 0 : 1,
                transition: 'opacity 0.3s ease'
              }}
              onLoad={() => setIsImageLoading(false)}
              onError={(e) => {
                e.currentTarget.src = '/images/placeholder.svg';
                setIsImageLoading(false);
              }}
            />
            
            {/* Loading overlay */}
            {isImageLoading && (
              <div style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: 'rgba(0, 71, 171, 0.1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <div
                  className="poi-modal-spinner"
                  style={{
                    width: '40px',
                    height: '40px',
                    border: '3px solid rgba(0, 71, 171, 0.3)',
                    borderTop: '3px solid var(--morocco-blue)',
                    borderRadius: '50%'
                  }}
                />
              </div>
            )}

            {/* Image navigation */}
            {hasMultipleImages && (
              <>
                <button
                  onClick={prevImage}
                  style={{
                    position: 'absolute',
                    left: '1rem',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '50%',
                    width: '40px',
                    height: '40px',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <ChevronLeft size={20} />
                </button>
                <button
                  onClick={nextImage}
                  style={{
                    position: 'absolute',
                    right: '1rem',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '50%',
                    width: '40px',
                    height: '40px',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <ChevronRight size={20} />
                </button>
                
                {/* Image indicators */}
                <div style={{
                  position: 'absolute',
                  bottom: '1rem',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  display: 'flex',
                  gap: '0.5rem'
                }}>
                  {images.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        setCurrentImageIndex(index);
                        setIsImageLoading(true);
                      }}
                      style={{
                        width: '8px',
                        height: '8px',
                        borderRadius: '50%',
                        border: 'none',
                        backgroundColor: index === currentImageIndex ? 'white' : 'rgba(255, 255, 255, 0.5)',
                        cursor: 'pointer'
                      }}
                    />
                  ))}
                </div>
              </>
            )}

            {/* Close button */}
            <button
              onClick={onClose}
              style={{
                position: 'absolute',
                top: '1rem',
                right: '1rem',
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                color: 'white',
                border: 'none',
                borderRadius: '50%',
                width: '40px',
                height: '40px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <X size={20} />
            </button>

            {/* Category badge */}
            <div style={{
              position: 'absolute',
              top: '1rem',
              left: '1rem',
              backgroundColor: getCategoryColor(poi.category || 'other'),
              color: 'white',
              padding: '0.5rem 1rem',
              borderRadius: 'var(--radius-full)',
              fontSize: '0.875rem',
              fontWeight: 600,
              textTransform: 'capitalize'
            }}>
              {poi.category || 'Point of Interest'}
            </div>
          </div>

          {/* Content */}
          <div style={{ padding: '2rem', maxHeight: 'calc(90vh - 300px)', overflowY: 'auto' }}>
            {/* Title and basic info */}
            <div style={{ marginBottom: '1.5rem' }}>
              <h2 style={{
                margin: '0 0 0.5rem',
                fontSize: '1.75rem',
                fontWeight: 700,
                color: 'var(--text-primary)',
                fontFamily: 'var(--font-heading)'
              }}>
                {poi.name}
              </h2>
              
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '1rem' }}>
                <MapPin size={16} color="var(--text-secondary)" />
                <span style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>
                  {poi.location || 'Morocco'}
                </span>
              </div>

              {poi.description && (
                <p style={{
                  margin: 0,
                  fontSize: '1rem',
                  lineHeight: 1.6,
                  color: 'var(--text-primary)'
                }}>
                  {poi.description}
                </p>
              )}
            </div>

            {/* Action buttons */}
            <div style={{ display: 'flex', gap: '1rem', marginBottom: '1.5rem' }}>
              {onAddToItinerary && (
                <button
                  onClick={() => onAddToItinerary(poi)}
                  style={{
                    flex: 1,
                    padding: '0.75rem 1.5rem',
                    backgroundColor: isInItinerary ? 'var(--morocco-green)' : 'var(--morocco-red)',
                    color: 'white',
                    border: 'none',
                    borderRadius: 'var(--radius-md)',
                    cursor: 'pointer',
                    fontFamily: 'var(--font-body)',
                    fontWeight: 600,
                    fontSize: '1rem',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: '0.5rem'
                  }}
                >
                  {isInItinerary ? (
                    <>
                      <Star size={18} />
                      In Itinerary
                    </>
                  ) : (
                    <>
                      <Heart size={18} />
                      Add to Trip
                    </>
                  )}
                </button>
              )}
              
              {onBooking && (
                <button
                  onClick={() => onBooking(poi)}
                  style={{
                    flex: 1,
                    padding: '0.75rem 1.5rem',
                    backgroundColor: 'transparent',
                    color: 'var(--morocco-blue)',
                    border: '2px solid var(--morocco-blue)',
                    borderRadius: 'var(--radius-md)',
                    cursor: 'pointer',
                    fontFamily: 'var(--font-body)',
                    fontWeight: 600,
                    fontSize: '1rem',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: '0.5rem'
                  }}
                >
                  <ExternalLink size={18} />
                  Book Now
                </button>
              )}
            </div>

            {/* Additional info */}
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
              {(poi as any).duration && (
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                  <Clock size={16} color="var(--morocco-blue)" />
                  <span style={{ fontSize: '0.875rem' }}>
                    {(poi as any).duration} hours
                  </span>
                </div>
              )}
              
              {(poi as any).cost && (
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                  <DollarSign size={16} color="var(--morocco-red)" />
                  <span style={{ fontSize: '0.875rem' }}>
                    ${(poi as any).cost}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Add CSS for spinner animation */}
      <style>
        {`
          .poi-modal-spinner {
            animation: poi-modal-spin 1s linear infinite;
          }

          @keyframes poi-modal-spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
        `}
      </style>
    </>
  );
};

export default POIModal;
