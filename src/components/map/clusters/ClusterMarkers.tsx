import React, { useEffect, useRef, useState } from 'react';
import mapboxgl from 'mapbox-gl';
import { CityCluster, RouteCluster, ExtendedPointOfInterest, ClusterFeature } from '../utils/cluster-types';

// Helper function to get marker style based on POI type
const getMarkerStyle = (type: string) => {
  switch (type) {
    case 'accommodation':
      return {
        backgroundColor: '#3B82F6', // Blue
        color: 'white',
        icon: '🏨'
      };
    case 'activity':
      return {
        backgroundColor: '#10B981', // Green
        color: 'white',
        icon: '🎯'
      };
    case 'restaurant':
      return {
        backgroundColor: '#F59E0B', // Yellow
        color: 'white',
        icon: '🍽️'
      };
    case 'landmark':
      return {
        backgroundColor: '#8B5CF6', // Purple
        color: 'white',
        icon: '🏛️'
      };
    default:
      return {
        backgroundColor: '#EF4444', // Red
        color: 'white',
        icon: '📍'
      };
  }
};

interface ClusterMarkersProps {
  map: mapboxgl.Map;
  cityPOIClusters: Record<string, CityCluster>;
  routePOIClusters: Record<string, RouteCluster>;
  selectedCategories: string[];
  onCityClusterClick: (cluster: ClusterFeature) => void;
  onRouteClusterClick: (cluster: ClusterFeature) => void;
  onPOIClick?: (feature: ClusterFeature) => void;
}

/**
 * Component that handles rendering city and route cluster markers on the map
 */
export const ClusterMarkers: React.FC<ClusterMarkersProps> = ({
  map,
  cityPOIClusters,
  routePOIClusters,
  selectedCategories,
  onCityClusterClick,
  onRouteClusterClick,
  onPOIClick
}) => {
  useEffect(() => {
    if (!map) return;

    // Remove existing markers
    const existingMarkers = document.querySelectorAll('.cluster-marker');
    existingMarkers.forEach(marker => marker.remove());

    // Create markers for route clusters
    Object.values(routePOIClusters).forEach(cluster => {
      // Only create a cluster marker if there are multiple POIs
      if (cluster.pois.length > 1) {
        const el = document.createElement('div');
        el.className = 'cluster-marker route-cluster';
        el.innerHTML = `${cluster.pois.length}`;
        
        // Create ClusterFeature for the click handler
        const feature: ClusterFeature = {
          id: cluster.id,
          type: 'Feature',
          geometry: {
            type: 'Point',
            coordinates: cluster.center
          },
          properties: {
            cluster: true,
            cluster_id: parseInt(cluster.id),
            point_count: cluster.pois.length,
            point_count_abbreviated: `${cluster.pois.length}`,
            category: 'route'
          }
        };
        
        el.addEventListener('click', () => onRouteClusterClick(feature));

        new mapboxgl.Marker({
          element: el,
        })
          .setLngLat(cluster.center)
          .addTo(map);
      }
    });

    // Create markers for city clusters
    Object.values(cityPOIClusters).forEach(cluster => {
      const el = document.createElement('div');
      el.className = 'cluster-marker city-cluster';
      el.innerHTML = `${cluster.pois.length}`;
      
      // Create ClusterFeature for the click handler
      const feature: ClusterFeature = {
        id: cluster.id,
        type: 'Feature',
        geometry: {
          type: 'Point',
          coordinates: cluster.center
        },
        properties: {
          cluster: true,
          cluster_id: parseInt(cluster.id),
          point_count: cluster.pois.length,
          point_count_abbreviated: `${cluster.pois.length}`,
          category: 'city',
          name: cluster.city
        }
      };
      
      el.addEventListener('click', () => onCityClusterClick(feature));

      new mapboxgl.Marker({
        element: el,
      })
        .setLngLat(cluster.center)
        .addTo(map);
    });
  }, [map, routePOIClusters, cityPOIClusters, onCityClusterClick, onRouteClusterClick]);

  return null;
};

export default ClusterMarkers; 