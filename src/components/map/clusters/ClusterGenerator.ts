import * as turf from '@turf/turf';
import { Destination, PointOfInterest } from '@/types/POITypes';
import { 
  CITY_RADIUS_KM, 
  POI_CLUSTER_RADIUS,
  ROUTE_POI_CLUSTER_RADIUS,
  CLUSTER_CONSTANTS
} from '../utils/types';

/**
 * Extend the base PointOfInterest type with lng/lat properties for easier access
 */
interface PointOfInterestWithCoords extends PointOfInterest {
  lng: number;
  lat: number;
}

/**
 * Extended Point of Interest with optional city property
 */
export interface ExtendedPointOfInterest {
  id: string;
  name: string;
  coordinates: [number, number]; // [longitude, latitude]
  type?: string;
  description?: string;
  image?: string;
  imageUrl?: string;
  rating?: number;
  price?: number;
  cost?: number;
  duration?: number;
  category?: string;
  categories?: string[];
  tags?: string[];
  address?: string;
  website?: string;
  openingHours?: string;
  city?: string;
  location?: string;
  discovered?: boolean;
  position?: { lat: number; lng: number; name?: string };
  lng?: number;
  lat?: number;
}

/**
 * City-based cluster containing POIs
 */
export interface CityCluster {
  city: string;
  pois: ExtendedPointOfInterest[];
  center: [number, number]; // [lng, lat]
}

/**
 * Route-based cluster containing POIs
 */
export interface RouteCluster {
  id: string;
  pois: ExtendedPointOfInterest[];
  center: [number, number]; // [lng, lat]
}

/**
 * Options for the cluster generator 
 */
export interface ClusterOptions {
  routePoints: [number, number][]; // [lng, lat][]
  pois: PointOfInterest[];
  cityClusterRadius?: number; // in kilometers
  routeClusterRadius?: number; // in kilometers
}

/**
 * Convert BasePointOfInterest to our extended version with lng/lat properties
 */
const convertToExtendedPOI = (poi: PointOfInterest): ExtendedPointOfInterest => {
  if (!poi.coordinates || poi.coordinates.length !== 2) {
    console.error('Invalid POI coordinates:', poi);
    return {
      ...poi,
      lng: 0,
      lat: 0,
      city: poi.location?.includes(',') ? poi.location.split(',')[0].trim() : undefined
    };
  }
  
  return {
    ...poi,
    lng: poi.coordinates[0],
    lat: poi.coordinates[1],
    city: poi.location?.includes(',') ? poi.location.split(',')[0].trim() : undefined
  };
};

/**
 * Calculate distance between two points using the Haversine formula
 */
export const calculateDistance = (
  point1: [number, number], 
  point2: [number, number]
): number => {
  const [lng1, lat1] = point1;
  const [lng2, lat2] = point2;
  
  const R = 6371; // Earth radius in km
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lng2 - lng1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  
  return R * c;
};

/**
 * Find the closest point on a route to a given point
 */
export const findClosestPointOnRoute = (
  point: [number, number],
  routePoints: [number, number][]
): { point: [number, number], distance: number, index: number } => {
  let minDistance = Infinity;
  let closestPoint: [number, number] = [0, 0];
  let closestSegmentIndex = 0;
  
  // Check each segment of the route
  for (let i = 0; i < routePoints.length - 1; i++) {
    const segment: [[number, number], [number, number]] = [routePoints[i], routePoints[i + 1]];
    const projectionResult = projectPointToSegment(point, segment);
    
    if (projectionResult.distance < minDistance) {
      minDistance = projectionResult.distance;
      closestPoint = projectionResult.point;
      closestSegmentIndex = i;
    }
  }
  
  return {
    point: closestPoint,
    distance: minDistance,
    index: closestSegmentIndex
  };
};

/**
 * Project a point onto a line segment
 */
export const projectPointToSegment = (
  point: [number, number],
  segment: [[number, number], [number, number]]
): { point: [number, number], distance: number } => {
  const [x, y] = point;
  const [[x1, y1], [x2, y2]] = segment;
  
  // Convert to Cartesian for simplicity (this is an approximation)
  const dx = x2 - x1;
  const dy = y2 - y1;
  
  // If segment is a point, return the distance to that point
  if (dx === 0 && dy === 0) {
    return {
      point: [x1, y1],
      distance: calculateDistance(point, [x1, y1])
    };
  }
  
  // Calculate projection ratio
  const t = ((x - x1) * dx + (y - y1) * dy) / (dx * dx + dy * dy);
  
  // Clamp t to segment bounds [0, 1]
  const tClamped = Math.max(0, Math.min(1, t));
  
  // Calculate projected point
  const projectedPoint: [number, number] = [
    x1 + tClamped * dx,
    y1 + tClamped * dy
  ];
  
  // Calculate distance to projected point
  const distance = calculateDistance(point, projectedPoint);
  
  return { point: projectedPoint, distance };
};

/**
 * Generate city-based POI clusters
 */
export const generateCityClusters = (
  pois: PointOfInterest[],
  radius: number = 50 // km
): Record<string, CityCluster> => {
  const cityClusters: Record<string, CityCluster> = {};
  
  // Convert POIs to extended format with lng/lat
  const extendedPois = pois.filter(poi => poi.coordinates && poi.coordinates.length === 2)
    .map(convertToExtendedPOI);
  
  // Group POIs by city
  extendedPois.forEach(poi => {
    // Skip POIs without a city
    if (!poi.city) return;
    
    // Create or update city cluster
    if (!cityClusters[poi.city]) {
      cityClusters[poi.city] = {
        city: poi.city,
        pois: [],
        center: [poi.lng, poi.lat] // Initial center
      };
    }
    
    // Add POI to cluster
    cityClusters[poi.city].pois.push(poi);
  });
  
  // Calculate center for each city cluster
  Object.values(cityClusters).forEach(cluster => {
    if (cluster.pois.length === 0) return;
    
    // Calculate average coordinates
    const sumCoords = cluster.pois.reduce(
      (sum, poi) => [sum[0] + poi.lng, sum[1] + poi.lat],
      [0, 0]
    );
    
    cluster.center = [
      sumCoords[0] / cluster.pois.length,
      sumCoords[1] / cluster.pois.length
    ];
  });
  
  return cityClusters;
};

/**
 * Generate route-based POI clusters
 */
export const generateRouteClusters = (
  pois: PointOfInterest[],
  routePoints: [number, number][],
  radius: number = 10 // km - reduced from 30km to 10km for more precise clustering
): Record<string, RouteCluster> => {
  const routeClusters: Record<string, RouteCluster> = {};
  
  // Convert POIs to extended format with lng/lat
  const extendedPois = pois
    .filter(poi => poi.coordinates && poi.coordinates.length === 2)
    .map(convertToExtendedPOI);
  
  // For each POI, find the closest point on the route
  extendedPois.forEach(poi => {
    const closestPointInfo = findClosestPointOnRoute(
      [poi.lng, poi.lat],
      routePoints
    );
    
    // Only include POIs that are actually close to the route (within radius)
    if (closestPointInfo.distance <= radius) {
      const clusterId = `route-segment-${closestPointInfo.index}`;
      
      // Create or update route cluster
      if (!routeClusters[clusterId]) {
        routeClusters[clusterId] = {
          id: clusterId,
          pois: [],
          center: closestPointInfo.point
        };
      }
      
      // Add POI to cluster
      routeClusters[clusterId].pois.push(poi);
    }
  });
  
  // Filter out clusters with only 1 POI - we only want to show clusters for multiple POIs
  const filteredClusters: Record<string, RouteCluster> = {};
  Object.entries(routeClusters).forEach(([id, cluster]) => {
    if (cluster.pois.length > 1) {
      filteredClusters[id] = cluster;
    }
  });
  
  return filteredClusters;
};

/**
 * Main function to generate both city and route clusters
 */
export const generateClusters = (options: ClusterOptions): {
  cityClusters: Record<string, CityCluster>;
  routeClusters: Record<string, RouteCluster>;
} => {
  const { 
    routePoints, 
    pois, 
    cityClusterRadius = 50, 
    routeClusterRadius = 30 
  } = options;
  
  console.log(`Generating clusters with ${pois.length} POIs and ${routePoints.length} route points`);
  
  // Filter out POIs without valid coordinates
  const validPOIs = pois.filter(poi => poi.coordinates && poi.coordinates.length === 2);
  
  if (validPOIs.length < pois.length) {
    console.warn(`Filtered out ${pois.length - validPOIs.length} POIs with invalid coordinates`);
  }
  
  // Generate clusters
  const cityClusters = generateCityClusters(validPOIs, cityClusterRadius);
  const routeClusters = generateRouteClusters(validPOIs, routePoints, routeClusterRadius);
  
  // Log results
  console.log(`Generated ${Object.keys(cityClusters).length} city clusters and ${Object.keys(routeClusters).length} route clusters`);
  
  return { cityClusters, routeClusters };
}; 