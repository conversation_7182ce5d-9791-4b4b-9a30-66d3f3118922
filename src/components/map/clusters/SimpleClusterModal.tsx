import React from 'react';
import { PointOfInterest } from '@/types/poi';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

// Simplified interface for ClusterFeature
interface ClusterFeature {
  id: string;
  properties: {
    point_count: number;
    name?: string;
  };
  geometry: {
    coordinates: [number, number];
  };
}

interface SimpleClusterModalProps {
  clusters: ClusterFeature[];
  onClose: () => void;
  discoveredPOIs: Set<string>;
  onPOIDiscovered: (poi: PointOfInterest) => void;
}

/**
 * A simplified version of the ClusterModal component
 * that displays POIs in a cluster when the cluster marker is clicked
 */
export const SimpleClusterModal: React.FC<SimpleClusterModalProps> = ({
  clusters,
  onClose,
  discoveredPOIs,
  onPOIDiscovered
}) => {
  // If no clusters data, don't render
  if (!clusters || clusters.length === 0) return null;
  
  const cluster = clusters[0];
  const poiCount = cluster.properties.point_count;
  const title = cluster.properties.name 
    ? `${cluster.properties.name} - ${poiCount} Points of Interest`
    : `${poiCount} Points of Interest`;
  
  return (
    <div 
      className="cluster-modal" 
      style={{
        position: 'absolute',
        bottom: '24px',
        right: '24px',
        width: '350px',
        maxHeight: '500px',
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 4px 16px rgba(0, 0, 0, 0.2)',
        zIndex: 1000,
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      <div 
        className="cluster-modal-header"
        style={{
          padding: '16px',
          borderBottom: '1px solid #e5e7eb',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}
      >
        <h3 style={{ margin: 0, fontSize: '16px', fontWeight: 'bold' }}>{title}</h3>
        <button 
          onClick={onClose}
          style={{
            backgroundColor: 'transparent',
            border: 'none',
            cursor: 'pointer',
            fontSize: '18px'
          }}
        >
          ✕
        </button>
      </div>
      
      {/* POI list placeholder - in a real scenario, this would show actual POIs */}
      <div 
        className="poi-list-placeholder"
        style={{
          padding: '16px',
          textAlign: 'center',
          color: '#6b7280'
        }}
      >
        <p>Click to explore {poiCount} points of interest in this area.</p>
        
        <Button 
          className="mt-4"
          onClick={onClose}
        >
          Explore Area
        </Button>
      </div>
    </div>
  );
};

export default SimpleClusterModal; 