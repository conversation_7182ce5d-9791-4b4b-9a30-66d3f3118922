import React, { useState } from 'react';
import { PointOfInterest } from '@/components/PointOfInterestCard';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CityCluster, RouteCluster, ExtendedPointOfInterest } from './ClusterGenerator';

interface ClusterModalProps {
  cluster: CityCluster | RouteCluster | null;
  isCity: boolean;
  onClose: () => void;
  onPOISelect: (poi: ExtendedPointOfInterest) => void;
  selectedCategories: string[];
  onCategoryChange: (categories: string[]) => void;
}

/**
 * Modal that displays POIs in a cluster when the cluster marker is clicked
 */
export const ClusterModal: React.FC<ClusterModalProps> = ({
  cluster,
  isCity,
  onClose,
  onPOISelect,
  selectedCategories,
  onCategoryChange
}) => {
  // If no cluster data, don't render
  if (!cluster) return null;
  
  const pois = cluster.pois;
  const name = isCity ? (cluster as CityCluster).city : '';
  
  // Render different title based on whether it's a city or route cluster
  const title = isCity ? `${name} - ${pois.length} Points of Interest` : `${pois.length} Points of Interest`;
  
  // Filter POIs by selected categories
  const filteredPOIs = pois.filter(poi => {
    if (selectedCategories.length === 0) return true;
    return selectedCategories.includes(poi.type) || 
           poi.tags?.some(tag => selectedCategories.includes(tag));
  });
  
  // Get unique categories from POIs for filter tabs
  const uniqueCategories = Array.from(new Set<string>(
    pois.flatMap(poi => {
      const categories = [poi.type];
      if (poi.tags) categories.push(...poi.tags);
      return categories;
    })
  ));
  
  // Handle category toggle
  const toggleCategory = (category: string) => {
    if (selectedCategories.includes(category)) {
      onCategoryChange(selectedCategories.filter(c => c !== category));
    } else {
      onCategoryChange([...selectedCategories, category]);
    }
  };
  
  return (
    <div 
      className="cluster-modal" 
      style={{
        position: 'absolute',
        bottom: '24px',
        right: '24px',
        width: '350px',
        maxHeight: '500px',
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 4px 16px rgba(0, 0, 0, 0.2)',
        zIndex: 1000,
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      <div 
        className="cluster-modal-header"
        style={{
          padding: '16px',
          borderBottom: '1px solid #e5e7eb',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}
      >
        <h3 style={{ margin: 0, fontSize: '16px', fontWeight: 'bold' }}>{title}</h3>
        <button 
          onClick={onClose}
          style={{
            backgroundColor: 'transparent',
            border: 'none',
            cursor: 'pointer',
            fontSize: '18px'
          }}
        >
          ✕
        </button>
      </div>
      
      {/* Category filters */}
      <div 
        className="category-filters"
        style={{
          padding: '12px 16px',
          borderBottom: '1px solid #e5e7eb',
          display: 'flex',
          flexWrap: 'wrap',
          gap: '8px'
        }}
      >
        {uniqueCategories.map(category => (
          <Badge 
            key={category}
            variant={selectedCategories.includes(category) ? 'default' : 'outline'}
            style={{ cursor: 'pointer' }}
            onClick={() => toggleCategory(category)}
          >
            {category}
          </Badge>
        ))}
        
        {selectedCategories.length > 0 && (
          <Badge 
            variant="secondary"
            style={{ cursor: 'pointer', marginLeft: 'auto' }}
            onClick={() => onCategoryChange([])}
          >
            Clear filters
          </Badge>
        )}
      </div>
      
      {/* POI list */}
      <div 
        className="poi-list"
        style={{
          overflowY: 'auto',
          padding: '8px 0',
          flex: '1'
        }}
      >
        {filteredPOIs.length === 0 ? (
          <div style={{ padding: '16px', textAlign: 'center', color: '#6b7280' }}>
            No POIs match your selected filters.
          </div>
        ) : (
          filteredPOIs.map(poi => (
            <div 
              key={poi.id}
              className="poi-item hover:bg-gray-50"
              style={{
                padding: '12px 16px',
                borderBottom: '1px solid #f3f4f6',
                cursor: 'pointer',
                transition: 'background-color 0.2s'
              }}
              onClick={() => onPOISelect(poi)}
            >
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <div 
                  className="poi-icon"
                  style={{
                    width: '32px',
                    height: '32px',
                    borderRadius: '50%',
                    backgroundColor: 
                      poi.type === 'landmark' ? '#3B82F6' : 
                      poi.type === 'activity' ? '#10B981' : 
                      poi.type === 'restaurant' ? '#F59E0B' : '#6366F1',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontWeight: 'bold',
                    fontSize: '14px',
                    flexShrink: 0
                  }}
                >
                  {poi.type.charAt(0).toUpperCase()}
                </div>
                <div style={{ flex: 1 }}>
                  <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>{poi.name}</div>
                  <div style={{ fontSize: '14px', color: '#6b7280' }}>
                    {poi.description && poi.description.length > 80 
                      ? `${poi.description.slice(0, 80)}...` 
                      : poi.description}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default ClusterModal;