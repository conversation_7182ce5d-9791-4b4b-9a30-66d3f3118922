import React, { useEffect, useRef, useCallback } from 'react';
import mapboxgl from 'mapbox-gl';
import { Destination, PointOfInterest } from '@/types/POITypes';
import * as turf from '@turf/turf';
import { whenMapReady } from '@/utils/MapHelpers';
import { throttle } from '@/utils/throttleUtils';

// Helper function to validate coordinates
const isValidCoordinates = (coordinates: any): coordinates is [number, number] => {
  return (
    Array.isArray(coordinates) &&
    coordinates.length === 2 &&
    typeof coordinates[0] === 'number' &&
    !isNaN(coordinates[0]) &&
    typeof coordinates[1] === 'number' &&
    !isNaN(coordinates[1]) &&
    coordinates[0] >= -180 && 
    coordinates[0] <= 180 &&
    coordinates[1] >= -90 && 
    coordinates[1] <= 90
  );
};

interface DirectDestinationMarkersProps {
  map: mapboxgl.Map;
  destinations: Destination[];
  selectedDestinations: Destination[];
  onDestinationClick: (destination: Destination) => void;
  pois: PointOfInterest[];
}

interface CategoryCount {
  accommodation: number;
  activity: number;
  restaurant: number;
  landmark: number;
}

const MAJOR_CITIES = ['Marrakech', 'Casablanca', 'Fez', 'Rabat', 'Tangier'];
const RADIUS_MILES = 25; // Used for POI stats, not map view

// --- Marker Style Constants ---
const markerBaseStyle = {
  borderRadius: '16px',
  background: 'rgba(255,255,255,0.85)',
  boxShadow: '0 4px 16px rgba(0,0,0,0.18)',
  border: '2px solid #41B3A3',
  color: '#1a1a1a',
  minWidth: '110px',
  padding: '7px 14px',
  fontWeight: '500',
  fontSize: '13px',
  transition: 'all 0.2s cubic-bezier(0.4,0,0.2,1)',
  backdropFilter: 'blur(4px)',
  WebkitBackdropFilter: 'blur(4px)',
  pointerEvents: 'auto',
  zIndex: '1500',
};
const markerSelectedStyle = {
  background: 'linear-gradient(90deg, #41B3A3 0%, #1de9b6 100%)',
  color: 'white',
  border: '2px solid #fff',
  boxShadow: '0 0 0 4px rgba(29,233,182,0.18), 0 8px 24px rgba(0,0,0,0.22)',
  zIndex: '1600',
};

/**
 * This component directly renders destination markers by appending DOM elements
 * to the Mapbox container instead of using React's DOM - this avoids positioning issues.
 * Uses a Map internally to manage markers by ID for efficient updates and cleanup.
 */
const DirectDestinationMarkers: React.FC<DirectDestinationMarkersProps> = ({
  map,
  destinations,
  selectedDestinations,
  onDestinationClick,
  pois
}) => {
  const markerElementsRef = useRef<Map<string, HTMLDivElement>>(new Map());
  const markersContainerRef = useRef<HTMLDivElement | null>(null);
  const tooltipRef = useRef<HTMLDivElement | null>(null);
  const isMountedRef = useRef<boolean>(false); // Use to prevent updates after unmount
  const markersLoadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Log every render and the selectedDestinations prop
  console.log('[DirectDestinationMarkers] Rendered. selectedDestinations:', selectedDestinations);
  useEffect(() => {
    console.log('[DirectDestinationMarkers] useEffect running. selectedDestinations:', selectedDestinations);
  }, [selectedDestinations]);

  // --- Helper functions ---
  const getPoiStats = (destinationName: string, coordinates: [number, number]): { total: number, categories: CategoryCount } => {
    try {
      const point = turf.point(coordinates);
      const nearbyPois = pois.filter((poi: PointOfInterest) => {
        // Skip POIs without valid coordinates
        if (!poi.coordinates || !Array.isArray(poi.coordinates) || poi.coordinates.length !== 2 || 
            typeof poi.coordinates[0] !== 'number' || typeof poi.coordinates[1] !== 'number') {
          console.warn(`Invalid POI coordinates for "${poi.name}"`, poi.coordinates);
          return false;
        }
        
        try {
          const poiPoint = turf.point(poi.coordinates);
          const distance = turf.distance(point, poiPoint, { units: 'miles' });
          return distance <= RADIUS_MILES;
        } catch (error) {
          console.warn(`Error calculating distance for POI "${poi.name}":`, error);
          return false;
        }
      });
      const categories = nearbyPois.reduce((acc: CategoryCount, poi: PointOfInterest) => {
        const type = poi.category as keyof CategoryCount;
        if (acc[type] !== undefined) {
          acc[type]++;
        }
        return acc;
      }, { accommodation: 0, activity: 0, restaurant: 0, landmark: 0 } as CategoryCount);
      return { total: nearbyPois.length, categories };
    } catch (error) {
      console.error(`Error in getPoiStats for ${destinationName}:`, error);
      return { 
        total: 0, 
        categories: { 
          accommodation: 0, 
          activity: 0, 
          restaurant: 0, 
          landmark: 0
        } 
      };
    }
  };

  const calculateExplorationTime = (poiCount: number): string => {
    const avgTimePerPoi = 2; // hours
    const totalHours = poiCount * avgTimePerPoi;
    return totalHours < 24 ? `${totalHours} hours` : `${Math.round(totalHours / 24)} days`;
  };

  const getDistanceToNextCity = (currentDestId: string): string | null => {
     const currentIndex = destinations.findIndex(d => d.id === currentDestId);
     if (currentIndex === -1 || currentIndex >= destinations.length - 1) return null;
     const current = destinations[currentIndex];
     const next = destinations[currentIndex + 1];
     if (!current?.coordinates || !next?.coordinates) return null;
     try {
        const distance = turf.distance(
            turf.point(current.coordinates),
            turf.point(next.coordinates),
            { units: 'kilometers' }
        );
        return `${Math.round(distance)} km to ${next.name}`;
     } catch (error) {
        console.error("Error calculating distance:", error);
        return null;
     }
  };
  // --- End Helper functions ---

  // Function to create or update a single marker
  const createOrUpdateMarker = useCallback((destination: Destination, bounds: mapboxgl.LngLatBounds) => {
    // Skip markers with invalid coordinates
    if (!destination.coordinates || !isValidCoordinates(destination.coordinates)) {
      console.warn(`Skipping destination marker for "${destination.name || destination.id}" due to invalid coordinates:`, destination.coordinates);
      return;
    }

    const destinationId = destination.id;
    const existingMarker = markerElementsRef.current.get(destinationId);
    const point = map.project(destination.coordinates);
    const isSelected = selectedDestinations.some(d => d.id === destination.id);
    const isVisible = bounds.contains(destination.coordinates);

    if (isVisible) {
      if (existingMarker) {
        // Update existing marker position and style
        // Directly set transform property for better GPU acceleration
        existingMarker.style.transform = 'translate3d(-50%, -50%, 0)';
        existingMarker.style.left = `${point.x}px`;
        existingMarker.style.top = `${point.y}px`;
        existingMarker.style.backgroundColor = isSelected ? markerSelectedStyle.background : markerBaseStyle.background;
        existingMarker.style.zIndex = isSelected ? markerSelectedStyle.zIndex : markerBaseStyle.zIndex;
        existingMarker.innerHTML = isSelected ? '<span style="font-size: 12px; color: white; line-height: 25px; font-weight: bold;">✓</span>' : ''; // Add/remove checkmark
        existingMarker.style.display = 'block'; // Ensure visibility
        if (isSelected) {
          existingMarker.classList.add('selected');
          existingMarker.style.borderColor = markerSelectedStyle.border;
          existingMarker.style.backgroundColor = markerSelectedStyle.background;
          existingMarker.style.zIndex = markerSelectedStyle.zIndex;
        } else {
          existingMarker.classList.remove('selected');
          existingMarker.style.borderColor = markerBaseStyle.border;
          existingMarker.style.backgroundColor = markerBaseStyle.background;
          existingMarker.style.zIndex = markerBaseStyle.zIndex;
        }
      } else {
        // Create new marker element
        const markerEl = document.createElement('div');
        markerEl.className = `dom-destination-marker ${isSelected ? 'selected' : ''}`;
        markerEl.dataset.destinationId = destinationId;
        markerEl.style.position = 'absolute';
        markerEl.style.left = `${point.x}px`;
        markerEl.style.top = `${point.y}px`;
        markerEl.style.transform = 'translate3d(-50%, -50%, 0)';
        markerEl.style.width = '25px';
        markerEl.style.height = '25px';
        markerEl.style.borderRadius = '50%';
        markerEl.style.backgroundColor = isSelected ? markerSelectedStyle.background : markerBaseStyle.background;
        markerEl.style.border = '2px solid white';
        markerEl.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';
        markerEl.style.cursor = 'pointer';
        markerEl.style.zIndex = isSelected ? markerSelectedStyle.zIndex : markerBaseStyle.zIndex;
        markerEl.style.pointerEvents = 'auto';
        // Use will-change for hardware acceleration
        markerEl.style.willChange = 'transform';
        // Set additional hardware acceleration properties
        markerEl.style.backfaceVisibility = 'hidden';
        markerEl.style.perspective = '1000px';
        markerEl.style.transformStyle = 'preserve-3d';
        // Better transition timing
        markerEl.style.transition = 'transform 0.15s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
        // Add text alignment for checkmark
        markerEl.style.textAlign = 'center'; 
        markerEl.style.lineHeight = '25px'; // Vertically center simple text checkmark

        // Add checkmark if selected
        if (isSelected) {
          markerEl.innerHTML = '<span style="font-size: 12px; color: white; line-height: 25px; font-weight: bold;">✓</span>';
        }

        markerEl.addEventListener('click', (e) => {
          e.stopPropagation();
          console.log('[DirectDestinationMarkers] Marker clicked:', destination);
          if (typeof (window as any).AnimationLogger?.log === 'function') {
            (window as any).AnimationLogger.log('info', 'ui', '[DirectDestinationMarkers] Marker clicked', { destination });
          }
          onDestinationClick(destination);
        });
        // Use 3D transform for hover effects
        markerEl.addEventListener('mouseenter', () => { 
          markerEl.style.transform = 'translate3d(-50%, -50%, 0) scale(1.2)';
        });
        markerEl.addEventListener('mouseleave', () => { 
          markerEl.style.transform = 'translate3d(-50%, -50%, 0)';
        });

        markersContainerRef.current?.appendChild(markerEl);
        markerElementsRef.current.set(destinationId, markerEl);
      }
    } else {
      // Destination is not visible, remove marker if it exists
      if (existingMarker) {
        existingMarker.remove();
        markerElementsRef.current.delete(destinationId);
      }
    }
  }, [map, selectedDestinations, onDestinationClick]);

  useEffect(() => {
    if (!map) return;
    isMountedRef.current = true;

    // --- Ensure tooltip and markers container exist ---
    if (!tooltipRef.current) {
      const tooltip = document.createElement('div');
      tooltip.className = 'destination-tooltip';
      tooltip.style.position = 'absolute';
      tooltip.style.display = 'none';
      tooltip.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
      tooltip.style.color = 'white';
      tooltip.style.padding = '12px';
      tooltip.style.borderRadius = '8px';
      tooltip.style.zIndex = '3000'; // Ensure tooltip is high
      tooltip.style.pointerEvents = 'none';
      tooltip.style.maxWidth = '300px';
       try {
           map.getContainer().appendChild(tooltip);
           tooltipRef.current = tooltip;
       } catch (e) { console.error("Failed to append tooltip", e); }
    }
    if (!markersContainerRef.current) {
      let container = document.getElementById('destination-markers-container');
      if (!container) {
        container = document.createElement('div');
        container.id = 'destination-markers-container';
        container.style.position = 'absolute';
        container.style.top = '0';
        container.style.left = '0';
        container.style.width = '100%';
        container.style.height = '100%';
        container.style.pointerEvents = 'none';
        container.style.zIndex = '1500'; // Container z-index
         try {
            map.getContainer().appendChild(container);
         } catch (e) { console.error("Failed to append marker container", e); }
      }
      markersContainerRef.current = container as HTMLDivElement;
    }
    // --- End container setup ---

    // --- Main Update Function ---
    const updateAllMarkers = async () => {
      if (!isMountedRef.current) return;
      
      if (!map) {
        console.warn('Cannot update markers: Map is not initialized');
        return;
      }
      
      // Use our new helper function to ensure map is fully ready
      try {
        // Wait for map to be ready with a reasonable timeout
        await whenMapReady(map, 3000);
      } catch (error) {
        console.warn(`⚠️ [${Date.now()}] Map readiness check timed out or failed:`, error);
        
        // Only set a timeout if we haven't already
        if (!markersLoadingTimeoutRef.current) {
          markersLoadingTimeoutRef.current = setTimeout(() => {
            markersLoadingTimeoutRef.current = null;
            if (isMountedRef.current && map) {
              console.log(`🔄 [${Date.now()}] Retrying marker update after delay`);
              updateAllMarkers();
            }
          }, 500);
        }
        return;
      }
      
      if (!markersContainerRef.current) {
        console.warn(`⚠️ [${Date.now()}] Markers container not found, attempting to create`);
        try {
          let container = document.getElementById('destination-markers-container');
          if (!container) {
            container = document.createElement('div');
            container.id = 'destination-markers-container';
            container.style.position = 'absolute';
            container.style.top = '0';
            container.style.left = '0';
            container.style.width = '100%';
            container.style.height = '100%';
            container.style.pointerEvents = 'none';
            container.style.zIndex = '1500';
            map.getContainer().appendChild(container);
            markersContainerRef.current = container as HTMLDivElement;
            console.log(`✅ [${Date.now()}] Created new markers container`);
          } else {
            markersContainerRef.current = container as HTMLDivElement;
            console.log(`✅ [${Date.now()}] Found existing markers container`);
          }
        } catch (e) {
          console.error(`❌ [${Date.now()}] Failed to create markers container:`, e);
          return;
        }
      }

      const currentZoom = map.getZoom();
      const currentBounds = map.getBounds();
      const thresholdZoom = 13;
      const currentVisibleIds = new Set<string>(); // Track IDs that should be visible NOW

      // Process destinations from props
      destinations.forEach((destination) => {
        const destId = destination.id;
        
        // Handle different coordinate formats
        let destCoords: [number, number];
        if (Array.isArray(destination.coordinates) && destination.coordinates.length === 2 &&
            typeof destination.coordinates[0] === 'number' && typeof destination.coordinates[1] === 'number') {
          destCoords = destination.coordinates as [number, number];
        } else if (typeof destination.coordinates === 'object' && 'lat' in destination.coordinates && 'lng' in destination.coordinates) {
          destCoords = [destination.coordinates.lng as number, destination.coordinates.lat as number];
        } else if (destination.position && typeof destination.position === 'object' && 'lat' in destination.position && 'lng' in destination.position) {
          destCoords = [destination.position.lng as number, destination.position.lat as number];
        } else {
          console.warn(`⚠️ [${Date.now()}] Invalid coordinates format for destination: ${destination.name}`);
          return;
        }
        
        // Basic check if coordinates are valid
        if (destCoords.length !== 2 || typeof destCoords[0] !== 'number' || typeof destCoords[1] !== 'number' || isNaN(destCoords[0]) || isNaN(destCoords[1])) {
             console.warn(`⚠️ [${Date.now()}] Invalid coordinates values for destination: ${destination.name}`);
             return;
        }

        const isInBounds = currentBounds.contains(destCoords);
        const isBelowZoomThreshold = currentZoom <= thresholdZoom;
        const shouldShow = isInBounds && isBelowZoomThreshold;

        const existingMarker = markerElementsRef.current.get(destId);

        if (shouldShow) {
          currentVisibleIds.add(destId);
          let point;
           try {
            // Ensure map is ready before projecting
            if (!map.isStyleLoaded()) {
              console.warn(`⚠️ [${Date.now()}] Map style not loaded during marker position calculation for ${destination.name}`);
              return; // Skip this marker until style is loaded
            }
            
               point = map.project(destCoords);
            
            // Validate projected point
            if (!point || typeof point.x !== 'number' || typeof point.y !== 'number' || 
                isNaN(point.x) || isNaN(point.y)) {
              console.error(`❌ [${Date.now()}] Invalid projection result for ${destination.name}:`, point);
              return; // Skip this marker
            }
           } catch (e) {
            console.error(`❌ [${Date.now()}] Failed to project coordinates for ${destination.name}:`, e);
               return; // Skip this marker if projection fails
           }

          if (existingMarker) {
            // Update Existing Marker
            existingMarker.style.left = `${point.x}px`;
            existingMarker.style.top = `${point.y}px`;
            existingMarker.style.display = 'flex';
            const isSelected = selectedDestinations.some(d => d.id === destId);
            existingMarker.style.backgroundColor = isSelected ? markerSelectedStyle.background : markerBaseStyle.background;
            existingMarker.style.color = isSelected ? markerSelectedStyle.color : markerBaseStyle.color;
            existingMarker.style.zIndex = isSelected ? markerSelectedStyle.zIndex : markerBaseStyle.zIndex;
            existingMarker.style.border = isSelected ? markerSelectedStyle.border : markerBaseStyle.border;
            // Ensure dataset is up-to-date (though usually persistent)
            existingMarker.dataset.destinationId = destId;
          } else {
            // Create New Marker
            const isSelected = selectedDestinations.some(d => d.id === destId);
            const isMajorCity = MAJOR_CITIES.includes(destination.name);
            const poiStats = getPoiStats(destination.name, destCoords);
            const distanceToNext = getDistanceToNextCity(destId); // Pass ID

            const markerEl = document.createElement('div');
            markerEl.className = `dom-destination-marker ${isSelected ? 'selected' : ''}`;
            markerEl.dataset.destinationId = destId;

            // Apply Styles
            markerEl.style.position = 'absolute';
            markerEl.style.left = `${point.x}px`;
            markerEl.style.top = `${point.y}px`;
            markerEl.style.transform = 'translate(-50%, -100%)'; // Bottom-center anchor
            markerEl.style.display = 'flex';
            markerEl.style.alignItems = 'center';
            markerEl.style.padding = isMajorCity ? '10px 20px' : '6px 12px';
            markerEl.style.borderRadius = '20px';
            markerEl.style.backgroundColor = isSelected ? markerSelectedStyle.background : markerBaseStyle.background;
            markerEl.style.color = isSelected ? markerSelectedStyle.color : markerBaseStyle.color;
            markerEl.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
            markerEl.style.cursor = 'pointer';
            markerEl.style.zIndex = isSelected ? markerSelectedStyle.zIndex : markerBaseStyle.zIndex;
            markerEl.style.pointerEvents = 'auto';
            markerEl.style.transition = 'all 0.2s ease';
            markerEl.style.border = isSelected ? markerSelectedStyle.border : markerBaseStyle.border;
            markerEl.style.minWidth = isMajorCity ? '140px' : '120px';
            markerEl.style.gap = '8px';

            // Apply Inner HTML
            markerEl.innerHTML = `
              <div style="display: flex; flex-direction: column; width: 100%;">
                <div style="display: flex; align-items: center; justify-content: space-between; width: 100%;">
                  <span style="font-weight: ${isMajorCity ? '600' : '500'}; font-size: ${isMajorCity ? '14px' : '12px'};">${destination.name}</span>
                  ${poiStats.total > 0 ? `<span style="background: ${isSelected ? 'rgba(255,255,255,0.2)' : markerBaseStyle.background}; color: ${isSelected ? markerSelectedStyle.color : markerBaseStyle.color}; padding: 2px 8px; border-radius: 12px; font-size: ${isMajorCity ? '12px' : '11px'}; font-weight: 600;">${poiStats.total} activities</span>` : ''}
                </div>
                ${poiStats.total > 0 ? `<div style="display: flex; flex-wrap: wrap; gap: 4px; margin-top: 4px; font-size: 10px; color: ${isSelected ? markerSelectedStyle.color : markerBaseStyle.color};">${Object.entries(poiStats.categories).filter(([_, count]) => count > 0).map(([category, count]) => `<span>${count} ${category.charAt(0).toUpperCase() + category.slice(1)}</span>`).join(' <span style="opacity: 0.5">•</span> ')}</div>` : ''}
              </div>
            `;

            // Add Event Listeners
            markerEl.addEventListener('click', (e) => {
                 e.stopPropagation(); // Prevent map click through marker
                 onDestinationClick(destination);
            });
            markerEl.addEventListener('mouseenter', () => {
              markerEl.style.transform = 'translate(-50%, -100%) scale(1.05)';
              markerEl.style.boxShadow = '0 6px 16px rgba(0,0,0,0.2)';
              if (tooltipRef.current) {
                    const tooltip = tooltipRef.current;
                    const distanceInfo = getDistanceToNextCity(destId);
                    // Regenerate tooltip content
                    tooltip.innerHTML = `
                        <div style="margin-bottom: 8px;">
                          <strong style="font-size: 16px;">${destination.name}</strong>
                          ${destination.description ? `<p style="margin: 4px 0; font-size: 12px; opacity: 0.9;">${destination.description}</p>` : ''}
                        </div>
                        ${poiStats.total > 0 ? `
                          <div style="font-size: 12px; margin-bottom: 6px;">
                            <strong>${poiStats.total} Points of Interest Nearby</strong>
                            <div style="margin-top: 4px; display: grid; grid-template-columns: repeat(2, 1fr); gap: 4px;">
                              ${Object.entries(poiStats.categories).filter(([_, count]) => count > 0).map(([category, count]) => `<div>${count} ${category.charAt(0).toUpperCase() + category.slice(1)}</div>`).join('')}
                            </div>
                          </div>
                          <div style="font-size: 12px; margin-top: 8px;">Estimated exploration: ${calculateExplorationTime(poiStats.total)}</div>
                        ` : ''}
                        ${distanceInfo ? `<div style="font-size: 12px; margin-top: 8px; padding-top: 8px; border-top: 1px solid rgba(255,255,255,0.2);">${distanceInfo}</div>` : ''}
                      `;

                     // Position tooltip (consider edge cases later if needed)
                    const markerRect = markerEl.getBoundingClientRect();
                    const tooltipWidth = 280; // Fixed width
                    tooltip.style.width = `${tooltipWidth}px`;
                    const tooltipHeight = tooltip.offsetHeight; // Get height after setting content and width
                    const mapContainerRect = map.getContainer().getBoundingClientRect();

                    let left = markerRect.left + (markerRect.width / 2) - (tooltipWidth / 2) - mapContainerRect.left;
                    let top = markerRect.top - tooltipHeight - 10 - mapContainerRect.top; // 10px above marker

                    // Adjust if too far left/right/top
                    if (left < 10) left = 10;
                    if (left + tooltipWidth > mapContainerRect.width - 10) left = mapContainerRect.width - tooltipWidth - 10;
                    if (top < 10) top = markerRect.bottom - mapContainerRect.top + 10; // Position below if not enough space above

                    tooltip.style.left = `${left}px`;
                    tooltip.style.top = `${top}px`;
                    tooltip.style.display = 'block';
              }
            });
            markerEl.addEventListener('mouseleave', () => {
              markerEl.style.transform = 'translate(-50%, -100%)';
              markerEl.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
              if (tooltipRef.current) { tooltipRef.current.style.display = 'none'; }
            });

            markersContainerRef.current!.appendChild(markerEl);
            markerElementsRef.current.set(destId, markerEl); // Add to Map
          }
        } else {
          // Should not show - remove if exists
          if (existingMarker) {
            existingMarker.remove();
            markerElementsRef.current.delete(destId);
          }
        }
      });

      // --- Second pass cleanup ---
      markerElementsRef.current.forEach((marker, destId) => {
        // Remove if not in current props OR not marked as visible in this cycle
        const existsInProps = destinations.some(d => d.id === destId);
        if (!existsInProps || !currentVisibleIds.has(destId)) {
          marker.remove();
          markerElementsRef.current.delete(destId);
        }
      });
    };
    // --- End updateAllMarkers ---

    const throttledUpdate = throttle(updateAllMarkers, 100);

    // Initial update
    updateAllMarkers();

    // Add map move/zoom listeners
    map.on('move', throttledUpdate);
    map.on('zoomend', updateAllMarkers);
    map.on('idle', updateAllMarkers); // Keep idle as well

    // Cleanup
    return () => {
      isMountedRef.current = false;
      map.off('move', throttledUpdate);
      map.off('zoomend', updateAllMarkers);
       map.off('idle', updateAllMarkers); // Remove idle listener too
      markerElementsRef.current.forEach(el => { try { el.remove(); } catch(e){} });
      markerElementsRef.current.clear();
      // Tooltip cleanup
      if (tooltipRef.current && tooltipRef.current.parentNode) {
        try { tooltipRef.current.parentNode.removeChild(tooltipRef.current); } catch(e){}
        tooltipRef.current = null;
      }
      // Container cleanup (optional, maybe keep for efficiency)
       const container = document.getElementById('destination-markers-container');
       if (container && container.parentNode) { try { container.parentNode.removeChild(container); } catch(e){} }
       markersContainerRef.current = null; // Clear ref too
    };
  }, [map, destinations, selectedDestinations, onDestinationClick]);

  return null;
};

export default DirectDestinationMarkers;
