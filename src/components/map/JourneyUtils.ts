import mapboxgl from 'mapbox-gl';
import CinematicController from './animation/CinematicController';
import CinematicControllerFactory from './animation/CinematicControllerFactory';
import { type Destination, type PointOfInterest, type POICategory, normalizeDestination } from '@/types/POITypes';
import { type Position, type PositionTuple, type PositionObject, isPositionObject } from '@/types/Position';

// Position utilities
const PositionUtils = {
  toPositionTuple: (pos: Position): [number, number] => {
    if (Array.isArray(pos)) {
      return pos;
    } else {
      return [pos.lng, pos.lat];
    }
  },
  isPositionObject: (pos: any): pos is PositionObject => {
    return pos && typeof pos === 'object' && 'lat' in pos && 'lng' in pos;
  }
};

import { 
    forceVehicleVisibility, 
    isVehicleVisible
} from './animation/VehicleController';
import { startAnimationMonitoring } from './animation/AnimationMonitoring';
import { createContinueJourneyButton } from './utils/AnimationHelpers';
import { JourneyPhase } from './utils/types';
import { AnimationManager } from './animation/AnimationManager';
import { shouldUseReactButton } from './animation/UIComponentManager';

// Utility functions for journey handling

export const updateJourneyPhase = (
  phase: JourneyPhase,
  setInternalJourneyPhase: (phase: JourneyPhase) => void,
  setJourneyPhaseWithLoggingRef: React.MutableRefObject<((phase: JourneyPhase) => void) | null>
) => {
  console.log(`🔄 [${new Date().toISOString()}] Forcing journey phase update: ${phase}`);
  setInternalJourneyPhase(phase);
  if (setJourneyPhaseWithLoggingRef.current) {
    setJourneyPhaseWithLoggingRef.current(phase);
  }
  document.dispatchEvent(new CustomEvent('journey-phase-changed', { 
    detail: { phase } 
  }));
};

export const initializeCinematicController = (
  map: mapboxgl.Map, 
  route: [number, number][], 
  pois: PointOfInterest[], 
  destinations: Destination[]
) => {
  console.log(`🔄 [${new Date().toISOString()}] Initializing cinematic controller`);
  return CinematicControllerFactory.create(map, route, pois, destinations);
};

/**
 * Start a simplified direct route animation as a fallback
 * @param map The Mapbox map instance
 * @param destinations Array of destination coordinates
 * @param onProgress Optional callback for animation progress
 * @param onComplete Optional callback for animation completion
 * @returns True if animation started successfully
 */
export const startFallbackRouteAnimation = (
  map: mapboxgl.Map,
  destinations: [number, number][],
  onProgress?: (progress: number, position: Position, bearing: number) => void,
  onComplete?: () => void
): boolean => {
  console.log(`🚀 [${new Date().toISOString()}] Starting fallback route animation`);
  
  try {
    // Get AnimationManager instance
    const animationManager = AnimationManager.getInstance();
    
    // Use direct route animation points
    const routeCoordinates = [...destinations];
    
    // Start animation with a fixed duration
    const ANIMATION_DURATION = 60000; // 60 seconds
    
    // Create the route object with coordinates
    const route = {
      coordinates: routeCoordinates
    };
    
    // Create animation options
    const animationOptions = {
      duration: ANIMATION_DURATION,
      onProgress: onProgress ? (progress: number, position: Position, bearing: number) => {
        onProgress(progress, position, bearing);
      } : undefined,
      onComplete
    };
    
    // Start animation with proper structure
    animationManager.startAnimation(route, animationOptions);
    
    return animationManager.isCurrentlyAnimating();
  } catch (error) {
    console.error('Failed to start direct route animation:', error);
    return false;
  }
};

// Update startEmergencyAnimation to use our new function
export const startEmergencyAnimation = (
  map: mapboxgl.Map,
  destinations: [number, number][],
  onProgress?: (progress: number, position: Position, bearing: number) => void,
  onComplete?: () => void
): boolean => {
  console.log('🆘 Starting emergency animation fallback');
  
  try {
    if (!map) {
      console.error('Map instance not available for emergency animation');
      return false;
    }
    
    // Use our direct route animation as the emergency fallback
    return startFallbackRouteAnimation(map, destinations, onProgress, onComplete);
  } catch (error) {
    console.error('Failed to start emergency animation:', error);
    return false;
  }
};

// Helper function for setting journey progress (add this if it doesn't exist)
const setCurrentJourneyProgress = (progress: number) => {
  // Dispatch a custom event that can be listened to by UI components
  document.dispatchEvent(new CustomEvent('journey-progress-update', { 
    detail: { progress } 
  }));
  
  // You might want to update this with your actual state management approach
  console.log(`Journey progress updated: ${progress}%`);
};

// Modify startCinematicSequence to use emergency animation as fallback
export const startCinematicSequence = async (
  map: mapboxgl.Map | null,
  destinations: Destination[],
  onComplete?: () => void
): Promise<boolean> => {
  console.log(`🎬 [${new Date().toISOString()}] Starting cinematic sequence with ${destinations.length} destinations`);
  
  if (!map || !destinations.length) {
    console.error('Cannot start cinematic sequence: Missing map or destinations');
    return false;
  }
  
  try {
    // Clean up any existing markers
    const mapContainer = map.getContainer();
    const existingMarkers = mapContainer.querySelectorAll('.vehicle-marker, .emergency-vehicle-marker');
    existingMarkers.forEach((marker: Element) => marker.remove());
    
    // First try to use the standard animation system
    const animationManager = AnimationManager.getInstance();
    
    // Extract route from destinations
    const route = destinations.map(dest => PositionUtils.toPositionTuple(dest.position)) as [number, number][];

    // Create proper MapboxRoute object
    const mapboxRoute = {
      coordinates: route
    };
    
    // Create separate animation options
    const animationOptions = {
      onProgress: (progress: number, position: Position, bearing: number) => {
        // Update UI progress
        const progressPercent = Math.round(progress * 100);
        console.log(`🎬 Animation progress: ${progressPercent}%`);
        
        // Set current journey progress
        setCurrentJourneyProgress(progressPercent);
      },
      onComplete: () => {
        console.log('🎬 Cinematic sequence completed');
        if (onComplete) onComplete();
      },
      duration: 60000 // 60 seconds duration
    };

    // Try standard animation with proper structure
    animationManager.startAnimation(mapboxRoute, animationOptions);
    
    // If standard animation fails, use emergency animation
    if (!animationManager.isCurrentlyAnimating()) {
      console.warn(`⚠️ [${new Date().toISOString()}] Standard animation failed, using emergency animation`);
      
      return startEmergencyAnimation(map, route, 
        (progress, position, bearing) => {
          // Update UI progress
          const progressPercent = Math.round(progress * 100);
          console.log(`🚨 Emergency animation progress: ${progressPercent}%`);
          
          // Set current journey progress
          setCurrentJourneyProgress(progressPercent);
        },
        onComplete
      );
    }
    
    return true;
  } catch (error) {
    console.error('❌ Failed to start cinematic sequence:', error);
    
    // Last resort - try emergency animation in catch block
    console.warn(`🚨 [${new Date().toISOString()}] Attempting emergency animation after error`);
    
    // Extract route coordinates from destinations
    const route = destinations.map(dest => PositionUtils.toPositionTuple(dest.position)) as [number, number][];
    
    return startEmergencyAnimation(map, route, 
      (progress, position, bearing) => {
        // Update UI progress
        const progressPercent = Math.round(progress * 100);
        console.log(`🚨 Emergency animation progress: ${progressPercent}%`);
        
        // Set current journey progress
        setCurrentJourneyProgress(progressPercent);
      }, 
      onComplete
    );
  }
};

/**
 * Ensures the Begin Adventure button is created when needed
 * 
 * @deprecated This function is deprecated. Button creation is now handled by the React component
 * in TravelAnimator.tsx, which is the single source of truth for journey buttons.
 * 
 * This function now acts as a compatibility layer that prevents duplicate buttons
 * by deferring to the React implementation.
 */
export const ensureBeginAdventureButton = (
  map: mapboxgl.Map | null, 
  destinations: any[], 
  journeyPhase: JourneyPhase | null,
  internalJourneyPhase: JourneyPhase | null,
  setIsAnimating: (isAnimating: boolean) => void,
  handleBeginAdventure: (phase: string) => void
): (() => void) | void => {
  // Log deprecation warning
  console.warn('⚠️ ensureBeginAdventureButton is deprecated. Button rendering is now handled by TravelAnimator.tsx React component.');
  
  // The React-based button in TravelAnimator is now the single source of truth
  // This function now exists for compatibility but won't create DOM buttons
  if (destinations?.length >= 2) {
    console.log(`Button request received for ${destinations.length} destinations, deferring to React component in TravelAnimator.tsx`);
  }
  
  // Return empty function for compatibility
  return () => {};
};

// Keep the old function for backward compatibility
export const ensureContinueJourneyButton = ensureBeginAdventureButton; 

/**
 * Creates a begin journey button for the map
 * @param map The Mapbox map instance
 * @param destinations Array of destinations
 * @param onStart Callback when journey starts
 * @param onPauseResume Callback for pause/resume
 * @param showButton Whether to show the button
 * @param canBeginJourney Whether journey can begin
 * @returns Cleanup function
 */
export const createBeginJourneyButton = (
  map: mapboxgl.Map,
  destinations: Destination[],
  onStart: () => void,
  onPauseResume: () => void,
  showButton: boolean = true,
  canBeginJourney: boolean = true
): (() => void) => {
  console.log(`🎯 [${new Date().toISOString()}] Creating begin journey button`);
  
  // Create button container
  const buttonContainer = document.createElement('div');
  buttonContainer.className = 'begin-journey-button-container';
  buttonContainer.style.position = 'absolute';
  buttonContainer.style.bottom = '20px';
  buttonContainer.style.left = '50%';
  buttonContainer.style.transform = 'translateX(-50%)';
  buttonContainer.style.zIndex = '1000';
  buttonContainer.style.display = showButton ? 'block' : 'none';
  
  // Create button
  const button = document.createElement('button');
  button.className = 'begin-journey-button';
  button.textContent = 'Begin Journey';
  button.style.padding = '10px 20px';
  button.style.backgroundColor = canBeginJourney ? '#4CAF50' : '#cccccc';
  button.style.color = 'white';
  button.style.border = 'none';
  button.style.borderRadius = '5px';
  button.style.cursor = canBeginJourney ? 'pointer' : 'not-allowed';
  button.style.fontSize = '16px';
  button.style.fontWeight = 'bold';
  button.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';
  button.style.transition = 'all 0.3s ease';
  
  // Add hover effect
  button.onmouseover = () => {
    if (canBeginJourney) {
      button.style.backgroundColor = '#45a049';
      button.style.transform = 'scale(1.05)';
    }
  };
  
  button.onmouseout = () => {
    if (canBeginJourney) {
      button.style.backgroundColor = '#4CAF50';
      button.style.transform = 'scale(1)';
    }
  };
  
  // Add click handler
  button.onclick = () => {
    if (canBeginJourney) {
      onStart();
    }
  };
  
  // Add button to container
  buttonContainer.appendChild(button);
  
  // Add container to map
  map.getContainer().appendChild(buttonContainer);
  
  // Return cleanup function
  return () => {
    buttonContainer.remove();
  };
}; 