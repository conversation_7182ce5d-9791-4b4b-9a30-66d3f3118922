import React, { useState, useEffect } from 'react';
import { motion, Variants } from 'framer-motion';
import {
  MapPin,
  Search,
  Filter,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  X,
  Plus,
  Check,
  Clock,
  Calendar,
  DollarSign,
  Sun,
  Cloud,
  CloudRain,
  Wind,
  Star,
  Play
} from 'lucide-react';
import { PointOfInterest } from '../../types';
import { ComponentInteractionManager } from './animation/ComponentInteractionManager';
import { Button } from '@/components/ui/button';
import '../../styles/modern-poi-panels.css';
import JourneyParametersForm from './JourneyParametersForm';
import { TravelStyle, TravelInterest } from '@/types/ItineraryParameters';

// Define interfaces for the expected event structures to avoid 'any'
interface POIDiscoveryEvent {
  data?: { poi?: any }; // Replace 'any' with a more specific POI type if known
  payload?: { poi?: any };
  poi?: any;
}

interface AnimationProgressEvent {
  data?: { progress?: number };
  payload?: { progress?: number };
  progress?: number;
}

// Panel animation variants
const panelVariants = {
  rightOpen: {
    x: 0,
    opacity: 1,
    transition: {
      type: 'spring',
      stiffness: 300,
      damping: 30,
    },
  },
  rightClosed: {
    x: 350,
    opacity: 0,
    transition: {
      type: 'spring',
      stiffness: 300,
      damping: 30,
    },
  },
};

interface RightSidebarProps {
  pointsOfInterest: PointOfInterest[];
  selectedPOIs: PointOfInterest[];
  onAddPOI: (poi: PointOfInterest) => void;
  onRemovePOI: (poi: PointOfInterest) => void;
  onReorderPOIs?: (reorderedPOIs: PointOfInterest[]) => void;
  onBeginJourney?: () => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;

  // Updated Journey Parameter Props
  numberOfTravelDays: number;
  onNumberOfTravelDaysChange: (days: number) => void;
  travelStyle: TravelStyle;
  onTravelStyleChange: (style: TravelStyle) => void;
  availableTravelStyles: { value: TravelStyle; label: string }[];
  selectedTravelInterests: TravelInterest[];
  onTravelInterestChange: (interest: TravelInterest, selected: boolean) => void;
  availableTravelInterests: { value: TravelInterest; label: string }[];

  // Props for mobile overlay behavior
  isMobileView?: boolean;
  onCloseMobile?: () => void;
  adjustContentForAnimator?: boolean;
}

const RightSidebar: React.FC<RightSidebarProps> = ({
  pointsOfInterest,
  selectedPOIs,
  onAddPOI,
  onRemovePOI,
  onReorderPOIs,
  onBeginJourney,
  isCollapsed = false,
  onToggleCollapse,
  // New Journey Parameters
  numberOfTravelDays,
  onNumberOfTravelDaysChange,
  travelStyle,
  onTravelStyleChange,
  availableTravelStyles,
  selectedTravelInterests,
  onTravelInterestChange,
  availableTravelInterests,
  // Mobile specific props
  isMobileView = false,
  onCloseMobile,
  adjustContentForAnimator = false,
}) => {
  // Connect to ComponentInteractionManager for animation events
  useEffect(() => {
    console.log('Setting up event listeners in RightSidebar');
    const interactionManager = ComponentInteractionManager.getInstance();
    
    const handlePOIDiscovery = (event: any) => { // Reverted to any for now
      console.log('RightSidebar received POI discovery event:', event);
      // Extract POI data regardless of the event structure
      const poiData = event.data?.poi || event.payload?.poi || (event.poi ? event.poi : null);
      
      if (poiData && poiData.id) {
        const matchingPOI = pointsOfInterest.find(poi => poi.id === poiData.id);
        if (matchingPOI) {
          console.log('Matching POI found in panel:', matchingPOI.name);
          highlightPOI(matchingPOI.id);
        }
      }
    };
    
    const handleAnimationProgress = (event: any) => { // Reverted to any for now
      const progress = event.data?.progress || event.payload?.progress || event.progress;
      if (typeof progress === 'number') {
        console.log('Animation progress:', progress);
        // Update any UI elements based on progress
      }
    };
    
    // Register event listeners with simplified approach
    const unsubscribePOIDiscovery = interactionManager.addEventListener(
      'poiDiscovered', 
      handlePOIDiscovery
    );
    
    const unsubscribeAnimationProgress = interactionManager.addEventListener(
      'animationProgress', 
      handleAnimationProgress
    );
    
    return () => {
      // Cleanup event listeners
      unsubscribePOIDiscovery();
      unsubscribeAnimationProgress();
    };
  }, [pointsOfInterest]);
  
  // Function to highlight a POI in the panel
  const highlightPOI = (poiId: string) => {
    // Implementation would depend on the UI structure
    // Could scroll to the POI, add a highlight class, etc.
    console.log('Highlighting POI:', poiId);
  };
  
  // Drag and drop state
  const [draggedPOI, setDraggedPOI] = useState<PointOfInterest | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);
  
  // Group selected POIs by day (for journey panel)
  const poiDays = selectedPOIs.reduce((acc, poi, index) => {
    // For demo purposes, assign POIs to days (1 POI per day)
    const day = Math.floor(index / 3) + 1;
    if (!acc[day]) acc[day] = [];
    acc[day].push(poi);
    return acc;
  }, {} as Record<number, PointOfInterest[]>);
  
  // Handle drag and drop
  const handleDragStart = (poi: PointOfInterest) => {
    setDraggedPOI(poi);
  };
  
  const handleDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault();
    setDragOverIndex(index);
  };
  
  const handleDrop = (e: React.DragEvent, targetIndex: number) => {
    e.preventDefault();
    if (!draggedPOI) return;
    const sourceIndex = selectedPOIs.findIndex((poi: PointOfInterest) => poi.id === draggedPOI.id);
    if (sourceIndex === -1) return;
    const reordered = [...selectedPOIs];
    const [removed] = reordered.splice(sourceIndex, 1);
    reordered.splice(targetIndex, 0, removed);
    if (onReorderPOIs) {
      onReorderPOIs(reordered);
    }
    setDraggedPOI(null);
    setDragOverIndex(null);
  };
  
  const handleDragEnd = () => {
    setDraggedPOI(null);
    setDragOverIndex(null);
  };
  
  // Calculate journey stats
  const journeyStats = {
    totalPOIs: selectedPOIs.length,
    totalDays: Object.keys(poiDays).length,
    estimatedCost: selectedPOIs.reduce((sum, poi) => sum + (poi.priceLevel || 0), 0),
  };
  
  // Weather icons based on condition
  const getWeatherIcon = (condition: string) => {
    switch (condition) {
      case 'sunny':
        return <Sun className="weather-icon sunny" />;
      case 'cloudy':
        return <Cloud className="weather-icon cloudy" />;
      case 'rainy':
        return <CloudRain className="weather-icon rainy" />;
      case 'windy':
        return <Wind className="weather-icon windy" />;
      default:
        return <Sun className="weather-icon sunny" />;
    }
  };

  // Extract unique cities from selected POIs - Memoize this
  const selectedCities = React.useMemo(() => [...new Set(
    selectedPOIs
      .map(poi => poi.location || '')
      .filter(Boolean)
      .map(cityName => cityName.trim().toLowerCase())
  )], [selectedPOIs]);

  // Debug the detected cities
  useEffect(() => {
    if (selectedCities.length > 0) {
      console.log('Detected cities from selected POIs:', selectedCities);
    }
  }, [selectedCities]);

  // Check if the user can begin the journey (needs at least 2 different cities)
  const canBeginJourney = selectedCities.length >= 2;

  // Handle begin journey button click
  const handleBeginJourney = () => {
    console.log('Begin Journey button clicked with cities:', selectedCities);
    if (onBeginJourney && canBeginJourney) {
      onBeginJourney();
    }
  };

  // Helper function to get POI color based on type
  const getPoiColor = (poiType?: string) => {
    switch (poiType) {
      case 'monument': return '#E27D60';
      case 'landmark': return '#3B82F6';
      case 'restaurant': return '#F7C59F';
      case 'activity': return '#85DCB9';
      case 'accommodation': return '#9D80BC';
      default: return '#FF5722';
    }
  };

  // Panel animation variants for width
  const rightPanelWrapperVariants: Variants = {
    open: { 
      width: '384px', // w-96
      opacity: 1, 
      transition: { duration: 0.3, ease: 'easeInOut' } 
    },
    collapsed: { 
      width: '50px', 
      opacity: 1, 
      transition: { duration: 0.3, ease: 'easeInOut' } 
    }
  };
  
  const animateState = isCollapsed && !isMobileView ? 'collapsed' : 'open'; // Mobile view should not be collapsible here

  // --- Journey Timeline Component ---
  const JourneyTimeline: React.FC = () => {
    if (selectedCities.length < 2) return null;
    return (
      <div className="w-full overflow-x-auto py-4">
        <div className="flex items-center justify-start space-x-0.5 min-w-[320px]">
          {selectedCities.map((city, idx) => (
            <React.Fragment key={city}>
              <div className="flex flex-col items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold text-white shadow-lg border-2
                  ${idx === 0 ? 'bg-blue-600 border-blue-700' : idx === selectedCities.length - 1 ? 'bg-green-600 border-green-700' : 'bg-gray-400 border-gray-500'}`}
                >
                  {idx + 1}
                </div>
                <span className="mt-1 text-xs text-gray-700 dark:text-gray-200 whitespace-nowrap max-w-[72px] truncate text-center">
                  {city.charAt(0).toUpperCase() + city.slice(1)}
                </span>
              </div>
              {idx < selectedCities.length - 1 && (
                <div className="flex-1 h-1 bg-gradient-to-r from-blue-400 to-green-400 mx-1 min-w-[24px]" style={{ minWidth: 24 }} />
              )}
            </React.Fragment>
          ))}
        </div>
      </div>
    );
  };

  const showJourneyPlanner = true; // Control when to show this, e.g., based on JourneyPhase

  return (
    <>
      {/* Right POI Journey Panel */}
      <motion.div 
        className={`modern-panel-base modern-journey-panel bg-gray-50 dark:bg-gray-900 shadow-xl flex flex-col`}
        variants={rightPanelWrapperVariants}
        initial={isCollapsed ? 'collapsed' : 'open'}
        animate={animateState}
        style={{
          height: '100%', // Ensure it takes full height of its container
          // overflowY: 'auto' // Let the inner div handle scrolling if needed
          // When isMobileView is true, this motion.div is inside a container that has overflow-y-auto.
          // So, this component itself doesn't need overflow-y-auto directly unless its content exceeds its own height.
          // For desktop, this allows the panel to be fixed height and its content scrollable via the inner div.
          // For mobile, the outer container in HomePage handles the primary scroll.
        }}
      >
        {/* Mobile View Header with Close Button */}
        {isMobileView && onCloseMobile && (
          <div className="flex justify-between items-center p-3 border-b border-gray-200 dark:border-gray-700 sticky top-0 bg-gray-50 dark:bg-gray-900 z-10">
            <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Itinerary</h2>
            <Button variant="ghost" size="icon" onClick={onCloseMobile} className="text-gray-700 dark:text-gray-200">
              <X size={20} />
            </Button>
          </div>
        )}

        {/* Collapse/Expand Button - Not shown in mobile overlay mode */}
        {!isMobileView && onToggleCollapse && (
          <button 
            onClick={onToggleCollapse} 
            className="absolute top-1/2 -left-3 transform -translate-y-1/2 bg-gray-200 dark:bg-gray-700 p-1 rounded-full shadow-md z-10 hover:bg-gray-300 dark:hover:bg-gray-600"
            aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
          >
            {isCollapsed ? <ChevronLeft size={18} /> : <ChevronRight size={18} />}
          </button>
        )}

        {/* --- Journey Timeline --- */}
        <JourneyTimeline />

        {/* Conditional rendering for main content based on collapsed state (only for non-mobile view) */}
        {(!isCollapsed || isMobileView) && (
          <div 
            className={`p-4 space-y-6 flex-grow ${isMobileView && adjustContentForAnimator ? 'pt-20' : ''} ${isMobileView ? 'overflow-y-auto' : 'overflow-y-auto'}`}
            style={{ height: isMobileView ? 'calc(100% - 60px)': 'auto'}} // 60px for the mobile header height if present
          >
            {/* Journey Parameters Form */}
            {showJourneyPlanner && (
              <JourneyParametersForm
                numberOfTravelDays={numberOfTravelDays}
                onNumberOfTravelDaysChange={onNumberOfTravelDaysChange}
                travelStyle={travelStyle}
                onTravelStyleChange={onTravelStyleChange}
                availableTravelStyles={availableTravelStyles}
                selectedTravelInterests={selectedTravelInterests}
                onTravelInterestChange={onTravelInterestChange}
                availableTravelInterests={availableTravelInterests}
              />
            )}

            {/* Existing Content: Journey Overview */}
            {selectedPOIs.length > 0 ? (
              <div className="space-y-4">
                <div className="grid grid-cols-3 gap-2 text-center text-xs mb-3">
                  <div>
                    <MapPin size={14} className="mx-auto mb-1 text-blue-500" />
                    {journeyStats.totalPOIs} Stops
                            </div>
                  <div>
                    <Calendar size={14} className="mx-auto mb-1 text-green-500" />
                    {journeyStats.totalDays} Days
                          </div>
                  <div>
                    <DollarSign size={14} className="mx-auto mb-1 text-yellow-500" />
                    Est. ${journeyStats.estimatedCost}
                            </div>
                          </div>
                          
                {Object.entries(poiDays).map(([day, pois]) => (
                  <div key={day} className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow">
                    <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-200 mb-2">Day {day}</h4>
                    {pois.map(poi => (
                      <div key={poi.id} className="py-1.5 border-b border-gray-100 dark:border-gray-700 last:border-b-0 flex items-center justify-between">
                        <span className="text-xs text-gray-600 dark:text-gray-300">{poi.name}</span>
                        <Button variant="ghost" size="icon" onClick={() => onRemovePOI(poi)} className="text-red-400 hover:text-red-600 dark:hover:text-red-300 p-1 h-auto w-auto">
                          <X size={12} />
                        </Button>
                      </div>
                    ))}
                </div>
                ))}
                {selectedPOIs.length === 0 && (
                  <p className="text-xs text-gray-400 dark:text-gray-500 text-center py-4">Add points of interest to build your journey.</p>
                )}
          
          {/* Begin Journey Button */}
                {canBeginJourney && onBeginJourney && (
          <Button 
            onClick={handleBeginJourney}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white flex items-center justify-center space-x-2"
                  >
                    <Play size={18} />
                    <span>Begin Journey</span>
                  </Button>
                )}
            {!canBeginJourney && (
                  <p className="text-sm text-gray-500 dark:text-gray-400 text-center">
                    Select POIs from at least two different cities to begin a journey.
                  </p>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <MapPin className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No Points of Interest Selected</h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Select points of interest from the left panel or map to build your journey.
                </p>
              </div>
            )}
        </div>
        )}
      </motion.div>
    </>
  );
};

export default RightSidebar; 