/**
 * JourneyStateRestorer.tsx
 * 
 * Component that handles the restoration of journey state.
 * Shows a resume button when a previous journey session exists.
 */

import React, { useEffect, useState } from 'react';
import { 
  canResumeJourney, 
  recoverJourneyState, 
  JourneyStateSnapshot 
} from './utils/JourneyStateManager';

interface JourneyStateRestorerProps {
  onResume: (state: JourneyStateSnapshot) => void;
  onDecline: () => void;
}

export const JourneyStateRestorer: React.FC<JourneyStateRestorerProps> = ({
  onResume,
  onDecline,
}) => {
  const [canResume, setCanResume] = useState<boolean>(false);
  const [journeyState, setJourneyState] = useState<JourneyStateSnapshot | null>(null);

  useEffect(() => {
    // Check if there's a resumable journey
    const canResumeState = canResumeJourney();
    setCanResume(canResumeState);
    
    if (canResumeState) {
      const state = recoverJourneyState();
      setJourneyState(state);
    }
  }, []);

  if (!canResume || !journeyState) {
    return null;
  }

  // Calculate time since journey was saved
  const timeSince = Date.now() - journeyState.timestamp;
  const minutesAgo = Math.floor(timeSince / (1000 * 60));
  const hoursAgo = Math.floor(minutesAgo / 60);
  
  const timeDisplay = hoursAgo > 0 
    ? `${hoursAgo} hour${hoursAgo === 1 ? '' : 's'} ago`
    : `${minutesAgo} minute${minutesAgo === 1 ? '' : 's'} ago`;

  return (
    <div
      className="journey-restore-container"
      style={{
        position: 'fixed',
        bottom: '40px',
        left: '50%',
        transform: 'translateX(-50%)',
        backgroundColor: 'white',
        padding: '16px',
        borderRadius: '8px',
        boxShadow: '0 4px 14px rgba(0,0,0,0.25)',
        zIndex: 1000,
        maxWidth: '90%',
        width: '400px',
      }}
    >
      <div
        style={{
          marginBottom: '12px',
          fontWeight: 'bold',
          fontSize: '16px',
          color: '#333',
        }}
      >
        Resume Your Journey?
      </div>
      
      <div
        style={{
          marginBottom: '16px',
          fontSize: '14px',
          color: '#666',
        }}
      >
        You have an unfinished journey from {timeDisplay}. 
        Would you like to continue where you left off?
      </div>
      
      <div
        style={{
          display: 'flex',
          gap: '12px',
        }}
      >
        <button
          onClick={() => onResume(journeyState)}
          style={{
            backgroundColor: '#10b981',
            color: 'white',
            border: 'none',
            padding: '8px 16px',
            borderRadius: '4px',
            cursor: 'pointer',
            flexGrow: 1,
            fontWeight: 'bold',
          }}
        >
          Resume Journey
        </button>
        
        <button
          onClick={onDecline}
          style={{
            backgroundColor: '#f3f4f6',
            color: '#4b5563',
            border: 'none',
            padding: '8px 16px',
            borderRadius: '4px',
            cursor: 'pointer',
          }}
        >
          Start New
        </button>
      </div>
    </div>
  );
};

export default JourneyStateRestorer; 