import React, { useEffect, useRef } from 'react';
import { Destination } from '@/data/destinations';
import mapboxgl from 'mapbox-gl';
import { logger } from '@/utils/debugLogger';

// Global registry for markers
const markerRegistry = new Map<string, mapboxgl.Marker>();

// Simple cleanup function
export const cleanupAllDestinationMarkers = () => {
  // Remove all markers from the map
  markerRegistry.forEach((marker) => {
    marker.remove();
  });
  
  // Clear the registry
  markerRegistry.clear();
  
  console.log("Cleaned up all destination markers");
};

// Optimized function following Mapbox best practices
// Avoids DOM manipulation and lets Mapbox handle positioning
export const forceFixAllMarkerPositions = () => {
  const count = markerRegistry.size;
  
  if (count === 0) {
    return 0;
  }
  
  console.log(`Refreshing ${count} markers (Mapbox native approach)`);
  
  try {
    // Let Mapbox handle marker positioning naturally by just calling
    // setLngLat with current coordinates - this triggers Mapbox's internal repositioning
    markerRegistry.forEach((marker) => {
      const currentPosition = marker.getLngLat();
      marker.setLngLat(currentPosition);
    });
  } catch (error) {
    console.error("Error refreshing markers:", error);
  }
  
  return count;
};

interface DestinationMarkerProps {
  destination: Destination;
  isSelected: boolean;
  order?: number;
  map: mapboxgl.Map;
  onClick: (destination: Destination) => void;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
}

const DestinationMarker: React.FC<DestinationMarkerProps> = ({
  destination,
  isSelected,
  order,
  map,
  onClick,
  onMouseEnter,
  onMouseLeave
}) => {
  const markerRef = useRef<mapboxgl.Marker | null>(null);
  
  useEffect(() => {
    if (!map) {
      console.log(`Map not ready for marker: ${destination.name}`);
      return;
    }
    
    // Generate a consistent ID for this marker
    const markerId = `destination-${destination.id}`;
    
    // Try to find existing marker first
    let marker = markerRegistry.get(markerId);
    
    // Create or update the marker
    if (!marker) {
      try {
      // Create a new marker element
        const el = document.createElement('div');
        el.className = 'destination-marker';
        el.id = markerId;
        
        if (isSelected) {
          el.classList.add('selected');
        }
        
        // Add order number if provided
        if (order !== undefined) {
          const orderText = document.createElement('span');
          orderText.textContent = String(order + 1);
          el.appendChild(orderText);
        }
        
        // Parse coordinates
        const coordinates: [number, number] = [
          Number(destination.coordinates[0]),
          Number(destination.coordinates[1])
        ];
        
        // Create marker with Mapbox's native approach
        // The key is using minimal options and letting Mapbox position it
        marker = new mapboxgl.Marker({
          element: el,
          anchor: 'center',
          offset: [0, 0]
        })
        .setLngLat(coordinates)
        .addTo(map);
        
        // Store in registry
        markerRegistry.set(markerId, marker);
        markerRef.current = marker;
        
        console.log(`Created marker for ${destination.name}`);
      } catch (error) {
        console.error(`Error creating marker for ${destination.name}:`, error);
      }
    } else {
      // Just update existing marker
      const el = marker.getElement();
      
      // Update selection state
      if (isSelected) {
        el.classList.add('selected');
      } else {
        el.classList.remove('selected');
      }
      
      // Update order number
      if (order !== undefined) {
        let orderEl = el.querySelector('span');
        if (!orderEl) {
          orderEl = document.createElement('span');
          el.appendChild(orderEl);
        }
        orderEl.textContent = String(order + 1);
      } else {
        // Remove order number if not needed
        const orderEl = el.querySelector('span');
        if (orderEl) {
          el.removeChild(orderEl);
        }
      }
    }
    
    // Skip event setup if marker wasn't created
    if (!marker) return;
    
    // Set up events
    const element = marker.getElement();
    
    // Define event handlers
    const handleClick = (e: Event) => {
      e.stopPropagation();
      onClick(destination);
    };
    
    const handleMouseEnter = () => {
      onMouseEnter();
    };
    
    const handleMouseLeave = () => {
      onMouseLeave();
    };
    
    // Remove existing listeners first
    element.removeEventListener('click', handleClick as EventListener);
    element.removeEventListener('mouseenter', handleMouseEnter);
    element.removeEventListener('mouseleave', handleMouseLeave);
    
    // Add new listeners
    element.addEventListener('click', handleClick as EventListener);
    element.addEventListener('mouseenter', handleMouseEnter);
    element.addEventListener('mouseleave', handleMouseLeave);
    
    // Cleanup function
    return () => {
      element.removeEventListener('click', handleClick as EventListener);
      element.removeEventListener('mouseenter', handleMouseEnter);
      element.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [destination, isSelected, order, map, onClick, onMouseEnter, onMouseLeave]);
  
  // Return null as React doesn't need to render anything
  // The Mapbox marker is created and managed outside React
  return null;
};

export default DestinationMarker;