import React from 'react';
import { createRoot } from 'react-dom/client';
import { PointOfInterest, Destination } from '@/types/POITypes';

/**
 * Create a DOM element for the POI popup
 */
export function createPoiPopupElement(poi: PointOfInterest): HTMLElement {
  // Create container element
  const container = document.createElement('div');
  container.className = 'poi-popup-content';
  
  // Create React root and render content
  const root = createRoot(container);
  root.render(<PoiPopupContent poi={poi} />);
  
  return container;
}

/**
 * Create a DOM element for the destination popup
 * @param destination The destination to display
 * @param order Optional order number for the destination in the itinerary
 */
export function createDestinationPopupElement(destination: Destination, order?: number): HTMLElement {
  // Create container element
  const container = document.createElement('div');
  container.className = 'destination-popup-content';
  
  // Create React root and render content
  const root = createRoot(container);
  root.render(<DestinationPopupContent destination={destination} order={order} />);
  
  return container;
}

/**
 * React component for POI popup content
 */
function PoiPopupContent({ poi }: { poi: PointOfInterest }) {
  const imageUrl = (poi.images && poi.images.length > 0 ? poi.images[0] : null) || (poi as any).imageUrl || '';
  
  return (
    <div className="max-w-[200px]">
      <div className="relative h-20 w-full mb-2 rounded-md overflow-hidden">
        <img 
          src={imageUrl} 
          alt={poi.name} 
          className="w-full h-full object-cover"
          onError={(e) => {
            // Set fallback image when loading fails
            e.currentTarget.src = 'https://via.placeholder.com/200x80?text=No+Image';
            e.currentTarget.classList.add('fallback-image');
            
            // Log error for debugging
            console.warn(`Image failed to load for POI: ${poi.name}`, {
              originalSrc: imageUrl,
              fallbackApplied: true
            });
            
            // Prevent infinite error loop
            e.currentTarget.onerror = null;
          }}
        />
      </div>
      <h3 className="font-medium text-sm text-gray-900">{poi.name}</h3>
      <p className="text-xs text-gray-500 mt-1 line-clamp-2">{poi.description}</p>
      
      <div className="flex items-center mt-2 text-xs gap-x-2">
        {(poi as any).duration && (
          <span className="bg-gray-100 px-2 py-0.5 rounded-full">
            {(poi as any).duration} {(poi as any).duration === 1 ? 'hour' : 'hours'}
          </span>
        )}
        
        {(poi as any).cost && (
          <span className="bg-gray-100 px-2 py-0.5 rounded-full">
            ${(poi as any).cost}
          </span>
        )}
      </div>
    </div>
  );
}

/**
 * React component for destination popup content
 */
function DestinationPopupContent({ 
  destination,
  order
}: { 
  destination: Destination;
  order?: number;
}) {
  const displayImage = (destination.images && destination.images.length > 0 ? destination.images[0] : null) || '';

  return (
    <div className="max-w-[200px]">
      <div className="relative h-20 w-full mb-2 rounded-md overflow-hidden">
        <img 
          src={displayImage} 
          alt={destination.name} 
          className="w-full h-full object-cover"
          onError={(e) => {
            // Set fallback image when loading fails
            e.currentTarget.src = 'https://via.placeholder.com/200x80?text=No+Image';
            e.currentTarget.classList.add('fallback-image');
            
            // Prevent infinite error loop
            e.currentTarget.onerror = null;
          }}
        />
        {order !== undefined && (
          <div className="absolute top-2 right-2 bg-morocco-terracotta text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">
            {order + 1}
          </div>
        )}
      </div>
      <h3 className="font-medium text-sm text-gray-900">{destination.name}</h3>
      <p className="text-xs text-gray-500 mt-1 line-clamp-2">{destination.description}</p>
      
      <div className="flex items-center mt-2 text-xs gap-x-2">
        <span className="bg-gray-100 px-2 py-0.5 rounded-full capitalize">
          Destination
        </span>
      </div>
    </div>
  );
} 