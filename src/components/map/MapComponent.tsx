import React, { useEffect, useRef, useState } from 'react';
import mapboxgl, { Map } from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { Destination, PointOfInterest } from '@/types/POITypes.updated';
import LeftPOIPanel from './LeftPOIPanel';
import { POIDiscoveryManager } from './animation/POIDiscoveryManager';
import CinematicController from './animation/CinematicController';
import '@/styles/core/markers-unified.css';
import '@/styles/enhanced-poi-panels.css';
import '@/styles/modern-poi-panels.css';

// Set Mapbox access token
mapboxgl.accessToken = 'pk.eyJ1Ijoid3dtcyIsImEiOiJjbHdrYWQ0eXAxNGM1MmptbTd4YXg2NGxqIn0.tAM-9pPFtZHoVAzuDuLkUg';

export interface ExploreMapProps {
  destinations: Destination[];
  selectedDestinations: Destination[];
  routePOIs: PointOfInterest[];
  selectedPOIs: PointOfInterest[];
  onDestinationSelect: (destination: Destination) => void;
  onPOISelect: (poi: PointOfInterest) => void;
  onShowPOIOverlay: (destination: Destination) => void;
  // 🎬 ENHANCED: Add region context props for dynamic cinematics
  mapCenter?: [number, number];
  mapBounds?: [[number, number], [number, number]];
}

export interface MapFeatureOptions {
  weatherIndicators: boolean;
  routeLabels: boolean;
  movingVehicle: boolean;
  terrainView: boolean;
  poiSlideshow: boolean;
}

const MapComponent = React.forwardRef<mapboxgl.Map | null, ExploreMapProps>(({
  destinations,
  selectedDestinations,
  routePOIs,
  selectedPOIs,
  onDestinationSelect,
  onPOISelect,
  onShowPOIOverlay,
  mapCenter = [-8, 31], // Default to Morocco center
  mapBounds
}, ref) => {
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<mapboxgl.Map | null>(null);

  useEffect(() => {
    console.log('[MapComponent] routePOIs updated:', routePOIs);
  }, [routePOIs]);
  // Keep the options state but remove UI for toggling them
  const [mapOptions] = useState<MapFeatureOptions>({
    weatherIndicators: false,
    routeLabels: true,
    movingVehicle: true,
    terrainView: false,
    poiSlideshow: true
  });

  // Initialize map
  useEffect(() => {
    if (!mapContainerRef.current) return;
    console.log('[MapComponent] Initial routePOIs:', routePOIs);
    
    const newMap = new mapboxgl.Map({
      container: mapContainerRef.current,
      style: 'mapbox://styles/mapbox/light-v11',
      center: mapCenter, // 🎬 ENHANCED: Use dynamic center
      zoom: 5.5,
      projection: 'mercator',
      pitch: 0, // Start with a flat view
      bearing: 0
    });
    
    newMap.on('load', () => {
      console.log('Map loaded');
      
      // Expose mapboxgl and map to window for debugging
      (window as any).mapboxgl = mapboxgl;
      (window as any).map = newMap;
      
      // Add terrain and sky layers
      newMap.addSource('mapbox-dem', {
        'type': 'raster-dem',
        'url': 'mapbox://mapbox.mapbox-terrain-dem-v1',
        'tileSize': 512,
        'maxzoom': 14
      });
      
      newMap.setTerrain({ 'source': 'mapbox-dem', 'exaggeration': 1.5 });
      
      // Add sky layer
      newMap.addLayer({
        'id': 'sky',
        'type': 'sky',
        'paint': {
          'sky-type': 'atmosphere',
          'sky-atmosphere-sun': [0.0, 0.0],
          'sky-atmosphere-sun-intensity': 15
        }
      });
      
      // Add 3D buildings
      newMap.addLayer({
        'id': '3d-buildings',
        'source': 'composite',
        'source-layer': 'building',
        'filter': ['==', 'extrude', 'true'],
        'type': 'fill-extrusion',
        'minzoom': 10,
        'paint': {
          'fill-extrusion-color': '#aaa',
          'fill-extrusion-height': [
            'interpolate', ['linear'], ['zoom'],
            15, 0,
            15.05, ['get', 'height']
          ],
          'fill-extrusion-base': [
            'interpolate', ['linear'], ['zoom'],
            15, 0,
            15.05, ['get', 'min_height']
          ],
          'fill-extrusion-opacity': 0.6
        }
      });
      
      setMap(newMap);

      // 🎬 ENHANCED: Set up region context for dynamic cinematics
      const cinematicController = CinematicController.getInstance();
      if (cinematicController && mapCenter && mapBounds) {
        cinematicController.setRegionContext(mapCenter, mapBounds);
        console.log('[MapComponent] 🎬 Region context set for cinematics:', { center: mapCenter, bounds: mapBounds });
      }

      // Set the ref if provided
      if (ref) {
        if (typeof ref === 'function') {
          ref(newMap);
        } else {
          ref.current = newMap;
        }
      }
    });
    
    return () => {
      if (newMap) {
        newMap.remove();
      }
    };
  }, []);
  
  // Handle destination selection
  const handleDestinationClick = (destination: Destination) => {
    onDestinationSelect(destination);
  };
  
  // Handle POI selection
  const handlePoiClick = (poi: PointOfInterest) => {
    onPOISelect(poi);
  };

  // Centralized POIDiscoveryManager initialization - only happens here
  useEffect(() => {
    if (map) {
      // Wait for map to be fully loaded before initializing POIDiscoveryManager
      map.once('idle', () => {
        console.log('Map fully loaded and idle, initializing POIDiscoveryManager');
        try {
          const poiManager = POIDiscoveryManager.getInstance();
          
          // Ensure POI coordinates are properly normalized before initialization
          const normalizedPOIs = routePOIs.map(poi => {
            // Create a copy to avoid mutating the original
            const normalizedPoi = {...poi};
            
            // Normalize position if needed
            if (normalizedPoi.position) {
              if (typeof normalizedPoi.position === 'object') {
                // Handle {lat, lng} format by ensuring both properties exist
                if ('lat' in normalizedPoi.position && 'lng' in normalizedPoi.position) {
                  // Format is correct, no change needed
                } else if ('latitude' in normalizedPoi.position && 'longitude' in normalizedPoi.position) {
                  // Convert from {latitude, longitude} to {lat, lng}
                  const lat = normalizedPoi.position.latitude as number;
                  const lng = normalizedPoi.position.longitude as number;
                  normalizedPoi.position = { lat, lng };
                }
              }
            }
            
            return normalizedPoi;
          });
          
          // Initialize with normalized POIs
          poiManager.initializePOIs(normalizedPOIs);
          
          // Set up event listeners for POI discovery events
          document.addEventListener('poi-discovery', (event: Event) => {
            const customEvent = event as CustomEvent;
            if (customEvent.detail && customEvent.detail.poi) {
              console.log('POI Discovery Event received:', customEvent.detail.poi.name);
              // You could trigger UI updates or animations here
            }
          });
          
          console.log('POI Discovery Manager initialized with', normalizedPOIs.length, 'POIs');
        } catch (error) {
          console.error('Error initializing POI Discovery Manager:', error);
        }
      });
    }
  }, [map, routePOIs]);

  // Handle adding/removing POIs from selection
  const handleAddPOI = (poi: PointOfInterest) => {
    if (!selectedPOIs.some(p => p.id === poi.id)) {
      onPOISelect(poi);
    }
  };

  const handleRemovePOI = (poi: PointOfInterest) => {
    if (selectedPOIs.some(p => p.id === poi.id)) {
      onPOISelect(poi); // Toggle selection off
    }
  };

  return (
    <div className="relative w-full h-full">
      <div ref={mapContainerRef} className="w-full h-full"></div>
      
      {/* Map controls */}
      <div className="absolute bottom-24 right-5 z-10 flex flex-col space-y-2">
        <button className="bg-white p-2 rounded-full shadow-md text-gray-700 hover:bg-gray-100" onClick={() => map?.zoomIn()}>
          <span className="text-lg">+</span>
        </button>
        <button className="bg-white p-2 rounded-full shadow-md text-gray-700 hover:bg-gray-100" onClick={() => map?.zoomOut()}>
          <span className="text-lg">-</span>
        </button>
      </div>
    </div>
  );
});

MapComponent.displayName = 'MapComponent';

export default MapComponent;
