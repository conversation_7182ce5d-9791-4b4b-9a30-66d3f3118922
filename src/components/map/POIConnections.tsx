import React, { useEffect, useMemo, useState } from 'react';
import { PointOfInterest } from '@/components/PointOfInterestCard';
import { Destination } from '@/data/destinations';
import mapboxgl from 'mapbox-gl';
import { logger } from '@/utils/debugLogger';

interface POIConnectionsProps {
  map: mapboxgl.Map;
  selectedPOIs: PointOfInterest[];
  destinations: Destination[];
  findNearestDestination: (poi: PointOfInterest) => Destination | undefined;
}

const POIConnections: React.FC<POIConnectionsProps> = ({
  map,
  selectedPOIs,
  destinations,
  findNearestDestination
}) => {
  // Add state to track if map is fully ready
  const [isMapReady, setIsMapReady] = useState(false);

  // Listen for the mapFullyReady event
  useEffect(() => {
    if (!map) return;

    // Check if map is already ready
    const checkMapReady = () => {
      // More thorough check for map readiness
      const isLoaded = map.loaded();
      const hasCenter = map.getCenter() && map.getCenter().lng && map.getCenter().lat;
      const hasZoom = map.getZoom() > 0;
      const hasStyle = map.isStyleLoaded();
      
      if (isLoaded && hasCenter && hasZoom && hasStyle) {
        logger.logMapEvent('POIConnections: Map is ready', { 
          zoom: map.getZoom(), 
          center: map.getCenter(),
          styleLoaded: hasStyle
        });
        setIsMapReady(true);
      } else {
        logger.logMapEvent('POIConnections: Map not fully ready', { 
          isLoaded, hasCenter, hasZoom, hasStyle
        });
        setTimeout(checkMapReady, 200);
      }
    };
    
    // Handler for the custom event
    const handleMapReady = (e: CustomEvent) => {
      logger.logMapEvent('POIConnections: Received mapFullyReady event');
      setIsMapReady(true);
    };

    // Add event listener
    document.addEventListener('mapFullyReady', handleMapReady as EventListener);
    
    // Initial check
    checkMapReady();
    
    // Cleanup
    return () => {
      document.removeEventListener('mapFullyReady', handleMapReady as EventListener);
    };
  }, [map]);

  // Calculate connections once when dependencies change
  const connections = useMemo(() => {
    const features = selectedPOIs
      .map(poi => {
        const nearest = findNearestDestination(poi);
        if (!nearest) return null;

        return {
          type: 'Feature' as const,
          properties: {
            id: `${poi.id}-${nearest.id}`,
            poiId: poi.id,
            destinationId: nearest.id,
            poiName: poi.name,
            destinationName: nearest.name,
            poiType: poi.type || 'default'
          },
          geometry: {
            type: 'LineString' as const,
            coordinates: [
              poi.coordinates,
              nearest.coordinates
            ]
          }
        };
      })
      .filter(Boolean);

    return {
      type: 'FeatureCollection' as const,
      features
    };
  }, [selectedPOIs, findNearestDestination]);

  useEffect(() => {
    if (!map || !isMapReady) {
      logger.logMapEvent('POIConnections: Deferring POI connections - map not ready');
      return;
    }

    if (connections.features.length === 0) {
      logger.logMapEvent('POIConnections: No POI connections to draw');
      return;
    }

    const sourceId = 'poi-connections';
    const layerId = 'poi-connections-layer';
    const highlightLayerId = 'poi-connections-highlight-layer';

    const setupConnectionsLayer = () => {
      try {
        logger.logMapEvent('POIConnections: Setting up connections layer', {
          connectionsCount: connections.features.length
        });
        
        // Add or update source
        if (!map.getSource(sourceId)) {
          map.addSource(sourceId, {
            type: 'geojson',
            data: connections
          });
        } else {
          const source = map.getSource(sourceId) as mapboxgl.GeoJSONSource;
          source.setData(connections);
        }

        // Add base layer if it doesn't exist
        if (!map.getLayer(layerId)) {
          map.addLayer({
            id: layerId,
            type: 'line',
            source: sourceId,
            layout: {
              'line-join': 'round',
              'line-cap': 'round',
              visibility: 'visible'
            },
            paint: {
              'line-color': [
                'match',
                ['get', 'poiType'],
                'historical', '#f6ad55',
                'cultural', '#9f7aea',
                'natural', '#48bb78',
                'adventure', '#f56565',
                'food', '#ed8936',
                'shopping', '#4299e1',
                '#4299e1' // default color
              ],
              'line-width': [
                'interpolate',
                ['linear'],
                ['zoom'],
                5, 1.5,
                10, 2,
                16, 3.5
              ],
              'line-opacity': 0.7,
              'line-dasharray': [1, 2]
            }
          });
          
          // Add highlight layer
          map.addLayer({
            id: highlightLayerId,
            type: 'line',
            source: sourceId,
            layout: {
              'line-join': 'round',
              'line-cap': 'round',
              visibility: 'visible'
            },
            paint: {
              'line-color': [
                'match',
                ['get', 'poiType'],
                'historical', '#dd6b20',
                'cultural', '#805ad5',
                'natural', '#2f855a',
                'adventure', '#e53e3e',
                'food', '#c05621',
                'shopping', '#2b6cb0',
                '#2b6cb0' // default color
              ],
              'line-width': [
                'interpolate',
                ['linear'],
                ['zoom'],
                5, 3,
                10, 4,
                16, 6
              ],
              'line-opacity': 0,
              'line-dasharray': [1, 1]
            },
            filter: ['==', 'id', '']
          });

          // Add hover effects
          map.on('mouseenter', layerId, (e) => {
            map.getCanvas().style.cursor = 'pointer';
            
            // Check if we have a feature
            if (e.features && e.features.length > 0) {
              const feature = e.features[0];
              // Highlight the connection
              map.setFilter(highlightLayerId, ['==', 'id', feature.properties.id]);
              map.setPaintProperty(highlightLayerId, 'line-opacity', 0.9);
              
              // Show a popup with the POI name
              new mapboxgl.Popup({
                closeButton: false,
                closeOnClick: false,
                className: 'connection-popup'
              })
              .setLngLat(e.lngLat)
              .setHTML(`<div>
                <h3>${feature.properties.poiName}</h3>
                <p>Connected to ${feature.properties.destinationName}</p>
              </div>`)
              .addTo(map);
            }
          });

          map.on('mouseleave', layerId, () => {
            map.getCanvas().style.cursor = '';
            map.setFilter(highlightLayerId, ['==', 'id', '']);
            map.setPaintProperty(highlightLayerId, 'line-opacity', 0);
            
            // Remove any popups
            const popups = document.getElementsByClassName('connection-popup');
            while(popups[0]) {
              popups[0].remove();
            }
          });
        }
        
        logger.logMapEvent('POIConnections: Successfully set up connections layer');
      } catch (error) {
        logger.logMapEvent('POIConnections: Error setting up connections', { error: String(error) });
      }
    };

    // If map is ready, set up the layer immediately
    setupConnectionsLayer();

    return () => {
      try {
        // Remove layers and source
        if (map.getLayer(highlightLayerId)) {
          map.removeLayer(highlightLayerId);
        }
        if (map.getLayer(layerId)) {
          map.removeLayer(layerId);
        }
        if (map.getSource(sourceId)) {
          map.removeSource(sourceId);
        }
        
        logger.logMapEvent('POIConnections: Cleaned up connections layer');
      } catch (error) {
        logger.logMapEvent('POIConnections: Error cleaning up connections', { error: String(error) });
      }
    };
  }, [map, connections, isMapReady]);

  return null;
};

export default React.memo(POIConnections); 