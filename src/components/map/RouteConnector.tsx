import React, { useEffect, useRef } from 'react';
import mapboxgl from 'mapbox-gl';
import { Destination } from '@/types/POITypes'; // Corrected import path
// PointOfInterest import might not be needed if POI badges are removed with markers
// import { PointOfInterest } from '@/components/PointOfInterestCard'; 

interface RouteConnectorProps {
  map: mapboxgl.Map | null;
  // destinations prop might not be needed if markers are not created here
  // destinations: Destination[]; 
  // selectedPOIs prop might not be needed if POI badges are removed
  // selectedPOIs: PointOfInterest[]; 
  selectedDestinations: Destination[];
}

const RouteConnector: React.FC<RouteConnectorProps> = ({
  map,
  // destinations, // Removed
  // selectedPOIs, // Removed
  selectedDestinations
}) => {
  // markersRef is no longer needed as this component will not create markers
  // const markersRef = useRef<mapboxgl.Marker[]>([]); 
  const routeSourceId = 'morocco-direct-route-source'; // Renamed for clarity
  const routeLayerId = 'morocco-direct-route-layer';   // Renamed for clarity
  const fallbackRouteLayerId = 'morocco-direct-route-fallback-layer'; // Renamed

  // Cleanup for markers is no longer needed here
  // useEffect(() => {
  //   return () => {
  //     if (markersRef.current) {
  //       markersRef.current.forEach(marker => marker.remove());
  //     }
  //   };
  // }, []);

  useEffect(() => {
    if (!map || !map.isStyleLoaded()) { // Ensure map style is loaded
      // Clear previous route if map becomes null or style not loaded
      if (map && map.getSource(routeSourceId)) {
        if (map.getLayer(routeLayerId)) map.removeLayer(routeLayerId);
        if (map.getLayer(fallbackRouteLayerId)) map.removeLayer(fallbackRouteLayerId);
        map.removeSource(routeSourceId);
      }
      return;
    }

    // If no selected destinations or less than 2, remove existing route and return
    if (selectedDestinations.length < 2) {
      if (map.getSource(routeSourceId)) {
        if (map.getLayer(routeLayerId)) map.removeLayer(routeLayerId);
        if (map.getLayer(fallbackRouteLayerId)) map.removeLayer(fallbackRouteLayerId);
        map.removeSource(routeSourceId);
      }
      return;
    }

    // Marker creation logic REMOVED from here.
    // It is assumed that another component (e.g., DestinationMarker.tsx) handles destination markers.

    // Create route lines if we have at least 2 destinations
    const coordinates = selectedDestinations.map(dest => dest.coordinates);

    // Always remove previous sources and layers to avoid conflicts and ensure clean updates
    if (map.getSource(routeSourceId)) {
      if (map.getLayer(routeLayerId)) map.removeLayer(routeLayerId);
      if (map.getLayer(fallbackRouteLayerId)) map.removeLayer(fallbackRouteLayerId);
      map.removeSource(routeSourceId);
    }

    map.addSource(routeSourceId, {
      type: 'geojson',
      data: {
        type: 'Feature',
        properties: {},
        geometry: {
          type: 'LineString',
          coordinates
        }
      }
    });

    map.addLayer({
      id: routeLayerId,
      type: 'line',
      source: routeSourceId,
      layout: {
        'line-join': 'round',
        'line-cap': 'round'
      },
      paint: {
        'line-color': '#E27D60', // Terracotta color for direct line
        'line-width': 3, // Slightly thinner than realistic route perhaps
        'line-opacity': 0.75
      }
    });

    // Optional: Add a fallback/shadow layer if desired for visual style
    // For simplicity, this example keeps one primary line.
    // If fallbackRouteLayerId is used, ensure its paint properties are distinct.
    /*
    map.addLayer({
      id: fallbackRouteLayerId,
      type: 'line',
      source: routeSourceId,
      layout: {
        'line-join': 'round',
        'line-cap': 'round'
      },
      paint: {
        'line-color': '#A45D5D', // Darker terracotta or contrasting color
        'line-width': 5,
        'line-opacity': 0.5,
        'line-gap-width': 2 // Creates a gap for the main line if desired
      }
    });
    */

    // Cleanup function to remove the source and layers when destinations change or component unmounts
    return () => {
      if (map && map.isStyleLoaded() && map.getSource(routeSourceId)) {
        if (map.getLayer(routeLayerId)) map.removeLayer(routeLayerId);
        if (map.getLayer(fallbackRouteLayerId)) map.removeLayer(fallbackRouteLayerId); // If used
        map.removeSource(routeSourceId);
      }
    };
  }, [map, selectedDestinations]); // Dependencies: map and selectedDestinations

  return null; // This component does not render any visible React elements
};

export default RouteConnector;
