/**
 * AnimationControlPanel.tsx
 * 
 * A client-themed control panel for animation management using the new hooks
 */

import React, { useState, useEffect } from 'react';
import useAnimationManager from '@/hooks/useAnimationManager';
import useTheme from '@/hooks/useTheme';
import ThemedButton from '@/components/ui/ThemedButton';
import { Play, Pause, Square, /* SkipForward, */ Settings } from 'lucide-react';

interface AnimationControlPanelProps {
  // Allow using either mapInstance or direct props
  mapInstance?: mapboxgl.Map;
  // Direct props for when animation manager is passed from parent
  isAnimating?: boolean;
  isPaused?: boolean;
  progress?: number;
  onPause?: () => void;
  onStop?: () => void;
  onRestart?: () => void;
  // Other props
  onSettingsClick?: () => void;
  showSettings?: boolean;
  className?: string;
}

const AnimationControlPanel: React.FC<AnimationControlPanelProps> = ({
  mapInstance,
  isAnimating: externalIsAnimating,
  isPaused: externalIsPaused,
  progress: externalProgress,
  onPause,
  onStop,
  onRestart,
  onSettingsClick,
  showSettings = true,
  className = ''
}) => {
  const { theme, utils } = useTheme();
  
  // Use the animation manager hook when mapInstance is provided
  const animationManager = mapInstance ? useAnimationManager(mapInstance) : null;
  
  // Use either the hook values or the props passed directly
  const isAnimating = externalIsAnimating ?? animationManager?.isAnimating ?? false;
  const isPaused = externalIsPaused ?? animationManager?.isPaused ?? false;
  const progress = externalProgress ?? animationManager?.progress ?? 0;
  
  // Local UI state
  const [speed, setSpeed] = useState(1.0);
  const [showSpeedControls, setShowSpeedControls] = useState(false);
  
  // Format progress as percentage
  const progressPercentage = Math.round(progress * 100);

  // Update speed when changed
  useEffect(() => {
    // If we had a setAnimationSpeed method in the hook, we'd call it here
    // For now, this is just a placeholder
  }, [speed]);

  // Panel styles based on theme
  const panelStyle = {
    backgroundColor: utils.getBgColor(),
    borderColor: utils.getBorderColor(),
    color: utils.getTextColor()
  };
  
  // Progress bar styles
  const progressBarStyle = {
    backgroundColor: utils.getAccentColor(),
    height: '4px'
  };
  
  // Create handler functions that use either callbacks or the animation manager
  const handlePause = () => {
    if (onPause) {
      onPause();
    } else if (animationManager) {
      animationManager.togglePause();
    }
  };
  
  const handleStop = () => {
    if (onStop) {
      onStop();
    } else if (animationManager) {
      animationManager.stopAnimation();
    }
  };
  
  const handleStart = () => {
    if (onRestart) {
      onRestart();
    } else if (animationManager && mapInstance) {
      // This would typically need route data
      console.log("Would start animation here");
      // animationManager.startAnimation(routeData);
    }
  };

  return (
    <div 
      className={`rounded-lg shadow-lg p-4 ${className}`}
      style={panelStyle}
    >
      {/* Progress indicator */}
      <div className="mb-4">
        {/* Comment out or remove the journey progress UI and Start button */}
        {/* <div className="flex justify-between mb-1 text-sm">
          <span>Journey Progress</span>
          <span>{progressPercentage}%</span>
        </div> */}
        <div className="bg-gray-700 rounded-full h-1 w-full">
          <div 
            className="rounded-full" 
            style={{
              ...progressBarStyle,
              width: `${progressPercentage}%`
            }} 
          />
        </div>
      </div>
      
      {/* Controls */}
      <div className="flex justify-between items-center">
        <div className="flex space-x-2">
          {!isAnimating ? (
            <ThemedButton 
              variant="primary"
              onClick={handleStart}
            >
              {/* Comment out or remove the journey progress UI and Start button */}
              {/* <Play size={16} className="mr-1" /> Start */}
              {/* Provide children to avoid linter error */}
              {/* Empty string as placeholder */}
              {''}
            </ThemedButton>
          ) : (
            <>
              {isPaused ? (
                <ThemedButton 
                  variant="primary"
                  onClick={handlePause}
                >
                  <Play size={16} className="mr-1" /> Resume
                </ThemedButton>
              ) : (
                <ThemedButton 
                  variant="secondary"
                  onClick={handlePause}
                >
                  <Pause size={16} className="mr-1" /> Pause
                </ThemedButton>
              )}
              
              <ThemedButton 
                variant="secondary"
                onClick={handleStop}
              >
                <Square size={16} className="mr-1" /> Stop
              </ThemedButton>
            </>
          )}
        </div>
        
        {showSettings && (
          <ThemedButton 
            variant="outline"
            onClick={onSettingsClick}
          >
            <Settings size={16} />
          </ThemedButton>
        )}
      </div>
      
      {/* Speed controls (toggleable) */}
      {showSpeedControls && (
        <div className="mt-4">
          <div className="flex justify-between items-center mb-1">
            <span className="text-sm">Animation Speed: {speed}x</span>
          </div>
          <input
            type="range"
            min="0.5"
            max="2"
            step="0.1"
            value={speed}
            onChange={(e) => setSpeed(parseFloat(e.target.value))}
            className="w-full"
            style={{
              accentColor: utils.getAccentColor()
            }}
          />
        </div>
      )}
    </div>
  );
};

export default AnimationControlPanel; 