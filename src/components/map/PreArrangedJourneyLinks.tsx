import React, { useState, useRef } from 'react';
import { PreArrangedJourney } from '@/types/ItineraryParameters';
import { Route, Clock, Star, ChevronLeft, ChevronRight, ChevronUp, ChevronDown } from 'lucide-react';

interface PreArrangedJourneyLinksProps {
  journeys: PreArrangedJourney[];
  onJourneySelect: (journey: PreArrangedJourney) => void;
  className?: string;
  isMobile?: boolean;
}

const PreArrangedJourneyLinks: React.FC<PreArrangedJourneyLinksProps> = ({
  journeys,
  onJourneySelect,
  className = '',
  isMobile = false
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -200, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 200, behavior: 'smooth' });
    }
  };

  return (
    <div
      className={`popular-journeys-bottom-bar ${className}`}
      style={{
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 30,
        backgroundColor: 'rgba(249, 246, 238, 0.98)',
        backdropFilter: 'blur(12px)',
        borderTop: '1px solid rgba(177, 155, 123, 0.3)',
        boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.1)',
        transition: 'all 0.3s ease',
        transform: isExpanded ? 'translateY(0)' : 'translateY(calc(100% - 60px))'
      }}
    >
      {/* Collapsed Header Bar */}
      <div
        onClick={() => setIsExpanded(!isExpanded)}
        style={{
          height: '60px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '0 1.5rem',
          cursor: 'pointer',
          borderBottom: isExpanded ? '1px solid rgba(177, 155, 123, 0.2)' : 'none'
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
          <Route size={18} style={{ color: 'var(--morocco-red)' }} />
          <span
            style={{
              fontFamily: 'var(--font-heading)',
              fontWeight: 600,
              fontSize: '1rem',
              color: 'var(--morocco-blue)',
              letterSpacing: '0.5px'
            }}
          >
            POPULAR JOURNEYS
          </span>
          <span
            style={{
              fontSize: '0.875rem',
              color: 'var(--text-secondary)',
              fontWeight: 500
            }}
          >
            ({journeys.length} available)
          </span>
        </div>

        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
          <span
            style={{
              fontSize: '0.75rem',
              color: 'var(--text-secondary)',
              fontWeight: 500
            }}
          >
            {isExpanded ? 'Collapse' : 'Explore'}
          </span>
          {isExpanded ? (
            <ChevronDown size={20} style={{ color: 'var(--morocco-blue)' }} />
          ) : (
            <ChevronUp size={20} style={{ color: 'var(--morocco-blue)' }} />
          )}
        </div>
      </div>

      {/* Expanded Content */}
      {isExpanded && (
        <div
          style={{
            padding: '1rem 0',
            maxHeight: '300px',
            overflow: 'hidden'
          }}
        >
          {/* Scroll Controls */}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '1rem',
              padding: '0 1.5rem',
              marginBottom: '1rem'
            }}
          >
            <button
              onClick={scrollLeft}
              style={{
                width: '36px',
                height: '36px',
                borderRadius: '50%',
                backgroundColor: 'rgba(0, 71, 171, 0.1)',
                border: '1px solid rgba(0, 71, 171, 0.2)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'rgba(0, 71, 171, 0.2)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'rgba(0, 71, 171, 0.1)';
              }}
            >
              <ChevronLeft size={18} style={{ color: 'var(--morocco-blue)' }} />
            </button>

            <span
              style={{
                fontSize: '0.875rem',
                color: 'var(--text-secondary)',
                fontWeight: 500
              }}
            >
              Scroll to explore all journeys
            </span>

            <button
              onClick={scrollRight}
              style={{
                width: '36px',
                height: '36px',
                borderRadius: '50%',
                backgroundColor: 'rgba(0, 71, 171, 0.1)',
                border: '1px solid rgba(0, 71, 171, 0.2)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'rgba(0, 71, 171, 0.2)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'rgba(0, 71, 171, 0.1)';
              }}
            >
              <ChevronRight size={18} style={{ color: 'var(--morocco-blue)' }} />
            </button>
          </div>

          {/* Horizontal Scrolling Journey Cards */}
          <div
            ref={scrollContainerRef}
            style={{
              display: 'flex',
              gap: '1rem',
              padding: '0 1.5rem',
              overflowX: 'auto',
              overflowY: 'hidden',
              scrollBehavior: 'smooth',
              scrollbarWidth: 'thin',
              scrollbarColor: 'var(--morocco-blue) transparent'
            }}
            className="journey-scroll-container"
          >
            {journeys.map((journey) => (
            <div
              key={journey.id}
              onClick={() => onJourneySelect(journey)}
              style={{
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                backdropFilter: 'blur(8px)',
                border: '1px solid rgba(177, 155, 123, 0.3)',
                borderRadius: 'var(--radius-lg)',
                padding: '1rem',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                boxShadow: 'var(--shadow-soft)',
                minWidth: '220px',
                maxWidth: '220px',
                height: '180px',
                position: 'relative',
                overflow: 'hidden',
                flexShrink: 0
              }}
              className="journey-card"
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = 'var(--shadow-medium)';
                e.currentTarget.style.borderColor = 'var(--morocco-blue)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = 'var(--shadow-soft)';
                e.currentTarget.style.borderColor = 'rgba(177, 155, 123, 0.3)';
              }}
            >
              {/* Journey Header */}
              <div style={{ marginBottom: '0.75rem' }}>
                <h3 
                  style={{
                    margin: '0 0 0.25rem',
                    fontSize: '0.875rem',
                    fontWeight: 600,
                    color: 'var(--morocco-blue)',
                    fontFamily: 'var(--font-heading)',
                    lineHeight: 1.2
                  }}
                >
                  {journey.name}
                </h3>
                <div 
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.75rem',
                    fontSize: '0.75rem',
                    color: 'var(--text-secondary)'
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                    <Clock size={12} />
                    <span>{journey.duration} days</span>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                    <Star size={12} />
                    <span className="capitalize">{journey.difficulty}</span>
                  </div>
                </div>
              </div>

              {/* Journey Description */}
              <p 
                style={{
                  margin: '0 0 0.75rem',
                  fontSize: '0.75rem',
                  color: 'var(--text-secondary)',
                  lineHeight: 1.4,
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden'
                }}
              >
                {journey.description}
              </p>

              {/* Journey Tags */}
              <div 
                style={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: '0.25rem',
                  marginBottom: '0.75rem'
                }}
              >
                {journey.tags?.slice(0, 3).map((tag) => (
                  <span
                    key={tag}
                    style={{
                      padding: '0.125rem 0.5rem',
                      backgroundColor: 'rgba(0, 71, 171, 0.1)',
                      color: 'var(--morocco-blue)',
                      borderRadius: 'var(--radius-full)',
                      fontSize: '0.625rem',
                      fontWeight: 500,
                      textTransform: 'capitalize'
                    }}
                  >
                    {tag.replace('-', ' ')}
                  </span>
                ))}
              </div>

              {/* Journey Price */}
              {journey.price && (
                <div 
                  style={{
                    fontSize: '0.75rem',
                    fontWeight: 600,
                    color: 'var(--morocco-red)',
                    textAlign: 'right'
                  }}
                >
                  From {journey.price.currency} {journey.price.from}
                </div>
              )}

              {/* Hover Overlay */}
              <div 
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: 'linear-gradient(45deg, rgba(0, 71, 171, 0.05), rgba(139, 26, 24, 0.05))',
                  opacity: 0,
                  transition: 'opacity 0.2s ease',
                  pointerEvents: 'none'
                }}
                className="journey-card-overlay"
              />
            </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default PreArrangedJourneyLinks;

// Add CSS for scrollbar styling
const scrollbarStyles = `
  .journey-scroll-container::-webkit-scrollbar {
    height: 6px;
  }

  .journey-scroll-container::-webkit-scrollbar-track {
    background: rgba(177, 155, 123, 0.1);
    border-radius: 3px;
  }

  .journey-scroll-container::-webkit-scrollbar-thumb {
    background: var(--morocco-blue);
    border-radius: 3px;
  }

  .journey-scroll-container::-webkit-scrollbar-thumb:hover {
    background: var(--morocco-red);
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = scrollbarStyles;
  document.head.appendChild(styleElement);
}
