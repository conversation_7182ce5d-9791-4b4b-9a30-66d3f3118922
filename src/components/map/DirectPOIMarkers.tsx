import React, { useEffect, useRef, useCallback, useState } from 'react';
import mapboxgl from 'mapbox-gl';
import * as turf from '@turf/turf';
import { throttle } from '@/utils/throttleUtils';
import { PointOfInterest } from '@/types/POITypes';
import { createPoiPopupElement } from './MapPopups';
import { toPositionObject as convertToPositionObject, PositionObject as TruePositionObject } from '@/types/Position';
import { useMapboxReadiness } from '@/hooks/useMapboxReadiness';

/**
 * DirectPOIMarkers.tsx
 *
 * Receives filtered POIs as props, following modular architecture:
 * - Country > Cities > POIs hierarchy
 * - All filtering is done in HomePage/ExploreMap, this component is a pure renderer
 */

// Helper function to validate coordinates
const isValidCoordinates = (coordinates: any): coordinates is [number, number] => {
  return (
    Array.isArray(coordinates) &&
    coordinates.length === 2 &&
    typeof coordinates[0] === 'number' &&
    !isNaN(coordinates[0]) &&
    typeof coordinates[1] === 'number' &&
    !isNaN(coordinates[1]) &&
    coordinates[0] >= -180 && 
    coordinates[0] <= 180 &&
    coordinates[1] >= -90 && 
    coordinates[1] <= 90
  );
};

interface DirectPOIMarkersProps {
  map: mapboxgl.Map;
  pois: PointOfInterest[];
  selectedPOIs: PointOfInterest[];
  onPOIClick: (poi: PointOfInterest) => void;
  onPOIHover?: (poi: PointOfInterest, event: MouseEvent) => void;
  onPOIHoverEnd?: () => void;
}

/**
 * This component directly renders POI markers by appending DOM elements
 * to the Mapbox container instead of using React's DOM - this avoids positioning issues.
 */
const DirectPOIMarkers: React.FC<DirectPOIMarkersProps> = ({
  map,
  pois: componentPois,
  selectedPOIs: componentSelectedPOIs,
  onPOIClick,
  onPOIHover,
  onPOIHoverEnd
}) => {
  const markerElementsRef = useRef<Map<string, HTMLDivElement>>(new Map());
  const isMountedRef = useRef(false);
  
  // Use the new hook for readiness detection
  const isMapFullyReady = useMapboxReadiness(map);

  // Helper: Debounce function
  const debounce = <F extends (...args: any[]) => any>(func: F, waitFor: number) => {
    let timeout: ReturnType<typeof setTimeout> | null = null;
    return (...args: Parameters<F>): Promise<ReturnType<F>> =>
      new Promise(resolve => {
        if (timeout) {
          clearTimeout(timeout);
        }
        timeout = setTimeout(() => resolve(func(...args)), waitFor);
      });
  };

  // Marker Management Functions
  const createOrUpdateMarker = useCallback(
    (poi: PointOfInterest) => {
      // Guard against map not being ready moved to useEffect
      if (!map) return;

      // Define position at the start of the function scope
      const position = convertToPositionObject(poi.coordinates || poi.position);
      if (!position || !isValidCoordinates([position.lng, position.lat])) {
        console.warn(`[DirectPOIMarkers] Invalid or missing coordinates for POI ID: ${poi.id}, Name: ${poi.name}`, poi.coordinates);
        return; 
      }

      let markerElement = markerElementsRef.current.get(String(poi.id));

      if (!markerElement) {
        // Create the marker element if it doesn't exist
        markerElement = document.createElement('div');
        markerElement.setAttribute('data-poi-id', String(poi.id));
        markerElement.style.width = '40px';
        markerElement.style.height = '40px';
        markerElement.style.backgroundColor = 'rgba(255,255,255,0.85)';
        markerElement.style.border = '2px solid #41B3A3';
        markerElement.style.zIndex = '10';
        markerElement.style.cursor = 'pointer';
        markerElement.style.opacity = '1';
        markerElement.style.borderRadius = '50%';
        markerElement.style.boxShadow = '0 4px 16px rgba(0,0,0,0.18)';
        markerElement.style.overflow = 'hidden';
        
        const img = document.createElement('img');
        img.alt = poi.name || "POI";
        img.style.width = "80px";
        img.style.height = "80px";
        img.style.objectFit = "cover";
        img.style.borderRadius = "4px";

        // Attempt to load the primary image, then local placeholder, then hide
        img.setAttribute('data-load-attempt', 'primary'); // Mark initial attempt as primary

        let imageToLoad = '';
        if (poi.images && poi.images.length > 0 && poi.images[0]) {
          imageToLoad = poi.images[0];
        } else {
          imageToLoad = '/images/placeholder.svg'; // Default to local placeholder
          // If we're already defaulting, mark it as a placeholder attempt directly
          img.setAttribute('data-load-attempt', 'placeholder'); 
        }

        if (imageToLoad.includes('via.placeholder.com')) {
          console.warn(`[DirectPOIMarkers createOrUpdateMarker] WARNING: Attempting to load via.placeholder.com for ${poi.name}. Image to load: ${imageToLoad}, POI Object:`, JSON.stringify(poi, null, 2));
        }

        img.onload = () => {
          // console.log(`[DirectPOIMarkers] Image loaded successfully for ${poi.name || poi.id}: ${img.src}`);
        };

        img.onerror = () => {
          if (img.getAttribute('data-load-attempt') === 'placeholder') {
            // Placeholder itself failed
            console.warn(`[DirectPOIMarkers] LOCAL PLACEHOLDER /images/placeholder.svg FAILED to load for POI: ${poi.name || poi.id}. Hiding image.`);
            img.style.display = 'none';
          } else {
            // Primary image failed, try placeholder
            // console.log(`[DirectPOIMarkers] Primary image for ${poi.name || poi.id} (src: ${img.src}) failed. Attempting local placeholder.`);
            img.setAttribute('data-load-attempt', 'placeholder');
            img.src = '/images/placeholder.svg';
          }
        };

        // Start loading the determined image
        img.src = imageToLoad;

        const nameDiv = document.createElement("div");
        nameDiv.textContent = poi.name;
        nameDiv.style.position = 'absolute';
        nameDiv.style.bottom = '0';
        nameDiv.style.left = '50%';
        nameDiv.style.transform = 'translate(-50%, 100%)';
        nameDiv.style.color = '#fff';
        nameDiv.style.fontSize = '12px';
        nameDiv.style.fontWeight = 'bold';
        nameDiv.style.padding = '4px';
        nameDiv.style.borderRadius = '4px 4px 0 0';
        nameDiv.style.background = 'rgba(0, 0, 0, 0.5)';

        markerElement.appendChild(img);
        markerElement.appendChild(nameDiv);
        markerElementsRef.current.set(String(poi.id), markerElement);

        // Add click event listener
        markerElement.addEventListener('click', (e) => {
          e.stopPropagation();
          onPOIClick(poi);
        });

        // Add hover event listeners
        if (onPOIHover) {
          markerElement.addEventListener('mouseenter', (e) => {
            onPOIHover(poi, e as MouseEvent);
          });
        }

        if (onPOIHoverEnd) {
          markerElement.addEventListener('mouseleave', () => {
            onPOIHoverEnd();
          });
        }
      }

      // Project the POI's lng/lat to screen coordinates
      const point = map.project([position.lng, position.lat]);
      markerElement.style.position = 'absolute';
      markerElement.style.left = `${point.x}px`;
      markerElement.style.top = `${point.y}px`;
      markerElement.style.transform = 'translate(-50%, -100%)';
      markerElement.style.display = 'block';
      const isSelected = componentSelectedPOIs?.some(p => String(p.id) === String(poi.id));
      if (isSelected) {
        markerElement.style.borderColor = '#1de9b6';
        markerElement.style.zIndex = '15';
      } else {
        markerElement.style.borderColor = '#41B3A3';
        markerElement.style.zIndex = '10';
      }

      // Add to map container if not already present
      const mapContainer = map.getContainer();
      if (mapContainer && !markerElement.parentNode) {
        mapContainer.appendChild(markerElement);
      } else if (!mapContainer) {
          console.warn(`[DirectPOIMarkers] Map container not found for POI ID: ${poi.id}. Cannot append marker.`);
      }
    },
    [map, onPOIClick, componentSelectedPOIs] // Removed isMapFullyReady from deps, handled by useEffect
  );

  const removeMarker = useCallback((poiId: string) => {
    const markerElement = markerElementsRef.current.get(poiId);
    if (markerElement) {
      markerElement.remove();
      markerElementsRef.current.delete(poiId);
    }
  }, []);

  const removeAllMarkers = useCallback(() => {
    let elementRemovalCount = 0;
    markerElementsRef.current.forEach(el => {
      el.remove();
      elementRemovalCount++;
    });
    markerElementsRef.current.clear();
    console.log(`[DirectPOIMarkers removeAllMarkers] Attempted to remove ${elementRemovalCount} custom elements.`);
  }, []);

  const updateAllMarkers = useCallback(() => {
    // Guard against map not being ready moved to useEffect
    if (!map ) { 
      console.log('[DirectPOIMarkers] updateAllMarkers called but map not ready. Bailing.');
      return;
    }
    console.log('[DirectPOIMarkers] Updating all markers. POI count:', componentPois.length);

    const currentPoiIds = new Set(componentPois.map(p => String(p.id)));
    // Remove marker elements for POIs that are no longer present
    const poiIdsToRemove = Array.from(markerElementsRef.current.keys()).filter(id => !currentPoiIds.has(id));
    poiIdsToRemove.forEach(poiId => {
        removeMarker(poiId);
    });

    componentPois.forEach(poi => {
      if (poi && poi.id && poi.position && poi.name) {
        try {
          createOrUpdateMarker(poi);
        } catch (e) {
          console.error(`[DirectPOIMarkers] Error in updateAllMarkers for POI ${poi.id}:`, e);
        }
      } else {
        console.warn('[DirectPOIMarkers] Invalid POI object in componentPois:', poi);
      }
    });
  }, [map, componentPois, createOrUpdateMarker, removeMarker]); // Removed isMapFullyReady from deps

  const debouncedUpdateAllMarkers = useCallback(debounce(updateAllMarkers, 100), [updateAllMarkers]);

  // Helper to update all marker positions on map move/zoom
  const updateMarkerPositions = useCallback(() => {
    // Guard against map not being ready moved to useEffect
    if (!map || !isMapFullyReady) return; 
    
    componentPois.forEach(poi => {
      const markerElement = markerElementsRef.current.get(String(poi.id));
      if (markerElement && map) {
        const position = convertToPositionObject(poi.coordinates || poi.position);
        if (position && isValidCoordinates([position.lng, position.lat])) {
          const point = map.project([position.lng, position.lat]);
          markerElement.style.left = `${point.x}px`;
          markerElement.style.top = `${point.y}px`;
        }
      }
    });
  }, [componentPois, map, isMapFullyReady]); // Added isMapFullyReady to deps

  const debouncedUpdateMarkerPositions = useCallback(debounce(updateMarkerPositions, 16), [updateMarkerPositions]); // 16ms for ~60fps

  // Effect to manage markers based on map state and props
  useEffect(() => {
    isMountedRef.current = true;
    console.log(`[DirectPOIMarkers] useEffect running. isMapFullyReady: ${isMapFullyReady}, POIs: ${componentPois.length}`);

    if (!map || !isMapFullyReady) {
      console.log('[DirectPOIMarkers] Map not fully ready in useEffect. Cleaning up existing markers if any.');
      // Clean up any existing markers if the map becomes not ready
      removeAllMarkers();
      return;
    }

    // Map is ready, proceed with marker setup/updates
    console.log('[DirectPOIMarkers] Map is fully ready, calling updateAllMarkers.');
    updateAllMarkers();

    // Add map move listener for updating positions
    // (Ensure this listener is also cleaned up)
    map.on('move', debouncedUpdateMarkerPositions);
    map.on('zoom', debouncedUpdateMarkerPositions);

    return () => {
      isMountedRef.current = false;
      // Remove map move listener on cleanup
      if (map) {
        map.off('move', debouncedUpdateMarkerPositions);
        map.off('zoom', debouncedUpdateMarkerPositions);
      }
      console.log('[DirectPOIMarkers] useEffect cleanup. (Not removing all markers by default here)');
    };
  }, [map, isMapFullyReady, componentPois.length]); // Simplified dependencies to prevent infinite loop

  // No explicit rendering from this component, it directly manipulates the map DOM
  return null; 
};

export default DirectPOIMarkers; 