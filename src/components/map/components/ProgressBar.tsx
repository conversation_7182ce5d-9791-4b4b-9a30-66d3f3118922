import React from 'react';

interface ProgressBarProps {
  progress: number;
  showPercentage?: boolean;
  className?: string;
  barColor?: string;
  height?: string;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  showPercentage = false,
  className = '',
  barColor = 'bg-blue-500',
  height = 'h-2'
}) => {
  const percentage = Math.round(progress * 100);
  
  return (
    <div className={`relative w-full ${height} bg-gray-200 rounded-full overflow-hidden ${className}`}>
      <div
        className={`absolute left-0 top-0 bottom-0 ${barColor} transition-all duration-300 ease-in-out`}
        style={{ width: `${percentage}%` }}
      />
      {showPercentage && (
        <div className="absolute inset-0 flex items-center justify-center text-xs font-medium">
          {percentage}%
        </div>
      )}
    </div>
  );
};

export default ProgressBar; 