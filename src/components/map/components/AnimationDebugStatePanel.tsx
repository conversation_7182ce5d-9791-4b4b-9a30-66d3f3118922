import React from 'react';
import { Destination } from '@/types/destination';

interface AnimationDebugStatePanelProps {
  isAnimating: boolean;
  isRouteLoading: boolean;
  routeError: string | null;
  showButton: boolean;
  initialRouteLength: number;
  hasMap: boolean;
  destinations: Destination[];
  onDebugStart?: () => void;
}

/**
 * Debug panel for monitoring animation state variables
 * Only displayed in development mode
 */
const AnimationDebugStatePanel: React.FC<AnimationDebugStatePanelProps> = ({
  isAnimating,
  isRouteLoading,
  routeError,
  showButton,
  initialRouteLength,
  hasMap,
  destinations,
  onDebugStart
}) => {
  // Only render in development
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <div className="debug-state-container">
      <div>Button Conditions:</div>
      <div>• isAnimating: {isAnimating ? '❌' : '✅'}</div>
      <div>• isRouteLoading: {isRouteLoading ? '🔄' : '✅'}</div>
      <div>• routeError: {routeError ? '❌' : '✅'}</div>
      <div>• showButton: {showButton ? '✅' : '❌'}</div>
      <div>• Button Should Show: {(!isAnimating && destinations?.length >= 2) ? '✅' : '❌'}</div>
      <div>• React Button Rendered: {(!isAnimating && destinations?.length >= 2) ? '✅' : '❌'}</div>
      <div>• DOM Button Count: {typeof document !== 'undefined' ? document.querySelectorAll('.begin-journey-button:not(.react-button), .continue-journey-button:not(.react-button)').length : 'N/A'}</div>
      <div>• initialRouteLength: {initialRouteLength}</div>
      <div>• map exists: {hasMap ? '✅' : '❌'}</div>
      <div>• destinations count: {destinations?.length || 0}</div>
      
      {/* Debug Button - will always show in development */}
      {destinations && destinations.length >= 2 && !isAnimating && onDebugStart && (
        <button
          className="debug-journey-button"
          onClick={onDebugStart}
        >
          Debug: Start Journey
        </button>
      )}
      
      {destinations && destinations.length > 0 && (
        <div>
          <div>Selected Destinations:</div>
          {destinations.map((dest, index) => (
            <div key={index} style={{ marginLeft: '8px', fontSize: '9px' }}>
              {index + 1}. {dest.name} [{dest.coordinates?.[0]?.toFixed(4)}, {dest.coordinates?.[1]?.toFixed(4)}]
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default AnimationDebugStatePanel; 