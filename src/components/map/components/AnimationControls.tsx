import React from 'react';
import JourneyButton from './JourneyButton';
import ProgressBar from './ProgressBar';
import JourneyProgressIndicator from './JourneyProgressIndicator';
import { Destination } from '@/types/destination';
import { ButtonState } from '@/types/TravelAnimatorTypes';
import { ComponentInteractionManager } from '../animation/ComponentInteractionManager';

interface AnimationControlsProps {
  isAnimating: boolean;
  isPaused: boolean;
  progress: number;
  buttonState: ButtonState;
  buttonError: string | null;
  showButton: boolean;
  destinations: Destination[];
  isRouteLoading: boolean;
  routeError: string | null;
  onStart: () => void;
  onPauseResume: () => void;
  onPauseClick: () => void;
  isLoading?: boolean;
  disabled?: boolean;
  setButtonState: (state: ButtonState) => void;
  setIsPaused?: (paused: boolean) => void;
  setButtonError: (error: string | null) => void;
}

/**
 * Component that contains all animation control UI elements
 */
const AnimationControls: React.FC<AnimationControlsProps> = ({
  isAnimating,
  isPaused,
  progress,
  buttonState,
  buttonError,
  showButton,
  destinations,
  isRouteLoading,
  routeError,
  onStart,
  onPauseResume,
  onPauseClick,
  isLoading = false,
  disabled = false,
  setButtonState,
  setIsPaused,
  setButtonError
}) => {
  const handleShowEventLog = () => {
    try {
      const interactionManager = ComponentInteractionManager.getInstance();
      interactionManager.printSequenceSummary();
      console.log('📊 Displaying animation event sequence summary');
    } catch (error) {
      console.error('Error showing event log:', error);
    }
  };

  return (
    <div className="animation-controls">
      {/* Journey Begin Button */}
      <JourneyButton
        buttonState={buttonState}
        buttonError={buttonError}
        isAnimating={isAnimating}
        isPaused={isPaused}
        onStart={onStart}
        showButton={showButton && !isAnimating}
      />
      
      {/* Pause/Resume Button - Only show during animation */}
      {isAnimating && (
        <JourneyButton
          buttonState="ready"
          buttonError={null}
          isAnimating={isAnimating}
          isPaused={isPaused}
          onStart={() => {}} // Not used for this button
          onPauseResume={onPauseResume}
          showButton={isAnimating}
        />
      )}
      
      {/* Progress bar - Only show during animation - REMOVED */}
      {/* {isAnimating && <ProgressBar progress={progress} />} */}

      {/* Journey progress indicator - Only show during animation - REMOVED */}
      {/* {isAnimating && (
        <JourneyProgressIndicator 
          destinations={destinations}
          progress={progress}
          isRouteLoading={isRouteLoading}
          routeError={routeError}
        />
      )} */}

      <button 
        className={`animation-button ${isPaused ? 'play' : 'pause'}`}
        onClick={onPauseClick}
        disabled={disabled || isLoading}
      >
        {isLoading ? 'Loading...' : isPaused ? 'Resume Journey' : 'Pause Journey'}
      </button>

    </div>
  );
};

export default AnimationControls;