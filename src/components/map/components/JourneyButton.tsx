import React from 'react';
import { ButtonState } from '@/types/TravelAnimatorTypes';
import type { Map as MapboxMap } from 'mapbox-gl';

interface JourneyButtonProps {
  map?: MapboxMap | null;
  buttonState: ButtonState;
  buttonError: string | null;
  isAnimating: boolean;
  isPaused: boolean;
  onStart: () => void;
  onPauseResume?: () => void;
  showButton: boolean;
  canBeginJourney: boolean;
}

/**
 * Component for both the "Begin Journey" and "Pause/Resume" buttons
 * This is the single source of truth for journey buttons
 */
export const JourneyButton: React.FC<JourneyButtonProps> = ({
  map,
  buttonState,
  buttonError,
  isAnimating,
  isPaused,
  onStart,
  onPauseResume,
  showButton,
  canBeginJourney,
}) => {
  // Debug logging disabled for production readiness
  // console.log(`[JourneyButton Render] Props: isAnimating: ${isAnimating}, buttonState: ${buttonState}, canBeginJourney: ${canBeginJourney}, showButton: ${showButton}`);

  if (!showButton) {
    return null;
  }

  const getButtonContent = () => {
    switch (buttonState) {
      case 'loading':
        return (
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            <span>Starting Journey...</span>
          </div>
        );
      case 'error':
        return (
          <div className="flex items-center space-x-2">
            <span className="text-red-200">Error: {buttonError}</span>
          </div>
        );
      case 'disabled':
        return (
          <div className="flex items-center space-x-2">
            <span>Select at least 2 cities to begin</span>
          </div>
        );
      default:
        return <span>Begin Journey</span>;
    }
  };

  const getButtonClasses = () => {
    const baseClasses = 'fixed bottom-20 left-1/2 transform -translate-x-1/2 z-50 px-6 py-3 rounded-full shadow-lg transition-all duration-200';
    
    if (buttonState === 'disabled' || !canBeginJourney) {
      return `${baseClasses} bg-gray-400 cursor-not-allowed opacity-50`;
    }
    
    if (buttonState === 'error') {
      return `${baseClasses} bg-red-600 hover:bg-red-700`;
    }
    
    return `${baseClasses} bg-blue-600 hover:bg-blue-700 text-white`;
  };

  return (
    <div className="fixed bottom-20 left-1/2 transform -translate-x-1/2 z-50">
      {isAnimating && onPauseResume ? (
        <button
          data-testid="pause-resume-button"
          onClick={onPauseResume}
          className="bg-blue-600 text-white px-6 py-3 rounded-full shadow-lg hover:bg-blue-700 transition-all duration-200"
        >
          {isPaused ? 'Resume Journey' : 'Pause Journey'}
        </button>
      ) : (
        <button
          onClick={() => {
            console.log("*** JourneyButton RAW CLICK DETECTED ***");
            console.log(`[JourneyButton onClick] 'Begin Journey' button clicked. Props at click: isAnimating: ${isAnimating}, buttonState: ${buttonState}, canBeginJourney: ${canBeginJourney}`);
            onStart();
          }}
          disabled={buttonState === 'disabled' || !canBeginJourney}
          className={getButtonClasses()}
        >
          {getButtonContent()}
        </button>
      )}
    </div>
  );
};

export default JourneyButton; 