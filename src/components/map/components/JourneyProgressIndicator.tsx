import React from 'react';
import { Destination } from '@/types/destination';

interface JourneyProgressIndicatorProps {
  destinations: Destination[];
  progress: number;
  isRouteLoading: boolean;
  routeError: string | null;
}

const JourneyProgressIndicator: React.FC<JourneyProgressIndicatorProps> = ({
  destinations,
  progress,
  isRouteLoading,
  routeError,
}) => {
  if (!destinations || destinations.length === 0) return null;

  return (
    <div className="journey-progress-indicator">
      {isRouteLoading && (
        <div className="flex items-center justify-center p-3">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500 mr-2"></div>
          <span>Loading route data...</span>
        </div>
      )}
      
      {!isRouteLoading && routeError && (
        <div className="text-center p-3">
          <span className="text-red-500">{routeError}</span>
          <p className="text-xs text-gray-500 mt-1">Please try selecting different destinations</p>
        </div>
      )}
      
      {!isRouteLoading && !routeError && (
        <>
          <div className="city-indicators">
            {destinations.map((destination, index) => {
              const isVisited = progress >= ((index / (destinations.length - 1)) * 100);
              const isCurrent = index > 0 && 
                               progress >= ((index - 1) / (destinations.length - 1) * 100) && 
                               progress < (index / (destinations.length - 1) * 100);
              
              return (
                <div 
                  key={destination.name}
                  className="city-indicator"
                  title={`${destination.name} ${isVisited ? '(Visited)' : isCurrent ? '(Current)' : '(Upcoming)'}`}
                >
                  <div className={`city-dot ${isVisited ? 'visited' : ''} ${isCurrent ? 'current' : ''}`}></div>
                  <div className="city-name">{destination.name}</div>
                </div>
              );
            })}
          </div>
          <div className="progress-line">
            <div className="progress-fill" style={{ width: `${progress}%` }}></div>
          </div>
        </>
      )}
    </div>
  );
};

export default JourneyProgressIndicator; 