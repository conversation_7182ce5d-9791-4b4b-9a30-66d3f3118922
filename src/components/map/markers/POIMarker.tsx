import React from 'react';
import { PointOfInterest } from '@/components/PointOfInterestCard';

interface POIMarkerProps {
  poi: PointOfInterest;
  isSelected?: boolean;
  onClick?: () => void;
}

const POIMarker: React.FC<POIMarkerProps> = ({
  poi,
  isSelected,
  onClick,
}) => {
  return (
    <div 
      className={`poi-marker ${isSelected ? 'selected' : ''}`}
      data-poi-type={poi.type.toLowerCase()}
      onClick={onClick}
    >
      <div className="poi-marker-dot"></div>
      {poi.name && (
        <div className="marker-tooltip">
          {poi.name}
        </div>
      )}
    </div>
  );
};

export default POIMarker; 