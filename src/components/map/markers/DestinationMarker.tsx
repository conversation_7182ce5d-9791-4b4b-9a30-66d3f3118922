import React from 'react';
import { Destination } from '@/data/destinations';
import { PointOfInterest } from '@/components/PointOfInterestCard';

interface DestinationMarkerProps {
  destination: Destination;
  index?: number;
  isSelected?: boolean;
  pois: PointOfInterest[];
  onClick?: () => void;
  onPoiClick?: () => void;
}

const DestinationMarker: React.FC<DestinationMarkerProps> = ({
  destination,
  index,
  isSelected,
  pois,
  onClick,
  onPoiClick,
}) => {
  const poiCount = pois.length;

  return (
    <div 
      className={`destination-marker ${isSelected ? 'selected' : ''}`}
      onClick={onClick}
    >
      <div className="destination-circle">
        {index !== undefined ? (
          <span className="order-number">{index + 1}</span>
        ) : (
          <span className="destination-dot"></span>
        )}
      </div>
      
      {poiCount > 0 && (
        <div 
          className="order-badge"
          onClick={(e) => {
            e.stopPropagation();
            onPoiClick?.();
          }}
        >
          {poiCount}
        </div>
      )}
      
      {destination.name && (
        <div className="marker-tooltip">
          {destination.name}
        </div>
      )}
    </div>
  );
};

export default DestinationMarker; 