import React from 'react';
import { PointOfInterest } from '@/components/PointOfInterestCard';
import { Landmark, Utensils, Hotel, Mountain, MapPin } from 'lucide-react';

interface EnhancedPOIMarkerProps {
  poi: PointOfInterest;
  isSelected?: boolean;
  onClick?: () => void;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
}

const EnhancedPOIMarker: React.FC<EnhancedPOIMarkerProps> = ({
  poi,
  isSelected,
  onClick,
  onMouseEnter,
  onMouseLeave
}) => {
  // Get the appropriate icon based on POI type
  const getIconForType = (type: string) => {
    switch (type.toLowerCase()) {
      case 'landmark':
      case 'monument':
        return <Landmark className="w-3 h-3" />;
      case 'restaurant':
        return <Utensils className="w-3 h-3" />;
      case 'accommodation':
        return <Hotel className="w-3 h-3" />;
      case 'nature':
        return <Mountain className="w-3 h-3" />;
      case 'activity':
      default:
        return <MapPin className="w-3 h-3" />;
    }
  };

  return (
    <div 
      className={`poi-marker ${isSelected ? 'selected' : ''} group`}
      data-poi-type={poi.type.toLowerCase()}
      onClick={onClick}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      <div className="poi-marker-icon flex items-center justify-center">
        {getIconForType(poi.type)}
      </div>
      <div className="marker-tooltip group-hover:opacity-100 group-hover:translate-y-0">
        <div className="font-medium">{poi.name}</div>
        <div className="text-xs opacity-75">{poi.type}</div>
      </div>
    </div>
  );
};

export default EnhancedPOIMarker;

// CSS to be added to global styles or component styles:
/*
.poi-marker {
  cursor: pointer;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: white;
  border: 2px solid currentColor;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  color: #E27D60;
  position: relative;
}

.poi-marker[data-poi-type="landmark"] { color: #3B82F6; }
.poi-marker[data-poi-type="monument"] { color: #8B5CF6; }
.poi-marker[data-poi-type="activity"] { color: #10B981; }
.poi-marker[data-poi-type="restaurant"] { color: #F59E0B; }
.poi-marker[data-poi-type="accommodation"] { color: #8B5CF6; }
.poi-marker[data-poi-type="nature"] { color: #059669; }

.poi-marker:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.poi-marker.selected {
  transform: scale(1.2);
  box-shadow: 0 0 0 4px rgba(255,255,255,0.5), 0 4px 8px rgba(0,0,0,0.3);
}

.marker-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(10px);
  background-color: white;
  color: #333;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  pointer-events: none;
  opacity: 0;
  transition: all 0.2s ease;
  margin-bottom: 8px;
  z-index: 10;
}

.marker-tooltip:after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -6px;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid white;
}
*/