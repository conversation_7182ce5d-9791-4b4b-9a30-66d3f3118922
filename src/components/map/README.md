# MapHelpers Consolidation Guide

## Background
According to our animation-architecture-rules, we should "Keep animation logic separate from React components" and "Create specialized utility files for animation calculations." We previously had two files with overlapping functionality:

- `MapHelpers.ts`: Core animation utilities with type definitions
- `MapHelpers.tsx`: React-specific utilities and duplicated functionality

This split approach violated our architecture rules and caused import issues.

## Consolidation Plan
We have consolidated all map utility functions into a single source of truth:

```
src/components/map/utils/MapHelpers.ts
```

The existing `MapHelpers.tsx` now serves as a transitional file that simply re-exports all functionality from `MapHelpers.ts`. It will be removed in a future update.

## Action Required
If your code imports from `MapHelpers.tsx`, please update it to import from `MapHelpers.ts` instead:

```typescript
// BEFORE
import { someFunction } from '../utils/MapHelpers.tsx';

// AFTER
import { someFunction } from '../utils/MapHelpers';
```

## New File Organization

The consolidated `MapHelpers.ts` file follows a clear organization pattern:

1. **Constants and Configuration**: All map constants, zoom levels, camera settings
2. **Core Animation Utilities**: Position calculation, bearing, terrain detection
3. **Camera Control**: Functions for implementing camera behaviors
4. **Utility Functions**: Helper methods for route preprocessing and map styling

## Benefits

- Single source of truth for map utilities
- Proper separation of concerns (following animation-architecture-rules)
- Elimination of import errors and duplicated code
- Better maintainability and testability
- Consistent naming conventions

## Timeline

This is a two-phase approach:
1. **Current Phase**: Everything consolidated in `MapHelpers.ts` with backwards compatibility
2. **Future Update**: Remove `MapHelpers.tsx` completely and update all imports

## Common Functions

Here are commonly used functions that are now available from `MapHelpers.ts`:

- `calculateBearing`: Calculates bearing between two points
- `determineTerrainType`: Gets terrain type for a geographic position
- `implementContextualRhythm`: Handles camera transitions during journey
- `preprocessRouteForAnimation`: Prepares route points for animation
- `removeCountryBordersAndLabels`: Controls map border visibility

Constants like `CITY_APPROACH_ZOOM`, `POI_DETECTION_RADIUS`, and `CAMERA_SETTINGS` are also available.

# Map Components

## Module Organization

This directory contains all map-related components and utilities for the Morocco Travel app.

### Import Standardization Guidelines

- **Primary Utility Locations**:
  - Map instance management: `src/components/map/utils/MapInstance.ts`
  - Vehicle management: `src/components/map/animation/VehicleManager.ts`
  - Animation control: `src/components/map/animation/AnimationManager.ts`

- **Import Style Guidelines**:
  - For imports within the same directory, use relative paths: `import { X } from './X'`
  - For imports from child directories, use relative paths: `import { X } from './subdirectory/X'`
  - For imports from parent or sibling directories, use relative paths with `../`: `import { X } from '../sibling/X'`
  - For external components like UI components, use absolute paths: `import { Button } from '@/components/ui/button'`

- **Deprecated Imports**:
  - `src/components/map/animation/utils/MapInstance.ts` is a redirector to the primary location
  - It will warn about deprecation but continue to work for backwards compatibility

### Directory Structure

- `animation/`: Animation-related modules
- `utils/`: Shared utility functions
- `markers/`: Map marker components
- `overlays/`: UI overlays for the map
- `controls/`: User interface controls for the map
- `camera/`: Camera control and animation
- `layers/`: Map rendering layers
- `vehicle/`: Vehicle marker and styling
- `clusters/`: Marker clustering utilities

### Singleton Patterns

The following modules use the singleton pattern for global access:

- **MapInstance**: `getMapInstance()` to access the global map
- **VehicleManager**: `VehicleManager.getInstance()` for vehicle control
- **AnimationManager**: `AnimationManager.getInstance()` for animation state

### Dynamic Imports

To avoid circular dependencies, some modules use dynamic imports. For example:

```typescript
// Set the global map instance for use in other components
if (isLoaded && map) {
  import('./utils/MapInstance').then(({ setMapInstance }) => {
    setMapInstance(map);
  });
}
```

Use dynamic imports when:
1. You need to break a circular dependency
2. The imported functionality isn't needed immediately
3. The import would cause a race condition with initialization 

# Morocco Travel App Map Module

This document outlines the organization of map-related components, utilities, and their import paths.

## Primary Utility Locations

- **Map Instance Management**: `src/components/map/utils/MapInstance.ts`
- **Vehicle Management**: `src/components/map/animation/VehicleManager.ts`
- **Animation Control**: `src/components/map/animation/AnimationManager.ts`

## Import Style Guidelines

For standardization, please use the following import patterns:

### For imports within the same directory:
```typescript
import { SomeComponent } from './SomeComponent';
```

### For imports from child directories:
```typescript
import { SomeUtility } from './utils/SomeUtility';
```

### For imports from parent or sibling directories:
```typescript
import { MapFeature } from '../MapFeature';
import { SomeComponent } from '../../SomeComponent';
```

### For external component imports:
```typescript
import { Button } from '@/components/ui/button';
```

## Deprecated Imports

The utility at `src/components/map/animation/utils/MapInstance.ts` is a redirector to the primary location and will continue to work for backwards compatibility. However, new code should use the standard path.

## Directory Structure

- **animation/**: Animation-related components and utilities
- **utils/**: Core map utilities like MapInstance
- **markers/**: Map marker management
- **overlays/**: UI overlays for the map
- **controls/**: UI controls for interaction
- **camera/**: Camera movement and positioning
- **layers/**: Map styling layers
- **vehicle/**: Vehicle marker and movement
- **clusters/**: Point clustering utilities

## Dynamic Imports

To avoid circular dependencies, some modules use dynamic imports. For example:

```typescript
// Set the global map instance for use in other components
if (isLoaded && map) {
  import('./utils/MapInstance').then(({ setMapInstance }) => {
    setMapInstance(map);
  });
}
```

Use dynamic imports when:
1. You need to break a circular dependency
2. The imported functionality isn't needed immediately
3. The import would cause a race condition with initialization 

## Import Path Troubleshooting

### Common Import Path Issues

If you're seeing errors like:

```
Failed to resolve import "../utils/PerformanceUtils" from "src/components/map/TravelAnimator.tsx"
```

This typically indicates an incorrect import path. Remember:

- When importing from the `utils` directory in a component that's directly in the `map` directory (like `TravelAnimator.tsx`), use `./utils/...` not `../utils/...`
- When importing from a sibling directory, use `../directoryName/...`

### Performance Utilities

The `PerformanceUtils.ts` module in `src/components/map/utils/` provides essential performance optimization functions:

- `debounce`: Delays function execution until after a specified wait period
- `throttle`: Limits function calls to a maximum frequency

Import these utilities with the correct path:

```typescript
// In TravelAnimator.tsx or other components in the map directory
import { debounce, throttle } from './utils/PerformanceUtils';

// In components within subdirectories (like animation/)
import { debounce, throttle } from '../utils/PerformanceUtils';
```

## Singleton Patterns

Several modules use the singleton pattern for global access:

- `MapInstance`: Singleton for map instance access
- `VehicleManager`: Singleton for vehicle marker management
- `AnimationManager`: Singleton for animation control

Several modules use the singleton pattern for global access:

- `MapInstance`: `getMapInstance()` to access the global map
- `VehicleManager`: `VehicleManager.getInstance()` for vehicle control
- `AnimationManager`: `AnimationManager.getInstance()` for animation state 