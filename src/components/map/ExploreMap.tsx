/**
 * ExploreMap.tsx
 * 
 * Main Map Component for the Morocco Travel Experience
 * --------------------------------------------------
 * 
 * RESPONSIBILITY:
 * Serves as the central React component for the interactive map experience. 
 * This component orchestrates the map rendering, marker management, journey 
 * planning, POI discovery, and animations to create an engaging user experience.
 * 
 * KEY FUNCTIONALITY:
 * - Initializes and configures the Mapbox GL map
 * - Manages destination and POI markers on the map
 * - Handles user interactions like clicking on destinations and POIs
 * - Controls the visibility of UI panels and overlays
 * - Coordinates with TravelAnimator for journey simulations
 * - Manages the journey planning workflow and state transitions
 * - <PERSON>les filtering and categorization of POIs
 * - Provides UI controls for map interactions
 * - Integrates with animation subsystems for journey visualization
 * 
 * COMMUNICATION PATTERNS:
 * - Uses React props for parent-child communication
 * - Leverages custom events for cross-component messaging
 * - Employs useRef hooks for stable references across renders
 * - Utilizes setState for reactive UI updates
 * - Implements centralized map access via MapInstance utility
 * 
 * INTEGRATION POINTS:
 * - TravelAnimator: Handles animation sequence for journeys
 * - MarkerManager: Manages markers for destinations and POIs
 * - RouteLayer: Renders the journey route on the map
 * - MapControls: Provides UI controls for map interactions
 * - NotificationPanel: Displays POI discoveries during journey
 * - CityTagsOverlay: Shows city names on the map
 * - VehicleManager: Controls the vehicle marker during animations
 * - AnimationIntegration: Initiates and coordinates animations
 *
 * PROPS:
 * - citiesForCountry: Array of all available cities in the country
 * - poisForCurrentSelection: Points of interest to display on the map
 * - selectedDestinations: Array of destinations in the current itinerary
 * - selectedPOIs: POIs selected by the user
 * - onDestinationSelect: Callback when a destination is selected
 * - onPOISelect: Callback when a POI is selected
 * - onPOIDeselect: Callback when a POI is deselected
 * - mapCenter?: [number, number]
 * - mapZoom?: number
 * - onCitySelect?: (destination: Destination) => void
 * - selectedCity?: Destination | null
 * - isMobile?: boolean
 * - showLeftPanelCommand?: boolean
 * - onCloseLeftPanel?: () => void
 * - isDarkValue?: boolean
 * - isLeftPanelCollapsed?: boolean
 * - toggleLeftPanelCollapse?: () => void
 * - onReplaceDestinations?: (destinations: Destination[]) => void
 * - onMapReady?: (map: mapboxgl.Map) => void
 */

import React, { useEffect, useState, useRef, useCallback, forwardRef, useImperativeHandle, useMemo } from 'react';
import mapboxgl from 'mapbox-gl';
import { useMapbox } from '@/hooks/use-mapbox';
import { positionToGeoJSON, positionToCoordinates } from '@/utils/positionUtils';
import RouteLayer from './layers/RouteLayer';
import MapControls from './controls/MapControls';
import RightSidebar from './RightSidebar';
import { douroValleyPOIs } from '@/data/douroValleyPOIs';
import CityTagsOverlay from './overlays/CityTagsOverlay';
import MapStyles from './MapStyles';
import { MapFeatureOptions } from './MapComponent';
import DirectDestinationMarkers from './DirectDestinationMarkers';
import DirectPOIMarkers from './DirectPOIMarkers';
// REFACTORED: TravelAnimator replaced with CoreAnimationEngine
import * as turf from '@turf/turf';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ensureContinueJourneyButton } from './JourneyUtils';
import { clearJourneyState } from './utils/JourneyStateManager';
import CinematicController from './animation/CinematicController';
import './animation/CinematicControllerFactory';
import { NotificationPanel } from './notifications/NotificationPanel';
import VehicleManager from './animation/VehicleManager';
import { shouldUseReactButton } from './animation/UIComponentManager';
import { useCoreAnimation, convertToCoordinates, validateCoordinates } from '@/hooks/useCoreAnimation';
import type { Coordinate } from './animation/CoreAnimationEngine';
import { InteractionFix } from './animation/fixes/InteractionFix';
import 'mapbox-gl/dist/mapbox-gl.css';
import '@/styles/core/markers-unified.css';
import { filterPOIsForCity, convertPOIToPointOfInterest } from '@/utils/poiUtils';
import { Destination, PointOfInterest, normalizePOI, normalizeDestination, isValidCoordinates } from '../../types';
import { Position, PositionTuple, PositionObject, toPositionTuple } from '@/types/Position';
import { AnimationEventType } from '@/types/AnimationEventTypes';
import AnimationLogger from '@/utils/animationLogger';
import JourneyButton from './components/JourneyButton';
import CountdownLoadingIndicator from '../ui/CountdownLoadingIndicator';
// REFACTORED: TravelAnimatorHandles no longer needed
import { type RouteData } from './animation/AnimationManagerWrapper';
import AnimationDebugTools from './animation/AnimationDebugTools';
import { EnhancedCameraBehavior } from './utils/EnhancedCameraBehavior';
import DiscoveryManager from '../discovery/DiscoveryManager';
// import RouteMetrics from './RouteMetrics'; // Commented out
// import { MapProvider } from '../../context/MapContext'; // Commented out

// Add type declaration for the window object to include cinematicController
declare global {
  interface Window {
    cinematicController?: any;
    __globalClickLoggerAdded?: boolean;
  }
}

// Ensure we have a stable overlay container reference
let stableOverlayContainer: HTMLElement | null = null;

// Function to set the stable overlay container
const setStableOverlayContainer = (container: HTMLElement) => {
  stableOverlayContainer = container;
};

// Function to fix clickable elements
const fixClickableElements = () => {
  // Apply InteractionFix to ensure markers and buttons are interactive
  InteractionFix.getInstance().applyFixes();
};

// Button State Type
type ButtonState = 'default' | 'loading' | 'error' | 'disabled' | 'playing' | 'paused';

interface ExploreMapProps {
  citiesForCountry: Destination[];
  poisForCurrentSelection: PointOfInterest[];
  selectedDestinations: Destination[];
  selectedPOIs: PointOfInterest[];
  onDestinationSelect: (destination: Destination) => void;
  onPOISelect: (poi: PointOfInterest) => void;
  onPOIDeselect: (poi: PointOfInterest) => void;
  mapCenter?: [number, number];
  mapZoom?: number;
  maxBounds?: [[number, number], [number, number]];
  onCitySelect?: (destination: Destination | null) => void;
  selectedCity?: Destination | null;
  isMobile?: boolean;
  showLeftPanelCommand?: boolean;
  onCloseLeftPanel?: () => void;
  isDarkValue?: boolean;
  isLeftPanelCollapsed?: boolean;
  toggleLeftPanelCollapse?: () => void;
  onReplaceDestinations?: (destinations: Destination[]) => void;
  onMapReady?: (map: mapboxgl.Map) => void;
  onAnimationStart?: () => void;
  onAnimationComplete?: () => void;
}

// Define the ExploreMap ref interface
export interface ExploreMapHandles {
  beginJourney: () => void;
  flyTo: (center: mapboxgl.LngLatLike, zoom: number, options?: mapboxgl.CameraOptions & mapboxgl.AnimationOptions) => void;
}

// Convert ExploreMap to use React.forwardRef
const ExploreMapComponent = forwardRef<ExploreMapHandles, ExploreMapProps>(({
  citiesForCountry,
  poisForCurrentSelection,
  selectedDestinations,
  selectedPOIs,
  onDestinationSelect,
  onPOISelect,
  onPOIDeselect,
  mapCenter,
  mapZoom,
  maxBounds,
  onCitySelect,
  selectedCity,
  isMobile,
  isDarkValue,
  onReplaceDestinations,
  onMapReady,
  onAnimationStart,
  onAnimationComplete
}, ref) => {
  // State organization
  const [map, setMap] = useState<mapboxgl.Map | null>(null);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [filteredCities, setFilteredCities] = useState<Destination[]>([]);
  const [activeFilters, setActiveFilters] = useState<any>({}); // Consider typing this

  // Use a ref to store the stable map instance from useMapbox
  const stableMapRef = useRef<mapboxgl.Map | null>(null);

  const finalIsDark = typeof isDarkValue === 'boolean' ? isDarkValue : false; // Ensure boolean

  // Map and basic UI state
  const [selectedDestination, setSelectedDestination] = useState<Destination | null>(null); 
  const [mapReady, setMapReady] = useState(false);
  const [mapOptions, setMapOptions] = useState<MapFeatureOptions>({
    weatherIndicators: false,
    routeLabels: true,
    movingVehicle: false,
    terrainView: true,
    poiSlideshow: false,
  });

  // Animation state
  const [countdownVisible, setCountdownVisible] = useState(false);
  const [countdownComplete, setCountdownComplete] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [pendingJourneyStart, setPendingJourneyStart] = useState(false);
  const [journeyStarted, setJourneyStarted] = useState(false);

  // POI and city state
  const [currentPOIPopup, setCurrentPOIPopup] = useState<PointOfInterest | null>(null);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedPOICategories, setSelectedPOICategories] = useState<string[]>([]);

  // Memoize the POIs passed to DirectPOIMarkers
  const directPOIMarkersPois = useMemo(() => {
    return poisForCurrentSelection.filter(poi => poi.coordinates && isValidCoordinates(poi.coordinates));
  }, [poisForCurrentSelection]);

  const directPOIMarkersSelectedPois = useMemo(() => {
    return selectedPOIs.filter(poi => poi.coordinates && isValidCoordinates(poi.coordinates));
  }, [selectedPOIs]);

  // Panels and UI visibility
  const [showRightPanel, setShowRightPanel] = useState<boolean>(true);
  const [showInterestsPanel, setShowInterestsPanel] = useState<boolean>(false);
  const [showNotificationPanel, setShowNotificationPanel] = useState<boolean>(false);
  const [userInterests, setUserInterests] = useState<string[]>([]);

  // Add state for notification panel visibility
  const [currentArea, setCurrentArea] = useState<string | null>(null);
  
  const mapContainer = useRef<HTMLDivElement>(null);

  // Initialize map - use containerId that matches what we expect
  // Rename map from useMapbox to avoid conflict with component's map state
  const { map: mapboxMapFromHook, isLoaded } = useMapbox('explore-map-container', mapCenter, mapZoom, maxBounds);

  // Derive a display-only route from selectedDestinations, not to be confused with the state variable 'selectedRoute'
  const derivedRouteForDisplay: Position[] = useMemo(() => {
    return selectedDestinations.map(dest => dest.position).filter(pos => pos !== undefined) as Position[];
  }, [selectedDestinations]);

  // Available interests for users to select - place after categories
  const availableCategories = ['monument', 'landmark', 'nature', 'activity'];
  const availableInterests = [
    'historical', 'cultural', 'outdoor', 'nature', 
    'food', 'adventure', 'relaxation', 'family', 'shopping'
  ];
  
  // Toggle a category in the filter
  const toggleCategory = useCallback((category: string) => {
    setSelectedPOICategories(prev => {
      if (prev.includes(category)) {
        return prev.filter(c => c !== category);
      } else {
        return [...prev, category];
      }
    });
  }, []);

  // Add toggle function for interests - place near toggleCategory
  const toggleInterest = (interest: string) => {
    setUserInterests(prev => 
      prev.includes(interest) 
        ? prev.filter(i => i !== interest) 
        : [...prev, interest]
    );
  };

  // Add category change handler
  const handleCategoryChange = useCallback((categories: string[]) => {
    setSelectedPOICategories(categories);
  }, []);

  // Map and state refs
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const mapRef = useRef<mapboxgl.Map | null>(null);
  const destinationMarkersRef = useRef<{ [key: string]: mapboxgl.Marker }>({});
  const poiMarkersRef = useRef<{ [key: string]: mapboxgl.Marker }>({});

  // State - MOVED UP to be available for routeForAnimatorProp
  const [selectedRoute, setSelectedRoute] = useState<PositionTuple[] | null>(null);

  // Route data for TravelAnimator - NOW PROPERLY PLACED AFTER selectedRoute DECLARATION
  const routeForAnimatorProp: RouteData = useMemo(() => {
    // FIXED: Use selectedRoute (detailed driving route) instead of derivedRouteForDisplay
    if (selectedRoute && selectedRoute.length >= 2) {
      // selectedRoute is already in the correct format (PositionTuple[])
      return selectedRoute;
    }

    // Fallback to empty array if no route available
    // TravelAnimator's internal useEffect checks for length < 2.
    return [];
  }, [selectedRoute]);

  // At the top of the component, normalize all destinations and POIs
  const normalizedDestinations = useMemo(() => citiesForCountry.map(city => {
    try {
      return normalizeDestination(city);
    } catch (error) {
      console.error('Failed to normalize destination:', city, error);
      return null; // Or some default/error state for the destination
    }
  }).filter(Boolean) as Destination[], [citiesForCountry]);

  const normalizedPOIs = useMemo(() => poisForCurrentSelection.map(poi => {
    try {
      return normalizePOI(poi);
    } catch (error) {
      console.error('Failed to normalize POI in ExploreMap:', poi.id || poi.name, error);
      return null; // Skip this POI if normalization fails
    }
  }).filter(Boolean) as PointOfInterest[], [poisForCurrentSelection]);

  // Memoize props for TravelAnimator
  const travelAnimatorDests = useMemo(() => selectedDestinations.map(normalizeDestination), [selectedDestinations]);
  const travelAnimatorAllDests = useMemo(() => citiesForCountry.map(normalizeDestination), [citiesForCountry]);
  const travelAnimatorPois = useMemo(() => poisForCurrentSelection.map(normalizePOI), [poisForCurrentSelection]);

  // Debug log for POI normalization
  useEffect(() => {
    console.debug('[ExploreMap] Normalized POIs:', normalizedPOIs);
  }, [normalizedPOIs]);

  // Add a global click logger (only once)
  if (typeof window !== 'undefined' && !window.__globalClickLoggerAdded) {
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      let desc = '';
      if (target) {
        let classNameString = '';
        if (typeof target.className === 'string') {
          classNameString = target.className;
        } else if (target.className && typeof target.className === 'object' && 'baseVal' in target.className) {
          // Handling for SVGAnimatedString
          classNameString = (target.className as SVGAnimatedString).baseVal;
        }

        desc = `[${target.tagName}${target.id ? `#${target.id}` : ''}${classNameString ? `.${classNameString.split(' ').join('.')}` : ''}]`;
        if (target.getAttribute('data-testid')) {
          desc += ` [data-testid=${target.getAttribute('data-testid')}]`;
        }
        if (target.textContent && target.textContent.trim().length > 0) {
          desc += ` text="${target.textContent.trim().slice(0, 40)}"`;
        }
      }
      console.log(`[GLOBAL CLICK] User clicked: ${desc}`, target);
    });
    window.__globalClickLoggerAdded = true;
  }

  // --- BUTTON ENABLEMENT ---
  const canBeginJourney: boolean = useMemo(() => {
    return Boolean(selectedDestinations.length >= 2 && selectedRoute && selectedRoute.length > 0);
  }, [selectedDestinations, selectedRoute]);

  // REFACTORED: Use new core animation system instead of TravelAnimator
  const coreAnimation = useCoreAnimation(mapboxMapFromHook);

  // Run once when component mounts or when map becomes available
  useEffect(() => {
    if (isLoaded && mapboxMapFromHook) {
      if (!stableMapRef.current) {
        console.log('[ExploreMap] Map is loaded and mapboxMapFromHook is available. Setting stableMapRef and map state.');
        stableMapRef.current = mapboxMapFromHook;
        setMap(mapboxMapFromHook);
        setMapReady(true);

        // 🎬 ENHANCED: Set up region context for dynamic cinematics
        const cinematicController = CinematicController.getInstance();
        if (cinematicController && mapCenter && maxBounds) {
          cinematicController.setRegionContext(mapCenter, maxBounds);
          console.log('[ExploreMap] 🎬 Region context set for cinematics:', { center: mapCenter, bounds: maxBounds });
        }

        if (onMapReady) {
          onMapReady(mapboxMapFromHook);
        }
      } else if (stableMapRef.current !== mapboxMapFromHook) {
        console.warn('[ExploreMap] mapboxMapFromHook has changed. Updating stableMapRef and map state. This could be problematic.');
        stableMapRef.current = mapboxMapFromHook;
        setMap(mapboxMapFromHook);

        // 🎬 ENHANCED: Update region context when map changes
        const cinematicController = CinematicController.getInstance();
        if (cinematicController && mapCenter && maxBounds) {
          cinematicController.setRegionContext(mapCenter, maxBounds);
          console.log('[ExploreMap] 🎬 Region context updated for cinematics:', { center: mapCenter, bounds: maxBounds });
        }

        if (onMapReady) {
          onMapReady(mapboxMapFromHook);
        }
      }
    } else if (!isLoaded && stableMapRef.current) {
      console.warn('[ExploreMap] Map was ready, but isLoaded is now false. Resetting mapReady state.');
      setMapReady(false);
    }
  }, [isLoaded, mapboxMapFromHook, onMapReady, mapCenter, maxBounds]);

  // Haversine formula to calculate distance between two coordinates
  const calculateDistance = (
    lon1: number, 
    lat1: number, 
    lon2: number, 
    lat2: number
  ): number => {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = (lat2 - lat1) * (Math.PI / 180);
    const dLon = (lon2 - lon1) * (Math.PI / 180);
    
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(lat1 * (Math.PI / 180)) * Math.cos(lat2 * (Math.PI / 180)) * 
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
      
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c; // Distance in kilometers
    
    return distance;
  };

  // Handle city selection and update map view
  const handleCitySelect = useCallback((cities: Destination[]) => {
    if (!mapboxMapFromHook) return;

    // For desktop single click, we expect only one city in the array, and we toggle it.
    const cityToToggle = cities.length === 1 ? cities[0] : null;

    if (onDestinationSelect && cityToToggle) {
      onDestinationSelect(cityToToggle); // This is HomePage.toggleDestination
    }

    // Update the single selectedCity state in HomePage (likely for panel filtering)
    // and fly the map to this city.
    if (onCitySelect) { 
      onCitySelect(cityToToggle); 
    }
    
    if (cityToToggle && cityToToggle.coordinates && isValidCoordinates(cityToToggle.coordinates)) { 
      mapboxMapFromHook.flyTo({
        center: cityToToggle.coordinates as [number, number], 
        zoom: 12, 
        essential: true,
      });
    } else if (cityToToggle) {
      console.warn(`City ${cityToToggle.name} has no valid coordinates to fly to.`);
    }
  }, [mapboxMapFromHook, onDestinationSelect, onCitySelect]);

  // Define zoomToCoordinates before it's referenced in handlePOISelect
  // Function to zoom to a specific coordinate
  const zoomToCoordinates = useCallback((coordinates: [number, number]) => {
    if (mapboxMapFromHook && isValidCoordinates(coordinates)) {
      EnhancedCameraBehavior.getInstance().followVehicle(coordinates, 0, { forceUpdate: true });
    }
  }, [mapboxMapFromHook]);

  // Handle POI selection
  const handlePOISelect = useCallback((poi: PointOfInterest) => {
    // Check if this POI is already selected
    const alreadySelected = selectedPOIs.some(p => p.id === poi.id);
    
    if (alreadySelected) {
      // Deselect the POI by calling the parent component's callback
      onPOIDeselect(poi);
    } else {
      // Select the POI by calling the parent component's callback
      onPOISelect(poi);
    }
    
    // Note: updateBeginJourneyStatus is now called when selectedDestinations prop changes,
    // so direct call here might be redundant if POI selection doesn't change selectedDestinations directly.
    // However, keeping it if POI selection indirectly influences journey readiness via parent state.
  }, [onPOISelect, onPOIDeselect, selectedPOIs]);
  
  // Function to handle "Add to Journey" action in the left panel
  const handleAddPOIToJourney = useCallback((poi: PointOfInterest) => {
    // Call onPOISelect callback if provided and not already selected
    if (!selectedPOIs.some(p => p.id === poi.id) && onPOISelect) {
      onPOISelect(poi);
    }
  }, [onPOISelect, selectedPOIs]);
  
  // Function to handle "Remove from Journey" action
  const handleRemovePOIFromJourney = useCallback((poi: PointOfInterest) => {
    // Call onPOIDeselect callback if provided
    if (onPOIDeselect) {
      onPOIDeselect(poi);
    }
  }, [onPOIDeselect]);
  
  // Function to show POIs for a specific city
  const showCityPOIs = useCallback((city: Destination) => {
    const normalizedCity = city && normalizeDestinations([city])[0];
    const normalizedDestinationsForFilter = normalizeDestinations(citiesForCountry);
    
    // Make sure the left panel is visible to show the filtered POIs
    // setShowLeftPanel(true); // TO BE COMMENTED OUT
    // This function's purpose might need re-evaluation if it was solely to show a local left panel.
    // If it triggers a panel in HomePage, that logic would be different.
    // For now, commenting out the line that causes the error.
  }, [poisForCurrentSelection, citiesForCountry, filterPOIsForCity]); // Dependencies need review based on actual logic if kept

  // Memoized callbacks for TravelAnimator and JourneyButton
  const memoizedOnProgressUpdate = useCallback((progress: number) => {
    // Potentially add to a list of discovered POIs for this journey
    // Ensure any state updates here are stable or their setters are in useCallback dependencies
  }, []); // Add dependencies if any state is updated inside

  const memoizedOnPOIDiscovered = useCallback((poi: any) => {
    AnimationLogger.log('info', 'ui', 'ExploreMap: Animation Event', { eventData: poi });
  }, []); // Add dependencies if any state is updated inside

  const memoizedOnAnimationEvent = useCallback((eventData: any) => {
    AnimationLogger.log('info', 'ui', 'ExploreMap: Animation Event', { eventData });
  }, []); // Add dependencies if any state is updated inside

  // --- JOURNEY START HANDLER (FIXED TO USE ACTUAL DRIVING ROUTE) ---
  const handleBeginJourney = useCallback(async () => {
    AnimationLogger.log('info', 'animation', '[ExploreMap] handleBeginJourney called.');
    console.log('🚀 [ExploreMap] handleBeginJourney called - selectedRoute:', selectedRoute ? `${selectedRoute.length} points` : 'null');
    console.log('🚀 [ExploreMap] selectedDestinations:', selectedDestinations.length, selectedDestinations.map(d => d.name));

    // REFACTORED: Check core animation system instead of TravelAnimator
    if (!coreAnimation.isInitialized) {
      AnimationLogger.log('error', 'animation', '[ExploreMap] Core animation system not initialized.');
      console.log('❌ [ExploreMap] Core animation system not initialized');
      return;
    }

    if (selectedDestinations.length < 2) {
      AnimationLogger.log('warn', 'animation', 'Cannot start journey, less than 2 destinations selected.');
      console.log('❌ [ExploreMap] Cannot start journey - need at least 2 destinations, have:', selectedDestinations.length);
      return;
    }

    // FIXED: Use the actual driving route from RouteLayer instead of just destination positions
    if (!selectedRoute || selectedRoute.length < 2) {
      AnimationLogger.log('warn', 'animation', 'Cannot start journey, no valid driving route available. Route points:', selectedRoute?.length || 0);
      console.log('❌ [ExploreMap] Cannot start journey - no valid route. selectedRoute:', selectedRoute ? `${selectedRoute.length} points` : 'null');
      return;
    }

    // Use the detailed driving route for animation
    const routeDataForAnimation: RouteData = selectedRoute;

    AnimationLogger.log('info', 'animation', `[ExploreMap] Using driving route with ${routeDataForAnimation.length} points for animation.`);

    setCountdownVisible(true);
    setCountdownComplete(false);
    setPendingJourneyStart(true);
    setIsAnimating(false);

    await new Promise(resolve => setTimeout(resolve, 3000));

    if (!pendingJourneyStart) {
      AnimationLogger.log('info', 'animation', 'Journey start cancelled during countdown.');
      setCountdownVisible(false);
      return;
    }

    setCountdownVisible(false);
    setCountdownComplete(true);

    // Animation will be started by handleCountdownComplete after countdown finishes
    AnimationLogger.log('info', 'animation', 'Countdown started - animation will begin after countdown completes', { routeData: routeDataForAnimation });
  }, [selectedDestinations, selectedRoute, pendingJourneyStart, coreAnimation, setIsAnimating, setJourneyStarted, setCountdownVisible, setCountdownComplete, setShowNotificationPanel]);
  // --- END OF PASTED JOURNEY START HANDLER ---

  // Expose methods via ref
  useImperativeHandle(ref, () => ({
    beginJourney: () => {
      console.log('🚀 [EXPLORE-MAP] beginJourney called via ref');
      console.log('🚀 [EXPLORE-MAP] selectedDestinations:', selectedDestinations.length, selectedDestinations.map(d => d.name));
      console.log('🚀 [EXPLORE-MAP] selectedRoute:', selectedRoute ? `${selectedRoute.length} points` : 'null');
      handleBeginJourney();
    },
    flyTo: (center: mapboxgl.LngLatLike, zoom: number, options?: mapboxgl.CameraOptions & mapboxgl.AnimationOptions) => {
      if (mapboxMapFromHook) {
        let LngLatArray: [number, number];
        if (Array.isArray(center)) {
          LngLatArray = center as [number, number];
        } else if ('lng' in center && 'lat' in center) {
          LngLatArray = [center.lng, center.lat];
        } else if ('lon' in center && 'lat' in center) { // Handle {lon, lat} case as well
          LngLatArray = [center.lon, center.lat];
        } else {
          console.error('[ExploreMap.flyTo] Received invalid center format:', center);
          return; // Or fly to a default location
        }
        // `zoom` is passed to followVehicle but not directly used by it for zoom level,
        // it uses calculateTargetZoom. We pass it in context if needed, or EnhancedCameraBehavior is refactored.
        // For now, EnhancedCameraBehavior.followVehicle primarily uses its internal logic and context for zoom.
        EnhancedCameraBehavior.getInstance().followVehicle(LngLatArray, 0, { forceUpdate: true /*, zoomOverride: zoom */ });
      }
    }
  }));

  // console.log(`[ExploreMap Render] Key props: map: ${!!mapboxMapFromHook}, selectedRoute: ${!!selectedRoute}, isAnimating: ${isAnimating}, journeyStarted: ${journeyStarted}`);

  // In the component body, ensure map instance is memoized and DirectPOIMarkers props are memoized:
  const memoizedPois = React.useMemo(() => poisForCurrentSelection, [poisForCurrentSelection]);
  const memoizedSelectedPOIs = React.useMemo(() => selectedPOIs, [selectedPOIs]);
  const memoizedOnPOIClick = React.useCallback((poi: PointOfInterest) => {
    // Call the original onPOISelect (which is HomePage.togglePOI)
    onPOISelect(poi);
    // Additionally, if you need to fly to the POI:
    if (mapboxMapFromHook && poi.coordinates && isValidCoordinates(poi.coordinates)) {
      mapboxMapFromHook.flyTo({ center: poi.coordinates as [number, number], zoom: 15, essential: true });
    }
  }, [mapboxMapFromHook, onPOISelect]);

  // Debug logs for CityTagsOverlay rendering conditions
  // console.log('[ExploreMap] Rendering CityTagsOverlay? Map exists:', !!mapboxMapFromHook, 'Map ready:', mapReady);
  // console.log('[ExploreMap] citiesForCountry received:', citiesForCountry ? citiesForCountry.map(c => c.name) : 'undefined');

  const handlePauseResumeAnimation = useCallback(() => {
    // REFACTORED: Pause/resume functionality for core animation
    if (coreAnimation.animationState.isPlaying) {
      coreAnimation.pauseAnimation();
    } else if (coreAnimation.animationState.isPaused) {
      coreAnimation.resumeAnimation();
    }
  }, [coreAnimation]);

  // Handle route readiness
  // Find the existing handleRouteReady definition and add logging at the top:
  const handleRouteReady = useCallback((routeCoordinates: PositionTuple[]) => {
    console.log('🛣️ [ExploreMap] handleRouteReady called. New route:', routeCoordinates ? `${routeCoordinates.length} points` : 'null');
    setSelectedRoute(routeCoordinates);
    console.log('🛣️ [ExploreMap] setSelectedRoute called with:', routeCoordinates ? `${routeCoordinates.length} points` : 'null');
  }, [setSelectedRoute]);

  // Debug logging for selectedRoute changes
  useEffect(() => {
    console.log('🛣️ [ExploreMap] selectedRoute updated:', selectedRoute ? `${selectedRoute.length} points` : 'null');
  }, [selectedRoute]);

  // Debug logging disabled for production readiness
  // useEffect(() => {
  //   console.log('[ExploreMap] selectedDestinations updated:', selectedDestinations.map(d => d.name));
  // }, [selectedDestinations]);

  // Debug logging disabled for production readiness
  // console.log('[ExploreMap] canBeginJourney computed:', canBeginJourney, 'selectedDestinations.length:', selectedDestinations.length, 'selectedRoute length:', selectedRoute?.length);

  // Expose imperative handles
  // ... existing code ...

  // Debug logging disabled for production readiness
  // (() => {
  //   console.log('[ExploreMap] About to render JourneyButton. Props for canBeginJourney: selectedRoute:', selectedRoute, 'selectedDestinations:', selectedDestinations.map(d=>d.name));
  //   AnimationLogger.log('info', 'ui', '[ExploreMap] Rendering JourneyButton', {
  //     isAnimating,
  //     canBeginJourney,
  //     selectedDestinations,
  //     selectedRoute
  //   });
  //   return null;
  // })();

  // Debug logging disabled for production readiness
  // useEffect(() => {
  //   console.log('[ExploreMap] pendingJourneyStart changed:', canBeginJourney);
  // }, [canBeginJourney]);
  // useEffect(() => {
  //   console.log('[ExploreMap] countdownVisible changed:', countdownVisible);
  // }, [countdownVisible]);
  // useEffect(() => {
  //   console.log('[ExploreMap] countdownComplete changed:', countdownComplete);
  // }, [countdownComplete]);
  // useEffect(() => {
  //   console.log('[ExploreMap] isAnimating changed:', isAnimating);
  // }, [isAnimating]);

  // Only one canBeginJourney declaration after selectedRoute
  // onAnimationComplete should be a () => void handler
  const handleAnimationComplete = useCallback(() => {
    console.log('[ExploreMap] Animation complete.');
    setIsAnimating(false);
    setCountdownComplete(false);
    setJourneyStarted(false);
    setShowNotificationPanel(false);

    // Call parent's animation complete handler
    if (onAnimationComplete) {
      onAnimationComplete();
    }
  }, [onAnimationComplete]);

  // Set up animation callbacks after all dependencies are defined
  useEffect(() => {
    if (coreAnimation.isInitialized) {
      coreAnimation.setProgressCallback((progress: number, position: Coordinate, bearing: number) => {
        // Handle progress updates
        memoizedOnProgressUpdate(progress);
      });

      coreAnimation.setCompleteCallback(() => {
        console.log('🏁 [ExploreMap] Animation completed via core system');
        handleAnimationComplete();
      });

      coreAnimation.setErrorCallback((error: Error) => {
        console.error('❌ [ExploreMap] Animation error via core system:', error);
        setIsAnimating(false);
      });
    }
  }, [coreAnimation.isInitialized, memoizedOnProgressUpdate, handleAnimationComplete, setIsAnimating]);

  // Initialize POIs for discovery when available
  useEffect(() => {
    if (coreAnimation.isInitialized && normalizedPOIs.length > 0) {
      console.log(`🔄 [ExploreMap] Setting ${normalizedPOIs.length} POIs for discovery...`);
      console.log('🔍 [ExploreMap] Sample POI structure:', normalizedPOIs[0]);
      coreAnimation.setPOIs(normalizedPOIs);
      console.log(`✅ [ExploreMap] POIs set for discovery`);
    }
  }, [coreAnimation.isInitialized, normalizedPOIs]);

  // Debug: Listen for automatic-pause events
  useEffect(() => {
    const handleAutomaticPause = (event: CustomEvent) => {
      console.log('🎯 [ExploreMap] Automatic pause event received:', event.detail);
    };

    window.addEventListener('automatic-pause', handleAutomaticPause as EventListener);

    return () => {
      window.removeEventListener('automatic-pause', handleAutomaticPause as EventListener);
    };
  }, []);

  // POI Discovery is handled automatically by POIDiscoveryManager
  // which emits 'automatic-pause' events that DiscoveryManager listens to

  // Define handleCountdownComplete for the CountdownLoadingIndicator
  const handleCountdownComplete = useCallback(async () => {
    console.log('[ExploreMap] Countdown complete. Re-validating route before setting isAnimating.');
    console.log('[ExploreMap] Route validation - selectedRoute:', selectedRoute ? `${selectedRoute.length} points` : 'null');
    console.log('[ExploreMap] Route validation - map:', map ? 'available' : 'null');

    setCountdownVisible(false);
    setCountdownComplete(true);

    // FIXED: Use selectedRoute (detailed driving route) instead of derivedRouteForDisplay (simple route)
    if (selectedRoute && selectedRoute.length >= 2) {
      console.log('[ExploreMap] ✅ Route validation PASSED. Starting animation...');

      // Actually start the animation via TravelAnimator ref
      // REFACTORED: Start animation via core animation system
      console.log('[ExploreMap] ✅ Starting animation with core animation system...');
      try {
        // Convert route to coordinate format
        const coordinates = convertToCoordinates(selectedRoute);

        // Validate coordinates
        if (!validateCoordinates(coordinates)) {
          console.error('[ExploreMap] ❌ Invalid coordinates in route');
          setIsAnimating(false);
          return;
        }

        // Start animation
        const started = await coreAnimation.startAnimation(coordinates, 30000);

        if (started) {
          console.log('[ExploreMap] ✅ Animation started successfully with core system!');
          setIsAnimating(true);
          setJourneyStarted(true);

          // Notify parent component that animation has started
          if (onAnimationStart) {
            onAnimationStart();
          }

          AnimationLogger.log("info", "animation", "Journey animation started with core system.", { routePoints: selectedRoute.length });
        } else {
          console.warn('[ExploreMap] ❌ Core animation failed to start.');
          setIsAnimating(false);
        }
      } catch (error) {
        console.error('[ExploreMap] ❌ Core animation error:', error);
        setIsAnimating(false);
      }

      // Fit map bounds to route
      if (map) {
        const coordinates = selectedRoute; // selectedRoute is already PositionTuple[]
        if (coordinates.length >= 2) {
          const bounds = coordinates.reduce((bounds, coord) => {
            return bounds.extend(coord);
          }, new mapboxgl.LngLatBounds(coordinates[0], coordinates[0]));
          map.fitBounds(bounds, {
            padding: { top: 100, bottom: 100, left: 100, right: 100 },
            duration: 1500,
            essential: true,
          });
        }
      }
    } else {
      console.warn('[ExploreMap] ❌ Route validation FAILED. Aborting animation start.', {
        selectedRouteExists: !!selectedRoute,
        selectedRouteLength: selectedRoute?.length || 0,
        mapExists: !!map
      });
      AnimationLogger.log("error", "animation", "Route became invalid during countdown. Animation aborted.", { selectedRouteLength: selectedRoute?.length || 0 });
      setIsAnimating(false); // Ensure it's false if route is invalid
      // Optionally, notify the user here
    }
  }, [map, selectedRoute]);



  // Add resize handler for when container size changes
  useEffect(() => {
    if (map && mapContainer.current) {
      // Use a timeout to ensure the container has finished resizing
      const resizeTimeout = setTimeout(() => {
        try {
          console.log('[ExploreMap] Triggering map resize due to container change');
          if (map && !map._removed && map.getCanvas()) {
            map.resize();
          }

          // Also trigger a bounds fit if we have bounds
          if (maxBounds && Array.isArray(maxBounds) && maxBounds.length === 2) {
            const bounds = new mapboxgl.LngLatBounds(maxBounds[0], maxBounds[1]);
            map.fitBounds(bounds, {
              padding: { top: 50, bottom: 50, left: 50, right: 50 },
              duration: 1000
            });
          }
        } catch (error) {
          console.error('[ExploreMap] Error during map resize:', error);
        }
      }, 100);

      return () => clearTimeout(resizeTimeout);
    }
  }, [map, maxBounds]);

  // Add ResizeObserver to watch for container size changes
  useEffect(() => {
    if (!mapContainer.current || !map) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;

        // Only resize if dimensions are meaningful
        if (width > 0 && height > 0) {
          console.log('[ExploreMap] Container size changed:', { width, height });

          // Debounce the resize call
          setTimeout(() => {
            if (map && !map._removed && map.getCanvas()) {
              try {
                map.resize();
              } catch (error) {
                console.warn('[ExploreMap] Map resize failed:', error);
              }
            }
          }, 50);
        }
      }
    });

    resizeObserver.observe(mapContainer.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [map]);

  // Add window resize listener as backup
  useEffect(() => {
    if (!map) return;

    const handleWindowResize = () => {
      setTimeout(() => {
        if (map && !map._removed && map.getCanvas()) {
          try {
            console.log('[ExploreMap] Window resized, triggering map resize');
            map.resize();
          } catch (error) {
            console.warn('[ExploreMap] Window resize failed:', error);
          }
        }
      }, 100);
    };

    window.addEventListener('resize', handleWindowResize);
    return () => window.removeEventListener('resize', handleWindowResize);
  }, [map]);

  // Spinner CSS (inline for now)
  const spinnerStyle = {
    width: '48px',
    height: '48px',
    border: '6px solid #eee',
    borderTop: '6px solid #41B3A3',
    borderRadius: '50%',
    animation: 'spin 1s linear infinite',
    margin: '0 auto',
  };
  const spinnerKeyframes = `@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }`;

  // Add POI click handler
  const handlePOIClick = useCallback((poi: PointOfInterest) => {
    if (mapRef.current && poi.coordinates) {
        const flyToPoiCoords = poi.coordinates as [number, number];
        // Simplified to use standard map flyTo, assuming EnhancedCameraBehavior might be complex
        mapRef.current.flyTo({ center: flyToPoiCoords, zoom: 15, duration: 1000 });
    }
    setCurrentPOIPopup(poi); 
    AnimationDebugTools.log('info', `POI Clicked in ExploreMap: ${poi.name}`, poi);
  }, []); // Removed mapRef from dependency array as mapRef.current usage inside useCallback can be tricky with deps

  // New handler for mobile modal confirmation
  const handleMobileDestinationsConfirm = useCallback((destinations: Destination[]) => {
    if (onReplaceDestinations) {
      onReplaceDestinations(destinations); // This is HomePage.replaceSelectedDestinations
    }

    // Fly to the first city in the confirmed list
    const firstCity = destinations.length > 0 ? destinations[0] : null;
    if (firstCity && firstCity.coordinates && mapboxMapFromHook && isValidCoordinates(firstCity.coordinates)) {
      mapboxMapFromHook.flyTo({ center: firstCity.coordinates as [number, number], zoom: 12, essential: true });
    }
     // Update the single selectedCity state in HomePage (likely for panel filtering)
    if (onCitySelect) { 
      onCitySelect(firstCity); 
    }
  }, [mapboxMapFromHook, onReplaceDestinations, onCitySelect]);

  const isJourneyButtonPaused: boolean = coreAnimation.animationState.isPaused;

  const cinematicController = useRef<CinematicController | null>(null);

  const handleDestinationSelectFromPanel = useCallback((destination: Destination) => {
    setSelectedDestination(destination);
    if (onCitySelect) {
      onCitySelect(destination);
    }
    const destPos = toPositionTuple(destination.position);
    if (destPos && map) {
      map.flyTo({ center: destPos, zoom: 12 });
    }
  }, [map, onCitySelect, setSelectedDestination]);

  const handleDirectPOISelect = useCallback((poi: PointOfInterest) => {
    onPOISelect(poi);
  }, [onPOISelect]);

  return (
    <div className="relative w-full h-full">
      <style>{spinnerKeyframes}</style>
      {/* Map loading overlay */}
      {!isLoaded && (
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: 'rgba(255,255,255,0.85)',
          zIndex: 1000,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          transition: 'opacity 0.4s',
        }}>
          <div style={{...spinnerStyle}}></div>
          <div style={{ marginTop: 16, color: '#41B3A3', fontWeight: 600, fontSize: 18 }}>Loading map…</div>
        </div>
      )}
      <div
        id="explore-map-container"
        ref={mapContainer}
        className="absolute inset-0"
        style={{
          overflow: 'visible',
          zIndex: 1,
          minHeight: '400px'
        }}
      />
      <div className="absolute top-4 right-4 z-10">
        {/* This is where a top-right button would go if we add one. For now, the main JourneyButton is in the footer. */}
      </div>

      {/* Render the main JourneyButton - it positions itself at the bottom/center */}
      <JourneyButton
        map={mapboxMapFromHook}
        isAnimating={isAnimating}
        isPaused={isJourneyButtonPaused}
        onStart={handleBeginJourney}
        onPauseResume={handlePauseResumeAnimation}
        showButton={selectedDestinations.length >= 2 || isAnimating}
        canBeginJourney={canBeginJourney}
        buttonState={canBeginJourney ? (isAnimating ? (isJourneyButtonPaused ? 'paused' : 'playing') : 'default') : 'disabled'}
        buttonError={null}
      />

      {/* Map controls */}
      <div className="absolute bottom-24 right-5 z-10 flex flex-col space-y-2">
        <button className="bg-white p-2 rounded-full shadow-md text-gray-700 hover:bg-gray-100" onClick={() => mapboxMapFromHook?.zoomIn()}>
          <span className="text-lg">+</span>
        </button>
        <button className="bg-white p-2 rounded-full shadow-md text-gray-700 hover:bg-gray-100" onClick={() => mapboxMapFromHook?.zoomOut()}>
          <span className="text-lg">-</span>
        </button>
      </div>

      {/* City Selection Overlay */}
      {mapboxMapFromHook && mapReady && (
        <CityTagsOverlay
          cities={normalizedDestinations}
          selectedCities={selectedDestinations || []} 
          onCitySelect={handleCitySelect} 
          onZoomToCity={(coordinates) => mapboxMapFromHook.flyTo({ center: coordinates, zoom: 12, essential: true })}
          onShowCityPOIs={showCityPOIs}
          isMobile={isMobile}
          isDark={finalIsDark}
          onMobileSelectionsConfirm={handleMobileDestinationsConfirm}
        />
      )}

      {/* Render RouteLayer to fetch and display the route */}
      {/* Debug logging disabled for production readiness */}
      {/* (() => {
        if (mapboxMapFromHook && selectedDestinations.length >= 2) {
          console.log('[ExploreMap] Rendering RouteLayer with destinations:', selectedDestinations.map(d => d.name), selectedDestinations.map(d => d.coordinates));
        } else if (mapboxMapFromHook && selectedDestinations.length < 2) {
          console.log('[ExploreMap] Not rendering RouteLayer, selectedDestinations count:', selectedDestinations.length);
        }
        return null;
      })() */}
      {mapboxMapFromHook && selectedDestinations.length >= 2 && (
        <RouteLayer
          map={mapboxMapFromHook}
          destinations={selectedDestinations} // Pass the selected destinations
          onRouteReady={handleRouteReady} // Pass the callback
          routeColor="#E27D60" // Optional: customize route color
        />
      )}

      {/* Direct POI Markers */}
      {mapboxMapFromHook && (
        <DirectPOIMarkers
          map={mapboxMapFromHook}
          pois={normalizedPOIs.filter(poi => Array.isArray(poi.coordinates) && poi.coordinates.length === 2 && poi.coordinates[0] !== undefined && poi.coordinates[1] !== undefined && poi.position)}
          selectedPOIs={selectedPOIs.map(normalizePOI).filter(poi => Array.isArray(poi.coordinates) && poi.coordinates.length === 2 && poi.coordinates[0] !== undefined && poi.coordinates[1] !== undefined && poi.position)}
          onPOIClick={memoizedOnPOIClick}
        />
      )}

      {/* Countdown Overlay */}
      {countdownVisible && (
        <CountdownLoadingIndicator
          isVisible={countdownVisible}
          onComplete={handleCountdownComplete}
        />
      )}

      {/* REFACTORED: Core Animation System - No JSX component needed */}
      {/* Animation is handled via useCoreAnimation hook */}

      {/* POI Discovery Manager - The cornerstone innovation */}
      <DiscoveryManager
        availablePOIs={normalizedPOIs}
        onPOISelect={handleDirectPOISelect}
        onPOIAddToItinerary={(poi) => {
          // Add POI to selected POIs
          if (!selectedPOIs.find(p => p.id === poi.id)) {
            onPOISelect(poi);
          }
        }}
        onExploreCity={(cityName) => {
          // Find and select the city
          const city = normalizedDestinations.find(d =>
            d.name.toLowerCase().includes(cityName.toLowerCase())
          );
          if (city && onCitySelect) {
            onCitySelect(city);
          }
        }}
      />
    </div>
  );
});

export default React.memo(ExploreMapComponent);
// Add a display name for better debugging
// (ExploreMapComponent as React.FC).displayName = 'ExploreMap'; // Removed to fix linter
// (React.memo(ExploreMapComponent) as React.FC).displayName = 'MemoizedExploreMap'; // Removed to fix linter

// Note: Using the fixClickableElements function defined at the top of the file
// Ensure this utility is correctly defined and exported if used elsewhere.

// Helper to normalize destinations to have distance as string
function normalizeDestinations(destinations: any[]): any[] {
  return destinations.map(dest => ({
    ...dest,
    distance: typeof dest.distance === 'number' ? String(dest.distance) : dest.distance
  }));
}

// Define isPositionObject locally if needed
const isPositionObject = (position: unknown): position is PositionObject => {
  return (
    typeof position === 'object' &&
    position !== null &&
    'lat' in position &&
    'lng' in position &&
    typeof (position as PositionObject).lat === 'number' &&
    typeof (position as PositionObject).lng === 'number'
  );
};

// Add these type guard functions after the type definitions
function isPositionTuple(position: unknown): position is PositionTuple {
  return (
    Array.isArray(position) &&
    position.length === 2 &&
    typeof position[0] === 'number' &&
    typeof position[1] === 'number'
  );
}

// Add a helper function to safely convert any position to PositionObject format
function toPositionObject(position: Position): PositionObject {
  if (isPositionTuple(position)) {
    return { lng: position[0], lat: position[1] };
  }
  return position;
}
