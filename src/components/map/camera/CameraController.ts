import mapboxgl from 'mapbox-gl';

/**
 * Camera modes for different contexts in the application
 */
export enum CameraMode {
  ROUTE_TRAVEL = 'route_travel',
  CITY_EXPLORATION = 'city_exploration',
  POI_DETAIL = 'poi_detail'
}

/**
 * Camera settings configuration
 */
interface CameraSettings {
  zoom: number;
  pitch: number;
  bearing: number;
  duration: number;
}

/**
 * Controller for managing map camera behaviors
 */
export class CameraController {
  private map: mapboxgl.Map;
  
  // Standard camera settings for different modes
  private readonly CAMERA_PRESETS: Record<CameraMode, CameraSettings> = {
    [CameraMode.ROUTE_TRAVEL]: {
      zoom: 8,
      pitch: 45,
      bearing: 0,
      duration: 500
    },
    [CameraMode.CITY_EXPLORATION]: {
      zoom: 14,
      pitch: 45,
      bearing: 0,
      duration: 1000
    },
    [CameraMode.POI_DETAIL]: {
      zoom: 16,
      pitch: 45,
      bearing: 0,
      duration: 1500
    }
  };
  
  /**
   * Create a new CameraController
   */
  constructor(map: mapboxgl.Map) {
    this.map = map;
  }
  
  /**
   * Follow the vehicle with appropriate camera settings
   */
  public followVehicle(position: [number, number], bearing: number, mode: CameraMode): void {
    const settings = this.CAMERA_PRESETS[mode];
    
    // Calculate a position slightly ahead of the vehicle for a better view
    const lookAheadDistance = 0.0005; // Small offset in degrees
    const bearingRad = (bearing * Math.PI) / 180;
    const lookAheadPosition: [number, number] = [
      position[0] + Math.sin(bearingRad) * lookAheadDistance,
      position[1] + Math.cos(bearingRad) * lookAheadDistance
    ];
    
    this.map.easeTo({
      center: lookAheadPosition,
      zoom: settings.zoom,
      pitch: settings.pitch,
      bearing: bearing,
      duration: settings.duration,
      essential: true // Won't be interrupted by user interaction
    });
  }
  
  /**
   * Transition to a specific camera mode at the given position
   */
  public transitionToMode(position: [number, number], mode: CameraMode): void {
    const settings = this.CAMERA_PRESETS[mode];
    
    this.map.flyTo({
      center: position,
      zoom: settings.zoom,
      pitch: settings.pitch,
      bearing: settings.bearing,
      duration: settings.duration,
      essential: true
    });
    
    console.log(`🔄 [${new Date().toISOString()}] Camera transitioned to ${mode} mode`);
  }
  
  /**
   * Fit the camera view to show the entire route
   */
  public fitToRoute(route: [number, number][]): void {
    if (!route || route.length < 2) return;
    
    // Create a bounds object that encompasses all route points
    const bounds = new mapboxgl.LngLatBounds();
    route.forEach(point => bounds.extend(point));
    
    // Fit map to the route with some padding
    this.map.fitBounds(bounds, {
      padding: 50,
      maxZoom: 10,
      duration: 1000
    });
    
    // After fitting bounds, set appropriate pitch
    setTimeout(() => {
      this.map.easeTo({
        pitch: this.CAMERA_PRESETS[CameraMode.ROUTE_TRAVEL].pitch,
        duration: 800
      });
    }, 1000);
  }
  
  /**
   * Get the current camera settings
   */
  public getCurrentSettings(): { center: [number, number], zoom: number, pitch: number, bearing: number } {
    return {
      center: this.map.getCenter().toArray() as [number, number],
      zoom: this.map.getZoom(),
      pitch: this.map.getPitch(),
      bearing: this.map.getBearing()
    };
  }
  
  /**
   * Smoothly reset the camera bearing to north (0 degrees)
   */
  public resetBearingToNorth(): void {
    this.map.easeTo({
      bearing: 0,
      duration: 1000
    });
  }
  
  /**
   * Apply custom camera settings
   */
  public applyCustomSettings(settings: Partial<CameraSettings>): void {
    this.map.easeTo({
      ...settings,
      duration: settings.duration || 800
    });
  }
} 