/**
 * AnimationFrameManager.ts
 * 
 * Provides optimized animation frame management with:
 * - Consistent frame timing
 * - Throttling for performance
 * - Proper cleanup of animation resources
 * - Animation health monitoring
 * 
 * This follows the Animation Architecture rule of using requestAnimationFrame
 * for animation loops and properly cleaning up resources.
 */

import AnimationDebugTools from '@/utils/AnimationDebugTools';

// Animation timing constants
const FRAME_RATE_TARGET = 60; // Target frame rate
const MIN_FRAME_DELAY = 16.67; // ~60fps in ms
const OPTIMAL_ANIMATION_DELAY = 100; // Follow the architecture rule of 100ms delay between updates
const MAX_STALLED_FRAMES = 10; // Number of frames to wait before considering animation stalled

export interface AnimationFrameConfig {
  onFrame: (deltaTime: number, timestamp: number) => void;
  onComplete?: () => void;
  onError?: (error: Error) => void;
  targetDelay?: number; // Optional custom delay between frames (ms)
  duration?: number; // Optional total duration (ms)
  shouldContinue?: () => boolean; // Optional condition to continue animation
}

export interface AnimationMetrics {
  fps: number;
  averageDeltaTime: number;
  frameCount: number;
  startTime: number;
  stalledCount: number;
  isHealthy: boolean;
}

export class AnimationFrameManager {
  private frameId: number | null = null;
  private lastFrameTime: number = 0;
  private startTime: number = 0;
  private frameCount: number = 0;
  private isRunning: boolean = false;
  private completionProgress: number = 0;
  private deltaTimeHistory: number[] = [];
  private stalledFrameCount: number = 0;
  private config: AnimationFrameConfig | null = null;
  private metrics: AnimationMetrics = {
    fps: 0,
    averageDeltaTime: 0,
    frameCount: 0,
    startTime: 0,
    stalledCount: 0,
    isHealthy: true
  };

  /**
   * Start animation with the given configuration
   */
  public start(config: AnimationFrameConfig): void {
    // Clean up any existing animation
    this.stop();
    
    this.config = config;
    this.isRunning = true;
    this.startTime = performance.now();
    this.lastFrameTime = this.startTime;
    this.frameCount = 0;
    this.completionProgress = 0;
    this.deltaTimeHistory = [];
    this.stalledFrameCount = 0;
    
    // Initialize metrics
    this.metrics = {
      fps: 0,
      averageDeltaTime: 0,
      frameCount: 0,
      startTime: this.startTime,
      stalledCount: 0,
      isHealthy: true
    };
    
    // Log start of animation
    AnimationDebugTools.info(`Animation started at ${Date.now()}`);
    
    // Schedule first frame
    this.frameId = requestAnimationFrame(this.handleAnimationFrame);
  }
  
  /**
   * Stop the animation and clean up resources
   */
  public stop(): void {
    if (this.frameId !== null) {
      cancelAnimationFrame(this.frameId);
      this.frameId = null;
      this.isRunning = false;
      AnimationDebugTools.info(`Animation stopped at ${Date.now()}`);
    }
  }
  
  /**
   * Check if animation is currently running
   */
  public isAnimating(): boolean {
    return this.isRunning;
  }
  
  /**
   * Get current animation metrics
   */
  public getMetrics(): AnimationMetrics {
    return { ...this.metrics };
  }
  
  /**
   * Handle animation frame with optimized timing
   */
  private handleAnimationFrame = (timestamp: number): void => {
    if (!this.isRunning || !this.config) {
      return;
    }
    
    try {
      // Calculate time since last frame
      const deltaTime = timestamp - this.lastFrameTime;
      
      // Calculate total elapsed time
      const elapsedTime = timestamp - this.startTime;
      
      // Update frame count
      this.frameCount++;
      
      // Update metrics
      this.updateMetrics(deltaTime, timestamp);
      
      // Check if animation should continue based on duration or custom condition
      const shouldContinue = this.shouldContinueAnimation(elapsedTime);
      
      if (!shouldContinue) {
        this.completeAnimation();
        return;
      }
      
      // Check if enough time has passed for next frame
      const targetDelay = this.config.targetDelay || OPTIMAL_ANIMATION_DELAY;
      
      if (deltaTime >= targetDelay) {
        // Reset stalled count since we're processing a frame
        this.stalledFrameCount = 0;
        
        // Execute frame callback
        this.config.onFrame(deltaTime, timestamp);
        
        // Update last frame time
        this.lastFrameTime = timestamp;
        
        // Track delta time for metrics
        this.deltaTimeHistory.push(deltaTime);
        if (this.deltaTimeHistory.length > 10) {
          this.deltaTimeHistory.shift();
        }
      } else {
        // Not enough time passed, wait for next frame without executing callback
        this.stalledFrameCount++;
      }
      
      // Check for stalled animation
      if (this.stalledFrameCount > MAX_STALLED_FRAMES) {
        AnimationDebugTools.error(`Animation appears stalled for ${MAX_STALLED_FRAMES} frames`);
        this.metrics.isHealthy = false;
        this.metrics.stalledCount++;
        
        // Force a frame update to recover
        this.config.onFrame(targetDelay, timestamp);
        this.lastFrameTime = timestamp;
        this.stalledFrameCount = 0;
      }
      
      // Schedule next frame
      this.frameId = requestAnimationFrame(this.handleAnimationFrame);
    } catch (error) {
      AnimationDebugTools.error(`Error in animation frame: ${error}`);
      if (this.config.onError) {
        this.config.onError(error as Error);
      }
      
      // Try to recover by scheduling next frame
      this.frameId = requestAnimationFrame(this.handleAnimationFrame);
    }
  };
  
  /**
   * Check if animation should continue
   */
  private shouldContinueAnimation(elapsedTime: number): boolean {
    if (!this.config) return false;
    
    // Check duration-based completion
    if (this.config.duration && elapsedTime >= this.config.duration) {
      return false;
    }
    
    // Check custom continue condition
    if (this.config.shouldContinue && !this.config.shouldContinue()) {
      return false;
    }
    
    return true;
  }
  
  /**
   * Complete the animation and trigger onComplete callback if provided
   */
  private completeAnimation(): void {
    // Calculate final metrics
    const totalTime = performance.now() - this.startTime;
    
    // Log completion
    AnimationDebugTools.info(`Animation completed after ${totalTime.toFixed(2)}ms with ${this.frameCount} frames`);
    
    // Clean up animation
    this.stop();
    
    // Trigger onComplete callback if provided
    if (this.config && this.config.onComplete) {
      this.config.onComplete();
    }
  }
  
  /**
   * Update animation metrics
   */
  private updateMetrics(deltaTime: number, timestamp: number): void {
    // Update FPS (use exponential moving average)
    const currentFps = deltaTime > 0 ? 1000 / deltaTime : 0;
    this.metrics.fps = this.metrics.fps * 0.9 + currentFps * 0.1;
    
    // Update average delta time
    if (this.deltaTimeHistory.length > 0) {
      const sum = this.deltaTimeHistory.reduce((a, b) => a + b, 0);
      this.metrics.averageDeltaTime = sum / this.deltaTimeHistory.length;
    } else {
      this.metrics.averageDeltaTime = deltaTime;
    }
    
    // Update other metrics
    this.metrics.frameCount = this.frameCount;
    
    // Update health status
    this.metrics.isHealthy = this.stalledFrameCount < MAX_STALLED_FRAMES / 2;
    
    // Regularly log animation metrics (only if debugging is enabled)
    this.logAnimationMetrics();
  }
  
  /**
   * Regularly log animation metrics (only if debugging is enabled)
   */
  private logAnimationMetrics(): void {
    if (this.frameCount % 60 === 0) {
      AnimationDebugTools.debug('Animation metrics', this.metrics);
    }
  }
}

// Export singleton instance
export const animationFrameManager = new AnimationFrameManager();

/**
 * Hook-friendly function to start an animation with proper cleanup
 */
export const useAnimationFrame = (
  callback: (deltaTime: number, timestamp: number) => void,
  dependencies: any[] = [],
  options: Partial<AnimationFrameConfig> = {}
): () => void => {
  const start = () => {
    animationFrameManager.start({
      onFrame: callback,
      ...options
    });
  };
  
  const stop = () => {
    animationFrameManager.stop();
  };
  
  return stop;
}; 