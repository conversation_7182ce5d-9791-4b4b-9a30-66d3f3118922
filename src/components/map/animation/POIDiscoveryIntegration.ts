/**
 * POIDiscoveryIntegration.ts
 * 
 * POI Discovery Integration Layer
 * ------------------------------
 * 
 * RESPONSIBILITY:
 * Provides integration between vehicle animation and POI discovery.
 * Manages POI discovery during travel, city exploration, and handles
 * user interaction with discovered POIs.
 * 
 * KEY FUNCTIONALITY:
 * - Initializes POI discovery for initial cities
 * - Finds nearby POIs based on vehicle position
 * - Schedules periodic POI checks during animation
 * - Creates and configures POIDiscoveryManager instances
 * - Announces POI discoveries to the user interface
 * 
 * INTEGRATION PATTERNS:
 * - Direct imports: POIDiscoveryManager
 * - Imported by: AnimationIntegration, TravelAnimator
 * 
 * DEPENDENCIES:
 * - POIDiscoveryManager: For POI detection and discovery events
 * - AnimationDebugTools: For logging and debugging
 * - MapboxGL: For map visualization and announcements
 * 
 * USAGE:
 * This module provides utility functions rather than a class instance:
 * 
 * ```typescript
 * // Initialize city POIs
 * initializeInitialCityPOIs(
 *   mapInstance,
 *   destinations,
 *   pointsOfInterest,
 *   userInterests,
 *   allDestinations,
 *   discoveredPOIsSet,
 *   handlePOIDiscovered,
 *   setDiscoveredPOIs,
 *   setCurrentArea,
 *   setJourneyPhase
 * );
 * 
 * // Create POI manager
 * const poiManager = createPOIDiscoveryManager(
 *   pointsOfInterest,
 *   discoveredPOIsSet,
 *   userInterests,
 *   selectedCategories,
 *   currentArea
 * );
 * 
 * // Check for POIs during animation
 * checkForPOIsDuringAnimation(
 *   vehiclePosition,
 *   isAnimating,
 *   poiManager,
 *   discoveredPOIsSet,
 *   handlePOIDiscovered,
 *   lastCheckTime,
 *   updateLastCheckTime,
 *   currentArea
 * );
 * ```
 */

import { PointOfInterest } from '@/types/poi.ts';
import { Destination } from '@/types/destination.ts';
import * as turf from '@turf/turf';
import { POIDiscoveryManager } from './POIDiscoveryManager';
import { getPOIsInArea } from '../utils/POIDiscovery';
import AnimationDebugTools from './AnimationDebugTools';
import { showAnnouncement } from './AnimationUtils';
import mapboxgl from 'mapbox-gl';
import { normalizePOI } from '@/types/POITypes';

// POI discovery constants
export const POI_DETECTION_RADIUS = 5; // km
export const CITY_POI_DETECTION_RADIUS = 10; // km

/**
 * Initialize POI discovery for the initial city (first destination)
 */
export const initializeInitialCityPOIs = (
  map: mapboxgl.Map | undefined,
  destinations: Destination[],
  pois: PointOfInterest[],
  userInterests: string[] = [],
  allDestinations: Destination[] = [],
  discoveredPOIs: Set<string>,
  onPOIDiscovered: (poi: PointOfInterest) => void,
  setDiscoveredPOIObjects: (setter: (prev: PointOfInterest[]) => PointOfInterest[]) => void,
  setCurrentArea: (area: string | null) => void,
  setJourneyPhase: (phase: any) => void
): void => {
  if (!map || !destinations.length || !pois.length) return;
  
  // Get initial city (typically Marrakech)
  const initialCity = destinations[0];
  
  console.log('Initializing POI discovery for initial city:', initialCity.name);
  
  // Get POIs in the initial city area
  const initialAreaPOIs = getPOIsInArea(
    initialCity.name,
    CITY_POI_DETECTION_RADIUS,
    allDestinations || [],
    pois,
    userInterests || []
  );
  
  console.log(`Found ${initialAreaPOIs.length} POIs in initial city`);
  
  // Set current area to initial city and update POI state
  if (initialAreaPOIs.length > 0) {
    setCurrentArea(initialCity.name);
    setJourneyPhase('selecting_pois');
    
    // Add POIs to discovered set and store POI objects
    initialAreaPOIs.forEach(poi => {
      if (!discoveredPOIs.has(poi.id)) {
        onPOIDiscovered(poi);
        setDiscoveredPOIObjects(prev => [...prev, poi]);
      }
    });
    
    // Center map on city
    if (initialCity.coordinates && map) {
      map.flyTo({
        center: initialCity.coordinates,
        zoom: 14, // City exploration zoom
        duration: 1000
      });
    }
  }
};

/**
 * Finds nearby POIs based on vehicle position
 */
export const findNearbyPOIs = (
  position: [number, number],
  pois: PointOfInterest[],
  discoveredPOIs: Set<string>
): PointOfInterest[] => {
  return pois.filter(poi => {
    // Skip already discovered POIs
    if (discoveredPOIs.has(poi.id)) return false;
    
    // Skip POIs without coordinates
    if (!poi.coordinates || poi.coordinates.length !== 2) return false;
    
    // Calculate distance
    const distance = turf.distance(
      turf.point(position),
      turf.point(poi.coordinates),
      { units: 'kilometers' }
    );
    
    // Return POIs within detection radius
    return distance < POI_DETECTION_RADIUS;
  });
};

/**
 * Checks for POIs during animation progress
 */
export function checkForPOIsDuringAnimation(
  position: [number, number],
  poiManager: POIDiscoveryManager | null,
  timestamp: number,
  options: {
    approachRadius?: number;
    debugMode?: boolean;
  } = {}
): { 
  nearbyPOIs: any[]; 
  isApproaching: boolean;
} {
  const { approachRadius = 1, debugMode = false } = options;
  
  // Log every check with position if debug mode is enabled
  if (debugMode) {
    console.log(`🔍 [${Date.now()}] Checking for POIs at position:`, position);
  }
  
  let nearbyPOIs: any[] = [];
  let isApproaching = false;
  
  // Validate position data
  if (!position || !Array.isArray(position) || position.length !== 2) {
    console.error(`❌ [${Date.now()}] Invalid position for POI check:`, position);
    return { nearbyPOIs, isApproaching };
  }
  
  // Ensure we have a valid POI manager
  if (!poiManager) {
    console.warn(`⚠️ [${Date.now()}] No POI manager available for POI check`);
    return { nearbyPOIs, isApproaching };
  }
  
  try {
    // First update the POI manager with the current position to trigger nearby POI detection
    nearbyPOIs = poiManager.updatePosition(position);
    
    // For debugging, log always
    console.log(`🔍 [${Date.now()}] POI check result: ${nearbyPOIs.length} POIs found near`, position);
    
    // Also check if we're approaching any POIs (this triggers the speed changes)
    poiManager.checkApproaching(position, approachRadius);
    
    // Determine if we're approaching based on nearby POIs
    isApproaching = nearbyPOIs.length > 0;
    
    // If approaching POIs, update the contextual speed controller
    if (isApproaching && nearbyPOIs.length > 0) {
      // Get the first POI for logging
      const firstPOI = nearbyPOIs[0];
      console.log(`👀 [${Date.now()}] Approaching POI: ${firstPOI?.name || 'Unknown'}`);
      
      // Check if ContextualSpeedController is available through POIManager
      try {
        const speedController = poiManager.getSpeedController?.();
        if (speedController && typeof speedController.updateContext === 'function') {
          console.log(`🐢 [${Date.now()}] Updating speed context for nearby POI`);
          speedController.updateContext({ isDiscoveringPOI: true });
        }
      } catch (error) {
        console.error(`❌ [${Date.now()}] Error updating speed controller:`, error);
      }
    }
  } catch (error) {
    console.error(`❌ [${Date.now()}] Error checking for POIs:`, error);
  }
  
  return { nearbyPOIs, isApproaching };
}

/**
 * Create a POI discovery manager instance
 */
export const createPOIDiscoveryManager = (
  pois: PointOfInterest[],
  discoveredPOIs: Set<string>,
  userInterests: string[] = [],
  selectedCategories: string[] = [],
  currentArea: string | null
) => {
  // Always normalize POIs before passing to POIDiscoveryManager
  const normalizedPOIs = pois.map(normalizePOI);
  if (process.env.NODE_ENV !== 'production') {
    console.debug('[POIDiscoveryIntegration] Normalized POIs for discovery:', normalizedPOIs);
  }
  return POIDiscoveryManager.getInstance({
    pois: normalizedPOIs,
    discoveredPOIs,
    userInterests,
    selectedCategories,
    detectionRadius: POI_DETECTION_RADIUS,
    currentArea
  });
};

/**
 * Announce POI discovery to the user
 */
export const announcePOIDiscovery = (
  map: mapboxgl.Map,
  poi: PointOfInterest
): void => {
  // Show announcement on map
  showAnnouncement(
    map,
    `Discovered: ${poi.name}!`,
    3000
  );
  
  // Log discovery
  console.log(`🔍 [${Date.now()}] Discovered POI: ${poi.name}`);
};

export default {
  initializeInitialCityPOIs,
  findNearbyPOIs,
  checkForPOIsDuringAnimation,
  createPOIDiscoveryManager,
  announcePOIDiscovery
}; 