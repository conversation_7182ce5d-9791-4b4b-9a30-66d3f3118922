import { 
  BROAD_CONTEXT_ZOOM, 
  REGIONAL_CONTEXT_ZOOM, 
  CITY_APPROACH_ZOOM,
  POI_DETAIL_ZOOM,
  MOROCCO_CENTER,
  RhythmParams 
} from '../utils/types';
import { findCulturalRegion, isInSignificantCulturalRegion, CULTURAL_REGIONS } from '@/utils/cultural-regions';
import * as turf from '@turf/turf';
import { Destination } from '@/types/destination';

/**
 * Interface for the rhythm context data
 */
interface RhythmContext {
  elapsed: number;
  currentPoint: [number, number];
  nearestCity?: Destination | null;
  distanceToCity?: number;
  inCulturalRegion?: boolean;
  culturalRegionName?: string;
  isApproachingPOI?: boolean;
  isInTransitSegment?: boolean;
  terrainType?: 'urban' | 'mountain' | 'desert' | 'coastal' | 'default';
}

/**
 * Factory function that creates a contextual rhythm handler
 * This returns a function that can be called on each animation frame
 */
export const createContextualRhythmHandler = (
  allDestinations: Destination[]
) => {
  // Track state between calls
  let lastZoomEvent = 0;
  let lastCulturalRegion: string | null = null;
  
  // Return the handler function
  return (
    currentPoint: [number, number],
    elapsedTime: number,
    isNearPOI: boolean = false
  ): RhythmParams | null => {
    // Don't apply rhythm changes too frequently
    if (elapsedTime - lastZoomEvent < 15000) {
      return null;
    }
    
    // Build the current context
    const context: RhythmContext = {
      elapsed: elapsedTime,
      currentPoint,
      nearestCity: findNearestCity(currentPoint, allDestinations),
      inCulturalRegion: isInSignificantCulturalRegion(currentPoint),
    };
    
    if (context.nearestCity) {
      context.distanceToCity = turf.distance(
        turf.point(currentPoint),
        turf.point(context.nearestCity.coordinates as [number, number]),
        { units: 'kilometers' }
      );
    }
    
    // Get rhythmic camera parameters based on context
    const rhythmParams = getDynamicZoomParams(context);
    
    if (rhythmParams) {
      // Update the last event time
      lastZoomEvent = elapsedTime;
      
      // If entering a cultural region, log it
      const region = findCulturalRegion(currentPoint);
      if (region && region.name !== lastCulturalRegion) {
        console.log(`Entering cultural region: ${region.name}`);
        lastCulturalRegion = region.name;
      }
      
      return rhythmParams;
    }
    
    return null;
  };
};

/**
 * Get dynamic zoom parameters based on the current context
 */
const getDynamicZoomParams = (context: RhythmContext): RhythmParams | null => {
  // Check for significant cultural regions
  const region = findCulturalRegion(context.currentPoint);
  
  // Entering a cultural region
  if (region && context.inCulturalRegion) {
    const startPoint = context.currentPoint;
    return {
      zoom: REGIONAL_CONTEXT_ZOOM,
      duration: 5000, // Much slower transition in cultural hotspots
      center: startPoint,
      bearing: 0
    };
  }
  
  // Approaching a city
  if (context.nearestCity && context.distanceToCity && context.distanceToCity < 10) {
    return {
      zoom: CITY_APPROACH_ZOOM,
      duration: 3000,
      center: context.nearestCity.coordinates as [number, number],
      bearing: 0
    };
  }
  
  // In transit - occasionally zoom out for broader context
  if (Math.random() < 0.3) { // 30% chance
    return {
      zoom: BROAD_CONTEXT_ZOOM,
      duration: 4000,
      center: MOROCCO_CENTER,
      bearing: 0
    };
  }
  
  return null;
};

/**
 * Find the nearest city to a given point
 */
const findNearestCity = (
  point: [number, number],
  destinations: Destination[]
): Destination | null => {
  if (!destinations.length) return null;
  
  let nearestCity: Destination | null = null;
  let minDistance = Infinity;
  
  destinations.forEach(city => {
    if (!city.coordinates) return;
    
    const distance = turf.distance(
      turf.point(point),
      turf.point(city.coordinates as [number, number]),
      { units: 'kilometers' }
    );
    
    if (distance < minDistance) {
      minDistance = distance;
      nearestCity = city;
    }
  });
  
  return nearestCity;
};

/**
 * Calculate distance from point to nearest city
 */
const getDistanceToCity = (
  point: [number, number],
  city: Destination
): number | null => {
  if (!city.coordinates) return null;
  
  return turf.distance(
    turf.point(point),
    turf.point(city.coordinates as [number, number]),
    { units: 'kilometers' }
  );
}; 