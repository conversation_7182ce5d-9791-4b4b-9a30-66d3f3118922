// src/components/map/animation/RouteAnimator.ts

import { Position } from './utils/types';
import * as turf from '@turf/turf';

// Easing function (can be moved to a shared utility later if needed)
const easeInOutCubic = (t: number): number => {
  return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
};

export interface PointData {
  position: Position;
  bearing: number;
  segmentIndex: number;
}

class RouteAnimator {
  private readonly LOG_PREFIX = '[RouteAnimator]';
  private route: Position[] = [];
  private segmentLengths: number[] = [];
  private totalRouteLength: number = 0;
  private preProcessed: boolean = false;

  constructor(routePoints?: Position[]) {
    if (routePoints && routePoints.length > 0) {
      this.setRoute(routePoints);
    } else {
      console.log(`${this.LOG_PREFIX} Initialized without a route. Call setRoute() to provide one.`);
    }
  }

  public setRoute(routePoints: Position[]): void {
    if (!routePoints || routePoints.length < 2) {
      console.error(`${this.LOG_PREFIX} Route must have at least two points. Provided: ${routePoints ? routePoints.length : 'null'}`);
      this.route = [];
      this.segmentLengths = [];
      this.totalRouteLength = 0;
      this.preProcessed = false;
      return;
    }

    this.route = [...routePoints]; 
    this.segmentLengths = [];
    this.totalRouteLength = 0;

    try {
      for (let i = 0; i < this.route.length - 1; i++) {
        const p1 = this.route[i];
        const p2 = this.route[i + 1];
        if (!p1 || !p2) {
            console.error(`${this.LOG_PREFIX} Invalid point found in route at index ${i} or ${i+1}. Skipping segment.`);
            this.segmentLengths.push(0); 
            continue;
        }
        const segmentDistance = turf.distance(turf.point(p1), turf.point(p2), { units: 'meters' });
        this.segmentLengths.push(segmentDistance);
        this.totalRouteLength += segmentDistance;
      }
      this.preProcessed = true;
      console.log(`${this.LOG_PREFIX} Route set and pre-processed. Total length: ${this.totalRouteLength.toFixed(2)}m, Segments: ${this.segmentLengths.length}`);
    } catch(error) {
        console.error(`${this.LOG_PREFIX} Error during route pre-processing:`, error);
        this.route = [];
        this.segmentLengths = [];
        this.totalRouteLength = 0;
        this.preProcessed = false;
    }
  }

  public getPointDataAtProgress(progress: number, customEasingFn?: (t: number) => number): PointData | null {
    if (!this.preProcessed || this.route.length === 0) {
      console.warn(`${this.LOG_PREFIX} Route not set or pre-processed. Cannot get point data. Progress: ${progress}`);
      return null;
    }
    
    if (this.totalRouteLength === 0) {
        const p1 = this.route[0];
        const p2 = this.route.length > 1 ? this.route[1] : p1; 
        return {
            position: [...p1],
            bearing: RouteAnimator.calculateBearing(p1, p2),
            segmentIndex: 0,
        };
    }

    const easingFunction = customEasingFn || easeInOutCubic; 
    let easedProgress = easingFunction(progress);
    easedProgress = Math.max(0, Math.min(1, easedProgress));

    const distanceAlongRoute = easedProgress * this.totalRouteLength;

    let accumulatedDistance = 0;
    for (let i = 0; i < this.segmentLengths.length; i++) {
      const segmentStartPoint = this.route[i];
      const segmentEndPoint = this.route[i + 1];
      const currentSegmentLength = this.segmentLengths[i];

      if (distanceAlongRoute <= accumulatedDistance + currentSegmentLength || i === this.segmentLengths.length - 1) {
        let segmentProgress = 0;
        if (currentSegmentLength > 0) {
          segmentProgress = (distanceAlongRoute - accumulatedDistance) / currentSegmentLength;
        }
        segmentProgress = Math.max(0, Math.min(1, segmentProgress));

        const currentPosition: Position = [
          segmentStartPoint[0] + (segmentEndPoint[0] - segmentStartPoint[0]) * segmentProgress,
          segmentStartPoint[1] + (segmentEndPoint[1] - segmentStartPoint[1]) * segmentProgress,
        ];

        // COORDINATE GENERATION DEBUGGING
        const isValidPos = currentPosition.every(coord => isFinite(coord) && !isNaN(coord));
        if (!isValidPos) {
          console.error(`❌ [RouteAnimator] Generated invalid position:`, {
            progress,
            segmentIndex: i,
            segmentProgress,
            segmentStartPoint,
            segmentEndPoint,
            currentPosition,
            calculation: {
              lng: `${segmentStartPoint[0]} + (${segmentEndPoint[0]} - ${segmentStartPoint[0]}) * ${segmentProgress}`,
              lat: `${segmentStartPoint[1]} + (${segmentEndPoint[1]} - ${segmentStartPoint[1]}) * ${segmentProgress}`
            }
          });
        }

        const bearing = RouteAnimator.calculateBearing(segmentStartPoint, segmentEndPoint);
        return { position: currentPosition, bearing, segmentIndex: i };
      }
      accumulatedDistance += currentSegmentLength;
    }

    const lastPoint = this.route[this.route.length - 1];
    const secondLastPoint = this.route.length > 1 ? this.route[this.route.length - 2] : lastPoint;
    return {
      position: [...lastPoint],
      bearing: RouteAnimator.calculateBearing(secondLastPoint, lastPoint),
      segmentIndex: Math.max(0, this.route.length - 2),
    };
  }

  public static calculateBearing(p1: Position, p2: Position): number {
    if (!p1 || !p2 || (p1[0] === p2[0] && p1[1] === p2[1])) return 0;
    
    const [lon1Rad, lat1Rad] = p1.map(coord => coord * Math.PI / 180);
    const [lon2Rad, lat2Rad] = p2.map(coord => coord * Math.PI / 180);

    const y = Math.sin(lon2Rad - lon1Rad) * Math.cos(lat2Rad);
    const x = Math.cos(lat1Rad) * Math.sin(lat2Rad) - Math.sin(lat1Rad) * Math.cos(lat2Rad) * Math.cos(lon2Rad - lon1Rad);
    let bearingDegrees = Math.atan2(y, x) * 180 / Math.PI;
    bearingDegrees = (bearingDegrees + 360) % 360;
    return bearingDegrees;
  }
  
  public getTotalRouteLength(): number {
    return this.totalRouteLength;
  }

  public getRoutePoints(): Position[] {
      return [...this.route];
  }

  public isReady(): boolean {
      return this.preProcessed;
  }

  public static createFallbackRoute(destinations?: any[]): Position[] {
    const logPrefix = '[RouteAnimator Fallback]';
    console.log(`${logPrefix} Attempting to create fallback route.`);
  if (destinations && destinations.length >= 2) {
    const fallbackRoute = destinations
        .filter(dest => dest && dest.coordinates && Array.isArray(dest.coordinates) && dest.coordinates.length === 2)
        .map(dest => dest.coordinates as Position);
    if (fallbackRoute.length >= 2) {
        console.log(`${logPrefix} Created fallback route from ${fallbackRoute.length} destinations.`);
      return fallbackRoute;
      }
    }
    const hardcodedRoute: Position[] = [
      [-8.00833, 31.629499], [-7.9, 31.8], [-7.7, 32.0], [-7.5, 32.2], [-7.3, 32.4],
      [-7.1, 32.6], [-6.9, 32.8], [-6.7, 33.0], [-6.5, 33.2], [-6.3, 33.4],
      [-6.0, 33.6], [-5.8, 33.8], [-5.6, 34.0], [-5.4, 34.2], [-5.2, 34.4],
      [-5.0, 34.6], [-4.8, 34.8], [-4.6, 35.0], [-4.4, 35.2], [-4.2, 35.4],
      [-4.0, 35.6], [-3.8, 35.8], [-5.8132, 35.7876]
    ];
    console.log(`${logPrefix} Created hardcoded fallback route with ${hardcodedRoute.length} points.`);
    return hardcodedRoute;
  }

  public dispose(): void {
    console.log(`${this.LOG_PREFIX} Disposing RouteAnimator.`);
    this.route = [];
    this.segmentLengths = [];
    this.totalRouteLength = 0;
    this.preProcessed = false;
  }
}

export { easeInOutCubic }; 
export default RouteAnimator;
