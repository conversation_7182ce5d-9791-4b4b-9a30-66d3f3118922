import mapboxgl from 'mapbox-gl';
import VehicleManager from './VehicleManager';
import RouteAnimator, { easeInOutCubic } from './RouteAnimator';
import { Position, AnimationConfig, AnimationStateType } from './utils/types';
import CinematicController from './CinematicController';
import ContextualSpeedController from './ContextualSpeedController';
import { POIDiscoveryManager } from './POIDiscoveryManager';
import { Destination, PointOfInterest } from '@/types/POITypes';
import { determineTerrainType } from '../utils/MapHelpers';
import { enumToTerrainType } from '@/types/TerrainType';
import * as turf from '@turf/turf';
import { isValidPosition } from '@/types/Position';

export type AnimationProgressCallback = (progress: number, position: Position, bearing: number) => void;
export type AnimationCompleteCallback = () => void;
export type AnimationErrorCallback = (error: Error) => void;

interface ExtendedAnimationConfig extends AnimationConfig {
  map?: mapboxgl.Map;
}

class AnimationManager {
  private static instance: AnimationManager;
  private readonly LOG_PREFIX = '[AnimationManager]';

  private vehicleManager: VehicleManager;
  private routeAnimator: RouteAnimator;
  private cinematicController: CinematicController;
  private contextualSpeedController: ContextualSpeedController;
  private poiDiscoveryManager: POIDiscoveryManager;
  private map: mapboxgl.Map | null = null;

  private animationState: AnimationStateType = AnimationStateType.IDLE;
  private route: Position[] = [];
  private destinations: Destination[] = [];
  private pois: PointOfInterest[] = [];
  private duration: number = 10000;
  private startTime: number | null = null;
  private totalPausedTime: number = 0;
  private pauseStartTime: number | null = null;
  private animationFrameId: number | null = null;
  private currentProgress: number = 0;
  private currentPosition: Position | null = null;
  private currentBearing: number = 0;

  private onProgressUpdate: AnimationProgressCallback | null = null;
  private onAnimationComplete: AnimationCompleteCallback | null = null;
  private onAnimationError: AnimationErrorCallback | null = null;

  // Panel widths for camera padding
  private leftPanelWidth: number = 0;
  private rightPanelWidth: number = 0;

  private constructor() {
    this.vehicleManager = VehicleManager.getInstance();
    this.routeAnimator = new RouteAnimator();
    this.cinematicController = CinematicController.getInstance();
    this.cinematicController.setAnimationManager(this);
    this.contextualSpeedController = ContextualSpeedController.getInstance();
    this.poiDiscoveryManager = POIDiscoveryManager.getInstance();
    this.destinations = [];
    this.pois = [];
    console.log(`${this.LOG_PREFIX} Initialized with VehicleManager, RouteAnimator, CinematicController, ContextualSpeedController, and POIDiscoveryManager.`);
  }

  public static getInstance(): AnimationManager {
    if (!AnimationManager.instance) {
      AnimationManager.instance = new AnimationManager();
    }
    return AnimationManager.instance;
  }

  public setMap(map: mapboxgl.Map): void {
    console.log(`${this.LOG_PREFIX} Setting map instance.`);
    this.map = map;
    this.vehicleManager.setMap(map);
    this.cinematicController.setMap(map);
    // POIDiscoveryManager doesn't have setMap method - it gets map from global window
    // Set map globally for POIDiscoveryManager
    (window as any).map = map;
  }

  public prepareAnimation(config: ExtendedAnimationConfig): void {
    console.log(`${this.LOG_PREFIX} Preparing animation with config:`, config);
    if (this.animationState === AnimationStateType.PLAYING || this.animationState === AnimationStateType.PAUSED) {
      this.stopAnimation();
    }
    
    this.route = config.routePoints;
    this.duration = config.duration || 10000;
    this.onProgressUpdate = config.onProgress || null;
    this.onAnimationComplete = config.onComplete || null;
    this.onAnimationError = config.onError || null;

    // Store panel widths from config if provided
    this.leftPanelWidth = (config as any).leftPanelWidth || 0;
    this.rightPanelWidth = (config as any).rightPanelWidth || 0;

    this.destinations = (config as any).destinations || [];
    this.pois = (config as any).pois || [];
    this.cinematicController.setDestinations(this.destinations);
    this.cinematicController.setPOIs(this.pois);
    this.poiDiscoveryManager.initializePOIs(this.pois);

    if ((config as any).destinations) {
        this.destinations = (config as any).destinations;
        this.cinematicController.setDestinations(this.destinations);
    }
    if ((config as any).pois) {
        this.pois = (config as any).pois;
        this.cinematicController.setPOIs(this.pois);
        this.poiDiscoveryManager.initializePOIs(this.pois);
    }

    if (config.map) {
        this.setMap(config.map);
    }

    if (!this.map) {
        const error = new Error("Map instance not set. Cannot prepare animation.");
        console.error(`${this.LOG_PREFIX} ${error.message}`);
        if(this.onAnimationError) this.onAnimationError(error);
        this.animationState = AnimationStateType.ERROR;
        return;
    }

    this.routeAnimator.setRoute(this.route);
    if (!this.routeAnimator.isReady()) {
      const error = new Error("RouteAnimator failed to process the route.");
      console.error(`${this.LOG_PREFIX} ${error.message}`);
      if (this.onAnimationError) this.onAnimationError(error);
      this.animationState = AnimationStateType.ERROR;
        return;
      }

    const initialPointData = this.routeAnimator.getPointDataAtProgress(0);
    if (!initialPointData) {
        const error = new Error("Could not get initial point data from RouteAnimator.");
        console.error(`${this.LOG_PREFIX} ${error.message}`);
        if(this.onAnimationError) this.onAnimationError(error);
        this.animationState = AnimationStateType.ERROR;
        return;
      }

    // Set route bounds for intelligent map centering
    this.vehicleManager.setRouteBounds(this.route);

    this.vehicleManager.createVehicleMarker(initialPointData.position, initialPointData.bearing);
    this.vehicleManager.setVisibility(true);

    this.currentPosition = initialPointData.position;
    this.currentBearing = initialPointData.bearing;
    this.currentProgress = 0;
    this.animationState = AnimationStateType.IDLE;
    console.log(`${this.LOG_PREFIX} Animation prepared. Vehicle at start of route.`);
    if (this.onProgressUpdate) {
        this.onProgressUpdate(0, initialPointData.position, initialPointData.bearing);
    }
  }

  public startAnimation(): void {
    if (this.animationState === AnimationStateType.PLAYING) {
      console.warn(`${this.LOG_PREFIX} Animation is already playing.`);
          return;
        }
    if (this.animationState === AnimationStateType.ERROR || !this.routeAnimator.isReady()) {
        console.error(`${this.LOG_PREFIX} Cannot start animation due to previous error or route not ready.`);
      return;
    }

    if (this.animationState === AnimationStateType.PAUSED) {
      if (this.pauseStartTime) {
        this.totalPausedTime += performance.now() - this.pauseStartTime;
        this.pauseStartTime = null;
      }
      console.log(`${this.LOG_PREFIX} Resuming animation.`);
    } else {
      this.startTime = performance.now();
      this.totalPausedTime = 0;
      this.pauseStartTime = null;
      this.currentProgress = 0;
      console.log(`${this.LOG_PREFIX} Starting animation.`);
    }
    
    this.animationState = AnimationStateType.PLAYING;
    
    if (this.animationFrameId) {
        cancelAnimationFrame(this.animationFrameId);
    }
    this.animationFrameId = requestAnimationFrame(this.animateFrame);
  }

  public pauseAnimation(isPaused: boolean): void {
    if (this.animationState !== AnimationStateType.PLAYING && this.animationState !== AnimationStateType.PAUSED) {
      console.warn(`${this.LOG_PREFIX} Animation is not playing or already paused. Cannot change pause state.`);
      return;
    }
    
    if (this.animationState === AnimationStateType.PLAYING && isPaused) {
        this.animationState = AnimationStateType.PAUSED;
        this.pauseStartTime = performance.now();
        console.log(`${this.LOG_PREFIX} Animation paused.`);
        if (this.animationFrameId) cancelAnimationFrame(this.animationFrameId); 
        this.animationFrameId = requestAnimationFrame(this.animateFrame);
    } else if (this.animationState === AnimationStateType.PAUSED && !isPaused) {
        this.startAnimation();
    }
  }

  public stopAnimation(): void {
    console.log(`${this.LOG_PREFIX} Stopping animation.`);
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
    this.animationState = AnimationStateType.IDLE;
    this.currentProgress = 0;
    this.startTime = null;
    this.totalPausedTime = 0;
    this.pauseStartTime = null;
  }

  private animateFrame = (timestamp: number): void => {
    if (this.animationState !== AnimationStateType.PLAYING) {
      if (this.animationState === AnimationStateType.PAUSED) {
        this.animationFrameId = requestAnimationFrame(this.animateFrame);
      } else {
        if (this.animationFrameId) cancelAnimationFrame(this.animationFrameId);
        this.animationFrameId = null;
      }
      return;
    }
    
      if (!this.startTime) {
      this.startTime = timestamp - (this.currentProgress * this.duration) - this.totalPausedTime;
      console.warn(`${this.LOG_PREFIX} Start time was null during active play. Resetting based on current progress.`);
    }

    const elapsed = timestamp - this.startTime - this.totalPausedTime;
    let baseProgress = Math.min(elapsed / this.duration, 1);

    // Debug animation progress - more frequent logging
    console.log(`${this.LOG_PREFIX} Frame: timestamp=${timestamp.toFixed(0)}, startTime=${this.startTime?.toFixed(0)}, elapsed=${elapsed.toFixed(0)}, duration=${this.duration}, progress=${(baseProgress * 100).toFixed(1)}%`);

    // Debug animation progress
    if (Math.floor(timestamp / 1000) !== Math.floor((timestamp - 16) / 1000)) { // Log every second
      console.log(`${this.LOG_PREFIX} Animation progress: ${(baseProgress * 100).toFixed(1)}% (${elapsed}ms / ${this.duration}ms)`);
    }

    // Get speed multiplier from ContextualSpeedController
    const speedMultiplier = this.contextualSpeedController.getSpeedMultiplier();

    // Apply speed multiplier to progress calculation
    let adjustedProgress = baseProgress;
    if (speedMultiplier !== 1.0) {
      // Adjust progress based on speed multiplier
      // Lower multiplier = slower progress = more time spent in this area
      adjustedProgress = Math.min(baseProgress * speedMultiplier, 1);
      console.log(`${this.LOG_PREFIX} Speed adjusted: base=${baseProgress.toFixed(3)}, multiplier=${speedMultiplier.toFixed(2)}, adjusted=${adjustedProgress.toFixed(3)}`);
    }

    this.currentProgress = adjustedProgress;

    const pointData = this.routeAnimator.getPointDataAtProgress(this.currentProgress, easeInOutCubic);

    if (pointData) {
      this.currentPosition = pointData.position;
      this.currentBearing = pointData.bearing;

      // COORDINATE SOURCE DEBUGGING: Check what RouteAnimator is providing
      const [lng, lat] = pointData.position;
      const isValid = isFinite(lng) && isFinite(lat) && lng >= -180 && lng <= 180 && lat >= -90 && lat <= 90;

      console.log(`🔍 [AnimationManager] Coordinate from RouteAnimator:`, {
        progress: this.currentProgress,
        position: pointData.position,
        bearing: pointData.bearing,
        segmentIndex: pointData.segmentIndex,
        isValid,
        lng: { value: lng, type: typeof lng, finite: isFinite(lng) },
        lat: { value: lat, type: typeof lat, finite: isFinite(lat) }
      });

      if (isValid) {
        this.vehicleManager.updateVehiclePosition(pointData.position, this.currentBearing);
      } else {
        console.error('❌ [AnimationManager] Invalid coordinates from RouteAnimator:', pointData.position);
      }
      
      // Calculate distances and other context for speed controller
      let distanceToCity: number | undefined = undefined;
      if (this.destinations.length > 0 && this.currentPosition) {
        const distancesToCities = this.destinations.map(dest => 
          dest.coordinates ? turf.distance(turf.point(this.currentPosition as [number, number]), turf.point(dest.coordinates as [number, number]), { units: 'kilometers'}) : Infinity
        );
        distanceToCity = Math.min(...distancesToCities);
      }

      let distanceToPOI: number | undefined = undefined;
      let isDiscoveringPOI = false;
      if (this.pois.length > 0 && this.currentPosition) {
        // Update POI Discovery Manager - this handles POI discovery logic and announcements
        const nearbyPOIs = this.poiDiscoveryManager.updatePosition(this.currentPosition);
        console.log(`${this.LOG_PREFIX} POI Discovery: Found ${nearbyPOIs.length} nearby POIs at position`, this.currentPosition);

        const distancesToPOIs = this.pois.map(poi =>
          poi.coordinates ? turf.distance(turf.point(this.currentPosition as [number, number]), turf.point(poi.coordinates as [number, number]), { units: 'kilometers'}) : Infinity
        );
        distanceToPOI = Math.min(...distancesToPOIs);
        if (distanceToPOI !== undefined && distanceToPOI < 0.5) { // Example: 0.5km threshold for active discovery
          isDiscoveringPOI = true;
        }
      }
      
      const terrainEnum = determineTerrainType(this.currentPosition as [number, number]);
      const initialTerrainString = enumToTerrainType[terrainEnum]; // This is of type TerrainType

      let cscTerrainString: 'default' | 'mountain' | 'desert' | 'coastal' | 'scenic';

      if (initialTerrainString === 'city' || initialTerrainString === 'urban' || initialTerrainString === 'standard') {
        cscTerrainString = 'default';
      } else if (initialTerrainString === 'mountain' || initialTerrainString === 'desert' || initialTerrainString === 'coastal' || initialTerrainString === 'scenic') {
        cscTerrainString = initialTerrainString;
      } else {
        cscTerrainString = 'default';
      }
      
      const isScenic = cscTerrainString === 'scenic';

      this.contextualSpeedController.updateSpeed(
        this.currentPosition as [number, number],
        {
          isNearPOI: distanceToPOI !== undefined && distanceToPOI < 2.0,
          isNearCity: distanceToCity !== undefined && distanceToCity < 5.0,
          terrain: cscTerrainString,
          inScenicArea: isScenic
        }
      );

      if (this.map && this.currentPosition && isValidPosition(this.currentPosition)) {
        // Call CinematicController's position update method, passing panel widths
        this.cinematicController.processPositionUpdate(
          this.currentPosition as [number, number],
          this.currentBearing,
          this.currentProgress,
          this.leftPanelWidth, // Pass stored panel width
          this.rightPanelWidth   // Pass stored panel width
        );
      }

      if (this.onProgressUpdate) {
        this.onProgressUpdate(this.currentProgress, pointData.position, pointData.bearing);
      }
      } else {
      console.error(`${this.LOG_PREFIX} Could not get point data at progress: ${this.currentProgress}`);
      this.animationState = AnimationStateType.ERROR;
      if (this.onAnimationError) this.onAnimationError(new Error('Failed to get point data during animation'));
      if (this.animationFrameId) cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
        return;
      }
      
    if (this.currentProgress >= 1) {
      console.log(`${this.LOG_PREFIX} Animation progress reached 1. Marking as complete.`);
      this.animationState = AnimationStateType.COMPLETED;
      this.vehicleManager.updateVehiclePosition(this.currentPosition!, this.currentBearing);
      if (this.onAnimationComplete) {
        this.onAnimationComplete();
      }
      if (this.animationFrameId) cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
        } else {
      this.animationFrameId = requestAnimationFrame(this.animateFrame);
    }
  }

  // Updated getAnimationState to return a structure that includes AnimationStateType 
  // and known fields from AnimationState, excluding those not directly maintained or complex to derive here.
  public getAnimationState(): { 
    animationStateType: AnimationStateType;
    progress: number;
    isAnimating: boolean;
    isPaused: boolean;
    bearing: number; 
  } {
    return {
      animationStateType: this.animationState,
      progress: this.currentProgress,
      isAnimating: this.animationState === AnimationStateType.PLAYING,
      isPaused: this.animationState === AnimationStateType.PAUSED,
          bearing: this.currentBearing,
    };
  }

  public getCurrentPosition(): Position | null {
    return this.currentPosition;
  }

  public getIsAnimating(): boolean {
    return this.animationState === AnimationStateType.PLAYING;
  }

  public getIsPaused(): boolean {
    return this.animationState === AnimationStateType.PAUSED;
  }

  public dispose(): void {
    console.log(`${this.LOG_PREFIX} Disposing AnimationManager.`);
        this.stopAnimation();
    this.vehicleManager.dispose();
    this.routeAnimator.dispose();
    this.cinematicController.dispose();
    // POIDiscoveryManager doesn't have dispose method - it's a singleton
    this.map = null;
    this.route = [];
    this.onProgressUpdate = null;
    this.onAnimationComplete = null;
    this.onAnimationError = null;
    console.log(`${this.LOG_PREFIX} Disposed.`);
  }
}

export default AnimationManager;
