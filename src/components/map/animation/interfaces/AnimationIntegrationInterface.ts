import { Position } from '../../../../types/Position';
import { AnimationComponentInterface } from './AnimationComponentInterface';
import { AnimationState, AnimationConfig, AnimationEventType } from '../../../../types/AnimationTypes';

/**
 * Progress callback type
 */
export type ProgressCallback = (progress: number, position: Position, bearing: number) => void;

/**
 * Complete callback type
 */
export type CompleteCallback = () => void;

/**
 * Animation phase enum
 */
export enum AnimationPhase {
  INITIAL = 'initial',
  ROUTE_TRAVEL = 'routeTravel',
  POI_DISCOVERY = 'poiDiscovery',
  CITY_EXPLORATION = 'cityExploration',
  COMPLETED = 'completed'
}

/**
 * Interface for the Animation Integration component
 * 
 * The Animation Integration is responsible for:
 * - Providing a unified interface to the animation system for React components
 * - Coordinating between animation components (Manager, VehicleController, etc.)
 * - Managing animation lifecycle and state transitions
 * - Handling high-level animation control commands
 */
export interface AnimationIntegrationInterface extends AnimationComponentInterface {
  /**
   * Initializes the animation integration with config
   * @param config Animation configuration
   */
  initialize(config: Partial<AnimationConfig>): Promise<void>;
  
  /**
   * Prepares a route for animation
   * @param routePoints Array of positions forming the route
   * @param onProgress Callback fired on animation progress
   * @param onComplete Callback fired when animation completes
   */
  prepareRoute(
    routePoints: Position[], 
    onProgress?: ProgressCallback, 
    onComplete?: CompleteCallback
  ): Promise<boolean>;
  
  /**
   * Starts the animation
   */
  startAnimation(): Promise<boolean>;
  
  /**
   * Pauses the animation
   */
  pauseAnimation(): Promise<boolean>;
  
  /**
   * Resumes a paused animation
   */
  resumeAnimation(): Promise<boolean>;
  
  /**
   * Stops the animation completely
   */
  stopAnimation(): Promise<boolean>;
  
  /**
   * Gets the current animation state
   */
  getAnimationState(): AnimationState;
  
  /**
   * Gets the current animation phase
   */
  getAnimationPhase(): AnimationPhase;
  
  /**
   * Sets the animation speed multiplier
   * @param speed Speed multiplier (1.0 is normal speed)
   */
  setAnimationSpeed(speed: number): void;
  
  /**
   * Gets the current animation progress (0-1)
   */
  getProgress(): number;
  
  /**
   * Gets the current vehicle position
   */
  getCurrentPosition(): Position | null;
  
  /**
   * Sets an event handler for animation events
   * @param eventType Type of event to listen for
   * @param handler Function to call when event occurs
   */
  setEventHandler(eventType: string, handler: (data: any) => void): () => void;
  
  /**
   * Enables or disables POI discovery during animation
   * @param enabled Whether POI discovery should be enabled
   */
  enablePOIDiscovery(enabled: boolean): void;
  
  /**
   * Enables or disables contextual speed adjustments
   * @param enabled Whether speed should adjust based on context
   */
  enableContextualSpeed(enabled: boolean): void;
  
  /**
   * Sets the camera follow mode during animation
   * @param mode Camera follow mode
   */
  setCameraFollowMode(mode: string): void;
  
  /**
   * Cleans up animation resources
   */
  cleanup(): void;
} 