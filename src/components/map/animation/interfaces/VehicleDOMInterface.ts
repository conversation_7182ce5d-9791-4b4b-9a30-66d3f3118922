import { Position } from '../../../../types/Position';
import { VehicleStyle } from '../../../../types/VehicleTypes';
import mapboxgl from 'mapbox-gl';

/**
 * Interface for the VehicleDOM component
 * 
 * The VehicleDOM component is responsible for:
 * - Low-level DOM manipulation for the vehicle marker
 * - Creating and updating marker elements
 * - Handling visibility and style changes
 * - Managing the DOM lifecycle of vehicle elements
 * 
 * It should NOT contain any animation logic, state management, or business rules.
 */
export interface VehicleDOMInterface {
  /**
   * Creates a new marker element
   * @param containerId Optional ID for the container element
   * @returns The created HTML element
   */
  createMarkerElement(containerId?: string): HTMLElement;
  
  /**
   * Updates the position of a marker element
   * @param element The marker element
   * @param position Geographic position
   * @param map Mapbox map instance
   */
  updateElementPosition(element: HTMLElement, position: Position, map: mapboxgl.Map): void;
  
  /**
   * Updates the rotation/bearing of a marker element
   * @param element The marker element
   * @param bearing Rotation in degrees
   */
  updateElementBearing(element: HTMLElement, bearing: number): void;
  
  /**
   * Applies styles to a marker element
   * @param element The marker element
   * @param style Style properties to apply
   */
  updateElementStyle(element: HTMLElement, style: Partial<VehicleStyle>): void;
  
  /**
   * Shows a marker element
   * @param element The marker element
   */
  showElement(element: HTMLElement): void;
  
  /**
   * Hides a marker element
   * @param element The marker element
   */
  hideElement(element: HTMLElement): void;
  
  /**
   * Checks if a marker element is visible
   * @param element The marker element
   * @returns Whether the element is visible
   */
  isElementVisible(element: HTMLElement): boolean;
  
  /**
   * Forces visibility for a marker element (for recovery)
   * @param element The marker element
   * @returns Whether visibility was successfully forced
   */
  forceElementVisibility(element: HTMLElement): boolean;
  
  /**
   * Gets the pixel position of a marker on the map
   * @param position Geographic position
   * @param map Mapbox map instance
   * @returns Pixel position {x, y}
   */
  getPixelPosition(position: Position, map: mapboxgl.Map): { x: number; y: number };
  
  /**
   * Creates a marker container element if it doesn't exist
   * @param map Mapbox map instance
   * @param containerId Optional container ID
   * @returns The container element
   */
  ensureMarkerContainer(map: mapboxgl.Map, containerId?: string): HTMLElement;
  
  /**
   * Removes a marker element from the DOM
   * @param element The marker element to remove
   */
  removeElement(element: HTMLElement): void;
  
  /**
   * Gets computed style information about a marker element
   * @param element The marker element
   * @returns Object with computed style properties
   */
  getElementComputedStyle(element: HTMLElement): {
    visibility: string;
    display: string;
    opacity: number;
    transform: string;
    left: string;
    top: string;
  };
} 