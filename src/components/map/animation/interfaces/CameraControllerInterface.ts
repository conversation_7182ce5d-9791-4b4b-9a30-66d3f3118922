import { Position } from '../../../../types/Position';
import mapboxgl from 'mapbox-gl';
import { AnimationComponentInterface } from './AnimationComponentInterface';

/**
 * Simple padding options type to avoid dependency on mapboxgl.PaddingOptions
 */
export interface PaddingOptions {
  top?: number;
  right?: number;
  bottom?: number;
  left?: number;
}

/**
 * Interface for camera animation options
 */
export interface CameraOptions {
  /**
   * Center position for the camera
   */
  center?: Position;
  
  /**
   * Zoom level (0-22)
   */
  zoom?: number;
  
  /**
   * Pitch in degrees (0-60)
   */
  pitch?: number;
  
  /**
   * Bearing in degrees (0-360)
   */
  bearing?: number;
  
  /**
   * Transition duration in milliseconds
   */
  duration?: number;
  
  /**
   * Animation easing function
   */
  easing?: (t: number) => number;
  
  /**
   * Whether the animation is essential (won't be interrupted)
   */
  essential?: boolean;
  
  /**
   * Padding options for camera framing
   */
  padding?: number | PaddingOptions;
}

/**
 * Interface for the Camera Controller
 * 
 * The Camera Controller is responsible for:
 * - Managing map camera movements and transitions
 * - Implementing camera behaviors for different animation contexts
 * - Providing cinematic camera effects
 * - Handling camera positioning relative to moving entities
 */
export interface CameraControllerInterface extends AnimationComponentInterface {
  /**
   * Initialize the camera controller
   * @param map The Mapbox map instance
   */
  initialize(map: mapboxgl.Map): Promise<void>;
  
  /**
   * Clean up resources
   */
  cleanup(): void;
  
  /**
   * Moves the camera to the specified position with options
   * @param options Camera movement options
   */
  moveCamera(options: CameraOptions): Promise<void>;
  
  /**
   * Makes the camera follow a position (e.g., vehicle)
   * @param position Position to follow
   * @param bearing Optional bearing in degrees
   * @param options Additional camera options
   */
  followPosition(position: Position, bearing?: number, options?: Partial<CameraOptions>): Promise<void>;
  
  /**
   * Animates the camera along a series of waypoints
   * @param waypoints Array of positions to move through
   * @param options Camera animation options
   */
  animateAlongPath(waypoints: Position[], options?: Partial<CameraOptions>): Promise<void>;
  
  /**
   * Fits the camera view to contain the specified bounds
   * @param bounds Bounds to fit within view
   * @param options Additional options for the camera movement
   */
  fitBounds(bounds: mapboxgl.LngLatBounds, options?: Partial<CameraOptions>): Promise<void>;
  
  /**
   * Performs a cinematic camera movement around a target position
   * @param center Target position for the cinematic move
   * @param options Camera options including rotation parameters
   */
  cinematicMove(center: Position, options?: Partial<CameraOptions> & { rotationDegrees?: number }): Promise<void>;
  
  /**
   * Stops any ongoing camera animations
   */
  stopAnimation(): void;
  
  /**
   * Sets the default camera behavior for the current context
   * @param context Animation context identifier (e.g., 'routeTravel', 'cityExploration')
   */
  setDefaultBehavior(context: string): void;
  
  /**
   * Gets the current camera position information
   */
  getCurrentCameraState(): CameraProperties;
  
  /**
   * Resets the camera to default parameters
   * @param options Optional parameters for the reset
   */
  resetCamera(options?: Partial<CameraOptions>): Promise<void>;

  /**
   * Sets the current camera mode and behavior
   * @param mode Camera mode identifier
   * @param options Additional options for the mode
   */
  setCameraMode(mode: string, options?: Record<string, any>): void;

  /**
   * Enables or disables smooth transitions
   * @param enabled Whether smooth transitions should be used
   */
  enableSmoothTransitions(enabled: boolean): void;

  /**
   * Handles map viewport changes
   * @param viewport Map viewport properties
   */
  handleViewportChange(viewport: {center?: Position, zoom?: number, bearing?: number, pitch?: number}): void;

  /**
   * Sets camera padding for frame calculations
   * @param padding Padding values or single number
   */
  setPadding(padding: number | PaddingOptions): void;
  
  /**
   * Move camera to a specific position
   * @param position The position to move the camera to
   * @param zoom Optional zoom level
   * @param bearing Optional bearing angle
   * @param pitch Optional pitch angle
   * @param duration Optional transition duration in milliseconds
   */
  moveToPosition(
    position: Position, 
    zoom?: number, 
    bearing?: number, 
    pitch?: number, 
    duration?: number
  ): void;
  
  /**
   * Follow the vehicle during animation
   * @param position Current vehicle position
   * @param bearing Current vehicle bearing
   * @param followMode The camera follow mode
   */
  followVehicle(
    position: Position, 
    bearing: number, 
    followMode: CameraFollowMode
  ): void;
  
  /**
   * Stop following the vehicle
   */
  stopFollowing(): void;
  
  /**
   * Set the default camera parameters
   * @param defaultZoom Default zoom level
   * @param defaultPitch Default pitch angle
   * @param defaultBearing Default bearing offset
   */
  setDefaultParameters(
    defaultZoom: number, 
    defaultPitch: number, 
    defaultBearing: number
  ): void;
  
  /**
   * Fit camera to show a route
   * @param routeCoordinates Array of positions defining the route
   * @param padding Optional padding around the bounds
   * @param duration Optional transition duration
   */
  fitToRoute(
    routeCoordinates: Position[], 
    padding?: number, 
    duration?: number
  ): void;
  
  /**
   * Create a cinematic camera sequence
   * @param keyframes Array of camera keyframes
   */
  createCinematicSequence(keyframes: CameraKeyframe[]): void;
  
  /**
   * Play a cinematic camera sequence
   * @param onComplete Optional callback when sequence completes
   */
  playCinematicSequence(onComplete?: () => void): void;
  
  /**
   * Stop the current cinematic sequence
   */
  stopCinematicSequence(): void;
  
  /**
   * Adjust camera for POI view
   * @param poiPosition Position of the POI
   * @param vehiclePosition Current vehicle position
   */
  adjustForPOI(poiPosition: Position, vehiclePosition: Position): void;
  
  /**
   * Adjust camera for city view
   * @param cityPosition Position of the city
   * @param vehiclePosition Current vehicle position
   */
  adjustForCity(cityPosition: Position, vehiclePosition: Position): void;
  
  /**
   * Set camera bearing offset
   * @param offset Bearing offset in degrees
   */
  setBearingOffset(offset: number): void;
  
  /**
   * Get current camera properties
   * @returns Object containing current camera properties
   */
  getCurrentCamera(): CameraProperties;
}

/**
 * Defines different camera follow modes
 */
export enum CameraFollowMode {
  /**
   * Fixed position directly above vehicle
   */
  OVERHEAD = 'overhead',
  
  /**
   * Follow vehicle with bearing aligned to vehicle direction
   */
  FOLLOW = 'follow',
  
  /**
   * Cinematic view that shows more of the surrounding area
   */
  CINEMATIC = 'cinematic',
  
  /**
   * Wide view of the entire route
   */
  ROUTE_OVERVIEW = 'routeOverview'
}

/**
 * Interface for camera keyframes in cinematic sequences
 */
export interface CameraKeyframe {
  /**
   * Position of the camera
   */
  position: Position;
  
  /**
   * Zoom level
   */
  zoom: number;
  
  /**
   * Bearing angle in degrees
   */
  bearing: number;
  
  /**
   * Pitch angle in degrees
   */
  pitch: number;
  
  /**
   * Duration to reach this keyframe from the previous one
   */
  duration: number;
  
  /**
   * Optional easing function name
   */
  easing?: string;
}

/**
 * Interface for camera properties
 */
export interface CameraProperties {
  /**
   * Current camera center position
   */
  center: Position;
  
  /**
   * Current zoom level
   */
  zoom: number;
  
  /**
   * Current bearing angle
   */
  bearing: number;
  
  /**
   * Current pitch angle
   */
  pitch: number;
} 