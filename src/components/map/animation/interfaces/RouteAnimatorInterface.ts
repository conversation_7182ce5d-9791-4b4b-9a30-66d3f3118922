import { Position } from '../../../../types/Position';
import { TerrainType } from '../../../../types/TerrainType';
import { RoutePoint } from '../../../../types/RoutePoint';
import { AnimationComponentInterface } from './AnimationComponentInterface';

/**
 * Interface for route animation options
 */
export interface RouteAnimationOptions {
  /**
   * Total duration of the animation in milliseconds
   */
  duration?: number;
  
  /**
   * Starting position in the route (0-1)
   */
  startPosition?: number;
  
  /**
   * Whether to loop the animation
   */
  loop?: boolean;
  
  /**
   * Animation speed multiplier
   */
  speedFactor?: number;
  
  /**
   * Callback fired on animation progress
   */
  onProgress?: (progress: number) => void;
  
  /**
   * Callback fired when animation completes
   */
  onComplete?: () => void;
  
  /**
   * Whether to reverse the animation direction
   */
  reverse?: boolean;
}

/**
 * Interface for the RouteAnimator component
 */
export interface RouteAnimatorInterface extends AnimationComponentInterface {
  /**
   * Prepare the route for animation
   */
  prepareRoute(route: Position[], options?: RouteAnimationOptions): Promise<boolean>;
  
  /**
   * Get the closest route point to a given position
   */
  getNearestRoutePoint(position: Position): RoutePoint;
  
  /**
   * Get the current route point for a given progress value
   */
  getRoutePointAtProgress(progress: number): RoutePoint;
  
  /**
   * Get the terrain type at a specific position
   */
  getTerrainAtPosition(position: Position): TerrainType;
  
  /**
   * Calculate the bearing between two positions
   */
  calculateBearing(position1: Position, position2: Position): number;
  
  /**
   * Interpolate between two route points
   */
  interpolateRoutePoints(pointA: RoutePoint, pointB: RoutePoint, factor: number): RoutePoint;
  
  /**
   * Reset the route animator
   */
  reset(): Promise<void>;
}

/**
 * Interface for animation progress event
 */
export interface AnimationProgressEvent {
  /**
   * Current progress (0-1)
   */
  progress: number;
  
  /**
   * Current position
   */
  position: Position;
  
  /**
   * Current bearing
   */
  bearing: number;
  
  /**
   * Current terrain type
   */
  terrain: TerrainType;
  
  /**
   * Elapsed time in milliseconds
   */
  elapsedTime: number;
  
  /**
   * Current point index in the route
   */
  pointIndex: number;
} 