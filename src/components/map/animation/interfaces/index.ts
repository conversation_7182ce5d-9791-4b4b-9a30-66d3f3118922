/**
 * Animation Interfaces Index File
 * 
 * This file exports all animation interfaces from a single location to simplify imports
 * and maintain a consistent interface structure across the animation system.
 */

// Core component interface
export type { AnimationComponentInterface } from './AnimationComponentInterface';

// Manager interfaces
export type { AnimationManagerInterface, RoutePreparationOptions } from './AnimationManagerInterface';
export type { VehicleManagerInterface } from './VehicleManagerInterface';
export type { VehicleControllerInterface } from './VehicleControllerInterface';
export type { VehicleDOMInterface } from './VehicleDOMInterface';
export type { CameraControllerInterface } from './CameraControllerInterface';
export type { RouteAnimatorInterface, RouteAnimationOptions } from './RouteAnimatorInterface';
export type { CinematicControllerInterface } from './CinematicControllerInterface';
export type { AnimationIntegrationInterface } from './AnimationIntegrationInterface';

// Common types and interfaces
export type { CameraFollowMode, CameraKeyframe, CameraProperties, PaddingOptions } from './CameraControllerInterface';
export type { <PERSON>Phase, <PERSON>Callback, CompleteCallback } from './AnimationIntegrationInterface'; 