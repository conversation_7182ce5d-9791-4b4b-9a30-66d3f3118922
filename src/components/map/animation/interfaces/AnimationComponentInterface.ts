import { AnimationEventCallback, AnimationEventType } from "../../../../types/AnimationEventTypes";
import mapboxgl from 'mapbox-gl';

/**
 * Base interface for all animation components
 * 
 * This interface defines the common methods that all animation
 * components must implement.
 */
export interface AnimationComponentInterface {
  /**
   * Initialize the component
   * 
   * @param mapOrConfig Map instance or configuration object
   * @param options Additional initialization options
   */
  initialize(mapOrConfig: mapboxgl.Map | Record<string, any>, options?: Record<string, any>): void | Promise<any>;
  
  /**
   * Check if the component is initialized
   */
  isInitialized(): boolean;
  
  /**
   * Reset the component state
   */
  reset(): Promise<void>;
  
  /**
   * Clean up resources and prepare for disposal
   */
  dispose(): void;

  /**
   * Register a callback for animation events
   */
  addEventListener(eventType: AnimationEventType, callback: AnimationEventCallback): () => void;

  /**
   * Remove a previously registered event listener
   */
  removeEventListener(eventType: AnimationEventType, callback: AnimationEventCallback): boolean;
}
