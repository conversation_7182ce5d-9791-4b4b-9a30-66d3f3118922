import { Position } from '../../../../types/Position';
import { PointOfInterest } from '../../../../types/POITypes';
import { Destination } from '../../../../types/destination';
import { AnimationComponentInterface } from './AnimationComponentInterface';
import mapboxgl from 'mapbox-gl';

/**
 * Interface for cinematic sequence options
 */
export interface CinematicSequenceOptions {
  /**
   * Duration of the sequence in milliseconds
   */
  duration?: number;
  
  /**
   * Whether the sequence can be interrupted by user interaction
   */
  interruptible?: boolean;
  
  /**
   * Callback to run when the sequence completes
   */
  onComplete?: () => void;
  
  /**
   * Callback to run if the sequence is aborted
   */
  onAbort?: () => void;
}

/**
 * Configuration for initializing the cinematic controller
 */
export interface CinematicControllerConfig {
  /**
   * The map instance
   */
  map: any;
  
  /**
   * Optional route coordinates
   */
  route?: Position[];
  
  /**
   * Optional points of interest
   */
  pois?: PointOfInterest[];
  
  /**
   * Optional destinations
   */
  destinations?: Destination[];
  
  /**
   * Additional options
   */
  options?: Record<string, any>;
}

/**
 * Camera mode for cinematic sequences
 */
export enum CameraMode {
  FOLLOW = 'follow',
  FIXED = 'fixed',
  ORBIT = 'orbit',
  OVERVIEW = 'overview',
  POI_FOCUS = 'poiFocus',
  CINEMATIC = 'cinematic'
}

/**
 * Camera keyframe for cinematic sequences
 */
export interface CameraKeyframe {
  position: Position;
  zoom: number;
  bearing: number;
  pitch: number;
  duration: number;
  easingFn?: string;
}

/**
 * Interface for cinematic sequences
 */
export interface CinematicSequence {
  id: string;
  name: string;
  keyframes: CameraKeyframe[];
  loop?: boolean;
  autoStart?: boolean;
  triggerDistance?: number;
  startPosition?: Position;
  endPosition?: Position;
}

/**
 * Options for POI focus
 */
export interface POIFocusOptions {
  duration?: number;
  zoom?: number;
  pitch?: number;
  bearing?: number;
  offset?: number[];
  showInfo?: boolean;
  autoReturn?: boolean;
}

/**
 * Options for city overview
 */
export interface CityOverviewOptions {
  duration?: number;
  zoom?: number;
  pitch?: number;
  bearing?: number;
  circleCity?: boolean;
  showHighlights?: boolean;
}

/**
 * Interface for the Cinematic Controller
 * 
 * The Cinematic Controller is responsible for:
 * - Creating cinematic camera sequences for key moments
 * - Coordinating visual effects during animation transitions
 * - Managing special cinematic moments (city approach, POI discovery)
 * - Enhancing the user experience with visual storytelling
 */
export interface CinematicControllerInterface extends AnimationComponentInterface {
  /**
   * Initializes the cinematic controller
   * @param config Configuration object
   */
  initialize(config: CinematicControllerConfig): void;
  
  /**
   * Plays an introductory cinematic sequence for the journey
   * @param startPosition Starting position for the sequence
   * @param options Cinematic sequence options
   */
  playJourneyIntroSequence(
    startPosition: Position,
    options?: CinematicSequenceOptions
  ): Promise<void>;
  
  /**
   * Creates a cinematic sequence for approaching a city
   * @param city City/destination information
   * @param currentPosition Current vehicle position
   * @param options Sequence options
   */
  playCityApproachSequence(
    city: Destination,
    currentPosition: Position,
    options?: CinematicSequenceOptions
  ): Promise<void>;
  
  /**
   * Creates a cinematic sequence for discovering a POI
   * @param poi Point of interest
   * @param currentPosition Current vehicle position
   * @param options Sequence options
   */
  playPOIDiscoverySequence(
    poi: PointOfInterest,
    currentPosition: Position,
    options?: CinematicSequenceOptions
  ): Promise<void>;
  
  /**
   * Creates a cinematic sequence for journey completion
   * @param finalPosition Final position of the journey
   * @param options Sequence options
   */
  playJourneyCompletionSequence(
    finalPosition: Position,
    options?: CinematicSequenceOptions
  ): Promise<void>;
  
  /**
   * Aborts any currently playing cinematic sequence
   */
  abortCurrentSequence(): void;
  
  /**
   * Checks if a cinematic sequence is currently playing
   */
  isSequencePlaying(): boolean;
  
  /**
   * Handles animation progress events from the AnimationManager
   * @param progress Current animation progress (0-1)
   * @param position Current position
   * @param bearing Current bearing
   */
  handleAnimationProgress(progress: number, position: Position, bearing: number): void;
  
  /**
   * Enables or disables cinematic mode
   * @param enabled Whether cinematic mode should be enabled
   */
  setCinematicModeEnabled(enabled: boolean): void;
  
  /**
   * Gets whether cinematic mode is currently enabled
   */
  isCinematicModeEnabled(): boolean;
  
  /**
   * Triggers a cinematic sequence highlighting a POI
   * @param poi The point of interest to highlight
   */
  highlightPOI(poi: PointOfInterest): Promise<void>;
  
  /**
   * Creates a city approach cinematic sequence
   * @param destination The destination to approach
   */
  approachCity(destination: Destination): Promise<void>;
  
  /**
   * Stops any ongoing cinematic sequence
   */
  stopCurrentSequence(): void;
  
  /**
   * Creates a custom cinematic sequence
   * @param waypoints An array of positions to use in the sequence
   * @param options Additional sequence options
   */
  createCustomSequence(waypoints: Position[], options?: any): Promise<void>;
  
  /**
   * Pauses or resumes the current cinematic sequence
   * @param isPaused Whether to pause or resume
   */
  setPaused(isPaused: boolean): void;
  
  /**
   * Clean up resources when controller is no longer needed
   */
  dispose(): void;

  /**
   * Enables or disables debugging visualizations
   * @param enabled Whether debug mode is enabled
   */
  setDebugMode(enabled: boolean): void;

  /**
   * Registers custom handlers for cinematic events
   * @param eventType Type of cinematic event
   * @param handler Handler function
   */
  registerEventHandler(eventType: string, handler: (data: any) => void): () => void;

  /**
   * Sets options for all cinematic sequences
   * @param options Global cinematic sequence options
   */
  setGlobalOptions(options: Partial<CinematicSequenceOptions>): void;

  /**
   * Focus the camera on a POI
   */
  focusOnPOI(poi: PointOfInterest, options?: POIFocusOptions): Promise<boolean>;
  
  /**
   * Show an overview of a city
   */
  showCityOverview(city: Destination, options?: CityOverviewOptions): Promise<boolean>;
  
  /**
   * Play a cinematic sequence
   */
  playSequence(sequence: CinematicSequence): Promise<boolean>;
  
  /**
   * Stop the current cinematic sequence
   */
  stopSequence(): void;
  
  /**
   * Set the camera mode
   */
  setCameraMode(mode: CameraMode): void;
  
  /**
   * Get the current camera mode
   */
  getCameraMode(): CameraMode;
  
  /**
   * Reset the cinematic controller
   */
  reset(): Promise<void>;
} 