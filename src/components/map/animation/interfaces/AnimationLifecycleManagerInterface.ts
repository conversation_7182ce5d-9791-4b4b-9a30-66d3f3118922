/**
 * AnimationLifecycleManagerInterface.ts
 * 
 * Interface for the AnimationLifecycleManager component.
 * Defines the contract for managing animation lifecycle phases.
 */

import { AnimationEventType, EventData } from '../../../../types/AnimationEventTypes';
import { Position } from '../../../../types/Position';
import { AnimationPhase } from '../../../../types/AnimationTypes';
import { AnimationComponentInterface } from './AnimationComponentInterface';
import mapboxgl from 'mapbox-gl';

/**
 * Interface for the Animation Lifecycle Manager
 * 
 * The Animation Lifecycle Manager is responsible for:
 * - Managing the animation phases
 * - Coordinating component initialization
 * - Running the animation loop
 * - Emitting lifecycle events
 */
export interface AnimationLifecycleManagerInterface extends AnimationComponentInterface {
  /**
   * Initialize the lifecycle manager with a map instance
   * @param map Mapbox map instance
   * @returns Promise resolving to success status
   */
  initialize(map: mapboxgl.Map): Promise<boolean>;
  
  /**
   * Register a component with the lifecycle manager
   * @param componentId Unique identifier for the component
   */
  registerComponent(componentId: string): void;
  
  /**
   * Report that a component is ready for animation
   * @param componentId Unique identifier for the component
   */
  reportComponentReady(componentId: string): void;
  
  /**
   * Prepare the animation with route data
   * @param route Array of positions defining the route
   * @param duration Duration of the animation in milliseconds
   * @returns Promise resolving to success status
   */
  prepareAnimation(route: Position[], duration: number): Promise<boolean>;
  
  /**
   * Start the animation
   * @returns Promise resolving to success status
   */
  startAnimation(): Promise<boolean>;
  
  /**
   * Pause the animation
   * @returns Promise resolving to success status
   */
  pauseAnimation(): Promise<boolean>;
  
  /**
   * Resume the animation from pause
   * @returns Promise resolving to success status
   */
  resumeAnimation(): Promise<boolean>;
  
  /**
   * Stop the animation
   * @returns Promise resolving to success status
   */
  stopAnimation(): Promise<boolean>;
  
  /**
   * Reset the animation to initial state
   * @returns Promise resolving to success status
   */
  resetAnimation(): Promise<boolean>;
  
  /**
   * Get the current animation phase
   * @returns Current phase
   */
  getPhase(): AnimationPhase;
  
  /**
   * Get the current animation progress
   * @returns Progress as a value between 0 and 1
   */
  getProgress(): number;
  
  /**
   * Set the animation progress directly
   * @param progress Progress as a value between 0 and 1
   * @returns Promise resolving to success status
   */
  setProgress(progress: number): Promise<boolean>;
  
  /**
   * Add an event listener
   * @param eventType Type of event to listen for
   * @param callback Function to call when event occurs
   * @returns Function to remove the listener
   */
  addEventListener(
    eventType: AnimationEventType, 
    callback: (data: EventData) => void
  ): () => void;
  
  /**
   * Remove an event listener
   * @param eventType Type of event
   * @param callback Function to remove
   * @returns Whether the listener was successfully removed
   */
  removeEventListener(
    eventType: AnimationEventType, 
    callback: (data: EventData) => void
  ): boolean;
  
  /**
   * Clean up resources used by the lifecycle manager
   */
  dispose(): void;
} 