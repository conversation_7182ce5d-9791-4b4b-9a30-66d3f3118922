/**
 * AnimationController.tsx
 * 
 * Core Animation Controller
 * ------------------------
 * 
 * RESPONSIBILITY:
 * This component handles the core animation loop for route travel animations,
 * managing the vehicle's position, speed adjustments, and contextual awareness.
 * It serves as the central coordinator for animation timing, progress events,
 * and environmental interactions.
 * 
 * KEY FUNCTIONALITY:
 * - Implements the main animation loop using requestAnimationFrame
 * - Calculates position interpolation along route points
 * - Dispatches animation events (start, progress, complete)
 * - Detects and notifies about nearby points of interest
 * - Applies contextual speed adjustments based on terrain and surroundings
 * - Manages direction indicators for significant route changes
 * - Coordinates camera behavior through contextual rhythm patterns
 * 
 * EVENT DISPATCHING:
 * - ANIMATION_START: When animation begins
 * - ANIMATION_PROGRESS: At regular intervals and key milestones (25%, 50%, 75%)
 * - ANIMATION_COMPLETE: When animation reaches 100% completion
 * 
 * INTEGRATION POINTS:
 * - Works with ComponentInteractionManager for event dispatching
 * - Uses ContextualSpeedController for terrain-aware speed adjustments
 * - Utilizes ContextualRhythm for camera behavior coordination
 * - Interfaces with POI and city detection systems
 * 
 * PROPS:
 * - map: The Mapbox GL map instance
 * - routePoints: Array of [lng, lat] coordinates defining the route
 * - isAnimating: Boolean to control animation state
 * - onAnimationComplete: Callback function when animation completes
 * - onPOIDiscovered: Callback function when new POI is encountered
 * - destinations: Array of destination cities along the route
 * - pois: Array of points of interest near the route
 * - routeClusters: Groups of related POIs along the route
 * - onPositionUpdate: Callback for position changes during animation
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import mapboxgl from 'mapbox-gl';
import * as turf from '@turf/turf';
import { Destination } from '@/types/destination';
import { ExtendedPointOfInterest, RouteCluster } from '../clusters/ClusterGenerator';
import { createContextualRhythmHandler } from './ContextualRhythm';
import ContextualSpeedController from './ContextualSpeedController';
import { getApproachingPOI, createPOINotification, getDirectionIndicator, createDirectionIndicator } from './AwarenessIndicators';
import { ANIMATION_DURATION } from '../utils/types';
import ComponentInteractionManager, { AnimationEventType } from './ComponentInteractionManager';

interface AnimationControllerProps {
  map: mapboxgl.Map | null;
  routePoints: [number, number][];
  isAnimating: boolean;
  onAnimationComplete: () => void;
  onPOIDiscovered: (poi: ExtendedPointOfInterest) => void;
  destinations?: Destination[];
  pois?: ExtendedPointOfInterest[];
  routeClusters?: Record<string, RouteCluster>;
  onPositionUpdate?: (position: [number, number], progress: number) => void;
}

const AnimationController: React.FC<AnimationControllerProps> = ({
  map,
  routePoints,
  isAnimating,
  onAnimationComplete,
  onPOIDiscovered,
  destinations = [],
  pois = [],
  routeClusters = {},
  onPositionUpdate
}) => {
  // Animation state
  const [progress, setProgress] = useState(0);
  const [currentPosition, setCurrentPosition] = useState<[number, number] | null>(null);
  
  // Animation refs
  const animationFrameRef = useRef<number | null>(null);
  const startTimeRef = useRef<number | null>(null);
  const currentRouteIndexRef = useRef(0);
  const lastTimestampRef = useRef<number | null>(null);
  const totalDistanceRef = useRef(0);
  
  // POI tracking refs
  const notifiedPOIRef = useRef<Set<string>>(new Set());
  const currentPOIRef = useRef<ExtendedPointOfInterest | null>(null);
  const poiNotificationElementRef = useRef<HTMLElement | null>(null);
  const directionIndicatorElementRef = useRef<HTMLElement | null>(null);
  
  // Create context handlers 
  const rhythmHandlerRef = useRef(createContextualRhythmHandler(destinations));

  // Initialize total distance when route points change
  useEffect(() => {
    if (routePoints && routePoints.length > 1) {
      let totalDistance = 0;
      for (let i = 1; i < routePoints.length; i++) {
        totalDistance += turf.distance(
          turf.point(routePoints[i - 1]),
          turf.point(routePoints[i]),
          { units: 'kilometers' }
        );
      }
      totalDistanceRef.current = totalDistance;
    }
  }, [routePoints]);

  // Clean up notifications when unmounting
  useEffect(() => {
    return () => {
      if (poiNotificationElementRef.current) {
        document.body.removeChild(poiNotificationElementRef.current);
      }
      if (directionIndicatorElementRef.current) {
        document.body.removeChild(directionIndicatorElementRef.current);
      }
    };
  }, []);

  // Animation loop function
  const animate = useCallback((timestamp: number) => {
    if (!map || !routePoints || routePoints.length < 2) return;
    
    // Initialize start time on first frame
    if (startTimeRef.current === null) {
      startTimeRef.current = timestamp;
      lastTimestampRef.current = timestamp;
      currentRouteIndexRef.current = 0;
      console.log('Animation started at timestamp:', timestamp);
      
      // Keep only one initial animation start event
      try {
        const manager = ComponentInteractionManager.getInstance();
        manager.dispatchAnimationEvent(AnimationEventType.ANIMATION_START, {
          timestamp: Date.now(),
          message: 'Animation started from AnimationController'
        });
        console.log('✅ Animation START event dispatched');
      } catch (e) {
        console.error('❌ Failed to dispatch event:', e);
      }
    }
    
    // Calculate elapsed time
    const elapsedTime = timestamp - startTimeRef.current;
    const frameDuration = lastTimestampRef.current ? timestamp - lastTimestampRef.current : 0;
    lastTimestampRef.current = timestamp;
    
    // Calculate animation progress (0 to 1)
    const animationProgress = Math.min(elapsedTime / ANIMATION_DURATION, 1);
    setProgress(animationProgress);
    
    // Get current and next point indices
    const pointCount = routePoints.length;
    const targetIndex = Math.min(Math.floor(animationProgress * (pointCount - 1)), pointCount - 2);
    
    // Update route index if changed
    if (targetIndex !== currentRouteIndexRef.current) {
      currentRouteIndexRef.current = targetIndex;
    }
    
    // Get current and next points
    const currentPoint = routePoints[targetIndex];
    const nextPoint = routePoints[targetIndex + 1];
    
    // Calculate position between current and next point
    const segmentProgress = (animationProgress * (pointCount - 1)) % 1;
    
    // Get the ContextualSpeedController instance
    const speedController = ContextualSpeedController.getInstance();
    
    // Transform points to the format expected by ContextualSpeedController
    const position = { lat: currentPoint[1], lng: currentPoint[0] };
    
    // Get nearby POIs for the speed controller
    const nearbyPOIs = pois.filter(poi => {
      if (!poi.coordinates) return false;
      const distance = turf.distance(
        turf.point(currentPoint),
        turf.point(poi.coordinates as [number, number]),
        { units: 'kilometers' }
      );
      return distance < 5; // POIs within 5km
    });
    
    // Get nearby cities for the speed controller
    const nearbyCities = destinations.filter(city => {
      if (!city.coordinates) return false;
      const distance = turf.distance(
        turf.point(currentPoint),
        turf.point(city.coordinates as [number, number]),
        { units: 'kilometers' }
      );
      return distance < 10; // Cities within 10km
    });
    
    // Determine terrain type
    const terrainType = determineTerrain(currentPoint);
    
    // Update context before getting speed multiplier
    speedController.updateContext({
      inCity: nearbyCities.length > 0,
      nearPOI: nearbyPOIs.length > 0,
      terrain: terrainType as 'default' | 'mountain' | 'desert' | 'coastal'
    });
    
    // Apply speed modifier based on context
    const speedModifier = speedController.getSpeedMultiplier();
    
    // Adjust segment progress by speed modifier
    const adjustedSegmentProgress = segmentProgress * speedModifier;
    
    // Interpolate position
    const interpolatedPosition: [number, number] = [
      currentPoint[0] + (nextPoint[0] - currentPoint[0]) * adjustedSegmentProgress,
      currentPoint[1] + (nextPoint[1] - currentPoint[1]) * adjustedSegmentProgress
    ];
    
    // Update position state
    setCurrentPosition(interpolatedPosition);
    
    // Call position update callback if provided
    if (onPositionUpdate) {
      onPositionUpdate(interpolatedPosition, animationProgress);
    }
    
    // Check for nearby POIs
    checkNearbyPOIs(interpolatedPosition, nextPoint);
    
    // Check for upcoming direction changes
    checkDirectionChanges(interpolatedPosition, routePoints.slice(targetIndex));
    
    // Apply contextual rhythm for camera behavior
    applyContextualRhythm(interpolatedPosition, elapsedTime);
    
    // Complete the animation when we reach the end
    if (animationProgress >= 1) {
      if (animationFrameRef.current !== null) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
      
      // Keep only the final animation complete event
      try {
        const manager = ComponentInteractionManager.getInstance();
        manager.dispatchAnimationEvent(AnimationEventType.ANIMATION_COMPLETE, {
          timestamp: Date.now(),
          message: 'Animation completed from AnimationController'
        });
        console.log('✅ Animation COMPLETE event dispatched');
      } catch (e) {
        console.error('❌ Failed to dispatch completion event:', e);
      }
      
      onAnimationComplete();
      return;
    }
    
    // Continue animation
    if (isAnimating) {
      animationFrameRef.current = requestAnimationFrame(animate);
    }
  }, [map, routePoints, isAnimating, onAnimationComplete, destinations, pois, routeClusters, onPositionUpdate]);

  // Start or stop animation based on isAnimating prop
  useEffect(() => {
    // Guard against duplicate animations by checking if one is already running
    if (isAnimating && animationFrameRef.current === null) {
      // Reset state for new animation
      startTimeRef.current = null;
      notifiedPOIRef.current = new Set();
      
      // Clear any existing animation frame (just to be safe)
      if (animationFrameRef.current !== null) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
      
      console.log('🚗 Starting fresh animation', { 
        routePointsLength: routePoints.length,
        isFirstAnimation: startTimeRef.current === null
      });
      
      // Start the animation
      animationFrameRef.current = requestAnimationFrame(animate);
    } else if (!isAnimating && animationFrameRef.current !== null) {
      console.log('🛑 Stopping animation');
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }
    
    // Clean up on unmount
    return () => {
      if (animationFrameRef.current !== null) {
        console.log('🧹 Cleaning up animation on unmount/effect change');
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
    };
  }, [isAnimating, animate]);

  // Check for nearby POIs and show notifications
  const checkNearbyPOIs = useCallback((currentPoint: [number, number], nextPoint: [number, number]) => {
    try {
      // Get notification for nearby POIs
      const notification = getApproachingPOI(
        currentPoint,
        nextPoint,
        pois,
        notifiedPOIRef.current
      );
      
      if (notification) {
        // Mark as notified
        notifiedPOIRef.current.add(notification.poi.id);
        
        // Create and show notification
        const notificationElement = createPOINotification(notification);
        
        // Add close button
        const closeBtn = document.createElement('button');
        closeBtn.innerHTML = '×';
        closeBtn.style.position = 'absolute';
        closeBtn.style.right = '8px';
        closeBtn.style.top = '8px';
        closeBtn.style.background = 'none';
        closeBtn.style.border = 'none';
        closeBtn.style.fontSize = '20px';
        closeBtn.style.cursor = 'pointer';
        closeBtn.style.opacity = '0.6';
        closeBtn.onclick = () => {
          notificationElement.remove();
          if (poiNotificationElementRef.current === notificationElement) {
            poiNotificationElementRef.current = null;
          }
        };
        notificationElement.appendChild(closeBtn);
        
        document.body.appendChild(notificationElement);
        poiNotificationElementRef.current = notificationElement;
        
        // Notify about the discovered POI
        onPOIDiscovered(notification.poi);
      }
    } catch (error) {
      console.error('Error checking nearby POIs:', error);
    }
  }, [pois, onPOIDiscovered]);

  // Check for upcoming direction changes
  const checkDirectionChanges = useCallback((currentPoint: [number, number], upcomingPoints: [number, number][]) => {
    // Remove existing indicator if present
    if (directionIndicatorElementRef.current) {
      directionIndicatorElementRef.current.remove();
      directionIndicatorElementRef.current = null;
    }
    
    try {
      // Get direction indicator for upcoming turns
      const indicator = getDirectionIndicator(currentPoint, upcomingPoints);
      
      if (indicator) {
        // Create and show direction indicator
        const indicatorElement = createDirectionIndicator(indicator);
        
        // Add close button
        const closeBtn = document.createElement('button');
        closeBtn.innerHTML = '×';
        closeBtn.style.position = 'absolute';
        closeBtn.style.right = '8px';
        closeBtn.style.top = '8px';
        closeBtn.style.background = 'none';
        closeBtn.style.border = 'none';
        closeBtn.style.fontSize = '20px';
        closeBtn.style.cursor = 'pointer';
        closeBtn.style.opacity = '0.6';
        closeBtn.onclick = () => {
          indicatorElement.remove();
          if (directionIndicatorElementRef.current === indicatorElement) {
            directionIndicatorElementRef.current = null;
          }
        };
        indicatorElement.appendChild(closeBtn);
        
        document.body.appendChild(indicatorElement);
        directionIndicatorElementRef.current = indicatorElement;
      }
    } catch (error) {
      console.error('Error checking direction changes:', error);
    }
  }, []);

  // Apply contextual rhythm for camera behavior
  const applyContextualRhythm = useCallback((currentPoint: [number, number], elapsedTime: number) => {
    if (!map) return;
    
    try {
      // Get rhythm parameters based on current context
      const rhythmParams = rhythmHandlerRef.current(currentPoint, elapsedTime);
      
      if (rhythmParams) {
        // Apply camera movement
        map.easeTo({
          center: rhythmParams.center,
          zoom: rhythmParams.zoom,
          duration: rhythmParams.duration,
          bearing: rhythmParams.bearing
        });
      }
    } catch (error) {
      console.error('Error applying contextual rhythm:', error);
    }
  }, [map]);

  // Add a function to determine terrain type
  const determineTerrain = (point: [number, number]): string => {
    // In a full implementation, this would use map data or terrain database
    // For now, a simplified version based on Morocco's geography
    const [lng, lat] = point;
    
    // Atlas Mountains
    if ((lng > -8 && lng < -5 && lat > 30 && lat < 33) || 
        (lng > -5 && lng < -2 && lat > 33 && lat < 35)) {
      return 'mountain';
    }
    
    // Sahara Desert
    if (lat < 30 && lng > -7) {
      return 'desert';
    }
    
    // Coastal areas
    if ((lng < -5 && lat > 33) || (lng > -3 && lat > 35)) {
      return 'coastal';
    }
    
    // Default
    return 'default';
  };

  // Component doesn't render anything visible directly
  return null;
};

export default AnimationController; 