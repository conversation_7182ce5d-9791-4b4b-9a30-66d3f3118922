/**
 * AnimationErrorHandler.ts
 * 
 * Provides systematic error handling for the animation system.
 * Centralizes error capturing, logging, and recovery strategies.
 */

import { AnimationEventType } from '@/types/AnimationEventTypes';
import { AnimationPhase } from '@/types/AnimationTypes';
import AnimationDebugTools from './AnimationDebugTools';

/**
 * Error categories for animation system
 */
export enum AnimationErrorCategory {
  INITIALIZATION = 'initialization',
  STATE_TRANSITION = 'state_transition',
  RESOURCE_LOADING = 'resource_loading',
  ANIMATION_LOOP = 'animation_loop',
  COMPONENT_COMMUNICATION = 'component_communication',
  DOM_MANIPULATION = 'dom_manipulation',
  MAPBOX_INTERACTION = 'mapbox_interaction',
  VEHICLE_MARKER = 'vehicle_marker',
  ROUTE_CALCULATION = 'route_calculation',
  EVENT_HANDLING = 'event_handling'
}

/**
 * Error severity levels
 */
export enum ErrorSeverity {
  LOW = 'low',         // Non-critical, can continue with degraded functionality
  MEDIUM = 'medium',   // Significant but contained issue, may need recovery
  HIGH = 'high',       // Critical component failure, needs immediate intervention
  FATAL = 'fatal'      // System cannot continue, requires full restart
}

/**
 * Error context information
 */
export interface ErrorContext {
  category: AnimationErrorCategory;
  severity: ErrorSeverity;
  component: string;
  phase: AnimationPhase;
  timestamp: number;
  operation: string;
  data?: Record<string, any>;
}

/**
 * Recovery action to be taken
 */
export interface RecoveryAction {
  action: string;
  description: string;
  execute: () => Promise<boolean>;
}

/**
 * Animation error with context and recovery options
 */
export class AnimationError extends Error {
  public readonly context: ErrorContext;
  public readonly recoveryActions: RecoveryAction[];
  
  constructor(
    message: string,
    context: ErrorContext,
    recoveryActions: RecoveryAction[] = []
  ) {
    super(message);
    this.name = 'AnimationError';
    this.context = context;
    this.recoveryActions = recoveryActions;
  }
}

/**
 * Handler for animation system errors
 */
export class AnimationErrorHandler {
  private static instance: AnimationErrorHandler | null = null;
  private errorLog: AnimationError[] = [];
  private maxLogSize: number = 50;
  private recoveryListeners: Map<AnimationErrorCategory, Array<(error: AnimationError) => Promise<boolean>>> = new Map();
  
  /**
   * Private constructor for singleton pattern
   */
  private constructor() {
    // Initialize recovery listeners map for all categories
    Object.values(AnimationErrorCategory).forEach(category => {
      this.recoveryListeners.set(category as AnimationErrorCategory, []);
    });
  }
  
  /**
   * Get singleton instance
   */
  public static getInstance(): AnimationErrorHandler {
    if (!this.instance) {
      this.instance = new AnimationErrorHandler();
    }
    return this.instance;
  }
  
  /**
   * Handle an animation error
   */
  public async handleError(error: AnimationError): Promise<boolean> {
    // Log the error
    this.logError(error);
    
    // Emit error event through debug tools
    AnimationDebugTools.log('error', `Animation error: ${error.message}`, {
      context: error.context,
      stack: error.stack
    });
    
    // For fatal errors, we don't attempt recovery
    if (error.context.severity === ErrorSeverity.FATAL) {
      console.error('FATAL ANIMATION ERROR - SYSTEM CANNOT RECOVER:', error);
      return false;
    }
    
    // Attempt recovery
    return await this.attemptRecovery(error);
  }
  
  /**
   * Create a new animation error
   */
  public createError(
    message: string,
    category: AnimationErrorCategory,
    severity: ErrorSeverity,
    component: string,
    phase: AnimationPhase,
    operation: string,
    data?: Record<string, any>
  ): AnimationError {
    // Create context
    const context: ErrorContext = {
      category,
      severity,
      component,
      phase,
      timestamp: Date.now(),
      operation,
      data
    };
    
    // Create recovery actions based on category and severity
    const recoveryActions = this.getRecoveryActions(context);
    
    // Create and return the error
    return new AnimationError(message, context, recoveryActions);
  }
  
  /**
   * Log error to internal log
   */
  private logError(error: AnimationError): void {
    // Add to circular buffer
    this.errorLog.push(error);
    
    // Trim if necessary
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog.shift();
    }
  }
  
  /**
   * Get all logged errors
   */
  public getErrorLog(): AnimationError[] {
    return [...this.errorLog];
  }
  
  /**
   * Clear error log
   */
  public clearErrorLog(): void {
    this.errorLog = [];
  }
  
  /**
   * Register a recovery handler for a specific error category
   */
  public registerRecoveryHandler(
    category: AnimationErrorCategory,
    handler: (error: AnimationError) => Promise<boolean>
  ): () => void {
    // Get handlers for this category
    const handlers = this.recoveryListeners.get(category) || [];
    
    // Add handler
    handlers.push(handler);
    this.recoveryListeners.set(category, handlers);
    
    // Return function to unregister
    return () => {
      const currentHandlers = this.recoveryListeners.get(category) || [];
      const index = currentHandlers.indexOf(handler);
      if (index !== -1) {
        currentHandlers.splice(index, 1);
        this.recoveryListeners.set(category, currentHandlers);
      }
    };
  }
  
  /**
   * Attempt to recover from an error
   */
  private async attemptRecovery(error: AnimationError): Promise<boolean> {
    try {
      // Get handlers for this category
      const handlers = this.recoveryListeners.get(error.context.category) || [];
      
      // Log recovery attempt
      AnimationDebugTools.log('info', `Attempting recovery for ${error.context.category} error`, {
        message: error.message,
        severity: error.context.severity,
        handlers: handlers.length
      });
      
      // Try built-in recovery actions first
      for (const action of error.recoveryActions) {
        try {
          AnimationDebugTools.log('info', `Executing recovery action: ${action.action}`, {
            description: action.description
          });
          
          const success = await action.execute();
          if (success) {
            AnimationDebugTools.log('info', `Recovery action ${action.action} succeeded`);
            return true;
          }
        } catch (recoveryError) {
          AnimationDebugTools.log('error', `Recovery action ${action.action} failed`, recoveryError);
        }
      }
      
      // Try registered handlers
      for (const handler of handlers) {
        try {
          const success = await handler(error);
          if (success) {
            AnimationDebugTools.log('info', 'Recovery handler succeeded');
            return true;
          }
        } catch (handlerError) {
          AnimationDebugTools.log('error', 'Recovery handler failed', handlerError);
        }
      }
      
      // If we get here, recovery failed
      AnimationDebugTools.log('warn', `Failed to recover from ${error.context.category} error`);
      return false;
    } catch (recoveryError) {
      AnimationDebugTools.log('error', 'Error during recovery attempt', recoveryError);
      return false;
    }
  }
  
  /**
   * Get appropriate recovery actions based on error context
   */
  private getRecoveryActions(context: ErrorContext): RecoveryAction[] {
    const actions: RecoveryAction[] = [];
    
    switch (context.category) {
      case AnimationErrorCategory.VEHICLE_MARKER:
        // Add vehicle marker recovery actions
        actions.push({
          action: 'recreate_vehicle_marker',
          description: 'Attempt to recreate the vehicle marker',
          execute: async () => {
            // This is a placeholder for the actual implementation
            console.log('Executing vehicle marker recovery');
            return true;
          }
        });
        break;
        
      case AnimationErrorCategory.ANIMATION_LOOP:
        // Add animation loop recovery actions
        actions.push({
          action: 'restart_animation_loop',
          description: 'Attempt to restart the animation loop',
          execute: async () => {
            // This is a placeholder for the actual implementation
            console.log('Executing animation loop recovery');
            return true;
          }
        });
        break;
        
      case AnimationErrorCategory.MAPBOX_INTERACTION:
        // Add mapbox interaction recovery actions
        actions.push({
          action: 'refresh_map_instance',
          description: 'Attempt to refresh the map instance',
          execute: async () => {
            // This is a placeholder for the actual implementation
            console.log('Executing map refresh recovery');
            return true;
          }
        });
        break;
      
      // Add more category-specific recovery actions as needed
      
      default:
        // Default recovery action - restart the component
        actions.push({
          action: 'reset_component',
          description: 'Attempt to reset the component',
          execute: async () => {
            // This is a placeholder for the actual implementation
            console.log('Executing component reset recovery');
            return true;
          }
        });
        break;
    }
    
    return actions;
  }
  
  /**
   * Wrap a function with error handling
   */
  public wrapWithErrorHandling<T>(
    fn: (...args: any[]) => Promise<T>,
    category: AnimationErrorCategory,
    severity: ErrorSeverity,
    component: string,
    operation: string
  ): (...args: any[]) => Promise<T> {
    return async (...args: any[]) => {
      try {
        return await fn(...args);
      } catch (error) {
        // Create structured error
        const animationError = this.createError(
          error instanceof Error ? error.message : String(error),
          category,
          severity,
          component,
          AnimationPhase.NOT_STARTED, // Default phase when unknown
          operation,
          { args }
        );
        
        // Handle the error
        const recovered = await this.handleError(animationError);
        
        if (recovered) {
          // Try again if recovery was successful
          return await fn(...args);
        }
        
        // If recovery failed, re-throw the error
        throw animationError;
      }
    };
  }
  
  /**
   * Try an operation with retries and error handling
   */
  public async tryOperation<T>(
    operation: () => Promise<T>,
    category: AnimationErrorCategory,
    severity: ErrorSeverity,
    component: string,
    operationName: string,
    phase: AnimationPhase,
    maxRetries: number = 3
  ): Promise<T> {
    let lastError: any = null;
    
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        // Create structured error
        const animationError = this.createError(
          error instanceof Error ? error.message : String(error),
          category,
          severity,
          component,
          phase,
          operationName,
          { attempt }
        );
        
        // Log the error
        this.logError(animationError);
        
        // Log error attempt
        AnimationDebugTools.log('warn', `Operation ${operationName} failed, attempt ${attempt + 1}/${maxRetries}`, {
          error: error instanceof Error ? error.message : String(error),
          component,
          category
        });
        
        // Handle the error
        const recovered = await this.handleError(animationError);
        
        if (!recovered) {
          // If we couldn't recover, don't retry
          throw animationError;
        }
        
        // If we recovered, we'll retry
      }
    }
    
    // If we get here, we've exhausted all retries
    const finalError = this.createError(
      lastError instanceof Error ? lastError.message : String(lastError),
      category,
      severity,
      component,
      phase,
      `${operationName} (max retries exhausted)`,
      { maxRetriesExhausted: true }
    );
    
    throw finalError;
  }
}

export default AnimationErrorHandler.getInstance(); 