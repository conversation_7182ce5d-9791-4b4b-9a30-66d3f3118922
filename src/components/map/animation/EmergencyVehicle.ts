/**
 * EmergencyVehicle.ts
 * 
 * A simplified emergency vehicle implementation that directly manipulates the DOM
 * to ensure vehicle visibility during animation.
 */

import mapboxgl from 'mapbox-gl';

/**
 * Creates a direct vehicle marker on the map with a simple implementation
 * @param map The Mapbox map instance
 * @param position The initial position [lng, lat]
 * @returns Information about the created marker
 */
export const createDirectVehicleMarker = (map: mapboxgl.Map, position: [number, number]) => {
  console.log(`🚨 [${Date.now()}] EMERGENCY: Creating direct vehicle marker`);
  
  try {
    // Find the map container
    const mapContainer = map?.getContainer();
    if (!mapContainer) {
      console.error('Map container not found for direct vehicle creation');
      return { success: false };
    }
    
    // Remove any existing markers with our ID
    const existingMarker = document.getElementById('direct-vehicle-marker');
    if (existingMarker && existingMarker.parentNode) {
      existingMarker.parentNode.removeChild(existingMarker);
    }
    
    // Create a new marker element
    const markerEl = document.createElement('div');
    markerEl.id = 'direct-vehicle-marker';
    markerEl.className = 'direct-vehicle-marker';
    
    // Set styles
    markerEl.style.position = 'absolute';
    markerEl.style.width = '20px';
    markerEl.style.height = '20px';
    markerEl.style.backgroundColor = 'red'; // Make it red to indicate emergency mode
    markerEl.style.borderRadius = '50%';
    markerEl.style.border = '2px solid white';
    markerEl.style.boxShadow = '0 0 10px rgba(0,0,0,0.5)';
    markerEl.style.zIndex = '1000';
    markerEl.style.transform = 'translate(-50%, -50%)';
    markerEl.style.pointerEvents = 'none';
    
    // Create a pulse effect
    const pulseEl = document.createElement('div');
    pulseEl.style.position = 'absolute';
    pulseEl.style.width = '100%';
    pulseEl.style.height = '100%';
    pulseEl.style.borderRadius = '50%';
    pulseEl.style.backgroundColor = 'rgba(255,0,0,0.3)';
    pulseEl.style.animation = 'pulse 1.5s infinite';
    markerEl.appendChild(pulseEl);
    
    // Add CSS animation for the pulse
    const style = document.createElement('style');
    style.innerHTML = `
      @keyframes pulse {
        0% { transform: scale(1); opacity: 1; }
        100% { transform: scale(3); opacity: 0; }
      }
    `;
    document.head.appendChild(style);
    
    // Append to map container
    mapContainer.appendChild(markerEl);
    
    // Position the marker initially
    updateDirectVehiclePosition(map, position, 0);
    
    console.log(`✅ [${Date.now()}] EMERGENCY: Direct vehicle marker created successfully`);
    return { success: true, element: markerEl };
  } catch (error) {
    console.error(`❌ [${Date.now()}] EMERGENCY: Failed to create direct vehicle marker`, error);
    return { success: false, error };
  }
};

/**
 * Updates the position of a directly created vehicle marker
 * @param map The Mapbox map instance
 * @param position The geographic position [lng, lat]
 * @param bearing The direction in degrees
 * @returns Success status
 */
export const updateDirectVehiclePosition = (map: mapboxgl.Map, position: [number, number], bearing: number = 0): boolean => {
  try {
    // Find our marker
    const marker = document.getElementById('direct-vehicle-marker');
    if (!marker) {
      console.warn(`⚠️ [${Date.now()}] EMERGENCY: Direct vehicle marker not found for update`);
      return false;
    }
    
    // Convert geographic coordinates to pixels
    const pixelPos = map.project(position);
    
    // Update position and rotation
    marker.style.left = `${pixelPos.x}px`;
    marker.style.top = `${pixelPos.y}px`;
    marker.style.transform = `translate(-50%, -50%) rotate(${bearing}deg)`;
    marker.style.display = 'block';
    
    return true;
  } catch (error) {
    console.error(`❌ [${Date.now()}] EMERGENCY: Failed to update direct vehicle position`, error);
    return false;
  }
};

/**
 * Checks if the direct vehicle marker exists and is visible
 * @returns Whether the marker exists and is visible
 */
export const checkDirectVehicleVisibility = (): boolean => {
  const marker = document.getElementById('direct-vehicle-marker');
  return !!marker && marker.style.display !== 'none';
};

/**
 * Forces the direct vehicle marker to be visible
 * @param map The Mapbox map instance
 * @param position The position to place the marker [lng, lat]
 * @returns Success status
 */
export const forceDirectVehicleVisibility = (map: mapboxgl.Map, position: [number, number]): boolean => {
  if (checkDirectVehicleVisibility()) {
    return true;
  }
  
  const result = createDirectVehicleMarker(map, position);
  return result.success;
}; 