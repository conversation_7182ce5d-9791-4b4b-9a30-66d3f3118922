/**
 * AnimationHandler.ts
 * 
 * Handles core animation logic and calculations, separated from DOM and event concerns.
 * Responsible for managing animation state, interpolation, and timing calculations.
 */

import { SpeedContext, AnimationState } from '@/types/AnimationTypes';
import { SPEED_MODIFIERS } from '../utils/types';
import AnimationDebugTools from './AnimationDebugTools';

export interface AnimationRefs {
  animationFrameId: number | null;
  startTime: number | null;
  lastTime: number | null;
  pointIndex: number;
  totalPausedTime: number;
  pauseStartTime: number | null;
  isAnimating: boolean;
}

// Base animation duration - will be modified by context
export const BASE_ANIMATION_DURATION_MS = 180000; // 3 minutes base duration

export class AnimationHandler {
  private routePoints: Array<[number, number]>;
  private refs: AnimationRefs;
  private speedContext: SpeedContext;

  constructor(routePoints: Array<[number, number]>) {
    this.routePoints = routePoints;
    this.refs = this.createAnimationRefs();
    this.speedContext = {
      inCity: false,
      nearPOICluster: false,
      inCulturalRegion: false,
      terrain: 'default',
      nearCity: false
    };
  }

  /**
   * Creates a new set of animation refs with default values
   */
  private createAnimationRefs(): AnimationRefs {
    return {
      animationFrameId: null,
      startTime: null,
      lastTime: null,
      pointIndex: 0,
      totalPausedTime: 0,
      pauseStartTime: null,
      isAnimating: false
    };
  }

  /**
   * Resets animation refs to initial state
   */
  public resetAnimation(): void {
    this.refs = this.createAnimationRefs();
  }

  /**
   * Updates the speed context for animation
   */
  public updateSpeedContext(context: Partial<SpeedContext>): void {
    this.speedContext = { ...this.speedContext, ...context };
  }

  /**
   * Calculate actual animation duration based on context
   */
  private getContextualDuration(): number {
    let durationModifier = 1.0;

    if (this.speedContext.inCity) {
      durationModifier *= SPEED_MODIFIERS.CITY_APPROACH;
    } else if (this.speedContext.nearPOICluster) {
      durationModifier *= SPEED_MODIFIERS.POI_CLUSTER;
    } else if (this.speedContext.inCulturalRegion) {
      durationModifier *= SPEED_MODIFIERS.CULTURAL_REGION;
    } else if (this.speedContext.nearCity) {
      durationModifier *= SPEED_MODIFIERS.CITY_APPROACH;
    }

    switch (this.speedContext.terrain) {
      case 'mountain':
        durationModifier *= SPEED_MODIFIERS.MOUNTAIN;
        break;
      case 'desert':
        durationModifier *= SPEED_MODIFIERS.DESERT;
        break;
      case 'urban':
        durationModifier *= SPEED_MODIFIERS.CITY_APPROACH;
        break;
      case 'coastal':
        durationModifier *= SPEED_MODIFIERS.DEFAULT;
        break;
      default:
        durationModifier *= SPEED_MODIFIERS.HIGHWAY;
    }

    return BASE_ANIMATION_DURATION_MS * durationModifier;
  }

  /**
   * Easing function for smoother progress
   */
  private easeInOutCubic(t: number): number {
    return t < 0.5
      ? 4 * t * t * t
      : 1 - Math.pow(-2 * t + 2, 3) / 2;
  }

  /**
   * Calculates vehicle speed based on position change and time
   */
  private calculateVehicleSpeed(
    currentPoint: [number, number],
    lastPoint: [number, number] | null,
    deltaTime: number
  ): number {
    if (!lastPoint || deltaTime === 0) return 0;

    const distance = Math.sqrt(
      Math.pow(currentPoint[0] - lastPoint[0], 2) +
      Math.pow(currentPoint[1] - lastPoint[1], 2)
    );

    return distance / (deltaTime / 1000); // Convert to units/second
  }

  /**
   * Calculates the animation progress and current position
   */
  public calculateAnimationState(timestamp: number): AnimationState {
    if (!this.refs.startTime) {
      return {
        isAnimating: true,
        progress: 0,
        currentPoint: this.routePoints[0],
        lastPoint: null,
        bearing: 0,
        pointIndex: 0,
        elapsedTime: 0,
        speedContext: this.speedContext
      };
    }

    const duration = this.getContextualDuration();
    const elapsed = timestamp - this.refs.startTime - this.refs.totalPausedTime;
    const deltaTime = this.refs.lastTime ? timestamp - this.refs.lastTime : 0;
    
    const progress = Math.min(elapsed / duration, 1.0);
    const easedProgress = this.easeInOutCubic(progress);
    
    const pointIndex = Math.min(
      Math.floor(easedProgress * this.routePoints.length), 
      this.routePoints.length - 1
    );
    
    const currentPoint = this.routePoints[pointIndex];
    const nextPoint = pointIndex < this.routePoints.length - 1 
      ? this.routePoints[pointIndex + 1] 
      : null;
    
    let bearing = 0;
    if (nextPoint) {
      bearing = this.calculateBearing(currentPoint, nextPoint);
    }
    
    const lastPoint = pointIndex > 0 ? this.routePoints[pointIndex - 1] : null;

    AnimationDebugTools.log('info', 'Animation State', {
      progress,
      pointIndex,
      speed: this.calculateVehicleSpeed(currentPoint, lastPoint, deltaTime),
      context: this.speedContext
    });
    
    return {
      isAnimating: progress < 1.0,
      progress,
      currentPoint,
      lastPoint,
      bearing,
      pointIndex,
      elapsedTime: elapsed,
      speedContext: this.speedContext
    };
  }

  /**
   * Calculates bearing between two points
   */
  private calculateBearing(
    point1: [number, number],
    point2: [number, number]
  ): number {
    const lon1 = point1[0] * Math.PI / 180;
    const lon2 = point2[0] * Math.PI / 180;
    const lat1 = point1[1] * Math.PI / 180;
    const lat2 = point2[1] * Math.PI / 180;

    const y = Math.sin(lon2 - lon1) * Math.cos(lat2);
    const x = Math.cos(lat1) * Math.sin(lat2) -
              Math.sin(lat1) * Math.cos(lat2) * Math.cos(lon2 - lon1);

    const bearing = Math.atan2(y, x) * 180 / Math.PI;
    return (bearing + 360) % 360;
  }
}