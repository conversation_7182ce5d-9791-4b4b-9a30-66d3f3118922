/**
 * ContextualSpeedController.ts
 * 
 * Dynamic Animation Speed Management System
 * ----------------------------------------
 * 
 * RESPONSIBILITY:
 * Dynamically adjusts animation speed based on geographic context such as terrain type,
 * proximity to points of interest, or city boundaries. Implements the Singleton pattern
 * to ensure consistent speed control across the animation system.
 * 
 * KEY FUNCTIONALITY:
 * - Analyzes the current geographic context during animations
 * - Calculates appropriate speed multipliers based on environmental factors
 * - Provides smooth transitions between different speed zones
 * - Adjusts animation pacing for optimal user experience in different contexts
 * - Offers a centralized speed control mechanism for all animation components
 * 
 * CONTEXT TYPES & SPEED MODIFIERS:
 * - City: Slower speed (0.4) to highlight urban environments
 * - POI: Significantly reduced speed (0.2) near points of interest
 * - Mountain: Slightly reduced speed (0.8) for challenging terrain
 * - Desert: Increased speed (1.2) for open, flat terrain
 * - Coastal: Moderate speed (0.9) for scenic coastal routes
 * - Default: Standard speed (1.0) for general route travel
 * 
 * INTEGRATION POINTS:
 * - AnimationManager: Requests speed adjustments during animation
 * - AnimationController: Applies speed modifiers to animation timing
 * - RouteAnimator: Incorporates speed changes into position calculations
 * - POIDiscoveryManager: Influences speed when approaching POIs
 * 
 * USAGE:
 * ```typescript
 * // Get the singleton instance
 * const speedController = ContextualSpeedController.getInstance();
 * 
 * // Update the current context
 * speedController.updateContext({
 *   nearPOI: true,
 *   inCity: false,
 *   terrain: "mountain"
 * });
 * 
 * // Get the current speed multiplier
 * const speedMultiplier = speedController.getSpeedMultiplier();
 * // Apply to animation calculations
 * const adjustedDistance = baseDistance * speedMultiplier;
 * ```
 */

/**
 * Singleton class that adjusts animation speed based on context
 * (nearby POIs, cities, terrain type, etc.)
 */
class ContextualSpeedController {
  private static instance: ContextualSpeedController | null = null;
  
  // Speed multipliers for different contexts - refined for user story requirements
  private speedMultipliers = {
    cityApproach: 0.5,    // Moderate slowdown when approaching cities
    cityCenter: 0.3,      // Significant slowdown in city centers
    poiNearby: 0.6,       // Moderate slowdown near POIs
    poiDiscovery: 0.2,    // Very slow during POI discovery moments
    scenicRoute: 0.7,     // Slower for scenic appreciation
    mountain: 0.6,        // Careful pace in mountainous terrain
    desert: 1.2,          // Faster in open desert areas
    coastal: 0.8,         // Moderate pace for coastal views
    default: 1.0          // Default speed
  };
  
  private currentContext: {
    cityProximity: 'none' | 'approaching' | 'center';
    poiStatus: 'none' | 'nearby' | 'discovery';
    terrain: 'default' | 'mountain' | 'desert' | 'coastal' | 'scenic';
    isScenic: boolean;
  } = {
    cityProximity: 'none',
    poiStatus: 'none',
    terrain: 'default',
    isScenic: false
  };
  
  private previousMultiplier: number = 1.0;
  private lastContextUpdate: number = 0;
  
  private constructor() {
    console.log('ContextualSpeedController initialized');
  }
  
  /**
   * Get singleton instance
   */
  public static getInstance(): ContextualSpeedController {
    if (!ContextualSpeedController.instance) {
      ContextualSpeedController.instance = new ContextualSpeedController();
    }
    return ContextualSpeedController.instance;
  }
  
  // Distance thresholds in kilometers
  private readonly distanceThresholds = {
    cityApproach: 5.0,    // Start slowing 5km from city
    cityCenter: 1.0,      // City center radius
    poiNearby: 2.0,       // POI detection radius
    poiDiscovery: 0.5     // Close enough for POI discovery
  };

  /**
   * Update speed based on position and context (legacy method for AnimationManager compatibility)
   */
  public updateSpeed(
    position: [number, number],
    context: {
      isNearPOI?: boolean;
      isNearCity?: boolean;
      terrain?: 'default' | 'mountain' | 'desert' | 'coastal' | 'scenic';
      inScenicArea?: boolean;
    }
  ): void {
    // Convert legacy context to new format
    this.updateContext({
      distanceToCity: context.isNearCity ? 1.0 : undefined,
      distanceToPOI: context.isNearPOI ? 1.0 : undefined,
      terrain: context.terrain,
      isScenic: context.inScenicArea
    });
  }

  /**
   * Update current context based on position and surroundings
   */
  public updateContext(context: {
    distanceToCity?: number;
    distanceToPOI?: number;
    isDiscoveringPOI?: boolean;
    terrain?: 'default' | 'mountain' | 'desert' | 'coastal' | 'scenic';
    isScenic?: boolean;
  }): void {
    const now = Date.now();
    const prevContext = {...this.currentContext};
    
    // Update city proximity status
    if (context.distanceToCity !== undefined) {
      if (context.distanceToCity <= this.distanceThresholds.cityCenter) {
        this.currentContext.cityProximity = 'center';
      } else if (context.distanceToCity <= this.distanceThresholds.cityApproach) {
        this.currentContext.cityProximity = 'approaching';
      } else {
        this.currentContext.cityProximity = 'none';
      }
    }
    
    // Update POI status
    if (context.isDiscoveringPOI) {
      this.currentContext.poiStatus = 'discovery';
    } else if (context.distanceToPOI !== undefined) {
      if (context.distanceToPOI <= this.distanceThresholds.poiDiscovery) {
        this.currentContext.poiStatus = 'discovery';
      } else if (context.distanceToPOI <= this.distanceThresholds.poiNearby) {
        this.currentContext.poiStatus = 'nearby';
      } else {
        this.currentContext.poiStatus = 'none';
      }
    }
    
    // Update terrain and scenic status
    if (context.terrain !== undefined) this.currentContext.terrain = context.terrain;
    if (context.isScenic !== undefined) this.currentContext.isScenic = context.isScenic;
    
    // Calculate new speed multiplier
    const prevMultiplier = this.calculateMultiplier();
    const newMultiplier = this.getSpeedMultiplier();
    
    // Log significant changes
    if (Math.abs(newMultiplier - prevMultiplier) > 0.1) {
      console.log(`🚗 [${now}] Speed context updated:`, {
        from: prevMultiplier.toFixed(2),
        to: newMultiplier.toFixed(2),
        context: this.currentContext
      });
    }
    
    this.lastContextUpdate = now;
  }
  
  /**
   * Resume normal speed after a POI discovery event
   */
  public resumeAfterPOIDiscovery(): void {
    // Reset the POI status to resume normal speed
    this.currentContext.poiStatus = 'none';
    console.log(`🔄 [${Date.now()}] Resuming normal speed after POI discovery. New POI status: ${this.currentContext.poiStatus}`);
    
    // Add a notification for animation system
    const event = new CustomEvent('speed-context-updated', {
      detail: {
        speedMultiplier: this.getSpeedMultiplier(),
        context: {...this.currentContext},
        timestamp: Date.now()
      }
    });
    document.dispatchEvent(event);
  }
  
  /**
   * Get current speed multiplier based on context
   */
  public getSpeedMultiplier(): number {
    return this.calculateMultiplier();
  }
  
  /**
   * Get the full current context object
   */
  public getFullContext(): {
    cityProximity: 'none' | 'approaching' | 'center';
    poiStatus: 'none' | 'nearby' | 'discovery';
    terrain: 'default' | 'mountain' | 'desert' | 'coastal' | 'scenic';
    isScenic: boolean;
  } {
    return { ...this.currentContext }; // Return a copy to prevent external modification
  }
  
  /**
   * Calculate the actual speed multiplier based on current context
   * Implements the contextual rhythm requirements from the user story
   */
  private calculateMultiplier(): number {
    let multiplier = this.speedMultipliers.default;
    
    // City proximity has highest precedence
    switch (this.currentContext.cityProximity) {
      case 'center':
        multiplier = this.speedMultipliers.cityCenter;
        break;
      case 'approaching':
        multiplier = this.speedMultipliers.cityApproach;
        break;
    }
    
    // POI status can override or further reduce speed
    switch (this.currentContext.poiStatus) {
      case 'discovery':
        multiplier = Math.min(multiplier, this.speedMultipliers.poiDiscovery);
        break;
      case 'nearby':
        multiplier = Math.min(multiplier, this.speedMultipliers.poiNearby);
        break;
    }
    
    // Apply terrain and scenic modifiers if not in more specific context
    if (this.currentContext.cityProximity === 'none' && this.currentContext.poiStatus === 'none') {
      if (this.currentContext.isScenic) {
        multiplier = this.speedMultipliers.scenicRoute;
      } else {
        switch (this.currentContext.terrain) {
          case 'mountain':
            multiplier = this.speedMultipliers.mountain;
            break;
          case 'desert':
            multiplier = this.speedMultipliers.desert;
            break;
          case 'coastal':
            multiplier = this.speedMultipliers.coastal;
            break;
        }
      }
    }
    
    return this.smoothSpeedTransition(multiplier);
  }

  private smoothSpeedTransition(targetMultiplier: number): number {
    // Simple linear interpolation for now, can be replaced with a more sophisticated easing function
    const transitionFactor = 0.1; // Adjust for faster/slower transitions
    this.previousMultiplier += (targetMultiplier - this.previousMultiplier) * transitionFactor;
    return this.previousMultiplier;
  }
}

export default ContextualSpeedController;