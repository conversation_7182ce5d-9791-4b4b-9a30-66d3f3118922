import * as turf from '@turf/turf';
import { POI_NOTIFICATION_DISTANCE } from '../utils/types';

/**
 * Local ExtendedPointOfInterest interface to match the one from ClusterGenerator
 * This avoids type errors when accessing properties
 */
interface ExtendedPointOfInterest {
  id: string;
  name: string;
  coordinates: [number, number]; // [longitude, latitude]
  type?: string;
  description?: string;
  image?: string;
  imageUrl?: string;
  city?: string;
  location?: string;
  position?: { lat: number; lng: number; name?: string };
  lng?: number;
  lat?: number;
}

/**
 * Types for notification data
 */
export interface POINotification {
  poi: ExtendedPointOfInterest;
  distance: number;
  direction: string;
  type: 'approaching' | 'passing';
}

export interface DirectionIndicator {
  direction: 'left' | 'right' | 'straight' | 'sharp-left' | 'sharp-right' | 'u-turn';
  distanceToTurn: number;
  nextSegmentName?: string;
}

/**
 * Function to check for approaching POIs based on current position and direction
 * @param currentPoint Current vehicle position
 * @param nextPoint Next point in the route (for direction)
 * @param poiList List of POIs to check against
 * @param alreadyNotifiedPOIs Set of POI IDs that have already been notified
 * @returns POI notification or null if no approaching POIs
 */
export const getApproachingPOI = (
  currentPoint: [number, number],
  nextPoint: [number, number],
  poiList: ExtendedPointOfInterest[],
  alreadyNotifiedPOIs: Set<string> = new Set()
): POINotification | null => {
  if (!poiList || !poiList.length) return null;

  // Create a vector for the current direction
  const direction = turf.bearing(
    turf.point(currentPoint),
    turf.point(nextPoint)
  );
  
  // Find POIs ahead of the vehicle and within notification distance
  let closestPOI: ExtendedPointOfInterest | null = null;
  let minDistance = POI_NOTIFICATION_DISTANCE;
  
  poiList.forEach(poi => {
    // Skip if POI is null or undefined
    if (!poi) return;
    
    // Skip if we've already notified about this POI
    if (poi.id && alreadyNotifiedPOIs.has(poi.id)) return;
    
    // Skip POIs without valid coordinates
    if (!poi.coordinates || !Array.isArray(poi.coordinates) || poi.coordinates.length !== 2) {
      console.warn(`POI ${poi.name || 'unknown'} has invalid coordinates`);
      return;
    }
    
    // Calculate distance to POI
    const distance = turf.distance(
      turf.point(currentPoint),
      turf.point(poi.coordinates),
      { units: 'kilometers' }
    );
    
    // Only consider POIs within notification distance
    if (distance > POI_NOTIFICATION_DISTANCE) return;
    
    // Calculate bearing to the POI
    const bearingToPOI = turf.bearing(
      turf.point(currentPoint),
      turf.point(poi.coordinates)
    );
    
    // Calculate angle difference
    const angleDiff = Math.abs(bearingToPOI - direction);
    const normalizedAngleDiff = angleDiff > 180 ? 360 - angleDiff : angleDiff;
    
    // Consider the POI if it's within 45 degrees of our heading
    if (normalizedAngleDiff < 45 && distance < minDistance) {
      minDistance = distance;
      closestPOI = poi;
    }
  });
  
  if (closestPOI) {
    // Skip if POI has invalid coordinates
    if (!closestPOI.coordinates || !Array.isArray(closestPOI.coordinates) || closestPOI.coordinates.length !== 2) {
      console.warn(`Closest POI ${closestPOI.name || 'unknown'} has invalid coordinates`);
      return null;
    }

    // Generate a cardinal direction description
    const bearingToPOI = turf.bearing(
      turf.point(currentPoint),
      turf.point(closestPOI.coordinates)
    );
    const direction = getCardinalDirection(bearingToPOI);
    
    return {
      poi: closestPOI,
      distance: minDistance,
      direction,
      type: 'approaching'
    };
  }
  
  return null;
};

/**
 * Get a direction indicator for upcoming turns in the route
 * @param currentPoint Current vehicle position
 * @param routePoints Array of route points ahead
 * @param lookaheadIndex How many points ahead to check for turns
 * @returns Direction indicator or null if no significant turn ahead
 */
export const getDirectionIndicator = (
  currentPoint: [number, number],
  routePoints: [number, number][],
  lookaheadIndex: number = 3
): DirectionIndicator | null => {
  if (routePoints.length <= lookaheadIndex) return null;
  
  // Get the next point and the lookahead point
  const nextPoint = routePoints[0];
  const lookaheadPoint = routePoints[lookaheadIndex];
  
  // Calculate the current direction and the future direction
  const currentBearing = turf.bearing(
    turf.point(currentPoint),
    turf.point(nextPoint)
  );
  
  const futureBearing = turf.bearing(
    turf.point(routePoints[lookaheadIndex - 1]),
    turf.point(lookaheadPoint)
  );
  
  // Calculate the bearing change
  let bearingChange = futureBearing - currentBearing;
  
  // Normalize to -180 to 180
  if (bearingChange > 180) bearingChange -= 360;
  if (bearingChange < -180) bearingChange += 360;
  
  // Only notify for significant changes in direction
  if (Math.abs(bearingChange) < 20) return null;
  
  // Determine the type of turn
  let direction: DirectionIndicator['direction'];
  
  if (bearingChange > 120) {
    direction = 'sharp-right';
  } else if (bearingChange > 45) {
    direction = 'right';
  } else if (bearingChange < -120) {
    direction = 'sharp-left';
  } else if (bearingChange < -45) {
    direction = 'left';
  } else if (Math.abs(bearingChange) > 150) {
    direction = 'u-turn';
  } else {
    direction = 'straight';
  }
  
  // Calculate distance to the turn
  const distanceToTurn = calculateRouteDistance(
    [currentPoint, ...routePoints.slice(0, lookaheadIndex)]
  );
  
  return {
    direction,
    distanceToTurn
  };
};

/**
 * Create a styled notification for an approaching POI
 * @param notification POI notification data
 * @returns HTML element with the notification
 */
export const createPOINotification = (notification: POINotification): HTMLElement => {
  const { poi, distance, direction } = notification;
  
  // Create the notification element
  const notificationElement = document.createElement('div');
  notificationElement.className = 'poi-notification';
  notificationElement.style.cssText = `
    position: fixed;
    bottom: 80px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    padding: 12px 16px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    max-width: 300px;
    font-family: sans-serif;
    animation: fadeIn 0.5s ease, pulse 2s infinite;
    z-index: 1000;
  `;
  
  // Format distance
  const formattedDistance = distance < 1 
    ? `${(distance * 1000).toFixed(0)}m` 
    : `${distance.toFixed(1)}km`;
  
  // Icon based on POI type
  const icon = getIconForCategory(poi.type);
  
  // Create HTML content
  notificationElement.innerHTML = `
    <div style="display: flex; align-items: center;">
      <div style="font-size: 24px; margin-right: 10px;">${icon}</div>
      <div>
        <div style="font-weight: bold; font-size: 14px;">Approaching: ${poi.name}</div>
        <div style="font-size: 12px; color: #555; margin-top: 4px;">
          ${formattedDistance} ${direction} | ${poi.type}
        </div>
      </div>
    </div>
  `;
  
  return notificationElement;
};

/**
 * Create a direction indicator element
 * @param indicator Direction indicator data
 * @returns HTML element with the direction indicator
 */
export const createDirectionIndicator = (indicator: DirectionIndicator): HTMLElement => {
  const { direction, distanceToTurn, nextSegmentName } = indicator;
  
  // Create the indicator element
  const indicatorElement = document.createElement('div');
  indicatorElement.className = 'direction-indicator';
  indicatorElement.style.cssText = `
    position: fixed;
    bottom: 150px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    border-radius: 8px;
    padding: 10px 14px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    max-width: 250px;
    font-family: sans-serif;
    animation: fadeIn 0.3s ease;
    z-index: 1000;
  `;
  
  // Get direction arrow
  const arrow = getDirectionArrow(direction);
  
  // Format distance
  const formattedDistance = distanceToTurn < 1 
    ? `${(distanceToTurn * 1000).toFixed(0)}m` 
    : `${distanceToTurn.toFixed(1)}km`;
  
  // Create HTML content
  indicatorElement.innerHTML = `
    <div style="display: flex; align-items: center;">
      <div style="font-size: 24px; margin-right: 12px;">${arrow}</div>
      <div>
        <div style="font-weight: bold; font-size: 14px;">
          ${direction.charAt(0).toUpperCase() + direction.slice(1)} in ${formattedDistance}
        </div>
        ${nextSegmentName ? `<div style="font-size: 12px; margin-top: 2px;">
          Onto ${nextSegmentName}
        </div>` : ''}
      </div>
    </div>
  `;
  
  return indicatorElement;
};

/**
 * Helper function to get a cardinal direction from a bearing
 */
const getCardinalDirection = (bearing: number): string => {
  const directions = ['North', 'Northeast', 'East', 'Southeast', 'South', 'Southwest', 'West', 'Northwest'];
  const index = Math.round(((bearing + 360) % 360) / 45) % 8;
  return directions[index];
};

/**
 * Helper function to calculate the distance along a route
 */
const calculateRouteDistance = (points: [number, number][]): number => {
  let totalDistance = 0;
  
  for (let i = 1; i < points.length; i++) {
    totalDistance += turf.distance(
      turf.point(points[i - 1]),
      turf.point(points[i]),
      { units: 'kilometers' }
    );
  }
  
  return totalDistance;
};

/**
 * Helper function to get an icon for a POI category
 */
const getIconForCategory = (type?: string): string => {
  if (!type) return '📍';
  
  const icons: Record<string, string> = {
    'accommodation': '🏨',
    'activity': '🏄‍♂️',
    'restaurant': '🍽️',
    'landmark': '🏛️'
  };
  
  return icons[type.toLowerCase()] || '📍';
};

/**
 * Helper function to get an arrow for a direction
 */
const getDirectionArrow = (direction: DirectionIndicator['direction']): string => {
  const arrows: Record<DirectionIndicator['direction'], string> = {
    'left': '↰',
    'right': '↱',
    'straight': '↑',
    'sharp-left': '↲',
    'sharp-right': '↳',
    'u-turn': '↩'
  };
  
  return arrows[direction];
}; 