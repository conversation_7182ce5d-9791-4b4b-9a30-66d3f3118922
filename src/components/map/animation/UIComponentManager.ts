/**
 * UIComponentManager.ts
 * 
 * Manages the creation and manipulation of UI components for the travel experience.
 * Handles direct DOM manipulation for critical UI elements like buttons and overlays.
 */

import mapboxgl from 'mapbox-gl';
import { Destination } from '@/types/destination';
import { JourneyPhase } from '../utils/types';

/**
 * Checks if a React-based button already exists in the UI
 * @returns {boolean} True if a React button is already present
 */
export const shouldUseReactButton = (): boolean => {
  // The React-based button in TravelAnimator is now the sole authority for button creation
  // To ensure consistency and prevent duplicates, we always prefer the React implementation
  
  // Add a clear console message explaining the architecture
  console.log('Button architecture: The React-based button in TravelAnimator.tsx is the single source of truth for journey buttons. DOM-based buttons are deprecated.');
  
  // Always return true to prevent DOM-based button creation from other modules
  return true;
};

/**
 * Creates a "Begin Adventure" button that allows users to start/continue the journey.
 * Uses direct DOM manipulation for higher reliability.
 */
export const createBeginAdventureButton = (
  map: mapboxgl.Map | undefined,
  destinations: Destination[],
  onBeginAdventure: () => void
): void => {
  // First check if we should use React button instead
  if (shouldUseReactButton()) {
    console.log('Skipping DOM button creation in favor of React button');
    return;
  }
  
  if (!map || destinations.length < 2) {
    console.log('Not creating Begin Adventure button - need map and multiple destinations:', {
      hasMap: !!map,
      destinationsCount: destinations.length
    });
    return;
  }
  
  console.log('Creating direct DOM-based Begin Adventure button');
  
  // Remove any existing button first to avoid duplicates
  const existingButton = document.getElementById('direct-begin-adventure-button');
  if (existingButton) existingButton.remove();
  
  // Create container
  const buttonContainer = document.createElement('div');
  buttonContainer.id = 'direct-begin-adventure-button';
  buttonContainer.style.position = 'fixed';
  buttonContainer.style.bottom = '40px';
  buttonContainer.style.left = '50%';
  buttonContainer.style.transform = 'translateX(-50%)';
  buttonContainer.style.zIndex = '99999'; // Increase z-index to be above all other elements
  buttonContainer.style.visibility = 'visible'; // Ensure visibility
  buttonContainer.style.display = 'block'; // Ensure display
  
  // Create button element
  const button = document.createElement('button');
  button.textContent = 'Begin Adventure';
  button.className = 'begin-adventure-button'; // Add class for consistency
  button.style.backgroundColor = '#10b981'; // Green color
  button.style.color = 'white';
  button.style.padding = '12px 24px';
  button.style.borderRadius = '8px';
  button.style.fontWeight = 'bold';
  button.style.boxShadow = '0 4px 14px rgba(0,0,0,0.25)';
  button.style.border = '2px solid white';
  button.style.display = 'flex';
  button.style.alignItems = 'center';
  button.style.justifyContent = 'center';
  button.style.gap = '8px';
  button.style.minWidth = '200px';
  button.style.cursor = 'pointer';
  
  // Add play icon to button
  const playIcon = document.createElement('span');
  playIcon.innerHTML = '▶';
  playIcon.style.fontSize = '14px';
  playIcon.style.marginRight = '4px';
  button.prepend(playIcon);
  
  // Add click event with callback
  button.addEventListener('click', () => {
    console.log('Direct DOM Begin Adventure button clicked');
    onBeginAdventure();
  });
  
  // Append to container
  buttonContainer.appendChild(button);
  
  // Add to document body
  document.body.appendChild(buttonContainer);
  
  console.log('Direct DOM Begin Adventure button created');
};

/**
 * Creates an announcement overlay for displaying messages to the user
 */
export const createAnnouncementOverlay = (): HTMLElement => {
  // Remove any existing overlay first
  const existingOverlay = document.getElementById('announcement-overlay');
  if (existingOverlay) {
    return existingOverlay;
  }
  
  // Create new overlay
  const overlay = document.createElement('div');
  overlay.id = 'announcement-overlay';
  overlay.style.position = 'fixed';
  overlay.style.top = '20%';
  overlay.style.left = '50%';
  overlay.style.transform = 'translateX(-50%)';
  overlay.style.zIndex = '99999';
  overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
  overlay.style.color = 'white';
  overlay.style.padding = '1rem 2rem';
  overlay.style.borderRadius = '8px';
  overlay.style.fontWeight = 'bold';
  overlay.style.textAlign = 'center';
  overlay.style.visibility = 'hidden';
  overlay.style.opacity = '0';
  overlay.style.transition = 'opacity 0.3s ease-in-out';
  
  // Add to document body
  document.body.appendChild(overlay);
  
  return overlay;
};

/**
 * Shows an announcement message to the user
 */
export const showUIAnnouncement = (
  message: string,
  duration: number = 3000
): void => {
  // Get or create overlay
  const overlay = document.getElementById('announcement-overlay') || createAnnouncementOverlay();
  
  // Set message and show
  overlay.textContent = message;
  overlay.style.visibility = 'visible';
  overlay.style.opacity = '1';
  
  // Auto-hide after duration
  setTimeout(() => {
    overlay.style.opacity = '0';
    setTimeout(() => {
      overlay.style.visibility = 'hidden';
    }, 300);
  }, duration);
};

/**
 * Clean up UI components when no longer needed
 */
export const cleanupUIComponents = (): void => {
  // Remove buttons
  const continueButton = document.getElementById('direct-begin-adventure-button');
  if (continueButton) continueButton.remove();
  
  // Remove other UI elements
  const overlay = document.getElementById('announcement-overlay');
  if (overlay) overlay.remove();
};

/**
 * Generate a script tag with inline JS to force button creation
 * Useful for ensuring button appears even with React rendering issues
 */
export const generateForcedButtonScript = (
  destinationsCount: number
): string => {
  return `
    setTimeout(() => {
      // Check if the button already exists
      if (document.getElementById('direct-begin-adventure-button')) return;
      
      // Check the conditions - now only based on destination count
      const destinationsCount = ${destinationsCount};
      
      if (destinationsCount >= 2) {
        console.log('Forcing Begin Adventure button creation via script');
        
        // Create container
        const buttonContainer = document.createElement('div');
        buttonContainer.id = 'direct-begin-adventure-button';
        buttonContainer.style.position = 'fixed';
        buttonContainer.style.bottom = '40px';
        buttonContainer.style.left = '50%';
        buttonContainer.style.transform = 'translateX(-50%)';
        buttonContainer.style.zIndex = '99999';
        buttonContainer.style.visibility = 'visible';
        buttonContainer.style.display = 'block';
        
        // Create button element
        const button = document.createElement('button');
        button.textContent = 'Begin Adventure';
        button.style.backgroundColor = '#10b981';
        button.style.color = 'white';
        button.style.padding = '12px 24px';
        button.style.borderRadius = '8px';
        button.style.fontWeight = 'bold';
        button.style.boxShadow = '0 4px 14px rgba(0,0,0,0.25)';
        button.style.border = '2px solid white';
        button.style.display = 'flex';
        button.style.alignItems = 'center';
        button.style.justifyContent = 'center';
        button.style.gap = '8px';
        button.style.minWidth = '200px';
        button.style.cursor = 'pointer';
        
        // Add play icon to button
        const playIcon = document.createElement('span');
        playIcon.innerHTML = '▶';
        playIcon.style.fontSize = '14px';
        playIcon.style.marginRight = '4px';
        button.prepend(playIcon);
        
        // Add click event 
        button.addEventListener('click', () => {
          console.log('Begin Adventure button clicked via script');
          window.dispatchEvent(new CustomEvent('begin-adventure-clicked'));
        });
        
        // Append to container
        buttonContainer.appendChild(button);
        
        // Add to document body
        document.body.appendChild(buttonContainer);
      }
    }, 1000);
  `;
};

// Keep the old function for backward compatibility, but have it use the new implementation
export const createContinueJourneyButton = (
  map: mapboxgl.Map | undefined,
  destinations: Destination[],
  onContinueJourney: () => void
): void => {
  console.log("Using renamed 'Begin Adventure' button instead of deprecated 'Continue Journey'");
  createBeginAdventureButton(map, destinations, onContinueJourney);
};

export default {
  createBeginAdventureButton,
  createAnnouncementOverlay,
  showUIAnnouncement,
  cleanupUIComponents,
  generateForcedButtonScript
}; 