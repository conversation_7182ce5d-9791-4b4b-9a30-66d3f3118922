# Animation System Testing Requirements Outline

This document outlines the testing requirements for the recently implemented features in the animation system. This outline will guide a developer in writing unit and integration tests.

**I. `ContextualSpeedController.ts` (`speedMultipliers`, terrain handling, smooth transitions)**

1.  **Unit Test: `calculateMultiplier()`**
    *   **Objective:** Verify correct speed multiplier selection based on context.
    *   **Scenarios:**
        *   Test with `cityProximity` set to 'center', 'approaching', 'none'. Assert correct multiplier.
        *   Test with `poiStatus` set to 'discovery', 'nearby', 'none' (and combinations with city proximity, ensuring POI discovery takes precedence or combines correctly). Assert correct multiplier.
        *   Test with various `terrain` types: 'mountain', 'desert', 'coastal', 'culturalRegion', 'default'. Assert correct multiplier.
        *   Test with `isScenic` as true. Assert correct multiplier.
        *   Test combinations, e.g., approaching a city in mountainous terrain (city should take precedence).
    *   **Assertions:** The returned value from `calculateMultiplier()` (after smoothing) should converge to the expected multiplier from the `speedMultipliers` object for the given context.

2.  **Unit Test: `smoothSpeedTransition()` (Time-based easeInOutCubic)**
    *   **Objective:** Verify the smooth transition logic.
    *   **Setup:**
        *   Mock `Date.now()` to control time progression.
        *   Set an initial `previousMultiplier`.
        *   Call `calculateMultiplier` such that it determines a new `idealMultiplier` different from `previousMultiplier`, thus triggering a new transition (setting `_activeTransitionTargetMultiplier`, `_transitionStartMultiplier`, `_transitionStartTime`).
    *   **Scenarios & Assertions:**
        *   Call `smoothSpeedTransition()` multiple times, advancing mock time for each call.
        *   Assert that `this.previousMultiplier` progresses from `_transitionStartMultiplier` towards `_activeTransitionTargetMultiplier` following an easeInOutCubic curve.
        *   Assert that after `_speedTransitionDuration` has elapsed, `this.previousMultiplier` is approximately equal to `_activeTransitionTargetMultiplier`.
        *   Assert that `_activeTransitionTargetMultiplier` becomes `null` after the transition completes.
        *   Test that if a new `idealMultiplier` is set mid-transition, a new transition starts correctly from the current `previousMultiplier`.

**II. `EnhancedCameraBehavior.ts` (Dynamic bearing, preset alignment)**

1.  **Unit Test: `getPresetKeyForContext()`**
    *   **Objective:** Verify correct preset key selection.
    *   **Scenarios:** Provide various `FollowVehicleOptions` (differing `isDiscoveringPOI`, `inCity`, `terrain`).
    *   **Assertions:** Assert that the returned preset key (e.g., 'poiDetail', 'cityExploration', 'routeTravel') is correct for the given context.

2.  **Integration Test: Camera parameters from presets in `followVehicle()`**
    *   **Objective:** Verify `followVehicle` uses zoom, pitch, duration, and bearing from the selected `CAMERA_PRESETS`.
    *   **Setup:** Mock `this.map.easeTo()`.
    *   **Scenarios:**
        *   Call `followVehicle` with contexts that trigger different presets (e.g., POI discovery, city exploration, default route travel, specific terrains).
        *   Pass a vehicle `bearing` to `followVehicle`.
    *   **Assertions:**
        *   Inspect the arguments passed to `this.map.easeTo()`.
        *   Assert that `zoom`, `pitch`, and `duration` match the values from the corresponding `CAMERA_PRESETS` entry.
        *   Assert that `bearing` argument to `easeTo()`:
            *   Matches the vehicle's bearing if the preset's bearing is `'vehicle'` or not numerically defined for that context.
            *   Matches the numeric bearing from the preset if specified.
        *   Assert `this.currentState` is updated correctly with these parameters.

**III. `AnimationManager.ts` (Cinematic integration, localStorage)**

1.  **Integration Test: `startAnimation()` - Journey Intro Cinematic**
    *   **Objective:** Verify intro cinematic is played on fresh start.
    *   **Setup:** Mock `this.cinematicController.playJourneyIntroSequence` (make it a spy or a mock that resolves a promise). Mock `this.routeAnimator.getPointDataAtProgress`.
    *   **Scenarios:** Call `startAnimation` when state is `IDLE`.
    *   **Assertions:**
        *   Verify `this.cinematicController.playJourneyIntroSequence` was called with the correct initial route position.
        *   Verify `this.startTime` is correctly set *after* the (mocked) intro sequence completes.

2.  **Integration Test: `animateFrame()` - Journey Completion Cinematic**
    *   **Objective:** Verify completion cinematic is played when progress reaches 1.
    *   **Setup:** Mock `this.cinematicController.playJourneyCompletionSequence`. Set `this.currentProgress = 1` and `this.currentPosition`.
    *   **Scenarios:** Call `animateFrame`.
    *   **Assertions:** Verify `this.cinematicController.playJourneyCompletionSequence` was called with `this.currentPosition`.

3.  **Integration Test: `animateFrame()` - City Approach Cinematic**
    *   **Objective:** Verify city approach cinematic is triggered.
    *   **Setup:**
        *   Mock `this.cinematicController.playCityApproachSequence`.
        *   Set `this.destinations` with at least one destination (with `id` and `coordinates`).
        *   Initialize `this._approachedDestinations` as an empty set.
    *   **Scenarios:** Call `animateFrame` multiple times, simulating `this.currentPosition` moving closer to the destination.
    *   **Assertions:**
        *   Verify `this.cinematicController.playCityApproachSequence` is called once when the position is within `CITY_APPROACH_CINEMATIC_THRESHOLD_KM`.
        *   Verify the correct destination and current position are passed.
        *   Verify the destination ID is added to `this._approachedDestinations`.

4.  **Integration Test: `localStorage` for Animation State**
    *   **Objective:** Verify saving and loading of route and progress.
    *   **Setup:** Mock `localStorage.setItem` and `localStorage.getItem`.
    *   **Scenarios (Save):**
        *   Call `prepareAnimation` with a route. Assert `localStorage.setItem` called for route and progress 0.
        *   Call `pauseAnimation(true)`. Assert `localStorage.setItem` called with current route/progress.
    *   **Scenarios (Load):**
        *   Set mock `localStorage` with saved route and progress.
        *   Call `loadAnimationStateFromLocalStorage()`.
    *   **Assertions (Load):**
        *   Assert `this.route` and `this.currentProgress` are restored.
        *   Assert `this.animationState` is `PAUSED`.
        *   Assert vehicle is positioned correctly based on loaded progress (mock `this.vehicleManager.createVehicleMarker` and `this.cinematicController.processPositionUpdate` to check args).

**IV. `POIDiscoveryManager.ts` (Cinematic integration, client filtering, localStorage)**

1.  **Unit Test: Client-Specific POI Filtering**
    *   **Objective:** Verify POIs are filtered based on `activeClientId`.
    *   **Setup:**
        *   Call `initializePOIs` with a list of POIs having various `clientId`s (e.g., 'clientA', 'clientB', 'generic', null).
    *   **Scenarios:**
        *   Call `setActiveClient('clientA')`. Then call `findNearbyPOIs()` (or `updatePosition()`) for a position where multiple POIs (clientA, clientB, generic) would be in range.
    *   **Assertions:** Assert that the returned/processed POIs only include those for 'clientA' and 'generic' (or null `clientId`). Repeat for 'clientB' and for `setActiveClient(null)` (should get generic/null and not client-specific ones, or all if that's the desired behavior for null client).

2.  **Integration Test: `triggerPOIDiscovery()` - POI Cinematic**
    *   **Objective:** Verify POI discovery cinematic is played.
    *   **Setup:** Mock `CinematicController.getInstance().playPOIDiscoverySequence`. Initialize POIs.
    *   **Scenarios:** Call `triggerPOIDiscovery` for a valid POI position.
    *   **Assertions:** Verify `cinematicController.playPOIDiscoverySequence` was called with the correct POI object and current vehicle position.

3.  **Integration Test: `localStorage` for Discovered POIs**
    *   **Objective:** Verify saving and loading of discovered POI IDs.
    *   **Setup:** Mock `localStorage.setItem` and `localStorage.getItem`.
    *   **Scenarios (Save):** Call `markAsDiscovered()` for a POI.
    *   **Assertions (Save):** Assert `localStorage.setItem` was called with the correct key and a JSON array of discovered POI IDs.
    *   **Scenarios (Load):** Set mock `localStorage` with a list of discovered POI IDs. Create a new `POIDiscoveryManager` instance (as loading happens in constructor).
    *   **Assertions (Load):** Assert `this.discoveredPOIs` set is correctly populated. 