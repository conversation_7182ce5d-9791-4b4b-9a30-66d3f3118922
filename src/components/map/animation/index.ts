/**
 * Animation System Index
 * Exports all animation-related components for easier imports
 */

export { AnimationManager } from './AnimationManager';
export { default as AnimationDebugTools } from './AnimationDebugTools';
export { default as ContextualSpeedController } from './ContextualSpeedController';
export { default as POIDiscoveryManager } from './POIDiscoveryManager';
export { default as CityDriveByManager } from './CityDriveByManager';
export { default as ComponentInteractionManager } from './ComponentInteractionManager';
export { default as VehicleManager } from './VehicleManager';

// Export new modular components
export { AnimationHandler } from './AnimationHandler';
export { EventManager } from './EventManager';
export { MarkerManager } from './MarkerManager';

// Export additional types and enums
export { JourneyPhase } from './ComponentInteractionManager';

/**
 * Animation module index
 * 
 * Re-exports key animation components, interfaces, and adapter utilities
 * to bridge the gap between simple position arrays and detailed RoutePoint objects
 */

// Export utilities
export * from './AnimationUtils';

// Re-export adapters for different RoutePoint formats
export {
  isDetailedRoutePoint,
  isSimpleRoutePoint,
  getRoutePointPosition,
  convertSimpleToDetailedRoutePoint,
  convertDetailedToSimpleRoutePoint,
  createRoutePointFromPosition,
  safeGetRoutePointPosition
} from '@/types/RoutePointAdapter';

// Export our RouteAnimator adapter functions for interface compatibility
export {
  getNearestRoutePoint,
  getRoutePointAtProgress,
  interpolateRoutePoints,
  determineTerrainType,
  calculateBearing
} from './RouteAnimatorAdapter';

// Export route animation utilities
export { 
  calculateAnimationState,
  createAnimationRefs,
  resetAnimationRefs,
  calculateVehicleSpeed,
  createFallbackRoute,
  getContextualDuration,
  easeInOutCubic,
  BASE_ANIMATION_DURATION_MS,
  type SpeedContext,
  type AnimationState
} from './RouteAnimator';

// Export animation types and interfaces
export * from './interfaces';