/**
 * AnimationLifecycleManager.ts
 * 
 * Provides centralized management of animation lifecycle:
 * - Streamlines initialization and startup sequence
 * - Reduces callback chaining and dependency handoffs
 * - Creates a predictable phase management system
 * - Consolidates animation loop management in a single component
 */

import { AnimationEventType, EventData } from '@/types/AnimationEventTypes';
import { Position } from '@/types/Position';
import { AnimationPhase } from '@/types/AnimationTypes';
import AnimationDebugTools from './AnimationDebugTools';
import AnimationStateValidator from './AnimationStateValidator';
import AnimationErrorHandler, { AnimationErrorCategory, ErrorSeverity } from './AnimationErrorHandler';
import { AnimationEventEmitter } from './AnimationEventEmitter';
import { AnimationLifecycleManagerInterface } from './interfaces/AnimationLifecycleManagerInterface';
import mapboxgl from 'mapbox-gl';

/**
 * Manager for animation lifecycle phases with consolidated loop management
 */
export class Animation<PERSON><PERSON>cycleManager implements AnimationLifecycleManagerInterface {
  private static instance: AnimationLifecycleManager | null = null;
  
  // Core animation state
  private phase: AnimationPhase = AnimationPhase.NOT_STARTED;
  private _isInitialized: boolean = false;
  private isPaused: boolean = false;
  private animationFrameId: number | null = null;
  
  // Time tracking
  private startTime: number | null = null;
  private pauseStartTime: number | null = null;
  private totalPausedTime: number = 0;
  private lastFrameTime: number = 0;
  
  // Resources
  private map: mapboxgl.Map | null = null;
  private eventEmitter: AnimationEventEmitter;
  private errorHandler: typeof AnimationErrorHandler;
  
  // Animation parameters
  private duration: number = 0;
  private route: Position[] = [];
  private currentProgress: number = 0;
  
  // Registered components
  private registeredComponents: Set<string> = new Set();
  private componentsReadyStatus: Map<string, boolean> = new Map();
  
  /**
   * Private constructor for singleton pattern
   */
  private constructor() {
    this.eventEmitter = new AnimationEventEmitter();
    this.errorHandler = AnimationErrorHandler;
    AnimationDebugTools.log('info', 'AnimationLifecycleManager created');
  }
  
  /**
   * Get the singleton instance
   */
  public static getInstance(): AnimationLifecycleManager {
    if (!AnimationLifecycleManager.instance) {
      AnimationLifecycleManager.instance = new AnimationLifecycleManager();
    }
    return AnimationLifecycleManager.instance;
  }
  
  /**
   * Check if the lifecycle manager is initialized
   */
  public isInitialized(): boolean {
    return this._isInitialized;
  }
  
  /**
   * Initialize the lifecycle manager with a map instance
   */
  public async initialize(map: mapboxgl.Map): Promise<boolean> {
    // Use state validator to check initialization state
    const validationResult = AnimationStateValidator.validateInitialization(this._isInitialized);
    
    if (!validationResult.isValid) {
      AnimationStateValidator.logValidationFailure(validationResult);
      return false;
    }
    
    try {
      return await this.errorHandler.tryOperation<boolean>(
        async () => {
          this.map = map;
          this._isInitialized = true;
          this.phase = AnimationPhase.INITIALIZED;
          
          // Emit initialization event
          this.emitEvent(AnimationEventType.INITIALIZED, { 
            timestamp: Date.now(),
            phase: this.phase
          });
          
          AnimationDebugTools.log('info', 'AnimationLifecycleManager initialized');
          return true;
        },
        AnimationErrorCategory.INITIALIZATION,
        ErrorSeverity.HIGH,
        'AnimationLifecycleManager',
        'initialize',
        this.phase,
        1 // Only try once for initialization
      );
    } catch (error) {
      AnimationDebugTools.log('error', 'Failed to initialize AnimationLifecycleManager', error);
      return false;
    }
  }
  
  /**
   * Register a component with the lifecycle manager
   */
  public registerComponent(componentId: string): void {
    // Validate component ID
    const validationResult = AnimationStateValidator.validateComponentRegistration(componentId);
    
    if (!validationResult.isValid) {
      AnimationStateValidator.logValidationFailure(validationResult);
      return;
    }
    
    this.registeredComponents.add(componentId);
    this.componentsReadyStatus.set(componentId, false);
    AnimationDebugTools.log('debug', `Component registered: ${componentId}`);
  }
  
  /**
   * Report that a component is ready
   */
  public reportComponentReady(componentId: string): void {
    // Validate component ID
    const validationResult = AnimationStateValidator.validateComponentRegistration(componentId);
    
    if (!validationResult.isValid) {
      AnimationStateValidator.logValidationFailure(validationResult);
      return;
    }
    
    if (!this.registeredComponents.has(componentId)) {
      this.registerComponent(componentId);
    }
    
    this.componentsReadyStatus.set(componentId, true);
    AnimationDebugTools.log('debug', `Component ready: ${componentId}`);
    
    this.checkAllComponentsReady();
  }
  
  /**
   * Check if all registered components are ready
   */
  private checkAllComponentsReady(): boolean {
    if (this.registeredComponents.size === 0) {
      return false;
    }
    
    for (const [componentId, isReady] of this.componentsReadyStatus.entries()) {
      if (!isReady) {
        return false;
      }
    }
    
    // All components are ready, emit system ready event
    this.emitEvent(AnimationEventType.SYSTEM_READY, {
      timestamp: Date.now(),
      components: Array.from(this.registeredComponents)
    });
    
    return true;
  }
  
  /**
   * Prepare the animation with route data
   */
  public async prepareAnimation(route: Position[], duration: number): Promise<boolean> {
    // Validate preparation state
    const validationResult = AnimationStateValidator.validatePreparation(
      this._isInitialized,
      route,
      duration
    );
    
    if (!validationResult.isValid) {
      AnimationStateValidator.logValidationFailure(validationResult);
      return false;
    }
    
    try {
      return await this.errorHandler.tryOperation<boolean>(
        async () => {
          // Stop any ongoing animations
          await this.stopAnimation();
          
          // Store animation parameters
          this.route = [...route];
          this.duration = duration;
          this.currentProgress = 0;
          
          // Set phase to prepared
          this.phase = AnimationPhase.PREPARED;
          
          // Emit preparation event
          this.emitEvent(AnimationEventType.STATE_CHANGE, {
            timestamp: Date.now(),
            phase: this.phase,
            route: this.route,
            duration: this.duration
          });
          
          AnimationDebugTools.log('info', 'Animation prepared', {
            routeLength: this.route.length,
            duration: this.duration
          });
          
          return true;
        },
        AnimationErrorCategory.STATE_TRANSITION,
        ErrorSeverity.MEDIUM,
        'AnimationLifecycleManager',
        'prepareAnimation',
        this.phase
      );
    } catch (error) {
      AnimationDebugTools.log('error', 'Failed to prepare animation', error);
      return false;
    }
  }
  
  /**
   * Start the animation
   */
  public async startAnimation(): Promise<boolean> {
    // Validate start state
    const validationResult = AnimationStateValidator.validateStart(
      this._isInitialized,
      this.phase
    );
    
    if (!validationResult.isValid) {
      AnimationStateValidator.logValidationFailure(validationResult);
      return false;
    }
    
    try {
      return await this.errorHandler.tryOperation<boolean>(
        async () => {
          // If we're resuming from pause
          if (this.phase === AnimationPhase.PAUSED) {
            this.isPaused = false;
            
            // Calculate total paused time
            if (this.pauseStartTime !== null) {
              this.totalPausedTime += Date.now() - this.pauseStartTime;
              this.pauseStartTime = null;
            }
          } else {
            // Starting fresh
            this.startTime = Date.now();
            this.lastFrameTime = this.startTime;
            this.totalPausedTime = 0;
            this.currentProgress = 0;
          }
          
          // Set phase to playing
          this.phase = AnimationPhase.PLAYING;
          
          // Start animation loop
          this.startAnimationLoop();
          
          // Emit start event
          this.emitEvent(AnimationEventType.START, {
            timestamp: Date.now(),
            phase: this.phase
          });
          
          AnimationDebugTools.log('info', 'Animation started');
          
          return true;
        },
        AnimationErrorCategory.ANIMATION_LOOP,
        ErrorSeverity.MEDIUM,
        'AnimationLifecycleManager',
        'startAnimation',
        this.phase
      );
    } catch (error) {
      AnimationDebugTools.log('error', 'Failed to start animation', error);
      return false;
    }
  }
  
  /**
   * Pause the animation
   */
  public async pauseAnimation(): Promise<boolean> {
    // Validate pause state
    const validationResult = AnimationStateValidator.validatePause(
      this._isInitialized,
      this.phase
    );
    
    if (!validationResult.isValid) {
      AnimationStateValidator.logValidationFailure(validationResult);
      return false;
    }
    
    try {
      return await this.errorHandler.tryOperation<boolean>(
        async () => {
          this.isPaused = true;
          this.pauseStartTime = Date.now();
          this.phase = AnimationPhase.PAUSED;
          
          // We don't stop the animation loop, just skip updates when paused
          
          // Emit pause event
          this.emitEvent(AnimationEventType.PAUSE, {
            timestamp: Date.now(),
            phase: this.phase,
            currentProgress: this.currentProgress
          });
          
          AnimationDebugTools.log('info', 'Animation paused', {
            progress: this.currentProgress
          });
          
          return true;
        },
        AnimationErrorCategory.STATE_TRANSITION,
        ErrorSeverity.LOW,
        'AnimationLifecycleManager',
        'pauseAnimation',
        this.phase
      );
    } catch (error) {
      AnimationDebugTools.log('error', 'Failed to pause animation', error);
      return false;
    }
  }
  
  /**
   * Resume the animation from pause
   */
  public async resumeAnimation(): Promise<boolean> {
    // Validate resume state
    const validationResult = AnimationStateValidator.validateResume(
      this._isInitialized,
      this.phase
    );
    
    if (!validationResult.isValid) {
      AnimationStateValidator.logValidationFailure(validationResult);
      return false;
    }
    
    try {
      return await this.errorHandler.tryOperation<boolean>(
        async () => {
          this.isPaused = false;
          
          // Calculate total paused time
          if (this.pauseStartTime !== null) {
            this.totalPausedTime += Date.now() - this.pauseStartTime;
            this.pauseStartTime = null;
          }
          
          // Set phase to playing
          this.phase = AnimationPhase.PLAYING;
          
          // Emit resume event
          this.emitEvent(AnimationEventType.RESUME, {
            timestamp: Date.now(),
            phase: this.phase,
            currentProgress: this.currentProgress
          });
          
          AnimationDebugTools.log('info', 'Animation resumed', {
            progress: this.currentProgress
          });
          
          return true;
        },
        AnimationErrorCategory.STATE_TRANSITION,
        ErrorSeverity.LOW,
        'AnimationLifecycleManager',
        'resumeAnimation',
        this.phase
      );
    } catch (error) {
      AnimationDebugTools.log('error', 'Failed to resume animation', error);
      return false;
    }
  }
  
  /**
   * Stop the animation
   */
  public async stopAnimation(): Promise<boolean> {
    if (!this._isInitialized) {
      return false;
    }
    
    try {
      return await this.errorHandler.tryOperation<boolean>(
        async () => {
          // Cancel animation frame if active
          if (this.animationFrameId !== null) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
          }
          
          // Reset animation state
          this.isPaused = false;
          this.startTime = null;
          this.pauseStartTime = null;
          this.totalPausedTime = 0;
          this.currentProgress = 0;
          
          // Set phase to stopped
          const previousPhase = this.phase;
          this.phase = AnimationPhase.STOPPED;
          
          // Emit stop event
          this.emitEvent(AnimationEventType.STOP, {
            timestamp: Date.now(),
            phase: this.phase,
            previousPhase
          });
          
          AnimationDebugTools.log('info', 'Animation stopped');
          
          return true;
        },
        AnimationErrorCategory.ANIMATION_LOOP,
        ErrorSeverity.LOW,
        'AnimationLifecycleManager',
        'stopAnimation',
        this.phase
      );
    } catch (error) {
      AnimationDebugTools.log('error', 'Failed to stop animation', error);
      return false;
    }
  }
  
  /**
   * Reset the animation to initial state
   */
  public async resetAnimation(): Promise<boolean> {
    if (!this._isInitialized) {
      return false;
    }
    
    try {
      return await this.errorHandler.tryOperation<boolean>(
        async () => {
          // Stop the animation first
          await this.stopAnimation();
          
          // Reset route and duration
          this.route = [];
          this.duration = 0;
          
          // Set phase to initial
          this.phase = AnimationPhase.INITIALIZED;
          
          // Emit reset event
          this.emitEvent(AnimationEventType.RESET, {
            timestamp: Date.now(),
            phase: this.phase
          });
          
          AnimationDebugTools.log('info', 'Animation reset');
          
          return true;
        },
        AnimationErrorCategory.STATE_TRANSITION,
        ErrorSeverity.MEDIUM,
        'AnimationLifecycleManager',
        'resetAnimation',
        this.phase
      );
    } catch (error) {
      AnimationDebugTools.log('error', 'Failed to reset animation', error);
      return false;
    }
  }
  
  /**
   * Start the animation loop
   */
  private startAnimationLoop(): void {
    // Skip if already running
    if (this.animationFrameId !== null) {
      return;
    }
    
    const animateFrame = (timestamp: number): void => {
      // Skip updates when paused, but keep the loop running
      if (!this.isPaused && this.startTime !== null) {
        // Calculate elapsed time accounting for pauses
        const elapsed = timestamp - this.startTime - this.totalPausedTime;
        
        // Calculate progress (0-1)
        const progress = Math.min(elapsed / this.duration, 1);
        this.currentProgress = progress;
        
        // Calculate frame delta time for consistent animations
        const deltaTime = timestamp - this.lastFrameTime;
        this.lastFrameTime = timestamp;
        
        // Emit progress event
        this.emitEvent(AnimationEventType.PROGRESS_UPDATE, {
          timestamp: Date.now(),
          progress,
          elapsed,
          deltaTime,
          phase: this.phase
        });
        
        // Check if animation is complete
        if (progress >= 1) {
          this.handleAnimationComplete();
          return;
        }
      }
      
      // Continue the animation loop
      this.animationFrameId = requestAnimationFrame(animateFrame);
    };
    
    // Start the animation loop
    this.animationFrameId = requestAnimationFrame(animateFrame);
  }
  
  /**
   * Handle animation completion
   */
  private handleAnimationComplete(): void {
    // Cancel the animation frame
    if (this.animationFrameId !== null) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
    
    // Set phase to completed
    this.phase = AnimationPhase.COMPLETED;
    
    // Emit complete event
    this.emitEvent(AnimationEventType.COMPLETE, {
      timestamp: Date.now(),
      phase: this.phase
    });
    
    AnimationDebugTools.log('info', 'Animation completed');
  }
  
  /**
   * Add an event listener
   */
  public addEventListener(
    eventType: AnimationEventType, 
    callback: (data: EventData) => void
  ): () => void {
    return this.eventEmitter.addEventListener(eventType, callback);
  }
  
  /**
   * Remove an event listener
   */
  public removeEventListener(
    eventType: AnimationEventType, 
    callback: (data: EventData) => void
  ): boolean {
    return this.eventEmitter.removeEventListener(eventType, callback);
  }
  
  /**
   * Emit an event
   */
  private emitEvent(
    eventType: AnimationEventType, 
    data: any
  ): void {
    this.eventEmitter.emit(eventType, {
      ...data,
      type: eventType
    });
  }
  
  /**
   * Get the current animation phase
   */
  public getPhase(): AnimationPhase {
    return this.phase;
  }
  
  /**
   * Get the current animation progress
   */
  public getProgress(): number {
    return this.currentProgress;
  }
  
  /**
   * Set the animation progress directly
   */
  public async setProgress(progress: number): Promise<boolean> {
    // Validate setting progress
    const validationResult = AnimationStateValidator.validateSetProgress(
      this._isInitialized,
      this.phase,
      progress
    );
    
    if (!validationResult.isValid) {
      AnimationStateValidator.logValidationFailure(validationResult);
      return false;
    }
    
    try {
      return await this.errorHandler.tryOperation<boolean>(
        async () => {
          // Clamp progress to 0-1
          this.currentProgress = Math.max(0, Math.min(1, progress));
          
          // Adjust startTime to maintain this progress
          if (this.startTime !== null) {
            const elapsed = this.currentProgress * this.duration;
            this.startTime = Date.now() - elapsed - this.totalPausedTime;
          }
          
          // Emit progress event
          this.emitEvent(AnimationEventType.PROGRESS_UPDATE, {
            timestamp: Date.now(),
            progress: this.currentProgress,
            phase: this.phase
          });
          
          return true;
        },
        AnimationErrorCategory.STATE_TRANSITION,
        ErrorSeverity.LOW,
        'AnimationLifecycleManager',
        'setProgress',
        this.phase
      );
    } catch (error) {
      AnimationDebugTools.log('error', 'Failed to set progress', error);
      return false;
    }
  }
  
  /**
   * Reset the component state
   * Required by AnimationComponentInterface
   */
  public async reset(): Promise<void> {
    try {
      // Use the existing resetAnimation method for core functionality
      const success = await this.resetAnimation();
      
      if (!success) {
        AnimationDebugTools.log('error', 'Failed to reset AnimationLifecycleManager');
        throw new Error('Failed to reset AnimationLifecycleManager');
      }
      
      // Additionally reset component registration state
      this.registeredComponents.clear();
      this.componentsReadyStatus.clear();
      
      // Set phase to initialized if we're already initialized
      if (this._isInitialized) {
        this.phase = AnimationPhase.INITIALIZED;
      } else {
        this.phase = AnimationPhase.NOT_STARTED;
      }
      
      // Emit reset event with updated phase
      this.emitEvent(AnimationEventType.RESET, {
        timestamp: Date.now(),
        phase: this.phase
      });
      
      AnimationDebugTools.log('info', 'AnimationLifecycleManager reset complete');
    } catch (error) {
      AnimationDebugTools.log('error', 'Error during AnimationLifecycleManager reset', error);
      throw error;
    }
  }
  
  /**
   * Cleanup resources
   */
  public dispose(): void {
    this.stopAnimation();
    this.eventEmitter.removeAllListeners();
    this.registeredComponents.clear();
    this.componentsReadyStatus.clear();
    this._isInitialized = false;
    this.map = null;
    
    AnimationDebugTools.log('info', 'AnimationLifecycleManager disposed');
  }
  
  /**
   * Create a new instance of the lifecycle manager
   */
  public static createInstance(): AnimationLifecycleManager {
    AnimationLifecycleManager.instance = new AnimationLifecycleManager();
    return AnimationLifecycleManager.instance;
  }
}

export default AnimationLifecycleManager.getInstance(); 