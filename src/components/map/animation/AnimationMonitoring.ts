/**
 * AnimationMonitoring.ts
 * 
 * Provides monitoring tools for animation health and performance.
 * Includes functions to track and fix animation issues during runtime.
 */

import mapboxgl from 'mapbox-gl';
import { AnimationManager } from './AnimationManager';
import { VehicleManager } from './VehicleManager';
import React from 'react';

interface AnimationMonitoringOptions {
  animationManager: AnimationManager;
  map: mapboxgl.Map;
  vehicleManagerRef: React.MutableRefObject<VehicleManager | null>;
  handleAnimationTick: (progress: number, position: [number, number], bearing: number) => void;
  onAnimationComplete: () => void;
  setVehiclePosition: (position: [number, number] | null) => void;
  setShowVehicle: (show: boolean) => void;
  setProgress: (progress: number) => void;
  setIsAnimating: (isAnimating: boolean) => void;
  initialRoute?: [number, number][];
}

/**
 * Starts monitoring animation performance and health
 * Returns a cleanup function
 */
export const startAnimationMonitoring = (options: AnimationMonitoringOptions) => {
  const {
    animationManager,
    map,
    vehicleManagerRef,
    handleAnimationTick,
    onAnimationComplete,
    setVehiclePosition,
    setShowVehicle,
    setProgress,
    setIsAnimating,
    initialRoute = []
  } = options;

  let lastProgressTime = Date.now();
  let lastProgress = 0;
  let monitoringInterval: NodeJS.Timeout;
  let visibilityCheckInterval: NodeJS.Timeout;
  
  // Separate monitoring from visibility checks for better performance
  // Monitor overall animation health
  monitoringInterval = setInterval(() => {
    try {
      const now = Date.now();
      const currentProgress = animationManager.getProgress();
      
      // Check if animation is stuck (no progress for 2 seconds while supposed to be animating)
      if (animationManager.isCurrentlyAnimating() && lastProgress === currentProgress && (now - lastProgressTime > 2000)) {
        const metrics = getAnimationPerformanceMetrics(animationManager, lastProgressTime);
        logAnimationError('Animation appears stuck', metrics);
        
        // Try to recover by forcing a progress update
        if (vehicleManagerRef.current) {
          // Force animation to continue from last position
          lastProgressTime = now;
          
          // Try to restart animation from current position
          try {
            console.log(`🔄 [${Date.now()}] Attempting to recover from stuck animation`);
            const currentPosition = animationManager.getCurrentPosition();
            const currentBearing = animationManager.getCurrentBearing();
            
            if (currentPosition) {
              // Force vehicle visibility
              vehicleManagerRef.current.forceVehicleVisibility();
              
              // Force position update
              vehicleManagerRef.current.updateVehiclePosition(currentPosition, currentBearing);
              
              // Force animation frame call
              handleAnimationTick(currentProgress, currentPosition, currentBearing);
            }
          } catch (recoveryError) {
            console.error('Error during animation recovery:', recoveryError);
          }
        }
      }
      
      // Update tracking variables
      if (currentProgress !== lastProgress) {
        lastProgressTime = now;
        lastProgress = currentProgress;
      }
    } catch (error) {
      console.error('Error in animation monitoring:', error);
    }
  }, 1000); // Check every second
  
  // Check and fix vehicle visibility more frequently
  visibilityCheckInterval = setInterval(() => {
    try {
      if (animationManager.isCurrentlyAnimating() && vehicleManagerRef.current) {
        const currentPosition = animationManager.getCurrentPosition();
        const currentBearing = animationManager.getCurrentBearing();
        
        if (currentPosition) {
          checkAndFixVehicleVisibility(
            vehicleManagerRef,
            currentPosition,
            currentBearing,
            setVehiclePosition,
            setShowVehicle
          );
        }
      }
    } catch (error) {
      console.error('Error in vehicle visibility check:', error);
    }
  }, 500); // Check every half-second
  
  // Return cleanup function
  return () => {
    clearInterval(monitoringInterval);
    clearInterval(visibilityCheckInterval);
    console.log(`🧹 [${Date.now()}] Animation monitoring stopped`);
  };
};

/**
 * Logs an animation error with performance context
 */
export const logAnimationError = (message: string, metrics: any) => {
  console.error(`🚨 [${Date.now()}] ANIMATION ERROR: ${message}`, metrics);
};

/**
 * Get current animation performance metrics
 */
export const getAnimationPerformanceMetrics = (animationManager: AnimationManager, lastUpdateTime: number) => {
  try {
    return {
      isAnimating: animationManager.isCurrentlyAnimating(),
      progress: animationManager.getProgress(),
      elapsedSinceLastUpdate: Date.now() - lastUpdateTime,
      position: animationManager.getCurrentPosition(),
      // Don't use properties that don't exist on AnimationManager
      timeSinceStart: Date.now() - lastUpdateTime,
      frameInfo: 'Not available' // Simplified value as frameRate isn't available
    };
  } catch (error) {
    return { error: 'Failed to get metrics', details: error };
  }
};

// Keep track of vehicle visibility fix attempts
let vehicleFixAttempts = 0;
const MAX_FIX_ATTEMPTS = 3;

/**
 * Checks if the vehicle is visible and attempts to fix it if not
 * Returns a boolean indicating success
 */
export const checkAndFixVehicleVisibility = (
  vehicleManagerRef: React.MutableRefObject<VehicleManager | null>,
  position: [number, number],
  bearing: number,
  setVehiclePosition: (position: [number, number] | null) => void,
  setShowVehicle: (show: boolean) => void
): boolean => {
  if (!vehicleManagerRef.current) {
    console.warn(`⚠️ [${Date.now()}] Vehicle manager not available for visibility check`);
    return false;
  }
  
  try {
    // First check if vehicle is visible
    const isVisible = vehicleManagerRef.current.checkVehicleVisibility();
    
    if (!isVisible) {
      console.warn(`⚠️ [${Date.now()}] Vehicle not visible, attempting to fix (attempt ${vehicleFixAttempts + 1}/${MAX_FIX_ATTEMPTS})`);
      
      // Track fix attempts
      vehicleFixAttempts++;
      
      // Try emergency fix if regular fixes have failed multiple times
      if (vehicleFixAttempts >= MAX_FIX_ATTEMPTS) {
        console.warn(`🚨 [${Date.now()}] Too many visibility fix attempts, using emergency fix`);
        
        // Apply emergency fix directly
        const emergencyFixResult = vehicleManagerRef.current.emergencyFixVehicleVisibility(position);
        
        // Reset fix attempts counter if successful
        if (emergencyFixResult) {
          vehicleFixAttempts = 0;
        }
        
        // Update position after emergency fix
        setTimeout(() => {
          if (vehicleManagerRef.current) {
            vehicleManagerRef.current.updateVehiclePosition(position, bearing);
          }
        }, 50);
        
        // Update React state
        setVehiclePosition(position);
        setShowVehicle(true);
        
        return emergencyFixResult;
      }
      
      // Try regular fix first
      vehicleManagerRef.current.forceVehicleVisibility();
      
      // Update position after forcing visibility
      setTimeout(() => {
        if (vehicleManagerRef.current) {
          const updateSuccess = vehicleManagerRef.current.updateVehiclePosition(position, bearing);
          
          if (!updateSuccess) {
            console.warn(`⚠️ [${Date.now()}] Position update failed after visibility fix`);
          }
        }
      }, 10);
      
      // Update React state as well
      setVehiclePosition(position);
      setShowVehicle(true);
      
      // Re-check visibility after fix
      setTimeout(() => {
        if (vehicleManagerRef.current) {
          const visibleAfterFix = vehicleManagerRef.current.checkVehicleVisibility();
          if (!visibleAfterFix) {
            console.error(`❌ [${Date.now()}] Vehicle still not visible after fix attempt`);
            
            // Leave for next cycle to increment the counter and potentially use emergency fix
          } else {
            // Reset counter on success
            vehicleFixAttempts = 0;
          }
        }
      }, 100);
      
      return true;
    } else {
      // Reset fix attempts counter when vehicle is visible
      vehicleFixAttempts = 0;
      return true;
    }
    
  } catch (error) {
    console.error(`❌ [${Date.now()}] Error fixing vehicle visibility:`, error);
    return false;
  }
}; 