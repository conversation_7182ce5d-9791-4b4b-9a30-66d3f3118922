/**
 * AnimationDebugTools.ts
 * 
 * Provides standardized debugging, logging, and performance measurement tools for animation components.
 * Features:
 * - Structured logging with levels (debug, info, warning, error, performance)
 * - Circular buffer for recent log entries
 * - Performance tracking utilities
 * - Animation monitoring capabilities
 * - Color-coded console output
 */

import type { LogLevel } from '@/types/LogLevel';

interface LogEntry {
  timestamp: number;
  level: LogLevel;
  message: string;
  data?: any;
}

interface AnimationMetrics {
  frameCount: number;
  lastFrameTime: number;
  fps: number;
}

const MAX_LOGS = 1000; // Maximum number of logs to keep in memory
const DEBUG_ENABLED = true; // Global flag to enable/disable debug functionality

class AnimationDebugTools {
  private static instance: AnimationDebugTools;
  private logs: LogEntry[] = [];
  private performanceStartTime: number = 0;
  private metrics: AnimationMetrics = {
    frameCount: 0,
    lastFrameTime: 0,
    fps: 0
  };
  private isMonitoring: boolean = false;
  private animationFrameId: number | null = null;

  private constructor() {}

  public static getInstance(): AnimationDebugTools {
    if (!AnimationDebugTools.instance) {
      AnimationDebugTools.instance = new AnimationDebugTools();
    }
    return AnimationDebugTools.instance;
  }

  /**
   * Log a message with the specified level and optional data
   */
  public log(level: LogLevel, message: string, data?: any): void {
    if (!DEBUG_ENABLED) return;

    const entry: LogEntry = {
      timestamp: Date.now(),
      level,
      message,
      data
    };

    // Add to circular buffer
    this.logs.push(entry);
    if (this.logs.length > MAX_LOGS) {
      this.logs.shift();
    }

    // Console output with color coding
    const colors = {
      debug: '#808080',
      info: '#4CAF50',
      warn: '#FFC107',
      error: '#F44336',
      performance: '#2196F3'
    };

    // Conditionally log to console, skip 'debug' level for now to reduce noise
    if (level !== 'debug' && level !== 'info') { // Also skip 'info' to reduce noise further
      console.log(
        `%c[${level.toUpperCase()}] ${message}`,
        `color: ${colors[level]}`,
        data || ''
      );
    }
  }

  /**
   * Get all logs or filter by level
   */
  public getLogs(level?: LogLevel): LogEntry[] {
    return level ? this.logs.filter(log => log.level === level) : this.logs;
  }

  /**
   * Clear all logs
   */
  public clearLogs(): void {
    this.logs = [];
  }

  /**
   * Start capturing performance metrics
   */
  public startPerformanceCapture(): void {
    this.performanceStartTime = performance.now();
    this.log('performance', 'Started performance capture');
  }

  /**
   * End performance capture and return duration
   */
  public endPerformanceCapture(operation: string): number {
    const duration = performance.now() - this.performanceStartTime;
    this.log('performance', `${operation} completed`, { durationMs: duration });
    return duration;
  }

  /**
   * Start monitoring animation performance
   */
  public startMonitoring(): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.metrics = {
      frameCount: 0,
      lastFrameTime: performance.now(),
      fps: 0
    };

    const animate = () => {
      if (!this.isMonitoring) return;

      const currentTime = performance.now();
      const deltaTime = currentTime - this.metrics.lastFrameTime;
      
      this.metrics.frameCount++;
      this.metrics.lastFrameTime = currentTime;
      this.metrics.fps = 1000 / deltaTime;

      // Log performance every 60 frames
      if (this.metrics.frameCount % 60 === 0) {
        this.log('performance', 'Animation metrics', {
          fps: Math.round(this.metrics.fps),
          frameCount: this.metrics.frameCount
        });
      }

      this.animationFrameId = requestAnimationFrame(animate);
    };

    this.animationFrameId = requestAnimationFrame(animate);
    this.log('info', 'Started animation monitoring');
  }

  /**
   * Stop monitoring animation performance
   */
  public stopMonitoring(): void {
    this.isMonitoring = false;
    if (this.animationFrameId !== null) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
    this.log('info', 'Stopped animation monitoring', this.metrics);
  }

  /**
   * Get current animation metrics
   */
  public getMetrics(): AnimationMetrics {
    return { ...this.metrics };
  }
}

// Export singleton instance
export default AnimationDebugTools.getInstance(); 