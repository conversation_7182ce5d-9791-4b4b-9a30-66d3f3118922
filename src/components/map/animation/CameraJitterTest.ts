/**
 * Camera Jitter Test Utility
 * 
 * This file contains test implementations for debugging camera jitter issues.
 * It isolates different camera control approaches to determine which method produces 
 * the most stable results.
 */

import mapboxgl from 'mapbox-gl';
import AnimationDebugTools from './AnimationDebugTools';
import { lerpAngle, exponentialSmoothing, criticalDamp } from '../utils/MathHelpers';

/**
 * Test Camera Controller that provides multiple approaches to 
 * updating the camera to determine which causes the least jitter
 */
export class CameraJitterTest {
  private map: mapboxgl.Map;
  private isTestActive = false;
  private testMode: TestMode = 'direct';
  private frameCount = 0;
  private startTime = 0;
  private prevPosition: [number, number] = [0, 0];
  private prevBearing = 0;
  private prevZoom = 0;
  private prevPitch = 0;
  
  // Buffers for moving average
  private bearingBuffer: number[] = [];
  private zoomBuffer: number[] = [];
  private positionBufferLng: number[] = [];
  private positionBufferLat: number[] = [];
  
  // Test parameters
  private testParameters = {
    updateInterval: 100, // ms
    movingAverageSize: 20,
    smoothingFactor: 0.1,
    hysteresisThreshold: 0.5,
    useJumpTo: false,
    useFlyTo: false,
    useEaseTo: true
  };
  
  // Record test results
  private testResults: {
    jitterDetected: number,
    framesProcessed: number,
    avgUpdateTime: number,
    maxUpdateTime: number
  } = {
    jitterDetected: 0,
    framesProcessed: 0,
    avgUpdateTime: 0,
    maxUpdateTime: 0
  };
  
  // Velocity tracking for critical damping
  private velocities = {
    zoom: 0,
    pitch: 0,
    lng: 0,
    lat: 0
  };
  
  private lastUpdateTime = 0;
  private updateTimes: number[] = [];

  constructor(map: mapboxgl.Map) {
    this.map = map;
  }
  
  /**
   * Start the camera jitter test
   */
  public startTest(mode: TestMode = 'direct'): void {
    this.isTestActive = true;
    this.testMode = mode;
    this.frameCount = 0;
    this.startTime = Date.now();
    this.prevPosition = [this.map.getCenter().lng, this.map.getCenter().lat];
    this.prevBearing = this.map.getBearing();
    this.prevZoom = this.map.getZoom();
    this.prevPitch = this.map.getPitch();
    
    // Reset test results
    this.testResults = {
      jitterDetected: 0,
      framesProcessed: 0,
      avgUpdateTime: 0,
      maxUpdateTime: 0
    };
    
    AnimationDebugTools.log(`Starting camera jitter test: ${mode} mode`);
    
    // Start the test animation
    this.startTestAnimation();
  }
  
  /**
   * Stop the camera jitter test
   */
  public stopTest(): void {
    this.isTestActive = false;
    
    // Calculate average update time
    if (this.updateTimes.length > 0) {
      this.testResults.avgUpdateTime = this.updateTimes.reduce((a, b) => a + b, 0) / this.updateTimes.length;
    }
    
    AnimationDebugTools.log('Camera jitter test results:', {
      mode: this.testMode,
      jitterDetected: this.testResults.jitterDetected,
      framesProcessed: this.testResults.framesProcessed,
      avgUpdateTime: `${this.testResults.avgUpdateTime.toFixed(2)}ms`,
      maxUpdateTime: `${this.testResults.maxUpdateTime.toFixed(2)}ms`
    });
  }
  
  /**
   * Start the test animation loop
   */
  private startTestAnimation(): void {
    if (!this.isTestActive) return;
    
    const testPath = this.generateTestPath();
    let currentPointIndex = 0;
    
    const animate = () => {
      if (!this.isTestActive) return;
      
      // Get the next target point
      const targetPoint = testPath[currentPointIndex];
      
      // Update camera based on test mode
      this.updateCameraForTest(targetPoint);
      
      // Move to next point
      currentPointIndex = (currentPointIndex + 1) % testPath.length;
      
      // Continue the animation
      requestAnimationFrame(animate);
    };
    
    // Start the animation
    animate();
  }
  
  /**
   * Generate a test path for the camera to follow
   */
  private generateTestPath(): { position: [number, number]; bearing: number; zoom: number; pitch: number }[] {
    const center = this.map.getCenter();
    const path = [];
    
    // Create a circular path around the current center
    const radius = 0.01; // ~1km
    const steps = 60;
    
    for (let i = 0; i < steps; i++) {
      const angle = (i / steps) * Math.PI * 2;
      const lng = center.lng + Math.cos(angle) * radius;
      const lat = center.lat + Math.sin(angle) * radius;
      
      // Vary zoom slightly
      const zoom = this.map.getZoom() + Math.sin(angle) * 0.5;
      
      // Vary bearing to follow the circle
      const bearing = (angle * 180 / Math.PI + 90) % 360;
      
      // Vary pitch slightly
      const pitch = 45 + Math.sin(angle * 2) * 10;
      
      path.push({
        position: [lng, lat],
        bearing,
        zoom,
        pitch
      });
    }
    
    return path;
  }
  
  /**
   * Update the camera based on the current test mode
   */
  private updateCameraForTest(target: { 
    position: [number, number]; 
    bearing: number; 
    zoom: number; 
    pitch: number 
  }): void {
    const updateStartTime = performance.now();
    
    // Apply the appropriate update method based on test mode
    switch (this.testMode) {
      case 'direct':
        this.updateDirect(target);
        break;
        
      case 'smoothed':
        this.updateSmoothed(target);
        break;
        
      case 'moving-average':
        this.updateMovingAverage(target);
        break;
        
      case 'hysteresis':
        this.updateHysteresis(target);
        break;
        
      case 'critical-damping':
        this.updateCriticalDamping(target);
        break;
    }
    
    // Record update time
    const updateEndTime = performance.now();
    const updateDuration = updateEndTime - updateStartTime;
    this.updateTimes.push(updateDuration);
    this.testResults.maxUpdateTime = Math.max(this.testResults.maxUpdateTime, updateDuration);
    
    // Increment frame counter
    this.frameCount++;
    this.testResults.framesProcessed++;
    
    // Check for jitter by comparing with previous update
    this.detectJitter();
  }
  
  /**
   * Direct camera update without smoothing
   */
  private updateDirect(target: { 
    position: [number, number]; 
    bearing: number; 
    zoom: number; 
    pitch: number 
  }): void {
    if (this.testParameters.useJumpTo) {
      // Direct jump - no animation
      this.map.jumpTo({
        center: target.position,
        bearing: target.bearing,
        zoom: target.zoom,
        pitch: target.pitch
      });
    } else if (this.testParameters.useFlyTo) {
      // Fly to - smooth curve
      this.map.flyTo({
        center: target.position,
        bearing: target.bearing,
        zoom: target.zoom,
        pitch: target.pitch,
        duration: this.testParameters.updateInterval,
        essential: true
      });
    } else {
      // Ease to - linear animation
      this.map.easeTo({
        center: target.position,
        bearing: target.bearing,
        zoom: target.zoom,
        pitch: target.pitch,
        duration: this.testParameters.updateInterval,
        essential: true
      });
    }
    
    // Save for next iteration
    this.prevPosition = target.position;
    this.prevBearing = target.bearing;
    this.prevZoom = target.zoom;
    this.prevPitch = target.pitch;
  }
  
  /**
   * Smoothed camera update using exponential smoothing
   */
  private updateSmoothed(target: { 
    position: [number, number]; 
    bearing: number; 
    zoom: number; 
    pitch: number 
  }): void {
    const factor = this.testParameters.smoothingFactor;
    
    // Apply smoothing to all parameters
    const smoothedZoom = exponentialSmoothing(this.prevZoom, target.zoom, factor);
    const smoothedBearing = lerpAngle(this.prevBearing, target.bearing, factor);
    const smoothedPitch = exponentialSmoothing(this.prevPitch, target.pitch, factor);
    
    // Smooth position
    const smoothedLng = exponentialSmoothing(this.prevPosition[0], target.position[0], factor);
    const smoothedLat = exponentialSmoothing(this.prevPosition[1], target.position[1], factor);
    
    if (this.testParameters.useJumpTo) {
      // Apply directly without animation
      this.map.jumpTo({
        center: [smoothedLng, smoothedLat],
        bearing: smoothedBearing,
        zoom: smoothedZoom,
        pitch: smoothedPitch
      });
    } else {
      // Use easeTo for extra smoothness
      this.map.easeTo({
        center: [smoothedLng, smoothedLat],
        bearing: smoothedBearing,
        zoom: smoothedZoom,
        pitch: smoothedPitch,
        duration: 0, // Immediate update
        essential: true
      });
    }
    
    // Save for next iteration
    this.prevPosition = [smoothedLng, smoothedLat];
    this.prevBearing = smoothedBearing;
    this.prevZoom = smoothedZoom;
    this.prevPitch = smoothedPitch;
  }
  
  /**
   * Moving average camera update
   */
  private updateMovingAverage(target: { 
    position: [number, number]; 
    bearing: number; 
    zoom: number; 
    pitch: number 
  }): void {
    const bufferSize = this.testParameters.movingAverageSize;
    
    // Add to buffers
    this.bearingBuffer.push(target.bearing);
    this.zoomBuffer.push(target.zoom);
    this.positionBufferLng.push(target.position[0]);
    this.positionBufferLat.push(target.position[1]);
    
    // Trim buffers
    if (this.bearingBuffer.length > bufferSize) this.bearingBuffer.shift();
    if (this.zoomBuffer.length > bufferSize) this.zoomBuffer.shift();
    if (this.positionBufferLng.length > bufferSize) this.positionBufferLng.shift();
    if (this.positionBufferLat.length > bufferSize) this.positionBufferLat.shift();
    
    // Calculate averages - special handling for bearing
    let sumSin = 0;
    let sumCos = 0;
    
    this.bearingBuffer.forEach(bearing => {
      const rad = bearing * Math.PI / 180;
      sumSin += Math.sin(rad);
      sumCos += Math.cos(rad);
    });
    
    const avgBearing = ((Math.atan2(sumSin, sumCos) * 180 / Math.PI) + 360) % 360;
    const avgZoom = this.zoomBuffer.reduce((sum, val) => sum + val, 0) / this.zoomBuffer.length;
    const avgLng = this.positionBufferLng.reduce((sum, val) => sum + val, 0) / this.positionBufferLng.length;
    const avgLat = this.positionBufferLat.reduce((sum, val) => sum + val, 0) / this.positionBufferLat.length;
    
    // Create properly typed coordinates
    const coordinates: [number, number] = [avgLng, avgLat];
    
    // Apply the smoothed values
    this.map.jumpTo({
      center: coordinates,
      bearing: avgBearing,
      zoom: avgZoom,
      pitch: target.pitch // Don't smooth pitch for this test
    });
    
    // Save for next iteration
    this.prevPosition = coordinates;
    this.prevBearing = avgBearing;
    this.prevZoom = avgZoom;
  }
  
  /**
   * Hysteresis camera update
   */
  private updateHysteresis(target: { 
    position: [number, number]; 
    bearing: number; 
    zoom: number; 
    pitch: number 
  }): void {
    const threshold = this.testParameters.hysteresisThreshold;
    const factor = this.testParameters.smoothingFactor;
    
    // Apply hysteresis
    let newZoom = this.prevZoom;
    let newBearing = this.prevBearing;
    let newPitch = this.prevPitch;
    const newPosition = [...this.prevPosition];
    
    // Only update zoom if change is above threshold
    if (Math.abs(target.zoom - this.prevZoom) > threshold) {
      newZoom = this.prevZoom + (target.zoom - this.prevZoom) * factor;
    }
    
    // Special handling for bearing (circular)
    const bearingDiff = Math.abs(((target.bearing - this.prevBearing + 180) % 360) - 180);
    if (bearingDiff > threshold) {
      // Use lerpAngle for proper circular interpolation
      newBearing = lerpAngle(this.prevBearing, target.bearing, factor);
    }
    
    // Only update pitch if change is above threshold
    if (Math.abs(target.pitch - this.prevPitch) > threshold) {
      newPitch = this.prevPitch + (target.pitch - this.prevPitch) * factor;
    }
    
    // Only update position if change is above threshold
    const posDistThreshold = 0.0001; // Approx 10 meters
    const lngDiff = Math.abs(target.position[0] - this.prevPosition[0]);
    const latDiff = Math.abs(target.position[1] - this.prevPosition[1]);
    
    if (lngDiff > posDistThreshold) {
      newPosition[0] = this.prevPosition[0] + (target.position[0] - this.prevPosition[0]) * factor;
    }
    
    if (latDiff > posDistThreshold) {
      newPosition[1] = this.prevPosition[1] + (target.position[1] - this.prevPosition[1]) * factor;
    }
    
    // Apply the update
    this.map.jumpTo({
      center: newPosition,
      bearing: newBearing,
      zoom: newZoom,
      pitch: newPitch
    });
    
    // Save for next iteration
    this.prevPosition = newPosition;
    this.prevBearing = newBearing;
    this.prevZoom = newZoom;
    this.prevPitch = newPitch;
  }
  
  /**
   * Critical damping camera update
   */
  private updateCriticalDamping(target: { 
    position: [number, number]; 
    bearing: number; 
    zoom: number; 
    pitch: number 
  }): void {
    const dampingRatio = 1.0; // Critical damping
    const dt = 1/60; // Assume 60fps
    
    // Apply critical damping to zoom
    const zoomResult = criticalDamp(
      this.prevZoom,
      target.zoom,
      this.velocities.zoom,
      dampingRatio,
      dt
    );
    
    // Apply critical damping to pitch
    const pitchResult = criticalDamp(
      this.prevPitch,
      target.pitch,
      this.velocities.pitch,
      dampingRatio,
      dt
    );
    
    // Apply critical damping to position
    const lngResult = criticalDamp(
      this.prevPosition[0],
      target.position[0],
      this.velocities.lng,
      dampingRatio,
      dt
    );
    
    const latResult = criticalDamp(
      this.prevPosition[1],
      target.position[1],
      this.velocities.lat,
      dampingRatio,
      dt
    );
    
    // Update velocities
    this.velocities.zoom = zoomResult.velocity;
    this.velocities.pitch = pitchResult.velocity;
    this.velocities.lng = lngResult.velocity;
    this.velocities.lat = latResult.velocity;
    
    // Special handling for bearing (circular)
    // For bearing we'll use lerpAngle instead of critical damping
    const bearingFactor = 0.1;
    const newBearing = lerpAngle(this.prevBearing, target.bearing, bearingFactor);
    
    // Apply the update
    this.map.jumpTo({
      center: [lngResult.value, latResult.value],
      bearing: newBearing,
      zoom: zoomResult.value,
      pitch: pitchResult.value
    });
    
    // Save for next iteration
    this.prevPosition = [lngResult.value, latResult.value];
    this.prevBearing = newBearing;
    this.prevZoom = zoomResult.value;
    this.prevPitch = pitchResult.value;
  }
  
  /**
   * Detect jitter by analyzing rate of change
   */
  private detectJitter(): void {
    // Implement a simple jitter detection by checking for oscillation
    // This is just a placeholder - a real implementation would be more sophisticated
    const jitterDetected = false;
    
    if (jitterDetected) {
      this.testResults.jitterDetected++;
    }
  }
  
  /**
   * Sets the test parameters
   */
  public setTestParameters(params: Partial<typeof this.testParameters>): void {
    this.testParameters = { ...this.testParameters, ...params };
  }
  
  /**
   * Gets the current test results
   */
  public getTestResults(): typeof this.testResults {
    return { ...this.testResults };
  }
}

// Define the available test modes
type TestMode = 'direct' | 'smoothed' | 'moving-average' | 'hysteresis' | 'critical-damping';

// Export a factory function
export const createCameraJitterTest = (map: mapboxgl.Map): CameraJitterTest => {
  return new CameraJitterTest(map);
}; 