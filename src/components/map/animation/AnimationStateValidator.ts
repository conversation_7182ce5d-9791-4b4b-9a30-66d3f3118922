/**
 * AnimationStateValidator.ts
 * 
 * Provides validation utilities for animation state transitions.
 * This helps enforce state machine rules and prevent invalid operations.
 */

import { AnimationPhase } from '@/types/AnimationTypes';
import { Position } from '@/types/Position';
import AnimationDebugTools from './AnimationDebugTools';

/**
 * Result of a validation check
 */
export interface ValidationResult {
  isValid: boolean;
  message: string;
}

/**
 * Animation StateValidator
 * Enforces rules for animation state transitions to prevent errors
 */
export class AnimationStateValidator {
  /**
   * Validate transition to the initialized phase
   * @param isInitialized Current initialization state
   */
  public static validateInitialization(isInitialized: boolean): ValidationResult {
    if (isInitialized) {
      return {
        isValid: false,
        message: 'Animation system is already initialized'
      };
    }
    
    return {
      isValid: true,
      message: 'Animation initialization valid'
    };
  }
  
  /**
   * Validate animation preparation
   * @param isInitialized Initialization state
   * @param route Route data
   * @param duration Animation duration
   */
  public static validatePreparation(
    isInitialized: boolean,
    route: Position[],
    duration: number
  ): ValidationResult {
    if (!isInitialized) {
      return {
        isValid: false,
        message: 'Cannot prepare animation: Animation system not initialized'
      };
    }
    
    if (!route || route.length < 2) {
      return {
        isValid: false,
        message: `Cannot prepare animation: Route must have at least 2 points, got ${route?.length || 0}`
      };
    }
    
    if (duration <= 0) {
      return {
        isValid: false,
        message: `Cannot prepare animation: Duration must be positive, got ${duration}`
      };
    }
    
    return {
      isValid: true,
      message: 'Animation preparation valid'
    };
  }
  
  /**
   * Validate animation start
   * @param isInitialized Initialization state
   * @param currentPhase Current animation phase
   */
  public static validateStart(
    isInitialized: boolean,
    currentPhase: AnimationPhase
  ): ValidationResult {
    if (!isInitialized) {
      return {
        isValid: false,
        message: 'Cannot start animation: Animation system not initialized'
      };
    }
    
    const validStartPhases = [AnimationPhase.PREPARED, AnimationPhase.PAUSED];
    if (!validStartPhases.includes(currentPhase)) {
      return {
        isValid: false,
        message: `Cannot start animation: Invalid phase ${currentPhase}, expected ${validStartPhases.join(' or ')}`
      };
    }
    
    return {
      isValid: true,
      message: 'Animation start valid'
    };
  }
  
  /**
   * Validate animation pause
   * @param isInitialized Initialization state
   * @param currentPhase Current animation phase
   */
  public static validatePause(
    isInitialized: boolean,
    currentPhase: AnimationPhase
  ): ValidationResult {
    if (!isInitialized) {
      return {
        isValid: false,
        message: 'Cannot pause animation: Animation system not initialized'
      };
    }
    
    if (currentPhase !== AnimationPhase.PLAYING) {
      return {
        isValid: false,
        message: `Cannot pause animation: Invalid phase ${currentPhase}, expected ${AnimationPhase.PLAYING}`
      };
    }
    
    return {
      isValid: true,
      message: 'Animation pause valid'
    };
  }
  
  /**
   * Validate animation resume
   * @param isInitialized Initialization state
   * @param currentPhase Current animation phase
   */
  public static validateResume(
    isInitialized: boolean,
    currentPhase: AnimationPhase
  ): ValidationResult {
    if (!isInitialized) {
      return {
        isValid: false,
        message: 'Cannot resume animation: Animation system not initialized'
      };
    }
    
    if (currentPhase !== AnimationPhase.PAUSED) {
      return {
        isValid: false,
        message: `Cannot resume animation: Invalid phase ${currentPhase}, expected ${AnimationPhase.PAUSED}`
      };
    }
    
    return {
      isValid: true,
      message: 'Animation resume valid'
    };
  }
  
  /**
   * Validate setting animation progress
   * @param isInitialized Initialization state
   * @param currentPhase Current animation phase
   * @param progress Progress value to set
   */
  public static validateSetProgress(
    isInitialized: boolean,
    currentPhase: AnimationPhase,
    progress: number
  ): ValidationResult {
    if (!isInitialized) {
      return {
        isValid: false,
        message: 'Cannot set progress: Animation system not initialized'
      };
    }
    
    if (currentPhase !== AnimationPhase.PLAYING && currentPhase !== AnimationPhase.PAUSED) {
      return {
        isValid: false,
        message: `Cannot set progress: Invalid phase ${currentPhase}, expected ${AnimationPhase.PLAYING} or ${AnimationPhase.PAUSED}`
      };
    }
    
    if (progress < 0 || progress > 1) {
      return {
        isValid: false,
        message: `Cannot set progress: Value must be between 0 and 1, got ${progress}`
      };
    }
    
    return {
      isValid: true,
      message: 'Set progress valid'
    };
  }
  
  /**
   * Validate animation component registration
   * @param componentId Component identifier
   */
  public static validateComponentRegistration(componentId: string): ValidationResult {
    if (!componentId || componentId.trim() === '') {
      return {
        isValid: false,
        message: 'Cannot register component: Invalid component ID'
      };
    }
    
    return {
      isValid: true,
      message: 'Component registration valid'
    };
  }
  
  /**
   * Log validation failure
   * @param result Validation result
   */
  public static logValidationFailure(result: ValidationResult): void {
    if (!result.isValid) {
      AnimationDebugTools.log('error', result.message);
    }
  }
}

export default AnimationStateValidator; 