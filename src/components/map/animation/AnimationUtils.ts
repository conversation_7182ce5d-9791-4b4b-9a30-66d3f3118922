import * as turf from '@turf/turf';
import { ANIMATION_DURATION } from '../utils/types';
import mapboxgl from 'mapbox-gl';

/**
 * Calculate animation progress based on elapsed time
 */
export const calculateProgress = (elapsedTime: number): number => {
  return Math.min(elapsedTime / ANIMATION_DURATION, 1);
};

/**
 * Calculate the segment distances between route points
 */
export const calculateSegmentDistances = (
  routePoints: Array<[number, number]>
): number[] => {
  const distances: number[] = [];
  for (let i = 0; i < routePoints.length - 1; i++) {
    const distance = turf.distance(
      turf.point(routePoints[i]),
      turf.point(routePoints[i + 1]),
      { units: 'kilometers' }
    );
    distances.push(distance);
  }
  return distances;
};

/**
 * Calculate the current position along the route based on progress
 */
export const getCurrentPosition = (
  routePoints: Array<[number, number]>,
  progress: number
): { currentPoint: [number, number]; nextPoint: [number, number] | null; pointIndex: number } => {
  if (routePoints.length === 0) {
    console.error('Route points array is empty');
    return { currentPoint: [0, 0], nextPoint: null, pointIndex: 0 };
  }

  if (routePoints.length === 1) {
    return { currentPoint: routePoints[0], nextPoint: null, pointIndex: 0 };
  }

  if (progress >= 1) {
    return {
      currentPoint: routePoints[routePoints.length - 1],
      nextPoint: null,
      pointIndex: routePoints.length - 1,
    };
  }

  // Calculate total route distance
  const totalDistanceKm = routePoints.reduce((total, point, index) => {
    if (index === 0) return 0;
    const prevPoint = routePoints[index - 1];
    const segmentDistance = turf.distance(
      turf.point(prevPoint),
      turf.point(point),
      { units: 'kilometers' }
    );
    return total + segmentDistance;
  }, 0);

  // Calculate how far we've traveled based on progress
  const distanceTraveled = progress * totalDistanceKm;

  // Find which segment we're in
  let accumulatedDistance = 0;
  let currentSegment = 0;

  for (let i = 1; i < routePoints.length; i++) {
    const prevPoint = routePoints[i - 1];
    const currentPoint = routePoints[i];
    
    const segmentDistance = turf.distance(
      turf.point(prevPoint),
      turf.point(currentPoint),
      { units: 'kilometers' }
    );
    
    if (accumulatedDistance + segmentDistance >= distanceTraveled) {
      currentSegment = i - 1;
      break;
    }
    
    accumulatedDistance += segmentDistance;
  }

  // Calculate progress within the current segment
  const start = routePoints[currentSegment];
  const end = routePoints[currentSegment + 1];
  const segmentDistance = turf.distance(
    turf.point(start),
    turf.point(end),
    { units: 'kilometers' }
  );
  
  const segmentProgress = segmentDistance === 0 
    ? 0 
    : (distanceTraveled - accumulatedDistance) / segmentDistance;

  // Linearly interpolate between start and end of current segment
  const currentPoint: [number, number] = [
    start[0] + segmentProgress * (end[0] - start[0]),
    start[1] + segmentProgress * (end[1] - start[1])
  ];

  return {
    currentPoint,
    nextPoint: currentSegment < routePoints.length - 1 ? end : null,
    pointIndex: currentSegment
  };
};

/**
 * Calculate bearing between two points (angle in degrees)
 */
export const calculateBearing = (
  start: [number, number],
  end: [number, number]
): number => {
  try {
    return turf.bearing(
      turf.point(start),
      turf.point(end)
    );
  } catch (error) {
    console.error('Error calculating bearing:', error);
    return 0;
  }
};

/**
 * Shows an announcement message on the map
 */
export const showAnnouncement = (
  map: mapboxgl.Map,
  message: string, 
  duration: number = 3000
): void => {
  try {
    // Create the announcement element if it doesn't exist
    let announcement = document.getElementById('map-announcement');
    
    if (!announcement) {
      announcement = document.createElement('div');
      announcement.id = 'map-announcement';
      announcement.style.position = 'absolute';
      announcement.style.top = '50%';
      announcement.style.left = '50%';
      announcement.style.transform = 'translate(-50%, -50%)';
      announcement.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
      announcement.style.color = 'white';
      announcement.style.padding = '16px 24px';
      announcement.style.borderRadius = '8px';
      announcement.style.fontSize = '16px';
      announcement.style.fontWeight = 'bold';
      announcement.style.zIndex = '1000';
      announcement.style.textAlign = 'center';
      announcement.style.maxWidth = '300px';
      announcement.style.transition = 'opacity 0.3s ease-in-out';
      announcement.style.opacity = '0';
      
      const container = map.getContainer();
      container.appendChild(announcement);
    }
    
    // Set the message
    announcement.textContent = message;
    
    // Show the announcement
  setTimeout(() => {
      if (announcement) {
        announcement.style.opacity = '1';
      }
  }, 10);
    
    // Hide the announcement after duration
    setTimeout(() => {
      if (announcement) {
        announcement.style.opacity = '0';
        
        // Remove the element after transition
        setTimeout(() => {
          if (announcement && announcement.parentNode) {
            announcement.parentNode.removeChild(announcement);
          }
        }, 300);
      }
  }, duration);
  } catch (error) {
    console.error('Error showing announcement:', error);
  }
};

/**
 * Shows a loading indicator on the map
 * @param map Map instance
 * @param message Optional message to display
 * @returns Function to hide the indicator
 */
export const showLoadingIndicator = (
  map: mapboxgl.Map | undefined,
  message: string = 'Loading...'
): (() => void) => {
  // Safely check if map is valid and has getContainer method
  if (!map || typeof map.getContainer !== 'function') {
    console.warn(`⚠️ [${Date.now()}] Cannot show loading indicator - map is invalid or missing getContainer method`);
    return () => {};
  }
  
  try {
    // Create container
    const container = document.createElement('div');
    container.className = 'journey-loading-indicator';
    container.setAttribute('style', `
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 15px 25px;
      border-radius: 25px;
      font-family: sans-serif;
      z-index: 1000;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    `);
    
    // Add spinner
    const spinner = document.createElement('div');
    spinner.className = 'journey-loading-spinner';
    spinner.setAttribute('style', `
      width: 40px;
      height: 40px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: white;
      animation: journey-spinner 1s linear infinite;
      margin-bottom: 15px;
    `);
    
    // Add spinner animation
    if (!document.getElementById('journey-spinner-style')) {
      const style = document.createElement('style');
      style.id = 'journey-spinner-style';
      style.textContent = `
        @keyframes journey-spinner {
          to { transform: rotate(360deg); }
        }
      `;
      document.head.appendChild(style);
    }
    
    // Add message
    const messageElement = document.createElement('div');
    messageElement.textContent = message;
    
    // Assemble and add to container
    container.appendChild(spinner);
    container.appendChild(messageElement);
    
    try {
      // Add to map container - safely get the container
      const mapContainer = map.getContainer();
      if (mapContainer) {
        mapContainer.appendChild(container);
        console.log(`📊 [${Date.now()}] Loading indicator shown: "${message}"`);
      } else {
        // Fallback to adding to body if map container not available
        document.body.appendChild(container);
        console.log(`📊 [${Date.now()}] Loading indicator attached to body (fallback): "${message}"`);
      }
    } catch (error) {
      // Fallback to document.body if getContainer fails
      console.warn(`⚠️ [${Date.now()}] Error accessing map container, using body instead:`, error);
      document.body.appendChild(container);
      console.log(`📊 [${Date.now()}] Loading indicator attached to body (fallback): "${message}"`);
    }
    
    // Return cleanup function
    return () => {
      if (container && container.parentNode) {
        container.parentNode.removeChild(container);
        console.log(`📊 [${Date.now()}] Loading indicator removed`);
      }
    };
  } catch (error) {
    console.error(`❌ [${Date.now()}] Error showing loading indicator:`, error);
    return () => {};
  }
};

/**
 * Shows a countdown on the map.
 * @param map Map instance (optional, if not provided, countdown appears globally).
 * @param durationSeconds Duration of the countdown in seconds.
 * @param onTick Optional callback that receives the remaining seconds on each tick.
 * @returns Promise that resolves when the countdown is complete.
 */
export const showCountdown = (
  durationSeconds: number, 
  onTick?: (remainingSeconds: number) => void,
  map?: mapboxgl.Map | undefined // Made map optional and last
): Promise<void> => {
  return new Promise((resolve) => {
    let remaining = durationSeconds;
      
    const countdownElement = document.createElement('div');
    countdownElement.id = 'map-countdown-timer';
    countdownElement.setAttribute('style', `
        position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 72px;
      font-weight: bold;
        color: white;
      background-color: rgba(0,0,0,0.6);
      padding: 20px 40px;
      border-radius: 10px;
      z-index: 2000;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
      `);
      
    const targetParent = map ? map.getContainer() : document.body;
    targetParent.appendChild(countdownElement);

    if (onTick) {
      onTick(remaining);
    }
    countdownElement.textContent = String(remaining);

    const intervalId = setInterval(() => {
      remaining -= 1;
      if (onTick) {
        onTick(remaining);
          }
      countdownElement.textContent = String(remaining);

      if (remaining <= 0) {
        clearInterval(intervalId);
        if (countdownElement.parentNode) {
          countdownElement.parentNode.removeChild(countdownElement);
        }
          resolve();
        }
    }, 1000);
  });
};

export default {
  showAnnouncement,
  showCountdown,
  showLoadingIndicator
}; 