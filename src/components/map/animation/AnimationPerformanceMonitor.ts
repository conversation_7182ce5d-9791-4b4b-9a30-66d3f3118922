/**
 * AnimationPerformanceMonitor.ts
 * 
 * Provides tools for monitoring animation performance:
 * - Frame rate tracking
 * - CPU and memory usage monitoring
 * - Animation state visualization
 * - Performance logging and alerting
 * 
 * This follows the Animation Architecture rule of implementing
 * performance monitoring and debug capabilities.
 */

import AnimationDebugTools from './AnimationDebugTools';

interface PerformanceMetrics {
  fps: number;
  averageFps: number;
  minFps: number;
  maxFps: number;
  frameTime: number;
  averageFrameTime: number;
  totalFrames: number;
  droppedFrames: number;
  memoryUsage?: number;
  cpuUsage?: number;
  lastUpdateTime: number;
}

interface PerformanceLogEntry {
  timestamp: number;
  metrics: PerformanceMetrics;
  animationState?: any;
  error?: string;
}

const PERFORMANCE_THRESHOLDS = {
  LOW_FPS: 15,
  CRITICAL_FPS: 10,
  HIGH_FRAME_TIME: 100,
  MAX_LOG_ENTRIES: 100,
  LOG_INTERVAL: 5000, // 5 seconds
};

export class AnimationPerformanceMonitor {
  private static instance: AnimationPerformanceMonitor;
  
  private metrics: PerformanceMetrics = {
    fps: 0,
    averageFps: 0,
    minFps: Infinity,
    maxFps: 0,
    frameTime: 0,
    averageFrameTime: 0,
    totalFrames: 0,
    droppedFrames: 0,
    lastUpdateTime: 0
  };
  
  private history: PerformanceLogEntry[] = [];
  private isMonitoring: boolean = false;
  private lastFrameTime: number = 0;
  private frameTimestamps: number[] = [];
  private animationStateProvider: (() => any) | null = null;
  private lowFpsCallback: ((fps: number) => void) | null = null;
  private intervalId: number | null = null;
  private isDebugPanelVisible: boolean = false;
  private debugPanelElement: HTMLElement | null = null;
  
  private constructor() {}
  
  public static getInstance(): AnimationPerformanceMonitor {
    if (!this.instance) {
      this.instance = new AnimationPerformanceMonitor();
    }
    return this.instance;
  }
  
  /**
   * Start monitoring animation performance
   */
  public start(options: {
    animationStateProvider?: () => any;
    lowFpsCallback?: (fps: number) => void;
    showDebugPanel?: boolean;
  } = {}): void {
    if (this.isMonitoring) {
      return;
    }
    
    this.isMonitoring = true;
    this.lastFrameTime = performance.now();
    this.frameTimestamps = [];
    this.history = [];
    this.animationStateProvider = options.animationStateProvider || null;
    this.lowFpsCallback = options.lowFpsCallback || null;
    
    // Create requestAnimationFrame loop for frame timing
    const monitorFrame = (timestamp: number) => {
      if (!this.isMonitoring) return;
      
      // Calculate frame time
      const now = performance.now();
      const frameTime = now - this.lastFrameTime;
      this.lastFrameTime = now;
      
      // Store timestamp for FPS calculation
      this.frameTimestamps.push(now);
      
      // Remove old timestamps (older than 1 second)
      while (this.frameTimestamps.length > 0 && this.frameTimestamps[0] < now - 1000) {
        this.frameTimestamps.shift();
      }
      
      // Calculate FPS
      const currentFps = this.frameTimestamps.length;
      
      // Update metrics
      this.metrics.fps = currentFps;
      this.metrics.frameTime = frameTime;
      this.metrics.totalFrames++;
      this.metrics.minFps = Math.min(this.metrics.minFps, currentFps);
      this.metrics.maxFps = Math.max(this.metrics.maxFps, currentFps);
      
      // Update averages (exponential moving average)
      if (this.metrics.totalFrames === 1) {
        this.metrics.averageFps = currentFps;
        this.metrics.averageFrameTime = frameTime;
      } else {
        this.metrics.averageFps = this.metrics.averageFps * 0.95 + currentFps * 0.05;
        this.metrics.averageFrameTime = this.metrics.averageFrameTime * 0.95 + frameTime * 0.05;
      }
      
      // Check for dropped frames (frame time > 50ms means likely dropped frames)
      if (frameTime > 50) {
        const estimatedDroppedFrames = Math.floor(frameTime / 16.67) - 1;
        if (estimatedDroppedFrames > 0) {
          this.metrics.droppedFrames += estimatedDroppedFrames;
        }
      }
      
      // Alert on low FPS
      if (this.lowFpsCallback && currentFps < PERFORMANCE_THRESHOLDS.LOW_FPS) {
        this.lowFpsCallback(currentFps);
      }
      
      // Update debug panel if visible
      if (this.isDebugPanelVisible) {
        this.updateDebugPanel();
      }
      
      // Continue monitoring
      requestAnimationFrame(monitorFrame);
    };
    
    // Start monitoring loop
    requestAnimationFrame(monitorFrame);
    
    // Set up interval for logging
    this.intervalId = window.setInterval(() => {
      this.logPerformance();
    }, PERFORMANCE_THRESHOLDS.LOG_INTERVAL);
    
    // Create debug panel if requested
    if (options.showDebugPanel) {
      this.showDebugPanel();
    }
    
    AnimationDebugTools.log('performance', 'Animation performance monitoring started');
  }
  
  /**
   * Stop performance monitoring
   */
  public stop(): void {
    this.isMonitoring = false;
    
    // Clear logging interval
    if (this.intervalId !== null) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    
    // Remove debug panel
    if (this.debugPanelElement && this.debugPanelElement.parentNode) {
      this.debugPanelElement.parentNode.removeChild(this.debugPanelElement);
      this.debugPanelElement = null;
    }
    
    this.isDebugPanelVisible = false;
    
    AnimationDebugTools.log('performance', 'Animation performance monitoring stopped');
  }
  
  /**
   * Get current performance metrics
   */
  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }
  
  /**
   * Get performance history
   */
  public getHistory(): PerformanceLogEntry[] {
    return [...this.history];
  }
  
  /**
   * Log an error with performance context
   */
  public logError(error: Error | string): void {
    const errorMessage = error instanceof Error ? error.message : error;
    
    this.history.push({
      timestamp: Date.now(),
      metrics: { ...this.metrics },
      animationState: this.animationStateProvider ? this.animationStateProvider() : undefined,
      error: errorMessage
    });
    
    // Trim history if too large
    if (this.history.length > PERFORMANCE_THRESHOLDS.MAX_LOG_ENTRIES) {
      this.history = this.history.slice(-PERFORMANCE_THRESHOLDS.MAX_LOG_ENTRIES);
    }
    
    AnimationDebugTools.log('error', `Animation error: ${errorMessage}`, {
      metrics: this.metrics,
      animationState: this.animationStateProvider ? this.animationStateProvider() : 'Not available'
    }, 'error');
  }
  
  /**
   * Show or hide performance debug panel
   */
  public showDebugPanel(): void {
    if (this.isDebugPanelVisible) {
      return;
    }
    
    // Create panel if it doesn't exist
    if (!this.debugPanelElement) {
      this.createDebugPanel();
    }
    
    // Show panel
    if (this.debugPanelElement) {
      this.debugPanelElement.style.display = 'block';
      this.isDebugPanelVisible = true;
    }
  }
  
  /**
   * Hide debug panel
   */
  public hideDebugPanel(): void {
    if (!this.isDebugPanelVisible || !this.debugPanelElement) {
      return;
    }
    
    this.debugPanelElement.style.display = 'none';
    this.isDebugPanelVisible = false;
  }
  
  /**
   * Log current performance metrics
   */
  private logPerformance(): void {
    // Don't log if not monitoring
    if (!this.isMonitoring) {
      return;
    }
    
    // Update timestamp
    this.metrics.lastUpdateTime = Date.now();
    
    // Add to history
    this.history.push({
      timestamp: this.metrics.lastUpdateTime,
      metrics: { ...this.metrics },
      animationState: this.animationStateProvider ? this.animationStateProvider() : undefined
    });
    
    // Trim history if too large
    if (this.history.length > PERFORMANCE_THRESHOLDS.MAX_LOG_ENTRIES) {
      this.history = this.history.slice(-PERFORMANCE_THRESHOLDS.MAX_LOG_ENTRIES);
    }
    
    // Log if performance is concerning
    if (this.metrics.fps < PERFORMANCE_THRESHOLDS.LOW_FPS) {
      AnimationDebugTools.log('performance', `Performance warning: Low FPS (${this.metrics.fps.toFixed(1)})`);
    }
    
    if (this.metrics.frameTime > PERFORMANCE_THRESHOLDS.HIGH_FRAME_TIME) {
      AnimationDebugTools.log('performance', `Performance warning: High frame time (${this.metrics.frameTime.toFixed(1)}ms)`);
    }
    
    // Log every 10th interval regardless of performance
    if (this.history.length % 10 === 0) {
      AnimationDebugTools.log('performance', 'Animation performance metrics:', {
        fps: this.metrics.fps.toFixed(1),
        avgFps: this.metrics.averageFps.toFixed(1),
        frameTime: this.metrics.frameTime.toFixed(1),
        droppedFrames: this.metrics.droppedFrames
      });
    }
  }
  
  /**
   * Create debug panel DOM element
   */
  private createDebugPanel(): void {
    // Create panel container
    const panel = document.createElement('div');
    panel.id = 'animation-performance-panel';
    panel.style.position = 'fixed';
    panel.style.top = '10px';
    panel.style.right = '10px';
    panel.style.width = '200px';
    panel.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    panel.style.color = 'white';
    panel.style.padding = '10px';
    panel.style.borderRadius = '5px';
    panel.style.fontFamily = 'monospace';
    panel.style.fontSize = '12px';
    panel.style.zIndex = '10000';
    
    // Create content
    panel.innerHTML = `
      <div style="font-weight: bold; margin-bottom: 5px; text-align: center;">Animation Performance</div>
      <div id="animation-fps">FPS: --</div>
      <div id="animation-frame-time">Frame Time: -- ms</div>
      <div id="animation-dropped">Dropped Frames: 0</div>
      <div id="animation-avg-fps">Avg FPS: --</div>
      <div style="margin-top: 5px; height: 30px; background: #444; position: relative;">
        <div id="animation-fps-bar" style="height: 100%; width: 0%; background: linear-gradient(to right, red, yellow, green);"></div>
      </div>
      <div style="display: flex; justify-content: space-between; margin-top: 5px;">
        <button id="animation-reset-btn" style="cursor: pointer; padding: 3px;">Reset</button>
        <button id="animation-close-btn" style="cursor: pointer; padding: 3px;">Close</button>
      </div>
    `;
    
    // Add to document
    document.body.appendChild(panel);
    
    // Add event listeners
    const closeBtn = panel.querySelector('#animation-close-btn');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        this.hideDebugPanel();
      });
    }
    
    const resetBtn = panel.querySelector('#animation-reset-btn');
    if (resetBtn) {
      resetBtn.addEventListener('click', () => {
        this.metrics.totalFrames = 0;
        this.metrics.droppedFrames = 0;
        this.metrics.minFps = Infinity;
        this.metrics.maxFps = 0;
        this.history = [];
      });
    }
    
    // Store reference
    this.debugPanelElement = panel;
  }
  
  /**
   * Update debug panel with current metrics
   */
  private updateDebugPanel(): void {
    if (!this.debugPanelElement) return;
    
    // Update text fields
    const fpsEl = this.debugPanelElement.querySelector('#animation-fps');
    if (fpsEl) {
      fpsEl.textContent = `FPS: ${this.metrics.fps.toFixed(1)}`;
      
      // Color-code FPS
      if (this.metrics.fps < PERFORMANCE_THRESHOLDS.CRITICAL_FPS) {
        (fpsEl as HTMLElement).style.color = 'red';
      } else if (this.metrics.fps < PERFORMANCE_THRESHOLDS.LOW_FPS) {
        (fpsEl as HTMLElement).style.color = 'yellow';
      } else {
        (fpsEl as HTMLElement).style.color = 'lime';
      }
    }
    
    const frameTimeEl = this.debugPanelElement.querySelector('#animation-frame-time');
    if (frameTimeEl) {
      frameTimeEl.textContent = `Frame Time: ${this.metrics.frameTime.toFixed(1)} ms`;
    }
    
    const droppedEl = this.debugPanelElement.querySelector('#animation-dropped');
    if (droppedEl) {
      droppedEl.textContent = `Dropped Frames: ${this.metrics.droppedFrames}`;
    }
    
    const avgFpsEl = this.debugPanelElement.querySelector('#animation-avg-fps');
    if (avgFpsEl) {
      avgFpsEl.textContent = `Avg FPS: ${this.metrics.averageFps.toFixed(1)}`;
    }
    
    // Update progress bar
    const fpsBar = this.debugPanelElement.querySelector('#animation-fps-bar');
    if (fpsBar) {
      const barWidth = Math.min(100, (this.metrics.fps / 60) * 100);
      (fpsBar as HTMLElement).style.width = `${barWidth}%`;
    }
  }
}

// Export singleton instance
export const animationPerformanceMonitor = AnimationPerformanceMonitor.getInstance();

/**
 * Helper function to start monitoring with automatic cleanup
 */
export const monitorAnimationPerformance = (options: {
  animationStateProvider?: () => any;
  lowFpsCallback?: (fps: number) => void;
  showDebugPanel?: boolean;
} = {}): () => void => {
  animationPerformanceMonitor.start(options);
  
  return () => {
    animationPerformanceMonitor.stop();
  };
}; 