/**
 * EventManager.ts
 * 
 * Centralizes event handling for the animation system.
 * Manages subscriptions, callbacks, and event dispatching.
 */

import { AnimationEventType, AnimationEventCallback } from '@/types/AnimationTypes';

export class EventManager {
  private eventSubscriptions: Map<AnimationEventType, Set<AnimationEventCallback>>;
  private progressCallbacks: Set<(progress: number) => void>;
  private completionCallbacks: Set<() => void>;

  constructor() {
    this.eventSubscriptions = new Map();
    this.progressCallbacks = new Set();
    this.completionCallbacks = new Set();
  }

  /**
   * Subscribe to an animation event
   */
  public subscribe(eventType: AnimationEventType, callback: AnimationEventCallback): void {
    if (!this.eventSubscriptions.has(eventType)) {
      this.eventSubscriptions.set(eventType, new Set());
    }
    this.eventSubscriptions.get(eventType)?.add(callback);
  }

  /**
   * Unsubscribe from an animation event
   */
  public unsubscribe(eventType: AnimationEventType, callback: AnimationEventCallback): void {
    this.eventSubscriptions.get(eventType)?.delete(callback);
  }

  /**
   * Dispatch an animation event to all subscribers
   */
  public dispatch(eventType: AnimationEventType, data?: any): void {
    this.eventSubscriptions.get(eventType)?.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('Error in animation event callback:', error);
      }
    });
  }

  /**
   * Add a progress callback
   */
  public onProgress(callback: (progress: number) => void): void {
    this.progressCallbacks.add(callback);
  }

  /**
   * Remove a progress callback
   */
  public removeProgressCallback(callback: (progress: number) => void): void {
    this.progressCallbacks.delete(callback);
  }

  /**
   * Add a completion callback
   */
  public onComplete(callback: () => void): void {
    this.completionCallbacks.add(callback);
  }

  /**
   * Remove a completion callback
   */
  public removeCompletionCallback(callback: () => void): void {
    this.completionCallbacks.delete(callback);
  }

  /**
   * Notify all progress callbacks
   */
  public notifyProgress(progress: number): void {
    this.progressCallbacks.forEach(callback => {
      try {
        callback(progress);
      } catch (error) {
        console.error('Error in progress callback:', error);
      }
    });
  }

  /**
   * Notify all completion callbacks
   */
  public notifyComplete(): void {
    this.completionCallbacks.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('Error in completion callback:', error);
      }
    });
  }

  /**
   * Clear all event subscriptions
   */
  public clearSubscriptions(): void {
    this.eventSubscriptions.clear();
    this.progressCallbacks.clear();
    this.completionCallbacks.clear();
  }
}