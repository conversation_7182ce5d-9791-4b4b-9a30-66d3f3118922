import { useEffect } from 'react';

/**
 * Component that injects CSS animations required for the travel animator
 * including pulse, fadeIn, fadeOut, and bubble-pop animations.
 */
export const AnimationStyles = () => {
  useEffect(() => {
    // Create a style element for animations
    const styleEl = document.createElement('style');
    styleEl.textContent = `
      @keyframes pulse {
        0% {
          transform: translate(-50%, -50%) scale(1);
          opacity: 0.7;
        }
        50% {
          transform: translate(-50%, -50%) scale(1.2);
          opacity: 0.5;
        }
        100% {
          transform: translate(-50%, -50%) scale(1);
          opacity: 0.7;
        }
      }
      
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      
      @keyframes fadeOut {
        from {
          opacity: 1;
          transform: translateY(0);
        }
        to {
          opacity: 0;
          transform: translateY(-10px);
        }
      }
      
      @keyframes bubble-pop {
        0% {
          transform: scale(0.8) translateY(10px);
          opacity: 0;
        }
        50% {
          transform: scale(1.05) translateY(-2px);
          opacity: 1;
        }
        100% {
          transform: scale(1) translateY(0);
          opacity: 1;
        }
      }
    `;
    
    // Add style to document head
    document.head.appendChild(styleEl);
    
    // Clean up on unmount
    return () => {
      if (styleEl.parentNode) {
        styleEl.parentNode.removeChild(styleEl);
      }
    };
  }, []);
  
  return null; // This component doesn't render anything
};

export default AnimationStyles; 