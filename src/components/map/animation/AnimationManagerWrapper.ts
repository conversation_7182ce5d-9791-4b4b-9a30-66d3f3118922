/**
 * AnimationManagerWrapper.ts
 * 
 * Singleton wrapper for animation management that decouples animation logic from UI components.
 * The wrapper serves as a facade pattern implementation that coordinates between UI,
 * animation subsystems, and map components while providing a simple interface.
 */

import mapboxgl from 'mapbox-gl';
import AnimationLogger from '@/utils/animationLogger';
import { Position as GlobalPosition, PositionTuple, toPositionTuple } from '@/types/Position'; // Renamed to GlobalPosition
import { AnimationConfig, AnimationEventCallback, CityDriveByInfo, AnimationEventType } from '@/types/animation';
import { EventData, CityProximityStage } from '@/types/AnimationEventTypes';
import * as turf from '@turf/turf';
import { Destination } from '@/types'; // Removed normalizeDestination as it's not used here

// Import core AnimationManager class
import AnimationManager from './AnimationManager'; // Changed to default import

// Import types directly from their source
import { AnimationStateType, AnimationConfig, Position as AnimationUtilPosition } from './utils/types';
import { AnimationEventType as CoreAnimationEventTypeValue } from '@/types/AnimationEventTypes'; // Renamed import for clarity

// Explicitly export AnimationEventType for direct use by consumers like useAnimationManager
export { CoreAnimationEventTypeValue as AnimationEventType }; // Direct export added

// Define types for the wrapper and for re-export
type CoreRouteData = GlobalPosition[];
type CoreAnimationProgressInfo = { progress: number; position: GlobalPosition; bearing: number };
type CoreAnimationOptions = AnimationConfig;
// CoreAnimationState is AnimationStateType (enum)
// CoreAnimationEventType is CoreAnimationEventTypeValue (enum)

// The wrapper defines its own interface it adheres to.
interface CoreAnimationManagerInterface {
  initialize(map: mapboxgl.Map): void;
  isReady(): boolean;
  startAnimation(route: CoreRouteData, options: Partial<CoreAnimationOptions>): Promise<boolean>;
  pauseAnimation(isPaused?: boolean): boolean;
  resumeAnimation(): void;
  stopAnimation(): void;
  addEventListener(eventType: CoreAnimationEventTypeValue, listener: AnimationEventCallback): () => void;
  removeEventListener(eventType: CoreAnimationEventTypeValue, listener: AnimationEventCallback): void;
  getState(): { animationState: AnimationStateType; progress: number; currentPosition: GlobalPosition | null; currentBearing: number; isPaused: boolean; };
  dispose(): void;
  setContextualData(destinations: Destination[], pois: PointOfInterest[]): void;
  getCurrentPosition(): GlobalPosition | null;
  getCurrentBearing(): number;
  getProgress(): number;
  getIsPaused(): boolean;
  setMap(map: mapboxgl.Map | null): void;
  isCurrentlyAnimating(): boolean;
}

// Import animation subsystems
import ComponentInteractionManager from './ComponentInteractionManager';
import { PointOfInterest } from '@/types/POITypes';

// Re-export the crucial types for consumers of the wrapper
export type {
  // AnimationStateType as AnimationState, // Will be exported as value
  CoreAnimationOptions as AnimationOptions,
  CoreRouteData as RouteData,
  CoreAnimationManagerInterface as AnimationManagerInterface,
  AnimationEventCallback,
  // CoreAnimationEventTypeValue as AnimationEventType, // Will be exported as value
  CoreAnimationProgressInfo
};

// Export enums as values with their desired public names
export {
  AnimationStateType as AnimationState,
  // CoreAnimationEventTypeValue as AnimationEventType // Removed duplicate value export
};

// Mock implementations of these classes that would be properly implemented
class CityDriveByManager {
  constructor(map: mapboxgl.Map) {
    // Implementation would initialize with map instance
  }
  
  checkForCities(position: GlobalPosition): CityDriveByInfo | null {
    // Implementation would check for nearby cities
    return null;
  }
}

class POIDiscoveryManager {
  constructor(map: mapboxgl.Map) {
    // Implementation would initialize with map instance
  }
  
  checkForPOIs(position: GlobalPosition): any[] {
    // Implementation would check for nearby POIs
    return [];
  }
}

/**
 * Animation Manager Wrapper
 * 
 * Centralizes animation control and coordinates between animation subsystems
 */
export class AnimationManagerWrapper implements CoreAnimationManagerInterface {
  private static instance: AnimationManagerWrapper | null = null;
  
  // Dependencies
  private map: mapboxgl.Map | null = null;
  private interactionManager: ComponentInteractionManager;
  private cityDriveByManager: CityDriveByManager | null = null;
  private poiDiscoveryManager: POIDiscoveryManager | null = null;
  private mainAnimationManager: CoreAnimationManagerInterface | null = null;
  
  // Animation state
  private isInitialized: boolean = false;
  private isAnimating: boolean = false;
  private isPaused: boolean = false;
  private animationProgress: number = 0;
  private animationRoute: GlobalPosition[] = []; // Use GlobalPosition
  private eventListeners: Map<string, AnimationEventCallback[]> = new Map(); // Use AnimationEventCallback
  private currentAnimationState: AnimationStateType = AnimationStateType.IDLE; // Use AnimationStateType.IDLE
  
  // Animation frame reference
  private animationFrameId: number | null = null;
  
  /**
   * Private constructor for singleton pattern
   */
  private constructor() {
    this.interactionManager = ComponentInteractionManager.getInstance();
    
    // Initialize event listeners map
    Object.values(AnimationEventType).forEach(event => {
      this.eventListeners.set(event, []);
    });
    
    AnimationLogger.log('info', 'animation', 'AnimationManagerWrapper initialized');
  }
  
  /**
   * Get singleton instance
   */
  public static getInstance(): AnimationManagerWrapper {
    if (!AnimationManagerWrapper.instance) {
      AnimationManagerWrapper.instance = new AnimationManagerWrapper();
    }
    return AnimationManagerWrapper.instance;
  }
  
  /**
   * Initialize the animation manager with required dependencies
   */
  public initialize(map: mapboxgl.Map): void {
    this.map = map;
    
    // Initialize animation subsystems
    this.cityDriveByManager = new CityDriveByManager(map);
    this.poiDiscoveryManager = new POIDiscoveryManager(map);

    // Initialize the main AnimationManager
    try {
      const coreManagerInstance = AnimationManager.getInstance();
      coreManagerInstance.setMap(map);

      this.mainAnimationManager = coreManagerInstance as unknown as CoreAnimationManagerInterface;
      AnimationLogger.log('info', 'animation', 'Main AnimationManager instance obtained/initialized via wrapper.');

      // Setup internal event handling from the core manager
      // This part is crucial and was missing or incorrect before.
      // We need to listen to the core manager's native events and translate them.
      // Assuming core AnimationManager has an event system or requires callbacks.
      // For this example, let's assume it uses callbacks passed during prepareAnimation.
      // The handleCoreAnimationEvent will be triggered by those callbacks.

    } catch (error) {
      AnimationLogger.log('error', 'animation', 'Failed to initialize main AnimationManager via wrapper or subscribe to its events:', error);
    }
    
    this.isInitialized = true;
    AnimationLogger.log('info', 'animation', 'AnimationManagerWrapper fully initialized with dependencies');
  }
  
  /**
   * Check if manager is initialized
   */
  public isReady(): boolean {
    return this.isInitialized && !!this.map;
  }
  
  /**
   * Start route animation
   */
  public async startAnimation(
    route: CoreRouteData,
    options: Partial<CoreAnimationOptions>
  ): Promise<boolean> {
    AnimationLogger.log('info', 'animation', '[ANIMATION WRAPPER] startAnimation called in WRAPPER.');
    if (!this.mainAnimationManager) {
      AnimationLogger.log('error', 'animation', '[ANIMATION WRAPPER] AnimationManager not initialized in WRAPPER.');
      return Promise.resolve(false);
    }
    AnimationLogger.log('info', 'animation', '[ANIMATION WRAPPER] Delegating startAnimation to MainAnimationManager.');

    // map GlobalPosition[] to AnimationUtilPosition[] ([number,number][]) for core manager
    const coreRoutePoints: AnimationUtilPosition[] = route.map(p => toPositionTuple(p));

    const prepareOptions: AnimationConfig = { // AnimationConfig from ./utils/types
        duration: options.duration || 60000,
        // easingFunction is not part of AnimationConfig from ./utils/types
        routePoints: coreRoutePoints, // This is now [number,number][]
        onProgress: (progress, position, bearing) => { // position here is AnimationUtilPosition
            const progressData: CoreAnimationProgressInfo = { progress, position: position as GlobalPosition, bearing }; // Cast AnimationUtilPosition to GlobalPosition for wrapper
            this.handleCoreAnimationEvent(CoreAnimationEventTypeValue.ANIMATION_PROGRESS, progressData);
        },
        onComplete: () => {
            this.handleCoreAnimationEvent(CoreAnimationEventTypeValue.ANIMATION_COMPLETE, {});
        },
        onError: (err) => {
            this.handleCoreAnimationEvent(CoreAnimationEventTypeValue.ANIMATION_ERROR, { error: err });
        },
        ...(options as Partial<AnimationConfig>) // Spread other compatible options
    };
    (this.mainAnimationManager as unknown as AnimationManager).prepareAnimation(prepareOptions);
    (this.mainAnimationManager as unknown as AnimationManager).startAnimation();
    return Promise.resolve(true);
  }
  
  /**
   * Pause animation
   */
  public pauseAnimation(isPaused?: boolean): boolean {
    AnimationLogger.log('info', 'animation', '🔄 [ANIMATION WRAPPER] pauseAnimation called in WRAPPER.');
    if (!this.mainAnimationManager) { // Removed isAnimating check as main manager tracks its state
      console.warn('[AnimationManagerWrapper] Main manager not available, cannot pause.');
      AnimationLogger.log('warn', 'animation', '⚠️ [ANIMATION WRAPPER] Main manager not available, cannot pause.');
      return false;
    }
    AnimationLogger.log('info', 'animation', '[Wrapper] Delegating pauseAnimation to MainAnimationManager.');
    const actualManager = this.mainAnimationManager as unknown as AnimationManager; // Cast to actual type
    actualManager.pauseAnimation(isPaused === undefined ? true : isPaused); // Core manager takes a boolean
    // The core manager doesn't return success. We assume it works or handles errors internally.
    this.isPaused = actualManager.getIsPaused ? actualManager.getIsPaused() : (isPaused === undefined ? true : isPaused);
    return true; // Placeholder
  }
  
  /**
   * Resume animation
   */
  public resumeAnimation(): void {
    AnimationLogger.log('info', 'animation', '▶️ [ANIMATION WRAPPER] resumeAnimation called in WRAPPER.');
    if (!this.mainAnimationManager) { // Removed isAnimating/isPaused check
      console.warn('[AnimationManagerWrapper] Main manager not available, cannot resume.');
      AnimationLogger.log('warn', 'animation', '⚠️ [ANIMATION WRAPPER] Main manager not available, cannot resume.');
      return;
    }

    AnimationLogger.log('info', 'animation', '🔄 [ANIMATION WRAPPER] Attempting to call mainAnimationManager.startAnimation() to resume (or pauseAnimation(false)).');
    // Core AnimationManager's pauseAnimation(false) is effectively resume if it was paused,
    // or it might use startAnimation() if it was in a PAUSED state and startAnimation() handles resume.
    // Given the structure of core AnimationManager, calling startAnimation() again seems more plausible if it handles resuming from PAUSED.
    // Or, if pauseAnimation(false) is the resume mechanism:
    (this.mainAnimationManager as unknown as AnimationManager).pauseAnimation(false);
    this.isPaused = (this.mainAnimationManager as unknown as AnimationManager).getIsPaused ? (this.mainAnimationManager as unknown as AnimationManager).getIsPaused() : false;
  }
  
  /**
   * Stop animation
   */
  public stopAnimation(): void {
    AnimationLogger.log('info', 'animation', '⏹️ [ANIMATION WRAPPER] stopAnimation called in WRAPPER.');
    if (!this.mainAnimationManager) {
      console.warn('[AnimationManagerWrapper] Main manager not available, cannot stop animation properly.');
      AnimationLogger.log('warn', 'animation', '⚠️ [ANIMATION WRAPPER] Main manager not available, cannot stop animation properly.');
      // Still attempt to clean up wrapper state
      this.isAnimating = false;
      this.isPaused = false;
      this.animationProgress = 0;
      if (this.animationFrameId) {
        cancelAnimationFrame(this.animationFrameId);
        this.animationFrameId = null;
      }
      return;
    }
    AnimationLogger.log('info', 'animation', '[Wrapper] Delegating stopAnimation to MainAnimationManager.');
    (this.mainAnimationManager as unknown as AnimationManager).stopAnimation();

    this.isAnimating = false;
    this.isPaused = false;
    this.animationProgress = 0; // Reset wrapper's view of progress

    // The animationFrameId logic below was part of the wrapper's own loop, which is removed.
    // if (this.animationFrameId) {
      // if (this.animationFrameId !== null) {
      //   cancelAnimationFrame(this.animationFrameId);
      //   this.animationFrameId = null;
      // }
    // }
  }
  
  /**
   * Complete animation - THIS SHOULD BE CALLED BY mainAnimationManager's logic, NOT directly by wrapper
   */
  private completeAnimation(): void {
    // This method is likely now redundant if mainAnimationManager handles completion and emits ANIMATION_COMPLETE
    AnimationLogger.log('info', 'animation', '[Wrapper] completeAnimation called - this should be handled by MainAnimationManager events.');
    // if (this.animationFrameId !== null) {
    //   cancelAnimationFrame(this.animationFrameId);
    //   this.animationFrameId = null;
    // }
    // this.isAnimating = false;
    // this.isPaused = false;
    // this.animationProgress = 1;
    // this.interactionManager.emit(AnimationEventType.ANIMATION_COMPLETE, {});
  }

  /**
   * Helper to get lng/lat coordinates from Position regardless of format
   */
  private getCoordinates(position: GlobalPosition): [number, number] {
    if (Array.isArray(position)) {
      return [position[0], position[1]]; // Lng, Lat for tuples
    } else if (typeof position === 'object' && position !== null && 'lng' in position && 'lat' in position) {
      // Use type assertion to tell TypeScript this is a position object with lng/lat
      const posObj = position as { lng: number; lat: number };
      return [posObj.lng, posObj.lat]; // Lng, Lat for objects
    } else if (typeof position === 'object' && position !== null && 'lon' in position && 'lat' in position) { // Added check for lon
      const posObj = position as { lon: number; lat: number };
      return [posObj.lon, posObj.lat]; // lon, Lat for objects
    } else {
      // Fallback or error handling if position is not in a recognized format
      console.error('Invalid position format:', position);
      AnimationLogger.log('error', 'animation', 'Invalid position format received in getCoordinates', {position});
      return [0, 0]; // Default to a safe value
    }
  }
  
  /**
   * Start animation loop
   */
  private startAnimationLoop(): void {
    // Implement animation frame logic here
    // This is a placeholder for actual animation logic
    const animate = () => {
      if (!this.isAnimating) return;
      
      // Skip frame if paused
      if (this.isPaused) {
        this.animationFrameId = requestAnimationFrame(animate);
        return;
      }
      
      // Update progress
      this.animationProgress += 0.001;
      
      // Calculate current position along route (simplified for the example)
      const currentPositionIndex = Math.min(
        Math.floor(this.animationProgress * this.animationRoute.length),
        this.animationRoute.length - 1
      );
      
      if (currentPositionIndex >= 0 && currentPositionIndex < this.animationRoute.length) {
        const currentPosition = this.animationRoute[currentPositionIndex];
        const nextPositionIndex = Math.min(currentPositionIndex + 1, this.animationRoute.length - 1);
        const nextPosition = this.animationRoute[nextPositionIndex];
        
        // Calculate bearing using extracted coordinates
        let bearing = 0;
        if (currentPositionIndex < nextPositionIndex) {
          const [currentLng, currentLat] = this.getCoordinates(currentPosition);
          const [nextLng, nextLat] = this.getCoordinates(nextPosition);
          
          bearing = turf.bearing(
            turf.point([currentLng, currentLat]),
            turf.point([nextLng, nextLat])
          );
        }

        // Emit progress event with AnimationProgressInfo
        const progressData: CoreAnimationProgressInfo = {
          progress: this.animationProgress,
          position: currentPosition,
          bearing: bearing
        };
        this.interactionManager.emit(CoreAnimationEventTypeValue.ANIMATION_PROGRESS, progressData);
        
        // Check for POIs and cities
        this.checkForPOIs(currentPosition);
        this.checkForCities(currentPosition);
      }
      
      // Check if animation is complete
      if (this.animationProgress >= 1) {
        this.completeAnimation();
        return;
      }
      
      // Continue animation
      this.animationFrameId = requestAnimationFrame(animate);
    };
    
    // Start the loop
    this.animationFrameId = requestAnimationFrame(animate);
  }
  
  /**
   * Check for POIs near the current position
   */
  private checkForPOIs(position: GlobalPosition): void {
    if (!this.poiDiscoveryManager) return;
    
    const discoveredPOIs = this.poiDiscoveryManager.checkForPOIs(position);
    if (discoveredPOIs && discoveredPOIs.length > 0) {
      discoveredPOIs.forEach(poi => {
        this.interactionManager.emit(CoreAnimationEventTypeValue.POI_DISCOVERED, { poi });
      });
    }
  }
  
  /**
   * Check for cities near the current position
   */
  private checkForCities(position: GlobalPosition): void {
    if (!this.cityDriveByManager) return;
    
    const cityInfo = this.cityDriveByManager.checkForCities(position);
    if (cityInfo) {
       // Assuming cityInfo is compatible with CityDriveByInfo or needs mapping
      this.interactionManager.emit(CoreAnimationEventTypeValue.CITY_APPROACHED, cityInfo as CityDriveByInfo);
    }
  }
  
  /**
   * Add event listener
   * Ensures that eventType is of type AnimationEventType
   */
  public addEventListener(eventType: CoreAnimationEventTypeValue, listener: AnimationEventCallback): () => void {
    const eventTypeStr = String(eventType);
    AnimationLogger.log('info', 'animation', '[AnimationManagerWrapper] addEventListener', { eventType: eventTypeStr });

    AnimationLogger.log('info', 'animation', '[AnimationManagerWrapper] Using internal listeners. Core manager does not have addEventListener.');
    if (!this.eventListeners.has(eventTypeStr)) {
      this.eventListeners.set(eventTypeStr, []);
    }
    const listenersArray = this.eventListeners.get(eventTypeStr)!;
    listenersArray.push(listener);

    // No interactionManager fallback needed here as this is the primary mechanism now.
    // const unsubscribeInteractionManager = this.interactionManager.addEventListener(eventTypeStr, (data: any) => {
    //   listener(data); 
    // });

    return () => {
      const currentListeners = this.eventListeners.get(eventTypeStr);
      if (currentListeners) {
        const idx = currentListeners.indexOf(listener);
        if (idx !== -1) currentListeners.splice(idx, 1);
      }
      // unsubscribeInteractionManager();
    };
  }

  /**
   * Remove event listener
   */
  public removeEventListener(eventType: CoreAnimationEventTypeValue, listener: AnimationEventCallback): void {
    const eventTypeStr = String(eventType); // Use CoreAnimationEventTypeValue
    AnimationLogger.log('info', 'animation', '[AnimationManagerWrapper] removeEventListener', { eventType: eventTypeStr });

    // Using internal listeners
    AnimationLogger.log('info', 'animation', '[AnimationManagerWrapper] Using internal listeners for removal.');
    const listeners = this.eventListeners.get(eventTypeStr);
    if (listeners) {
      const idx = listeners.indexOf(listener);
      if (idx !== -1) listeners.splice(idx, 1);
    }
  }
  
  /**
   * Get current animation state
   */
  public getState(): { animationState: AnimationStateType; progress: number; currentPosition: GlobalPosition | null; currentBearing: number; isPaused: boolean; } {
    if (this.mainAnimationManager && (this.mainAnimationManager as unknown as AnimationManager).getAnimationState) {
      AnimationLogger.log('debug', 'animation', '[AnimationManagerWrapper] Delegating getState to mainAnimationManager');
      const coreManager = (this.mainAnimationManager as unknown as AnimationManager);
      const coreInternalState = coreManager.getAnimationState();
      const currentPos = coreManager.getCurrentPosition(); // This is AnimationUtilPosition | null
      return {
          animationState: coreInternalState.animationStateType,
          progress: coreInternalState.progress,
          currentPosition: currentPos ? currentPos as GlobalPosition : null, // Cast AnimationUtilPosition to GlobalPosition
          currentBearing: coreInternalState.bearing,
          isPaused: coreInternalState.isPaused,
      };
    }
    AnimationLogger.log('warn', 'animation', '[AnimationManagerWrapper] mainAnimationManager.getState not available. Using potentially stale wrapper state.');
    return {
      animationState: this.currentAnimationState, // Use the wrapper's currentAnimationState
      progress: this.animationProgress,
      currentPosition: this.getCurrentPosition(),
      currentBearing: this.getCurrentBearing(),
      isPaused: this.getIsPaused(),
    };
  }
  
  /**
   * Clean up resources
   */
  public dispose(): void {
    // Stop animation
    this.stopAnimation();
    
    // Clear event listeners
    this.eventListeners.clear();
    
    // Reset dependencies
    this.cityDriveByManager = null;
    this.poiDiscoveryManager = null;
    this.map = null;
    this.isInitialized = false;
    
    AnimationLogger.log('info', 'animation', 'AnimationManagerWrapper disposed');
  }

  /**
   * Sets contextual data (destinations and POIs) for the main AnimationManager.
   */
  public setContextualData(destinations: Destination[], pois: PointOfInterest[]): void {
    if (!this.mainAnimationManager) return;
    this.mainAnimationManager.setContextualData(destinations, pois);
  }

  /**
   * Get current animation state (the enum value)
   */
  public getAnimationStateTypeEnum(): AnimationStateType { // Renamed from getAnimationState
    // Get the full state object from the core manager (via the interface method)
    const coreStateObject = this.mainAnimationManager?.getState(); 
    // Extract the enum value from the 'animationState' property of the object
    return coreStateObject?.animationState || AnimationStateType.IDLE; 
  }

  public getCurrentPosition(): GlobalPosition | null {
    if (this.mainAnimationManager && (this.mainAnimationManager as unknown as AnimationManager).getCurrentPosition) {
        const pos = (this.mainAnimationManager as unknown as AnimationManager).getCurrentPosition(); // AnimationUtilPosition | null
        return pos ? pos as GlobalPosition : null; // Cast
    }
    return null;
  }

  public getCurrentBearing(): number {
    if (this.mainAnimationManager && (this.mainAnimationManager as unknown as AnimationManager).getAnimationState) {
        return (this.mainAnimationManager as unknown as AnimationManager).getAnimationState().bearing;
    }
    return 0;
  }

  public getProgress(): number {
    if (this.mainAnimationManager && (this.mainAnimationManager as unknown as AnimationManager).getAnimationState) {
        return (this.mainAnimationManager as unknown as AnimationManager).getAnimationState().progress;
    }
    return 0;
  }

  public getIsPaused(): boolean {
    if (this.mainAnimationManager && (this.mainAnimationManager as unknown as AnimationManager).getIsPaused) {
        return (this.mainAnimationManager as unknown as AnimationManager).getIsPaused();
    }
    return this.isPaused; // Fallback to wrapper's own isPaused
  }

  public setMap(map: mapboxgl.Map | null): void {
    if (map) { this.map = map; }
    if (this.mainAnimationManager && typeof this.mainAnimationManager.setMap === 'function') {
        this.mainAnimationManager.setMap(map);
    }
  }

  public isCurrentlyAnimating(): boolean {
    if (this.mainAnimationManager && (this.mainAnimationManager as unknown as AnimationManager).getIsAnimating) {
      return (this.mainAnimationManager as unknown as AnimationManager).getIsAnimating();
    }
    AnimationLogger.log('warn', 'animation', '[AnimationManagerWrapper] mainAnimationManager.isCurrentlyAnimating not available. Using wrapper state.');
    // return this.isAnimating && !this.isPaused; // Fallback to wrapper's internal state
    return this.currentAnimationState === AnimationStateType.PLAYING;
  }

  private handleCoreAnimationEvent(eventType: CoreAnimationEventTypeValue, eventData: any): void {
    AnimationLogger.log('debug', 'animation', `[Wrapper] handleCoreAnimationEvent: ${eventType}`, { eventData });
    
    const listeners = this.eventListeners.get(eventType as string);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          let adaptedEventData = eventData;
          if (eventType === CoreAnimationEventTypeValue.ANIMATION_PROGRESS && eventData.position) {
            adaptedEventData = { ...eventData, position: eventData.position as GlobalPosition };
          }
          listener(adaptedEventData);
        } catch (error) {
          AnimationLogger.log('error', 'animation', `[Wrapper] Error calling listener for event ${eventType}`, { error, listener: listener.name });
        }
      });
    }

    switch (eventType) {
      case CoreAnimationEventTypeValue.STATE_CHANGE:
        const newState = (typeof eventData === 'object' && eventData !== null && 'state' in eventData) ? eventData.state : eventData;
        if (newState && Object.values(AnimationStateType).includes(newState as AnimationStateType)){
          this.currentAnimationState = newState as AnimationStateType;
          this.isAnimating = this.currentAnimationState === AnimationStateType.PLAYING;
          this.isPaused = this.currentAnimationState === AnimationStateType.PAUSED;
          AnimationLogger.log('info', 'animation', `[Wrapper] Internal state updated by core event: ${this.currentAnimationState}`);
        }
        break;
      case CoreAnimationEventTypeValue.ANIMATION_PROGRESS:
        if (typeof eventData === 'object' && eventData !== null && 'progress' in eventData) {
          this.animationProgress = (eventData as CoreAnimationProgressInfo).progress;
          if ('position' in eventData) this.animationRoute = [eventData.position];
        }
        break;
      case CoreAnimationEventTypeValue.ANIMATION_COMPLETE:
        this.currentAnimationState = AnimationStateType.IDLE;
        this.isAnimating = false;
        this.isPaused = false;
        this.animationProgress = 1;
        break;
      case CoreAnimationEventTypeValue.ANIMATION_ERROR:
        this.currentAnimationState = AnimationStateType.ERROR;
        this.isAnimating = false;
        this.isPaused = false;
        break;
      default:
        break;
    }
  }
}

export default AnimationManagerWrapper; 