import mapboxgl, { CameraOptions, AnimationOptions, LngLat, Map as MapboxMap } from 'mapbox-gl';
import * as turf from '@turf/turf';
import { PointOfInterest } from '@/types/poi.ts';
import { Destination } from '@/types/POITypes';
import AnimationDebugTools from './AnimationDebugTools';
import { showCountdown, showAnnouncement, showLoadingIndicator } from './AnimationUtils';
import AnimationManager from './AnimationManager.ts';
import { createCameraJitterTest } from './CameraJitterTest';
import EventEmitter from 'eventemitter3';
import * as MapHelpers from '../utils/MapHelpers';
import { determineTerrainType } from '../utils/MapHelpers';
import MapController from '../MapController';
import POIDiscoveryManager from './POIDiscoveryManager.ts';
import { ComponentInteractionManager } from './ComponentInteractionManager';
import { AnimationEventType } from '@/types/AnimationEventTypes.ts';
import { Position, PositionObject, PositionTuple, isPositionObject, isPositionTuple } from '@/types/Position';
import { EnhancedCameraBehavior, LookAheadOptions, CameraState as EnhancedCameraState } from '../utils/EnhancedCameraBehavior';
import { InteractionEventType } from '@/types/AnimationEventTypes';

// Define global debug flag at the top of the file
const DEBUG_ENABLED = true; // Enable debug logs

// Constants for cinematic sequences - MADE DYNAMIC
const DEFAULT_CENTER: [number, number] = [-5.0, 31.794]; // Fallback center
const BROAD_CONTEXT_ZOOM = 6; // Zoom level for region overview
const ROUTE_TRAVEL_ZOOM = 8; // Standard zoom for route travel
const CITY_APPROACH_ZOOM = 14; // Zoom for city exploration
const POI_DETAIL_ZOOM = 16; // Zoom for POI detail

// Camera transition durations
const CONTEXT_ZOOM_DURATION = 2000; // ms
const COUNTDOWN_ZOOM_DURATION = 1500; // ms

// Add camera smoothing constants
const BEARING_SMOOTHING_FACTOR = 0.15; // Higher = more smoothing
const POSITION_SMOOTHING_FACTOR = 0.12; // For position
const ZOOM_SMOOTHING_FACTOR = 0.08; // For zoom
const PITCH_SMOOTHING_FACTOR = 0.1; // For pitch

// City-specific smoothing - higher values mean more aggressive smoothing
const CITY_SMOOTHING_MULTIPLIER = 1.5;
const MARRAKECH_SMOOTHING_MULTIPLIER = 2.0;

// Hysteresis thresholds to prevent tiny oscillations
const HYSTERESIS_THRESHOLD = {
  zoom: 0.05,
  pitch: 0.5,
  bearing: 1.0
};

// City-specific hysteresis thresholds - higher values mean more stability
const CITY_HYSTERESIS_MULTIPLIER = 2.0;
const MARRAKECH_HYSTERESIS_MULTIPLIER = 3.0;

// Camera update timing
const CAMERA_UPDATE_INTERVAL = 100; // ms - decreased from 800ms for smoother motion
const CITY_CAMERA_UPDATE_INTERVAL = 300; // ms - longer interval in cities for more stability
const MARRAKECH_CAMERA_UPDATE_INTERVAL = 500; // ms - even longer interval for Marrakech

// Add debug constants
const EXTREME_SMOOTHING = false; // Enable extreme smoothing for testing
const FIXED_CAMERA_TEST = false; // Temporarily fix camera to test if jittering is input-related
const DISABLE_TERRAIN_TRANSITIONS = false; // Test if terrain transitions are causing jitter
const LOG_ALL_CAMERA_UPDATES = true; // Log every camera update (warning: verbose)
const ALTERNATE_CAMERA_UPDATE_METHOD = false; // Use an alternative update approach
const EXTREME_SMOOTHING_FACTORS = {
  zoom: 0.03,
  pitch: 0.05,
  bearing: 0.05,
  position: 0.03
};

// Add the missing MAJOR_CITIES constant
const MAJOR_CITIES = ['Marrakech', 'Casablanca', 'Fez', 'Rabat', 'Tangier'];

// Helper function to determine if position is in Marrakech city
const isInMarrakech = (position: [number, number]): boolean => {
  // TEMPORARILY DISABLED FOR TESTING
  return false;
  
  // Marrakech city center and approximate radius
  // const marrakechCenter: [number, number] = [-7.9811, 31.6295];
  // const marrakechRadius = 15; // km
  
  // if (!position) return false;
  
  // const distance = turf.distance(
  //   turf.point(position),
  //   turf.point(marrakechCenter),
  //   { units: 'kilometers' }
  // );
  
  // return distance <= marrakechRadius;
};

// Helper function to determine if position is in any major city
const isInMajorCity = (position: [number, number], destinations: Destination[]): boolean => {
  if (!position || !destinations || destinations.length === 0) return false;
  
  const cityRadius = 10; // km
  
  for (const dest of destinations) {
    if (!dest.coordinates) continue;
    
    const distance = turf.distance(
      turf.point(position),
      turf.point(dest.coordinates as [number, number]),
      { units: 'kilometers' }
    );
    
    if (distance <= cityRadius) {
      return true;
    }
  }
  
  return false;
};

interface TerrainZone {
  name: string;
  bounds: [[number, number], [number, number]];
  description: string;
  cameraSettings: {
    pitch: number;
    zoom: number;
    bearing: number;
    speed: number;
  };
}

interface POICluster {
  center: [number, number];
  pois: PointOfInterest[];
  radius: number;
}

interface CinematicSequence {
  step: 'context_zoom' | 'countdown' | 'animation' | 'complete';
  startTime: number;
  duration: number;
  callback?: () => void;
}

// Define a proper type for announcements
interface Announcement {
  message: string;
  duration: number;
}

// Add an interface for camera settings with the announcement property
interface CameraSettings {
  center: [number, number];
  pitch: number;
  zoom: number;
  bearing: number;
  speed: number;
  essential: boolean;
  duration: number;
  announcement?: Announcement | null;
}

// Add a simpler interface for tracking previous camera state
interface PrevCameraState {
  center: [number, number];
  zoom: number;
  pitch: number;
  bearing: number;
}

interface SequenceType {
  type: 'standard' | 'cinematic' | 'quick';
  duration?: number;
  easing?: (t: number) => number;
}

// Add CameraParams interface
interface CameraParams {
  center: Position;
  zoom: number;
  bearing: number;
  pitch: number;
  active: boolean;
}

// Add interface for jitter detection state
interface JitterDetectionState {
  zoom: number;
  bearing: number;
  pitch: number;
  center: mapboxgl.LngLat;
  timestamp: number;
  lastLoggedZoom: number;
  lastLoggedPitch: number;
  lastLoggedBearing: number;
  lastLoggedCenter: mapboxgl.LngLat;
}

// Add interface for jitter thresholds
interface JitterThresholds {
  bearing: number;  // Degrees
  zoom: number;     // Zoom levels
  pitch: number;    // Degrees
  position: number; // Coordinate units
}

// Add interface for velocity tracking
interface VelocityRefs {
  zoom: { current: number };
  pitch: { current: number };
  bearing: { current: number };
  longitude: { current: number };
  latitude: { current: number };
}

// Add interface for terrain transition state
interface TerrainTransitionState {
  currentType: string | null;
  lastChangeTime: number;
  inTransition: boolean;
}

// Update CameraState interface to be more comprehensive
interface CameraState {
  active: boolean;
  center: Position | null;
  zoom: number;
  pitch: number;
  bearing: number;
  timestamp?: number;
  velocityRefs?: VelocityRefs;
  jitterDetection?: JitterDetectionState;
}

// Add constants
const CAMERA_SMOOTHING_FACTOR = 0.12; // Adjust based on desired smoothness

/**
 * Calculate the angular distance between two bearing values
 * considering the circular nature of bearings (0-360)
 */
function getAngularDistance(a: number, b: number): number {
  const diff = Math.abs(((a - b + 180) % 360) - 180);
  return diff > 180 ? 360 - diff : diff;
}

/**
 * Apply a moving average filter to a time series of values
 * This provides extremely stable smoothing at the cost of some lag
 */
const movingAverageFilter = (
  buffer: number[],
  newValue: number,
  maxSize: number = 15,
  isCircular: boolean = false
): number => {
  // Add new value to buffer
  buffer.push(newValue);
  
  // Keep buffer at maxSize
  if (buffer.length > maxSize) {
    buffer.shift();
  }
  
  // For circular values like bearing, use special averaging
  if (isCircular) {
    let sumSin = 0;
    let sumCos = 0;
    
    buffer.forEach(value => {
      const radians = value * (Math.PI / 180);
      sumSin += Math.sin(radians);
      sumCos += Math.cos(radians);
    });
    
    const avgRadian = Math.atan2(sumSin, sumCos);
    return ((avgRadian * 180 / Math.PI) + 360) % 360;
  }
  
  // For regular values, use standard average
  return buffer.reduce((sum, val) => sum + val, 0) / buffer.length;
};

/**
 * Calculate exponential smoothing for camera movement
 * Provides smoother deceleration as values approach the target
 */
export const exponentialSmoothing = (
  current: number, 
  target: number, 
  factor: number
): number => {
  const delta = target - current;
  const smoothedDelta = delta * (1 - Math.exp(-factor));
  return current + smoothedDelta;
};

// Update the event interface to handle optional map parameter
interface CinematicEvents {
  cameraStateChange: (state: CameraParams) => void;
  transitionComplete: (params: CameraParams) => void;
  cameraParamsUpdate: (params: CameraParams) => void;
  jitterDetected: (state: JitterDetectionState) => void;
}

// Custom options type that extends mapbox types
interface ExtendedCameraOptions extends CameraOptions {
  easeId?: string;
  noMoveStart?: boolean;
  duration?: number;
  essential?: boolean;
  onComplete?: () => void;
}

interface CameraJitterTestParams {
  jitterAngle: number;
  jitterFrequency: number;
  testDuration: number;
}

// Add missing interface for debug options
interface CinematicControllerDebugOptions {
  fixedCamera: boolean | { active: boolean; center: mapboxgl.LngLat | null; zoom: number; pitch: number; bearing: number; };
  extremeSmoothing: boolean;
  disableTerrainTransitions: boolean;
  alternateUpdateMethod: boolean;
  forceBearingNorth: boolean;
  lastSuccessfulUpdate: number;
  skipFrameCount: number;
  maxSkipFrames: number;
}

// Add default debug overrides
const DEFAULT_DEBUG_OVERRIDES: CinematicControllerDebugOptions = {
  fixedCamera: false,
  extremeSmoothing: false,
  disableTerrainTransitions: false,
  alternateUpdateMethod: false,
  forceBearingNorth: false,
  lastSuccessfulUpdate: 0,
  skipFrameCount: 0,
  maxSkipFrames: 3
};

/**
 * Manages cinematic camera transitions for different animation states
 * like POI discovery, city approach, and journey completion.
 * Implements the singleton pattern.
 */
class CinematicController {
  private static instance: CinematicController | null = null;
  private mapInstance: mapboxgl.Map | null = null;
  private animationManager: AnimationManager | null = null;
  private enhancedCameraBehavior: EnhancedCameraBehavior;
  private isTransitioning = false;
  private lastTransitionTime = 0;
  private transitionCooldown = 500; // Minimum time between transitions (ms)
  private interactionManager: ComponentInteractionManager;
  private _lastPosition: [number, number] | null = null;
  private _lastBearing: number | null = null;
  private _lastProgress: number | null = null;

  // Dynamic region support
  private regionCenter: [number, number] = DEFAULT_CENTER;
  private regionBounds: [[number, number], [number, number]] | null = null;
  private _lastCity: string | null = null;
  private _lastPOI: string | null = null;
  // Add fields for POIs and destinations
  private pois: PointOfInterest[] = [];
  private destinations: Destination[] = [];
  
  // Standard zoom levels per map-camera-rules
  private readonly ZOOM_LEVELS = {
    ROUTE_TRAVEL: 8,
    CITY_EXPLORATION: 14,
    POI_DETAIL: 16,
    COUNTRY_OVERVIEW: 6,
  };
  
  // Standard camera transition durations
  private readonly TRANSITION_DURATIONS = {
    SHORT: 800,
    MEDIUM: 1500,
    LONG: 2000,
    POI_APPROACH: 1800,
  };
  
  private constructor() {
    this.interactionManager = ComponentInteractionManager.getInstance();
    this.mapInstance = null; // Initialize mapInstance
    this.enhancedCameraBehavior = EnhancedCameraBehavior.getInstance(); // Initialize
    
    // Listen for camera ready events
    document.addEventListener('mapbox-camera-ready', this.handleMapReady);
    
    // Setup cleanup on page unload
    window.addEventListener('beforeunload', () => {
      document.removeEventListener('mapbox-camera-ready', this.handleMapReady);
    });
    
    AnimationDebugTools.log('info', "CinematicController initialized", { isSingleton: true });
  }
  
  private handleMapReady = (event: any) => {
    if (event.detail && event.detail.map && event.detail.mapId === (this.mapInstance as any)?._container?.id) {
      AnimationDebugTools.log('info', "CinematicController: mapbox-camera-ready event received.");
      // Potentially initialize EnhancedCameraBehavior here if it needs the map instance
      // and wasn't initialized in constructor, or re-initialize if map changes.
      // this.enhancedCameraBehavior.initialize(this.mapInstance);
    }
  };
  
  /**
   * Get singleton instance
   */
  public static getInstance(): CinematicController {
    if (!CinematicController.instance) {
      CinematicController.instance = new CinematicController();
    }
    return CinematicController.instance;
  }
  
  /**
   * Set the animation manager reference
   */
  public setAnimationManager(animationManager: AnimationManager): void {
    this.animationManager = animationManager;
    AnimationDebugTools.log('info', "CinematicController: AnimationManager instance set.", { animationManagerExists: !!this.animationManager });
  }
  
  /**
   * Set the map instance
   */
  public setMap(map: mapboxgl.Map): void {
    this.mapInstance = map;
    this.enhancedCameraBehavior.initialize(this.mapInstance); // Initialize EnhancedCameraBehavior with the map
    AnimationDebugTools.log('info', "CinematicController: Map instance set.", { mapExists: !!this.mapInstance });
    // If there's specific map-related setup needed for cinematics, do it here
  }

  /**
   * Set the region center and bounds for dynamic cinematics
   */
  public setRegionContext(
    center: [number, number],
    bounds?: [[number, number], [number, number]]
  ): void {
    this.regionCenter = center;
    this.regionBounds = bounds || null;
    AnimationDebugTools.log('info', 'CinematicController: Region context updated', { center, bounds });
  }
  
  /**
   * Trigger cinematic sequence for POI discovery
   */
  public triggerPOIDiscoverySequence(poiData: any): boolean {
    if (!this.mapInstance || !this.animationManager) {
      AnimationDebugTools.log('error', "Map or AnimationManager not initialized in CinematicController for POI discovery.");
      return false;
    }
    if (this.isTransitioning || !this.canStartTransition()) {
      AnimationDebugTools.log('warn', "CinematicController is already transitioning or in cooldown. POI Discovery skipped.");
      return false;
    }
    
    const poi = poiData as PointOfInterest;
    const currentVehiclePosition = this.animationManager.getCurrentPosition();

    if (!currentVehiclePosition) {
        AnimationDebugTools.log('error', "Cannot trigger POI discovery: current vehicle position unknown.");
        return false;
    }
    
      this.isTransitioning = true;
    this.lastTransitionTime = performance.now();
    AnimationDebugTools.log('info', `Starting POI Discovery Sequence for: ${poi.name}`);

    // Example using EnhancedCameraBehavior for a part of the sequence
    // This is a placeholder; specific methods from EnhancedCameraBehavior would be used.
    // For instance, if EnhancedCameraBehavior has a focusOnPOI method:
    /*
    this.enhancedCameraBehavior.focusOnPOI(poi.coordinates, {
        zoom: POI_DETAIL_ZOOM, // Use constants if defined in EnhancedCameraBehavior or pass them
        pitch: DEFAULT_CAMERA_SETTINGS.pitch, 
        bearing: this.mapInstance.getBearing(), // Or calculate optimal bearing
        duration: 2000 
    }).then(() => {
        // ... rest of POI discovery logic (showing panel, etc.)
        showAnnouncement(this.mapInstance!, `Discovering ${poi.name}`, 3000);
        this.interactionManager.emit(AnimationEventType.POI_DISCOVERED, poi);

        // Example: Hold camera for a bit, then allow resume
        setTimeout(() => {
            this.isTransitioning = false;
            // Optionally, provide a way for user to resume or auto-resume
            // this.animationManager.pauseAnimation(false); // If animation was paused
        }, 5000); // Hold for 5 seconds
    }).catch(error => {
        AnimationDebugTools.log('error', "Error during POI focus sequence:", error);
        this.isTransitioning = false;
    });
    */

    // Fallback to simpler map.easeTo if EnhancedCameraBehavior doesn't have a direct method yet,
    // or if CinematicController needs to manage more complex multi-step sequences.
      this.mapInstance.easeTo({
      center: poi.coordinates as [number, number],
      zoom: 16, // POI_DETAIL_ZOOM
      pitch: 50,
      bearing: this.mapInstance.getBearing(), // Or calculate optimal bearing
      duration: 2000,
      essential: true,
      easing: (t: number) => t * (2 - t) // easeOutQuad
    });

    setTimeout(() => {
      showAnnouncement(this.mapInstance!, `Discovering ${poi.name}`, 3000);
      this.interactionManager.emit(AnimationEventType.POI_DISCOVERED, poi);
      // After showing details, allow animation to be resumed or POI to be explored.
      // For now, just mark transition as ended. Actual resume logic might be elsewhere.
          this.isTransitioning = false;
       // this.animationManager.pauseAnimation(true); // Pause animation for discovery
    }, 2100); // After camera move
      
      return true;
  }
  
  /**
   * Resume journey after POI discovery (called by UI or another component)
   */
  public resumeJourneyAfterPOI(): void {
    if (!this.mapInstance || !this.animationManager) {
      AnimationDebugTools.log('error', "Map or AnimationManager not initialized for resuming journey.");
      return;
    }
    AnimationDebugTools.log('info', "Resuming journey after POI discovery.");
    this.isTransitioning = false; // Ensure transition lock is released
    
    // Example: Use EnhancedCameraBehavior to return to route travel view
    const currentVehiclePosition = this.animationManager.getCurrentPosition();
    const currentBearing = this.animationManager.getAnimationState().bearing;

    if (currentVehiclePosition) {
        this.enhancedCameraBehavior.followVehicle(currentVehiclePosition, currentBearing, {
            // Use options to hint at a return to standard travel
            forceUpdate: true, // Force an update to potentially different camera settings
        });
    } else {
        // Fallback if vehicle position isn't available
      this.mapInstance.easeTo({
            zoom: 8, // ROUTE_TRAVEL_ZOOM
        pitch: 45,
        bearing: 0, // North orientation for route travel
            duration: 1500,
        essential: true,
        });
    }
    this.animationManager.pauseAnimation(false); // Resume animation
  }
  
  /**
   * Check if a transition can be started
   */
  private canStartTransition(): boolean {
    const now = performance.now();
    if (now - this.lastTransitionTime < this.transitionCooldown) {
      AnimationDebugTools.log('warn', "Transition cooldown active. New transition blocked.");
      return false;
    }
    return true;
  }
  
  /**
   * Cleanup resources
   */
  public dispose(): void {
    AnimationDebugTools.log('info', "CinematicController: Disposing...");
    if (this.mapInstance) {
        this.mapInstance.off('load', this.handleMapReady); // From backup's destroy
    }
    this.mapInstance = null;
    this.animationManager = null;
    // Call dispose on EnhancedCameraBehavior if it has one
    // if (this.enhancedCameraBehavior && typeof this.enhancedCameraBehavior.dispose === 'function') {
    //   this.enhancedCameraBehavior.dispose();
    // }
    CinematicController.instance = null; // From backup's destroy
  }

  /**
   * Start a cinematic sequence with a callback when it completes
   * This is a simple entry point used by AnimationIntegration
   */
  public startCinematicSequence(callback: () => void): void {
    if (!this.mapInstance) {
      AnimationDebugTools.log('error', "Map not initialized. Cannot start cinematic sequence.");
      callback();
      return;
    }
     if (this.isTransitioning || !this.canStartTransition()) {
      AnimationDebugTools.log('warn', "CinematicController is already transitioning or in cooldown. Cinematic Sequence start skipped.");
      callback(); // still call callback to not block flow
      return;
    }

    this.isTransitioning = true;
    this.lastTransitionTime = performance.now();
    AnimationDebugTools.log('info', "Starting general cinematic sequence (e.g., journey intro).");

    // Use dynamic region center instead of hardcoded Morocco coordinates
    // Enhanced cinematic intro sequence with region-aware positioning
    this.mapInstance.flyTo({
      center: this.regionCenter, // Dynamic region center
      zoom: BROAD_CONTEXT_ZOOM,
      pitch: 30,
      bearing: 0,
      duration: CONTEXT_ZOOM_DURATION,
      essential: true,
    });

    setTimeout(() => {
      this.isTransitioning = false;
      if (callback) callback();
    }, 2100); // After camera move
  }

  public async playJourneyIntroSequence(startPosition: any, options?: any): Promise<void> {
    if (!this.mapInstance) {
        AnimationDebugTools.log('error', "Map not set for playJourneyIntroSequence");
        return Promise.reject("Map not set");
    }
    AnimationDebugTools.log('info', "Playing Journey Intro Sequence", { startPosition, options });
    
    // Option 1: If EnhancedCameraBehavior has a specific method for this
    // return this.enhancedCameraBehavior.playJourneyIntro(startPosition, options);

    // Option 2: Use EnhancedCameraBehavior's generic methods like dramaticZoom
    // This is a simplified example of how it might be used.
    // The actual parameters for dramaticZoom would need to match its definition.
    try {
      // Phase 1: Dramatic zoom out to show full region context
      await this.enhancedCameraBehavior.dramaticZoom(
        this.regionCenter,
        {
          initialZoom: this.mapInstance.getZoom(),
          finalZoom: BROAD_CONTEXT_ZOOM - 1, // Even wider for dramatic effect
          initialDuration: 1500,
          finalDuration: 1000,
        }
      );

      // Phase 2: Smooth transition to journey start position
      await this.enhancedCameraBehavior.dramaticZoom(
        startPosition as [number, number],
        {
          initialZoom: BROAD_CONTEXT_ZOOM - 1,
          finalZoom: ROUTE_TRAVEL_ZOOM + 2, // Closer zoom for journey start
          initialDuration: 2000,
          finalDuration: 1500,
        }
      );

      // Phase 3: Enhanced countdown with journey announcement
      showAnnouncement(this.mapInstance, "🚗 Get ready for an amazing journey!", 3000);
      showCountdown(this.mapInstance, () => {
        AnimationDebugTools.log('info', "Enhanced Journey Intro Complete - Starting Animation");
        showAnnouncement(this.mapInstance, "✨ Discover incredible places along the way!", 4000);
      });

    } catch (error) {
      AnimationDebugTools.log('error', "Error in enhanced journey intro sequence", error);
      // Fallback to basic countdown
      showCountdown(this.mapInstance, () => {
        AnimationDebugTools.log('info', "Journey Intro Countdown Complete");
      });
    }
  }

  public async playCityApproachSequence(city: any, currentPosition: any, options?: any): Promise<void> {
    if (!this.mapInstance) {
        AnimationDebugTools.log('error', "Map not set for playCityApproachSequence");
        return Promise.reject("Map not set");
    }
    AnimationDebugTools.log('info', "Playing City Approach Sequence", { city, currentPosition, options });

    const cityCoords = city.coordinates as [number, number]; // Assuming city has coordinates
    
    // Example using dramaticZoom from EnhancedCameraBehavior
    await this.enhancedCameraBehavior.dramaticZoom(
        cityCoords,
        {
            initialZoom: this.mapInstance.getZoom(), // Current zoom
            finalZoom: 14, // CITY_APPROACH_ZOOM
            initialDuration: 1000, // Duration to approach city overview
            finalDuration: 1500,   // Duration to settle into city view
        }
    );
    showAnnouncement(this.mapInstance, `Approaching ${city.name}`, 3000);
  }

  public async playPOIDiscoverySequence(poi: any, currentPosition: any, options?: any): Promise<void> {
    if (!this.mapInstance) {
        AnimationDebugTools.log('error', "Map not set for playPOIDiscoverySequence");
        return Promise.reject("Map not set");
    }
    AnimationDebugTools.log('info', "Playing POI Discovery Sequence", { poi, currentPosition, options });
    
    const poiCoords = poi.coordinates as [number, number];

    // Use dramaticZoom or a more specific POI focus method from EnhancedCameraBehavior
    await this.enhancedCameraBehavior.dramaticZoom(
        poiCoords,
        {
            initialZoom: this.mapInstance.getZoom(),
            finalZoom: 16, // POI_DETAIL_ZOOM
            initialDuration: 1200,
            finalDuration: 1800,
        }
    );
    showAnnouncement(this.mapInstance, `Discovering ${poi.name}`, 3000);
    this.interactionManager.emit(AnimationEventType.POI_DISCOVERED, poi);
  }

  public async playJourneyCompletionSequence(finalPosition: any, options?: any): Promise<void> {
     if (!this.mapInstance) {
        AnimationDebugTools.log('error', "Map not set for playJourneyCompletionSequence");
        return Promise.reject("Map not set");
    }
    AnimationDebugTools.log('info', "Playing Enhanced Journey Completion Sequence", { finalPosition, options });

    try {
      // Phase 1: Zoom to final destination with celebration
      await this.enhancedCameraBehavior.dramaticZoom(
        finalPosition as [number, number],
        {
          initialZoom: this.mapInstance.getZoom(),
          finalZoom: CITY_APPROACH_ZOOM, // Close view of final destination
          initialDuration: 2000,
          finalDuration: 1500,
        }
      );

      // Phase 2: Pull back for overview of completed journey
      await this.enhancedCameraBehavior.dramaticZoom(
        this.regionCenter,
        {
          initialZoom: CITY_APPROACH_ZOOM,
          finalZoom: BROAD_CONTEXT_ZOOM, // Show full region overview
          initialDuration: 2500,
          finalDuration: 2000,
        }
      );

      // Phase 3: Celebration announcements
      showAnnouncement(this.mapInstance, "🎉 Journey Complete! What an amazing adventure!", 5000);

      // Optional: Add journey statistics or summary
      setTimeout(() => {
        showAnnouncement(this.mapInstance, "✨ Thank you for exploring with us!", 3000);
      }, 3000);

    } catch (error) {
      AnimationDebugTools.log('error', "Error in journey completion sequence", error);
      // Fallback to simple completion
      showAnnouncement(this.mapInstance, "Journey Complete!", 5000);
    }
  }

  public processPositionUpdate(
    position: [number, number],
    bearing: number,
    progress: number,
    leftPanelWidth?: number,
    rightPanelWidth?: number 
  ): void {
    if (!this.mapInstance || !position || typeof bearing === 'undefined' || typeof progress === 'undefined') {
      AnimationDebugTools.log('warn', 'CinematicController: processPositionUpdate called with invalid params', { position, bearing, progress });
      return;
    }

    this._lastPosition = position;
    this._lastBearing = bearing;
    this._lastProgress = progress;

    const currentTerrain = determineTerrainType(position); // Use a unique name
    const cameraContext = {
      terrain: currentTerrain,
      leftPanelWidth: leftPanelWidth || 0,
      rightPanelWidth: rightPanelWidth || 0,
    };

    this.enhancedCameraBehavior.followVehicle(position, bearing, cameraContext);

    // Original logic for POI and City detection for triggering *cinematic sequences* can remain,
    // but the direct camera following is now handled above with panel awareness.
    const nearbyPOIs = POIDiscoveryManager.getInstance().findNearbyPOIs(position, 1); // Use findNearbyPOIs, 1km radius
    if (nearbyPOIs.length > 0 && nearbyPOIs[0].name !== this._lastPOI) {
      this._lastPOI = nearbyPOIs[0].name;
      // Use InteractionEventType.POI_DISCOVERED for general POI events, or a more specific one if available
      this.interactionManager.emit(InteractionEventType.POI_DISCOVERED, { poi: nearbyPOIs[0], reason: 'approaching' }); 
      AnimationDebugTools.log('info', `POI Approaching/Discovered: ${this._lastPOI}`);
    }

    const currentCity = this.destinations.find(dest => 
      dest.coordinates && 
      turf.distance(turf.point(position), turf.point(dest.coordinates as [number, number])) < 2 // 2km threshold
    );

    if (currentCity && currentCity.name !== this._lastCity) {
      this._lastCity = currentCity.name;
      this.interactionManager.emit(InteractionEventType.CITY_APPROACHED, { city: currentCity }); 
      AnimationDebugTools.log('info', `City Approaching: ${this._lastCity}`);
    }
  }

  // Add setters for POIs and destinations
  public setPOIs(pois: PointOfInterest[]): void {
    this.pois = pois;
  }
  public setDestinations(destinations: Destination[]): void {
    this.destinations = destinations;
  }
}

export default CinematicController; 