/**
 * RouteDataLoader.ts
 * 
 * Handles loading route data from Mapbox, with polling and fallback mechanisms
 * when route data isn't immediately available.
 */
import mapboxgl from 'mapbox-gl';
import { Map } from 'mapbox-gl';
import { Position, PositionTuple, Destination, toPositionTuple, toPositionObject } from '@/types';
// import { RouteData } from '@/types/AnimationTypes'; // Commenting out problematic import
import { PointOfInterest } from '@/types/POITypes';


// Define RouteData locally as it's not exported from AnimationTypes
export type RouteData = [number, number][];

// Alias toPositionTuple for clarity if it's used as toPositionArray
const toPositionArray = toPositionTuple;

export interface RouteLoaderOptions {
  map: mapboxgl.Map;
  destinations?: Destination[];
  waypoints?: { coordinates: PositionTuple; name?: string; }[];
  profile?: string;
  retryAttempts?: number;
  retryDelay?: number;
  timeout?: number;
  accessToken?: string;
}

const DEFAULT_OPTIONS: Omit<RouteLoaderOptions, 'map' | 'accessToken'> = {
  retryAttempts: 10,
  retryDelay: 300,
  timeout: 15000
};

let routeDataPollingPromise: Promise<{ route: [number, number][] | null }> | null = null;
let routeDataPollingStart: number = 0;

/**
 * Tries to get route data from the map's source.
 * Returns an array of coordinates if available, or null if not.
 */
export const tryGetRouteData = (map: mapboxgl.Map): Array<[number, number]> | null => {
  if (!map) {
    console.warn('Map is not available for route data access');
    return null;
  }

  try {
    // Get route source that was created by RouteLayer
    let routeSource;
    try {
      if (!map.getSource('morocco-route')) {
        console.log('Route source not available yet, will retry');
        return null;
      }
      routeSource = map.getSource('morocco-route') as mapboxgl.GeoJSONSource;
    } catch (sourceError) {
      console.warn('Error accessing route source:', sourceError);
      return null;
    }

    try {
      const data = (routeSource as any)._data;
      if (!data || !data.geometry) {
        console.log('Route source exists but has no data, will retry');
        return null;
      }

      if (data.geometry.type !== 'LineString') {
        // Try to handle if it's a Feature Collection or another format
        if (data.type === 'FeatureCollection' && data.features && data.features.length > 0) {
          // Try to extract from the first feature
          const feature = data.features.find((f: any) => f.geometry && f.geometry.type === 'LineString');
          if (feature && feature.geometry && feature.geometry.coordinates) {
            const coordinates = feature.geometry.coordinates;
            if (Array.isArray(coordinates) && coordinates.length > 0) {
              console.log(`Found ${coordinates.length} route points in FeatureCollection`);
              
              // Convert to [number, number][] and validate
              const typedCoordinates: [number, number][] = [];
              for (const coord of coordinates) {
                if (Array.isArray(coord) && coord.length >= 2) {
                  typedCoordinates.push([coord[0], coord[1]]);
                }
              }
              
              if (typedCoordinates.length > 0) {
                return typedCoordinates;
              }
            }
          }
        }
        
        console.log(`Invalid route data structure (type: ${data.geometry.type}), will retry`);
        return null;
      }

      const coordinates = data.geometry.coordinates;
      if (!Array.isArray(coordinates)) {
        console.log('Route coordinates are not an array, will retry');
        return null;
      }

      if (coordinates.length === 0) {
        console.log('Route coordinates array is empty, will retry');
        return null;
      }

      // Convert to [number, number][] and validate
      const typedCoordinates: [number, number][] = [];
      for (const coord of coordinates) {
        if (Array.isArray(coord) && coord.length >= 2) {
          typedCoordinates.push([coord[0], coord[1]]);
        } else {
          console.log('Route contains invalid coordinates, will retry');
          return null;
        }
      }

      console.log(`Found ${coordinates.length} valid route points`);
      return typedCoordinates;
    } catch (dataError) {
      console.warn('Error accessing route data:', dataError);
      return null;
    }
  } catch (error) {
    console.error('Error accessing route coordinates:', error);
    return null;
  }
};

/**
 * Creates a direct route between destinations as a fallback
 */
export const createDirectRouteFallback = (
  destinations: Destination[]
): [number, number][] => {
  console.log('Using fallback route - direct lines between destinations');
  
  const fallbackRoute: [number, number][] = [];
  
  // Helper function to extract valid coordinates from a destination
  const getValidCoordinates = (d: Destination): [number, number] | null => {
    // Handle Position object-style coordinates {lng, lat} or PositionTuple
    if (d.position) {
      const posObj = toPositionObject(d.position);
      if (posObj && typeof posObj.lng === 'number' && typeof posObj.lat === 'number' &&
          !isNaN(posObj.lng) && !isNaN(posObj.lat)) {
        return [posObj.lng, posObj.lat];
      }
    }

    // Handle array-style coordinates [lng, lat]
    if (d.coordinates && Array.isArray(d.coordinates) && d.coordinates.length === 2 &&
        typeof d.coordinates[0] === 'number' && typeof d.coordinates[1] === 'number' &&
        !isNaN(d.coordinates[0]) && !isNaN(d.coordinates[1])) {
      return [d.coordinates[0], d.coordinates[1]];
    }
    
    console.warn('[RouteDataLoader] Destination missing valid position or coordinates:', d);
    return null;
  };
  
  // Get valid coordinates for each destination
  const validCoordinates = destinations.map(getValidCoordinates).filter(Boolean) as [number, number][];
  
  if (validCoordinates.length < 2) {
    console.error('No valid destination coordinates for fallback route', {
      totalDestinations: destinations.length,
      validDestinations: validCoordinates.length,
      destinationCoordinates: destinations.map(d => getValidCoordinates(d))
    });
    return [];
  }
  
  // Use the valid coordinates for the fallback route
  return validCoordinates;
};

// Helper to parse Mapbox Directions API response
const parseDirectionsAPIResponse = (responseData: any): [number, number][] | null => {
  if (responseData && responseData.routes && responseData.routes.length > 0) {
    const route = responseData.routes[0];
    if (route.geometry && route.geometry.coordinates) {
      // Ensure coordinates are in [lng, lat] format
      const coordinates = route.geometry.coordinates.map((coord: any) => {
        if (Array.isArray(coord) && coord.length === 2 && typeof coord[0] === 'number' && typeof coord[1] === 'number') {
          return [coord[0], coord[1]] as [number, number];
        }
        return null;
      }).filter((c: [number, number] | null) => c !== null) as [number, number][];
      
      if (coordinates.length > 0) {
        console.log(`[RouteDataLoader] Fetched detailed route with ${coordinates.length} points from API.`);
        return coordinates;
      }
    }
  }
  console.error('[RouteDataLoader] Invalid or empty route data from Directions API:', responseData);
  return null;
};

/**
 * Fetches a detailed route from Mapbox Directions API.
 */
export const fetchDetailedRouteFromAPI = async (
  waypoints: [number, number][],
  accessToken: string,
  profile: string = 'mapbox/driving-traffic' // Default profile
): Promise<[number, number][] | null> => {
  if (!accessToken || accessToken === 'YOUR_MAPBOX_ACCESS_TOKEN' || accessToken.trim() === '') {
    console.error('[RouteDataLoader] Mapbox Access Token is missing or invalid for Directions API call.');
    return null;
  }
  if (!waypoints || waypoints.length < 2) {
    console.error('[RouteDataLoader] At least two waypoints are required to fetch a route.');
    return null;
  }

  const coordinatesString = waypoints.map(wp => wp.join(',')).join(';');
  const apiUrl = `https://api.mapbox.com/directions/v5/${profile}/${coordinatesString}?geometries=geojson&overview=full&steps=true&access_token=${accessToken}`;

  console.log(`[RouteDataLoader] Fetching detailed route from Mapbox Directions API for ${waypoints.length} waypoints using profile ${profile}...`);

  try {
    const response = await fetch(apiUrl);
    if (!response.ok) {
      const errorData = await response.text();
      console.error(`[RouteDataLoader] Mapbox Directions API request failed: ${response.status} ${response.statusText}`, errorData);
      return null;
    }
    const responseData = await response.json();
    return parseDirectionsAPIResponse(responseData);
  } catch (error) {
    console.error('[RouteDataLoader] Error fetching or parsing route from Mapbox Directions API:', error);
    return null;
  }
};

/**
 * Load route data with polling and fallbacks
 * Returns a Promise that resolves with the route data
 */
export const loadRouteData = (
  options: RouteLoaderOptions
): Promise<{ route: [number, number][] | null }> => {
  // Combine default options with provided options
  // Map and accessToken are mandatory if certain paths are taken, ensure they are handled.
  const opts = { ...DEFAULT_OPTIONS, ...options };
  const { map, destinations, waypoints, profile, accessToken } = opts;
  
  // If we already have a polling promise in progress, return it
  // This needs to be adapted if the promise was for a different set of options
  // For simplicity, we'll assume new call = new promise for now.
  // if (routeDataPollingPromise) { ... }

  routeDataPollingStart = Date.now();
  
  routeDataPollingPromise = new Promise((resolve, reject) => {
    const pollForRoute = async () => {
      if (Date.now() - routeDataPollingStart > (opts.timeout || DEFAULT_OPTIONS.timeout!)) {
        console.warn('[RouteDataLoader] Route polling timeout reached.');
        
        let waypointsForApi: [number, number][] | undefined;
        if (waypoints && waypoints.length > 0) {
          waypointsForApi = waypoints.map(wp => wp.coordinates);
        } else if (destinations && destinations.length > 0) {
          waypointsForApi = destinations.map(d => toPositionTuple(d.coordinates || d.position!)).filter(Boolean) as [number, number][];
        }

        if (waypointsForApi && waypointsForApi.length >= 2 && accessToken) {
          console.log('[RouteDataLoader] Timeout: Attempting fetchDetailedRouteFromAPI as fallback.');
          const apiRoute = await fetchDetailedRouteFromAPI(waypointsForApi, accessToken, profile);
          if (apiRoute) {
            resolve({ route: apiRoute });
            routeDataPollingPromise = null;
            return;
          }
        }
        const fallbackRoute = destinations ? createDirectRouteFallback(destinations) : [];
        resolve({ route: fallbackRoute.length > 0 ? fallbackRoute : null });
        routeDataPollingPromise = null;
        return;
      }
      
      const currentRouteData = tryGetRouteData(map);
      if (currentRouteData) {
        console.log('[RouteDataLoader] Route data obtained successfully via tryGetRouteData.');
        resolve({ route: currentRouteData });
        routeDataPollingPromise = null;
      } else {
        let waypointsForEarlyApiAttempt: [number, number][] | undefined;
        if (waypoints && waypoints.length > 0) {
          waypointsForEarlyApiAttempt = waypoints.map(wp => wp.coordinates);
        } else if (destinations && destinations.length > 0) {
          waypointsForEarlyApiAttempt = destinations.map(d => toPositionTuple(d.coordinates || d.position!)).filter(Boolean) as [number, number][];
        }

        if (waypointsForEarlyApiAttempt && waypointsForEarlyApiAttempt.length >=2 && accessToken && (Date.now() - routeDataPollingStart > 2000)) { 
            console.log('[RouteDataLoader] Map source slow: Attempting fetchDetailedRouteFromAPI early.');
            const apiRoute = await fetchDetailedRouteFromAPI(waypointsForEarlyApiAttempt, accessToken, profile);
            if (apiRoute) {
              resolve({ route: apiRoute });
              routeDataPollingPromise = null;
        return;
            }
        }
        setTimeout(pollForRoute, opts.retryDelay || DEFAULT_OPTIONS.retryDelay!);
      }
    };
    pollForRoute();
  });
  
  return routeDataPollingPromise;
};

/**
 * Monitors route data loading and returns a callback cleaner
 */
export const monitorRouteLoading = (
  options: RouteLoaderOptions, // Changed to single options object
  onRouteLoaded: (route: Array<[number, number]>) => void
): () => void => {
  let isCancelled = false;
  const { map, destinations, waypoints, profile, accessToken } = options; // Destructure from options

  const checkRoute = async () => {
    if (isCancelled) return;

    // Prioritize explicit waypoints if provided
    const routeWaypoints = waypoints || (destinations?.map(d => toPositionTuple(d.coordinates || d.position!)).filter(Boolean) as [number, number][]);

    // Use the refactored loadRouteData
    const result = await loadRouteData(options);
    
    if (result && result.route) {
      if (!isCancelled) {
        onRouteLoaded(result.route);
      }
    } else if (!isCancelled) {
      // Retry logic or handle no route
      console.warn('[RouteDataLoader] monitorRouteLoading: No route loaded, will retry if underlying loadRouteData retries.');
      // setTimeout(checkRoute, options.retryDelay || DEFAULT_OPTIONS.retryDelay || 1000); // loadRouteData handles its own polling
    }
  };

  checkRoute();

  return () => {
    isCancelled = true;
    // Any specific cleanup for monitorRouteLoading if needed
  };
};