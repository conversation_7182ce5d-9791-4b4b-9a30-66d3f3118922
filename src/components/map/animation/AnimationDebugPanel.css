.animation-debug-panel {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 15px;
  border-radius: 8px;
  width: 320px;
  z-index: 9999; /* Debug panel should be on top */
  font-family: monospace;
  font-size: 12px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  max-height: 80vh;
  overflow-y: auto;
}

.animation-debug-panel.minimized {
  height: 30px;
  overflow: hidden;
}

.debug-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.debug-panel-title {
  font-weight: bold;
  font-size: 14px;
  color: #10b981;
}

.debug-panel-toggle {
  cursor: pointer;
  color: #999;
  font-size: 16px;
}

.debug-panel-section {
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.debug-panel-section-title {
  font-weight: bold;
  color: #9ca3af;
  margin-bottom: 5px;
}

.debug-status {
  display: flex;
  margin-bottom: 8px;
}

.debug-status-label {
  width: 140px;
  color: #d1d5db;
}

.debug-status-value {
  flex: 1;
  font-weight: bold;
}

.debug-status-value.active {
  color: #10b981;
}

.debug-status-value.inactive {
  color: #ef4444;
}

.debug-coordinates {
  word-break: break-all;
  color: #93c5fd;
}

.debug-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.debug-button {
  background-color: #1f2937;
  color: white;
  border: 1px solid #374151;
  border-radius: 4px;
  padding: 6px 10px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.debug-button:hover {
  background-color: #374151;
}

.debug-button.primary {
  background-color: #2563eb;
  border-color: #1d4ed8;
}

.debug-button.primary:hover {
  background-color: #1d4ed8;
}

.debug-button.danger {
  background-color: #dc2626;
  border-color: #b91c1c;
}

.debug-button.danger:hover {
  background-color: #b91c1c;
}

.debug-event-log {
  max-height: 150px;
  overflow-y: auto;
  background-color: rgba(0, 0, 0, 0.3);
  padding: 8px;
  border-radius: 4px;
  margin-top: 10px;
}

.debug-log-entry {
  font-size: 11px;
  line-height: 1.4;
  margin-bottom: 3px;
  color: #d1d5db;
  word-break: break-all;
}

.debug-log-entry.error {
  color: #ef4444;
}

.debug-log-entry.warning {
  color: #f59e0b;
}

.debug-performance {
  font-size: 11px;
  color: #9ca3af;
  margin-top: 5px;
}

.debug-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.debug-indicator.green {
  background-color: #10b981;
}

.debug-indicator.red {
  background-color: #ef4444;
}

.debug-indicator.yellow {
  background-color: #f59e0b;
}

/* Animation for active indicators */
@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.debug-indicator.active {
  animation: pulse 1.5s infinite;
} 