/**
 * POIDiscoveryManager.ts
 * 
 * Point of Interest Discovery System
 * ----------------------------------
 * 
 * RESPONSIBILITY:
 * Manages the discovery of Points of Interest during animation,
 * including slowing/pausing animations and triggering UI notifications.
 * Implements the Singleton pattern to provide centralized POI discovery services.
 * 
 * KEY FUNCTIONALITY:
 * - Detects when vehicle approaches Points of Interest
 * - Manages the discovery state of POIs
 * - Coordinates with ContextualSpeedController to slow vehicle near POIs
 * - Pauses animation via AnimationManager when discovering POIs
 * - Emits discovery events for UI notifications
 * - <PERSON>les camera transitions for cinematic POI approaches
 * 
 * INTEGRATION PATTERNS:
 * - Direct imports: AnimationManager, ContextualSpeedController
 * - Imported by: POIDiscoveryIntegration, CinematicController
 * - Events: Emits custom DOM events for POI discovery/approach
 * 
 * DEPENDENCIES:
 * - AnimationManager: For pausing/resuming animation during POI discovery
 * - ContextualSpeedController: For adjusting vehicle speed near POIs
 * - ComponentInteractionManager: For broadcasting events
 * - CinematicController: For camera transitions when approaching POIs
 * 
 * INITIALIZATION ORDER:
 * POIDiscoveryManager should be initialized after:
 * 1. AnimationManager
 * 2. ComponentInteractionManager
 * 3. ContextualSpeedController
 * 
 * USAGE:
 * ```typescript
 * const poiManager = POIDiscoveryManager.getInstance();
 * 
 * // Register discovery listener
 * poiManager.onPOIDiscovered((event) => {
 *   console.log(`Discovered POI: ${event.payload.poi.name}`);
 * });
 * 
 * // Initialize with POIs
 * poiManager.initializePOIs(pointsOfInterestData);
 * 
 * // Update position during animation
 * poiManager.updatePosition([longitude, latitude]);
 * ```
 */

import mapboxgl from 'mapbox-gl';
import { Position, PositionTuple, toPosition, isValidPosition, toPositionTuple } from '@/types/Position';
import { ComponentInteractionManager } from './ComponentInteractionManager';
import ContextualSpeedController from './ContextualSpeedController';
import CinematicController from './CinematicController';
import { PointOfInterest } from '@/types/POITypes';
import { 
  convertPOITypesToPoi, 
  convertPoiToPOITypes, 
  isPoiTSType, 
  isPOITypesType,
  getPOIPosition,
  safelyGetPOIType
} from '@/types/POIAdapter';
import { 
  AnimationEventType, 
  InteractionEventType,
  DiscoveryEvent, 
  DiscoveryEventPayload 
} from '@/types/AnimationEventTypes.ts';
import AnimationLogger from '@/utils/animationLogger';
import * as turf from '@turf/turf';
import AnimationManagerWrapper from './AnimationManagerWrapper';
import DiscoveryHistoryManager from '../../discovery/DiscoveryHistoryManager';
import PerformanceOptimizer from '../../../services/PerformanceOptimizer';

// Use the updated POI interface from POITypes.updated.ts
export interface POI extends PointOfInterest {
  discoverable: boolean;
  discovered?: boolean;
  importance: number;
  status?: string;
  distance?: number;
}

// Configuration options for POI discovery behavior
export interface POIDiscoveryConfig {
  // Automatic Pause Settings
  automaticPauseEnabled?: boolean;
  pauseOnHighImportancePOIs?: boolean;
  pauseOnCityApproach?: boolean;
  pauseDuration?: number; // seconds
  minImportanceForPause?: number; // 1-10 scale

  // Discovery Settings
  discoveryPauseEnabled?: boolean;
  discoveryDuration?: number;
  approachRadius?: number;
  discoveryRadius?: number;
  maxPOIsToShow?: number;
  prioritizeByDistance?: boolean;
  prioritizeByImportance?: boolean;

  // Mobile Optimization
  mobileOptimized?: boolean;
  touchFriendlyControls?: boolean;
  reducedAnimations?: boolean;
}

// Add new interface for vehicle slowing event
interface VehicleSlowingEvent {
  nearbyPOIs: POI[];
  position: Position;
  timestamp: number;
  originalSpeed: number;
  reducedSpeed: number;
}

// Constants
const MIN_DISTANCE_BETWEEN_DISCOVERIES = 30; // km
const DISCOVERY_COOLDOWN = 45000; // ms (45 seconds)
const DISCOVERY_RADIUS = 2.0; // km (TEMP: increased for easier testing)
const DISCOVERY_APPROACH_RADIUS = 3.0; // km for gradual slowdown
const VEHICLE_SLOW_EVENT = 'vehicle-slowing-for-poi';

// Event name for discovery
const POI_DISCOVERY_EVENT = 'poi-discovery';
const APPROACHING_POI_EVENT = 'approaching-poi';

export interface POIDiscoveryOptions {
  coordinates: Position;
  discoveryRadius?: number;
  discoveryDelay?: number;
}

export interface POIDiscoveryConfig {
  pois?: any[];
  discoveredPOIs?: Set<string>;
  userInterests?: string[];
  selectedCategories?: string[];
  detectionRadius?: number;
  currentArea?: string | null;
}

// Event callback type
type DiscoveryEventCallback = (event: DiscoveryEvent) => void;

/**
 * Theme-agnostic event types for POI/city discovery
 */
export type POIDiscoveryEventType =
  | 'poi-approaching'
  | 'poi-discovered'
  | 'city-approaching'
  | 'city-entered'
  | 'batch-poi-discovered';

export interface POIDiscoveryEventBase {
  type: POIDiscoveryEventType;
  timestamp: number;
}

export interface POIApproachingEvent extends POIDiscoveryEventBase {
  type: 'poi-approaching';
  poi: POI;
  distance: number;
  position: Position;
}

export interface POIDiscoveredEvent extends POIDiscoveryEventBase {
  type: 'poi-discovered';
  poi: POI;
  position: Position;
}

export interface CityApproachingEvent extends POIDiscoveryEventBase {
  type: 'city-approaching';
  city: string;
  distance: number;
  position: Position;
}

export interface CityEnteredEvent extends POIDiscoveryEventBase {
  type: 'city-entered';
  city: string;
  position: Position;
}

export interface BatchPOIDiscoveredEvent extends POIDiscoveryEventBase {
  type: 'batch-poi-discovered';
  pois: POI[];
  area: string;
  position: Position;
}

export type POIDiscoveryFrameworkEvent =
  | POIApproachingEvent
  | POIDiscoveredEvent
  | CityApproachingEvent
  | CityEnteredEvent
  | BatchPOIDiscoveredEvent;

export type POIDiscoveryFrameworkEventListener = (event: POIDiscoveryFrameworkEvent) => void;

export function onPOIDiscoveryFrameworkEvent(listener: POIDiscoveryFrameworkEventListener) {
  document.addEventListener('poi-discovery-framework', (event: Event) => {
    const customEvent = event as CustomEvent;
    listener(customEvent.detail);
  });
}

function emitPOIDiscoveryFrameworkEvent(event: POIDiscoveryFrameworkEvent) {
  const customEvent = new CustomEvent('poi-discovery-framework', {
    detail: event
  });
  document.dispatchEvent(customEvent);
}

/**
 * POIDiscoveryManager class
 * Handles discovery and management of Points of Interest during animations
 */
export class POIDiscoveryManager {
  private static instance: POIDiscoveryManager | null = null;
  
  // Array of POIs
  private pois: POI[] = [];
  
  // Set of discovered POI IDs
  private discoveredPOIs: Set<string> = new Set();
  
  // Discovery settings
  private discoveryRadius: number = DISCOVERY_RADIUS;
  private approachRadius: number = DISCOVERY_APPROACH_RADIUS;
  
  // Vehicle position
  private currentPosition: Position | null = null;
  
  // Timestamp of last discovery
  private lastDiscoveryTime: number = 0;
  
  // Callbacks for discovery events
  private discoveryCallbacks: DiscoveryEventCallback[] = [];
  
  // Animation manager reference
  // private animationManager: AnimationManager | null = null;
  
  // Speed controller reference
  private speedController: ContextualSpeedController | null = null;
  
  // Component interaction manager
  // private interactionManager: ComponentInteractionManager | null = null;
  
  // Debug mode
  private debugMode: boolean = false;
  
  // Configuration
  private config: POIDiscoveryConfig = {};
  
  // Discovery behavior configuration
  private discoveryPauseEnabled: boolean = true;
  private discoveryDuration: number = 5000; // ms

  // Automatic Pause System
  private automaticPauseEnabled: boolean = true;
  private pauseOnHighImportancePOIs: boolean = true;
  private pauseOnCityApproach: boolean = true;
  private pauseDuration: number = 8000; // ms - longer for exploration
  private minImportanceForPause: number = 7; // 1-10 scale
  private currentlyPaused: boolean = false;
  private pauseTimeoutId: number | null = null;
  private pauseReason: 'poi' | 'city' | null = null;

  // Mobile Optimization
  private mobileOptimized: boolean = false;
  private touchFriendlyControls: boolean = false;
  private reducedAnimations: boolean = false;

  // Discovery History Manager
  private historyManager: DiscoveryHistoryManager;

  // Performance Optimizer
  private performanceOptimizer: PerformanceOptimizer;

  // Add declaration for poiMap if it is used in the class
  private poiMap: Map<string, POI> = new Map();

  private activeClientId: string | null = null; // For client-specific POI filtering
  private readonly LS_KEY_DISCOVERED_POIS = 'moroccoTravel_discoveredPoiIds'; // localStorage key
  private lastNearbyPOIs: POI[] = []; // Cache for performance optimization
  
  /**
   * Configure POI discovery behavior
   * @param config Configuration options for POI discovery
   */
  public configure(config: {
    discoveryPauseEnabled?: boolean;
    discoveryDuration?: number;
    approachRadius?: number;
    discoveryRadius?: number;
    maxPOIsToShow?: number;
    prioritizeByDistance?: boolean;
    prioritizeByImportance?: boolean;
  }): void {
    this.discoveryPauseEnabled = config.discoveryPauseEnabled ?? true;
    this.discoveryDuration = config.discoveryDuration ?? 5000;
    this.approachRadius = config.approachRadius ?? DISCOVERY_APPROACH_RADIUS;
    this.discoveryRadius = config.discoveryRadius ?? DISCOVERY_RADIUS;
    
    // Handle additional configuration options
    if (config.maxPOIsToShow !== undefined) {
      this.config.maxPOIsToShow = config.maxPOIsToShow;
    }
    
    if (config.prioritizeByDistance !== undefined) {
      this.config.prioritizeByDistance = config.prioritizeByDistance;
    }
    
    if (config.prioritizeByImportance !== undefined) {
      this.config.prioritizeByImportance = config.prioritizeByImportance;
    }
    
    // Log configuration
    if (this.debugMode) {
      console.log('POI Discovery configured:', {
        discoveryPauseEnabled: this.discoveryPauseEnabled,
        discoveryDuration: this.discoveryDuration,
        approachRadius: this.approachRadius,
        discoveryRadius: this.discoveryRadius,
        maxPOIsToShow: this.config.maxPOIsToShow,
        prioritizeByDistance: this.config.prioritizeByDistance,
        prioritizeByImportance: this.config.prioritizeByImportance
      });
    }
    
    AnimationLogger.log('info', 'poi', 'POI Discovery configuration updated', config);
  }
  
  /**
   * Get singleton instance with optional configuration
   */
  public static getInstance(config?: POIDiscoveryConfig): POIDiscoveryManager {
    if (!POIDiscoveryManager.instance) {
      POIDiscoveryManager.instance = new POIDiscoveryManager(config);
    }
    
    // Apply new config if provided
    if (config) {
      POIDiscoveryManager.instance.applyConfig(config);
    }
    
    return POIDiscoveryManager.instance;
  }
  
  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor(config?: POIDiscoveryConfig) {
    this.applyConfig(config || {});
    this.speedController = ContextualSpeedController.getInstance();
    this.historyManager = DiscoveryHistoryManager.getInstance();
    this.performanceOptimizer = PerformanceOptimizer.getInstance();

    // Detect mobile device and apply optimizations
    this.detectAndApplyMobileOptimizations();

    // Initialize event listeners and other setup
    this.initializeEventListeners();

    // Load discovered POIs from localStorage
    this.loadDiscoveredPOIsFromLocalStorage();

    // Start a new discovery session
    this.historyManager.startSession();

    if (this.debugMode) {
      AnimationLogger.log('info', 'animation', 'POIDiscoveryManager initialized');
    }
  }

  /**
   * Detect mobile device and apply mobile optimizations
   */
  private detectAndApplyMobileOptimizations(): void {
    const isMobile = window.innerWidth <= 768 ||
                    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    if (isMobile) {
      this.mobileOptimized = true;
      this.touchFriendlyControls = true;
      this.reducedAnimations = true;

      // Adjust timing for mobile
      this.pauseDuration = Math.min(this.pauseDuration, 6000); // Shorter pause on mobile
      this.discoveryDuration = Math.min(this.discoveryDuration, 3000); // Faster discovery

      // Adjust detection radius for mobile (smaller screens)
      this.discoveryRadius = Math.max(this.discoveryRadius * 0.8, 1); // Slightly smaller radius
      this.approachRadius = Math.max(this.approachRadius * 0.8, 3); // Smaller approach radius

      AnimationLogger.log('info', 'poi', 'Mobile optimizations applied', {
        pauseDuration: this.pauseDuration,
        discoveryDuration: this.discoveryDuration,
        discoveryRadius: this.discoveryRadius,
        approachRadius: this.approachRadius
      });
    }
  }

  /**
   * Initialize event listeners for external control
   */
  private initializeEventListeners(): void {
    // Listen for force resume events from UI
    document.addEventListener('force-resume-animation', () => {
      this.forceResumeFromPause();
    });

    // Listen for mobile optimization events
    document.addEventListener('mobile-optimization-toggle', (event: Event) => {
      const customEvent = event as CustomEvent;
      this.mobileOptimized = customEvent.detail.enabled;
      if (this.mobileOptimized) {
        this.touchFriendlyControls = true;
        this.reducedAnimations = true;
        this.pauseDuration = Math.min(this.pauseDuration, 6000);
      }
    });
  }
  
  /**
   * Apply configuration settings
   */
  private applyConfig(config: POIDiscoveryConfig): void {
    // Store config
    this.config = {...this.config, ...config};

    // Automatic Pause Settings
    if (config.automaticPauseEnabled !== undefined) {
      this.automaticPauseEnabled = config.automaticPauseEnabled;
    }
    if (config.pauseOnHighImportancePOIs !== undefined) {
      this.pauseOnHighImportancePOIs = config.pauseOnHighImportancePOIs;
    }
    if (config.pauseOnCityApproach !== undefined) {
      this.pauseOnCityApproach = config.pauseOnCityApproach;
    }
    if (config.pauseDuration !== undefined) {
      this.pauseDuration = config.pauseDuration * 1000; // Convert to ms
    }
    if (config.minImportanceForPause !== undefined) {
      this.minImportanceForPause = config.minImportanceForPause;
    }

    // Mobile Optimization Settings
    if (config.mobileOptimized !== undefined) {
      this.mobileOptimized = config.mobileOptimized;
      // Auto-enable mobile optimizations
      if (this.mobileOptimized) {
        this.touchFriendlyControls = true;
        this.reducedAnimations = true;
        this.pauseDuration = Math.min(this.pauseDuration, 6000); // Shorter on mobile
      }
    }
    if (config.touchFriendlyControls !== undefined) {
      this.touchFriendlyControls = config.touchFriendlyControls;
    }
    if (config.reducedAnimations !== undefined) {
      this.reducedAnimations = config.reducedAnimations;
    }

    // Discovery Settings
    if (config.discoveryPauseEnabled !== undefined) {
      this.discoveryPauseEnabled = config.discoveryPauseEnabled;
    }
    if (config.discoveryDuration !== undefined) {
      this.discoveryDuration = config.discoveryDuration;
    }
    if (config.approachRadius !== undefined) {
      this.approachRadius = config.approachRadius;
    }
    if (config.discoveryRadius !== undefined) {
      this.discoveryRadius = config.discoveryRadius;
    }

    // Initialize POIs if provided
    if (config.pois && Array.isArray(config.pois)) {
      this.initializePOIs(config.pois);
    }

    // Initialize discovered POIs if provided
    if (config.discoveredPOIs) {
      this.discoveredPOIs = new Set(config.discoveredPOIs);
    }

    // Set detection radius if provided (legacy support)
    if (config.detectionRadius) {
      this.discoveryRadius = config.detectionRadius;
      this.approachRadius = config.detectionRadius * 3; // Triple the detection radius
    }

    AnimationLogger.log('info', 'poi', 'POIDiscoveryManager config applied', {
      automaticPauseEnabled: this.automaticPauseEnabled,
      pauseOnHighImportancePOIs: this.pauseOnHighImportancePOIs,
      pauseOnCityApproach: this.pauseOnCityApproach,
      pauseDuration: this.pauseDuration,
      minImportanceForPause: this.minImportanceForPause,
      mobileOptimized: this.mobileOptimized,
      ...config
    });
  }
  
  /**
   * Initialize POIs for discovery
   */
  public initializePOIs(pois: any[]): void {
    this.pois = pois.map(poi => {
      // Check if POI structure matches our POI interface
      if ('position' in poi && 'id' in poi && 'name' in poi && 'clientId' in poi) {
        // Already has the right structure
        if (!poi.discoverable) {
          poi.discoverable = true;
        }
        return poi as POI;
      }
      
      // Convert from different POI type
      return {
        id: poi.id || `poi-${Math.random().toString(36).substr(2, 9)}`,
        clientId: poi.clientId || `client-${Math.random().toString(36).substr(2, 9)}`,
        name: poi.name || 'Unnamed POI',
        position: toPositionTuple(this.getCoordinatesFromPOI(poi)),
        type: poi.type || 'generic',
        description: poi.description || '',
        images: poi.images || [],
        image: poi.image || '',
        discoverable: true,
        discovered: false,
        importance: poi.importance || 1,
        status: poi.status || 'active',
        tags: poi.tags || [],
        categories: poi.categories || []
      };
    });
    
    AnimationLogger.log('info', 'poi', `Initialized ${this.pois.length} POIs for discovery`);
  }
  
  /**
   * Add a single POI to the discovery system
   */
  public addPOI(poi: any): void {
    try {
      let formattedPoi: POI;
      
      // Check if POI structure matches our POI interface
      if ('position' in poi && 'id' in poi && 'name' in poi && 'clientId' in poi) {
        // Already has the right structure
        formattedPoi = { ...poi, discoverable: poi.discoverable ?? true } as POI;
      } else {
        // Convert from different POI type
        formattedPoi = {
          id: poi.id || `poi-${Math.random().toString(36).substr(2, 9)}`,
          clientId: poi.clientId || `client-${Math.random().toString(36).substr(2, 9)}`,
          name: poi.name || 'Unnamed POI',
          position: toPositionTuple(this.getCoordinatesFromPOI(poi)),
          type: poi.type || 'generic',
          description: poi.description || '',
          images: poi.images || [],
          image: poi.image || '',
          discoverable: true,
          discovered: false,
          importance: poi.importance || 1,
          status: poi.status || 'active',
          tags: poi.tags || [],
          categories: poi.categories || []
        };
      }
      
      // Check if POI with same ID already exists
      const existingPoiIndex = this.pois.findIndex(p => p.id === formattedPoi.id);
      if (existingPoiIndex >= 0) {
        // Update existing POI
        this.pois[existingPoiIndex] = formattedPoi;
        AnimationLogger.poi('info', `Updated existing POI: ${formattedPoi.name} (${formattedPoi.id})`);
      } else {
        // Add new POI
        this.pois.push(formattedPoi);
        AnimationLogger.poi('info', `Added new POI: ${formattedPoi.name} (${formattedPoi.id})`);
      }
    } catch (error) {
      console.error('Error adding POI:', error);
      AnimationLogger.poi('error', `Failed to add POI: ${poi?.name || 'unknown'}`, error);
    }
  }
  
  /**
   * Extract coordinates from various POI formats
   */
  private getCoordinatesFromPOI(poi: any): Position {
    if (!poi) return [0, 0];
    
    // Handle different POI types
    if (isPoiTSType(poi)) {
      return toPosition(poi.position) || [0, 0];
    } else if (isPOITypesType(poi)) {
      return toPosition(poi.position) || [0, 0];
    } else if (poi.position) {
      return toPosition(poi.position) || [0, 0];
    } else if (poi.coordinates) {
      return toPosition(poi.coordinates) || [0, 0];
    }
    
    return [0, 0];
  }
  
  /**
   * Helper function to convert POICoordinates to turf point
   */
  private coordinatesToPoint(coordinates: Position) {
    const posTuple = toPositionTuple(coordinates);
    return turf.point(posTuple);
  }
  
  /**
   * Helper functions for coordinate conversion
   */
  private coordinatesToPOICoordinates(coordinates: Position): Position {
    return toPosition(coordinates) || [0, 0];
  }
  
  /**
   * Update position and check for nearby POIs
   * Returns array of nearby POIs
   */
  public updatePosition(position: Position): POI[] {
    try {
      // Performance optimization: Skip frame if needed
      if (this.performanceOptimizer.shouldSkipFrame()) {
        return this.lastNearbyPOIs || [];
      }

      // Validate position
      if (!isValidPosition(position)) {
        console.warn('Invalid position provided to updatePosition:', position);
        return [];
      }

      // Convert position to standard format
      const standardPos = toPosition(position);
      if (!standardPos) {
        console.warn('Failed to convert position to standard format:', position);
        return [];
      }

      this.currentPosition = standardPos;
      const nearbyPOIs: POI[] = [];

      // Create a turf point for the current position
      const positionPoint = turf.point(toPositionTuple(standardPos));

      for (const poi of this.pois) {
        try {
          // Get POI position and validate
          const poiPos = toPositionTuple(poi.position);
          if (!poiPos || !isValidPosition(poiPos)) {
            console.warn(`Invalid POI position for ${poi.id}:`, poi.position);
            continue;
          }

          // Create turf point for POI
          const poiPoint = turf.point(poiPos);
          
          // Calculate distance
          const distance = turf.distance(positionPoint, poiPoint, { units: 'kilometers' });
          
          // Update POI distance
          poi.distance = distance;
          
          // Check if within discovery radius
          if (distance <= this.discoveryRadius) {
            nearbyPOIs.push(poi);

            // Check for discovery
            this.checkDiscovery(poi, standardPos);

            // Check for automatic pause (only for high-importance POIs)
            if (this.shouldAutoPauseForPOI(poi, distance) && !poi.discovered) {
              this.triggerAutomaticPause('poi', {
                poi,
                distance,
                position: standardPos
              });
            }
          }

          // Check for approach notifications (wider radius)
          else if (distance <= this.approachRadius && !poi.discovered) {
            // Emit approaching event for UI notifications
            const approachEvent: POIApproachingEvent = {
              type: 'poi-approaching',
              timestamp: Date.now(),
              poi,
              distance,
              position: standardPos
            };
            emitPOIDiscoveryFrameworkEvent(approachEvent);
          }
        } catch (poiError) {
          console.warn(`Error processing POI ${poi.id}:`, poiError);
          continue;
        }
      }

      // Check for city approaches
      this.checkCityApproaches(standardPos);

      // After finding nearbyPOIs:
      const clusters = this.findPOIClusters(position);
      if (clusters.length > 0) {
        // Emit cluster event and update speed context
        const largestCluster = clusters.reduce((a, b) => (a.length > b.length ? a : b));
        // Emit event
        const clusterEvent = {
          type: 'batch-poi-discovered' as 'batch-poi-discovered',
          timestamp: Date.now(),
          pois: largestCluster,
          area: largestCluster.length > 0 && largestCluster[0].position ? `Cluster near ${toPositionTuple(largestCluster[0].position).join(',')}` : "Discovered Cluster",
          position: position
        };
        emitPOIDiscoveryFrameworkEvent(clusterEvent);
        // Update speed controller for cluster
        if (this.speedController) {
          this.speedController.updateContext({
            distanceToPOI: Math.min(...largestCluster.map(p => p.distance || Infinity)),
            isDiscoveringPOI: largestCluster.some(p => !p.discovered)
          });
        }
      }

      // Update speed controller if we found nearby POIs
      if (nearbyPOIs.length > 0 && this.speedController) {
        this.speedController.updateContext({
          distanceToPOI: Math.min(...nearbyPOIs.map(p => p.distance || Infinity)),
          isDiscoveringPOI: nearbyPOIs.some(p => !p.discovered)
        });
      }

      // Cache results for performance optimization
      this.lastNearbyPOIs = nearbyPOIs;

      return nearbyPOIs;
    } catch (error) {
      console.error('Error in updatePosition:', error);
      return [];
    }
  }
  
  /**
   * Find POIs within specified radius of position
   */
  public findNearbyPOIs(position: Position, radius: number = this.discoveryRadius): POI[] {
    if (!position || !isValidPosition(position)) {
      return [];
    }
    
    const positionPoint = this.coordinatesToPoint(position);
    const nearbyPOIs: POI[] = [];
    
    for (const poi of this.pois) {
      // Skip POIs that don't match the active client
      if (this.activeClientId && poi.clientId && poi.clientId !== this.activeClientId && poi.clientId !== 'generic') {
        continue;
      }
      
      // Continue with existing logic
      try {
        const poiCoordinates = this.getCoordinatesFromPOI(poi);
        if (!isValidPosition(poiCoordinates)) continue;
        
        const poiPoint = this.coordinatesToPoint(poiCoordinates);
        const distance = turf.distance(positionPoint, poiPoint, { units: 'kilometers' });
        
        if (distance <= radius) {
          // Mark this POI with its distance from current position
          poi.distance = distance;
          nearbyPOIs.push(poi);
        }
      } catch (error) {
        console.error(`Error calculating distance to POI ${poi.name}:`, error);
      }
    }
    
    return nearbyPOIs;
  }
  
  /**
   * Check if the vehicle is approaching any POIs
   */
  public checkApproaching(position: Position, radius: number = this.approachRadius): POI[] {
    try {
      // Validate position
      if (!isValidPosition(position)) {
        console.warn('Invalid position provided to checkApproaching:', position);
        return [];
      }

      // Convert position to standard format
      const standardPos = toPosition(position);
      if (!standardPos) {
        console.warn('Failed to convert position to standard format:', position);
        return [];
      }

      const positionPoint = turf.point(toPositionTuple(standardPos));
      const nearbyPOIs: POI[] = [];

      for (const poi of this.pois) {
        try {
          // Get POI position and validate
          const poiPos = toPositionTuple(poi.position);
          if (!poiPos || !isValidPosition(poiPos)) {
            console.warn(`Invalid POI position for ${poi.id}:`, poi.position);
            continue;
          }

          // Create turf point for POI
          const poiPoint = turf.point(poiPos);
          
          // Calculate distance
          const distance = turf.distance(positionPoint, poiPoint, { units: 'kilometers' });
          
          // Update POI distance
          poi.distance = distance;
          
          // Check if within approach radius
          if (distance <= radius) {
            nearbyPOIs.push(poi);
            
            // Adjust speed if approaching
            this.adjustSpeedForPOI(standardPos, poi, distance);
            
            // Emit approaching event
            this.emitApproachingEvent(poi, standardPos, distance);
          }
        } catch (poiError) {
          console.warn(`Error processing POI ${poi.id}:`, poiError);
          continue;
        }
      }

      // Update speed controller if we found nearby POIs
      if (nearbyPOIs.length > 0 && this.speedController) {
        this.speedController.updateContext({
          distanceToPOI: Math.min(...nearbyPOIs.map(p => p.distance || Infinity)),
          isDiscoveringPOI: nearbyPOIs.some(p => !p.discovered)
        });
      }

      return nearbyPOIs;
    } catch (error) {
      console.error('Error in checkApproaching:', error);
      return [];
    }
  }
  
  /**
   * Adjust vehicle speed when approaching POI
   */
  private adjustSpeedForPOI(position: Position, poi: POI, distance: number): void {
    if (!this.speedController) return;
    
    try {
      // Determine proximity status based on distance
      let speedContext: any = {};
      
      if (distance <= this.discoveryRadius) {
        // Very close, treat as POI discovery
        speedContext = {
          isNearPOI: true,
          isInPoiCluster: false,
          terrain: 'default'
        };
      } else if (distance <= this.approachRadius) {
        // In approach radius, slow down gradually
        speedContext = {
          isNearPOI: true,
          isInPoiCluster: false,
          terrain: 'default'
        };
      }
      
      // Update the speed controller with the new context
      this.speedController.updateSpeed(toPositionTuple(position) as [number, number], speedContext);
      
      // Log the speed adjustment
      if (this.debugMode) {
        AnimationLogger.log('info', 'animation', `Adjusting speed for POI: ${poi.name}, distance: ${distance.toFixed(2)}km`);
      }
    } catch (error) {
      console.error('Error in adjustSpeedForPOI:', error);
    }
  }
  
  /**
   * Emit approaching event
   */
  private emitApproachingEvent(poi: POI, position: Position, distance: number): void {
    try {
      // Validate inputs
      if (!poi || !poi.id || !poi.name || !poi.position ||
          !isValidPosition(position) || typeof distance !== 'number' || isNaN(distance)) {
        console.warn('Invalid parameters for emitApproachingEvent');
        return;
      }

      // Convert POI position to standard format
      const poiPos = toPositionTuple(poi.position);
      if (!poiPos) {
        console.warn(`Invalid POI position for ${poi.id}:`, poi.position);
        return;
      }
      
      const event = {
        type: APPROACHING_POI_EVENT,
        poi: {
          id: poi.id,
          name: poi.name,
          position: poiPos,
        },
        distance,
        timestamp: Date.now(),
        position
      };
      
      // Dispatch DOM event
      if (typeof document !== 'undefined') {
        const customEvent = new CustomEvent(APPROACHING_POI_EVENT, { detail: event });
        document.dispatchEvent(customEvent);
      }
      
      // Log approach
      AnimationLogger.poi('info', `Approaching POI: ${poi.name} (${distance.toFixed(2)}km)`);
    } catch (error) {
      console.error('Error in emitApproachingEvent:', error);
    }
  }
  
  /**
   * Check if a POI should be discovered
   */
  private checkDiscovery(poi: POI, position: Position): boolean {
    try {
      // Validate position
      if (!isValidPosition(position)) {
        console.warn('Invalid position provided to checkDiscovery:', position);
        return false;
      }

      // Convert position to standard format
      const standardPos = toPosition(position);
      if (!standardPos) {
        console.warn('Failed to convert position to standard format:', position);
        return false;
      }
      
      // Skip if already discovered
      if (this.discoveredPOIs.has(poi.id)) {
        return false;
      }
      
      // Skip if not discoverable
      if (poi.discoverable === false) {
        return false;
      }
      
      // Skip if on discovery cooldown
      const now = Date.now();
      if (now - this.lastDiscoveryTime < DISCOVERY_COOLDOWN) {
        return false;
      }
      
      // Get POI position and validate
      const poiPos = toPositionTuple(poi.position);
      if (!poiPos || !isValidPosition(poiPos)) {
        console.warn(`Invalid POI position for ${poi.id}:`, poi.position);
        return false;
      }
      
      // Calculate distance
      const positionPoint = turf.point(toPositionTuple(standardPos));
      const poiPoint = turf.point(poiPos);
      const distance = turf.distance(positionPoint, poiPoint, { units: 'kilometers' });
      
      // Check if within discovery radius
      if (distance <= this.discoveryRadius) {
        // Trigger discovery
        this.triggerPOIDiscovery(toPositionTuple(poi.position));
        
        // Mark as discovered
        this.markAsDiscovered(poi);
        
        // Update last discovery time
        this.lastDiscoveryTime = now;
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error(`Error in checkDiscovery for POI ${poi?.id || 'unknown'}:`, error);
      return false;
    }
  }
  
  /**
   * Trigger POI discovery
   */
  private triggerPOIDiscovery(position: PositionTuple): void {
    if (!this.currentPosition) return;
    
    // Get the nearest undiscovered POI
    const nearbyPOIs = this.findNearbyPOIs(position);
    if (nearbyPOIs.length === 0) return;
    
    // Sort by distance
    const sortedPOIs = [...nearbyPOIs].sort((a, b) => (a.distance || 999) - (b.distance || 999));
    
    // Find first undiscovered POI
    const poiToDiscover = sortedPOIs.find(poi => 
      !this.discoveredPOIs.has(poi.id) && poi.discoverable !== false
    );
    
    if (!poiToDiscover) return;
    
    // Mark as discovered
    this.markAsDiscovered(poiToDiscover);

    // Record discovery in history
    this.historyManager.recordDiscovery(
      poiToDiscover as any, // Convert POI type
      this.currentPosition,
      poiToDiscover.distance || 0
    );

    // Pause animation for discovery, if enabled
    if (this.discoveryPauseEnabled) {
      const animationManager = AnimationManagerWrapper.getInstance();
      if (animationManager) {
        animationManager.pauseAnimation(true);

        // Auto-resume after duration
        setTimeout(() => {
          animationManager.pauseAnimation(false);
        }, this.discoveryDuration);
      }
    }
    
    // Use CinematicController for POI discovery sequence
    try {
      const cinematicController = CinematicController.getInstance();
      cinematicController.playPOIDiscoverySequence(poiToDiscover as any, position);
    } catch (error) {
      console.error('Failed to play POI discovery cinematic:', error);
    }
    
    // Emit discovery event
    const event: DiscoveryEvent = {
      type: InteractionEventType.POI_DISCOVERED,
      timestamp: Date.now(),
      payload: {
        poi: poiToDiscover,
        position: position,
        discoveryTime: Date.now()
      }
    };
    
    // Update last discovery time
    this.lastDiscoveryTime = Date.now();
    
    // Notify listeners
    this.notifyDiscoveryListeners(event);
    
    // Emit framework event
    const frameworkEvent: POIDiscoveredEvent = {
      type: 'poi-discovered',
      timestamp: Date.now(),
      poi: poiToDiscover,
      position: position
    };
    
    emitPOIDiscoveryFrameworkEvent(frameworkEvent);
  }
  
  /**
   * Find POI at specific coordinates
   */
  private findPoiAtCoordinates(coordinates: Position): POI | null {
    // Find POI with matching position
    const targetPos = toPosition(coordinates);
    if (!targetPos) return null;

    return this.pois.find(poi => {
      const poiPos = toPositionTuple(poi.position) as PositionTuple;
      if (!poiPos) return false;

      // Compare positions with a small tolerance
      const poiTuple = toPositionTuple(poiPos);
      const targetTuple = toPositionTuple(targetPos);
      const latDiff = Math.abs(poiTuple[1] - targetTuple[1]);
      const lngDiff = Math.abs(poiTuple[0] - targetTuple[0]);
      return latDiff < 0.0001 && lngDiff < 0.0001;
    }) || null;
  }
  
  /**
   * Mark a POI as discovered
   */
  private markAsDiscovered(poi: POI): void {
    if (!poi) return;
    
    poi.discovered = true;
    this.discoveredPOIs.add(poi.id);
    
    // Save to localStorage whenever a new POI is discovered
    this.saveDiscoveredPOIsToLocalStorage();
  }
  
  private saveDiscoveredPOIsToLocalStorage(): void {
    try {
      const poiIdsArray = Array.from(this.discoveredPOIs);
      localStorage.setItem(this.LS_KEY_DISCOVERED_POIS, JSON.stringify(poiIdsArray));
    } catch (error) {
      console.error('Failed to save discovered POIs to localStorage:', error);
    }
  }
  
  private loadDiscoveredPOIsFromLocalStorage(): void {
    try {
      const savedPOIs = localStorage.getItem(this.LS_KEY_DISCOVERED_POIS);
      if (savedPOIs) {
        const poiIds = JSON.parse(savedPOIs) as string[];
        poiIds.forEach(id => this.discoveredPOIs.add(id));
        
        if (this.debugMode) {
          AnimationLogger.log('info', 'animation', `Loaded ${this.discoveredPOIs.size} discovered POIs from localStorage`);
        }
      }
    } catch (error) {
      console.error('Failed to load discovered POIs from localStorage:', error);
    }
  }
  
  /**
   * Register discovery listener
   */
  public onPOIDiscovered(callback: DiscoveryEventCallback): void {
    this.discoveryCallbacks.push(callback);
  }
  
  /**
   * Notify all discovery listeners
   */
  private notifyDiscoveryListeners(event: DiscoveryEvent): void {
    this.discoveryCallbacks.forEach(callback => {
      try {
        callback(event);
      } catch (error) {
        console.error('Error in POI discovery callback:', error);
      }
    });
  }
  
  /**
   * Get the ContextualSpeedController instance
   */
  public getSpeedController(): ContextualSpeedController | null {
    return this.speedController;
  }
  
  /**
   * Reset POI discoveries
   */
  public reset(): void {
    this.discoveredPOIs.clear();
    this.lastDiscoveryTime = 0;
    this.currentPosition = null;
    
    // Reset POI discovery status
    this.pois.forEach(poi => {
      poi.discovered = false;
    });
    
    AnimationLogger.log('info', 'state', 'POI discovery manager reset');
  }
  
  /**
   * Set debug mode
   */
  public setDebugMode(debug: boolean): void {
    this.debugMode = debug;
  }

  /**
   * Get POIs for a specified area
   */
  public getPOIs(areaName: string | null): POI[] {
    try {
      // Validate area name
      if (!areaName) {
        console.warn(`⚠️ [${Date.now()}] getPOIs called with null or empty area name`);
        return [];
      }
      
      // Get POIs for the area from our map
      const pois = this.poiMap.get(areaName);
      
      // If no POIs found for the area, return empty array
      if (!pois || !Array.isArray(pois)) {
        return [];
      }
      
      // Filter and validate POIs before returning
      return pois.filter(poi => {
        // Ensure POI has valid coordinates
        if (!poi || !poi.coordinates || 
            typeof poi.coordinates.lng !== 'number' || isNaN(poi.coordinates.lng) ||
            typeof poi.coordinates.lat !== 'number' || isNaN(poi.coordinates.lat)) {
          console.warn(`⚠️ [${Date.now()}] Invalid POI found in area ${areaName}:`, poi);
          return false;
        }
        return true;
      });
    } catch (error) {
      console.error(`❌ [${Date.now()}] Error in getPOIs:`, error);
      return [];
    }
  }

  /**
   * TESTING/DEBUG: Force POI discovery for a given POI ID (ignores cooldown/discovered)
   * Now always triggers discovery event, regardless of vehicle position or distance.
   */
  public forceDiscoverPOIById(poiId: string): void {
    const poi = this.pois.find(p => p.id === poiId);
    if (!poi) {
      console.warn(`POI with ID ${poiId} not found`);
      return;
    }

    // Ensure POI has valid position
    const poiTuple = toPositionTuple(poi.position);
    if (!poi || !poi.position ||
        typeof poiTuple[0] !== 'number' || isNaN(poiTuple[0]) ||
        typeof poiTuple[1] !== 'number' || isNaN(poiTuple[1])) {
      console.warn(`Invalid position for POI ${poiId}`);
      return;
    }

    // Set current position to POI position
    this.currentPosition = toPositionTuple(poi.position);
    if (!this.currentPosition) {
      console.warn(`Failed to convert POI position for ${poiId}`);
      return;
    }

    // Trigger discovery
    this.triggerPOIDiscovery(toPositionTuple(poi.position));
  }

  // =====================================================
  // AUTOMATIC PAUSE SYSTEM
  // =====================================================

  /**
   * Check if animation should be automatically paused for a POI
   */
  private shouldAutoPauseForPOI(poi: POI, distance: number): boolean {
    if (!this.automaticPauseEnabled || this.currentlyPaused) {
      return false;
    }

    // Check if POI meets importance threshold
    if (this.pauseOnHighImportancePOIs && poi.importance >= this.minImportanceForPause) {
      return true;
    }

    // Check for special POI categories that should always pause
    const alwaysPauseCategories = ['landmark', 'unesco', 'palace', 'mosque', 'museum'];
    if (poi.category && alwaysPauseCategories.includes(poi.category.toLowerCase())) {
      return true;
    }

    // Check for special tags
    const alwaysPauseTags = ['must-see', 'iconic', 'famous', 'historic'];
    if (poi.tags && poi.tags.some(tag => alwaysPauseTags.includes(tag.toLowerCase()))) {
      return true;
    }

    return false;
  }

  /**
   * Check if animation should be automatically paused for city approach
   */
  private shouldAutoPauseForCity(cityName: string, distance: number): boolean {
    if (!this.automaticPauseEnabled || !this.pauseOnCityApproach || this.currentlyPaused) {
      return false;
    }

    // Pause for major cities within 2km
    const majorCities = ['marrakech', 'casablanca', 'rabat', 'fez', 'chefchaouen'];
    if (distance <= 2.0 && majorCities.includes(cityName.toLowerCase())) {
      return true;
    }

    return false;
  }

  /**
   * Trigger automatic pause with reason
   */
  private triggerAutomaticPause(reason: 'poi' | 'city', data: any): void {
    if (this.currentlyPaused) {
      return; // Already paused
    }

    this.currentlyPaused = true;
    this.pauseReason = reason;

    // Pause the animation
    const animationManager = AnimationManagerWrapper.getInstance();
    if (animationManager) {
      animationManager.pauseAnimation();
    }

    // Emit pause event for UI
    this.emitAutomaticPauseEvent(reason, data);

    // Set timeout to auto-resume
    this.pauseTimeoutId = window.setTimeout(() => {
      this.resumeFromAutomaticPause();
    }, this.pauseDuration);

    AnimationLogger.log('info', 'poi', `Automatic pause triggered: ${reason}`, data);
  }

  /**
   * Resume from automatic pause
   */
  public resumeFromAutomaticPause(): void {
    if (!this.currentlyPaused) {
      return;
    }

    // Clear timeout if exists
    if (this.pauseTimeoutId) {
      clearTimeout(this.pauseTimeoutId);
      this.pauseTimeoutId = null;
    }

    this.currentlyPaused = false;
    const previousReason = this.pauseReason;
    this.pauseReason = null;

    // Resume the animation
    const animationManager = AnimationManagerWrapper.getInstance();
    if (animationManager) {
      animationManager.resumeAnimation();
    }

    // Emit resume event for UI
    this.emitAutomaticResumeEvent(previousReason);

    AnimationLogger.log('info', 'poi', `Automatic pause resumed from: ${previousReason}`);
  }

  /**
   * Force resume from automatic pause (user action)
   */
  public forceResumeFromPause(): void {
    this.resumeFromAutomaticPause();
  }

  /**
   * Emit automatic pause event
   */
  private emitAutomaticPauseEvent(reason: 'poi' | 'city', data: any): void {
    const event: POIDiscoveryFrameworkEvent = {
      type: reason === 'poi' ? 'poi-discovered' : 'city-entered',
      timestamp: Date.now(),
      ...data
    };

    emitPOIDiscoveryFrameworkEvent(event);

    // Also emit specific automatic pause event
    const pauseEvent = new CustomEvent('automatic-pause', {
      detail: {
        reason,
        data,
        pauseDuration: this.pauseDuration,
        canResume: true,
        timestamp: Date.now()
      }
    });
    document.dispatchEvent(pauseEvent);
  }

  /**
   * Emit automatic resume event
   */
  private emitAutomaticResumeEvent(reason: 'poi' | 'city' | null): void {
    const resumeEvent = new CustomEvent('automatic-resume', {
      detail: {
        previousReason: reason,
        timestamp: Date.now()
      }
    });
    document.dispatchEvent(resumeEvent);
  }

  /**
   * Get current pause status
   */
  public getPauseStatus(): {
    isPaused: boolean;
    reason: 'poi' | 'city' | null;
    remainingTime: number;
  } {
    return {
      isPaused: this.currentlyPaused,
      reason: this.pauseReason,
      remainingTime: this.pauseTimeoutId ? this.pauseDuration : 0
    };
  }

  // =====================================================
  // CITY APPROACH DETECTION
  // =====================================================

  /**
   * Check for city approaches and trigger appropriate events
   */
  private checkCityApproaches(position: Position): void {
    const majorCities = [
      { name: 'Marrakech', coordinates: [-7.9811, 31.6295], radius: 15 },
      { name: 'Casablanca', coordinates: [-7.5898, 33.5731], radius: 20 },
      { name: 'Rabat', coordinates: [-6.8498, 34.0209], radius: 15 },
      { name: 'Fez', coordinates: [-5.0003, 34.0181], radius: 12 },
      { name: 'Chefchaouen', coordinates: [-5.2685, 35.1689], radius: 8 },
      { name: 'Essaouira', coordinates: [-9.7769, 31.5085], radius: 10 },
      { name: 'Merzouga', coordinates: [-4.0135, 31.0801], radius: 5 }
    ];

    const positionPoint = turf.point(toPositionTuple(position));

    for (const city of majorCities) {
      const cityPoint = turf.point(city.coordinates);
      const distance = turf.distance(positionPoint, cityPoint, { units: 'kilometers' });

      // Check for city approach (within radius but not too close)
      if (distance <= city.radius && distance > 2) {
        this.handleCityApproach(city.name, distance, position);
      }
      // Check for city entry (very close)
      else if (distance <= 2) {
        this.handleCityEntry(city.name, position);
      }
    }
  }

  /**
   * Handle city approach event
   */
  private handleCityApproach(cityName: string, distance: number, position: Position): void {
    // Emit city approaching event
    const approachEvent: CityApproachingEvent = {
      type: 'city-approaching',
      timestamp: Date.now(),
      city: cityName,
      distance,
      position
    };
    emitPOIDiscoveryFrameworkEvent(approachEvent);

    // Check for automatic pause
    if (this.shouldAutoPauseForCity(cityName, distance)) {
      this.triggerAutomaticPause('city', {
        cityName,
        distance,
        position
      });
    }

    AnimationLogger.log('info', 'poi', `Approaching city: ${cityName} (${distance.toFixed(1)}km)`);
  }

  /**
   * Handle city entry event
   */
  private handleCityEntry(cityName: string, position: Position): void {
    // Emit city entered event
    const entryEvent: CityEnteredEvent = {
      type: 'city-entered',
      timestamp: Date.now(),
      city: cityName,
      position
    };
    emitPOIDiscoveryFrameworkEvent(entryEvent);

    // Update speed controller for city context
    if (this.speedController) {
      this.speedController.updateContext({
        distanceToCity: 0,
        isInCity: true,
        cityName: cityName
      });
    }

    AnimationLogger.log('info', 'poi', `Entered city: ${cityName}`);
  }

  // =====================================================
  // DISCOVERY HISTORY INTEGRATION
  // =====================================================

  /**
   * Track POI exploration start
   */
  public trackPOIExplorationStart(poiId: string): void {
    this.historyManager.recordExplorationStart(poiId);
  }

  /**
   * Track POI exploration end
   */
  public trackPOIExplorationEnd(poiId: string): void {
    this.historyManager.recordExplorationEnd(poiId);
  }

  /**
   * Track POI added to itinerary
   */
  public trackPOIAddedToItinerary(poiId: string): void {
    this.historyManager.recordAddedToItinerary(poiId);
  }

  /**
   * Get current session statistics
   */
  public getCurrentSessionStats() {
    return this.historyManager.getCurrentSessionStats();
  }

  /**
   * Get travel insights
   */
  public getTravelInsights() {
    return this.historyManager.generateTravelInsights();
  }

  /**
   * End current discovery session
   */
  public endDiscoverySession() {
    return this.historyManager.endSession();
  }

  /**
   * Get discovery history
   */
  public getDiscoveryHistory() {
    return this.historyManager.getDiscoveryHistory();
  }

  /**
   * TESTING/DEBUG: Move vehicle marker to a POI's location (for debug UI)
   */
  public moveVehicleToPOI(poiId: string): void {
    const poi = this.pois.find(p => p.id === poiId);
    if (!poi) {
      console.warn(`POI with ID ${poiId} not found`);
      return;
    }

    // Set current position to POI position
    const poiTuple = toPositionTuple(poi.position);
    if (!poi || !poi.position ||
        typeof poiTuple[0] !== 'number' || isNaN(poiTuple[0]) ||
        typeof poiTuple[1] !== 'number' || isNaN(poiTuple[1])) {
      console.warn(`Invalid position for POI ${poiId}`);
      return;
    }

    // Set current position to POI position
    this.currentPosition = toPositionTuple(poi.position);
    if (!this.currentPosition) {
      console.warn(`Failed to convert POI position for ${poiId}`);
      return;
    }

    // Update vehicle marker on map
    try {
      const map = this.getMap();
      if (map) {
        const vehicleMarker = map.getSource('vehicle');
        if (vehicleMarker) {
          (vehicleMarker as any).setData({
            type: 'Feature',
            properties: {},
            geometry: {
              type: 'Point',
              coordinates: this.currentPosition
            }
          });
        }
      }
    } catch (error) {
      console.error('Error updating vehicle marker:', error);
    }
  }

  private getMap(): mapboxgl.Map | null {
    return (window as any).map || null;
  }

  /**
   * Find clusters of POIs within a given radius and minimum cluster size
   */
  public findPOIClusters(position: Position, clusterRadius: number = 3, minClusterSize: number = 3): POI[][] {
    const clusters: POI[][] = [];
    const checked = new Set<string>();
    const positionPoint = turf.point(toPositionTuple(position));
    for (const poi of this.pois) {
      if (checked.has(poi.id)) continue;
      const cluster: POI[] = [poi];
      const poiPoint = turf.point(toPositionTuple(poi.position));
      for (const other of this.pois) {
        if (poi.id === other.id || checked.has(other.id)) continue;
        const otherPoint = turf.point(toPositionTuple(other.position));
        const distance = turf.distance(poiPoint, otherPoint, { units: 'kilometers' });
        if (distance <= clusterRadius) {
          cluster.push(other);
        }
      }
      if (cluster.length >= minClusterSize) {
        cluster.forEach(p => checked.add(p.id));
        clusters.push(cluster);
      }
    }
    return clusters;
  }

  public setActiveClient(clientId: string | null): void {
    this.activeClientId = clientId;
    if (this.debugMode) {
      AnimationLogger.log('info', 'animation', `POIDiscoveryManager: Active client set to ${clientId || 'none/generic'}`);
    }
  }
}

export default POIDiscoveryManager;
