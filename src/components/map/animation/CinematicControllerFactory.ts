/**
 * CinematicControllerFactory.ts
 * 
 * Factory for creating CinematicController instances
 */

import CinematicController from './CinematicController';
import { PointOfInterest } from '@/types/poi';
import { Destination } from '@/types/destination';
import mapboxgl from 'mapbox-gl';

/**
 * Extends the CinematicController class with a static create method
 * to work around the private constructor issue
 */
export default class CinematicControllerFactory {
  /**
   * Creates a new CinematicController instance
   * This is a workaround for the private constructor in CinematicController
   */
  static create(
    map: mapboxgl.Map,
    route: [number, number][],
    pois: PointOfInterest[],
    destinations: Destination[]
  ): any {
    // @ts-ignore - Bypass the private constructor restriction
    return new CinematicController(map, route, pois, destinations);
  }
}

// Add the static create method to the CinematicController class
// @ts-ignore - Adding static method to existing class
CinematicController.create = CinematicControllerFactory.create;