import mapboxgl from 'mapbox-gl';
import AnimationDebugTools from './AnimationDebugTools';

// Define the type for the event detail from ContextualSpeedController
interface SpeedControllerContext {
  cityProximity: 'none' | 'approaching' | 'center';
  poiStatus: 'none' | 'nearby' | 'discovery';
  terrain: 'default' | 'mountain' | 'desert' | 'coastal' | 'scenic';
  isScenic: boolean;
}

interface SpeedContextUpdateEventDetail {
  speedMultiplier: number;
  context: SpeedControllerContext;
    timestamp: number;
}

export interface VehicleConfig {
  elementId?: string;
  map: mapboxgl.Map;
}

export interface VehicleDebugInfo {
  elementId: string;
  isVisible: boolean;
  exists: boolean;
  position?: { left: string; top: string };
  styles?: {
    visibility: string;
    display: string;
    opacity: string;
    zIndex: string;
    transform: string;
  };
  computedStyles?: {
    visibility: string;
    display: string;
    opacity: string;
    zIndex: string;
    transform: string;
  };
  parentNode?: string | null;
  timestamp: string;
}

export class VehicleManager {
  private static instance: VehicleManager;
  private elementId: string;
  private map: mapboxgl.Map | null = null;
  private isVisible: boolean = false;
  private lastKnownPosition: [number, number] | null = null;
  private lastKnownBearing: number = 0;
  private routeBounds: { center: [number, number]; bounds: [[number, number], [number, number]] } | null = null;
  private debugLog: VehicleDebugInfo[] = [];
  private mapboxMarker: mapboxgl.Marker | null = null;
  private simpleMarker: HTMLElement | null = null;
  private debugLoggingEnabled: boolean = true;
  private visibilityCheckInterval: number | null = null;
  private lastSetLngLatTime: number = 0;
  private frameCounter: number = 0;
  private lastPosition: [number, number] | null = null;

  private constructor() {
    this.elementId = 'direct-vehicle-marker';
    // console.log(`🔧 [${new Date().toISOString()}] VehicleManager initialized`); // Commented out

    // Add global reference for debugging
    (window as any).vehicleManager = this;

    // Track this instance globally for debugging
    if (typeof window !== 'undefined' && (window as any).__vehicleMarkerTracker) {
      (window as any).__vehicleMarkerTracker.instances.add(this);
      console.log(`🔍 VehicleManager instance created. Total instances: ${(window as any).__vehicleMarkerTracker.instances.size}`);
    }

    // Listen for speed context updates
    document.addEventListener('speed-context-updated', this.handleSpeedContextUpdate as EventListener);
  }

  public static getInstance(): VehicleManager {
    if (!this.instance) {
      this.instance = new VehicleManager();
    }
    return this.instance;
  }

  public initialize(config: VehicleConfig) {
    this.map = config.map;
    if (config.elementId) {
      this.elementId = config.elementId;
    }

    // Clean up any existing markers from previous sessions
    this.cleanupDuplicateMarkers();

    // console.log(`🔧 [${new Date().toISOString()}] VehicleManager initialized with:`, { // Commented out
    //   mapInstance: !!this.map,
    //   mapLoaded: this.map?.loaded(),
    //   elementId: this.elementId
    // });

    // Start periodic vehicle visibility check
    this.startVisibilityChecking();

    return this;
  }

  public setMap(map: mapboxgl.Map): void {
    this.initialize({ map });
  }
  
  private startVisibilityChecking(): void {
    if (this.visibilityCheckInterval) {
      window.clearInterval(this.visibilityCheckInterval);
    }
    
    this.visibilityCheckInterval = window.setInterval(() => {
      const vehicleInfo = this.getVehicleDebugInfo();
      if (this.debugLoggingEnabled) {
        // console.log(`🔍 [${vehicleInfo.timestamp}] Vehicle visibility check:`, vehicleInfo); // Commented out to reduce noise
      }
      
      // Store info in debug log (keep last 20 entries)
      this.debugLog.push(vehicleInfo);
      if (this.debugLog.length > 20) {
        this.debugLog.shift();
      }
      
      // Auto fix if marker exists but isn't visible
      if (vehicleInfo.exists && !vehicleInfo.isVisible && this.lastKnownPosition) {
        console.log(`🔧 [${new Date().toISOString()}] Auto-fixing vehicle visibility`);
        this.forceVehicleVisibility();
      }
    }, 3000);
  }
  
  private stopVisibilityChecking(): void {
    if (this.visibilityCheckInterval) {
      window.clearInterval(this.visibilityCheckInterval);
      this.visibilityCheckInterval = null;
    }
  }

  private handleSpeedContextUpdate = (event: CustomEvent<SpeedContextUpdateEventDetail>) => {
    const eventDetail = event.detail;
    console.log(`🚗 [${new Date().toISOString()}] VehicleManager received speed-context-updated:`, eventDetail);
    AnimationDebugTools.log('info', 'VehicleManager received speed-context-updated', eventDetail);

    const vehicleEl = document.getElementById(this.elementId);
    if (!vehicleEl) {
      console.warn(`⚠️ [${new Date().toISOString()}] Vehicle element ${this.elementId} not found during context update`);
      return;
    }

    // Remove previous contextual classes to ensure a clean slate for new context
    vehicleEl.classList.remove(
      'vehicle-marker--city-center',
      'vehicle-marker--city-approach',
      'vehicle-marker--poi-discovery',
      'vehicle-marker--poi-nearby',
      'vehicle-marker--terrain-mountain',
      'vehicle-marker--terrain-desert',
      'vehicle-marker--terrain-coastal',
      'vehicle-marker--scenic',
      'vehicle-marker--default' // Add default to ensure it's cleared if set
    );

    let scale = 1.0;
    let appliedContextClass = 'vehicle-marker--default'; // Start with a default class

    const newContext = eventDetail.context; // Use the nested context object

    // Apply styles based on context
    if (newContext.cityProximity === 'center') {
      vehicleEl.classList.add('vehicle-marker--city-center');
      appliedContextClass = 'vehicle-marker--city-center';
      scale = 0.7;
    } else if (newContext.cityProximity === 'approaching') {
      vehicleEl.classList.add('vehicle-marker--city-approach');
      appliedContextClass = 'vehicle-marker--city-approach';
      scale = 0.85;
    }

    if (newContext.poiStatus === 'discovery') {
      vehicleEl.classList.add('vehicle-marker--poi-discovery');
      // Append class if another is already set, or set it if it's the first contextual class
      appliedContextClass = appliedContextClass !== 'vehicle-marker--default' ? `${appliedContextClass} vehicle-marker--poi-discovery` : 'vehicle-marker--poi-discovery';
      scale = Math.min(scale, 0.75); 
    } else if (newContext.poiStatus === 'nearby') {
      vehicleEl.classList.add('vehicle-marker--poi-nearby');
      appliedContextClass = appliedContextClass !== 'vehicle-marker--default' ? `${appliedContextClass} vehicle-marker--poi-nearby` : 'vehicle-marker--poi-nearby';
      scale = Math.min(scale, 0.9);
    }

    // Only apply terrain/scenic if no city/POI context has set a more specific class/scale
    if (appliedContextClass === 'vehicle-marker--default') {
      if (newContext.isScenic) {
        vehicleEl.classList.add('vehicle-marker--scenic');
        appliedContextClass = 'vehicle-marker--scenic';
        scale = 0.9;
      } else {
        switch (newContext.terrain) {
          case 'mountain':
            vehicleEl.classList.add('vehicle-marker--terrain-mountain');
            appliedContextClass = 'vehicle-marker--terrain-mountain';
            scale = 0.8;
            break;
          case 'desert':
            vehicleEl.classList.add('vehicle-marker--terrain-desert');
            appliedContextClass = 'vehicle-marker--terrain-desert';
            scale = 1.1;
            break;
          case 'coastal':
            vehicleEl.classList.add('vehicle-marker--terrain-coastal');
            appliedContextClass = 'vehicle-marker--terrain-coastal';
            scale = 0.95;
            break;
          default:
            // If no specific terrain, ensure default class is added if nothing else applied
            if(appliedContextClass === 'vehicle-marker--default') vehicleEl.classList.add('vehicle-marker--default');
            break;
        }
      }
    } else if (vehicleEl.classList.length === 0) {
        // If, after all checks, no specific class was added (e.g. only scale changed by city/poi but no specific terrain/scenic class),
        // ensure the default class is present if no other more specific class was added.
        vehicleEl.classList.add('vehicle-marker--default');
    }

    // Update the transform, preserving rotation
    const currentTransform = vehicleEl.style.transform || '';
    const rotationMatch = currentTransform.match(/rotate\\(([^)]+)\\)/);
    const currentRotationDeg = rotationMatch && rotationMatch[1] ? parseFloat(rotationMatch[1]) : this.lastKnownBearing;

    vehicleEl.style.transform = `translate(-50%, -50%) rotate(${currentRotationDeg}deg) scale(${scale})`;
    
    console.log(`🎨 [${new Date().toISOString()}] Applied vehicle style for context. Classes: ${Array.from(vehicleEl.classList).join(' ')}, Scale: ${scale}`);
    AnimationDebugTools.log('info', `Applied vehicle style. Classes: ${Array.from(vehicleEl.classList).join(' ')}`, { context: newContext, scale });
  };

  public updateVehicleVisibility(visible: boolean): boolean {
    const timestamp = new Date().toISOString();

    // If using Mapbox marker, handle visibility through Mapbox API
    if (this.mapboxMarker) {
      if (visible) {
        // Make sure marker is added to map
        if (this.map) {
          this.mapboxMarker.addTo(this.map);
          console.log(`✅ [${timestamp}] Mapbox marker made visible`);
        }
      } else {
        // Remove marker from map
        this.mapboxMarker.remove();
        console.log(`✅ [${timestamp}] Mapbox marker hidden`);
      }
      this.isVisible = visible;
      return true;
    }

    // If using Mapbox marker, don't interfere with DOM-based visibility
    // The Mapbox marker handles its own visibility through addTo/remove
    const vehicleEl = document.getElementById(this.elementId);

    if (!vehicleEl) {
      console.warn(`⚠️ [${timestamp}] Vehicle element ${this.elementId} not found`);
      AnimationDebugTools.log('warn', `Vehicle element ${this.elementId} not found`);

      // If element not found but we have position data, recreate it
      if (visible && this.lastKnownPosition) {
        console.log(`🔄 [${timestamp}] Recreating missing vehicle marker`);
        this.recreateVehicleMarker(this.lastKnownPosition, this.lastKnownBearing);
        return true;
      }

      return false;
    }

    // For Mapbox markers, ensure the element is always visible in DOM
    // Don't set display: none as it conflicts with Mapbox positioning
    console.log(`🚗 [${timestamp}] Ensuring Mapbox marker element is visible:`, {
      from: {
        visibility: vehicleEl.style.visibility,
        display: vehicleEl.style.display,
        opacity: vehicleEl.style.opacity
      }
    });

    // Force the element to be visible - Mapbox handles show/hide through addTo/remove
    vehicleEl.style.zIndex = '99999';
    vehicleEl.style.visibility = 'visible';
    vehicleEl.style.display = 'block'; // Always block for Mapbox markers
    vehicleEl.style.opacity = '1';
    this.isVisible = visible;

    // Add a brief flash to make the vehicle more noticeable (if making visible)
    if (visible) {
      vehicleEl.style.transform = vehicleEl.style.transform || 'translate(-50%, -50%)';
      const originalTransform = vehicleEl.style.transform;
      vehicleEl.style.transform = `${originalTransform} scale(1.5)`;
      vehicleEl.style.transition = 'transform 0.2s ease-out';
      
      setTimeout(() => {
        if (vehicleEl) {
          vehicleEl.style.transform = originalTransform;
        }
      }, 200);
    }

    // Verify DOM update was applied
          setTimeout(() => {
      const updatedEl = document.getElementById(this.elementId);
      if (updatedEl) {
        console.log(`✅ [${new Date().toISOString()}] Vehicle visibility updated:`, {
          requested: visible,
          actual: {
            visibility: updatedEl.style.visibility,
            display: updatedEl.style.display,
            opacity: updatedEl.style.opacity,
            computed: {
              visibility: window.getComputedStyle(updatedEl).visibility,
              display: window.getComputedStyle(updatedEl).display,
              opacity: window.getComputedStyle(updatedEl).opacity
            }
          },
          success:
            (visible && updatedEl.style.visibility === 'visible' &&
             window.getComputedStyle(updatedEl).visibility === 'visible') ||
            (!visible && (updatedEl.style.visibility === 'hidden' ||
             window.getComputedStyle(updatedEl).visibility === 'hidden'))
        });
        } else {
        console.warn(`⚠️ [${new Date().toISOString()}] Vehicle element disappeared during visibility update - will recreate if needed`);
        // Don't treat this as a fatal error - React might have re-rendered
        // The element will be recreated on the next position update if needed
        }
    }, 50);
      
    AnimationDebugTools.log('info', `Vehicle visibility set to ${visible}`);
      return true;
  }

  public updateVehiclePosition(position: [number, number] | { x: number, y: number }, bearing: number = 0) {
    const timestamp = new Date().toISOString();

    if (!this.map) {
      console.error(`❌ [${timestamp}] Map not initialized in VehicleManager`);
      AnimationDebugTools.log('error', 'Map not initialized in VehicleManager');
      return;
    }

    // Handle geographic coordinates (preferred method)
    if (Array.isArray(position)) {
      // Store last known geographic position
      this.lastKnownPosition = position;
      this.lastKnownBearing = bearing;

      // DISABLED: Cleanup was interfering with marker positioning
      // this.cleanupDuplicateMarkers();

      // Use ONLY Mapbox GL JS Marker for proper coordinate handling
      this.updateMapboxMarker(position, bearing);
      return;
    }

    // Convert pixel position to geographic coordinates if needed
    console.warn(`⚠️ [${timestamp}] Received pixel coordinates, converting to geographic coordinates`);

    // For now, we'll skip pixel positioning and only use geographic coordinates
    // This ensures consistency and avoids DOM conflicts
    console.warn(`⚠️ [${timestamp}] Skipping pixel positioning - use geographic coordinates instead`);
    return;
  }

  public isVehicleVisible(): boolean {
    // Check actual DOM element state
    const vehicleEl = document.getElementById(this.elementId);
    if (vehicleEl) {
      const computedStyle = window.getComputedStyle(vehicleEl);
      const isActuallyVisible = 
        computedStyle.visibility === 'visible' && 
        computedStyle.display !== 'none' && 
        parseFloat(computedStyle.opacity) > 0;
      
      // Update internal state to match reality
      this.isVisible = isActuallyVisible;
    }
    
    return this.isVisible;
  }
  
  public forceVehicleVisibility(): boolean {
    const vehicleEl = document.getElementById(this.elementId);
    const timestamp = new Date().toISOString();
    
    if (!vehicleEl) {
      console.warn(`⚠️ [${timestamp}] Cannot force visibility - element not found`);
      
      // Try to recreate if we have position data
      if (this.lastKnownPosition) {
        this.recreateVehicleMarker(this.lastKnownPosition, this.lastKnownBearing);
        return true;
      }
      
          return false;
        }
        
    console.log(`🔆 [${timestamp}] Forcing vehicle visibility with ultra-high z-index`);
    
    // Apply all visibility properties with !important
    vehicleEl.setAttribute('style', `
      position: absolute !important;
      visibility: visible !important;
      display: block !important;
      opacity: 1 !important;
      z-index: 99999 !important;
      pointer-events: none !important;
      left: ${vehicleEl.style.left || '50%'} !important;
      top: ${vehicleEl.style.top || '50%'} !important;
      width: 50px !important;
      height: 50px !important;
      border-radius: 50% !important;
      background-color: #ff0000 !important;
      border: 5px solid yellow !important;
      box-shadow: 0 0 30px rgba(255, 255, 0, 0.8) !important;
      outline: 3px dashed red !important;
    `);
    
    this.isVisible = true;
    
    // Add bright debug label
    let debugLabel = vehicleEl.querySelector('.vehicle-debug-label') as HTMLElement;
    if (!debugLabel) {
      debugLabel = document.createElement('div');
      debugLabel.className = 'vehicle-debug-label';
      debugLabel.innerHTML = 'VEHICLE<br>MARKER';
      debugLabel.style.position = 'absolute';
      debugLabel.style.top = '-40px';
      debugLabel.style.left = '0';
      debugLabel.style.right = '0';
      debugLabel.style.textAlign = 'center';
      debugLabel.style.color = 'white';
      debugLabel.style.backgroundColor = 'red';
      debugLabel.style.padding = '2px 5px';
      debugLabel.style.fontWeight = 'bold';
      debugLabel.style.fontSize = '12px';
      debugLabel.style.zIndex = '10000';
      debugLabel.style.borderRadius = '4px';
      vehicleEl.appendChild(debugLabel);
    }
      
      return true;
  }

  public createVehicleMarker(position: [number, number] | { x:number, y:number }, bearing: number): HTMLElement | null {
      AnimationDebugTools.log('info', 'VehicleManager.createVehicleMarker called - using Mapbox marker approach');

      // Only handle geographic coordinates
      if (Array.isArray(position)) {
        this.updateMapboxMarker(position, bearing);
        return null; // Mapbox markers don't return HTML elements
      } else {
        console.warn('⚠️ Pixel coordinates not supported in createVehicleMarker - use geographic coordinates');
        return null;
      }
  }

  public setVisibility(visible: boolean): void {
      AnimationDebugTools.log('info', 'VehicleManager.setVisibility called (delegating to updateVehicleVisibility)');
      this.updateVehicleVisibility(visible);
  }

  public getVehicleDebugInfo(): VehicleDebugInfo {
    const vehicleEl = document.getElementById(this.elementId);
    const timestamp = new Date().toISOString();
    
    const debugInfo: VehicleDebugInfo = {
      elementId: this.elementId,
      isVisible: this.isVisible,
      exists: !!vehicleEl,
      timestamp
    };
    
    if (vehicleEl) {
      debugInfo.position = {
        left: vehicleEl.style.left,
        top: vehicleEl.style.top
      };
      
      debugInfo.styles = {
        visibility: vehicleEl.style.visibility,
        display: vehicleEl.style.display,
        opacity: vehicleEl.style.opacity,
        zIndex: vehicleEl.style.zIndex,
        transform: vehicleEl.style.transform
      };
      
      debugInfo.computedStyles = {
        visibility: window.getComputedStyle(vehicleEl).visibility,
        display: window.getComputedStyle(vehicleEl).display,
        opacity: window.getComputedStyle(vehicleEl).opacity,
        zIndex: window.getComputedStyle(vehicleEl).zIndex,
        transform: window.getComputedStyle(vehicleEl).transform
      };
      
      debugInfo.parentNode = vehicleEl.parentNode ? 
        (vehicleEl.parentNode as HTMLElement).id || 'unnamed-parent' : 
        null;
    }
    
    return debugInfo;
  }
  
  public getDebugLog(): VehicleDebugInfo[] {
    return [...this.debugLog];
  }
  
  public toggleDebugLogging(enabled?: boolean): boolean {
    if (typeof enabled === 'boolean') {
      this.debugLoggingEnabled = enabled;
    } else {
      this.debugLoggingEnabled = !this.debugLoggingEnabled;
    }
    
    console.log(`🔧 [${new Date().toISOString()}] Vehicle debug logging ${this.debugLoggingEnabled ? 'enabled' : 'disabled'}`);
    return this.debugLoggingEnabled;
  }
  
  public checkOverlappingElements(): Array<{id: string, className: string, zIndex: string}> {
    console.log(`🔍 [${new Date().toISOString()}] Checking for elements that might overlap the vehicle marker`);
    
    const overlappingElements = Array.from(document.querySelectorAll('[style*="z-index"]'))
      .filter(el => {
        const zIndex = window.getComputedStyle(el).zIndex;
        return !isNaN(parseInt(zIndex)) && parseInt(zIndex) > 5000;
      })
      .map(el => ({
        id: (el as HTMLElement).id,
        className: (el as HTMLElement).className,
        zIndex: window.getComputedStyle(el).zIndex
      }));
    
    console.log(`📋 [${new Date().toISOString()}] Found ${overlappingElements.length} potentially overlapping elements:`, overlappingElements);
    return overlappingElements;
  }

  private recreateVehicleMarker(
    position: [number, number] | { x: number, y: number },
    bearing: number
  ): HTMLElement | null {
    const timestamp = new Date().toISOString();
    
    if (!this.map) {
      console.error(`❌ [${timestamp}] Cannot recreate vehicle marker - map not initialized`);
      return null;
    }

    // Remove existing marker if it exists
    const existingMarker = document.getElementById(this.elementId);
    if (existingMarker && existingMarker.parentNode) {
      console.log(`🗑️ [${timestamp}] Removing existing vehicle marker`);
      existingMarker.parentNode.removeChild(existingMarker);
    }

    console.log(`🆕 [${timestamp}] Recreating vehicle marker`);

    // Create new marker with extreme visibility settings
    const markerEl = document.createElement('div');
    markerEl.id = this.elementId;
    markerEl.className = 'vehicle-marker';
    markerEl.style.position = 'absolute'; // Use absolute positioning relative to map container
    markerEl.style.transform = `translate(-50%, -50%) rotate(${bearing}deg)`;
    markerEl.style.width = '60px'; // Large but not overwhelming
    markerEl.style.height = '60px'; // Large but not overwhelming
    markerEl.style.backgroundColor = '#ff0000'; // Bright red for visibility
    markerEl.style.borderRadius = '50%';
    markerEl.style.zIndex = '999999'; // Ultra high z-index
    markerEl.style.border = '4px solid #ffff00'; // Bold yellow border
    markerEl.style.boxShadow = '0 0 30px rgba(255, 0, 0, 0.8), 0 0 60px rgba(255, 255, 0, 0.6)'; // Strong glow effect
    markerEl.style.visibility = 'visible';
    markerEl.style.display = 'block';
    markerEl.style.opacity = '1';
    markerEl.style.pointerEvents = 'none'; // Don't interfere with map interactions
    markerEl.setAttribute('data-testid', 'vehicle-marker');

    // Start with a test position in the center of the map container
    markerEl.style.left = '50%';
    markerEl.style.top = '50%';
    
    // Add debug label
    const debugLabel = document.createElement('div');
    debugLabel.textContent = 'VEHICLE';
    debugLabel.style.position = 'absolute';
    debugLabel.style.top = '-30px';
    debugLabel.style.left = '0';
    debugLabel.style.right = '0';
    debugLabel.style.textAlign = 'center';
    debugLabel.style.color = 'white';
    debugLabel.style.backgroundColor = 'red';
    debugLabel.style.borderRadius = '4px';
    debugLabel.style.padding = '2px 5px';
    debugLabel.style.fontWeight = 'bold';
    debugLabel.style.fontSize = '12px';
    debugLabel.style.zIndex = '10000';
    markerEl.appendChild(debugLabel);

    // Use stable container for proper positioning - avoid React re-render issues
    let targetContainer: HTMLElement | null = null;

    // First try to find the explore-map-container (more stable than Mapbox container)
    const exploreMapContainer = document.getElementById('explore-map-container');
    if (exploreMapContainer) {
      targetContainer = exploreMapContainer;
      console.log(`🔧 [${timestamp}] Using explore-map-container for vehicle marker positioning`);
    } else if (this.map) {
      targetContainer = this.map.getContainer();
      console.log(`🔧 [${timestamp}] Using map container for vehicle marker positioning`);
    } else {
      targetContainer = document.body;
      console.warn(`🔧 [${timestamp}] Map not available, falling back to document.body`);
    }
    
    console.log(`📌 [${timestamp}] Appending vehicle marker to:`, {
      containerType: targetContainer.nodeName,
      containerId: targetContainer.id,
      containerClassName: targetContainer.className
    });
    
    // Append to container
    targetContainer.appendChild(markerEl);

    // Add blinking animation for debugging
    markerEl.style.animation = 'blink 1s infinite';

    // Add CSS animation if it doesn't exist
    if (!document.getElementById('vehicle-marker-styles')) {
      const style = document.createElement('style');
      style.id = 'vehicle-marker-styles';
      style.textContent = `
        @keyframes blink {
          0%, 50% { opacity: 1; }
          51%, 100% { opacity: 0.3; }
        }
      `;
      document.head.appendChild(style);
    }

    // Update position based on type
    if (Array.isArray(position)) {
      // Position is geo coordinates
      try {
        if (this.map) {
            const point = this.map.project(position);
            this.updateVehiclePosition({ x: point.x, y: point.y }, bearing);
        } else {
            console.error(`❌ [${timestamp}] Map not available for projecting coordinates in recreateVehicleMarker`);
        }
      } catch (error) {
        console.error(`❌ [${timestamp}] Error projecting coordinates:`, error);
      }
    } else {
      // Position is already pixel coordinates
      this.updateVehiclePosition(position, bearing);
    }
    
    this.updateVehicleVisibility(true);

    AnimationDebugTools.log('info', 'Vehicle marker recreated');
    
    // Return the created element for any additional processing
    return markerEl;
  }
  
  public dispose(): void {
    this.stopVisibilityChecking();
    document.removeEventListener('speed-context-updated', this.handleSpeedContextUpdate as EventListener); // Clean up listener
    
    const vehicleEl = document.getElementById(this.elementId);
    if (vehicleEl && vehicleEl.parentNode) {
      vehicleEl.parentNode.removeChild(vehicleEl);
    }
    
    this.map = null;
    this.isVisible = false;
    this.lastKnownPosition = null;
    this.debugLog = [];
    
    console.log(`🧹 [${new Date().toISOString()}] VehicleManager disposed`);
  }

  /**
   * Set route bounds for intelligent map centering
   */
  public setRouteBounds(routePoints: [number, number][]): void {
    if (routePoints.length === 0) return;

    // Calculate bounds from route points
    let minLng = routePoints[0][0], maxLng = routePoints[0][0];
    let minLat = routePoints[0][1], maxLat = routePoints[0][1];

    for (const [lng, lat] of routePoints) {
      minLng = Math.min(minLng, lng);
      maxLng = Math.max(maxLng, lng);
      minLat = Math.min(minLat, lat);
      maxLat = Math.max(maxLat, lat);
    }

    const center: [number, number] = [
      (minLng + maxLng) / 2,
      (minLat + maxLat) / 2
    ];

    this.routeBounds = {
      center,
      bounds: [[minLng, minLat], [maxLng, maxLat]]
    };

    console.log(`📍 Route bounds calculated:`, this.routeBounds);
  }

  /**
   * Calculate the center point of the current route for map centering
   */
  private calculateRouteBounds(currentPosition: [number, number]): [number, number] {
    // Use calculated route center if available
    if (this.routeBounds) {
      return this.routeBounds.center;
    }

    // Fallback to last known position or current position
    if (this.lastKnownPosition) {
      return this.lastKnownPosition;
    }

    return currentPosition;
  }

  /**
   * Calculate distance between two geographic points in kilometers
   */
  private calculateDistance(point1: [number, number], point2: [number, number]): number {
    const [lng1, lat1] = point1;
    const [lng2, lat2] = point2;

    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLng = this.toRadians(lng2 - lng1);

    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  /**
   * Convert degrees to radians
   */
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Update vehicle position using Mapbox GL JS Marker (more reliable)
   */
  private updateMapboxMarker(position: [number, number], bearing: number): void {
    const timestamp = new Date().toISOString();

    try {
      // Don't remove DOM elements - let Mapbox handle its own markers

      // Clean up any existing duplicate markers first
      this.cleanupDuplicateMarkers();

      // NUCLEAR OPTION: Create simple DOM marker instead of Mapbox marker
      if (!this.simpleMarker) {
        console.log(`🚀 [${timestamp}] Creating SIMPLE DOM marker (bypassing Mapbox)`);

        // Create a simple DOM element
        this.simpleMarker = document.createElement('div');
        this.simpleMarker.id = 'simple-vehicle-marker';
        this.simpleMarker.style.cssText = `
          position: absolute !important;
          width: 20px !important;
          height: 20px !important;
          background: #FF0000 !important;
          border: 3px solid #FFFFFF !important;
          border-radius: 50% !important;
          z-index: 999999 !important;
          pointer-events: none !important;
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
          transform-origin: center center !important;
        `;

        // Add to map container
        const mapContainer = this.map!.getContainer();
        mapContainer.appendChild(this.simpleMarker);

        console.log(`✅ [${timestamp}] Simple DOM marker created and added to map`);
      }

      // Update marker position and rotation
      const vehicleElement = this.mapboxMarker.getElement();
      if (vehicleElement) {
        vehicleElement.style.transform = `rotate(${bearing}deg)`;
      }

      // Validate coordinates before setting position
      const isValidCoordinate = (coord: number) =>
        !isNaN(coord) && isFinite(coord); // Remove coord !== 0 check - 0 is valid!

      const [lng, lat] = position;
      const coordsValid = isValidCoordinate(lng) && isValidCoordinate(lat) &&
                         lng >= -180 && lng <= 180 && lat >= -90 && lat <= 90;

      // PERFORMANCE OPTIMIZATION: Skip heavy debugging during animation
      // Only validate coordinates, skip expensive map operations
      if (!coordsValid) {
        console.error(`❌ [${timestamp}] Invalid coordinates detected!`, {
          lng, lat, coordsValid
        });
        return; // Don't set invalid coordinates
      }

      // RAPID POSITION CHANGE DEBUGGING
      const frameCount = (this.frameCounter = (this.frameCounter || 0) + 1);
      const now = performance.now();
      const timeSinceLastUpdate = this.lastSetLngLatTime ? now - this.lastSetLngLatTime : 0;

      // Check for rapid position jumps that might cause flashing
      let positionJump = false;
      let jumpDistance = 0;
      if (this.lastPosition) {
        const [lastLng, lastLat] = this.lastPosition;
        jumpDistance = Math.sqrt(Math.pow(lng - lastLng, 2) + Math.pow(lat - lastLat, 2));
        positionJump = jumpDistance > 0.1; // Large jump in coordinates
      }

      // Check for coordinate anomalies that cause flashing
      const isNearZero = Math.abs(lng) < 0.001 && Math.abs(lat) < 0.001;
      const isTopLeft = lng === 0 && lat === 0;
      const hasNaN = isNaN(lng) || isNaN(lat);
      const isInfinite = !isFinite(lng) || !isFinite(lat);

      // Only log problematic frames or every 10th frame
      const shouldLog = frameCount % 10 === 0 || positionJump || isNearZero || isTopLeft || hasNaN || isInfinite || timeSinceLastUpdate < 5;

      if (shouldLog) {
        console.log(`📍 [${timestamp}] COORDINATE DEBUG (frame ${frameCount}):`, {
          position: [lng, lat],
          lastPosition: this.lastPosition,
          bearing,
          coordsValid,
          timeSinceLastUpdate: `${timeSinceLastUpdate.toFixed(2)}ms`,
          positionJump,
          jumpDistance: jumpDistance.toFixed(6),
          isNearZero,
          isTopLeft,
          hasNaN,
          isInfinite,
          rapidUpdate: timeSinceLastUpdate < 5
        });
      }

      this.lastPosition = [lng, lat];
      this.lastSetLngLatTime = now;

      // SIMPLE MARKER POSITIONING: Debug coordinate projection
      if (this.simpleMarker) {
        // Convert geographic coordinates to pixel coordinates
        const point = this.map!.project(position);

        // DEBUG: Check if projection is working correctly
        const mapCenter = this.map!.getCenter();
        const mapZoom = this.map!.getZoom();
        const mapBounds = this.map!.getBounds();
        const containerSize = this.map!.getContainer().getBoundingClientRect();

        console.log(`🔍 [${timestamp}] PROJECTION DEBUG:`, {
          inputCoordinates: [lng, lat],
          projectedPixels: { x: point.x, y: point.y },
          mapCenter: [mapCenter.lng, mapCenter.lat],
          mapZoom,
          mapBounds: {
            north: mapBounds.getNorth(),
            south: mapBounds.getSouth(),
            east: mapBounds.getEast(),
            west: mapBounds.getWest()
          },
          containerSize: {
            width: containerSize.width,
            height: containerSize.height
          },
          coordinatesInBounds: mapBounds.contains(position),
          pixelsValid: point.x > 0 && point.y > 0 && point.x < containerSize.width && point.y < containerSize.height
        });

        // Only position if coordinates seem valid
        if (point.x > 0 && point.y > 0 && point.x < containerSize.width && point.y < containerSize.height) {
          this.simpleMarker.style.left = `${point.x - 10}px`;
          this.simpleMarker.style.top = `${point.y - 10}px`;
          this.simpleMarker.style.transform = `rotate(${bearing}deg)`;

          console.log(`✅ [${timestamp}] Marker positioned at valid coordinates`);
        } else {
          console.warn(`⚠️ [${timestamp}] Invalid pixel coordinates, keeping marker at current position`);
        }
      } else {
        console.error(`❌ [${timestamp}] Simple marker not found!`);
      }

      // DEBUGGING: Check marker visibility after positioning
      setTimeout(() => {
        const markerElement = this.mapboxMarker?.getElement();
        if (markerElement) {
          const computedStyle = window.getComputedStyle(markerElement);
          const rect = markerElement.getBoundingClientRect();

          console.log(`🔍 [${timestamp}] MARKER VISIBILITY DEBUG:`, {
            element: !!markerElement,
            display: computedStyle.display,
            visibility: computedStyle.visibility,
            opacity: computedStyle.opacity,
            zIndex: computedStyle.zIndex,
            position: computedStyle.position,
            transform: computedStyle.transform,
            width: computedStyle.width,
            height: computedStyle.height,
            boundingRect: {
              x: rect.x,
              y: rect.y,
              width: rect.width,
              height: rect.height,
              visible: rect.width > 0 && rect.height > 0
            },
            parentElement: markerElement.parentElement?.tagName,
            classList: Array.from(markerElement.classList),
            innerHTML: markerElement.innerHTML.substring(0, 100)
          });
        } else {
          console.error(`❌ [${timestamp}] No marker element found!`);
        }
      }, 100);

      console.log(`✅ [${timestamp}] Vehicle marker updated via Mapbox GL:`, {
        position,
        bearing,
        markerExists: !!this.mapboxMarker,
        elementId: this.elementId,
        isVisible: this.isVisible
      });

      this.isVisible = true;
      AnimationDebugTools.log('info', `Vehicle position updated via Mapbox marker: ${JSON.stringify(position)}, bearing: ${bearing}`);

    } catch (error) {
      console.error(`❌ [${timestamp}] Error updating Mapbox marker:`, error);
      // DISABLED: Fallback marker system was causing flashing
      // this.createFallbackMarker(position, bearing);
      console.log(`🔧 [${timestamp}] Fallback marker disabled to prevent flashing`);
    }
  }

  /**
   * Create vehicle element for Mapbox marker
   */
  private createVehicleElement(): HTMLElement {
    const vehicleElement = document.createElement('div');
    vehicleElement.id = this.elementId;
    vehicleElement.className = 'vehicle-marker mapbox-vehicle';

    // Create a more visible vehicle marker
    vehicleElement.innerHTML = `
      <div class="vehicle-icon" style="
        width: 32px;
        height: 32px;
        background: #ff0000;
        border: 3px solid #ffffff;
        border-radius: 50%;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
      ">
        <div style="
          width: 0;
          height: 0;
          border-left: 6px solid transparent;
          border-right: 6px solid transparent;
          border-bottom: 12px solid #ffffff;
          transform: translateY(-2px);
        "></div>
      </div>
    `;

    // Add styles for maximum visibility - ensure display is always block
    vehicleElement.style.cssText = `
      position: relative;
      width: 32px;
      height: 32px;
      cursor: pointer;
      z-index: 99999;
      transition: transform 0.3s ease;
      pointer-events: auto;
      visibility: visible !important;
      opacity: 1 !important;
      display: block !important;
    `;

    console.log(`🚗 Created vehicle element with ID: ${this.elementId}`);
    return vehicleElement;
  }

  /**
   * Clean up any duplicate vehicle markers
   */
  private cleanupDuplicateMarkers(): void {
    const timestamp = new Date().toISOString();

    // Find all potential vehicle marker elements
    const potentialMarkers = [
      'direct-vehicle-marker',
      'travel-vehicle-marker',
      'fallback-vehicle-marker',
      'vehicle-marker'
    ];

    let cleanedCount = 0;

    potentialMarkers.forEach(markerId => {
      const existingMarker = document.getElementById(markerId);
      if (existingMarker && markerId !== this.elementId) {
        console.log(`🧹 [${timestamp}] Removing duplicate marker: ${markerId}`);
        existingMarker.remove();
        cleanedCount++;
      }
    });

    // Also look for any elements with vehicle-related classes
    const vehicleElements = document.querySelectorAll('.vehicle-marker, .mapboxgl-marker');
    vehicleElements.forEach((element, index) => {
      const el = element as HTMLElement;
      // Remove if it's not our current marker and looks like a vehicle marker
      if (el.id !== this.elementId &&
          (el.classList.contains('vehicle-marker') ||
           el.innerHTML.includes('vehicle-icon'))) {
        console.log(`🧹 [${timestamp}] Removing duplicate vehicle element (index ${index}):`, {
          id: el.id,
          className: el.className,
          hasVehicleIcon: el.innerHTML.includes('vehicle-icon')
        });
        el.remove();
        cleanedCount++;
      }
    });

    if (cleanedCount > 0) {
      console.log(`🧹 [${timestamp}] Cleaned up ${cleanedCount} duplicate markers`);
    }
  }

  /**
   * Create a simple fallback marker if Mapbox marker fails
   */
  private createFallbackMarker(position: [number, number], bearing: number): void {
    const timestamp = new Date().toISOString();
    console.log(`🚨 [${timestamp}] Creating fallback marker`);

    try {
      // Remove any existing fallback marker
      const existingFallback = document.getElementById('fallback-vehicle-marker');
      if (existingFallback) {
        existingFallback.remove();
      }

      // Create a simple, highly visible marker
      const fallbackMarker = document.createElement('div');
      fallbackMarker.id = 'fallback-vehicle-marker';
      fallbackMarker.style.cssText = `
        position: absolute;
        width: 40px;
        height: 40px;
        background: #ff0000;
        border: 4px solid #ffffff;
        border-radius: 50%;
        z-index: 999999;
        pointer-events: none;
        box-shadow: 0 0 20px rgba(255, 0, 0, 0.8);
        animation: blink 1s infinite;
      `;

      // Position it using map projection
      const point = this.map!.project(position);
      fallbackMarker.style.left = `${point.x - 20}px`; // Center the 40px marker
      fallbackMarker.style.top = `${point.y - 20}px`;
      fallbackMarker.style.transform = `rotate(${bearing}deg)`;

      // Add to map container
      const mapContainer = this.map!.getContainer();
      mapContainer.appendChild(fallbackMarker);

      console.log(`✅ [${timestamp}] Fallback marker created at pixel position:`, {
        x: point.x,
        y: point.y,
        bearing
      });

    } catch (error) {
      console.error(`❌ [${timestamp}] Fallback marker creation failed:`, error);
    }
  }

  /**
   * Update DOM-based vehicle position (renamed from original method)
   */
  private updateDOMVehiclePosition(pixelPosition: { x: number, y: number }, bearing: number): void {
    const vehicleEl = document.getElementById(this.elementId);
    if (!vehicleEl) {
      console.warn('Vehicle element not found, recreating...');
      this.recreateVehicleMarker(pixelPosition, bearing);
      return;
    }

    // Update position and rotation
    const currentTransform = vehicleEl.style.transform || '';
    const scaleMatch = currentTransform.match(/scale\\(([^)]+)\\)/);
    const currentScale = scaleMatch && scaleMatch[1] ? scaleMatch[1] : '1.0';

    vehicleEl.style.transform = `translate(-50%, -50%) rotate(${bearing}deg) scale(${currentScale})`;
    vehicleEl.style.left = `${pixelPosition.x}px`;
    vehicleEl.style.top = `${pixelPosition.y}px`;
    vehicleEl.style.visibility = 'visible';
    vehicleEl.style.display = 'block';
    vehicleEl.style.opacity = '1';
    vehicleEl.style.zIndex = '9999';
  }

  /**
   * Clean up all vehicle markers and reset state
   */
  public cleanup(): void {
    const timestamp = new Date().toISOString();
    console.log(`🧹 [${timestamp}] Cleaning up VehicleManager`);

    // Clean up our main marker
    if (this.mapboxMarker) {
      this.mapboxMarker.remove();
      this.mapboxMarker = null;
    }

    const vehicleEl = document.getElementById(this.elementId);
    if (vehicleEl) {
      vehicleEl.remove();
    }

    // Clean up any duplicate markers that might have been created
    this.cleanupDuplicateMarkers();

    this.isVisible = false;
    this.lastKnownPosition = null;
    this.lastKnownBearing = 0;

    console.log(`✅ [${timestamp}] VehicleManager cleanup complete`);
  }
}

// Global vehicle marker tracking for debugging
if (typeof window !== 'undefined') {
  if (!window.__vehicleMarkerTracker) {
    window.__vehicleMarkerTracker = {
      instances: new Set(),
      markers: new Map(),
      log: function() {
        console.log('🔍 Global Vehicle Marker Tracker:', {
          instanceCount: this.instances.size,
          markerCount: this.markers.size,
          instances: Array.from(this.instances),
          markers: Array.from(this.markers.entries())
        });
      }
    };
  }
}

export default VehicleManager;