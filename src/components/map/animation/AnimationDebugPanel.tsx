/**
 * AnimationDebugPanel.tsx
 * A debug panel component for testing animation functionality
 */

import React, { useEffect, useState, useCallback, useRef } from 'react';
import AnimationManager from './AnimationManager';
import VehicleManager from './VehicleManager';
import RouteAnimator from './RouteAnimator';
// import * as DebugHelper from './DebugHelper'; // Commented out as it's causing errors
import type { Map as MapboxMap } from 'mapbox-gl';
import './AnimationDebugPanel.css';
import { getMapInstance } from '../utils/MapInstance';
import mapboxgl from 'mapbox-gl';
import { ComponentInteractionManager } from './ComponentInteractionManager';
import AnimationDebugTools from './AnimationDebugTools';
// import { forceVehicleVisibility, isVehicleVisible } from './VehicleController'; // Removed deleted controller import

const DEBUG_MODE = false; // Ensure debug mode is always disabled

// Add proper type definitions
interface VehiclePosition {
  lngLat?: {
    lng: number;
    lat: number;
  };
  pixel?: {
    x: number;
    y: number;
  };
  bearing?: number;
}

interface AnimationDebugPanelProps {
  map: mapboxgl.Map | null;
}

interface AnimationState {
  isAnimating: boolean;
  position: { lng: number; lat: number } | null;
  progress: number;
}

interface DebugState {
  animationActive: boolean;
  progress: number;
  vehicleVisible: boolean;
  position: [number, number] | null;
  bearing: number;
  pointIndex: number;
  frameCount: number;
  errors: string[];
}

/**
 * Debug panel for animation debugging
 * Shows critical animation state information
 */
const AnimationDebugPanel: React.FC<AnimationDebugPanelProps> = ({ map }) => {
  const [state, setState] = useState<DebugState>({
    animationActive: false,
    progress: 0,
    vehicleVisible: false,
    position: null,
    bearing: 0,
    pointIndex: 0,
    frameCount: 0,
    errors: []
  });

  const [expanded, setExpanded] = useState(true);
  const [isStartingAnimation, setIsStartingAnimation] = useState(false);
  
  // Track if panel is fully initialized
  const [initialized, setInitialized] = useState<boolean>(false);
  
  // Use map prop or try to get it from MapInstance
  const mapInstance = map || getMapInstance();
  
  // Helper to add messages to the debug panel's error log
  const addToPanelLog = useCallback((message: string, type: 'info' | 'error' = 'info') => {
    const prefix = type === 'error' ? '❌' : '✅';
    setState(prev => ({
      ...prev,
      errors: [...prev.errors, `${prefix} [${Date.now()}] - ${message}`].slice(-5)
    }));
    if (type === 'error') {
      console.error(`[AnimationDebugPanel] ${message}`);
    } else {
      console.log(`[AnimationDebugPanel] ${message}`);
    }
  }, []);

  // Add initialization check for required props
  useEffect(() => {
    if (!mapInstance) {
      console.warn(`⚠️ [${Date.now()}] AnimationDebugPanel missing required map prop`);
      
      setState(prev => ({
        ...prev,
        errors: [...prev.errors, `${Date.now()} - Missing required map prop`].slice(-5)
      }));
    } else {
      const vehicleManagerInstance = VehicleManager.getInstance();
      if (vehicleManagerInstance && typeof vehicleManagerInstance.setMap === 'function') {
        console.log(`✅ [${Date.now()}] Setting map instance in VehicleManager during initialization`);
        vehicleManagerInstance.setMap(mapInstance);
      }
    }
  }, [mapInstance]);

  useEffect(() => {
    if (mapInstance) {
      const vehicleManagerInstance = VehicleManager.getInstance();
      if (vehicleManagerInstance && typeof vehicleManagerInstance.setMap === 'function') {
        // Check if VehicleManager already has this map instance to avoid unnecessary re-initialization.
        // This requires a getMap() method on VehicleManager, which it doesn't have.
        // For now, we'll proceed with setting it, assuming VehicleManager.setMap is idempotent enough
        // or that mapInstance reference changes are the main concern.
        console.log(`✅ [${Date.now()}] AnimationDebugPanel: Setting map instance in VehicleManager.`);
        vehicleManagerInstance.setMap(mapInstance);
      }
    } else {
      // This is line 96 from the error log if mapInstance becomes null/undefined after being set.
      console.warn(`⚠️ [${Date.now()}] AnimationDebugPanel: mapInstance is not valid in useEffect.`);
      setState(prev => {
        const newError = `${Date.now()} - mapInstance became invalid in useEffect.`;
        if (prev.errors[prev.errors.length - 1] === newError) return prev; // Avoid duplicate error logs
        return {
          ...prev,
          errors: [...prev.errors, newError].slice(-5)
        };
      });
    }
  }, [mapInstance]);

  useEffect(() => {
    let isComponentMounted = true;
    
    const updateDebugState = () => {
      if (!isComponentMounted) return;
      
      const currentMap = getMapInstance(); 

      try {
        let currentManagersInitialized = initialized;
        if (!currentManagersInitialized) {
          const animManager = AnimationManager.getInstance();
          if (animManager && typeof animManager.getAnimationState === 'function') {
             const initialAnimState = animManager.getAnimationState();
             if (initialAnimState) {
                if (isComponentMounted) setInitialized(true);
                currentManagersInitialized = true;
                console.log(`✅ [${Date.now()}] Debug panel connected to AnimationManager`);
             }
          }
        }
        
        if (currentManagersInitialized) {
          const animManager = AnimationManager.getInstance();
          const animStateFromManager = animManager.getAnimationState();
          const currentPositionFromManager = animManager.getCurrentPosition(); // [lng, lat] | null

          const vehicleManagerInstance = VehicleManager.getInstance();
          let vehicleIsVisibleByManager = false;
          if (vehicleManagerInstance && typeof vehicleManagerInstance.isVehicleVisible === 'function') {
            if (currentMap && typeof vehicleManagerInstance.setMap === 'function') {
                // Avoid calling setMap repeatedly here if it's heavy,
                // relies on the other useEffect to manage this.
            }
            vehicleIsVisibleByManager = vehicleManagerInstance.isVehicleVisible();
          } else {
            const vehicleEl = document.getElementById('vehicle-marker') || 
                              document.getElementById('direct-vehicle-marker') ||
                              document.getElementById('emergency-vehicle-marker');
            vehicleIsVisibleByManager = !!vehicleEl;
          }
          
          if (isComponentMounted) {
            setState(prev => {
              const newAnimationActive = animStateFromManager ? animStateFromManager.isAnimating : false;
              const newProgress = animStateFromManager && animStateFromManager.progress !== undefined ? animStateFromManager.progress : 0;
              
              let positionChanged = false;
              if (currentPositionFromManager === null && prev.position !== null) {
                positionChanged = true;
              } else if (currentPositionFromManager !== null && prev.position === null) {
                positionChanged = true;
              } else if (currentPositionFromManager && prev.position && 
                         (currentPositionFromManager[0] !== prev.position[0] || currentPositionFromManager[1] !== prev.position[1])) {
                positionChanged = true;
              }

              if (prev.animationActive === newAnimationActive &&
                  prev.progress === newProgress &&
                  !positionChanged &&
                  prev.vehicleVisible === vehicleIsVisibleByManager) {
                return prev; // No actual change, prevent setState
              }

              return {
                ...prev,
                animationActive: newAnimationActive,
                progress: newProgress,
                position: positionChanged ? currentPositionFromManager : prev.position,
                vehicleVisible: vehicleIsVisibleByManager
              };
            });
          }
        }
      } catch (e: any) { 
        const error = e as Error;
        if (process.env.NODE_ENV === 'development') {
          if (!initialized) { 
              if (Math.random() < 0.1) { 
              console.warn(`Debug panel waiting for animation managers to initialize...`);
            }
          } else {
            console.warn(`Debug panel state update error: ${error.message}`);
          }
        }
        if (isComponentMounted) {
          setState(prev => {
            const newErrorMessage = `${Date.now()} - ${error.message}`;
            // Avoid adding duplicate error messages rapidly
            if (prev.errors.length > 0 && prev.errors[prev.errors.length - 1].endsWith(error.message)) {
              return prev;
            }
            return {
              ...prev,
              errors: [...prev.errors, newErrorMessage].slice(-5)
            };
          });
        }
      }
    };

    const intervalId = setInterval(updateDebugState, 750); // Increased interval slightly
    
    return () => {
      isComponentMounted = false;
      clearInterval(intervalId);
    };
  }, [initialized]); // Add 'initialized' to dependency array, so if it changes, the effect re-evaluates `currentManagersInitialized` correctly.

  // Ensure the panel is never rendered in production
  // if (true) return null; // Effectively disable the debug panel - Commented out to enable

  // The following code will now be unreachable, but kept for potential future debugging needs
  // if (!DEBUG_MODE) return null;

  const toggleExpanded = () => setExpanded(!expanded);

  const forceVehicleVisibilityHandler = () => {
    try {
      console.log(`🔍 [${Date.now()}] Attempting to make existing vehicle visible`);
      
      const vehicleManagerInstance = VehicleManager.getInstance();
      if (!vehicleManagerInstance) {
        const errorMsg = `VehicleManager not available`;
        addToPanelLog(errorMsg, 'error');
        return;
      }
      
      let success = false;
      if (vehicleManagerInstance && typeof vehicleManagerInstance.forceVehicleVisibility === 'function') {
        success = vehicleManagerInstance.forceVehicleVisibility();
      }

      if (success) {
        addToPanelLog('Vehicle made visible successfully');
        setState(prev => ({ ...prev, vehicleVisible: true }));
      } else {
        // Try calling recoverVehicleVisibility on VehicleManager if available (this might be old logic, ensure method exists)
        // Assuming forceVehicleVisibility is the primary method. If it fails, it fails.
        const errorMsg = `Failed to make vehicle visible (forceVehicleVisibility returned false or method not found)`;
        addToPanelLog(errorMsg, 'error');
      }
    } catch (e) {
      const error = e as Error;
      addToPanelLog(`Error in forceVehicleVisibility: ${error.message}`, 'error');
    }
  };

  const showVehiclePosition = () => {
    console.log(`🐞 [${Date.now()}] Showing vehicle position`);
    const vehicleManagerInstance = VehicleManager.getInstance();

    if (!vehicleManagerInstance) {
      addToPanelLog('VehicleManager not available for showVehiclePosition', 'error');
      return;
    }

    // Make sure vehicle is visible first
    const vehicleIsCurrentlyVisible = vehicleManagerInstance.isVehicleVisible ? vehicleManagerInstance.isVehicleVisible() : false;
    if (!vehicleIsCurrentlyVisible) {
      addToPanelLog('Vehicle not visible, attempting to force visibility first.', 'info');
      if (vehicleManagerInstance.forceVehicleVisibility) {
        vehicleManagerInstance.forceVehicleVisibility();
      } else {
        addToPanelLog('forceVehicleVisibility method not found on VehicleManager.', 'error');
        return;
      }
    }
    
    try {
      if (vehicleManagerInstance.isVehicleVisible && vehicleManagerInstance.isVehicleVisible()) {
        const mockPosition: [number, number] = [-8.00833, 31.629499]; // Marrakech coordinates
        addToPanelLog(`Vehicle position (mock): Lng: ${mockPosition[0]}, Lat: ${mockPosition[1]}`);
        setState(prev => ({
          ...prev,
          position: mockPosition,
          bearing: 0
        }));
      } else {
        addToPanelLog('Could not get vehicle position (vehicle not visible after attempt).', 'error');
        setState(prev => ({ ...prev, position: null }));
      }
    } catch (e) {
      const error = e as Error;
      addToPanelLog(`Error getting vehicle position: ${error.message}`, 'error');
    }
  };

  const restartAnimation = () => {
    try {
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('restart-animation'));
        addToPanelLog('Restart animation event dispatched');
      }
    } catch (e) {
      const error = e as Error;
      addToPanelLog(`Error dispatching restart event: ${error.message}`, 'error');
    }
  };
  
  // New function to start the full animation sequence with cinematic transitions
  const startProperAnimation = async () => {
    try {
      if (!mapInstance) {
        const errorMsg = `Map instance not available`;
        addToPanelLog(errorMsg, 'error');
        return;
      }
      
      setIsStartingAnimation(true);
      addToPanelLog('Starting full animation sequence...');
      
      try {
        window.dispatchEvent(new CustomEvent('start-animation-sequence'));
        addToPanelLog('Animation sequence event dispatched');
      } catch (e) {
        const error = e as Error;
        addToPanelLog(`Error dispatching animation event: ${error.message}`, 'error');
      } finally {
        setIsStartingAnimation(false);
      }
    } catch (e) {
      const error = e as Error;
      addToPanelLog(`Error starting animation: ${error.message}`, 'error');
      setIsStartingAnimation(false);
    }
  };
  
  // New function to create a test vehicle in Marrakech
  const createTestVehicleInMarrakech = () => {
    try {
      // DebugHelper.createTestVehicle();
      setState(prev => ({
        ...prev,
        errors: [...prev.errors, `${Date.now()} - Test vehicle created in Marrakech`].slice(-5)
      }));
    } catch (e) {
      const error = e as Error;
      setState(prev => ({
        ...prev,
        errors: [...prev.errors, `${Date.now()} - Error creating vehicle: ${error.message}`].slice(-5)
      }));
    }
  };
  
  // const [debugLog, setDebugLog] = useState<string[]>([]); // This was re-declared, remove. Original is fine.
  // const [vehiclePosition, setVehiclePosition] = useState<Position | null>(null); // Already commented out
  // const [cameraState, setCameraState] = useState<any>(null); // Already commented out

  // useEffect(() => { // Already commented out
  //  DebugHelper.subscribeToVehiclePosition(setVehiclePosition);
  //  DebugHelper.subscribeToCameraState(setCameraState);
  //  return () => {
  //    DebugHelper.unsubscribeFromVehiclePosition(setVehiclePosition);
  //    DebugHelper.unsubscribeFromCameraState(setCameraState);
  //  };
  // }, []);

  // const formatLogData = (data: any): string => { // This was re-declared, remove. Original is fine if it exists.
  //   // ... existing code ...
  // };

  const handleGetAnimationState = () => { // Added dummy function to avoid error if button exists
    const animManager = AnimationManager.getInstance();
    const animState = animManager.getAnimationState();
    const currentPos = animManager.getCurrentPosition();
    addToPanelLog(`Anim State: ${JSON.stringify(animState)}, Pos: ${JSON.stringify(currentPos)}`);
  };

  // Ensure all commented out JSX and problematic comments are fixed
  // The main issue was // ... existing code ... inside handleForceVehicleVisibility function.
  // That has been removed by letting the edit tool replace the whole function.
  // Also, the duplicated useState/useEffect/formatLogData from previous faulty edit will be removed by this targeted edit.

  // Corrected structure for the problematic section, assuming addToLog is now addToPanelLog
  /*
  const handleForceVehicleVisibility = () => {
    const vehicleManager = VehicleManager.getInstance();
    if (vehicleManager && typeof vehicleManager.forceVehicleVisibility === 'function') {
      vehicleManager.forceVehicleVisibility(); 
      addToPanelLog("Attempted to force vehicle visibility via VehicleManager.");
      // The JSX comments below are fine as they are standard JSX comments
      // {* {vehiclePosition && ( ... )} *}
      // {* {cameraState && ( ... )} *}
      // <div className="debug-actions">
      //   <button onClick={handleGetAnimationState}>Get Animation State</button>
      // ... more buttons or content
      // </div>
    } else {
      addToPanelLog("VehicleManager or forceVehicleVisibility not available.", "error");
    }
  };
  */
  // The code below is the return statement for the component, which should now be syntactically correct.

  return (
    <div 
      style={{
        position: 'fixed',
        bottom: expanded ? '10px' : 'auto',
        top: expanded ? 'auto' : '10px',
        right: '10px',
        backgroundColor: 'rgba(0, 0, 0, 0.85)',
        color: '#fff',
        padding: expanded ? '15px' : '5px',
        borderRadius: '5px',
        zIndex: 9999,
        fontSize: '12px',
        fontFamily: 'monospace',
        maxWidth: '400px',
        maxHeight: expanded ? '50vh' : 'auto',
        overflow: 'auto',
        boxShadow: '0 0 10px rgba(0, 0, 0, 0.5)',
        border: '1px solid #444'
      }}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: expanded ? '10px' : '0' }}>
        <button
          onClick={toggleExpanded}
          style={{
            background: 'none',
            border: 'none',
            color: '#fff',
            cursor: 'pointer',
            padding: '0',
            fontSize: '14px'
          }}
        >
          {expanded ? '▼ Collapse' : '▲ Debug Panel'}
        </button>
        
        {expanded && (
          <div>
            <button
              onClick={forceVehicleVisibilityHandler}
              style={{
                background: '#2563eb',
                border: 'none',
                color: '#fff',
                padding: '2px 5px',
                borderRadius: '3px',
                marginRight: '5px',
                cursor: 'pointer',
                fontSize: '10px'
              }}
            >
              Force Vehicle
            </button>
            
            <button
              onClick={showVehiclePosition}
              style={{
                background: '#2563eb',
                border: 'none',
                color: '#fff',
                padding: '2px 5px',
                borderRadius: '3px',
                marginRight: '5px',
                cursor: 'pointer',
                fontSize: '10px'
              }}
            >
              Show Vehicle Position
            </button>
            
            <button
              onClick={restartAnimation}
              style={{
                background: '#dc2626',
                border: 'none',
                color: '#fff',
                padding: '2px 5px',
                borderRadius: '3px',
                cursor: 'pointer',
                fontSize: '10px'
              }}
            >
              Restart Animation
            </button>
            
            <button
              onClick={createTestVehicleInMarrakech}
              style={{
                background: '#10b981',
                border: 'none',
                color: '#fff',
                padding: '2px 5px',
                borderRadius: '3px',
                cursor: 'pointer',
                fontSize: '10px'
              }}
            >
              Create Vehicle
            </button>
          </div>
        )}
      </div>
      
      {expanded && (
        <>
          <div>
            <span style={{ color: state.animationActive ? '#10b981' : '#ef4444' }}>●</span>
            <strong> Animation: </strong>
            <span>{state.animationActive ? 'ACTIVE' : 'INACTIVE'}</span>
            <span style={{ color: '#aaa', marginLeft: '5px' }}>
              {Math.round(state.progress)}%
            </span>
          </div>
          
          <div>
            <span style={{ color: state.vehicleVisible ? '#10b981' : '#ef4444' }}>●</span>
            <strong> Vehicle: </strong>
            <span>{state.vehicleVisible ? 'VISIBLE' : 'HIDDEN'}</span>
          </div>
          
          {state.position && (
            <div style={{ fontSize: '10px', color: '#aaa', marginTop: '5px' }}>
              <div>Position: [{state.position?.[0]?.toFixed(5) || '0.00000'}, {state.position?.[1]?.toFixed(5) || '0.00000'}]</div>
              <div>Bearing: {(state.bearing || 0).toFixed(1)}°</div>
              <div>Point: {state.pointIndex} | Frames: {state.frameCount}</div>
            </div>
          )}
          
          {/* Add new button for starting the full animation sequence */}
          <div style={{ marginTop: '10px' }}>
            <button
              onClick={startProperAnimation}
              disabled={isStartingAnimation}
              style={{
                background: '#10b981',
                border: 'none',
                color: '#fff',
                padding: '8px 12px',
                borderRadius: '3px',
                cursor: isStartingAnimation ? 'default' : 'pointer',
                fontSize: '12px',
                width: '100%',
                opacity: isStartingAnimation ? 0.6 : 1
              }}
            >
              {isStartingAnimation ? 'Starting Animation...' : 'Start Full Animation'}
            </button>
          </div>
          
          {state.errors.length > 0 && (
            <div style={{ marginTop: '10px', borderTop: '1px solid #444', paddingTop: '5px' }}>
              <strong>Recent Events:</strong>
              <ul style={{ margin: '5px 0', padding: '0 0 0 15px', fontSize: '10px' }}>
                {state.errors.map((error, i) => (
                  <li key={i} style={{ marginBottom: '3px', color: '#f87171' }}>
                    {error}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default AnimationDebugPanel;