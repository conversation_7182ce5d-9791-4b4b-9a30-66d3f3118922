/**
 * CORE ANIMATION ENGINE - UNIFIED SYSTEM
 * Replaces: AnimationManager, VehicleManager animation logic, UnifiedAnimationManager
 * Single source of truth for all map animations
 */

import mapboxgl from 'mapbox-gl';
import { trackAnimation, trackRendering, trackData, performanceMonitor } from '@/utils/PerformanceMonitor';
import { CameraController, CameraMode } from '../camera/CameraController';
import { POIDiscoveryManager } from './POIDiscoveryManager';

// Unified coordinate type
export type Coordinate = readonly [longitude: number, latitude: number];

// Animation state
export interface AnimationState {
  isPlaying: boolean;
  isPaused: boolean;
  progress: number;
  currentPosition: Coordinate | null;
  currentBearing: number;
  startTime: number;
  duration: number;
}

// Vehicle renderer interface
export interface VehicleRenderer {
  create(map: mapboxgl.Map): void;
  updatePosition(position: Coordinate, bearing: number): void;
  setVisibility(visible: boolean): void;
  dispose(): void;
}

// Simple, reliable vehicle renderer
class SimpleVehicleRenderer implements VehicleRenderer {
  private element: HTMLElement | null = null;
  private map: mapboxgl.Map | null = null;

  create(map: mapboxgl.Map): void {
    this.map = map;
    
    // Create simple, reliable marker element
    this.element = document.createElement('div');
    this.element.id = 'core-vehicle-marker';
    this.element.style.cssText = `
      position: absolute !important;
      width: 16px !important;
      height: 16px !important;
      background: #FF0000 !important;
      border: 2px solid #FFFFFF !important;
      border-radius: 50% !important;
      z-index: 999999 !important;
      pointer-events: none !important;
      display: block !important;
      visibility: visible !important;
      opacity: 1 !important;
      transform-origin: center center !important;
      box-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
    `;

    // Add to map container
    const container = map.getContainer();
    container.appendChild(this.element);
    
    console.log('✅ [CoreEngine] Simple vehicle renderer created');
  }

  updatePosition(position: Coordinate, bearing: number): void {
    if (!this.element || !this.map) return;

    try {
      // Convert to pixels
      const point = this.map.project(position);
      
      // Update position directly
      this.element.style.left = `${point.x - 8}px`; // Center 16px element
      this.element.style.top = `${point.y - 8}px`;
      this.element.style.transform = `rotate(${bearing}deg)`;
      
    } catch (error) {
      console.warn('[CoreEngine] Position update failed:', error);
    }
  }

  setVisibility(visible: boolean): void {
    if (this.element) {
      this.element.style.display = visible ? 'block' : 'none';
    }
  }

  dispose(): void {
    if (this.element) {
      this.element.remove();
      this.element = null;
    }
    this.map = null;
  }
}

// Processed route interface
interface ProcessedRoute {
  points: Coordinate[];
  totalLength: number;
  segmentLengths: number[];
}

// Route processor with caching
class RouteProcessor {
  private cache = new Map<string, ProcessedRoute>();

  processRoute(points: Coordinate[]): ProcessedRoute {
    return trackData('route-processing', () => {
      const key = this.generateRouteKey(points);

      if (this.cache.has(key)) {
        console.log('✅ [CoreEngine] Using cached route data');
        return this.cache.get(key)!;
      }

      console.log('🔄 [CoreEngine] Processing new route...');
      const processed = this.calculateRouteMetrics(points);
      this.cache.set(key, processed);

      return processed;
    });
  }

  private generateRouteKey(points: Coordinate[]): string {
    // Simple hash of first, middle, and last points
    if (points.length < 2) return 'empty';
    
    const first = points[0];
    const middle = points[Math.floor(points.length / 2)];
    const last = points[points.length - 1];
    
    return `${first[0]},${first[1]}-${middle[0]},${middle[1]}-${last[0]},${last[1]}`;
  }

  private calculateRouteMetrics(points: Coordinate[]): ProcessedRoute {
    const segmentLengths: number[] = [];
    let totalLength = 0;

    for (let i = 0; i < points.length - 1; i++) {
      const distance = this.calculateDistance(points[i], points[i + 1]);
      segmentLengths.push(distance);
      totalLength += distance;
    }

    return {
      points: [...points],
      totalLength,
      segmentLengths
    };
  }

  private calculateDistance(p1: Coordinate, p2: Coordinate): number {
    // Simple Euclidean distance (good enough for route segments)
    const dx = p2[0] - p1[0];
    const dy = p2[1] - p1[1];
    return Math.sqrt(dx * dx + dy * dy);
  }
}

// Main animation engine
export class CoreAnimationEngine {
  private state: AnimationState;
  private vehicleRenderer: VehicleRenderer;
  private routeProcessor: RouteProcessor;
  private animationFrameId: number | null = null;
  private route: ProcessedRoute | null = null;

  // Cinematic and Discovery Systems
  private cameraController: CameraController | null = null;
  private poiDiscoveryManager: POIDiscoveryManager | null = null;
  private lastScenicDiscovery: number = 0; // Timestamp of last scenic discovery
  private lastPOICheck = 0;
  private lastCityCheck = 0;
  private discoveredPOIs = new Set<string>();
  private discoveredCities = new Set<string>();
  
  // Callbacks
  private onProgress?: (progress: number, position: Coordinate, bearing: number) => void;
  private onComplete?: () => void;
  private onError?: (error: Error) => void;

  constructor() {
    this.state = {
      isPlaying: false,
      isPaused: false,
      progress: 0,
      currentPosition: null,
      currentBearing: 0,
      startTime: 0,
      duration: 30000
    };
    
    this.vehicleRenderer = new SimpleVehicleRenderer();
    this.routeProcessor = new RouteProcessor();
    
    console.log('✅ [CoreEngine] Animation engine initialized');
  }

  // Initialize with map
  initialize(map: mapboxgl.Map): void {
    this.vehicleRenderer.create(map);

    // Initialize cinematic systems
    this.cameraController = new CameraController(map);
    this.poiDiscoveryManager = POIDiscoveryManager.getInstance();

    // CRITICAL: Set global map reference for POI discovery overlays
    // POIDiscoveryManager expects map to be available at window.map
    (window as any).map = map;
    console.log('✅ [CoreEngine] Global map reference set for POI discovery');

    if (this.poiDiscoveryManager) {
      // Connect pause/resume events from POI discovery to our animation engine
      this.setupDiscoveryEventListeners();
    }

    console.log('✅ [CoreEngine] Engine initialized with map and cinematic systems');
  }

  // Set POIs for discovery
  setPOIs(pois: any[]): void {
    if (this.poiDiscoveryManager && pois.length > 0) {
      console.log(`🔄 [CoreEngine] Initializing ${pois.length} POIs for discovery...`);
      console.log('🔍 [CoreEngine] Sample POI:', pois[0]);

      // Initialize POI manager with POI data
      this.poiDiscoveryManager.initializePOIs(pois);

      // Enable discovery features
      this.poiDiscoveryManager.configure({
        discoveryPauseEnabled: true,
        discoveryDuration: 8000,
        approachRadius: 3.0,
        discoveryRadius: 2.0,
        maxPOIsToShow: 5
      });

      console.log(`✅ [CoreEngine] ${pois.length} POIs loaded and discovery configured`);
    } else {
      console.warn(`⚠️ [CoreEngine] Cannot set POIs: manager=${!!this.poiDiscoveryManager}, pois=${pois.length}`);
    }
  }

  // Set route for animation
  setRoute(points: Coordinate[]): void {
    if (points.length < 2) {
      throw new Error('Route must have at least 2 points');
    }

    this.route = this.routeProcessor.processRoute(points);
    this.state.currentPosition = this.route.points[0];
    this.state.progress = 0;
    
    console.log(`✅ [CoreEngine] Route set: ${points.length} points, ${this.route.totalLength.toFixed(2)} total length`);
  }

  // Start animation
  startAnimation(duration: number = 30000): void {
    if (!this.route) {
      throw new Error('No route set');
    }

    if (this.state.isPlaying) {
      console.warn('[CoreEngine] Animation already playing');
      return;
    }

    this.state.duration = duration;
    this.state.startTime = performance.now();
    this.state.isPlaying = true;
    this.state.isPaused = false;
    
    this.vehicleRenderer.setVisibility(true);
    this.animateFrame();
    
    console.log('🚀 [CoreEngine] Animation started');
  }

  // Animation frame loop
  private animateFrame = (): void => {
    if (!this.state.isPlaying || !this.route) return;

    // Skip frame if paused
    if (this.state.isPaused) return;

    trackAnimation('animation-frame', () => {
      const now = performance.now();
      const elapsed = now - this.state.startTime;
      this.state.progress = Math.min(elapsed / this.state.duration, 1);

      // Calculate current position
      const position = trackAnimation('position-interpolation', () =>
        this.interpolatePosition(this.state.progress)
      );
      const bearing = trackAnimation('bearing-calculation', () =>
        this.calculateBearing(this.state.progress)
      );

      this.state.currentPosition = position;
      this.state.currentBearing = bearing;

      // Update vehicle
      trackRendering('vehicle-update', () => {
        this.vehicleRenderer.updatePosition(position, bearing);
      });

      // Cinematic camera following
      trackRendering('camera-update', () => {
        this.updateCinematicCamera(position, bearing);
      });

      // POI and City Discovery
      trackAnimation('discovery-check', () => {
        this.checkForDiscoveries(position, now);
      });

      // Notify progress
      if (this.onProgress) {
        this.onProgress(this.state.progress, position, bearing);
      }

      // Continue or complete
      if (this.state.progress >= 1) {
        this.completeAnimation();
      } else {
        this.animationFrameId = requestAnimationFrame(this.animateFrame);
      }
    });
  };

  // Interpolate position along route
  private interpolatePosition(progress: number): Coordinate {
    if (!this.route || this.route.points.length < 2) {
      return this.route?.points[0] || [0, 0];
    }

    const totalDistance = progress * this.route.totalLength;
    let accumulatedDistance = 0;

    for (let i = 0; i < this.route.segmentLengths.length; i++) {
      const segmentLength = this.route.segmentLengths[i];
      
      if (accumulatedDistance + segmentLength >= totalDistance) {
        const segmentProgress = (totalDistance - accumulatedDistance) / segmentLength;
        const p1 = this.route.points[i];
        const p2 = this.route.points[i + 1];
        
        return [
          p1[0] + (p2[0] - p1[0]) * segmentProgress,
          p1[1] + (p2[1] - p1[1]) * segmentProgress
        ] as Coordinate;
      }
      
      accumulatedDistance += segmentLength;
    }

    return this.route.points[this.route.points.length - 1];
  }

  // Calculate bearing
  private calculateBearing(progress: number): number {
    if (!this.route || this.route.points.length < 2) return 0;

    const position = this.interpolatePosition(progress);
    const nextProgress = Math.min(progress + 0.001, 1);
    const nextPosition = this.interpolatePosition(nextProgress);

    const dx = nextPosition[0] - position[0];
    const dy = nextPosition[1] - position[1];
    
    return Math.atan2(dx, dy) * (180 / Math.PI);
  }

  // Complete animation
  private completeAnimation(): void {
    this.state.isPlaying = false;
    this.state.progress = 1;
    
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }

    if (this.onComplete) {
      this.onComplete();
    }

    console.log('✅ [CoreEngine] Animation completed');
  }

  // Pause animation
  pauseAnimation(): void {
    if (!this.state.isPlaying || this.state.isPaused) {
      return; // Already paused or not playing
    }

    this.state.isPaused = true;

    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }

    console.log('⏸️ [CoreEngine] Animation paused');
  }

  // Resume animation
  resumeAnimation(): void {
    if (!this.state.isPlaying || !this.state.isPaused) {
      return; // Not paused or not playing
    }

    this.state.isPaused = false;

    // Adjust start time to account for pause duration
    const now = performance.now();
    const elapsedBeforePause = this.state.progress * this.state.duration;
    this.state.startTime = now - elapsedBeforePause;

    // Resume animation loop
    this.animateFrame();

    console.log('▶️ [CoreEngine] Animation resumed');
  }

  // Stop animation
  stopAnimation(): void {
    this.state.isPlaying = false;
    this.state.isPaused = false;
    
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }

    this.vehicleRenderer.setVisibility(false);
    console.log('⏹️ [CoreEngine] Animation stopped');
  }

  // Set callbacks
  setCallbacks(callbacks: {
    onProgress?: (progress: number, position: Coordinate, bearing: number) => void;
    onComplete?: () => void;
    onError?: (error: Error) => void;
  }): void {
    this.onProgress = callbacks.onProgress;
    this.onComplete = callbacks.onComplete;
    this.onError = callbacks.onError;
  }

  // Get current state
  getState(): AnimationState {
    return { ...this.state };
  }

  // Cinematic camera following (north-facing, no bearing rotation)
  private updateCinematicCamera(position: Coordinate, bearing: number): void {
    if (!this.cameraController) return;

    // Use route travel mode but keep north-facing (bearing = 0)
    this.cameraController.followVehicle(position, 0, CameraMode.ROUTE_TRAVEL);
  }

  // POI and City Discovery
  private checkForDiscoveries(position: Coordinate, now: number): void {
    if (!this.poiDiscoveryManager) {
      console.warn('⚠️ [CoreEngine] POI Discovery Manager not available');
      return;
    }

    // Use POIDiscoveryManager's built-in position update which handles both POI and city discovery
    // This method already includes throttling and proper discovery logic
    try {
      // Debug: Log current position every few seconds
      if (now % 3000 < 100) { // Log every ~3 seconds
        console.log(`📍 [CoreEngine] Current position: [${position[0].toFixed(4)}, ${position[1].toFixed(4)}]`);
        console.log(`🔍 [CoreEngine] POI Manager initialized and ready for discovery`);
      }

      // Update position in POI manager - this triggers all discovery logic
      const nearbyPOIs = this.poiDiscoveryManager.updatePosition(position);

      // Enhanced logging for debugging
      if (nearbyPOIs && nearbyPOIs.length > 0) {
        console.log(`🎯 [CoreEngine] ${nearbyPOIs.length} POIs detected near current position:`,
          nearbyPOIs.map(poi => `${poi.name} (${poi.distance?.toFixed(2)}km)`));
      }

      // Enhanced discovery: Check for scenic locations and interesting road segments
      this.checkForScenicDiscoveries(position);

    } catch (error) {
      console.error('❌ [CoreEngine] Error in discovery check:', error);
      console.error('❌ [CoreEngine] Error details:', error.stack);
    }
  }



  // Set up discovery event listeners to connect POI discovery with our animation engine
  private setupDiscoveryEventListeners(): void {
    // Listen for automatic pause events from POI discovery
    window.addEventListener('automatic-pause', (event: Event) => {
      const customEvent = event as CustomEvent;
      const { reason, pauseDuration, data } = customEvent.detail;

      console.log(`🎯 [CoreEngine] Automatic pause triggered: ${reason} for ${pauseDuration}ms`);

      // Enhanced discovery experience based on reason
      this.handleDiscoveryPause(reason, data, pauseDuration);
    });

    // Listen for manual resume events
    window.addEventListener('discovery-resume', () => {
      console.log('🎯 [CoreEngine] Manual resume from discovery overlay');
      this.resumeAnimation();
    });

    console.log('✅ [CoreEngine] Discovery event listeners set up');
  }

  // Enhanced discovery pause handling with different behaviors for different discovery types
  private handleDiscoveryPause(reason: string, data: any, pauseDuration: number): void {
    // Pause the animation
    this.pauseAnimation();

    // Enhanced camera behavior for different discovery types
    if (reason === 'poi' && data?.poi) {
      this.enhancedPOIDiscovery(data.poi, pauseDuration);
    } else if (reason === 'city' && data?.city) {
      this.enhancedCityApproach(data.city, pauseDuration);
    } else if (reason === 'scenic' && data?.location) {
      this.enhancedScenicStop(data.location, pauseDuration);
    } else {
      // Default behavior
      setTimeout(() => {
        console.log(`🎯 [CoreEngine] Auto-resuming animation after ${reason} discovery`);
        this.resumeAnimation();
      }, pauseDuration || 8000);
    }
  }

  // Enhanced POI discovery with cinematic camera movement
  private enhancedPOIDiscovery(poi: any, pauseDuration: number): void {
    console.log(`🎯 [CoreEngine] Enhanced POI discovery: ${poi.name}`);

    // Cinematic zoom to POI using enhanced camera control
    if (this.cameraController && poi.position) {
      const [lng, lat] = Array.isArray(poi.position) ? poi.position : [poi.position.lng, poi.position.lat];

      // Smooth cinematic zoom to POI
      this.cameraController.smoothTransition({
        center: [lng, lat],
        zoom: 16,
        pitch: 45,
        bearing: 0,
        duration: 2000
      });

      // Return to vehicle after discovery
      setTimeout(() => {
        if (this.currentPosition) {
          // Smooth return to vehicle
          this.cameraController?.smoothTransition({
            center: this.currentPosition,
            zoom: 14,
            pitch: 30,
            bearing: 0,
            duration: 1500
          });
        }

        setTimeout(() => {
          console.log(`🎯 [CoreEngine] Auto-resuming after POI discovery: ${poi.name}`);
          this.resumeAnimation();
        }, 1500);
      }, pauseDuration - 1500);
    } else {
      // Fallback to simple resume
      setTimeout(() => {
        this.resumeAnimation();
      }, pauseDuration);
    }
  }

  // Enhanced city approach with overview camera
  private enhancedCityApproach(city: any, pauseDuration: number): void {
    console.log(`🎯 [CoreEngine] Enhanced city approach: ${city.name}`);

    // Cinematic city overview with enhanced camera control
    if (this.cameraController && city.coordinates) {
      // Elevated city overview
      this.cameraController.smoothTransition({
        center: city.coordinates,
        zoom: 12,
        pitch: 60,
        bearing: 0,
        duration: 3000
      });

      // Return to route after city overview
      setTimeout(() => {
        if (this.currentPosition) {
          // Smooth return to route
          this.cameraController?.smoothTransition({
            center: this.currentPosition,
            zoom: 14,
            pitch: 30,
            bearing: 0,
            duration: 2000
          });
        }

        setTimeout(() => {
          console.log(`🎯 [CoreEngine] Auto-resuming after city approach: ${city.name}`);
          this.resumeAnimation();
        }, 2000);
      }, pauseDuration - 2000);
    } else {
      setTimeout(() => {
        this.resumeAnimation();
      }, pauseDuration);
    }
  }

  // Enhanced scenic stop with panoramic view
  private enhancedScenicStop(location: any, pauseDuration: number): void {
    console.log(`🎯 [CoreEngine] Enhanced scenic stop at: ${location.name || 'scenic location'}`);

    // Panoramic camera rotation for scenic views with enhanced control
    if (this.cameraController && this.currentPosition) {
      let currentBearing = 0;
      const rotationInterval = setInterval(() => {
        currentBearing += 45;
        if (currentBearing >= 360) {
          clearInterval(rotationInterval);
          // Return to forward view and resume
          this.cameraController?.smoothTransition({
            center: this.currentPosition!,
            zoom: 14,
            pitch: 30,
            bearing: 0,
            duration: 1000
          });

          setTimeout(() => {
            console.log(`🎯 [CoreEngine] Auto-resuming after scenic stop`);
            this.resumeAnimation();
          }, 1000);
        } else {
          // Smooth panoramic rotation
          this.cameraController?.smoothTransition({
            center: this.currentPosition!,
            zoom: 15,
            pitch: 45,
            bearing: currentBearing,
            duration: 1000
          });
        }
      }, 1000);
    } else {
      setTimeout(() => {
        this.resumeAnimation();
      }, pauseDuration);
    }
  }

  // Check for scenic discoveries and interesting road segments
  private checkForScenicDiscoveries(position: PositionTuple): void {
    if (!this.routePoints) return;

    const [lng, lat] = position;

    // Check for elevation changes (mountain passes, valleys)
    if (this.currentIndex > 10 && this.currentIndex < this.routePoints.length - 10) {
      const elevationChange = this.calculateElevationChange();
      if (elevationChange > 200) { // Significant elevation change
        this.triggerScenicDiscovery('mountain_pass', {
          name: 'Mountain Pass',
          description: 'Scenic mountain pass with panoramic views',
          coordinates: position,
          elevationChange
        });
      }
    }

    // Check for coastal segments (if near water)
    if (this.isNearCoast(lng, lat)) {
      this.triggerScenicDiscovery('coastal_view', {
        name: 'Coastal Scenic View',
        description: 'Beautiful coastal scenery along the route',
        coordinates: position
      });
    }

    // Check for historical route segments
    if (this.isOnHistoricalRoute(lng, lat)) {
      this.triggerScenicDiscovery('historical_route', {
        name: 'Historical Trade Route',
        description: 'Ancient caravan route with historical significance',
        coordinates: position
      });
    }
  }

  // Calculate elevation change over recent route segment
  private calculateElevationChange(): number {
    // Simplified elevation calculation - in real implementation,
    // this would use elevation data from the route
    return Math.random() * 300; // Mock elevation change
  }

  // Check if current position is near coast
  private isNearCoast(lng: number, lat: number): boolean {
    // Morocco coastal coordinates (simplified)
    const atlanticCoast = lng < -6 && lat > 30 && lat < 36;
    const mediterraneanCoast = lng > -6 && lat > 34.5;
    return atlanticCoast || mediterraneanCoast;
  }

  // Check if current position is on historical route
  private isOnHistoricalRoute(lng: number, lat: number): boolean {
    // Check for major historical trade routes in Morocco
    // Trans-Saharan trade routes, Silk Road connections, etc.
    const isOnCaravanRoute = (lng > -8 && lng < -4) && (lat > 30 && lat < 34);
    return isOnCaravanRoute && Math.random() < 0.1; // 10% chance when on historical route
  }

  // Trigger scenic discovery
  private triggerScenicDiscovery(type: string, location: any): void {
    // Avoid triggering too frequently
    const now = Date.now();
    if (this.lastScenicDiscovery && (now - this.lastScenicDiscovery) < 60000) return; // 1 minute cooldown

    this.lastScenicDiscovery = now;

    console.log(`🌄 [CoreEngine] Scenic discovery triggered: ${type} at ${location.name}`);

    // Emit scenic discovery event
    const scenicEvent = new CustomEvent('automatic-pause', {
      detail: {
        reason: 'scenic',
        data: { location },
        pauseDuration: 12000 // 12 seconds for scenic stops
      }
    });

    if (typeof window !== 'undefined') {
      window.dispatchEvent(scenicEvent);
    }
  }

  // Get performance summary
  getPerformanceSummary(): void {
    performanceMonitor.logSummary();
  }

  // Dispose
  dispose(): void {
    this.stopAnimation();
    this.vehicleRenderer.dispose();
    this.routeProcessor.cache.clear();

    // Log final performance summary
    console.log('📊 [CoreEngine] Final performance summary:');
    this.getPerformanceSummary();

    console.log('🗑️ [CoreEngine] Engine disposed');
  }
}
