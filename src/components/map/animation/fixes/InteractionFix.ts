/**
 * InteractionFix.ts
 * 
 * Fixes issues with click events on city markers and map controls
 * by ensuring proper event propagation and pointer-events settings.
 * 
 * This class addresses common interaction problems with map elements:
 * - Marker click events not registering
 * - Map controls being unresponsive
 * - Event propagation issues with overlapping elements
 * - Z-index conflicts between markers and controls
 */

import { ComponentInteractionManager } from '../ComponentInteractionManager';

export class InteractionFix {
  private static instance: InteractionFix | null = null;
  private interactionManager: ComponentInteractionManager;
  
  private constructor() {
    this.interactionManager = ComponentInteractionManager.getInstance();
    // console.log('🔧 InteractionFix initialized'); // Commented out
  }
  
  /**
   * Get singleton instance
   */
  public static getInstance(): InteractionFix {
    if (!this.instance) {
      this.instance = new InteractionFix();
    }
    return this.instance;
  }
  
  /**
   * Apply fixes to ensure city markers and buttons are clickable
   * This is the main method that should be called to fix all interaction issues
   */
  public applyFixes(): void {
    this.fixMarkerClickEvents();
    this.fixButtonClickEvents();
    this.fixEventPropagation();
    this.fixMapOverlays();
    this.fixClusterMarkers();
    // console.log('✅ Applied interaction fixes for map elements'); // Commented out to reduce noise
  }
  
  /**
   * Fix marker click events by ensuring proper event handling
   * Addresses issues with city markers, POI markers, and other map elements
   */
  private fixMarkerClickEvents(): void {
    // Debounced event handler to prevent duplicated POI click events
    const debounce = (fn: Function, delay: number) => {
      let timeoutId: ReturnType<typeof setTimeout> | null = null;
      return (...args: any[]) => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        timeoutId = setTimeout(() => {
          fn(...args);
          timeoutId = null;
        }, delay);
      };
    };
    
    // Create debounced POI click handler
    const debouncedPoiClickHandler = debounce((event: any) => {
      // console.log('POI marker clicked (debounced):', event); // Commented out
      this.interactionManager.emit('poi-clicked', event);
    }, 300); // 300ms debounce

    // Ensure event listeners are properly registered
    this.interactionManager.addEventListener('city-clicked', (event) => {
      // console.log('City marker clicked:', event); // Commented out
      // Re-emit the event to ensure it's captured
      this.interactionManager.emit('city-clicked', event);
    });
    
    // Register POI click events with debouncing
    this.interactionManager.addEventListener('poi-clicked', (event) => {
      // Use debounced handler to prevent multiple rapid fires
      debouncedPoiClickHandler(event);
    });
    
    // Fix marker elements in the DOM
    setTimeout(() => {
      // Track markers we've already attached listeners to
      const processedMarkers = new Set<Element>();
      
      // Target all marker types
      const allMarkers = document.querySelectorAll(
        '.city-marker, .dom-poi-marker, .poi-marker, .destination-marker, ' +
        '.discovered-poi-marker, .mapboxgl-marker, .marker-container'
      );
      
      allMarkers.forEach(marker => {
        // Skip if we've already processed this marker
        if (processedMarkers.has(marker)) {
          return;
        }
        
        if (marker instanceof HTMLElement) {
          marker.style.pointerEvents = 'auto';
          marker.style.zIndex = '100';
          marker.style.cursor = 'pointer';
          
          // Remove any existing click listeners to prevent duplicates
          const oldElement = marker.cloneNode(true);
          if (marker.parentNode) {
            marker.parentNode.replaceChild(oldElement, marker);
          }
          
          // Add a single click event listener to the new element
          if (oldElement instanceof HTMLElement) {
            oldElement.addEventListener('click', (e) => {
              // console.log('Direct marker click:', oldElement.className); // Commented out
              // Prevent the event from being stopped
              e.stopPropagation();
              
              // Emit appropriate event based on marker type
              if (oldElement.classList.contains('city-marker')) {
                this.interactionManager.emit('city-clicked', { element: oldElement, originalEvent: e });
              } else if (oldElement.classList.contains('poi-marker') || oldElement.classList.contains('dom-poi-marker')) {
                // Use the debounced handler for POI clicks
                debouncedPoiClickHandler({ element: oldElement, originalEvent: e });
              }
            }, { once: true }); // Use once:true to ensure the event only fires once
            
            // Mark as processed
            processedMarkers.add(oldElement);
          }
        }
      });
      
      // Fix marker dots and inner elements
      const markerDots = document.querySelectorAll('.destination-dot, .poi-marker-dot');
      markerDots.forEach(dot => {
        if (dot instanceof HTMLElement) {
          dot.style.pointerEvents = 'auto';
        }
      });
    }, 500); // Delay to ensure DOM is ready
  }
  
  /**
   * Fix button click events
   * Ensures all map control buttons are clickable and properly styled
   */
  private fixButtonClickEvents(): void {
    setTimeout(() => {
      // Target all control buttons and interactive elements
      const buttons = document.querySelectorAll(
        '.map-controls button, .map-controls .control-button, ' +
        '.animation-controls button, .travel-controls button, ' +
        '.notification-panel button, .marker-expand-toggle, ' +
        '.poi-view-button, .more-info-btn'
      );
      
      buttons.forEach(button => {
        if (button instanceof HTMLElement) {
          button.style.pointerEvents = 'auto';
          button.style.zIndex = '800';
          button.style.position = 'relative';
          button.style.cursor = 'pointer';
          
          // REMOVE the global click event handler to allow React to handle clicks
          // button.addEventListener('click', (e) => {
          //   console.log('Control button clicked:', button.className);
          //   // Prevent default to ensure the event is handled
          //   e.stopPropagation();
          // });
        }
      });
      
      // Fix control containers
      const controlContainers = document.querySelectorAll(
        '.map-controls, .animation-controls, .travel-controls'
      );
      
      controlContainers.forEach(container => {
        if (container instanceof HTMLElement) {
          container.style.pointerEvents = 'auto';
          container.style.zIndex = '500';
        }
      });
    }, 500); // Delay to ensure DOM is ready
  }
  
  /**
   * Fix event propagation issues
   * Addresses problems with container elements blocking events
   */
  private fixEventPropagation(): void {
    // Fix container elements that might be blocking events
    setTimeout(() => {
      const containers = [
        document.getElementById('poi-markers-container'),
        document.getElementById('destination-markers-container'),
        document.getElementById('map-markers-container'),
        document.getElementById('cluster-markers-container')
      ];
      
      containers.forEach(container => {
        if (container) {
          // CRITICAL FIX: Do not override pointerEvents for poi-markers-container
          // as it's intentionally set to 'none' by DirectPOIMarkers.tsx
          if (container.id !== 'poi-markers-container') {
            container.style.pointerEvents = 'auto';
          }
          // console.log(`Fixed container: ${container.id}${container.id === 'poi-markers-container' ? ' (pointerEvents intentionally skipped)' : ''}`); // Commented out
        }
      });
      
      // Fix any animation overlays that might be blocking clicks
      const overlays = document.querySelectorAll('.animation-overlay, .map-overlay');
      overlays.forEach(overlay => {
        if (overlay instanceof HTMLElement) {
          overlay.style.pointerEvents = 'none';
          // console.log(`Fixed overlay: ${overlay.className}`); // Commented out
        }
      });
      
      // Fix marker hover cards to ensure they don't block clicks
      const hoverCards = document.querySelectorAll('.marker-hover-card, .cluster-hover-card');
      hoverCards.forEach(card => {
        if (card instanceof HTMLElement) {
          // Only make hover cards clickable when visible
          if (card.classList.contains('visible') || card.classList.contains('expanded')) {
            card.style.pointerEvents = 'auto';
          } else {
            card.style.pointerEvents = 'none';
          }
        }
      });
    }, 500);
  }
  /**
   * Fix map overlays that might be blocking interaction
   */
  private fixMapOverlays(): void {
    setTimeout(() => {
      // Fix popup containers
      const popups = document.querySelectorAll('.mapboxgl-popup-content');
      popups.forEach(popup => {
        if (popup instanceof HTMLElement) {
          popup.style.pointerEvents = 'auto';
          popup.style.zIndex = '500';
        }
      });
      
      // Fix notification panels and POI lists
      const panels = document.querySelectorAll(
        '.notification-panel, .poi-list-container, .poi-slideshow'
      );
      panels.forEach(panel => {
        if (panel instanceof HTMLElement) {
          // Only make panels clickable when visible
          if (panel.classList.contains('visible') || panel.style.display !== 'none') {
            panel.style.pointerEvents = 'auto';
          }
        }
      });
    }, 600);
  }
  
  /**
   * Fix cluster markers which may have special handling
   */
  private fixClusterMarkers(): void {
    setTimeout(() => {
      const clusterMarkers = document.querySelectorAll(
        '.cluster-marker, .poi-cluster-marker, .marker-cluster'
      );
      
      clusterMarkers.forEach(marker => {
        if (marker instanceof HTMLElement) {
          marker.style.pointerEvents = 'auto';
          marker.style.zIndex = '100';
          marker.style.cursor = 'pointer';
          
          // Add click handler for clusters
          marker.addEventListener('click', (e) => {
            // console.log('Cluster marker clicked:', marker.className); // Commented out
            e.stopPropagation();
            this.interactionManager.emit('cluster-clicked', { element: marker, originalEvent: e });
          });
        }
      });
    }, 700);
  }

}

// Auto-initialize when imported in a browser environment
if (typeof window !== 'undefined') {
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      InteractionFix.getInstance().applyFixes();
      
      // Also apply fixes after map style loads, which often recreates markers
      window.addEventListener('mapbox.styleloaded', () => {
        setTimeout(() => {
          // console.log('Map style loaded, reapplying interaction fixes'); // Commented out
          InteractionFix.getInstance().applyFixes();
        }, 1000);
      });
      
      // Periodically check and fix interactions for dynamically added elements
      // setInterval(() => { // Commented out
      //   InteractionFix.getInstance().applyFixes();
      // }, 5000); // Check every 5 seconds
    });
  } else {
    InteractionFix.getInstance().applyFixes();
    
    // Also apply fixes after map style loads
    window.addEventListener('mapbox.styleloaded', () => {
      setTimeout(() => {
        // console.log('Map style loaded, reapplying interaction fixes'); // Commented out
        InteractionFix.getInstance().applyFixes();
      }, 1000);
    });
    
    // Periodically check and fix interactions for dynamically added elements
    // setInterval(() => { // Commented out
    //   InteractionFix.getInstance().applyFixes();
    // }, 5000); // Check every 5 seconds
  }
}