/**
 * Consolidated type definitions for the animation system.
 * This file contains all types, interfaces, and enums used across the animation components.
 */

import { Map, LngLatBounds, CameraOptions } from 'mapbox-gl';
import { PointOfInterest } from '@/types/poi';
import { Destination } from '@/types/destination';
import { AnimationEventType, AnimationEventCallback } from '../../../../types/AnimationEventTypes';
import type { LogLevel } from '../../../../types/LogLevel';
import { Position as GlobalPositionType, PositionTuple, toPositionTuple } from '../../../../types/Position';

// -------------------------------------------------------------
// Basic Types
// -------------------------------------------------------------

/**
 * Represents a geographical position as a longitude-latitude pair for use within the animation system.
 * longitude: number, latitude: number
 */
export type Position = [number, number];

/**
 * Represents a 2D point on the screen, typically in pixels.
 * x: number, y: number
 */
export type ScreenPoint = {
  x: number;
  y: number;
};

export interface LngLatPosition {
  lng: number;
  lat: number;
}

export interface LatLngPosition {
  lat: number;
  lng: number;
}

export interface PixelPosition {
  x: number;
  y: number;
}

// Type guards and assertions
export const isLngLatPosition = (pos: any): pos is LngLatPosition => {
  return typeof pos === 'object' && 'lng' in pos && 'lat' in pos;
};

export const isLatLngPosition = (pos: any): pos is LatLngPosition => {
  return typeof pos === 'object' && 'lat' in pos && 'lng' in pos;
};

export const isPixelPosition = (pos: any): pos is PixelPosition => {
  return typeof pos === 'object' && 'x' in pos && 'y' in pos;
};

export const isPosition = (pos: any): pos is Position => {
  return Array.isArray(pos) && pos.length === 2 && 
    typeof pos[0] === 'number' && typeof pos[1] === 'number';
};

export type Timestamp = number;
export type AnimationCallback = (progress: number) => void;

// -------------------------------------------------------------
// Animation Types
// -------------------------------------------------------------

export enum AnimationStateType {
  IDLE = 'idle',
  PLAYING = 'playing',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  ERROR = 'error'
}

export interface AnimationState {
  isAnimating: boolean;
  progress: number;
  currentPoint: Position | null;
  lastPoint: Position | null;
  bearing: number;
  pointIndex: number;
  elapsedTime: number;
  isPaused: boolean;
  terrain: string;
  inCity: boolean;
}

export interface AnimationFrameConfig {
  onFrame: (deltaTime: number, timestamp: number) => void;
  onComplete?: () => void;
  onError?: (error: Error) => void;
  targetDelay?: number;
  duration?: number;
  shouldContinue?: () => boolean;
}

export interface AnimationMetrics {
  fps: number;
  averageDeltaTime: number;
  frameCount: number;
  startTime: number;
  stalledCount: number;
  isHealthy: boolean;
}

export interface AnimationConfig {
  routePoints: Position[];
  duration?: number;
  onProgress?: (progress: number, position: Position, bearing: number, cameraSettings?: CameraSettings) => void;
  onComplete?: () => void;
  onError?: (error: Error) => void;
}

export interface AnimationOptions extends AnimationConfig {
  camera: CameraSettings;
  debug?: DebugConfig;
}

export interface AnimationIntegrationOptions extends AnimationConfig {
  map: Map;
  initialPosition?: Position;
  initialBearing?: number;
  usePerformanceMonitoring?: boolean;
  showDebugPanel?: boolean;
  useSmoothVehicle?: boolean;
  useEnhancedCamera?: boolean;
}

// -------------------------------------------------------------
// Camera Types
// -------------------------------------------------------------

export interface CameraSettings {
  center: Position;
  zoom: number;
  pitch: number;
  bearing: number;
  speed?: number;
  essential?: boolean;
  duration?: number;
}

export interface MapTransitionOptions {
  center: Position;
  zoom: number;
  bearing: number;
  pitch: number;
  duration: number;
  essential: boolean;
  easing?: (t: number) => number;
}

export interface ExtendedCameraOptions extends CameraOptions {
  easeId?: string;
  noMoveStart?: boolean;
  duration?: number;
  essential?: boolean;
  onComplete?: () => void;
}

// -------------------------------------------------------------
// Vehicle Types
// -------------------------------------------------------------

export interface VehicleStyleOptions {
  color: string;
  size: number;
  opacity: number;
  outline?: boolean;
  outlineColor?: string;
  shadow?: boolean;
  shadowColor?: string;
}

export interface VehicleConfig {
  type: 'car' | 'truck' | 'bike' | 'custom';
  style: VehicleStyleOptions;
  initialPosition?: Position;
  initialBearing?: number;
  useSmoothing?: boolean;
}

export interface VehicleDebugInfo {
  position: Position;
  bearing: number;
  speed: number;
  terrain: string;
  smoothingEnabled: boolean;
}

// -------------------------------------------------------------
// Debug Types
// -------------------------------------------------------------

export type AnimationContext = 'vehicle' | 'camera' | 'route' | 'poi' | 'ui' | 'animation' | 'state' | 'marker';

export interface LogEntry {
  timestamp: number;
  level: LogLevel;
  message: string;
  context?: AnimationContext;
  data?: any;
}

export interface DebugConfig {
  enabled: boolean;
  maxLogEntries: number;
  consoleOutput: boolean;
  performanceMonitoring: boolean;
  highlightVehicle: boolean;
}

export interface CinematicControllerDebugOptions {
  fixedCamera: boolean | { 
    active: boolean; 
    center: Position | null; 
    zoom: number; 
    pitch: number; 
    bearing: number; 
  };
  extremeSmoothing: boolean;
  disableTerrainTransitions: boolean;
  alternateUpdateMethod: boolean;
  forceBearingNorth: boolean;
  lastSuccessfulUpdate: number;
  skipFrameCount: number;
  maxSkipFrames: number;
}

// -------------------------------------------------------------
// Map Feature Types
// -------------------------------------------------------------

export interface MapFeatureOptions {
  weatherIndicators: boolean;
  routeLabels: boolean;
  movingVehicle: boolean;
  terrainView: boolean;
  poiSlideshow: boolean;
}

export interface MapboxOptions {
  initialCenter?: Position;
  initialZoom?: number;
  minZoom?: number;
  maxZoom?: number;
  style?: string;
  containerStyle?: React.CSSProperties;
  containerClassName?: string;
  interactive?: boolean;
  onMapLoad?: (map: Map) => void;
  onMapClick?: (e: mapboxgl.MapMouseEvent) => void;
  onMapMove?: (e: mapboxgl.MapMouseEvent) => void;
}

// -------------------------------------------------------------
// Cultural Region Types
// -------------------------------------------------------------

export interface CulturalRegion {
  name: string;
  type: 'mountain' | 'desert' | 'city' | 'default';
  bounds: [Position, Position];
  cameraSettings?: Partial<CameraSettings>;
}

// -------------------------------------------------------------
// Event Types
// -------------------------------------------------------------

export type AnimationUpdateCallback = (state: AnimationState) => void;
export type AnimationErrorCallback = (error: Error) => void;

export interface AnimationDebugInfo {
  state: AnimationStateType;
  progress: number;
  elapsedTime: number;
  frameCount: number;
  fps: number;
}

// -------------------------------------------------------------
// Constants
// -------------------------------------------------------------

export const ANIMATION_CONSTANTS = {
  FRAME_RATE_TARGET: 60,
  MIN_FRAME_DELAY: 16.67,
  OPTIMAL_ANIMATION_DELAY: 100,
  MAX_STALLED_FRAMES: 10,
  BASE_ANIMATION_DURATION_MS: 60000,
  ANIMATION_SPEEDS: {
    CITY: 0.5,
    ROUTE: 1.0,
    SCENIC: 0.3,
    MOUNTAIN: 0.6,
    DESERT: 1.2
  }
} as const;

// -------------------------------------------------------------
// Conversion utilities
// -------------------------------------------------------------

export const positionToLngLat = (pos: Position): LngLatPosition => ({
  lng: pos[0],
  lat: pos[1]
});

export const positionToLatLng = (pos: Position): LatLngPosition => {
  const [lng, lat] = toPositionTuple(pos);
  return { lat, lng };
};

export const lngLatToPosition = (pos: LngLatPosition): PositionTuple => [
  pos.lng,
  pos.lat
];

export const latLngToPosition = (pos: LatLngPosition): PositionTuple => [
  pos.lng,
  pos.lat
];

// Position helper functions
export const calculateDistance = (pos1: Position, pos2: Position): number => {
  const [lng1, lat1] = pos1;
  const [lng2, lat2] = pos2;
  
  // Convert to radians
  const lat1Rad = (lat1 * Math.PI) / 180;
  const lat2Rad = (lat2 * Math.PI) / 180;
  const lng1Rad = (lng1 * Math.PI) / 180;
  const lng2Rad = (lng2 * Math.PI) / 180;

  // Haversine formula
  const dLat = lat2Rad - lat1Rad;
  const dLng = lng2Rad - lng1Rad;
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1Rad) * Math.cos(lat2Rad) * 
    Math.sin(dLng / 2) * Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  
  // Earth's radius in kilometers
  const R = 6371;
  return R * c;
};

export const interpolatePosition = (pos1: Position, pos2: Position, fraction: number): Position => {
  const [lng1, lat1] = pos1;
  const [lng2, lat2] = pos2;
  
  return [
    lng1 + (lng2 - lng1) * fraction,
    lat1 + (lat2 - lat1) * fraction
  ];
};

export const isValidPosition = (pos: Position): boolean => {
  const [lng, lat] = toPositionTuple(pos);
  return (
    typeof lng === 'number' && 
    typeof lat === 'number' &&
    !isNaN(lng) && 
    !isNaN(lat) &&
    lng >= -180 && 
    lng <= 180 &&
    lat >= -90 && 
    lat <= 90
  );
};

// -------------------------------------------------------------
// Export all types
// -------------------------------------------------------------

export default {
  AnimationStateType,
  AnimationEventType
};

// Interface for the position during animation
export interface PositionInfo {
  currentPoint: PositionTuple;
  nextPoint: PositionTuple | null;
  pointIndex: number;
} 