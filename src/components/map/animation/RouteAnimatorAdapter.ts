/**
 * RouteAnimatorAdapter.ts
 * 
 * This adapter bridges the gap between the simple [number, number] array implementation
 * in RouteAnimator.ts and the detailed RoutePoint interface expected by the
 * RouteAnimatorInterface.
 */

import { Position } from '@/types/Position';
import { TerrainType } from '@/types/TerrainType';
import { RoutePoint } from '@/types/RoutePoint';
import { 
  createRoutePointFromPosition,
  convertSimpleToDetailedRoutePoint 
} from '@/types/RoutePointAdapter';
import * as turf from '@turf/turf';

/**
 * Finds the nearest route point to the given position
 */
export function getNearestRoutePoint(
  position: Position,
  routePoints: Position[]
): RoutePoint {
  if (!routePoints || routePoints.length === 0) {
    return createRoutePointFromPosition(position, 0);
  }
  
  // Find the closest point in the route
  let closestIndex = 0;
  let minDistance = Infinity;
  
  for (let i = 0; i < routePoints.length; i++) {
    const distance = turf.distance(
      turf.point(position),
      turf.point(routePoints[i]),
      { units: 'kilometers' }
    );
    
    if (distance < minDistance) {
      minDistance = distance;
      closestIndex = i;
    }
  }
  
  // Calculate bearing between this point and next point if available
  let bearing = 0;
  if (closestIndex < routePoints.length - 1) {
    bearing = calculateBearing(
      routePoints[closestIndex],
      routePoints[closestIndex + 1]
    );
  }
  
  // Determine terrain based on position 
  // This is a simplified implementation - in a real app, would use map data
  const terrain = determineTerrainType(position);
  
  // Determine distance along route (simplified estimate)
  let distance = 0;
  for (let i = 0; i < closestIndex; i++) {
    distance += turf.distance(
      turf.point(routePoints[i]),
      turf.point(routePoints[i + 1]),
      { units: 'meters' }
    );
  }
  
  return {
    position: routePoints[closestIndex],
    index: closestIndex,
    bearing,
    distance,
    terrain,
    // Additional fields could be added as needed
    elevation: undefined,
    nearestLocation: undefined,
    pois: undefined,
    metadata: undefined
  };
}

/**
 * Get the route point at a specific progress value (0-1)
 */
export function getRoutePointAtProgress(
  progress: number,
  routePoints: Position[]
): RoutePoint {
  if (!routePoints || routePoints.length === 0) {
    // Default to [0,0] if no route points available
    return createRoutePointFromPosition([0, 0], 0);
  }
  
  // Clamp progress to 0-1 range
  const clampedProgress = Math.max(0, Math.min(1, progress));
  
  // Calculate point index based on progress
  const pointIndex = Math.min(
    Math.floor(clampedProgress * routePoints.length),
    routePoints.length - 1
  );
  
  // Calculate bearing from current to next point
  let bearing = 0;
  if (pointIndex < routePoints.length - 1) {
    bearing = calculateBearing(
      routePoints[pointIndex],
      routePoints[pointIndex + 1]
    );
  }
  
  // Calculate distance along route to this point
  let distance = 0;
  for (let i = 0; i < pointIndex; i++) {
    distance += turf.distance(
      turf.point(routePoints[i]),
      turf.point(routePoints[i + 1]),
      { units: 'meters' }
    );
  }
  
  // Determine terrain based on position
  const terrain = determineTerrainType(routePoints[pointIndex]);
  
  return {
    position: routePoints[pointIndex],
    index: pointIndex,
    bearing,
    distance,
    terrain,
    // Additional fields
    elevation: undefined,
    nearestLocation: undefined,
    pois: undefined,
    metadata: {
      progress: clampedProgress
    }
  };
}

/**
 * Interpolate between two route points with a factor (0-1)
 */
export function interpolateRoutePoints(
  pointA: RoutePoint,
  pointB: RoutePoint,
  factor: number
): RoutePoint {
  // Clamp factor to 0-1 range
  const clampedFactor = Math.max(0, Math.min(1, factor));
  
  // Interpolate position
  const interpolatedPosition: Position = [
    pointA.position[0] + (pointB.position[0] - pointA.position[0]) * clampedFactor,
    pointA.position[1] + (pointB.position[1] - pointA.position[1]) * clampedFactor
  ];
  
  // Interpolate bearing (simple linear interpolation)
  // In a real application, might need to handle circular interpolation correctly
  const bearing = pointA.bearing + (pointB.bearing - pointA.bearing) * clampedFactor;
  
  // Interpolate distance along route
  const distance = pointA.distance + (pointB.distance - pointA.distance) * clampedFactor;
  
  // For terrain, use the terrain at the point we're closer to
  const terrain = clampedFactor < 0.5 ? pointA.terrain : pointB.terrain;
  
  // Determine index (can be fractional to represent in-between state)
  const index = pointA.index + (pointB.index - pointA.index) * clampedFactor;
  
  return {
    position: interpolatedPosition,
    bearing,
    distance,
    terrain,
    index: Math.floor(index), // Index should be an integer
    // Additional fields
    elevation: undefined,
    nearestLocation: undefined,
    pois: undefined,
    metadata: {
      interpolationFactor: clampedFactor,
      exactIndex: index
    }
  };
}

/**
 * Determine terrain type for a given position
 * This is a simplified implementation for demonstration
 * In a real application, would use map data or external service
 */
export function determineTerrainType(position: Position): TerrainType {
  // For demonstration, implement simple terrain determination based on position
  // Example - assigning terrain based on longitude ranges
  const longitude = position[0];
  const latitude = position[1];
  
  // Coastal along most of Morocco's western edge
  if (longitude < -7 && longitude > -10) {
    return 'coastal';
  }
  
  // Mountains in the Atlas range
  if (latitude > 30 && latitude < 33 && longitude > -8 && longitude < -5) {
    return 'mountain';
  }
  
  // Desert in the south and east
  if ((latitude < 30 && longitude > -10) || (latitude > 30 && longitude > -4)) {
    return 'desert';
  }
  
  // Cities (simplified for demonstration)
  // Checking proximity to major cities
  const cities = [
    { name: "Marrakech", position: [-8.00833, 31.629499], radius: 0.2 },
    { name: "Casablanca", position: [-7.58333, 33.533329], radius: 0.3 },
    { name: "Rabat", position: [-6.8499999, 34.033329], radius: 0.2 },
    { name: "Fes", position: [-5.00028, 34.033329], radius: 0.2 },
    { name: "Tangier", position: [-5.8132, 35.7876], radius: 0.2 }
  ];
  
  for (const city of cities) {
    const distance = turf.distance(
      turf.point(position),
      turf.point(city.position),
      { units: 'kilometers' }
    );
    
    if (distance < city.radius * 20) { // 20km is arbitrary city "urban area"
      return 'urban';
    }
  }
  
  // Default fallback
  return 'standard';
}

/**
 * Calculate bearing between two positions in degrees
 */
export function calculateBearing(start: Position, end: Position): number {
  const startPoint = turf.point(start);
  const endPoint = turf.point(end);
  return turf.bearing(startPoint, endPoint);
} 