import { useRef, useEffect } from 'react';

/**
 * Custom hook for robust animation frame scheduling
 * Includes fallbacks and error handling
 */
export function useAnimationFrame(callback: (deltaTime: number) => void, deps: any[] = []) {
  const requestRef = useRef<number | null>(null);
  const previousTimeRef = useRef<number | null>(null);
  const callbackRef = useRef(callback);
  const errorCountRef = useRef(0);
  const activeRef = useRef(true);
  
  // Update callback ref when callback changes
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);
  
  useEffect(() => {
    console.log(`🔄 [${Date.now()}] Animation frame hook initialized`);
    
    const animate = (time: number) => {
      if (!activeRef.current) return;
      
      try {
        if (previousTimeRef.current !== null) {
          const deltaTime = time - previousTimeRef.current;
          callbackRef.current(deltaTime);
        } else {
          console.log(`⏱️ [${Date.now()}] Animation frame first tick`);
        }
        
        previousTimeRef.current = time;
        errorCountRef.current = 0; // Reset error count on successful frame
        
        // Schedule next frame
        if (window.requestAnimationFrame) {
          requestRef.current = window.requestAnimationFrame(animate);
        } else {
          // Fallback to setTimeout if requestAnimationFrame is not available
          requestRef.current = window.setTimeout(() => animate(performance.now()), 16) as unknown as number;
        }
      } catch (error) {
        console.error(`❌ [${Date.now()}] Error in animation frame:`, error);
        
        // Increment error count and try to recover
        errorCountRef.current += 1;
        
        if (errorCountRef.current < 5) {
          console.warn(`⚠️ [${Date.now()}] Trying to recover from animation error (${errorCountRef.current}/5)`);
          
          // Schedule next frame despite error
          if (window.requestAnimationFrame) {
            requestRef.current = window.requestAnimationFrame(animate);
          } else {
            requestRef.current = window.setTimeout(() => animate(performance.now()), 16) as unknown as number;
          }
        } else {
          // Too many errors, stop animation
          console.error(`🛑 [${Date.now()}] Too many animation errors, stopping animation frame hook`);
          activeRef.current = false;
        }
      }
    };
    
    // Start the animation
    if (window.requestAnimationFrame) {
      requestRef.current = window.requestAnimationFrame(animate);
    } else {
      console.warn(`⚠️ [${Date.now()}] requestAnimationFrame not available, using setTimeout fallback`);
      requestRef.current = window.setTimeout(() => animate(performance.now()), 16) as unknown as number;
    }
    
    return () => {
      activeRef.current = false;
      
      // Cancel animation
      if (requestRef.current !== null) {
        if (window.cancelAnimationFrame) {
          window.cancelAnimationFrame(requestRef.current);
        } else {
          window.clearTimeout(requestRef.current);
        }
        console.log(`🛑 [${Date.now()}] Animation frame hook cleaned up`);
      }
    };
  }, deps);
  
  // Return functions to manually control the animation
  return {
    // Pause animation
    pause: () => {
      activeRef.current = false;
      if (requestRef.current !== null) {
        if (window.cancelAnimationFrame) {
          window.cancelAnimationFrame(requestRef.current);
        } else {
          window.clearTimeout(requestRef.current);
        }
        requestRef.current = null;
      }
      console.log(`⏸️ [${Date.now()}] Animation frame hook paused`);
    },
    
    // Resume animation
    resume: () => {
      if (!activeRef.current) {
        activeRef.current = true;
        const animate = (time: number) => {
          if (!activeRef.current) return;
          
          try {
            const deltaTime = previousTimeRef.current !== null ? time - previousTimeRef.current : 0;
            callbackRef.current(deltaTime);
            previousTimeRef.current = time;
            
            // Schedule next frame
            if (window.requestAnimationFrame) {
              requestRef.current = window.requestAnimationFrame(animate);
            } else {
              requestRef.current = window.setTimeout(() => animate(performance.now()), 16) as unknown as number;
            }
          } catch (error) {
            console.error(`❌ [${Date.now()}] Error in resumed animation frame:`, error);
            errorCountRef.current += 1;
            
            if (errorCountRef.current < 5) {
              if (window.requestAnimationFrame) {
                requestRef.current = window.requestAnimationFrame(animate);
              } else {
                requestRef.current = window.setTimeout(() => animate(performance.now()), 16) as unknown as number;
              }
            } else {
              activeRef.current = false;
            }
          }
        };
        
        previousTimeRef.current = null; // Reset time to avoid jumps
        
        if (window.requestAnimationFrame) {
          requestRef.current = window.requestAnimationFrame(animate);
        } else {
          requestRef.current = window.setTimeout(() => animate(performance.now()), 16) as unknown as number;
        }
        
        console.log(`▶️ [${Date.now()}] Animation frame hook resumed`);
      }
    },
    
    // Check if animation is active
    isActive: () => activeRef.current
  };
} 