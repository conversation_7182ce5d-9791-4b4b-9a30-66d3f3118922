/**
 * CityDriveByManager.ts
 * Manages camera behavior, zoom levels, and UI changes when
 * the animation passes near unselected cities.
 */

import mapboxgl from 'mapbox-gl';
import * as turf from '@turf/turf';
import { PointOfInterest } from '@/types/POITypes';
import { Destination } from '@/types/destination';
import { City } from '@/types/City';
import { Position, toPositionObject, toPositionTuple, isPositionTuple, isPositionObject, MapPosition } from '@/types/Position';
import AnimationDebugTools from './AnimationDebugTools';
import ContextualSpeedController from './ContextualSpeedController';
import { ComponentInteractionManager } from './ComponentInteractionManager';
import { CityDriveByEvent, InteractionEventType, AnimationEventType, CityProximityStage } from '@/types/AnimationEventTypes';
import type { LogLevel } from '@/types/LogLevel';
import AnimationLogger from '@/utils/animationLogger';
// Use local LogLevel to match AnimationDebugTools' LogLevel definition

// Extend Destination with additional properties needed for city drive-by
interface DriveByDestination extends Destination {
  isSelected?: boolean;
}

// Coordinates interface for position
interface Coordinates {
  lat: number;
  lng: number;
}

// Camera settings interface
interface CameraSettings {
  zoom: number;
  pitch: number;
  duration: number;
}

// Callback types
type CameraUpdateCallback = (settings: CameraSettings) => void;
type POIOverlayCallback = (pois: PointOfInterest[]) => void;

// Event handler types
type CityDriveByEventHandler = (event: CityDriveByEvent) => void;
interface EventHandlers {
  cityDriveBy: CityDriveByEventHandler[];
  [key: string]: ((...args: any[]) => void)[];
}

// Constants
const APPROACHING_DISTANCE = 8.0; // km, start preparing for city drive-by
const CITY_PROXIMITY = 3.0; // km, adjust camera for city view
const CITY_CORE = 1.0; // km, inside city
const CITY_EVENT = 'city-drive-by';

// City proximity thresholds for different stages
const CITY_PROXIMITY_THRESHOLDS = {
  APPROACHING: 20, // km - First detection of city
  ENTERING: 8,     // km - Closer to city, start slowing down
  INSIDE: 3,       // km - Inside city limits
  LEAVING: 5       // km - Leaving city limits
};

// Camera settings for different proximity stages
const CAMERA_SETTINGS = {
  APPROACHING: {
    zoom: 10,
    pitch: 50,
    duration: 2000
  },
  ENTERING: {
    zoom: 12,
    pitch: 55,
    duration: 1800
  },
  INSIDE: {
    zoom: 14,
    pitch: 60,
    duration: 1500
  },
  LEAVING: {
    zoom: 11,
    pitch: 50,
    duration: 2000
  },
  DEFAULT: {
    zoom: 8,
    pitch: 45,
    duration: 1000
  }
};

type CityDriveByStage = CityProximityStage;

interface CityDriveByState {
  nearbyCity: DriveByDestination | null;
  stage: CityProximityStage;
  distance: number;
  lastStageChange: number;
  cityPOIs: PointOfInterest[];
  overlayVisible: boolean;
  estimatedTimeToNextCity?: number;
}

export default class CityDriveByManager {
  private static instance: CityDriveByManager | null = null;
  private map: mapboxgl.Map | null = null;
  private cities: DriveByDestination[] = [];
  private visitedCities: Set<string> = new Set();
  private selectedCities: Set<string> = new Set();
  private poisByCity: Map<string, PointOfInterest[]> = new Map();
  private state: CityDriveByState = {
    nearbyCity: null,
    stage: CityProximityStage.APPROACHING,
    distance: Infinity,
    lastStageChange: 0,
    cityPOIs: [],
    overlayVisible: false
  };
  private updateCameraCallback: CameraUpdateCallback | null = null;
  private updatePOIOverlayCallback: POIOverlayCallback | null = null;
  private cooldownPeriod: number = 5000; // ms
  private lastOverlayTrigger: number = 0;
  private lastPosition: [number, number] | null = null;
  private debugMode: boolean = false;
  private currentVehicleSpeed: number = 0; // km/h
  private speedController: ContextualSpeedController;
  private cameraUpdateCallback: ((settings: CameraSettings) => void) | null = null;
  private uiCallback: ((event: CityDriveByEvent) => void) | null = null;
  private lastCameraUpdate: number = 0;
  private driveByActive: boolean = false;
  private lastHeading: number = 0;
  private cameraTransitionsEnabled: boolean = true;
  private driveByListeners: CityDriveByEventHandler[] = [];
  private activeCityDriveBy: DriveByDestination | null = null;
  private driveByStage: CityDriveByStage | null = null;
  private customEventHandlers: EventHandlers = {
    cityDriveBy: []
  };
  
  /**
   * Private constructor for singleton
   */
  private constructor() {
    this.speedController = ContextualSpeedController.getInstance();
    this.debugMode = window.location.search.includes('debug=true');
    
    // Initialize arrays and collections
    this.cities = [];
    this.visitedCities = new Set();
    this.selectedCities = new Set();
    this.poisByCity = new Map();
    this.driveByListeners = [];
    
    // Initialize state properties
    this.lastPosition = null;
    this.activeCityDriveBy = null;
    this.driveByStage = null;
    
    console.log('CityDriveByManager initialized');
  }
  
  /**
   * Get the singleton instance
   */
  public static getInstance(): CityDriveByManager {
    if (!this.instance) {
      this.instance = new CityDriveByManager();
    }
    return this.instance;
  }
  
  /**
   * Initialize with map instance
   */
  public initialize(map: mapboxgl.Map): this {
    this.map = map;
    return this;
  }
  
  /**
   * Initialize cities data
   */
  public initializeCities(cities: DriveByDestination[]): this {
    this.cities = cities;
    AnimationDebugTools.log('info', `Initialized ${cities.length} cities for drive-by experiences`);
    return this;
  }
  
  /**
   * Set the callback for camera updates
   */
  public setCameraUpdateCallback(callback: CameraUpdateCallback): this {
    this.updateCameraCallback = callback;
    return this;
  }
  
  /**
   * Set the callback for UI updates during city drive-by
   */
  public setUICallback(callback: (event: CityDriveByEvent) => void): void {
    this.uiCallback = callback;
  }
  
  /**
   * Set callback for POI overlay updates
   */
  public setPOIOverlayCallback(callback: POIOverlayCallback): this {
    this.updatePOIOverlayCallback = callback;
    return this;
  }
  
  /**
   * Set selected cities to know which to skip for drive-by experience
   */
  public setSelectedCities(cityIds: string[]): this {
    this.selectedCities = new Set(cityIds);
    return this;
  }
  
  /**
   * Set POIs for each city
   */
  public setCityPOIs(cityName: string, pois: PointOfInterest[]): this {
    this.poisByCity.set(cityName.toLowerCase(), pois);
    return this;
  }
  
  /**
   * Set current vehicle speed for time estimation
   */
  public setVehicleSpeed(speedKmh: number): this {
    this.currentVehicleSpeed = speedKmh;
    return this;
  }
  
  /**
   * Update position and check for nearby cities
   * Returns the nearest unvisited, unselected city if applicable
   */
  public updatePosition(position: [number, number]): any {
    console.log(`[CityDriveByManager] updatePosition called with position: ${JSON.stringify(position)}`);
    if (!position || position.length !== 2) {
      console.log('[CityDriveByManager] Invalid vehicle position:', position);
      return null;
    }

    // Store the current position
    this.lastPosition = position;
    
    // Check if we're near any cities
    const [lng, lat] = position;
    const nearbyCity = this.findNearestCity(position);
    
    if (nearbyCity) {
      const distance = this.calculateDistance(
        [lng, lat], 
        nearbyCity.coordinates
      );
      
      // Determine proximity stage
      let stage: CityProximityStage;
      let stageText: string;
      
      if (distance < CITY_CORE) {
        stage = CityProximityStage.INSIDE;
        stageText = 'entered';
        console.log(`🏙️ Entered city: ${nearbyCity.name} (${distance.toFixed(1)}km)`);
        
        // Trigger entered event if this is a new city or stage changed
        if (this.state.nearbyCity?.name !== nearbyCity.name || this.state.stage !== CityProximityStage.INSIDE) {
          this.transitionToStage(CityProximityStage.INSIDE, nearbyCity, distance);
        }
      } else if (distance < CITY_PROXIMITY) {
        stage = CityProximityStage.ENTERING;
        stageText = 'approaching';
        console.log(`🏙️ Approaching city: ${nearbyCity.name} (${distance.toFixed(1)}km)`);
        
        // Trigger approaching event if stage changed
        if (this.state.stage !== CityProximityStage.ENTERING || this.state.nearbyCity?.name !== nearbyCity.name) {
          this.transitionToStage(CityProximityStage.ENTERING, nearbyCity, distance);
        }
      } else if (distance < APPROACHING_DISTANCE) {
        stage = CityProximityStage.APPROACHING;
        stageText = 'nearby';
        // Only log once when first detected as nearby
        if (this.state.stage !== CityProximityStage.APPROACHING || this.state.nearbyCity?.name !== nearbyCity.name) {
          console.log(`🏙️ City nearby: ${nearbyCity.name} (${distance.toFixed(1)}km)`);
          this.transitionToStage(CityProximityStage.APPROACHING, nearbyCity, distance);
        }
      } else {
        stage = CityProximityStage.NONE;
        stageText = 'distant';
      }
      
      // Update state with current proximity information
      this.state.nearbyCity = nearbyCity;
      this.state.distance = distance;
      this.state.stage = stage;
      
      // Dispatch event for city drive-by
      this.dispatchCityDriveByEvent(nearbyCity, distance);
      
      // Return city proximity information
      return {
        nearbyCity,
        stage: stageText,
        distance
      };
    } else if (this.state.stage !== CityProximityStage.NONE && this.state.nearbyCity) {
      // We've left the proximity of the last city
      console.log(`🏙️ Left city area: ${this.state.nearbyCity?.name}`);
      this.transitionToStage(CityProximityStage.LEAVING, this.state.nearbyCity, 15);
      
      // Dispatch leaving event
      if (this.state.nearbyCity) {
        this.dispatchCityDriveByEvent(this.state.nearbyCity, 15, CityProximityStage.LEAVING);
      }
      
      // Update state to NONE after handling the LEAVING event
      const previousCity = this.state.nearbyCity;
      this.state.stage = CityProximityStage.NONE;
      this.state.nearbyCity = null;
      this.state.distance = Infinity;
      
      return {
        nearbyCity: previousCity,
        stage: 'leaving',
        distance: 15
      };
    }
    
    // No city nearby
    return {
      nearbyCity: null,
      stage: 'distant',
      distance: 9999
    };
  }
  
  /**
   * Handle stage transition with appropriate camera and UI updates
   */
  private transitionToStage(stage: CityProximityStage, city: DriveByDestination | null, distance: number): void {
    const prevStage = this.state.stage;
    const now = Date.now();
    console.log(`[CityDriveByManager] transitionToStage: ${prevStage} -> ${stage} for city: ${city?.name || 'none'}, distance: ${distance}`);
    
    // Skip if no actual stage change and same city (prevents redundant transitions)
    if (stage === prevStage && city?.id === this.state.nearbyCity?.id) {
      return;
    }
    
    // Log stage change for debugging
    if (this.debugMode) {
      console.log(`City stage change: ${prevStage} -> ${stage}`, {
        city: city?.name || 'none',
        distance: `${distance.toFixed(1)}km`,
        timeSinceLastChange: `${((now - this.state.lastStageChange) / 1000).toFixed(1)}s`
      });
    }
    
    // Always log stage transitions
    AnimationDebugTools.log('info', `City stage transition: ${prevStage} -> ${stage}`, {
      city: city?.name || 'none',
      distance: `${distance.toFixed(1)}km`
    });
    
    // Update POIs if city changed or we don't have any
    const cityPOIs = (city && (city.id !== this.state.nearbyCity?.id || this.state.cityPOIs.length === 0))
      ? this.getPOIsForCity(city.name)
      : this.state.cityPOIs;
    
    // Update state
    this.state = {
      ...this.state,
      nearbyCity: city,
      stage,
      distance,
      lastStageChange: now,
      cityPOIs
    };
    
    // Add to visited cities if we entered it
    if ((stage === CityProximityStage.INSIDE || stage === CityProximityStage.ENTERING) && city) {
      this.visitedCities.add(city.id);
    }
    
    // Update camera based on new stage if transitions are enabled
    if (this.lastPosition && city && this.cameraTransitionsEnabled) {
      this.updateCameraForStage(stage, city, this.lastPosition);
    }
    
    // Handle POI overlay visibility
    this.updatePOIOverlayVisibility(stage, city);
    
    // Update drive-by state
    if (stage !== CityProximityStage.NONE && stage !== CityProximityStage.LEAVING) {
      this.driveByActive = true;
      this.activeCityDriveBy = city;
      this.driveByStage = stage;
    } else if (stage === CityProximityStage.LEAVING || stage === CityProximityStage.NONE) {
      // Only end the drive-by if we were in an active drive-by
      if (this.driveByActive) {
        this.driveByActive = false;
      }
    }
    
    // Log event to debug tools
    AnimationDebugTools.log('debug', 'City Drive By Status', {
      stage: stage,
      city: city?.name || 'none',
      distance: `${distance.toFixed(1)}km`,
      pois: this.state.cityPOIs.length,
      timestamp: Date.now()
    });
  }
  
  /**
   * Update camera settings based on proximity stage
   */
  private updateCameraForStage(
    stage: CityProximityStage, 
    city: DriveByDestination | null, 
    position: [number, number]
  ): void {
    if (!city || !this.updateCameraCallback) return;
    
    const settings = CAMERA_SETTINGS[stage.toUpperCase() as keyof typeof CAMERA_SETTINGS] || CAMERA_SETTINGS.DEFAULT;
    
    let bearing = 0;
    if (['APPROACHING', 'ENTERING'].includes(stage)) {
      const pointToCity = turf.bearing(
        turf.point(position),
        turf.point(city.coordinates as [number, number])
      );
      bearing = pointToCity;
    } else if (stage === CityProximityStage.LEAVING) {
      const pointFromCity = turf.bearing(
        turf.point(city.coordinates as [number, number]),
        turf.point(position)
      );
      bearing = pointFromCity;
    }
    
    console.log(`[CityDriveByManager] updateCameraForStage: stage=${stage}, city=${city.name}, settings=${JSON.stringify(settings)}, bearing=${bearing}`);
    this.updateCameraCallback({
      zoom: settings.zoom,
      pitch: settings.pitch,
      duration: settings.duration
    });
  }
  
  /**
   * Update POI overlay visibility based on stage
   */
  private updatePOIOverlayVisibility(stage: CityProximityStage, city: DriveByDestination | null): void {
    if (!this.updatePOIOverlayCallback) return;
    
    const now = Date.now();
    const cooldownElapsed = now - this.lastOverlayTrigger > this.cooldownPeriod;
    
    // Show overlay when entering city or inside city (not selected in itinerary)
    const shouldShowOverlay = (
      // Show when approaching or entering a city (not selected in itinerary)
      ((stage === CityProximityStage.ENTERING || stage === CityProximityStage.INSIDE) && 
       city && !this.selectedCities.has(city.id)) ||
      // Also show when leaving a city to discover on-the-way POIs
      (stage === CityProximityStage.LEAVING && city !== null)
    );
    
    // Force update on stage change or city change, or respect cooldown for visibility changes
    const forceUpdate = this.state.nearbyCity?.id !== city?.id || this.state.stage !== stage;
    
    if (forceUpdate || (shouldShowOverlay !== this.state.overlayVisible && cooldownElapsed)) {
      this.state.overlayVisible = shouldShowOverlay;
      this.lastOverlayTrigger = now;
      
      // Update overlay via callback with current POIs
      this.updatePOIOverlayCallback(shouldShowOverlay ? this.state.cityPOIs : []);
      
      // Log event
      AnimationDebugTools.log('info', `POI Overlay ${shouldShowOverlay ? 'Shown' : 'Hidden'}`, {
        city: city?.name || 'none',
        stage: stage,
        pois: this.state.cityPOIs.length,
        timestamp: now,
        reason: forceUpdate ? 'stage/city change' : 'visibility change'
      });
    }
  }
  
  /**
   * Find the nearest unselected city
   */
  private findNearestCity(position: [number, number]): DriveByDestination | null {
    if (!this.cities || this.cities.length === 0) {
      return null;
    }
    
    const [lng, lat] = position;
    let nearestCity = null;
    let shortestDistance = Number.MAX_VALUE;
    
    // Find the nearest city that isn't selected in the itinerary
    for (const city of this.cities) {
      // Skip cities that are selected in the itinerary
      if (this.selectedCities.has(city.id)) {
        continue;
      }
      
      const distance = this.calculateDistance(
        [lng, lat], 
        city.coordinates
      );
      
      if (distance < shortestDistance) {
        shortestDistance = distance;
        nearestCity = city;
      }
    }
    
    // Only return if within our maximum detection range
    if (shortestDistance <= CITY_PROXIMITY_THRESHOLDS.APPROACHING && nearestCity) {
      // Log detection of nearby city
      this.log('debug', `Detected nearby city: ${nearestCity.name}`, {
        distance: shortestDistance.toFixed(1) + 'km',
        selected: false,
        coordinates: nearestCity.coordinates
      });
      return nearestCity;
    }
    
    return null;
  }
  
  /**
   * Get POIs for a specific city
   */
  private getPOIsForCity(cityName: string): PointOfInterest[] {
    return this.poisByCity.get(cityName.toLowerCase()) || [];
  }
  
  /**
   * Calculate estimated time to the next city based on current position and speed
   */
  public getEstimatedTimeToNextCity(): number | undefined {
    if (!this.lastPosition || this.currentVehicleSpeed <= 0) return undefined;
    
    const point = turf.point(this.lastPosition);
    let minDistance = Infinity;
    
    // Find closest city that's not the current one and not visited
    for (const city of this.cities) {
      // Skip current city or visited cities
      if (
        (this.state.nearbyCity && city.id === this.state.nearbyCity.id) ||
        this.visitedCities.has(city.id)
      ) continue;
      
      const cityPoint = turf.point(city.coordinates as [number, number]);
      const distance = turf.distance(point, cityPoint, { units: 'kilometers' });
      
      if (distance < minDistance) {
        minDistance = distance;
      }
    }
    
    if (minDistance === Infinity) return undefined;
    
    // Calculate time in minutes
    const timeHours = minDistance / this.currentVehicleSpeed;
    return Math.round(timeHours * 60);
  }
  
  /**
   * Reset state
   */
  public reset(): void {
    this.state = {
      nearbyCity: null,
      stage: CityProximityStage.NONE,
      distance: Infinity,
      lastStageChange: 0,
      cityPOIs: [],
      overlayVisible: false
    };
    this.visitedCities.clear();
    this.lastPosition = null;
    this.lastOverlayTrigger = 0;
  }
  
  /**
   * Enable or disable camera transitions
   */
  public setCameraTransitionsEnabled(enabled: boolean): void {
    this.cameraTransitionsEnabled = enabled;
    AnimationDebugTools.log('info', `Camera transitions ${enabled ? 'enabled' : 'disabled'}`);
  }
  
  /**
   * Get nearby cities for contextual speed adjustments
   */
  public getNearbyCities(): DriveByDestination[] {
    if (!this.lastPosition) return [];
    
    return this.cities.filter(city => {
      if (!city.coordinates) return false;
      
      const distance = this.calculateDistance(
        this.lastPosition,
        city.coordinates as [number, number]
      );
      
      return distance <= APPROACHING_DISTANCE;
    });
  }
  
  /**
   * Register callback for city drive-by events
   */
  public onCityDriveBy(callback: (event: CityDriveByEvent) => void): () => void {
    // Ensure listeners array is initialized
    if (!this.driveByListeners) {
      this.driveByListeners = [];
    }
    
    // Safely add the listener
    if (Array.isArray(this.driveByListeners)) {
      this.driveByListeners.push(callback);
    } else {
      console.error("Failed to add city drive-by listener: driveByListeners is not an array");
      // Initialize and add if it wasn't an array
      this.driveByListeners = [callback];
    }
    
    // Return unsubscribe function
    return () => {
      if (this.driveByListeners && Array.isArray(this.driveByListeners)) {
        this.driveByListeners = this.driveByListeners.filter(cb => cb !== callback);
      }
    };
  }
  
  /**
   * Check if there are any unselected cities nearby
   * This method is called periodically to update city proximity status
   */
  private checkForCityDriveBy(): void {
    if (!this.lastPosition) return;
    
    // Find the closest unselected city
    let closestCity: DriveByDestination | null = null;
    let minDistance = Infinity;
    
    for (const city of this.cities) {
      // Skip selected cities
      if (this.selectedCities.has(city.id)) continue;
      
      if (!city.coordinates) return false;
      
      const distance = this.calculateDistance(
        this.lastPosition,
        city.coordinates as [number, number]
      );
      
      if (distance < minDistance) {
        minDistance = distance;
        closestCity = city;
      }
    }
    
    // If no city is nearby or the closest city is too far, reset drive-by state
    if (!closestCity || minDistance > APPROACHING_DISTANCE) {
      if (this.driveByActive) {
        this.log('info', 'No cities nearby, ending active drive-by');
        this.endCityDriveBy();
      }
      return;
    }
    
    // Determine the appropriate stage based on distance
    let newStage: CityProximityStage;
    
    if (minDistance <= CITY_CORE) {
      // Inside city core
      newStage = CityProximityStage.INSIDE;
    } else if (minDistance <= CITY_PROXIMITY) {
      // Entering city proximity
      newStage = CityProximityStage.ENTERING;
    } else if (minDistance <= APPROACHING_DISTANCE) {
      // Approaching city
      newStage = CityProximityStage.APPROACHING;
    } else {
      // Shouldn't reach here due to earlier check, but just in case
      newStage = CityProximityStage.NONE;
    }
    
    // Only transition if the stage or city has changed
    if (newStage !== this.driveByStage || closestCity.id !== this.activeCityDriveBy?.id) {
      this.transitionToStage(newStage, closestCity, minDistance);
      
      // Update speed controller if needed
      if (newStage === CityProximityStage.ENTERING || newStage === CityProximityStage.INSIDE) {
        // Use the available method on the speed controller
      this.speedController.adjustSpeed(minDistance, 'city', { proximity: CITY_PROXIMITY });
      }
    }
  }
  
  /**
   * Handle the different stages of city drive-by
   */
  private handleCityStage(
    city: DriveByDestination,
    stage: CityProximityStage,
    distance: number
  ): void {
    switch (stage) {
      case CityProximityStage.APPROACHING:
        AnimationDebugTools.log('info', `Approaching ${city.name}`);
        break;
      case CityProximityStage.ENTERING:
        AnimationDebugTools.log('info', `Entering ${city.name}`);
        break;
      case CityProximityStage.INSIDE:
        AnimationDebugTools.log('info', `Inside ${city.name}`);
        break;
      case CityProximityStage.LEAVING:
        AnimationDebugTools.log('info', `Leaving ${city.name}`);
        break;
      default:
        AnimationDebugTools.log('info', `Unknown stage for ${city.name}`);
    }
  }
  
  /**
   * Show city preview notification with key stats
   */
  private showCityPreviewNotification(city: DriveByDestination, distance: number): void {
    if (!city || !this.lastPosition) return;
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'city-preview-notification';
    notification.style.position = 'fixed';
    notification.style.top = '80px';
    notification.style.right = '20px';
    notification.style.width = '280px';
    notification.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
    notification.style.borderRadius = '12px';
    notification.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)';
    notification.style.overflow = 'hidden';
    notification.style.zIndex = '1000';
    notification.style.animation = 'slideInRight 0.3s ease-out forwards';
    notification.style.transform = 'translateX(100%)';
    
    // Add some custom styles for animation
    const styleElement = document.createElement('style');
    styleElement.textContent = `
      @keyframes slideInRight {
        from { transform: translateX(100%); }
        to { transform: translateX(0); }
      }
      @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
      }
    `;
    document.head.appendChild(styleElement);
    
    // Populate with city data
    notification.innerHTML = `
      <div style="position: relative;">
        <!-- Header with city name -->
        <div style="background-color: #E76F51; color: white; padding: 12px 16px; font-weight: bold; font-size: 16px; display: flex; justify-content: space-between; align-items: center;">
          <span>Approaching ${city.name}</span>
          <span style="font-size: 14px; font-weight: normal;">${Math.round(distance)} km</span>
        </div>
        
        <!-- City stats -->
        <div style="padding: 12px 16px;">
          <div style="margin-bottom: 12px;">
            <p style="margin: 0 0 8px 0; color: #333; line-height: 1.4; font-size: 14px;">
              ${city.description || 'Explore this beautiful Moroccan city not on your current itinerary.'}
            </p>
          </div>
          
          <!-- Action buttons -->
          <div style="display: flex; gap: 8px;">
            <button id="explore-city-btn" style="flex: 1; background-color: #2A9D8F; color: white; border: none; padding: 8px 12px; border-radius: 4px; cursor: pointer; font-weight: medium;">
              Explore City
            </button>
            <button id="dismiss-city-btn" style="flex: 1; background-color: #F4F5F7; border: none; padding: 8px 12px; border-radius: 4px; cursor: pointer;">
              Continue Journey
            </button>
          </div>
        </div>
        
        <!-- Close button -->
        <button id="close-city-notification" style="position: absolute; top: 8px; right: 8px; background: none; border: none; color: white; cursor: pointer; font-size: 18px;">
          &times;
        </button>
      </div>
    `;
    
    // Add to DOM
    document.body.appendChild(notification);
    
    // Add event listeners
    const closeBtn = notification.querySelector('#close-city-notification') as HTMLButtonElement;
    const exploreBtn = notification.querySelector('#explore-city-btn') as HTMLButtonElement;
    const dismissBtn = notification.querySelector('#dismiss-city-btn') as HTMLButtonElement;
    
    // Close notification handler
    const closeNotification = () => {
      notification.style.animation = 'fadeOut 0.3s forwards';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    };
    
    // Handle close button click
    if (closeBtn) {
      closeBtn.addEventListener('click', closeNotification);
    }
    
    // Handle explore button click
    if (exploreBtn) {
      exploreBtn.addEventListener('click', () => {
        // Trigger event to explore this city
        const event = new CustomEvent('explore-non-selected-city', {
          detail: { city }
        });
        document.dispatchEvent(event);
        closeNotification();
      });
    }
    
    // Handle dismiss button click
    if (dismissBtn) {
      dismissBtn.addEventListener('click', closeNotification);
    }
    
    // Auto-dismiss after 10 seconds
    setTimeout(closeNotification, 10000);
    
    // Log notification
    AnimationDebugTools.log(
      'info',
      `Showing city preview notification for ${city.name}`,
      { distance: `${Math.round(distance)} km` }
    );
  }
  
  /**
   * End the current city drive-by
   */
  private endCityDriveBy(): void {
    if (!this.activeCityDriveBy) return;
    
    const city = this.activeCityDriveBy;
    
    // Create leaving event
    const event: CityDriveByEvent = {
      city,
      timestamp: Date.now(),
      // Use a property that exists in CityDriveByEvent
      location: this.lastPosition!,
      distanceToCity: this.calculateDistance(
        this.lastPosition!,
        city.coordinates
      ),
      stage: CityProximityStage.LEAVING
    };
    
    // Log the end of drive-by
    AnimationDebugTools.log(
      'info',
      `Ending drive-by for ${city.name}`,
      { stage: CityProximityStage.LEAVING }
    );
    
    // Update camera for leaving if transitions are enabled
    if (this.cameraTransitionsEnabled) {
      this.updateCameraForCity(city, CityProximityStage.LEAVING);
    }
    
    // Notify UI if callback is set
    if (this.uiCallback) {
      this.uiCallback(event);
    }
    
    // Notify listeners
    this.notifyDriveByListeners(event);
    
    // Dispatch DOM event
    // Ensure distanceToCity is a number before passing it
    const distanceToCity = typeof event.distanceToCity === 'number' ? event.distanceToCity : 0;
    this.dispatchCityDriveByEvent(city, distanceToCity);
    
    // Reset drive-by state
    this.activeCityDriveBy = null;
    this.driveByStage = null;
    this.driveByActive = false;
  }
  
  /**
   * Update camera settings based on city and stage
   */
  private updateCameraForCity(
    city: DriveByDestination,
    stage: CityProximityStage
  ): void {
    let settings: CameraSettings;
    switch (stage) {
      case CityProximityStage.APPROACHING:
        settings = CAMERA_SETTINGS.APPROACHING;
        break;
      case CityProximityStage.ENTERING:
        settings = CAMERA_SETTINGS.ENTERING;
        break;
      case CityProximityStage.INSIDE:
        settings = CAMERA_SETTINGS.INSIDE;
        break;
      case CityProximityStage.LEAVING:
        settings = CAMERA_SETTINGS.LEAVING;
        break;
      default:
        settings = CAMERA_SETTINGS.DEFAULT;
    }
    
    if (this.updateCameraCallback) {
      this.updateCameraCallback(settings);
    }
  }
  
  /**
   * Calculate distance between two coordinates in kilometers
   */
  private calculateDistance(coord1: [number, number], coord2: [number, number]): number {
    const lat1 = coord1[1];
    const lon1 = coord1[0];
    const lat2 = coord2[1];
    const lon2 = coord2[0];
    const R = 6371; // Earth's radius in km
    const dLat = this.deg2rad(lat2 - lat1);
    const dLng = this.deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
      Math.sin(dLng / 2) * Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    return distance;
  }
  
  /**
   * Convert degrees to radians
   */
  private deg2rad(deg: number): number {
    return deg * (Math.PI / 180);
  }
  
  /**
   * Calculate bearing between two coordinates
   */
  private calculateBearing(start: [number, number], end: [number, number]): number {
    const lat1 = this.deg2rad(start[1]);
    const lon1 = this.deg2rad(start[0]);
    const lat2 = this.deg2rad(end[1]);
    const lon2 = this.deg2rad(end[0]);
    
    const y = Math.sin(lon2 - lon1) * Math.cos(lat2);
    const x =
      Math.cos(lat1) * Math.sin(lat2) -
      Math.sin(lat1) * Math.cos(lat2) * Math.cos(lon2 - lon1);
    
    let bearing = Math.atan2(y, x);
    bearing = (bearing * 180) / Math.PI;
    bearing = (bearing + 360) % 360;
    
    return bearing;
  }
  
  /**
   * Calculate midpoint between two coordinates
   */
  private calculateMidpoint(coord1: [number, number], coord2: [number, number]): [number, number] {
    const lat1 = this.deg2rad(coord1[1]);
    const lon1 = this.deg2rad(coord1[0]);
    const lat2 = this.deg2rad(coord2[1]);
    const lon2 = this.deg2rad(coord2[0]);
    const dLon = lon2 - lon1;
    const dLat = lat2 - lat1;
    const a = Math.sin(dLat / 2) ** 2 + Math.cos(lat1) * Math.cos(lat2) * Math.sin(dLon / 2) ** 2;
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const midLon = lon1 + dLon / 2;
    const midLat = lat1 + dLat / 2;
    const midLonRads = midLon * Math.PI / 180;
    const midLatRads = midLat * Math.PI / 180;
    const x = Math.cos(midLatRads) * Math.cos(midLonRads);
    const y = Math.cos(midLatRads) * Math.sin(midLonRads);
    const z = Math.sin(midLatRads);
    const midLonOut = Math.atan2(y, x) * 180 / Math.PI;
    const midLatOut = Math.atan2(z, Math.sqrt(x * x + y * y)) * 180 / Math.PI;
    return [midLonOut, midLatOut];
  }
  
  /**
   * Notify drive-by event listeners
   */
  private notifyDriveByListeners(event: CityDriveByEvent): void {
    // Ensure listeners array is initialized
    if (!this.driveByListeners || !Array.isArray(this.driveByListeners)) {
      this.driveByListeners = [];
      return;
    }
    
    this.driveByListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in city drive-by listener', error);
      }
    });
  }
  
  /**
   * Dispatch DOM event for city drive-by
   */
  private dispatchCityDriveByEvent(city: DriveByDestination, distance: number, stage: CityProximityStage = CityProximityStage.APPROACHING): void {
    const event: CityDriveByEvent = {
      timestamp: Date.now(),
      type: AnimationEventType.CITY_APPROACH,
      eventType: AnimationEventType.CITY_APPROACH,
      data: {
        nearbyCity: {
          name: city.name
        },
        distance,
        stage,
        pois: this.getPOIsForCity(city.name).map(poi => ({
          id: poi.id,
          name: poi.name,
          position: poi.coordinates || [0, 0]
        })),
        timestamp: Date.now(),
        position: city.coordinates || [0, 0]
      },
      city: city, // For backward compatibility
      distanceToCity: distance // For backward compatibility
    };
    this.notifyDriveByListeners(event);
  }
  
  /**
   * Force camera update for a specific city (for debugging)
   */
  public debugForceCameraUpdate(cityId: string, stage: 'approaching' | 'entering' | 'inside' | 'leaving'): boolean {
    const city = this.cities.find(c => c.id === cityId);
    
    if (!city || !this.lastPosition) {
      return false;
    }
    
    this.updateCameraForCity(city, stage as CityProximityStage);
    return true;
  }

  private log(level: LogLevel, message: string, data?: any) {
    // Convert LogLevel type to the internal debug level
    let debugLevel: 'debug' | 'info' | 'warn' | 'error' | 'performance';
    
    // Map the imported LogLevel to AnimationDebugTools.LogLevel
    switch(level) {
      case 'debug':
        debugLevel = 'debug';
        break;
      case 'info':
        debugLevel = 'info';
        break;
      case 'warn':
        debugLevel = 'warn'; // Update to 'warn' to match the LogLevel type
        break;
      case 'error':
        debugLevel = 'error';
        break;
      case 'performance':
        debugLevel = 'performance';
        break;
      default:
        debugLevel = 'info';
    }
    
    AnimationDebugTools.log(debugLevel, message, data);
  }

  private handleCityProximity(position: Position, city: DriveByDestination, distanceToCity: number): void {
    // Convert position to [number, number] tuple
    const positionTuple = this.getPositionArray(position);

    // Update speed controller context
    this.speedController.updateContext({
      distanceToCity,
      terrain: 'default',
      isScenic: false
    });

    // Get the speed multiplier
    const speedMultiplier = this.speedController.getSpeedMultiplier();

    // Determine stage based on distance
    let stage: CityProximityStage;
    if (distanceToCity <= CITY_CORE) {
      stage = CityProximityStage.INSIDE;
    } else if (distanceToCity <= CITY_PROXIMITY) {
      stage = CityProximityStage.ENTERING;
    } else if (distanceToCity <= APPROACHING_DISTANCE) {
      stage = CityProximityStage.APPROACHING;
    } else {
      stage = CityProximityStage.NONE;
    }

    // Update state and trigger events
    this.transitionToStage(stage, city, distanceToCity);
  }

  private handleCityDriveBy(city: DriveByDestination): void {
    // Implementation will be added later
  }

  private handlePOIOverlayVisibility(isVisible: boolean): void {
    // Implementation will be added later
  }

  private handleCityApproach(city: DriveByDestination): void {
    // Implementation will be added later
  }

  /**
   * Convert [number, number] to Position
   */
  private convertToCoordinates(position: [number, number]): Position {
    return position;
  }

  /**
   * Get position array from Position
   */
  private getPositionArray(position: Position | null | undefined): [number, number] {
    if (!position) {
      return [0, 0]; // Default fallback
    }
    if (isPositionTuple(position)) {
      return position;
    }
    if (isPositionObject(position)) {
      return [position.lng, position.lat];
    }
    return [0, 0]; // Default fallback
  }

  /**
   * Convert a Position to MapPosition
   */
  private convertToMapPosition(position: Position | null | undefined): PositionObject {
    if (!position) {
      return { lng: 0, lat: 0 }; // Default fallback
    }
    if (isPositionTuple(position)) {
      return { lng: position[0], lat: position[1] };
    }
    if (isPositionObject(position)) {
      return { lng: position.lng, lat: position.lat };
    }
    return { lng: 0, lat: 0 }; // Default fallback
  }

  /**
   * Initialize mock city data for testing
   */
  private initializeMockCityData(): void {
    this.cities = [
      {
        id: 'casablanca',
        name: 'Casablanca',
        coordinates: [-7.5898, 33.5731],
        description: 'Morocco\'s largest city and economic capital',
        image: 'casablanca.jpg'
      },
      {
        id: 'marrakech',
        name: 'Marrakech',
        coordinates: [-7.9811, 31.6295],
        description: 'The Red City, known for its vibrant markets and historic medina',
        image: 'marrakech.jpg'
      }
    ];
  }

  /**
   * Load cities data
   */
  private async loadCities(): Promise<void> {
    try {
      const destinations = await this.fetchDestinationsFromAPI();
      this.cities = destinations.map(dest => ({
        id: dest.id || '',
        name: dest.name || '',
        coordinates: dest.coordinates || [0, 0],
        description: dest.description || '',
        image: dest.image || ''
      }));
    } catch (error) {
      console.error('Failed to load cities:', error);
      this.initializeMockCityData();
    }
  }

  /**
   * Fetch destinations from API with proper typing
   */
  private async fetchDestinationsFromAPI(): Promise<Partial<Destination>[]> {
    // Mock API response
    return [
      {
        id: 'casablanca',
        name: 'Casablanca',
        coordinates: [-7.5898, 33.5731],
        description: 'Morocco\'s largest city and economic capital',
        image: 'casablanca.jpg'
      },
      {
        id: 'marrakech',
        name: 'Marrakech',
        coordinates: [-7.9811, 31.6295],
        description: 'The Red City, known for its vibrant markets and historic medina',
        image: 'marrakech.jpg'
      }
    ];
  }
}