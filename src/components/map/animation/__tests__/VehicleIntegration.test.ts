/**
 * VehicleIntegration.test.ts
 * 
 * Integration tests for the vehicle animation system components
 * Tests the interactions between VehicleDOM, VehicleManager, and VehicleController
 */

import { VehicleDOM } from '../VehicleDOM';
import { VehicleManager } from '../VehicleManager';
import { VehicleController } from '../VehicleController';
import { Position } from '@/types/Position';
import { TerrainType } from '../../../../types/CoreAnimationTypes';
import { AnimationEventType } from '../../../../types/AnimationEventTypes';

// Mock mapboxgl
const mockMapboxgl = {
  Map: jest.fn().mockImplementation(() => ({
    getContainer: jest.fn().mockReturnValue(document.createElement('div')),
    project: jest.fn().mockImplementation((lngLat) => ({
      x: 100,
      y: 100
    })),
    unproject: jest.fn().mockImplementation((point) => ({
      lng: 0,
      lat: 0
    })),
    isStyleLoaded: jest.fn().mockReturnValue(true),
    on: jest.fn(),
    off: jest.fn()
  }))
};

jest.mock('mapbox-gl', () => mockMapboxgl);

// Mock AnimationDebugTools
jest.mock('../AnimationDebugTools', () => ({
  __esModule: true,
  default: {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
    vehicle: jest.fn()
  }
}));

describe('Vehicle Animation System Integration', () => {
  let vehicleDOM: VehicleDOM;
  let vehicleManager: VehicleManager;
  let vehicleController: VehicleController;
  let mockMap: any;
  
  beforeEach(() => {
    // Setup document body for DOM testing
    document.body.innerHTML = '';
    
    // Create mock map
    mockMap = {
      getContainer: jest.fn().mockReturnValue(document.createElement('div')),
      project: jest.fn().mockImplementation((lngLat) => ({
        x: 100,
        y: 100
      })),
      unproject: jest.fn().mockImplementation((point) => ({
        lng: 0,
        lat: 0
      })),
      isStyleLoaded: jest.fn().mockReturnValue(true),
      on: jest.fn(),
      off: jest.fn()
    };
    
    // Create real components
    vehicleDOM = new VehicleDOM();
    vehicleManager = VehicleManager.createInstance(vehicleDOM);
    vehicleController = new VehicleController(vehicleManager);
  });
  
  afterEach(() => {
    jest.clearAllMocks();
    document.body.innerHTML = '';
  });
  
  describe('Component Initialization', () => {
    test('should initialize all components in the correct order', async () => {
      // Initialize controller which should initialize manager and DOM
      await vehicleController.initialize(mockMap);
      
      // Verify all components are initialized
      expect(vehicleController.isInitialized()).toBe(true);
      expect(vehicleManager.isInitialized()).toBe(true);
      
      // Verify DOM container was created
      const container = document.getElementById('vehicle-markers-container');
      expect(container).toBeDefined();
    });
  });
  
  describe('Vehicle Movement', () => {
    beforeEach(async () => {
      await vehicleController.initialize(mockMap);
    });
    
    test('should propagate position updates through all layers', () => {
      // Spy on DOM methods
      const updatePositionSpy = jest.spyOn(vehicleDOM, 'updateElementPosition');
      const updateBearingSpy = jest.spyOn(vehicleDOM, 'updateElementBearing');
      
      // Update position through controller
      const position: Position = [10, 20];
      const bearing = 45;
      
      vehicleController.moveTo(position, bearing);
      
      // Verify position update propagated to DOM
      expect(updatePositionSpy).toHaveBeenCalled();
      expect(updateBearingSpy).toHaveBeenCalled();
    });
  });
  
  describe('Visibility Control', () => {
    beforeEach(async () => {
      await vehicleController.initialize(mockMap);
    });
    
    test('should propagate visibility changes through all layers', () => {
      // Spy on DOM methods
      const showElementSpy = jest.spyOn(vehicleDOM, 'showElement');
      const hideElementSpy = jest.spyOn(vehicleDOM, 'hideElement');
      
      // Show vehicle through controller
      vehicleController.setVisible(true);
      expect(showElementSpy).toHaveBeenCalled();
      
      // Hide vehicle through controller
      vehicleController.setVisible(false);
      expect(hideElementSpy).toHaveBeenCalled();
    });
  });
  
  describe('Terrain Updates', () => {
    beforeEach(async () => {
      await vehicleController.initialize(mockMap);
    });
    
    test('should propagate terrain changes through all layers', () => {
      // Spy on DOM methods
      const updateStyleSpy = jest.spyOn(vehicleDOM, 'updateElementStyle');
      
      // Update terrain through controller
      const terrain: TerrainType = 'MOUNTAIN';
      vehicleController.updateTerrain(terrain);
      
      // Verify style update propagated to DOM
      expect(updateStyleSpy).toHaveBeenCalled();
    });
  });
  
  describe('Event Propagation', () => {
    beforeEach(async () => {
      await vehicleController.initialize(mockMap);
    });
    
    test('should propagate events through the component hierarchy', () => {
      // Create event handlers
      const controllerHandler = jest.fn();
      const managerHandler = jest.fn();
      
      // Register event handlers
      vehicleController.addEventListener(AnimationEventType.VEHICLE_POSITION_UPDATED, controllerHandler);
      vehicleManager.addEventListener(AnimationEventType.VEHICLE_POSITION_UPDATED, managerHandler);
      
      // Trigger event by updating position
      const position: Position = [10, 20];
      vehicleController.moveTo(position, 0);
      
      // Verify both handlers were called
      expect(controllerHandler).toHaveBeenCalled();
      expect(managerHandler).toHaveBeenCalled();
    });
  });
  
  describe('Error Recovery', () => {
    beforeEach(async () => {
      await vehicleController.initialize(mockMap);
    });
    
    test('should recover from visibility issues', () => {
      // Spy on recovery methods
      const recoverSpy = jest.spyOn(vehicleManager, 'recoverVehicleVisibility');
      
      // Trigger recovery through controller
      vehicleController.recoverVisibility();
      
      // Verify recovery was attempted
      expect(recoverSpy).toHaveBeenCalled();
    });
  });
  
  describe('Component Cleanup', () => {
    beforeEach(async () => {
      await vehicleController.initialize(mockMap);
    });
    
    test('should clean up all components properly', async () => {
      // Spy on cleanup methods
      const resetSpy = jest.spyOn(vehicleManager, 'reset');
      
      // Reset controller
      await vehicleController.reset();
      
      // Verify manager was reset
      expect(resetSpy).toHaveBeenCalled();
      
      // Dispose controller
      vehicleController.dispose();
      
      // Verify vehicle is no longer visible
      expect(vehicleController.isVisible()).toBe(false);
    });
  });
  
  describe('Performance Benchmarks', () => {
    beforeEach(async () => {
      await vehicleController.initialize(mockMap);
    });
    
    test('should handle rapid position updates efficiently', () => {
      // Create a large number of position updates
      const updates = 100;
      const startTime = performance.now();
      
      // Perform updates
      for (let i = 0; i < updates; i++) {
        const position: Position = [i * 0.01, i * 0.01];
        vehicleController.moveTo(position, i);
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Log performance metrics
      console.log(`Performed ${updates} position updates in ${duration.toFixed(2)}ms (${(duration / updates).toFixed(2)}ms per update)`);
      
      // Verify updates were processed
      expect(vehicleController.getCurrentPosition()).toBeDefined();
      
      // This is not a strict test, but helps identify performance regressions
      // A threshold could be added if needed
    });
  });
});