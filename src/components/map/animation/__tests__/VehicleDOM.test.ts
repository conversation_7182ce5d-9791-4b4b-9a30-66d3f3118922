/**
 * VehicleDOM.test.ts
 * 
 * Unit tests for the VehicleDOM component
 */

import { VehicleDOM } from '../VehicleDOM';
import { VehicleDOMInterface } from '../interfaces/VehicleDOMInterface';
import { Position } from '@/types/Position';
import { VehicleType } from '@/types/VehicleTypes';

// Mock mapboxgl
const mockMapboxgl = {
  Map: jest.fn().mockImplementation(() => ({
    getContainer: jest.fn().mockReturnValue(document.createElement('div')),
    project: jest.fn().mockImplementation((lngLat) => ({
      x: 100,
      y: 100
    })),
    unproject: jest.fn().mockImplementation((point) => ({
      lng: 0,
      lat: 0
    }))
  }))
};

jest.mock('mapbox-gl', () => mockMapboxgl);

describe('VehicleDOM', () => {
  let vehicleDOM: VehicleDOMInterface;
  let mockMap: any;
  let mockElement: HTMLElement;
  
  beforeEach(() => {
    // Setup document body for DOM testing
    document.body.innerHTML = '';
    
    // Create mock map
    mockMap = {
      getContainer: jest.fn().mockReturnValue(document.createElement('div')),
      project: jest.fn().mockImplementation((lngLat) => ({
        x: 100,
        y: 100
      })),
      unproject: jest.fn().mockImplementation((point) => ({
        lng: 0,
        lat: 0
      }))
    };
    
    // Create mock element
    mockElement = document.createElement('div');
    mockElement.id = 'test-vehicle';
    
    // Initialize VehicleDOM
    vehicleDOM = new VehicleDOM();
  });
  
  afterEach(() => {
    jest.clearAllMocks();
    document.body.innerHTML = '';
  });
  
  describe('ensureMarkerContainer', () => {
    test('should create marker container if it does not exist', () => {
      const container = vehicleDOM.ensureMarkerContainer(mockMap);
      
      expect(container).toBeDefined();
      expect(container.id).toBe('vehicle-markers-container');
      expect(mockMap.getContainer).toHaveBeenCalled();
    });
    
    test('should return existing container if it already exists', () => {
      // First call creates the container
      const firstContainer = vehicleDOM.ensureMarkerContainer(mockMap);
      
      // Second call should return the same container
      const secondContainer = vehicleDOM.ensureMarkerContainer(mockMap);
      
      expect(secondContainer).toBe(firstContainer);
      expect(mockMap.getContainer).toHaveBeenCalledTimes(1);
    });
    
    test('should use custom container ID if provided', () => {
      const customId = 'custom-vehicle-container';
      const container = vehicleDOM.ensureMarkerContainer(mockMap, customId);
      
      expect(container.id).toBe(customId);
    });
  });
  
  describe('createMarkerElement', () => {
    test('should create a vehicle marker element with proper styling', () => {
      const element = vehicleDOM.createMarkerElement('test-vehicle-id');
      
      expect(element).toBeDefined();
      expect(element.id).toBe('test-vehicle-id');
      expect(element.className).toContain('vehicle-marker');
      
      // Check that the element has the expected styles
      expect(element.style.position).toBe('absolute');
      expect(element.style.transform).toContain('translate(-50%, -50%)');
      
      // Check that it has a direction indicator
      const indicator = element.querySelector('.vehicle-direction-indicator');
      expect(indicator).toBeDefined();
    });
  });
  
  describe('updateElementPosition', () => {
    test('should update element position based on map projection', () => {
      const element = vehicleDOM.createMarkerElement('test-vehicle');
      const position: Position = [0, 0];
      
      vehicleDOM.updateElementPosition(element, position, mockMap);
      
      expect(mockMap.project).toHaveBeenCalledWith({ lng: position[0], lat: position[1] });
      expect(element.style.left).toBe('100px');
      expect(element.style.top).toBe('100px');
    });
    
    test('should handle errors gracefully', () => {
      const element = vehicleDOM.createMarkerElement('test-vehicle');
      const position: Position = [0, 0];
      
      // Mock project to throw an error
      mockMap.project.mockImplementationOnce(() => {
        throw new Error('Map projection error');
      });
      
      // Should not throw
      expect(() => {
        vehicleDOM.updateElementPosition(element, position, mockMap);
      }).not.toThrow();
    });
  });
  
  describe('updateElementBearing', () => {
    test('should update element rotation based on bearing', () => {
      const element = vehicleDOM.createMarkerElement('test-vehicle');
      const bearing = 45;
      
      vehicleDOM.updateElementBearing(element, bearing);
      
      // Check that the transform includes rotation
      expect(element.style.transform).toContain(`rotate(${bearing}deg)`);
    });
  });
  
  describe('updateElementStyle', () => {
    test('should apply style properties to the element', () => {
      const element = vehicleDOM.createMarkerElement('test-vehicle');
      const style: Partial<VehicleStyle> = {
        color: '#ff0000',
        size: 30,
        opacity: 0.8,
        borderColor: '#ffffff',
        borderWidth: 3
      };
      
      vehicleDOM.updateElementStyle(element, style);
      
      expect(element.style.backgroundColor).toBe(style.color);
      expect(element.style.width).toBe(`${style.size}px`);
      expect(element.style.height).toBe(`${style.size}px`);
      expect(element.style.opacity).toBe(style.opacity!.toString());
      expect(element.style.borderColor).toBe(style.borderColor);
      expect(element.style.borderWidth).toBe(`${style.borderWidth}px`);
    });
    
    test('should handle partial style updates', () => {
      const element = vehicleDOM.createMarkerElement('test-vehicle');
      
      // Initial style
      vehicleDOM.updateElementStyle(element, {
        color: '#ff0000',
        size: 30
      });
      
      // Update only opacity
      vehicleDOM.updateElementStyle(element, {
        opacity: 0.5
      });
      
      // Color and size should remain unchanged
      expect(element.style.backgroundColor).toBe('#ff0000');
      expect(element.style.width).toBe('30px');
      expect(element.style.opacity).toBe('0.5');
    });
  });
  
  describe('showElement and hideElement', () => {
    test('should show element by setting display to block', () => {
      const element = vehicleDOM.createMarkerElement('test-vehicle');
      element.style.display = 'none';
      
      vehicleDOM.showElement(element);
      
      expect(element.style.display).toBe('block');
      expect(element.style.visibility).toBe('visible');
    });
    
    test('should hide element by setting display to none', () => {
      const element = vehicleDOM.createMarkerElement('test-vehicle');
      
      vehicleDOM.hideElement(element);
      
      expect(element.style.display).toBe('none');
    });
  });
  
  describe('appendChild and removeChild', () => {
    test('should append child to container', () => {
      const container = vehicleDOM.ensureMarkerContainer(mockMap);
      const element = vehicleDOM.createMarkerElement('test-vehicle');
      
      vehicleDOM.appendChild(element);
      
      expect(container.contains(element)).toBe(true);
    });
    
    test('should remove child from container', () => {
      const container = vehicleDOM.ensureMarkerContainer(mockMap);
      const element = vehicleDOM.createMarkerElement('test-vehicle');
      
      vehicleDOM.appendChild(element);
      vehicleDOM.removeChild(element);
      
      expect(container.contains(element)).toBe(false);
    });
  });
  
  describe('getPixelPosition', () => {
    test('should convert geographic position to pixel position', () => {
      const position: Position = [0, 0];
      
      const pixelPosition = vehicleDOM.getPixelPosition(position, mockMap);
      
      expect(mockMap.project).toHaveBeenCalledWith({ lng: position[0], lat: position[1] });
      expect(pixelPosition).toEqual({ x: 100, y: 100 });
    });
    
    test('should handle errors gracefully', () => {
      const position: Position = [0, 0];
      
      // Mock project to throw an error
      mockMap.project.mockImplementationOnce(() => {
        throw new Error('Map projection error');
      });
      
      // Should return default position and not throw
      const pixelPosition = vehicleDOM.getPixelPosition(position, mockMap);
      expect(pixelPosition).toEqual({ x: 0, y: 0 });
    });
  });
});