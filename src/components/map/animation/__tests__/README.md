# Vehicle Animation System Tests

This directory contains unit tests, integration tests, and performance benchmarks for the vehicle animation system components. These tests are part of the Phase 2 refactoring of the animation system as outlined in the project's Memory Bank.

## Test Structure

The test suite is organized into the following categories:

### Unit Tests

- `VehicleDOM.test.ts`: Tests the DOM manipulation component in isolation
- `VehicleManager.test.ts`: Tests the state management component in isolation
- `VehicleController.test.ts`: Tests the animation and coordination component in isolation

### Integration Tests

- `VehicleIntegration.test.ts`: Tests the interactions between all three components

### Performance Benchmarks

- `VehiclePerformance.test.ts`: Measures performance metrics for various operations

## Running the Tests

To run the tests, use the following commands:

```bash
# Install Jest if not already installed
npm install --save-dev jest @types/jest ts-jest

# Run all tests
npm test

# Run specific test file
npm test -- VehicleDOM.test.ts

# Run with coverage report
npm test -- --coverage
```

## Test Configuration

The tests use Jest as the testing framework. You may need to add the following to your `package.json` file if not already present:

```json
"scripts": {
  "test": "jest"
},
"jest": {
  "preset": "ts-jest",
  "testEnvironment": "jsdom",
  "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"],
  "testMatch": ["**/__tests__/**/*.ts?(x)", "**/?(*.)+(spec|test).ts?(x)"],
  "transform": {
    "^.+\\.(ts|tsx)$": "ts-jest"
  }
}
```

## Testing Approach

### Unit Tests

Unit tests focus on testing each component in isolation with mocked dependencies. They verify that:

- Each component correctly implements its interface
- Methods behave as expected with various inputs
- Error handling works correctly
- Event handling is properly implemented

### Integration Tests

Integration tests verify that components work together correctly. They test:

- Component initialization order
- Event propagation between components
- End-to-end workflows like vehicle movement and visibility changes
- Error recovery mechanisms

### Performance Benchmarks

Performance tests measure execution time for various operations to identify potential bottlenecks and ensure the system meets performance requirements. They test:

- Initialization time
- Position update performance
- Animation processing efficiency
- DOM operation performance
- Memory usage patterns

## Mocking Strategy

The tests use the following mocking approach:

- External dependencies like `mapbox-gl` are fully mocked
- `AnimationDebugTools` is mocked to avoid console output during tests
- When testing a component in isolation, its dependencies are mocked
- Integration tests use real component implementations

## Adding New Tests

When adding new tests, follow these guidelines:

1. Place unit tests in the appropriate test file based on the component being tested
2. Add integration tests for new interactions between components
3. Update performance benchmarks when optimizing code
4. Ensure all tests are independent and don't rely on global state

## Test Coverage Goals

The test suite aims to achieve:

- High code coverage (>80%) for all components
- Testing of all public methods and interfaces
- Verification of error handling and edge cases
- Performance validation for critical operations

This comprehensive test suite supports the animation system refactoring by ensuring that changes maintain correctness while improving architecture and performance.