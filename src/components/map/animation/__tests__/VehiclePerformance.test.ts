/**
 * VehiclePerformance.test.ts
 * 
 * Performance benchmarks for the vehicle animation system
 * Tests the performance of VehicleDOM, VehicleManager, and VehicleController
 */

import { VehicleDOM } from '../VehicleDOM';
import { VehicleManager } from '../VehicleManager';
import { VehicleController } from '../VehicleController';
import { Position } from '../../../../types/Position';
import { TerrainType } from '../../../../types/CoreAnimationTypes';

// Mock mapboxgl
const mockMapboxgl = {
  Map: jest.fn().mockImplementation(() => ({
    getContainer: jest.fn().mockReturnValue(document.createElement('div')),
    project: jest.fn().mockImplementation((lngLat) => ({
      x: 100,
      y: 100
    })),
    unproject: jest.fn().mockImplementation((point) => ({
      lng: 0,
      lat: 0
    })),
    isStyleLoaded: jest.fn().mockReturnValue(true),
    on: jest.fn(),
    off: jest.fn()
  }))
};

jest.mock('mapbox-gl', () => mockMapboxgl);

// Mock AnimationDebugTools
jest.mock('../AnimationDebugTools', () => ({
  __esModule: true,
  default: {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
    vehicle: jest.fn()
  }
}));

/**
 * Helper function to measure execution time
 * @param callback Function to measure
 * @returns Execution time in milliseconds
 */
async function measureExecutionTime(callback: () => Promise<void> | void): Promise<number> {
  const start = performance.now();
  await callback();
  const end = performance.now();
  return end - start;
}

describe('Vehicle Animation System Performance', () => {
  let vehicleDOM: VehicleDOM;
  let vehicleManager: VehicleManager;
  let vehicleController: VehicleController;
  let mockMap: any;
  
  beforeEach(async () => {
    // Setup document body for DOM testing
    document.body.innerHTML = '';
    
    // Create mock map
    mockMap = {
      getContainer: jest.fn().mockReturnValue(document.createElement('div')),
      project: jest.fn().mockImplementation((lngLat) => ({
        x: 100,
        y: 100
      })),
      unproject: jest.fn().mockImplementation((point) => ({
        lng: 0,
        lat: 0
      })),
      isStyleLoaded: jest.fn().mockReturnValue(true),
      on: jest.fn(),
      off: jest.fn()
    };
    
    // Create real components
    vehicleDOM = new VehicleDOM();
    vehicleManager = VehicleManager.createInstance(vehicleDOM);
    vehicleController = new VehicleController(vehicleManager);
    
    // Initialize components
    await vehicleController.initialize(mockMap);
  });
  
  afterEach(() => {
    jest.clearAllMocks();
    document.body.innerHTML = '';
  });
  
  describe('Initialization Performance', () => {
    test('should measure initialization time', async () => {
      // Reset components
      vehicleController.dispose();
      vehicleDOM = new VehicleDOM();
      vehicleManager = VehicleManager.createInstance(vehicleDOM);
      vehicleController = new VehicleController(vehicleManager);
      
      // Measure initialization time
      const initTime = await measureExecutionTime(async () => {
        await vehicleController.initialize(mockMap);
      });
      
      console.log(`Initialization time: ${initTime.toFixed(2)}ms`);
      
      // Verify initialization was successful
      expect(vehicleController.isInitialized()).toBe(true);
      
      // This is not a strict test, but helps identify performance regressions
      // A threshold could be added if needed
      expect(initTime).toBeLessThan(1000); // Should initialize in less than 1 second
    });
  });
  
  describe('Position Update Performance', () => {
    test('should measure single position update time', async () => {
      const position: Position = [10, 20];
      const bearing = 45;
      
      // Measure single update time
      const updateTime = await measureExecutionTime(() => {
        vehicleController.moveTo(position, bearing);
      });
      
      console.log(`Single position update time: ${updateTime.toFixed(2)}ms`);
      
      // Verify update was successful
      expect(vehicleController.getCurrentPosition()).toEqual(position);
      
      // This is not a strict test, but helps identify performance regressions
      expect(updateTime).toBeLessThan(50); // Should update in less than 50ms
    });
    
    test('should measure batch position update performance', async () => {
      const updates = 100;
      const positions: Position[] = [];
      
      // Generate test positions
      for (let i = 0; i < updates; i++) {
        positions.push([i * 0.01, i * 0.01]);
      }
      
      // Measure batch update time
      const batchTime = await measureExecutionTime(async () => {
        for (let i = 0; i < updates; i++) {
          vehicleController.moveTo(positions[i], i);
        }
      });
      
      const avgTime = batchTime / updates;
      console.log(`Batch position updates (${updates}): ${batchTime.toFixed(2)}ms total, ${avgTime.toFixed(2)}ms per update`);
      
      // Verify updates were processed
      expect(vehicleController.getCurrentPosition()).toEqual(positions[positions.length - 1]);
      
      // This is not a strict test, but helps identify performance regressions
      expect(avgTime).toBeLessThan(10); // Should average less than 10ms per update
    });
  });
  
  describe('Animation Performance', () => {
    test('should measure animation performance', async () => {
      jest.useFakeTimers();
      
      const startPosition: Position = [0, 0];
      const endPosition: Position = [10, 10];
      const duration = 1000;
      
      let animationCompleted = false;
      
      // Start animation
      const animationTime = await measureExecutionTime(() => {
        vehicleController.moveTo(endPosition, 45, {
          duration,
          easing: 'linear',
          animateBearing: true,
          onComplete: () => {
            animationCompleted = true;
          }
        });
        
        // Fast-forward time to complete animation
        jest.advanceTimersByTime(duration);
      });
      
      console.log(`Animation processing time: ${animationTime.toFixed(2)}ms for ${duration}ms animation`);
      
      // Verify animation completed
      expect(animationCompleted).toBe(true);
      
      // This is not a strict test, but helps identify performance regressions
      expect(animationTime).toBeLessThan(duration * 0.1); // Processing should take less than 10% of animation time
      
      jest.useRealTimers();
    });
  });
  
  describe('Style Update Performance', () => {
    test('should measure terrain style update performance', async () => {
      const terrains: TerrainType[] = ['DEFAULT', 'MOUNTAIN', 'DESERT', 'FOREST', 'WATER'];
      const updates = 50;
      
      // Measure batch style update time
      const styleTime = await measureExecutionTime(async () => {
        for (let i = 0; i < updates; i++) {
          const terrain = terrains[i % terrains.length];
          vehicleController.updateTerrain(terrain);
        }
      });
      
      const avgTime = styleTime / updates;
      console.log(`Style updates (${updates}): ${styleTime.toFixed(2)}ms total, ${avgTime.toFixed(2)}ms per update`);
      
      // This is not a strict test, but helps identify performance regressions
      expect(avgTime).toBeLessThan(10); // Should average less than 10ms per style update
    });
  });
  
  describe('Memory Usage and Cleanup', () => {
    test('should not leak memory during repeated operations', async () => {
      const iterations = 10;
      const operationsPerIteration = 100;
      
      for (let i = 0; i < iterations; i++) {
        // Perform a batch of operations
        for (let j = 0; j < operationsPerIteration; j++) {
          const position: Position = [j * 0.01, j * 0.01];
          vehicleController.moveTo(position, j);
          vehicleController.setVisible(j % 2 === 0);
          vehicleController.updateTerrain(j % 2 === 0 ? 'MOUNTAIN' : 'DEFAULT');
        }
        
        // Reset between iterations to verify cleanup
        await vehicleController.reset();
        
        // Verify state after reset
        expect(vehicleController.getCurrentPosition()).toEqual([0, 0]);
        expect(vehicleController.isVisible()).toBe(true);
      }
      
      // Final cleanup
      vehicleController.dispose();
      
      // Verify proper cleanup
      expect(vehicleController.isInitialized()).toBe(false);
    });
    
    test('should properly cleanup DOM elements and event listeners', () => {
      // Create and track some test elements
      const elements: HTMLElement[] = [];
      for (let i = 0; i < 5; i++) {
        elements.push(vehicleDOM.createMarkerElement(`test-vehicle-${i}`));
      }
      
      // Add elements to the DOM
      elements.forEach(element => {
        document.body.appendChild(element);
      });
      
      // Dispose of components
      vehicleController.dispose();
      vehicleManager.dispose();
      vehicleDOM.dispose();
      
      // Verify DOM cleanup
      elements.forEach(element => {
        expect(document.body.contains(element)).toBe(false);
      });
      
      // Verify component state
      expect(vehicleController.isInitialized()).toBe(false);
      expect(mockMap.off).toHaveBeenCalled();
    });
    
    test('should handle disposal during active animation', async () => {
      jest.useFakeTimers();
      
      // Start an animation
      vehicleController.moveTo([10, 10], 45, {
        duration: 1000,
        easing: 'linear',
        animateBearing: true
      });
      
      // Dispose mid-animation
      vehicleController.dispose();
      
      // Fast-forward time
      jest.advanceTimersByTime(1000);
      
      // Verify cleanup
      expect(vehicleController.isInitialized()).toBe(false);
      
      jest.useRealTimers();
    });
  });
  
  describe('DOM Operations Performance', () => {
    test('should measure DOM operation performance', async () => {
      // Create a large number of elements
      const elements: HTMLElement[] = [];
      const elementCount = 50;
      
      // Measure element creation time
      const creationTime = await measureExecutionTime(() => {
        for (let i = 0; i < elementCount; i++) {
          elements.push(vehicleDOM.createMarkerElement(`test-vehicle-${i}`));
        }
      });
      
      console.log(`DOM element creation (${elementCount}): ${creationTime.toFixed(2)}ms total, ${(creationTime / elementCount).toFixed(2)}ms per element`);
      
      // Measure element update time
      const updateTime = await measureExecutionTime(() => {
        for (let i = 0; i < elementCount; i++) {
          vehicleDOM.updateElementPosition(elements[i], [i, i], mockMap);
          vehicleDOM.updateElementBearing(elements[i], i);
          vehicleDOM.updateElementStyle(elements[i], { color: `#${i}${i}${i}` });
        }
      });
      
      console.log(`DOM element updates (${elementCount}): ${updateTime.toFixed(2)}ms total, ${(updateTime / elementCount).toFixed(2)}ms per element`);
      
      // This is not a strict test, but helps identify performance regressions
      expect(creationTime / elementCount).toBeLessThan(10); // Should create elements in less than 10ms each
      expect(updateTime / elementCount).toBeLessThan(10); // Should update elements in less than 10ms each
    });
  });
});