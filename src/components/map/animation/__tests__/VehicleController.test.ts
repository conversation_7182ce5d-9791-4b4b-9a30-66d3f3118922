/**
 * VehicleController.test.ts
 * 
 * Unit tests for the VehicleController component
 */

import { VehicleController } from '../VehicleController';
import { VehicleControllerInterface, VehicleAnimationOptions } from '../interfaces/VehicleControllerInterface';
import { VehicleManagerInterface } from '../interfaces/VehicleManagerInterface';
import { Position } from '@/types/Position';
import { TerrainType } from '@/types/TerrainType';
import { VehicleType } from '@/types/VehicleTypes';
import { AnimationEventType } from '@/types/animation';

// Mock VehicleManager
class MockVehicleManager implements Partial<VehicleManagerInterface> {
  initialize = jest.fn().mockResolvedValue(undefined);
  isInitialized = jest.fn().mockReturnValue(true);
  reset = jest.fn().mockResolvedValue(undefined);
  dispose = jest.fn();
  addEventListener = jest.fn().mockReturnValue(() => {});
  removeEventListener = jest.fn().mockReturnValue(true);
  updatePosition = jest.fn().mockReturnValue(true);
  setVisible = jest.fn().mockReturnValue(true);
  applyStyle = jest.fn().mockReturnValue(true);
  updateTerrain = jest.fn().mockReturnValue(true);
  isVisible = jest.fn().mockReturnValue(true);
  getCurrentPosition = jest.fn().mockReturnValue([0, 0] as Position);
  getCurrentBearing = jest.fn().mockReturnValue(0);
  getCurrentTerrain = jest.fn().mockReturnValue('DEFAULT' as TerrainType);
  recoverVisibility = jest.fn().mockReturnValue(true);
}

// Mock mapboxgl
const mockMapboxgl = {
  Map: jest.fn().mockImplementation(() => ({
    getContainer: jest.fn().mockReturnValue(document.createElement('div')),
    project: jest.fn().mockImplementation((lngLat) => ({
      x: 100,
      y: 100
    })),
    unproject: jest.fn().mockImplementation((point) => ({
      lng: 0,
      lat: 0
    })),
    isStyleLoaded: jest.fn().mockReturnValue(true),
    on: jest.fn(),
    off: jest.fn()
  }))
};

jest.mock('mapbox-gl', () => mockMapboxgl);

// Mock AnimationDebugTools
jest.mock('../AnimationDebugTools', () => ({
  __esModule: true,
  default: {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
    vehicle: jest.fn()
  }
}));

// Mock requestAnimationFrame and cancelAnimationFrame
global.requestAnimationFrame = jest.fn().mockReturnValue(1);
global.cancelAnimationFrame = jest.fn();

describe('VehicleController', () => {
  let vehicleController: VehicleControllerInterface;
  let mockMap: any;
  let mockVehicleManager: MockVehicleManager;
  
  beforeEach(() => {
    // Setup document body for DOM testing
    document.body.innerHTML = '';
    
    // Create mock map
    mockMap = {
      getContainer: jest.fn().mockReturnValue(document.createElement('div')),
      project: jest.fn().mockImplementation((lngLat) => ({
        x: 100,
        y: 100
      })),
      unproject: jest.fn().mockImplementation((point) => ({
        lng: 0,
        lat: 0
      })),
      isStyleLoaded: jest.fn().mockReturnValue(true),
      on: jest.fn(),
      off: jest.fn()
    };
    
    // Create mock VehicleManager
    mockVehicleManager = new MockVehicleManager();
    
    // Create VehicleController with mock dependencies
    vehicleController = new VehicleController(mockVehicleManager as VehicleManagerInterface);
  });
  
  afterEach(() => {
    jest.clearAllMocks();
    document.body.innerHTML = '';
    jest.useRealTimers();
  });
  
  describe('initialize', () => {
    test('should initialize with a map instance', async () => {
      await vehicleController.initialize(mockMap);
      
      expect(mockVehicleManager.initialize).toHaveBeenCalledWith(mockMap);
      expect(vehicleController.isInitialized()).toBe(true);
    });
    
    test('should throw error if map is not provided', async () => {
      await expect(vehicleController.initialize(null as any)).rejects.toThrow();
    });
  });
  
  describe('moveTo', () => {
    beforeEach(async () => {
      await vehicleController.initialize(mockMap);
    });
    
    test('should move vehicle to position immediately when no animation options', () => {
      const position: Position = [10, 20];
      const bearing = 45;
      
      vehicleController.moveTo(position, bearing);
      
      expect(mockVehicleManager.updatePosition).toHaveBeenCalledWith(position, bearing, undefined);
    });
    
    test('should animate vehicle movement when animation options provided', () => {
      jest.useFakeTimers();
      
      const position: Position = [10, 20];
      const bearing = 45;
      const options: VehicleAnimationOptions = {
        duration: 1000,
        easing: 'linear',
        animateBearing: true
      };
      
      const onCompleteMock = jest.fn();
      options.onComplete = onCompleteMock;
      
      vehicleController.moveTo(position, bearing, options);
      
      // Animation should have started
      expect(global.requestAnimationFrame).toHaveBeenCalled();
      
      // Fast-forward time to complete animation
      jest.advanceTimersByTime(1000);
      
      // onComplete should have been called
      expect(onCompleteMock).toHaveBeenCalled();
    });
  });
  
  describe('setVisible', () => {
    beforeEach(async () => {
      await vehicleController.initialize(mockMap);
    });
    
    test('should set vehicle visibility', () => {
      vehicleController.setVisible(true);
      
      expect(mockVehicleManager.setVisible).toHaveBeenCalledWith(true);
    });
  });
  
  describe('applyStyle', () => {
    beforeEach(async () => {
      await vehicleController.initialize(mockMap);
    });
    
    test('should apply style to vehicle', () => {
      const style: Partial<VehicleStyle> = {
        color: '#ff0000',
        size: 30
      };
      
      vehicleController.applyStyle(style);
      
      expect(mockVehicleManager.applyStyle).toHaveBeenCalledWith(style);
    });
  });
  
  describe('updateTerrain', () => {
    beforeEach(async () => {
      await vehicleController.initialize(mockMap);
    });
    
    test('should update vehicle terrain', () => {
      const terrain: TerrainType = 'MOUNTAIN';
      
      vehicleController.updateTerrain(terrain);
      
      expect(mockVehicleManager.updateTerrain).toHaveBeenCalledWith(terrain);
    });
  });
  
  describe('isVisible', () => {
    beforeEach(async () => {
      await vehicleController.initialize(mockMap);
    });
    
    test('should return visibility state from manager', () => {
      mockVehicleManager.isVisible.mockReturnValue(true);
      
      const isVisible = vehicleController.isVisible();
      
      expect(isVisible).toBe(true);
      expect(mockVehicleManager.isVisible).toHaveBeenCalled();
    });
  });
  
  describe('getCurrentPosition', () => {
    beforeEach(async () => {
      await vehicleController.initialize(mockMap);
    });
    
    test('should return current position from manager', () => {
      const position: Position = [10, 20];
      mockVehicleManager.getCurrentPosition.mockReturnValue(position);
      
      const currentPosition = vehicleController.getCurrentPosition();
      
      expect(currentPosition).toBe(position);
      expect(mockVehicleManager.getCurrentPosition).toHaveBeenCalled();
    });
  });
  
  describe('getCurrentBearing', () => {
    beforeEach(async () => {
      await vehicleController.initialize(mockMap);
    });
    
    test('should return current bearing from manager', () => {
      const bearing = 45;
      mockVehicleManager.getCurrentBearing.mockReturnValue(bearing);
      
      const currentBearing = vehicleController.getCurrentBearing();
      
      expect(currentBearing).toBe(bearing);
      expect(mockVehicleManager.getCurrentBearing).toHaveBeenCalled();
    });
  });
  
  describe('getCurrentTerrain', () => {
    beforeEach(async () => {
      await vehicleController.initialize(mockMap);
    });
    
    test('should return current terrain from manager', () => {
      const terrain: TerrainType = 'MOUNTAIN';
      mockVehicleManager.getCurrentTerrain.mockReturnValue(terrain);
      
      const currentTerrain = vehicleController.getCurrentTerrain();
      
      expect(currentTerrain).toBe(terrain);
      expect(mockVehicleManager.getCurrentTerrain).toHaveBeenCalled();
    });
  });
  
  describe('recoverVisibility', () => {
    beforeEach(async () => {
      await vehicleController.initialize(mockMap);
    });
    
    test('should attempt to recover visibility', () => {
      vehicleController.recoverVisibility();
      
      expect(mockVehicleManager.recoverVisibility).toHaveBeenCalled();
    });
  });
  
  describe('reset', () => {
    beforeEach(async () => {
      await vehicleController.initialize(mockMap);
    });
    
    test('should reset controller state', async () => {
      await vehicleController.reset();
      
      expect(mockVehicleManager.reset).toHaveBeenCalled();
    });
  });
  
  describe('dispose', () => {
    beforeEach(async () => {
      await vehicleController.initialize(mockMap);
    });
    
    test('should dispose controller resources', () => {
      vehicleController.dispose();
      
      expect(mockVehicleManager.dispose).toHaveBeenCalled();
    });
  });
  
  describe('event handling', () => {
    beforeEach(async () => {
      await vehicleController.initialize(mockMap);
    });
    
    test('should register and forward event listeners to manager', () => {
      const mockCallback = jest.fn();
      const eventType = AnimationEventType.VEHICLE_POSITION_UPDATED;
      
      // Register event listener
      vehicleController.addEventListener(eventType, mockCallback);
      
      expect(mockVehicleManager.addEventListener).toHaveBeenCalledWith(eventType, mockCallback);
    });
    
    test('should remove event listeners from manager', () => {
      const mockCallback = jest.fn();
      const eventType = AnimationEventType.VEHICLE_POSITION_UPDATED;
      
      vehicleController.removeEventListener(eventType, mockCallback);
      
      expect(mockVehicleManager.removeEventListener).toHaveBeenCalledWith(eventType, mockCallback);
    });
  });
});