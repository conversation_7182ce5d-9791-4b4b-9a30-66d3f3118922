/**
 * OptimizedAnimationIntegration.ts
 * 
 * Provides utility functions to integrate all optimized animation modules:
 * - AnimationFrameManager for frame management
 * - SmoothVehicleController for vehicle movement
 * - EnhancedCameraBehavior for camera control
 * - AnimationPerformanceMonitor for performance monitoring
 * 
 * This follows the Animation Architecture rules for animation integration.
 */

import * as turf from '@turf/turf';
import mapboxgl from 'mapbox-gl';
import { animationFrameManager } from './AnimationFrameManager';
import { smoothVehicleController } from './SmoothVehicleController';
import { enhancedCameraBehavior } from '../utils/EnhancedCameraBehavior';
import { animationPerformanceMonitor } from './AnimationPerformanceMonitor';
import AnimationDebugTools from './AnimationDebugTools';
import { ANIMATION_CONSTANTS, PERFORMANCE_THRESHOLDS } from '../utils/constants';

// Animation state interface for proper typing
export interface AnimationIntegrationState {
  isAnimating: boolean;
  progress: number;
  position: [number, number] | null;
  bearing: number;
  speed: number;
  terrain: string;
  inCity: boolean;
  cityName?: string;
  isPaused: boolean;
  distanceTraveled: number;
}

// Configuration options for animation
export interface AnimationIntegrationOptions {
  map: mapboxgl.Map;
  routePoints: Array<[number, number]>;
  onProgress?: (progress: number, position: [number, number], bearing: number) => void;
  onComplete?: () => void;
  onError?: (error: Error) => void;
  duration?: number;
  debug?: boolean;
  initialPosition?: [number, number];
  initialBearing?: number;
  usePerformanceMonitoring?: boolean;
  showDebugPanel?: boolean;
  useSmoothVehicle?: boolean;
  useEnhancedCamera?: boolean;
}

// Private module state
let animationState: AnimationIntegrationState | null = null;
let animationFrameId: number | null = null;
let startTime: number = 0;
const resourceCleanupHandlers: Array<() => void> = [];
let isResourceCleanupRegistered = false;

// Animation performance monitor instance
const animationPerformanceMonitorInstance = animationPerformanceMonitor;

/**
 * Initialize and configure all optimized animation modules
 */
export const initializeOptimizedAnimation = (options: AnimationIntegrationOptions): (() => void) => {
  const {
    map,
    debug = false,
    usePerformanceMonitoring = true,
    showDebugPanel = false,
    useSmoothVehicle = true,
    useEnhancedCamera = true
  } = options;
  
  // State tracking
  animationState = {
    isAnimating: false,
    progress: 0,
    position: options.initialPosition || null,
    bearing: options.initialBearing || 0,
    speed: 0,
    terrain: 'default',
    inCity: false,
    isPaused: false,
    distanceTraveled: 0
  };
  
  // Initialize performance monitoring if enabled
  let performanceCleanup: (() => void) | null = null;
  
  if (usePerformanceMonitoring) {
    const monitorResult = animationPerformanceMonitorInstance.start({
      animationStateProvider: () => ({ ...animationState }),
      lowFpsCallback: (fps) => {
        if (debug) {
          AnimationDebugTools.log(`Low animation FPS detected: ${fps.toFixed(1)}`);
        }
      },
      showDebugPanel
    });
    
    // Assign cleanup function if one was returned
    if (typeof monitorResult === 'function') {
      performanceCleanup = monitorResult;
    } else {
      performanceCleanup = () => {
        AnimationDebugTools.log('Default cleanup for performance monitor');
      };
    }
  }
  
  // Initialize SmoothVehicleController if enabled
  if (useSmoothVehicle) {
    smoothVehicleController.initialize(map);
    
    // Set initial position if available
    if (options.initialPosition) {
      smoothVehicleController.updatePosition(
        options.initialPosition,
        options.initialBearing || 0,
        { immediate: true }
      );
    }
  }
  
  // Initialize EnhancedCameraBehavior if enabled
  if (useEnhancedCamera) {
    enhancedCameraBehavior.initialize(map);
    
    // Configure look-ahead behavior
    enhancedCameraBehavior.setLookAhead({
      enabled: true,
      distance: 0.25, // Look ahead 25% of the way
      adaptiveMode: 'terrain-based'
    });
  }
  
  if (debug) {
    AnimationDebugTools.log('Optimized animation modules initialized', {
      usePerformanceMonitoring,
      useSmoothVehicle,
      useEnhancedCamera,
      routePointCount: options.routePoints.length
    });
  }
  
  // Register a window event listener to ensure cleanup on page unload
  if (!isResourceCleanupRegistered) {
    window.addEventListener('beforeunload', () => {
      performFullCleanup();
    });
    isResourceCleanupRegistered = true;
  }
  
  // Return cleanup function
  return () => {
    // Stop performance monitoring
    if (performanceCleanup) {
      performanceCleanup();
    }
    
    // Clean up controllers
    if (useSmoothVehicle) {
      smoothVehicleController.cleanup();
    }
    
    if (useEnhancedCamera) {
      enhancedCameraBehavior.reset();
    }
    
    if (debug) {
      AnimationDebugTools.log('Optimized animation modules cleaned up');
    }
  };
};

/**
 * Start an optimized animation using all modules
 */
export const startOptimizedAnimation = (
  options: AnimationIntegrationOptions
): (() => void) => {
  const {
    map,
    routePoints,
    onProgress,
    onComplete,
    onError,
    duration = ANIMATION_CONSTANTS.BASE_ROUTE_ANIMATION_DURATION, // Use constant
    debug = false,
    useSmoothVehicle = true,
    useEnhancedCamera = true
  } = options;
  
  if (!map || !routePoints || routePoints.length < 2) {
    throw new Error('Cannot start animation: map or route points not provided');
  }
  
  // Initialize modules if needed
  const cleanup = initializeOptimizedAnimation(options);
  
  // Add cleanup to our resource handlers
  registerCleanupHandler(cleanup);
  
  // Track animation state
  animationState = {
    isAnimating: true,
    progress: 0,
    position: routePoints[0],
    bearing: 0,
    speed: 0,
    terrain: 'default',
    inCity: false,
    isPaused: false,
    distanceTraveled: 0
  };
  
  // Start performance monitoring with proper cleanup
  const monitorResult = animationPerformanceMonitorInstance.start({
    showDebugPanel: debug,
    animationStateProvider: () => animationState
  });
  
  // Ensure proper resource cleanup
  let performanceCleanup: (() => void) | null = null;
  
  if (typeof monitorResult === 'function') {
    performanceCleanup = monitorResult;
    registerCleanupHandler(performanceCleanup);
  }
  
  // Set up health check interval
  const healthCheckInterval = setInterval(() => {
    checkAnimationHealth();
  }, 5000);
  
  // Register interval cleanup
  registerCleanupHandler(() => {
    clearInterval(healthCheckInterval);
  });
  
  // Setup animation frame counting for health monitoring
  const lastFrameTime = Date.now();
  const frameCount = 0;
  
  // Start animation loop
  startTime = Date.now();
  
  // Return cleanup function for animation
  return () => {
    performFullCleanup();
    
    // Execute onComplete if provided
    if (onComplete) {
      try {
        onComplete();
      } catch (error) {
        console.error('Error in animation completion callback:', error);
      }
    }
  };
};

/**
 * Check animation health and recover if needed
 */
export const checkAnimationHealth = (): void => {
  if (!animationState || !animationState.isAnimating) return;
  
  const now = Date.now();
  const elapsed = now - startTime;
  
  // Log health status
  console.log(`🔍 [${Date.now()}] Animation health check:`, {
    elapsed,
    progress: animationState.progress,
    distance: animationState.distanceTraveled,
    frameRate: metrics.frameRate,
    isHealthy: metrics.isHealthy
  });
  
  // Check for stalled animation
  if (lastProgressDelta < PERFORMANCE_THRESHOLDS.MIN_PROGRESS_DELTA && 
      elapsed > PERFORMANCE_THRESHOLDS.STALLED_ANIMATION_THRESHOLD_MS) {
    
    console.warn(`⚠️ [${Date.now()}] Animation may be stalled - progress: ${animationState.progress.toFixed(2)}`);
    
    // TODO: Add recovery mechanism here if needed
    
    return {
      isStalled: true,
      progress: animationState.progress,
      lastProgressDelta
    };
  }
};

/**
 * Register a cleanup handler
 */
export const registerCleanupHandler = (handler: () => void): void => {
  resourceCleanupHandlers.push(handler);
};

/**
 * Perform full resource cleanup
 */
export const performFullCleanup = (): void => {
  // Cancel animation frame
  if (animationFrameId !== null) {
    cancelAnimationFrame(animationFrameId);
    animationFrameId = null;
  }
  
  // Reset animation state
  if (animationState) {
    animationState.isAnimating = false;
  }
  
  // Execute all cleanup handlers
  while (resourceCleanupHandlers.length > 0) {
    const handler = resourceCleanupHandlers.pop();
    try {
      if (handler) handler();
    } catch (error) {
      console.error('Error in animation cleanup handler:', error);
    }
  }
  
  // Log cleanup
  console.log(`🧹 [${Date.now()}] Animation resources cleaned up successfully`);
};

/**
 * Update animation state with external information
 */
export const updateAnimationContext = (
  contextUpdates: Partial<AnimationIntegrationState>
): void => {
  // This function allows TravelAnimator to inform the animation
  // about contextual information such as terrain, city state, etc.
  
  // Update SmoothVehicleController
  if (contextUpdates.terrain && contextUpdates.position) {
    smoothVehicleController.updatePosition(
      contextUpdates.position,
      contextUpdates.bearing || 0,
      { immediate: false }
    );
  }
  
  // Update EnhancedCameraBehavior
  if (contextUpdates.inCity !== undefined && contextUpdates.position) {
    enhancedCameraBehavior.followVehicle(
      contextUpdates.position,
      contextUpdates.bearing || 0,
      {
        terrain: contextUpdates.terrain,
        inCity: contextUpdates.inCity,
        cityName: contextUpdates.cityName,
        forceUpdate: false
      }
    );
  }
};

/**
 * Pause optimized animation
 */
export const pauseOptimizedAnimation = (): void => {
  animationFrameManager.stop();
};

/**
 * Resume optimized animation
 */
export const resumeOptimizedAnimation = (options: AnimationIntegrationOptions): (() => void) => {
  return startOptimizedAnimation(options);
}; 