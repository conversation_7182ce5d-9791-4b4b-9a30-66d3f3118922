// src/components/map/animation/POIDiscoveryFrameworkEvents.ts

/**
 * POI/City Discovery Framework Event System
 * -----------------------------------------
 *
 * This file provides a theme-agnostic, strongly-typed event system for POI/city discovery.
 * Use this in both your core logic (to emit events) and in client apps/themes (to listen for events).
 *
 * Usage (in core logic):
 *   import { emitPOIDiscoveryFrameworkEvent } from './POIDiscoveryFrameworkEvents';
 *   emitPOIDiscoveryFrameworkEvent({ type: 'poi-discovered', ... });
 *
 * Usage (in client app/theme):
 *   import { onPOIDiscoveryFrameworkEvent } from './POIDiscoveryFrameworkEvents';
 *   onPOIDiscoveryFrameworkEvent((event) => { ... });
 */

// --- Event Types ---
export type POIDiscoveryEventType =
  | 'poi-approaching'
  | 'poi-discovered'
  | 'city-approaching'
  | 'city-entered'
  | 'batch-poi-discovered';

// --- POI Type (minimal, extend as needed) ---
export interface POI {
  id: string;
  name: string;
  coordinates: { lat: number; lng: number };
  type?: string;
  category?: string;
  description?: string;
  images?: string[];
  image?: string;
  importance?: number;
  [key: string]: any;
}

// --- Event Interfaces ---
export interface POIDiscoveryEventBase {
  type: POIDiscoveryEventType;
  timestamp: number;
}

export interface POIApproachingEvent extends POIDiscoveryEventBase {
  type: 'poi-approaching';
  poi: POI;
  distance: number;
  position: [number, number];
}

export interface POIDiscoveredEvent extends POIDiscoveryEventBase {
  type: 'poi-discovered';
  poi: POI;
  position: [number, number];
}

export interface CityApproachingEvent extends POIDiscoveryEventBase {
  type: 'city-approaching';
  city: string;
  distance: number;
  position: [number, number];
}

export interface CityEnteredEvent extends POIDiscoveryEventBase {
  type: 'city-entered';
  city: string;
  position: [number, number];
}

export interface BatchPOIDiscoveredEvent extends POIDiscoveryEventBase {
  type: 'batch-poi-discovered';
  pois: POI[];
  area: string;
  position: [number, number];
}

export type POIDiscoveryFrameworkEvent =
  | POIApproachingEvent
  | POIDiscoveredEvent
  | CityApproachingEvent
  | CityEnteredEvent
  | BatchPOIDiscoveredEvent;

export type POIDiscoveryFrameworkEventListener = (event: POIDiscoveryFrameworkEvent) => void;

// --- Event Registration API ---
const frameworkEventListeners: POIDiscoveryFrameworkEventListener[] = [];

/**
 * Register a listener for POI/city discovery events (for client apps/themes)
 * Returns an unsubscribe function.
 */
export function onPOIDiscoveryFrameworkEvent(listener: POIDiscoveryFrameworkEventListener) {
  frameworkEventListeners.push(listener);
  // Return unsubscribe function
  return () => {
    const idx = frameworkEventListeners.indexOf(listener);
    if (idx !== -1) frameworkEventListeners.splice(idx, 1);
  };
}

/**
 * Emit a POI/city discovery event (for core logic)
 */
export function emitPOIDiscoveryFrameworkEvent(event: POIDiscoveryFrameworkEvent) {
  for (const listener of frameworkEventListeners) {
    try {
      listener(event);
    } catch (err) {
      // Log but do not break
      if (typeof console !== 'undefined') {
        console.warn('[POIDiscoveryFrameworkEvent] Error in listener', err);
      }
    }
  }
} 