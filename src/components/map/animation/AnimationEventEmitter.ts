/**
 * AnimationEventEmitter.ts
 * 
 * Provides centralized event handling for animation events.
 * This component allows for type-safe event subscription and publishing.
 */

import { AnimationEventType, EventData } from '@/types/AnimationEventTypes';
import AnimationDebugTools from './AnimationDebugTools';

/**
 * Event emitter for animation events
 */
export class AnimationEventEmitter {
  // Map of event types to arrays of callbacks
  private listeners: Map<AnimationEventType, Set<(data: EventData) => void>> = new Map();

  /**
   * Add an event listener
   * 
   * @param eventType The type of event to listen for
   * @param callback The callback to execute when the event occurs
   * @returns A function to remove this specific listener
   */
  public addEventListener(
    eventType: AnimationEventType, 
    callback: (data: EventData) => void
  ): () => void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }

    const callbacks = this.listeners.get(eventType)!;
    callbacks.add(callback);

    AnimationDebugTools.log('debug', `Added event listener for ${eventType}`);

    // Return a function to remove this specific listener
    return () => {
      this.removeEventListener(eventType, callback);
    };
  }

  /**
   * Remove an event listener
   * 
   * @param eventType The type of event to stop listening for
   * @param callback The callback to remove
   * @returns Whether the listener was successfully removed
   */
  public removeEventListener(
    eventType: AnimationEventType, 
    callback: (data: EventData) => void
  ): boolean {
    if (!this.listeners.has(eventType)) {
      return false;
    }

    const callbacks = this.listeners.get(eventType)!;
    const removed = callbacks.delete(callback);

    if (removed) {
      AnimationDebugTools.log('debug', `Removed event listener for ${eventType}`);
    }

    return removed;
  }

  /**
   * Emit an event to all registered listeners
   * 
   * @param eventType The type of event to emit
   * @param data The data to pass to listeners
   */
  public emit(eventType: AnimationEventType, data: EventData): void {
    if (!this.listeners.has(eventType)) {
      return;
    }

    const callbacks = this.listeners.get(eventType)!;
    
    // Make a copy of callbacks to avoid issues if callbacks modify the listener list
    const callbacksCopy = Array.from(callbacks);
    
    for (const callback of callbacksCopy) {
      try {
        callback(data);
      } catch (error) {
        AnimationDebugTools.log('error', `Error in event listener for ${eventType}`, error);
      }
    }
  }

  /**
   * Remove all event listeners
   * 
   * @param eventType Optional event type to clear listeners for. If not provided, all listeners will be removed.
   */
  public removeAllListeners(eventType?: AnimationEventType): void {
    if (eventType) {
      this.listeners.delete(eventType);
      AnimationDebugTools.log('debug', `Removed all listeners for ${eventType}`);
    } else {
      this.listeners.clear();
      AnimationDebugTools.log('debug', 'Removed all event listeners');
    }
  }

  /**
   * Get the number of listeners for a specific event type
   * 
   * @param eventType The type of event to count listeners for
   * @returns The number of listeners registered for the event type
   */
  public listenerCount(eventType: AnimationEventType): number {
    if (!this.listeners.has(eventType)) {
      return 0;
    }

    return this.listeners.get(eventType)!.size;
  }

  /**
   * Check if there are any listeners for a specific event type
   * 
   * @param eventType The type of event to check
   * @returns Whether there are any listeners for the event type
   */
  public hasListeners(eventType: AnimationEventType): boolean {
    return this.listenerCount(eventType) > 0;
  }
} 