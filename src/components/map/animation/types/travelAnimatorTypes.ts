/**
 * Types for TravelAnimator.tsx
 */

// Define Journey Phases for clarity in UI and logic
export enum JourneyPhase {
  IDLE = 'idle',
  PREPARING = 'preparing',
  READY = 'ready', // Prepared, waiting for user to start
  ANIMATING = 'animating',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  ERROR = 'error',
}

// Re-import types from AnimationManagerWrapper for use here
import type { RouteData, AnimationOptions } from '../AnimationManagerWrapper';

// Define handles that can be called from the parent component via ref
export interface TravelAnimatorHandles {
  getActualIsPaused: () => boolean;
  toggleActualPause: () => void;
  startAnimation: (route: RouteData, options?: Partial<AnimationOptions>) => Promise<boolean>;
} 