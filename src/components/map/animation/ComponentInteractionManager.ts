/**
 * ComponentInteractionManager.ts
 * 
 * Animation Event Bus & Communication System
 * -----------------------------------------
 * 
 * RESPONSIBILITY:
 * Acts as a centralized event bus for communication between different animation
 * components. Implements the Singleton pattern to ensure a single shared instance
 * across the application, facilitating reliable cross-component communication.
 * 
 * KEY FUNCTIONALITY:
 * - Dispatches animation events (start, progress, complete, etc.)
 * - Manages animation event listeners and subscriptions
 * - Provides debug logging for animation events
 * - Tracks event history for debugging purposes
 * - Implements error recovery mechanisms for event handling failures
 * 
 * EVENT TYPES:
 * - ANIMATION_START: When animation begins
 * - ANIMATION_PROGRESS: Regular position and timing updates during animation
 * - ANIMATION_COMPLETE: When animation reaches completion
 * - ANIMATION_PAUSE: When animation is paused
 * - ANIMATION_RESUME: When animation resumes after pausing
 * - POI_DISCOVERED: When a point of interest is encountered during animation
 * - CITY_APPROACHED: When approaching a city during animation
 * - ANIMATION_ERROR: When an error occurs during animation
 * 
 * INTEGRATION PATTERNS:
 * - Direct imports: POIDiscoveryManager, CityDriveByManager
 * - Reverse imports: AnimationController, TravelAnimator, AnimationManager
 *   (These components import and use this class, rather than vice versa)
 * 
 * INTEGRATION POINTS:
 * - AnimationController: Main source of animation events (imports this class)
 * - TravelAnimator: Primary consumer of animation events (imports this class)
 * - AnimationManager: Core animation state coordinator (imports this class)
 * - POIDiscoveryManager: Manages POI interactions (directly imported here)
 * - CityDriveByManager: Manages city proximity events (directly imported here)
 * 
 * INITIALIZATION ORDER:
 * This manager should be initialized before animation components that depend on it.
 * Recommended initialization sequence:
 * 1. ComponentInteractionManager
 * 2. AnimationDebugTools
 * 3. POIDiscoveryManager / CityDriveByManager
 * 4. AnimationManager
 * 5. UI Components (TravelAnimator, etc.)
 * 
 * USAGE:
 * ```typescript
 * // Get the singleton instance
 * const manager = ComponentInteractionManager.getInstance();
 * 
 * // Register a listener
 * const unsubscribe = manager.addEventListener(
 *   AnimationEventType.ANIMATION_PROGRESS,
 *   (data) => console.log("Progress:", data.progress)
 * );
 * 
 * // Alternative syntax using 'on' method
 * const unsubscribe = manager.on(
 *   AnimationEventType.ANIMATION_PROGRESS,
 *   (data) => console.log("Progress:", data.progress)
 * );
 * 
 * // Dispatch an event
 * manager.dispatchAnimationEvent(
 *   AnimationEventType.ANIMATION_PROGRESS,
 *   { progress: 0.5, position: [lng, lat], timestamp: Date.now() }
 * );
 * 
 * // Cleanup when component unmounts
 * unsubscribe();
 * ```
 */

import POIDiscoveryManager from './POIDiscoveryManager';
import CityDriveByManager from './CityDriveByManager';
import AnimationDebugTools from './AnimationDebugTools';
import { Position, MapPosition } from '@/types/Position';
import { 
  InteractionEventType,
  AnimationEventType,
  CityProximityStage,
  EventData,
  AnimationEventData,
  EventCallback,
  DiscoveryEventPayload,
  CityDriveByPayload,
  AnimationEventCallback,
  DiscoveryEvent,
  CityDriveByEvent
} from '@/types/AnimationEventTypes';
import type { PointOfInterest, Destination } from '@/types/POITypes';

// Journey phases
export enum JourneyPhase {
  NOT_STARTED = 'NOT_STARTED',
  PLANNING = 'PLANNING',
  ACTIVE = 'ACTIVE',
  PAUSED = 'PAUSED',
  INITIAL_CITY = 'INITIAL_CITY',
  JOURNEY = 'JOURNEY',
  COMPLETED = 'COMPLETED'
}

// Component visibility interface
interface ComponentVisibility {
  poiOverlay: boolean;
  notificationPanel: boolean;
  cityPanel: boolean;
  journeyControls: boolean;
}

// Event sequence logging
interface EventSequenceEntry {
  eventType: AnimationEventType;
  timestamp: number;
  elapsed: number;
  progress?: number;
  details?: string;
}

/**
 * ComponentInteractionManager - Singleton event bus
 * 
 * This class facilitates communication between the various animation components
 * (AnimationManager, VehicleManager, CameraController, etc.) without creating
 * direct dependencies between them.
 */
export class ComponentInteractionManager {
  private static instance: ComponentInteractionManager | null = null;
  private eventListeners: Map<string, Set<EventCallback<EventData>>> = new Map();
  private debugTools: typeof AnimationDebugTools;
  private interactionListeners: Map<string, Set<Function>> = new Map();
  private animationListeners: Map<string, Set<AnimationEventCallback>> = new Map();
  private eventHistory: AnimationEventData[] = [];
  private maxHistoryLength: number = 50;
  private debugMode: boolean = true;
  private journeyPhase: JourneyPhase = JourneyPhase.PLANNING;
  private poiManager: POIDiscoveryManager | null = null;
  private cityManager: CityDriveByManager | null = null;
  private componentVisibility: ComponentVisibility = {
    poiOverlay: false,
    notificationPanel: false,
    cityPanel: false,
    journeyControls: false
  };
  private customEventHandlers: Map<string, Function> = new Map();
  private lastInteractionTime: number = 0;
  
  // Event sequence logging
  private eventSequence: EventSequenceEntry[] = [];
  private sequenceStartTime: number | null = null;
  private animationSequenceLog: boolean = true; // Enable animation sequence logging
  private lastProgressValue: number = 0;
  private progressSampleRate: number = 0.05; // Log progress every 5%
  private lastProgressLogTime: number = 0;
  private progressLogInterval: number = 2000; // Min 2 seconds between progress logs
  
  private constructor() {
    // Debug logging disabled for production readiness
    // console.log(`🔧 [${Date.now()}] ComponentInteractionManager initialized`);
    
    this.debugTools = AnimationDebugTools;
    
    // Initialize collections
    this.interactionListeners = new Map();
    this.animationListeners = new Map();
    this.eventHistory = [];
    this.customEventHandlers = new Map();
    
    // Initialize empty event listener maps for each event type
    Object.values(InteractionEventType).forEach(eventType => {
      this.interactionListeners.set(eventType, new Set());
    });
    
    Object.values(AnimationEventType).forEach(eventType => {
      this.animationListeners.set(eventType, new Set());
    });
    
    // Initialize event listeners
    this.initEventListeners();
    
    // Debug logging disabled for production readiness
    // console.log(`🔍 Available AnimationEventTypes: ${Object.keys(AnimationEventType).join(', ')}`);
    // console.log(`🔍 ANIMATION_PROGRESS enum value: ${AnimationEventType.ANIMATION_PROGRESS}`);
  }
  
  /**
   * Get the singleton instance
   */
  public static getInstance(): ComponentInteractionManager {
    if (!ComponentInteractionManager.instance) {
      ComponentInteractionManager.instance = new ComponentInteractionManager();
    }
    return ComponentInteractionManager.instance;
  }
  
  /**
   * Initialize event listeners for POI and city events
   */
  private initEventListeners(): void {
    try {
      // Listen for POI discovery events
      if (this.getPOIDiscoveryManager()) {
        try {
          this.getPOIDiscoveryManager()?.onPOIDiscovered((event: DiscoveryEvent) => {
            this.handlePOIDiscovery(event);
          });
        } catch (error) {
          console.error(`❌ [${Date.now()}] Error adding POI discovery listener:`, error);
        }
      }
      
      // Listen for city drive-by events
      if (this.getCityDriveByManager()) {
        try {
          this.getCityDriveByManager()?.onCityDriveBy((event: CityDriveByEvent) => {
            this.handleCityDriveBy(event);
          });
        } catch (error) {
          console.error(`❌ [${Date.now()}] Error adding city drive-by listener:`, error);
        }
      }
      
      // Initialize DOM event listeners
      if (typeof window !== 'undefined') {
        window.addEventListener('poi-discovery', (event: Event) => {
          const customEvent = event as CustomEvent;
          this.emit(InteractionEventType.POI_DISCOVERED, customEvent.detail);
        });
        
        window.addEventListener('city-drive-by', (event: Event) => {
          const customEvent = event as CustomEvent;
          this.emit(InteractionEventType.CITY_APPROACHED, customEvent.detail);
        });
      }
      
      console.log('Component interaction event listeners initialized');
    } catch (error) {
      console.error(`❌ [${Date.now()}] Error initializing event listeners:`, error);
    }
  }
  
  /**
   * Enable or disable animation sequence logging
   */
  public setAnimationSequenceLogging(enabled: boolean): void {
    this.animationSequenceLog = enabled;
    if (enabled) {
      console.log(`📊 [${Date.now()}] Animation sequence logging ENABLED`);
      this.resetEventSequence();
    } else {
      console.log(`📊 [${Date.now()}] Animation sequence logging DISABLED`);
    }
  }
  
  /**
   * Reset the event sequence log
   */
  public resetEventSequence(): void {
    this.eventSequence = [];
    this.sequenceStartTime = null;
    this.lastProgressValue = 0;
    this.lastProgressLogTime = 0;
    console.log(`📊 [${Date.now()}] Event sequence log reset`);
  }
  
  /**
   * Log an animation event to the sequence log
   * Special attention is paid to start, progress, and complete events
   */
  private logEventSequence(eventType: AnimationEventType, data: Record<string, unknown>): void {
    // Always log what event is being received regardless of other conditions
    console.log(`🔄 EVENT RECEIVED FOR SEQUENCE: type=${eventType}, progress=${data.progress !== undefined ? (data.progress as number).toFixed(4) : 'N/A'}`);
    
    if (!this.animationSequenceLog) return;
    
    // Extra debug for ANIMATION_PROGRESS events - ensure correct comparison
    const isProgressEvent = eventType === AnimationEventType.ANIMATION_PROGRESS;
    
    if (isProgressEvent) {
      console.log(`🔍 ADDING PROGRESS TO SEQUENCE: progress=${(data.progress as number)?.toFixed(4) || 'unknown'}, sequence length=${this.eventSequence.length}`);
    }
    
    const now = performance.now();
    
    // Set the sequence start time if this is the first event or explicitly a start event
    if (this.sequenceStartTime === null || eventType === AnimationEventType.ANIMATION_START) {
      this.sequenceStartTime = now;
    }
    
    // Calculate elapsed time
    const elapsed = this.sequenceStartTime ? now - this.sequenceStartTime : 0;
    
    // Create entry with basic information
    const entry: EventSequenceEntry = {
      eventType,
      timestamp: now,
      elapsed: Math.round(elapsed)
    };
    
    // Add special handling for specific event types
    switch (eventType) {
      case AnimationEventType.ANIMATION_START:
        entry.details = 'Animation started';
        console.log(`📊 [${Date.now()}] 🚀 ANIMATION_START - Sequence initialized`);
        break;
        
      case AnimationEventType.ANIMATION_PROGRESS:
        // Only log progress at certain intervals to avoid spam
        const progress = typeof data.progress === 'number' ? data.progress : 0;
        
        // Log the actual progress value we're working with to diagnose issues
        console.log(`🔍 PROGRESS VALUE CHECK: raw=${data.progress}, converted=${progress}, type=${typeof progress}`);
        
        // Ensure progress is always stored as a number
        entry.progress = progress;
        entry.details = `Progress: ${(progress * 100).toFixed(1)}%`;
        
        // Store this progress event even if we don't log it to the console
        this.eventSequence.push(entry);
        
        const shouldLogByValue = Math.abs(progress - this.lastProgressValue) >= this.progressSampleRate;
        const shouldLogByTime = now - this.lastProgressLogTime >= this.progressLogInterval;
        
        if (shouldLogByValue || shouldLogByTime) {
          this.lastProgressValue = progress;
          this.lastProgressLogTime = now;
          console.log(`📊 [${Date.now()}] ⏱️ ANIMATION_PROGRESS - ${(progress * 100).toFixed(1)}% at ${Math.round(elapsed)}ms`);
        }
        
        // Skip further processing for progress events - we've already added them to the sequence
        return;
        
      case AnimationEventType.ANIMATION_COMPLETE:
        entry.details = `Animation completed after ${Math.round(elapsed)}ms`;
        console.log(`📊 [${Date.now()}] 🏁 ANIMATION_COMPLETE - Total time: ${Math.round(elapsed)}ms`);
        
        // Log the complete sequence summary
        setTimeout(() => this.printSequenceSummary(), 100);
        break;
        
      case AnimationEventType.ANIMATION_PAUSE:
        entry.details = 'Animation paused';
        console.log(`📊 [${Date.now()}] ⏸️ ANIMATION_PAUSE at ${Math.round(elapsed)}ms`);
        break;
        
      case AnimationEventType.ANIMATION_RESUME:
        entry.details = 'Animation resumed';
        console.log(`📊 [${Date.now()}] ▶️ ANIMATION_RESUME at ${Math.round(elapsed)}ms`);
        break;
        
      case AnimationEventType.ANIMATION_ERROR:
        entry.details = `ERROR: ${data.message || 'Unknown error'}`;
        console.log(`📊 [${Date.now()}] ❌ ANIMATION_ERROR: ${data.message || 'Unknown error'}`);
        break;
        
      case AnimationEventType.VEHICLE_MARKER_CREATED:
        entry.details = 'Vehicle marker created';
        console.log(`📊 [${Date.now()}] 🚗 VEHICLE_MARKER_CREATED at ${Math.round(elapsed)}ms`);
        break;
    }
    
    // Add the entry to the sequence
    this.eventSequence.push(entry);
  }
  
  /**
   * Print a summary of the event sequence
   */
  public printSequenceSummary(): void {
    if (!this.animationSequenceLog || this.eventSequence.length === 0) return;
    
    // Calculate total time
    const totalTime = this.eventSequence[this.eventSequence.length - 1].elapsed;
    
    // Count occurrences of each event type
    const eventCounts: Record<string, number> = {};
    for (const entry of this.eventSequence) {
      eventCounts[entry.eventType] = (eventCounts[entry.eventType] || 0) + 1;
    }
    
    // Debug: Log all unique event types in the sequence
    const uniqueEventTypes = Array.from(new Set(this.eventSequence.map(e => e.eventType)));
    console.log(`🔍 DEBUG - All event types in sequence: ${JSON.stringify(uniqueEventTypes)}`);
    
    // DETAILED DIAGNOSTICS: Log the actual value of AnimationEventType.ANIMATION_PROGRESS
    console.log(`🔍 ANIMATION_PROGRESS enum value: '${AnimationEventType.ANIMATION_PROGRESS}'`);
    
    // COMPREHENSIVE DIAGNOSTICS: Find and output all progress events
    // Use proper type comparison
    const allProgressTypeEvents = this.eventSequence.filter(e => 
      e.eventType === AnimationEventType.ANIMATION_PROGRESS
    );
    console.log(`🔍 Found ${allProgressTypeEvents.length} events with type='${AnimationEventType.ANIMATION_PROGRESS}'`);
    
    // Check the first few progress events in detail
    if (allProgressTypeEvents.length > 0) {
      console.log('🔍 DETAILED PROGRESS EVENT INSPECTION:');
      allProgressTypeEvents.slice(0, 3).forEach((e, i) => {
        console.log(`  Event ${i}:`);
        console.log(`    eventType: '${e.eventType}'`);
        console.log(`    eventType === ANIMATION_PROGRESS: ${e.eventType === AnimationEventType.ANIMATION_PROGRESS}`);
        console.log(`    progress: ${e.progress} (type: ${typeof e.progress})`);
        console.log(`    details: ${e.details}`);
        console.log(`    timestamp: ${e.timestamp}`);
        console.log(`    elapsed: ${e.elapsed}`);
      });
    }
    
    // DIAGNOSTICS: Test the filter that's used to find progress events
    const progressEvents = this.eventSequence.filter(e => 
      e.eventType === AnimationEventType.ANIMATION_PROGRESS
    );
    
    // Generate synthetic progress events if none exist
    let syntheticProgressEvents: EventSequenceEntry[] = [];
    if (progressEvents.length === 0) {
      console.warn("⚠️ No real progress events found, generating synthetic ones for visualization");
      
      // Find start and end events
      const startEvent = this.eventSequence.find(e => e.eventType === AnimationEventType.ANIMATION_START);
      const endEvent = this.eventSequence.find(e => e.eventType === AnimationEventType.ANIMATION_COMPLETE);
      
      if (startEvent && endEvent) {
        // Generate 10 synthetic progress points
        for (let i = 1; i <= 10; i++) {
          const progress = i / 10;
          const elapsed = Math.round(startEvent.elapsed + (endEvent.elapsed - startEvent.elapsed) * progress);
          
          syntheticProgressEvents.push({
            eventType: AnimationEventType.ANIMATION_PROGRESS,
            timestamp: startEvent.timestamp + (endEvent.timestamp - startEvent.timestamp) * progress,
            elapsed: elapsed,
            progress: progress,
            details: `SYNTHETIC Progress: ${(progress * 100).toFixed(1)}%`
          });
        }
        
        // Update event counts with synthetic events
        eventCounts[AnimationEventType.ANIMATION_PROGRESS] = syntheticProgressEvents.length;
      }
    }
    
    // Use real or synthetic progress events
    const displayProgressEvents = progressEvents.length > 0 ? progressEvents : syntheticProgressEvents;
    
    console.group(`📊 ANIMATION SEQUENCE SUMMARY (Total time: ${totalTime}ms)`);
    
    // Log important events in order with timestamps
    console.log('KEY EVENTS SEQUENCE:');
    
    // Extract start, pause/resume, and complete events for clarity
    const keyEventTypes = [
      AnimationEventType.ANIMATION_START,
      AnimationEventType.ANIMATION_PAUSE,
      AnimationEventType.ANIMATION_RESUME,
      AnimationEventType.ANIMATION_COMPLETE,
      AnimationEventType.ANIMATION_ERROR
    ];
    
    const keyEvents = this.eventSequence.filter(e => keyEventTypes.includes(e.eventType));
    
    keyEvents.forEach(event => {
      console.log(`  ${event.elapsed}ms: ${event.eventType} - ${event.details || ''}`);
    });
    
    // Log progression of progress updates
    console.log(`Found ${progressEvents.length} real progress events in sequence of ${this.eventSequence.length} total events`);
    
    if (displayProgressEvents.length > 0) {
      const isSynthetic = progressEvents.length === 0;
      console.log(`\nPROGRESS SAMPLING ${isSynthetic ? "(SYNTHETIC)" : ""}:`);
      let prevProgress = 0;
      let prevTime = 0;
      
      // Take at most 10 samples evenly distributed
      const sampleCount = Math.min(10, displayProgressEvents.length);
      const step = Math.max(1, Math.floor(displayProgressEvents.length / sampleCount));
      
      for (let i = 0; i < displayProgressEvents.length; i += step) {
        const event = displayProgressEvents[i];
        if (!event) continue; // Skip if event is undefined
        
        // FIXED: Assign default progress value of 0 if progress is undefined
        const eventProgress = event.progress !== undefined ? event.progress : 0;
        
        const progressRate = i > 0 
          ? ((eventProgress - prevProgress) / (event.elapsed - prevTime) * 1000).toFixed(4) 
          : 'N/A';
          
        console.log(`  ${event.elapsed}ms: ${(eventProgress * 100).toFixed(1)}% ${isSynthetic ? "(synthetic)" : ""} (rate: ${progressRate}%/s)`);
        
        prevProgress = eventProgress;
        prevTime = event.elapsed;
      }
      
      // Always include the last progress event if not already included
      const lastIdx = displayProgressEvents.length - 1;
      if (lastIdx > 0 && lastIdx % step !== 0) {
        const lastEvent = displayProgressEvents[lastIdx];
        // FIXED: Assign default progress value of 0 if progress is undefined
        const lastEventProgress = lastEvent.progress !== undefined ? lastEvent.progress : 0;
        const progressRate = ((lastEventProgress - prevProgress) / (lastEvent.elapsed - prevTime) * 1000).toFixed(4);
        console.log(`  ${lastEvent.elapsed}ms: ${(lastEventProgress * 100).toFixed(1)}% ${isSynthetic ? "(synthetic)" : ""} (rate: ${progressRate}%/s)`);
      }
    } else {
      console.log('\nPROGRESS SAMPLING: No progress events recorded');
    }
    
    console.log('\nEVENT COUNTS:');
    Object.entries(eventCounts).forEach(([eventType, count]) => {
      console.log(`  ${eventType}: ${count}`);
    });
    
    console.log(`\nTOTAL EVENTS: ${this.eventSequence.length + syntheticProgressEvents.length}`);
    console.groupEnd();
  }
  
  /**
   * Set the current journey phase and update component visibility
   */
  public setJourneyPhase(phase: JourneyPhase): void {
    const previousPhase = this.journeyPhase;
    this.journeyPhase = phase;
    
    // Update component visibility based on phase
    this.updateComponentVisibility();
    
    // Log the phase change
    console.log(`Journey phase changed: ${previousPhase} -> ${phase}`);
    
    // Emit phase change event
    this.emit(InteractionEventType.CITY_APPROACHED, {
      type: InteractionEventType.CITY_APPROACHED,
      payload: {
        stage: 'leaving',
        visible: false
      },
      timestamp: Date.now()
    });
  }
  
  /**
   * Get current journey phase
   */
  public getJourneyPhase(): JourneyPhase {
    return this.journeyPhase;
  }
  
  /**
   * Update component visibility based on journey phase
   */
  private updateComponentVisibility(): void {
    const visibility: ComponentVisibility = {
      poiOverlay: false,
      notificationPanel: false,
      cityPanel: false,
      journeyControls: false
    };
    
    // Set visibility based on journey phase
    switch (this.journeyPhase) {
      case JourneyPhase.PLANNING:
        // Initial state, no components visible
        break;
        
      case JourneyPhase.ACTIVE:
        // Show notification panel for discoveries during journey
        visibility.notificationPanel = true;
        visibility.journeyControls = true;
        break;
        
      case JourneyPhase.PAUSED:
        // Journey paused, no components except controls
        visibility.journeyControls = true;
        break;
        
      case JourneyPhase.COMPLETED:
        // Journey completed, no components except controls
        visibility.journeyControls = true;
        break;
    }
    
    // Update the component visibility state
    this.componentVisibility = visibility;
    
    console.log('Component visibility updated');
  }
  
  /**
   * Get current component visibility state
   */
  public getComponentVisibility(): ComponentVisibility {
    return { ...this.componentVisibility };
  }
  
  /**
   * Register event listener for interaction events
   */
  public addEventListener(type: string, callback: EventCallback<EventData>): () => void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, new Set());
    }

    const listeners = this.eventListeners.get(type)!;
    listeners.add(callback);

    this.debugTools.log('debug', `Added event listener for ${type}`);

    // Return cleanup function
    return () => {
      this.removeEventListener(type, callback);
    };
  }

  public removeEventListener(type: string, callback: EventCallback<EventData>): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      listeners.delete(callback);
      this.debugTools.log('debug', `Removed event listener for ${type}`);
    }
  }

  public emit(type: string, data: any): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          this.debugTools.log('error', `Error in event listener for ${type}`, error);
        }
      });
      this.debugTools.log('debug', `Emitted ${type} event`, data);
    }
  }

  public clearAllListeners(): void {
    this.eventListeners.clear();
    this.debugTools.log('info', 'Cleared all event listeners');
  }
  
  /**
   * Handle POI discovery event
   * 
   * @param event The POI discovery event data
   */
  private handlePOIDiscovery(event: DiscoveryEvent): void {
    try {
      // Log the discovery event
      console.log(`🔍 [${Date.now()}] POI discovered:`, event);
      
      // Emit the POI discovery event
      this.emit(InteractionEventType.POI_DISCOVERED, event);
      
      // Update component visibility if needed
      this.updateComponentVisibility();
    } catch (error) {
      console.error(`❌ [${Date.now()}] Error handling POI discovery:`, error);
    }
  }
  
  /**
   * Handle city drive-by event
   * 
   * @param event The city drive-by event data
   */
  private handleCityDriveBy(event: CityDriveByEvent): void {
    try {
      // Log the city drive-by event
      console.log(`🏙️ [${Date.now()}] City drive-by:`, event);
      
      // Emit the city approached event
      this.emit(InteractionEventType.CITY_APPROACHED, event);
      
      // Update component visibility based on city proximity
      this.updateComponentVisibility();
    } catch (error) {
      console.error(`❌ [${Date.now()}] Error handling city drive-by:`, error);
    }
  }

  /**
   * Dispatch an animation event
   * @param type The type of animation event
   * @param data The event data
   */
  public dispatchAnimationEvent(type: AnimationEventType, data: any): void {
    const listeners = this.animationListeners.get(type);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          this.debugTools.log('error', `Error in animation event listener for ${type}`, error);
        }
      });
      if (this.debugMode) {
        this.debugTools.log('debug', `Dispatched animation event ${type}`, data);
      }
    }
  }

  /**
   * Add event listener - alias for addEventListener to maintain compatibility
   * with legacy code and common event emitter patterns
   */
  public on(type: string, callback: EventCallback<EventData>): () => void {
    this.debugTools.log('debug', `Adding event listener for ${type} via 'on' method`);
    return this.addEventListener(type, callback);
  }

  /**
   * Remove all registered event listeners
   */
  public removeAllListeners(): void {
    this.eventListeners.clear();
    this.interactionListeners.clear();
    this.animationListeners.clear();
  }
  
  /**
   * Lazily get POIDiscoveryManager instance
   */
  private getPOIDiscoveryManager(): POIDiscoveryManager | null {
    try {
      return POIDiscoveryManager.getInstance();
    } catch (error) {
      if (this.debugMode) {
        console.warn('ComponentInteractionManager: POIDiscoveryManager not initialized yet:', error);
      }
      return null;
    }
  }

  /**
   * Lazily get CityDriveByManager instance
   */
  private getCityDriveByManager(): CityDriveByManager | null {
    try {
      return CityDriveByManager.getInstance();
    } catch (error) {
      if (this.debugMode) {
        console.warn('ComponentInteractionManager: CityDriveByManager not initialized yet:', error);
      }
      return null;
    }
  }
}

// Export the class as default export
export default ComponentInteractionManager;