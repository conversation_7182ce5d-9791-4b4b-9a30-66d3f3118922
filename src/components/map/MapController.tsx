import React, { useCallback, useEffect } from 'react';
import mapboxgl from 'mapbox-gl';
import { Destination } from '@/types/destination';

interface MapControllerProps {
  selectedDestination: Destination | null;
  map: mapboxgl.Map | null;
}

const MapController: React.FC<MapControllerProps> = ({ selectedDestination, map }) => {
  const zoomToCity = useCallback((city: Destination) => {
    if (!map) return;
    
    // Calculate the bounding box for the city's POIs
    const bounds = new mapboxgl.LngLatBounds();
    city.pois.forEach(poi => {
      bounds.extend([poi.longitude, poi.latitude]);
    });
    
    // Add some padding to the bounds
    bounds.extend([
      bounds.getWest() - 0.02,
      bounds.getSouth() - 0.02,
      bounds.getEast() + 0.02,
      bounds.getNorth() + 0.02
    ]);
    
    // Animate to the new bounds
    map.fitBounds(bounds, {
      padding: { top: 50, bottom: 50, left: 400, right: 400 },
      duration: 1000,
      maxZoom: 14 // Prevent zooming in too close
    });
  }, [map]);

  // Watch for changes in selected destination
  useEffect(() => {
    if (selectedDestination) {
      zoomToCity(selectedDestination);
    }
  }, [selectedDestination, zoomToCity]);

  return null; // This component only handles map effects
};

export default MapController; 