import React, { useState, useEffect, CSSProperties } from 'react';
import { motion } from 'framer-motion';
import { MapPin, Plus, Check, X } from 'lucide-react';
import useTheme from '@/hooks/useTheme';
import { Destination, isValidCoordinates } from '@/types/POITypes';
import { Button } from '@/components/ui/button';

interface CityTagsOverlayProps {
  cities: Destination[];
  selectedCities: Destination[];
  onCitySelect: (cities: Destination[]) => void;
  onZoomToCity: (coordinates: [number, number]) => void;
  onShowCityPOIs: (city: Destination) => void;
  isMobile?: boolean;
  isDark: boolean;
  onMobileSelectionsConfirm?: (cities: Destination[]) => void;
}

// Mobile City Selection Modal (Internal Component)
interface MobileCitySelectionModalProps {
  isVisible: boolean;
  cities: Destination[];
  initialSelectedCities: Destination[];
  onConfirmSelections: (selectedCities: Destination[]) => void;
  onClose: () => void;
  isDark: boolean;
}

const MobileCitySelectionModal: React.FC<MobileCitySelectionModalProps> = ({
  isVisible,
  cities,
  initialSelectedCities,
  onConfirmSelections,
  onClose,
  isDark
}) => {
  const [localSelectedCities, setLocalSelectedCities] = useState<Destination[]>(initialSelectedCities);

  // Effect to update localSelectedCities if initialSelectedCities prop changes while modal is open
  // (e.g., if selections could somehow be modified externally - unlikely for this modal but good practice)
  useEffect(() => {
    setLocalSelectedCities(initialSelectedCities);
  }, [initialSelectedCities]);

  if (!isVisible) return null;

  const handleToggleCity = (city: Destination) => {
    setLocalSelectedCities(prev => 
      prev.find(c => c.id === city.id)
        ? prev.filter(c => c.id !== city.id)
        : [...prev, city]
    );
  };

  const handleConfirm = () => {
    // Here, we need to call a new prop, e.g., onConfirm, with localSelectedCities
    // For now, let's assume onCitySelect will be adapted or replaced
    // This will need to be reconciled with how HomePage.toggleDestination works
    if (onConfirmSelections) {
      onConfirmSelections(localSelectedCities);
    }
    onClose();
  };

  return (
    <div style={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0, backgroundColor: 'rgba(0,0,0,0.5)', zIndex: 100, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
      <div style={{ background: isDark ? '#1F2937' : 'white', padding: '20px', borderRadius: '8px', width: '90%', maxWidth: '400px', maxHeight: '80vh', display: 'flex', flexDirection: 'column' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <h4 style={{ fontSize: '18px', fontWeight: '600', color: isDark ? 'white' : 'black' }}>Select a City</h4>
          <button onClick={onClose} style={{ background: 'none', border: 'none', cursor: 'pointer' }}>
            <X size={24} color={isDark ? 'white' : 'black'} />
          </button>
        </div>
        <div style={{ overflowY: 'auto', flexGrow: 1 }}>
          {cities.map(city => {
            const isLocallySelected = localSelectedCities.some(c => c.id === city.id);
            return (
              <button 
                key={city.id} 
                onClick={() => { handleToggleCity(city); }}
                style={{
                  display: 'flex', // Changed to flex for checkbox alignment
                  alignItems: 'center', // Align items vertically
                  width: '100%',
                  padding: '12px 10px',
                  textAlign: 'left',
                  background: isLocallySelected ? (isDark ? '#2D3748' : '#E2E8F0') : 'none',
                  border: 'none',
                  borderBottom: `1px solid ${isDark ? '#374151' : '#E5E7EB'}`,
                  color: isDark ? '#F9FAFB' : '#1F2937',
                  cursor: 'pointer'
                }}
                className="hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <input 
                  type="checkbox"
                  checked={isLocallySelected}
                  readOnly // Prevent direct interaction, button click handles state
                  style={{ marginRight: '10px' }} 
                />
                {city.name}
              </button>
            );
          })}
        </div>
        <div style={{ display: 'flex', justifyContent: 'center', marginTop: '16px' }}>
          <Button onClick={handleConfirm}>Confirm</Button>
        </div>
      </div>
    </div>
  );
};

const CityTagsOverlay: React.FC<CityTagsOverlayProps> = ({
  cities,
  selectedCities,
  onCitySelect,
  onZoomToCity,
  onShowCityPOIs,
  isMobile,
  isDark,
  onMobileSelectionsConfirm
}) => {
  const [showMobileSelector, setShowMobileSelector] = useState(false);
  const [cityHovering, setCityHovering] = useState<string | null>(null);

  // Group cities by a valid property (e.g., address or use 'Other')
  const cityGroups = cities.reduce((acc, city) => {
    // Use address as region if available, otherwise 'Other'
    const region = city.address || 'Other';
    if (!acc[region]) {
      acc[region] = [];
    }
    acc[region].push(city);
    return acc;
  }, {} as Record<string, Destination[]>);

  // Check if city is selected
  const isCitySelected = (cityId: string) => {
    return selectedCities.some(city => city.id === cityId);
  };

  // Get selection order number (1, 2, 3...) if city is selected
  const getCitySelectionOrder = (cityId: string) => {
    const index = selectedCities.findIndex(city => city.id === cityId);
    return index !== -1 ? index + 1 : null;
  };

  // Handle city tag click
  const handleCityClick = (city: Destination) => {
    console.log(`City tag clicked: ${city.name}`);
    
    // First zoom to city on map - visual feedback
    if (isValidCoordinates(city.coordinates)) {
      onZoomToCity(city.coordinates);
      console.log(`Zooming to coordinates: [${city.coordinates[0]}, ${city.coordinates[1]}]`);
    } else {
      console.warn(`Invalid coordinates for city ${city.name}:`, city.coordinates);
    }
    
    // Toggle selection
    onCitySelect([city]);
    console.log(`Selected city: ${city.name}`);
    
    // Show POIs for this city
    onShowCityPOIs(city);
    console.log(`Showing POIs for ${city.name}`);
  };

  // This will be passed to the modal
  const handleMobileConfirmSelectionsInternal = (newlySelectedCities: Destination[]) => {
    if (onMobileSelectionsConfirm) {
      onMobileSelectionsConfirm(newlySelectedCities);
    }
    setShowMobileSelector(false);
  };

  // CSS styles objects
  const styles = {
    overlay: {
      position: 'absolute',
      top: '20px',
      left: 0,
      right: 0,
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      zIndex: 20,
      padding: '0 20px',
    } as CSSProperties,
    
    container: {
      background: isDark ? 'rgba(17, 24, 39, 0.9)' : 'rgba(255, 255, 255, 0.9)',
      backdropFilter: 'blur(8px)',
      borderRadius: '12px',
      padding: '16px 20px',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
      width: 'fit-content',
      maxWidth: '800px',
    } as CSSProperties,
    
    regionGroup: {
      marginBottom: '16px',
    } as CSSProperties,
    
    regionGroupLast: {
      marginBottom: 0,
    } as CSSProperties,
    
    regionTitle: {
      fontSize: '12px',
      fontWeight: 600,
      textTransform: 'uppercase' as const,
      color: isDark ? '#9CA3AF' : '#6B7280',
      marginBottom: '8px',
      letterSpacing: '0.05em',
    } as CSSProperties,
    
    cityTags: {
      display: 'flex',
      flexWrap: 'wrap' as const,
      gap: '8px',
    } as CSSProperties,
    
    cityTag: {
      backgroundColor: isDark ? '#1F2937' : 'white',
      borderRadius: '24px',
      padding: '6px 14px',
      cursor: 'pointer',
      userSelect: 'none' as const,
      border: `1px solid ${isDark ? '#374151' : '#E5E7EB'}`,
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',
      transition: 'all 0.2s ease',
    } as CSSProperties,
    
    cityTagSelected: {
      backgroundColor: isDark ? '#1E3A8A' : '#EFF6FF',
      border: `1px solid ${isDark ? '#60A5FA' : '#3B82F6'}`,
    } as CSSProperties,
    
    cityTagContent: {
      display: 'flex',
      alignItems: 'center',
      gap: '6px',
    } as CSSProperties,
    
    cityIcon: {
      color: '#3B82F6',
    } as CSSProperties,
    
    cityName: {
      fontWeight: 500,
      color: isDark ? '#F9FAFB' : '#1F2937',
    } as CSSProperties,
    
    selectionIndicator: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      width: '18px',
      height: '18px',
      backgroundColor: '#3B82F6',
      borderRadius: '50%',
    } as CSSProperties,
    
    selectionNumber: {
      color: 'white',
      fontSize: '10px',
      fontWeight: 600,
    } as CSSProperties,
    
    citySelectButton: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      width: '18px',
      height: '18px',
      backgroundColor: '#3B82F6',
      borderRadius: '50%',
      color: 'white',
    } as CSSProperties,
    
    journeyPath: {
      position: 'relative',
      height: '100px',
      width: '80%',
      maxWidth: '700px',
      marginTop: '30px',
    } as CSSProperties,
    
    pathLine: {
      position: 'absolute',
      top: '20px',
      left: 0,
      right: 0,
      height: '3px',
      background: 'linear-gradient(to right, #3B82F6, #10B981)',
      borderRadius: '2px',
    } as CSSProperties,
    
    pathPoint: (index: number, total: number): CSSProperties => ({
      position: 'absolute',
      top: 0,
      left: `${index * (100 / (total - 1))}%`,
      transform: 'translateX(-50%)',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
    }),
    
    pathMarker: {
      width: '40px',
      height: '40px',
      borderRadius: '50%',
      backgroundColor: '#3B82F6',
      border: `3px solid ${isDark ? '#1F2937' : 'white'}`,
      boxShadow: '0 2px 10px rgba(59, 130, 246, 0.3)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1,
    } as CSSProperties,
    
    pathNumber: {
      color: 'white',
      fontWeight: 700,
      fontSize: '16px',
    } as CSSProperties,
    
    pathLabel: {
      marginTop: '8px',
      backgroundColor: isDark ? '#1F2937' : 'white',
      padding: '4px 10px',
      borderRadius: '12px',
      fontSize: '12px',
      fontWeight: 600,
      boxShadow: '0 2px 5px rgba(0, 0, 0, 0.1)',
      whiteSpace: 'nowrap',
      color: isDark ? '#F9FAFB' : 'inherit',
    } as CSSProperties,
  };

  if (isMobile) {
    const currentSelectedJourneyCities = selectedCities; 
    let buttonText = "Select City";
    if (currentSelectedJourneyCities.length === 1) {
      buttonText = currentSelectedJourneyCities[0].name;
    } else if (currentSelectedJourneyCities.length > 1) {
      buttonText = `${currentSelectedJourneyCities.length} Cities Selected`;
    }

    // Mobile specific style override for the overlay
    const mobileOverlayStyle: CSSProperties = {
      ...styles.overlay,
      top: '74px', // Override top
      padding: '0px', // Override padding to remove horizontal for mobile
    };

    return (
      <div style={mobileOverlayStyle}> 
        <Button 
          onClick={() => setShowMobileSelector(true)}
          variant="outline" 
          className="shadow-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 mx-auto"
        >
          <MapPin size={16} className="mr-2" />
          {buttonText}
        </Button>
        <MobileCitySelectionModal 
          isVisible={showMobileSelector}
          cities={cities}
          initialSelectedCities={selectedCities}
          onConfirmSelections={handleMobileConfirmSelectionsInternal}
          onClose={() => setShowMobileSelector(false)}
          isDark={isDark}
        />
      </div>
    );
  }

  // Desktop view (existing layout)
  return (
    <div style={styles.overlay}>
      <div style={styles.container}>
        {(Object.entries(cityGroups) as [string, Destination[]][]).map(([region, regionCities], groupIndex) => (
          <div 
            key={region} 
            style={groupIndex === Object.keys(cityGroups).length - 1 ? styles.regionGroupLast : styles.regionGroup}
          >
            <h3 style={styles.regionTitle}>{region}</h3>
            <div style={styles.cityTags}>
              {regionCities.map((city: Destination) => {
                const isSelected = isCitySelected(city.id);
                const selectionOrder = getCitySelectionOrder(city.id);
                
                return (
                  <motion.div
                    key={city.id}
                    style={{
                      ...styles.cityTag,
                      ...(isSelected ? styles.cityTagSelected : {})
                    }}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.98 }}
                    onMouseEnter={() => setCityHovering(city.id)}
                    onMouseLeave={() => setCityHovering(null)}
                    onClick={() => handleCityClick(city)}
                  >
                    <div style={styles.cityTagContent}>
                      {isSelected ? (
                        <div style={styles.selectionIndicator}>
                          <span style={styles.selectionNumber}>{selectionOrder}</span>
                        </div>
                      ) : (
                        <MapPin 
                          size={16} 
                          style={styles.cityIcon} 
                        />
                      )}
                      <span style={styles.cityName}>{city.name}</span>
                      
                      {/* Selection button */}
                      <motion.div 
                        style={styles.citySelectButton}
                        initial={{ opacity: 0 }}
                        animate={{ 
                          opacity: cityHovering === city.id || isSelected ? 1 : 0,
                          rotate: isSelected ? 0 : 0
                        }}
                        transition={{ duration: 0.2 }}
                      >
                        {isSelected ? (
                          <Check size={14} />
                        ) : (
                          <Plus size={14} />
                        )}
                      </motion.div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>
        ))}
      </div>
      
      {selectedCities.length > 1 && (
        <div style={styles.journeyPath}>
          <div style={styles.pathLine}></div>
          {selectedCities.map((city, index) => (
            <div key={city.id} style={styles.pathPoint(index, selectedCities.length)}>
              <div style={styles.pathMarker}>
                <span style={styles.pathNumber}>{index + 1}</span>
              </div>
              <div style={styles.pathLabel}>{city.name}</div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default CityTagsOverlay; 