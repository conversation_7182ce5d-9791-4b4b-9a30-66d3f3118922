import React, { useEffect } from 'react';
import { PointOfInterest } from '@/components/PointOfInterestCard';
import mapboxgl from 'mapbox-gl';
import * as turf from '@turf/turf';

interface POINotificationProps {
  poi: PointOfInterest;
  distance: number;
  map: mapboxgl.Map;
  onShowDetails: (poi: PointOfInterest) => void;
}

/**
 * Shows a notification when approaching a POI
 */
export const POINotification: React.FC<POINotificationProps> = ({ 
  poi, 
  distance,
  map, 
  onShowDetails
}) => {
  useEffect(() => {
    try {
      // Remove existing notifications
      const existingNotifications = document.querySelectorAll('.poi-approach-notification');
      existingNotifications.forEach(el => {
        if (el.parentNode) el.parentNode.removeChild(el);
      });
      
      // Create notification element
      const notificationEl = document.createElement('div');
      notificationEl.className = 'poi-approach-notification';
      notificationEl.style.position = 'absolute';
      notificationEl.style.bottom = '80px';
      notificationEl.style.left = '50%';
      notificationEl.style.transform = 'translateX(-50%)';
      notificationEl.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
      notificationEl.style.borderRadius = '8px';
      notificationEl.style.padding = '12px 16px';
      notificationEl.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
      notificationEl.style.display = 'flex';
      notificationEl.style.alignItems = 'center';
      notificationEl.style.gap = '12px';
      notificationEl.style.zIndex = '1001';
      notificationEl.style.maxWidth = '320px';
      notificationEl.style.animation = 'fadeIn 0.3s ease-out';
      
      // Icon based on POI type
      const iconBg = poi.type === 'landmark' ? '#3B82F6' : 
                    poi.type === 'activity' ? '#10B981' : 
                    poi.type === 'restaurant' ? '#F59E0B' : '#6366F1';
      
      notificationEl.innerHTML = `
        <div style="width: 32px; height: 32px; background-color: ${iconBg}; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">
          ${poi.type.charAt(0).toUpperCase()}
        </div>
        <div style="flex-grow: 1;">
          <div style="font-weight: bold; margin-bottom: 4px;">Approaching: ${poi.name}</div>
          <div style="font-size: 12px; color: #666;">${Math.round(distance)}km away</div>
        </div>
      `;
      
      // Add details button
      const detailsButton = document.createElement('button');
      detailsButton.innerText = 'Details';
      detailsButton.style.backgroundColor = '#3B82F6';
      detailsButton.style.color = 'white';
      detailsButton.style.border = 'none';
      detailsButton.style.borderRadius = '4px';
      detailsButton.style.padding = '6px 12px';
      detailsButton.style.fontSize = '12px';
      detailsButton.style.fontWeight = 'bold';
      detailsButton.style.cursor = 'pointer';
      detailsButton.addEventListener('click', () => onShowDetails(poi));
      notificationEl.appendChild(detailsButton);
      
      // Add to map container
      if (map && map.getContainer()) {
        map.getContainer().appendChild(notificationEl);
        
        // Show direction indicator
        showDirectionIndicator(poi.coordinates as [number, number]);
        
        // Auto-remove after 4 seconds
        setTimeout(() => {
          if (notificationEl.parentNode) {
            notificationEl.style.animation = 'fadeOut 0.3s ease-in forwards';
            setTimeout(() => {
              if (notificationEl.parentNode) {
                notificationEl.parentNode.removeChild(notificationEl);
              }
            }, 300);
          }
        }, 4000);
      }
    } catch (error) {
      console.error('Error showing approaching POI notification:', error);
    }
    
    // Clean up on unmount
    return () => {
      const notifications = document.querySelectorAll('.poi-approach-notification');
      notifications.forEach(el => {
        if (el.parentNode) el.parentNode.removeChild(el);
      });
    };
  }, [poi, distance, map, onShowDetails]);
  
  return null; // This is a non-visual component
};

/**
 * Shows direction indicator pointing to a POI
 */
export const showDirectionIndicator = (
  poiCoordinates: [number, number],
  map?: mapboxgl.Map,
  currentPoint?: [number, number]
) => {
  try {
    if (!map || !currentPoint) return;
    
    // Remove existing indicators
    const existingIndicators = document.querySelectorAll('.direction-indicator');
    existingIndicators.forEach(el => {
      if (el.parentNode) el.parentNode.removeChild(el);
    });
    
    // Calculate bearing between current location and POI
    const bearing = turf.bearing(
      turf.point(currentPoint),
      turf.point(poiCoordinates)
    );
    
    // Create indicator element
    const indicatorEl = document.createElement('div');
    indicatorEl.className = 'direction-indicator';
    indicatorEl.style.position = 'absolute';
    indicatorEl.style.width = '40px';
    indicatorEl.style.height = '40px';
    indicatorEl.style.borderRadius = '50%';
    indicatorEl.style.backgroundColor = 'rgba(59, 130, 246, 0.2)';
    indicatorEl.style.border = '2px solid rgba(59, 130, 246, 0.6)';
    indicatorEl.style.boxShadow = '0 0 12px rgba(59, 130, 246, 0.4)';
    indicatorEl.style.transform = 'translate(-50%, -50%)';
    indicatorEl.style.zIndex = '998';
    indicatorEl.style.pointerEvents = 'none';
    
    // Add arrow inside
    const arrowEl = document.createElement('div');
    arrowEl.style.position = 'absolute';
    arrowEl.style.top = '50%';
    arrowEl.style.left = '50%';
    arrowEl.style.width = '16px';
    arrowEl.style.height = '16px';
    arrowEl.style.borderTop = '3px solid rgba(59, 130, 246, 0.9)';
    arrowEl.style.borderRight = '3px solid rgba(59, 130, 246, 0.9)';
    arrowEl.style.transform = `translate(-50%, -50%) rotate(${bearing + 45}deg)`;
    
    indicatorEl.appendChild(arrowEl);
    
    // Position at edge of viewport in direction of POI
    const mapContainer = map.getContainer();
    const centerX = mapContainer.offsetWidth / 2;
    const centerY = mapContainer.offsetHeight / 2;
    const radius = Math.min(centerX, centerY) * 0.8;
    
    // Calculate position on the circle edge based on bearing
    const x = centerX + radius * Math.sin(bearing * Math.PI / 180);
    const y = centerY - radius * Math.cos(bearing * Math.PI / 180);
    
    indicatorEl.style.left = `${x}px`;
    indicatorEl.style.top = `${y}px`;
    
    // Add to map container
    mapContainer.appendChild(indicatorEl);
    
    // Remove after 3 seconds
    setTimeout(() => {
      if (indicatorEl.parentNode) {
        indicatorEl.parentNode.removeChild(indicatorEl);
      }
    }, 3000);
  } catch (error) {
    console.error('Error showing direction indicator:', error);
  }
};

export default POINotification; 