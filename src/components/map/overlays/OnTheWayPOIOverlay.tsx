/**
 * OnTheWayPOIOverlay.tsx
 * 
 * Purpose: Renders the overlay panel displayed on the left side of the screen
 *          during journey animation when passing through non-selected cities
 *          or discovering points of interest outside of cities.
 *          Designed for a more immersive exploration experience.
 * 
 * Usage: Rendered by TravelAnimator.tsx during journey animation.
 *        Visibility is controlled by the AnimationManager via ComponentInteractionManager.
 */
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MapPin, X, Clock, DollarSign, Star, ChevronRight, Pause, Play } from 'lucide-react';
import { PointOfInterest } from '@/types/POITypes';
import useTheme from '@/hooks/useTheme';

interface OnTheWayPOIOverlayProps {
  isVisible: boolean;
  pois: PointOfInterest[];
  vehiclePosition: [number, number] | null;
  selectedPOIs: PointOfInterest[];
  onClose: () => void;
  onSelectPOI: (poi: PointOfInterest) => void;
  onPauseJourney?: () => void;
  onResumeJourney?: () => void;
  isJourneyPaused?: boolean;
}

const OnTheWayPOIOverlay: React.FC<OnTheWayPOIOverlayProps> = ({
  isVisible,
  pois,
  vehiclePosition,
  selectedPOIs,
  onClose,
  onSelectPOI,
  onPauseJourney = () => {},
  onResumeJourney = () => {},
  isJourneyPaused = false
}) => {
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  const [activePOIIndex, setActivePOIIndex] = useState(0);

  // Limit number of POIs shown to prevent overwhelming the user
  const visiblePOIs = pois.slice(0, 5);

  // Auto-rotate POIs if multiple are discovered (every 5 seconds)
  useEffect(() => {
    if (visiblePOIs.length <= 1 || isJourneyPaused) return;
    
    const timer = setInterval(() => {
      setActivePOIIndex(prev => (prev + 1) % visiblePOIs.length);
    }, 5000);
    
    return () => clearInterval(timer);
  }, [visiblePOIs.length, isJourneyPaused]);

  // When POIs change, reset to the first one
  useEffect(() => {
    setActivePOIIndex(0);
  }, [pois]);

  // Helper to determine if a POI is selected
  const isPOISelected = (poi: PointOfInterest) => 
    selectedPOIs.some(selected => selected.id === poi.id);

  // When no POIs, don't render
  if (!isVisible || visiblePOIs.length === 0) return null;
  
  // Current POI to display
  const currentPOI = visiblePOIs[activePOIIndex];

  return (
    <AnimatePresence>
      <motion.div
        key="poi-overlay"
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        exit={{ y: 50, opacity: 0 }}
        transition={{ duration: 0.3 }}
        className={`fixed bottom-4 left-1/2 transform -translate-x-1/2 z-30 
                   rounded-lg shadow-lg max-w-md w-11/12 overflow-hidden
                   ${isDark ? 'bg-gray-800 text-white' : 'bg-white text-gray-800'}`}
      >
        {/* Header with Title and Controls */}
        <div className={`flex items-center justify-between p-3 border-b ${isDark ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="flex items-center space-x-2">
            <MapPin className="text-teal-500" size={18} />
            <h3 className="font-semibold text-lg">Discovered Near You</h3>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Journey Control Button */}
            <button 
              onClick={isJourneyPaused ? onResumeJourney : onPauseJourney}
              className={`p-1.5 rounded-full ${isDark ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
            >
              {isJourneyPaused ? (
                <Play size={18} className="text-teal-500" />
              ) : (
                <Pause size={18} className="text-teal-500" />
              )}
            </button>
            
            {/* Close Button */}
            <button 
              onClick={onClose}
              className={`p-1.5 rounded-full ${isDark ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
            >
              <X size={18} />
            </button>
          </div>
        </div>
        
        {/* POI Content */}
        <div className="p-4">
          {currentPOI && (
            <div className="flex flex-col">
              {/* POI Title */}
              <h4 className="font-bold text-lg mb-2">{currentPOI.name}</h4>
              
              {/* POI Type and Distance */}
              <div className="flex items-center text-sm text-gray-500 mb-3">
                <span className={`rounded-full px-2 py-0.5 text-xs mr-2 ${isDark ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  {currentPOI.category || 'Point of Interest'}
                </span>
                
                {vehiclePosition && (
                  <span className="flex items-center">
                    <Clock size={14} className="mr-1" />
                    Nearby
                  </span>
                )}
              </div>
              
              {/* POI Image if available */}
              {currentPOI.images && currentPOI.images.length > 0 && (
                <div className="mb-3 rounded-lg overflow-hidden h-40 w-full">
                  <img 
                    src={currentPOI.images[0]} 
                    alt={currentPOI.name} 
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
              
              {/* POI Description */}
              <p className={`text-sm mb-3 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                {currentPOI.description || "Explore this interesting location during your journey."}
              </p>
              
              {/* POI Details */}
              <div className="flex items-center space-x-4 mb-4 text-sm">
                {currentPOI.rating && (
                  <div className="flex items-center">
                    <Star size={14} className="text-yellow-500 mr-1" />
                    <span>{currentPOI.rating}</span>
                  </div>
                )}
                
                {currentPOI.priceLevel && (
                  <div className="flex items-center">
                    <DollarSign size={14} className="text-green-500 mr-1" />
                    <span>{typeof currentPOI.priceLevel === 'number' ? `$${currentPOI.priceLevel}` : currentPOI.priceLevel}</span>
                  </div>
                )}
              </div>
              
              {/* POI Tags */}
              {currentPOI.tags && currentPOI.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-4">
                  {currentPOI.tags.map((tag: string) => (
                    <span 
                      key={tag} 
                      className={`text-xs px-2 py-1 rounded-full ${isDark ? 'bg-gray-700' : 'bg-gray-100'}`}
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )}
              
              {/* Action Buttons */}
              <div className="flex justify-between items-center mt-2">
                {/* POI Selector (if multiple POIs) */}
                {visiblePOIs.length > 1 && (
                  <div className="flex space-x-1">
                    {visiblePOIs.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => setActivePOIIndex(index)}
                        className={`w-2 h-2 rounded-full transition-all ${
                          index === activePOIIndex 
                            ? 'bg-teal-500 w-4' 
                            : isDark ? 'bg-gray-600' : 'bg-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                )}
                
                {/* Add to Selected Button */}
                <motion.button
                  whileTap={{ scale: 0.95 }}
                  onClick={() => onSelectPOI(currentPOI)}
                  className={`
                    flex items-center px-3 py-1.5 rounded-md font-medium text-sm
                    ${isPOISelected(currentPOI)
                      ? isDark 
                        ? 'bg-teal-700 text-white' 
                        : 'bg-teal-100 text-teal-800'
                      : isDark 
                        ? 'bg-teal-600 text-white' 
                        : 'bg-teal-500 text-white'
                    }
                  `}
                >
                  {isPOISelected(currentPOI) ? 'Added to Journey' : 'Add to Journey'}
                  <ChevronRight size={16} className="ml-1" />
                </motion.button>
              </div>
            </div>
          )}
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default OnTheWayPOIOverlay; 