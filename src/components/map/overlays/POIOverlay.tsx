/**
 * POIOverlay.tsx
 * 
 * Purpose: Renders the overlay panel displayed on the left side of the screen 
 *          when a user clicks on a destination marker in the ExploreMap view, 
 *          *before* starting the journey animation. It lists the POIs 
 *          associated with that specific destination, allowing users to select them 
 *          for their itinerary.
 * 
 * Usage: Rendered by ExploreMap.tsx when a destination is selected.
 *        Visibility is controlled by the `isVisible` prop passed from ExploreMap.
 */
import React, { useEffect, useState, useMemo } from 'react';
import { X, MapPin, Clock, Check, Search, Filter, SortAsc, LocateFixed, Star, Landmark, Mountain, Compass, DollarSign } from 'lucide-react';
import { Destination, PointOfInterest } from '@/types/POITypes';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import * as turf from '@turf/turf';

// Extended interface for POIs with distance calculation
interface POIWithDistance extends PointOfInterest {
  distance?: number;
  type?: string;  // For backward compatibility
}

interface POIOverlayProps {
  isVisible: boolean;
  destination: Destination;
  pois: PointOfInterest[];
  selectedPOIs: PointOfInterest[];
  onClose: () => void;
  onSelectPOI: (poi: PointOfInterest) => void;
}

// Define categories for filtering - redesigned for road trips
const CATEGORIES = [
  { id: 'monument', label: 'Monuments & Culture', icon: <Landmark size={12} className="mr-1" /> },
  { id: 'nature', label: 'Nature & Landscapes', icon: <Mountain size={12} className="mr-1" /> },
  { id: 'activity', label: 'Activities & Experiences', icon: <Compass size={12} className="mr-1" /> },
  { id: 'landmark', label: 'Historic Sites', icon: <Star size={12} className="mr-1" /> }
];

// Define duration filters for multi-day planning
const DURATIONS = [
  { id: 'quick', label: 'Quick (< 1h)', value: 1 },
  { id: 'medium', label: 'Medium (1-2h)', value: 2 },
  { id: 'long', label: 'Extended (> 2h)', value: 3 }
];

// Define pricing filters
const PRICING = [
  { id: 'all', label: 'All' },
  { id: 'free', label: 'Free Only' },
  { id: 'paid', label: 'Paid Only' }
];

const POIOverlay: React.FC<POIOverlayProps> = ({
  isVisible = false,
  destination,
  pois = [],
  selectedPOIs = [],
  onClose = () => console.warn('onClose not provided to POIOverlay'),
  onSelectPOI = () => console.warn('onSelectPOI not provided to POIOverlay'),
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedDuration, setSelectedDuration] = useState<string>('');
  const [selectedPricing, setSelectedPricing] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'name' | 'distance' | 'recommended'>('recommended');
  
  // Add clean debug logging
  useEffect(() => {
    console.log(`POIOverlay state: isVisible=${isVisible}, destination=${destination?.name || 'none'}`);
  }, [isVisible, destination]);

  // Calculate distances - always call this hook regardless of visibility
  const poisWithDistance = useMemo((): POIWithDistance[] => {
    if (!destination?.coordinates || !pois.length) return [];
    const destinationPoint = turf.point(destination.coordinates);
    
    // Ensure all POIs have required fields
    return pois.map(poi => {
      // Add required fields with fallbacks
      const normalizedPoi = {
        ...poi,
        category: poi.category || 'other',
        images: poi.images || [],
        tags: poi.tags || []
      };
      
      // Then add distance
      return {
        ...normalizedPoi,
        distance: turf.distance(destinationPoint, turf.point(normalizedPoi.coordinates), { units: 'kilometers' })
      };
    }) as POIWithDistance[];
  }, [pois, destination]);

  // Filtering logic - always call this hook
  const filteredPOIs = useMemo((): POIWithDistance[] => {
    if (!isVisible || !destination) return [];
    
    return poisWithDistance.filter(poi => {
      // Map POI category to our new categories (backward compatibility)
      const poiCategory = poi.category || 'other';
      
      // Category filter
      const categoryMatch = selectedCategories.length === 0 || 
                            selectedCategories.includes(poiCategory) || 
                            (poi.tags && poi.tags.some((tag: string) => selectedCategories.includes(tag)));
      
      // Duration filter
      let durationMatch = true;
      if (selectedDuration) {
        if (selectedDuration === 'quick' && (poi.duration && poi.duration >= 1)) durationMatch = false;
        if (selectedDuration === 'medium' && (poi.duration && (poi.duration < 1 || poi.duration > 2))) durationMatch = false;
        if (selectedDuration === 'long' && (poi.duration && poi.duration <= 2)) durationMatch = false;
      }
      
      // Pricing filter
      let pricingMatch = true;
      const poiCost = poi.cost || 0;
      if (selectedPricing === 'free' && poiCost > 0) pricingMatch = false;
      if (selectedPricing === 'paid' && poiCost === 0) pricingMatch = false;
      
      // Search term filter
      const searchMatch = !searchTerm ||
                          poi.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          (poi.category && poi.category.toLowerCase().includes(searchTerm.toLowerCase())) ||
                          (poi.tags && poi.tags.some((tag: string) => tag.toLowerCase().includes(searchTerm.toLowerCase())));
                          
      return categoryMatch && durationMatch && pricingMatch && searchMatch;
    });
  }, [poisWithDistance, selectedCategories, selectedDuration, selectedPricing, searchTerm, isVisible, destination]);

  // Sorting logic - always call this hook
  const sortedPOIs = useMemo((): POIWithDistance[] => {
    return [...filteredPOIs].sort((a, b) => {
      // Primary sort: Selected items first
      const aSelected = selectedPOIs.some(p => p.id === a.id);
      const bSelected = selectedPOIs.some(p => p.id === b.id);
      if (aSelected && !bSelected) return -1;
      if (!aSelected && bSelected) return 1;

      // Secondary sort based on sortBy state
      if (sortBy === 'distance') {
        return (a.distance ?? Infinity) - (b.distance ?? Infinity);
      }
      if (sortBy === 'recommended') {
        // Use a simple recommendation score based on popularity (cost as proxy for now)
        // Higher cost often means more significant attractions in context of road trips
        const scoreA = (a.tags?.length || 0) + (a.duration || 0);
        const scoreB = (b.tags?.length || 0) + (b.duration || 0);
        if (scoreA !== scoreB) return scoreB - scoreA;
      }
      // Default to sorting by name
      return a.name.localeCompare(b.name);
    });
  }, [filteredPOIs, selectedPOIs, sortBy]);

  // Handler for category toggle
  const toggleCategory = (category: string) => {
    setSelectedCategories(prev =>
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  };
  
  // Handler for duration selection
  const toggleDuration = (duration: string) => {
    setSelectedDuration(prev => prev === duration ? '' : duration);
  };
  
  // Handler for pricing selection
  const handlePricingChange = (pricing: string) => {
    setSelectedPricing(pricing);
  };

  // If the overlay is not visible, return null
  if (!isVisible || !destination) {
    return null;
  }

  return (
    <div 
      className="poi-overlay"
      style={{
        position: 'fixed',
        left: 0,
        top: '80px',
        width: '350px',
        height: 'calc(100vh - 80px)',
        background: 'white',
        borderRadius: '0 12px 12px 0',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.2)',
        overflow: 'auto',
        zIndex: 50,
        display: 'block',
        transform: 'translateX(0)',
        transition: 'transform 0.3s ease',
        border: '2px solid var(--morocco-blue)'
      }}
    >
      {/* Header with destination name */}
      <div className="p-4 bg-blue-600 text-white rounded-tr-xl flex justify-between items-center">
        <div className="flex items-center">
          <MapPin className="mr-2" size={18} />
          <h2 className="text-xl font-semibold">{destination.name}</h2>
        </div>
        <button 
          onClick={onClose}
          className="p-1 hover:bg-blue-700 rounded-full transition-colors"
          aria-label="Close"
        >
          <X size={20} />
        </button>
      </div>

      {/* Controls Area: Search, Filters, Sort */}
      <div className="p-4 border-b bg-white/50 space-y-4">
        {/* Search bar */}
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="text"
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-morocco-terracotta focus:border-morocco-terracotta sm:text-sm"
            placeholder="Search activities and landmarks..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        {/* Category Filters */}
        <div>
          <label className="text-xs font-medium text-gray-500 flex items-center mb-2">
            <Filter size={14} className="mr-1" /> Filter by Category
          </label>
          <div className="flex flex-wrap gap-2">
            {CATEGORIES.map(category => (
              <Badge
                key={category.id}
                variant={selectedCategories.includes(category.id) ? 'default' : 'outline'}
                onClick={() => toggleCategory(category.id)}
                className={`cursor-pointer transition-all ${selectedCategories.includes(category.id) ? 'bg-morocco-blue text-white' : ''}`}
              >
                {category.icon} {category.label}
              </Badge>
            ))}
             {selectedCategories.length > 0 && (
                <button onClick={() => setSelectedCategories([])} className="text-xs text-morocco-blue hover:underline">Clear</button>
             )}
          </div>
        </div>
        
        {/* Duration Filters */}
        <div>
          <label className="text-xs font-medium text-gray-500 flex items-center mb-2">
            <Clock size={14} className="mr-1" /> Duration
          </label>
          <div className="flex flex-wrap gap-2">
            {DURATIONS.map(duration => (
              <Badge
                key={duration.id}
                variant={selectedDuration === duration.id ? 'default' : 'outline'}
                onClick={() => toggleDuration(duration.id)}
                className={`cursor-pointer transition-all ${selectedDuration === duration.id ? 'bg-morocco-blue text-white' : ''}`}
              >
                {duration.label}
              </Badge>
            ))}
            {selectedDuration && (
              <button onClick={() => setSelectedDuration('')} className="text-xs text-morocco-blue hover:underline">Clear</button>
            )}
          </div>
        </div>
        
        {/* Pricing Filter */}
        <div>
          <label className="text-xs font-medium text-gray-500 flex items-center mb-2">
            <DollarSign size={14} className="mr-1" /> Pricing
          </label>
          <div className="flex flex-wrap gap-2">
            {PRICING.map(price => (
              <Badge
                key={price.id}
                variant={selectedPricing === price.id ? 'default' : 'outline'}
                onClick={() => handlePricingChange(price.id)}
                className={`cursor-pointer transition-all ${selectedPricing === price.id ? 'bg-morocco-blue text-white' : ''}`}
              >
                {price.label}
              </Badge>
            ))}
          </div>
        </div>

        {/* Sort */}
        <div>
           <label className="text-xs font-medium text-gray-500 flex items-center mb-1">
             <SortAsc size={14} className="mr-1" /> Sort by
           </label>
           <Select value={sortBy} onValueChange={(value) => setSortBy(value as 'name' | 'distance' | 'recommended')}>
             <SelectTrigger className="w-full h-9 text-sm">
               <SelectValue placeholder="Sort by..." />
             </SelectTrigger>
             <SelectContent>
               <SelectItem value="recommended">Recommended</SelectItem>
               <SelectItem value="distance">Distance</SelectItem>
               <SelectItem value="name">Name (A-Z)</SelectItem>
             </SelectContent>
           </Select>
        </div>
      </div>

      {/* POI List */}
      <div className="overflow-y-auto p-4 space-y-3 h-[calc(100vh-430px)] bg-white/50">
        {sortedPOIs.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            {searchTerm ? (
              <p>No activities found matching "{searchTerm}"</p>
            ) : (
              <p>No points of interest available for {destination.name}</p>
            )}
          </div>
        ) : (
          sortedPOIs.map((poi) => {
            const isSelected = selectedPOIs.some((p) => p.id === poi.id);
            // Get distance string for display
            const distanceStr = poi.distance !== undefined ? `(${poi.distance.toFixed(1)} km)` : '';
            return (
              <div
                key={poi.id}
                className={`flex items-center justify-between p-3 rounded-lg transition-all duration-200 cursor-pointer ${
                  isSelected 
                    ? 'bg-green-100 border-l-4 border-green-500'
                    : 'bg-gray-50/90 hover:bg-gray-100/90 hover:shadow-sm'
                }`}
                onClick={() => {
                  console.log(`${isSelected ? 'Removing' : 'Viewing'} POI via container click:`, poi.name);
                  onSelectPOI(poi);
                }}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => (e.key === 'Enter' || e.key === ' ') && onSelectPOI(poi)}
                aria-pressed={isSelected}
              >
                <div className="flex-1 pr-4">
                  <h3 className="font-medium text-gray-900 flex items-center gap-1">
                    {poi.name} 
                    {(poi.cost !== undefined && poi.cost > 0) && <DollarSign size={14} className="text-morocco-terracotta ml-1" aria-label="Paid activity" />}
                    <span className="text-xs text-gray-500 font-normal ml-auto">{distanceStr}</span>
                  </h3>
                  <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                    <span className="flex items-center gap-1 capitalize">
                      <MapPin size={14} />
                      {poi.type?.replace(/_/g, ' ')}
                    </span>
                    {poi.duration && (
                      <span className="flex items-center gap-1">
                        <Clock size={14} />
                        {poi.duration}h
                      </span>
                    )}
                  </div>
                  {poi.tags && poi.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-1">
                      {poi.tags.slice(0, 3).map(tag => (
                        <span key={tag} className="text-xs px-1.5 py-0.5 bg-gray-100 rounded text-gray-600">
                          {tag}
                        </span>
                      ))}
                      {poi.tags.length > 3 && (
                        <span className="text-xs px-1.5 py-0.5 bg-gray-100 rounded text-gray-600">
                          +{poi.tags.length - 3} more
                        </span>
                      )}
                    </div>
                  )}
                </div>
                <button
                  className={`flex items-center justify-center flex-shrink-0 w-20 h-8 px-2 rounded-md transition-colors text-sm font-medium ${
                    isSelected
                      ? 'bg-green-100 text-green-700'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                  aria-label={isSelected ? `Remove ${poi.name} from itinerary` : `Add ${poi.name} to itinerary`}
                  onClick={(e) => {
                    e.stopPropagation();
                    onSelectPOI(poi);
                  }}
                >
                  {isSelected ? <Check size={16} /> : 'Add'}
                </button>
              </div>
            );
          })
        )}
      </div>
      
      {/* Helpful Tips Section */}
      <div className="p-3 bg-blue-50 border-t border-blue-100 text-sm text-blue-700">
        <h4 className="font-semibold mb-1">Trip Planning Tips:</h4>
        <ul className="list-disc list-inside space-y-1 text-xs">
          <li>Select attractions that fit your interests and time constraints</li>
          <li>We'll build your customized journey based on your selections</li>
          <li>Our quote will include all transportation between destinations</li>
          <li>Entrance fees for paid attractions are not included in quotes</li>
        </ul>
      </div>
    </div>
  );
};

export default POIOverlay; 