import React from 'react';
import { Destination } from '@/data/destinations';

// Add interface for destination with days
interface DestinationWithDays extends Destination {
  daysToSpend?: number;
}

interface FeedbackOverlayProps {
  selectedDestinations: DestinationWithDays[];
}

const FeedbackOverlay: React.FC<FeedbackOverlayProps> = ({
  selectedDestinations
}) => {
  if (selectedDestinations.length === 0) return null;

  return (
    <div className="map-feedback-overlay">
      <p>Selected Cities: {selectedDestinations.length}</p>
      <ul className="selected-cities-list">
        {selectedDestinations.map((destination, index) => (
          <li key={destination.id} className="city-list-item">
            <div className="counter-badge">{index + 1}</div>
            {destination.name}
            <span className="days-badge">
              {destination.daysToSpend || 1} {(destination.daysToSpend || 1) === 1 ? 'day' : 'days'}
            </span>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default FeedbackOverlay; 