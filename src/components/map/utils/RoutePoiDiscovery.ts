import * as turf from '@turf/turf';
import type { PointOfInterest } from '@/types/POITypes';
import mapboxgl from 'mapbox-gl';
import { calculatePOIImportance } from '@/utils/poi-importance';

// Constants
export const ROUTE_POI_DETECTION_RADIUS = 10; // km - broader radius to detect POIs along route
export const APPROACHING_POI_MIN_DISTANCE = 3; // km - minimum distance to consider a POI approaching
export const APPROACHING_POI_MAX_DISTANCE = 8; // km - maximum distance to consider a POI approaching
export const POI_NOTIFICATION_COOLDOWN = 15000; // ms - time between notifications
export const POI_CLUSTER_THRESHOLD = 3; // Number of POIs that constitute a cluster
export const POI_CLUSTER_RADIUS = 2; // km - Maximum distance between POIs to be considered part of the same cluster

// Track last notification time 
let lastNotificationTime = Date.now() - POI_NOTIFICATION_COOLDOWN; // Initialize to allow immediate first check

// Interface for ranked POI with additional metadata
interface RankedPOI {
  poi: PointOfInterest;
  distance: number;
  importance: number;
  timeRelevance: number; // 0-1 score for time of day relevance
  bearing: number; // Direction from current position
}

// Interface for POI clusters
interface POICluster {
  pois: RankedPOI[];
  center: [number, number];
  primaryPOI: RankedPOI;
  totalImportance: number;
}

/**
 * Calculate time of day relevance (0-1)
 * Different POIs are more relevant at different times of day
 */
function calculateTimeRelevance(poi: PointOfInterest): number {
  const hour = new Date().getHours();
  
  // Default relevance
  let relevance = 0.7;
  
  // Adjust based on POI type and time of day
  switch (poi.category) {
    case 'restaurant':
      // Restaurants more relevant during meal times
      if ((hour >= 11 && hour <= 14) || (hour >= 18 && hour <= 21)) {
        relevance = 1.0;
      } else if (hour < 7 || hour > 22) {
        relevance = 0.3; // Less relevant when closed
      }
      break;
      
    case 'accommodation':
      // Accommodations more relevant in evening
      if (hour >= 16) {
        relevance = 0.9;
      }
      break;
      
    case 'landmark':
      // Landmarks best visited during daylight
      if (hour >= 9 && hour <= 18) {
        relevance = 1.0;
      } else if (hour < 6 || hour > 21) {
        relevance = 0.4; // Less relevant at night
      }
      break;
      
    case 'activity':
      // Activities best during day
      if (hour >= 9 && hour <= 17) {
        relevance = 1.0;
      } else if (hour < 7 || hour > 21) {
        relevance = 0.3;
      }
      break;
  }
  
  return relevance;
}

/**
 * Check for POIs along the route that are approaching but not yet reached
 * Returns either individual POIs or clusters based on proximity
 */
export function checkForApproachingRoutePOIs(
  currentPosition: [number, number],
  pois: PointOfInterest[],
  discoveredPOIs: Set<string>,
  userInterests: string[] = []
): { poi: PointOfInterest; distance: number; isClustered?: boolean; poiCount?: number } | null {
  if (!pois || !currentPosition) return null;
  
  // Implement rate limiting to avoid too frequent checks
  const now = Date.now();
  if (now - lastNotificationTime < POI_NOTIFICATION_COOLDOWN) {
    return null;
  }
  
  try {
    console.log(`[RoutePoiDiscovery] Searching for approaching POIs among ${pois.length} POIs`);
    
    // Find all POIs in the approaching distance range
    const approachingPOIs = pois.filter(poi => {
      if (!poi.coordinates || discoveredPOIs.has(poi.id)) return false;
      
      const distance = turf.distance(
        turf.point(currentPosition),
        turf.point(poi.coordinates as [number, number]),
        { units: 'kilometers' }
      );
      
      return distance >= APPROACHING_POI_MIN_DISTANCE && 
             distance <= APPROACHING_POI_MAX_DISTANCE && 
             !discoveredPOIs.has(poi.id);
    });
    
    if (approachingPOIs.length === 0) {
      console.log('[RoutePoiDiscovery] No approaching POIs found');
      return null;
    }
    
    console.log(`[RoutePoiDiscovery] Found ${approachingPOIs.length} approaching POIs`);
    
    // Create ranked POIs with additional metadata
    const rankedPOIs: RankedPOI[] = approachingPOIs.map(poi => {
      const distance = turf.distance(
        turf.point(currentPosition),
        turf.point(poi.coordinates as [number, number]),
        { units: 'kilometers' }
      );
      
      // Calculate bearing (direction)
      const bearing = turf.bearing(
        turf.point(currentPosition),
        turf.point(poi.coordinates as [number, number])
      );
      
      // Calculate importance factors
      const baseImportance = calculatePOIImportance(poi, { interests: userInterests });
      const timeRelevance = calculateTimeRelevance(poi);
      
      // Calculate combined importance score
      const importance = baseImportance * timeRelevance * (1 - distance/APPROACHING_POI_MAX_DISTANCE * 0.3);
      
      return {
        poi,
        distance,
        importance,
        timeRelevance,
        bearing
      };
    });
    
    // Group POIs into clusters based on proximity
    const clusters: POICluster[] = [];
    const processedPOIs = new Set<string>();
    
    // First, find clusters
    for (const poiItem of rankedPOIs) {
      if (processedPOIs.has(poiItem.poi.id)) continue;
      
      // Find all POIs close to this one
      const nearbyPOIs = rankedPOIs.filter(other => {
        if (other.poi.id === poiItem.poi.id || processedPOIs.has(other.poi.id)) return false;
        
        const distance = turf.distance(
          turf.point(poiItem.poi.coordinates as [number, number]),
          turf.point(other.poi.coordinates as [number, number]),
          { units: 'kilometers' }
        );
        
        return distance <= POI_CLUSTER_RADIUS;
      });
      
      // If we found enough nearby POIs to form a cluster
      if (nearbyPOIs.length >= POI_CLUSTER_THRESHOLD - 1) {
        const clusterPOIs = [poiItem, ...nearbyPOIs];
        
        // Mark all as processed
        clusterPOIs.forEach(p => processedPOIs.add(p.poi.id));
        
        // Calculate center point of cluster
        const points = clusterPOIs.map(p => turf.point(p.poi.coordinates as [number, number]));
        const features = turf.featureCollection(points);
        const center = turf.center(features).geometry.coordinates as [number, number];
        
        // Find primary (most important) POI in cluster
        const primaryPOI = clusterPOIs.reduce((best, current) => 
          current.importance > best.importance ? current : best, clusterPOIs[0]);
        
        // Calculate total importance of cluster
        const totalImportance = clusterPOIs.reduce((sum, p) => sum + p.importance, 0);
        
        clusters.push({
          pois: clusterPOIs,
          center,
          primaryPOI,
          totalImportance
        });
      }
    }
    
    // Add remaining individual POIs
    const individualPOIs = rankedPOIs.filter(p => !processedPOIs.has(p.poi.id));
    
    console.log(`[RoutePoiDiscovery] Created ${clusters.length} clusters and have ${individualPOIs.length} individual POIs`);
    
    // Prioritize between clusters and individual POIs
    if (clusters.length > 0) {
      // Sort clusters by total importance
      clusters.sort((a, b) => b.totalImportance - a.totalImportance);
      const bestCluster = clusters[0];
      
      // Calculate distance to the cluster center
      const distanceToCluster = turf.distance(
        turf.point(currentPosition),
        turf.point(bestCluster.center),
        { units: 'kilometers' }
      );
      
      console.log(`[RoutePoiDiscovery] Best cluster has ${bestCluster.pois.length} POIs with primary POI: ${bestCluster.primaryPOI.poi.name}`);
      
      // Update notification time
      lastNotificationTime = now;
      
      // Return the primary POI with cluster metadata
      return {
        poi: bestCluster.primaryPOI.poi,
        distance: distanceToCluster,
        isClustered: true,
        poiCount: bestCluster.pois.length
      };
    } 
    else if (individualPOIs.length > 0) {
      // Sort individual POIs by importance
      individualPOIs.sort((a, b) => b.importance - a.importance);
      const bestPOI = individualPOIs[0];
      
      console.log(`[RoutePoiDiscovery] Best individual POI: ${bestPOI.poi.name} (${bestPOI.distance.toFixed(2)}km away)`);
      
      // Update notification time
      lastNotificationTime = now;
      
      return {
        poi: bestPOI.poi,
        distance: bestPOI.distance
      };
    }
    
    return null;
  } 
  catch (error) {
    console.error('Error checking for approaching POIs:', error);
    return null;
  }
}

/**
 * Show visual indication of approaching POI with enhanced UI
 * @returns A cleanup function to remove the indicator
 */
export function createRoutePOIIndicator(
  map: mapboxgl.Map,
  poi: PointOfInterest,
  vehiclePosition: [number, number],
  onPoiClick: (poi: PointOfInterest) => void,
  options?: {
    isClustered?: boolean;
    poiCount?: number;
    distance?: number;
  }
): () => void {
  if (!map || !poi.coordinates) return () => {};
  
  // Remove any existing indicators
  const existingIndicator = document.getElementById('route-poi-indicator');
  if (existingIndicator && existingIndicator.parentNode) {
    existingIndicator.parentNode.removeChild(existingIndicator);
  }
  
  // Get type-specific styling
  const getPoiTypeColor = (type: string): string => {
    switch (type?.toLowerCase()) {
      case 'restaurant': return '#FF5722';
      case 'accommodation': return '#4CAF50';
      case 'landmark': return '#2196F3';
      case 'activity': return '#FFC107';
      default: return '#9C27B0';
    }
  };
  
  const getPoiTypeIcon = (type: string): string => {
    switch (type?.toLowerCase()) {
      case 'restaurant': return '🍽️';
      case 'accommodation': return '🏨';
      case 'landmark': return '🏛️';
      case 'activity': return '🏄‍♂️';
      default: return '📍';
    }
  };
  
  // Create indicator element with improved styling
  const indicator = document.createElement('div');
  indicator.id = 'route-poi-indicator';
  indicator.style.position = 'absolute';
  indicator.style.zIndex = '900';
  indicator.style.backgroundColor = getPoiTypeColor(poi.category);
  indicator.style.color = 'white';
  indicator.style.padding = '8px 12px';
  indicator.style.borderRadius = '16px';
  indicator.style.fontSize = '14px';
  indicator.style.fontWeight = 'bold';
  indicator.style.boxShadow = '0 2px 8px rgba(0,0,0,0.3)';
  indicator.style.transform = 'translate(-50%, -100%)';
  indicator.style.pointerEvents = 'all';
  indicator.style.cursor = 'pointer';
  indicator.style.display = 'flex';
  indicator.style.alignItems = 'center';
  indicator.style.gap = '8px';
  indicator.style.transition = 'transform 0.3s ease';
  
  // Add subtle animation
  indicator.style.animation = 'pulse 2s infinite';
  
  // Add CSS for pulse animation if it doesn't exist
  if (!document.getElementById('route-poi-indicator-styles')) {
    const style = document.createElement('style');
    style.id = 'route-poi-indicator-styles';
    style.textContent = `
      @keyframes pulse {
        0% { transform: translate(-50%, -100%) scale(1); }
        50% { transform: translate(-50%, -100%) scale(1.05); }
        100% { transform: translate(-50%, -100%) scale(1); }
      }
      
      @keyframes bounce {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-5px); }
      }
    `;
    document.head.appendChild(style);
  }
  
  // Icon element
  const icon = document.createElement('div');
  icon.textContent = getPoiTypeIcon(poi.category);
  icon.style.fontSize = '18px';
  icon.style.animation = 'bounce 1s infinite';
  
  // Direction arrow
  const arrow = document.createElement('div');
  arrow.style.width = '12px';
  arrow.style.height = '12px';
  arrow.style.borderRight = '3px solid white';
  arrow.style.borderBottom = '3px solid white';
  arrow.style.transform = 'rotate(-45deg)';
  
  // Text content with distance
  const text = document.createElement('div');
  const isClustered = options?.isClustered;
  const poiCount = options?.poiCount || 0;
  
  // Calculate estimated arrival time (rough estimate)
  const averageSpeedKmh = 60; // km/h
  const distanceKm = options?.distance || 5; // Use distance from options or default to 5km
  const etaMinutes = Math.round(distanceKm / averageSpeedKmh * 60);
  
  if (isClustered && poiCount > 0) {
    text.innerHTML = `${poiCount} Points of Interest<br><span style="font-size: 12px; opacity: 0.9;">${Math.round(distanceKm)}km ahead (${etaMinutes} min)</span>`;
  } else {
    text.innerHTML = `${poi.name}<br><span style="font-size: 12px; opacity: 0.9;">${Math.round(distanceKm)}km ahead (${etaMinutes} min)</span>`;
  }
  
  // Assemble indicator
  indicator.appendChild(icon);
  indicator.appendChild(text);
  indicator.appendChild(arrow);
  
  // Add click handler
  indicator.addEventListener('click', () => {
    onPoiClick(poi);
    
    // Fly to POI
    map.flyTo({
      center: poi.coordinates as [number, number],
      zoom: 14,
      duration: 1000
    });
    
    // Remove the indicator after click
    if (indicator.parentNode) {
      indicator.parentNode.removeChild(indicator);
    }
  });
  
  // Add to map container
  map.getContainer().appendChild(indicator);
  
  // Position the indicator - place it above the vehicle
  const updatePosition = () => {
    if (!map || !vehiclePosition) return;
    
    const point = map.project(vehiclePosition);
    indicator.style.left = `${point.x}px`;
    indicator.style.top = `${point.y - 80}px`; // Position above vehicle
    
    // Calculate direction to POI
    if (poi.coordinates) {
      const vehiclePoint = turf.point(vehiclePosition);
      const poiPoint = turf.point(poi.coordinates as [number, number]);
      
      const bearing = turf.bearing(vehiclePoint, poiPoint);
      arrow.style.transform = `rotate(${bearing - 135}deg)`;
    }
  };
  
  // Initial position
  updatePosition();
  
  // Update on map move
  map.on('move', updatePosition);
  
  // Add removal after 15 seconds
  setTimeout(() => {
    if (indicator.parentNode) {
      // Fade out
      indicator.style.transition = 'opacity 0.5s';
      indicator.style.opacity = '0';
      
      // Remove after fade
      setTimeout(() => {
        if (indicator.parentNode) {
          indicator.parentNode.removeChild(indicator);
          map.off('move', updatePosition);
        }
      }, 500);
    }
  }, 15000);
  
  // Return cleanup function
  return () => {
    if (indicator.parentNode) {
      indicator.parentNode.removeChild(indicator);
      map.off('move', updatePosition);
    }
  };
} 