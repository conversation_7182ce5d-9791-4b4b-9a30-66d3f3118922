import mapboxAdapter, { MapInstance } from '../../../utils/mapbox-adapter';
import * as turf from '@turf/turf';
import React from 'react';
import { Destination } from '@/types/destination';
import { RouteCluster } from '@/types/clusters';
import type { CulturalRegion } from '@/types/cultural-regions';
import { TerrainTypeEnum } from '@/types/TerrainType';

// For backward compatibility, use TerrainTypeEnum as TerrainType
const TerrainType = TerrainTypeEnum;

// Get the mapboxgl object from our adapter
const mapboxgl = mapboxAdapter.getMapboxGL();

// -------------------------------------------------------------
// CONSTANTS AND CONFIGURATION
// -------------------------------------------------------------

// Configuration constants
export const ANIMATION_DURATION = 60000; // 60 seconds for full route animation

// Detection radii
export const POI_DETECTION_RADIUS = 20; // km
export const CITY_DETECTION_RADIUS = 10; // km - smaller radius for cities directly on the route
export const ROUTE_PROXIMITY_THRESHOLD = 5; // km - how close a city/POI needs to be to the route to be considered "on the way"
export const ROUTE_POI_CLUSTER_RADIUS = 5; // km - threshold for clustering POIs along the route

// Define common map zoom levels
export const BROAD_CONTEXT_ZOOM = 4.0; // Country in relation to surroundings
export const REGIONAL_CONTEXT_ZOOM = 5.5; // Regional view
export const CITY_APPROACH_ZOOM = 7.0; // Approaching a major city
export const POI_DETAIL_ZOOM = 9.0; // Detailed view of a specific POI
export const MAX_ZOOM_DEFAULT = 5.2; // More zoomed out default view to show more country context
export const MAX_ZOOM_POI_CLUSTER = 7.5; // Slightly more zoomed in for POI clusters, but still showing context

// Constants for speed modifiers
const SPEED_MODIFIERS = {
  DEFAULT: 1.0,
  CITY_APPROACH: 0.6,
  CULTURAL_REGION: 0.8,
  POI_CLUSTER: 0.7,
  MOUNTAIN: 0.9,
  DESERT: 1.2
};

// Camera settings - conforms to map-camera-rules
export const CAMERA_SETTINGS = {
  DEFAULT: {
    zoom: 8, // Standard route travel zoom
    pitch: 45, // 3D perspective
    bearing: 0, // Fixed north orientation
    duration: 1000, // 1 second transition
    easing: (t: number) => t * t * (3 - 2 * t) // Smooth ease-in-out
  },
  CONTINUE_JOURNEY: {
    zoom: 12,
    pitch: 45,
    bearing: 0, 
    duration: 1500, // Longer transition for journey continuation
    easing: (t: number) => t * (2 - t) // Ease-out
  },
  POI_DISCOVERY: {
    zoom: 16, // Detailed zoom for POIs
    pitch: 50,
    bearing: 30,
    duration: 2000, // Longer for dramatic effect
    easing: (t: number) => t * t * (3 - 2 * t) // Smooth ease-in-out
  },
  CITY_APPROACH: {
    zoom: 14, // City exploration zoom
    pitch: 45,
    bearing: 0,
    duration: 2000, // Longer for city approach
    easing: (t: number) => t * t * (3 - 2 * t) // Smooth ease-in-out
  },
  BROAD_CONTEXT: {
    zoom: 8,
    pitch: 40,
    bearing: 0,
    duration: 3000, // Longest duration for broad context
    easing: (t: number) => t * t * (3 - 2 * t) // Smooth ease-in-out
  },
  MOUNTAIN: {
    zoom: 11,
    pitch: 60, // Higher pitch for mountains
    bearing: 45, // Angled for dramatic effect
    duration: 1500,
    easing: (t: number) => t * t * (3 - 2 * t)
  },
  DESERT: {
    zoom: 10,
    pitch: 35, // Lower pitch for desert expanses
    bearing: 15,
    duration: 1500,
    easing: (t: number) => t * t * (3 - 2 * t)
  },
  CULTURAL_SITE: {
    zoom: 15,
    pitch: 50,
    bearing: 30,
    duration: 2000,
    easing: (t: number) => t * t * (3 - 2 * t)
  }
};

// Cultural regions data
const CULTURAL_REGIONS: CulturalRegion[] = [
  {
    name: 'Atlas Mountains',
    type: 'mountain',
    bounds: [[-9, 31], [-5, 33]]
  },
  {
    name: 'Sahara Desert',
    type: 'desert',
    bounds: [[-7, 29], [-2, 31]]
  }
];

// -------------------------------------------------------------
// CORE ANIMATION UTILITIES
// -------------------------------------------------------------

/**
 * Calculate progress based on elapsed time and total duration
 */
export const calculateProgress = (elapsedTime: number, duration: number): number => {
  return Math.min(elapsedTime / duration, 1);
};

/**
 * Calculate distances between route segments
 */
export const calculateSegmentDistances = (points: Array<[number, number]>): number[] => {
  const distances: number[] = [];
  for (let i = 0; i < points.length - 1; i++) {
    distances.push(
      turf.distance(turf.point(points[i]), turf.point(points[i + 1]), { units: 'kilometers' })
    );
  }
  return distances;
};

/**
 * Get current position along route based on progress
 */
export const getCurrentPosition = (
  points: Array<[number, number]>, 
  progress: number
): { currentPoint: [number, number]; nextPoint: [number, number] | null; pointIndex: number } => {
  if (!points || points.length < 2) {
    // Handle edge case of insufficient points
    return { 
      currentPoint: points?.[0] || [0, 0], 
      nextPoint: null, 
      pointIndex: 0 
    };
  }

  // Calculate target segment based on progress
  const totalSegments = points.length - 1;
  const targetSegmentIndex = Math.min(Math.floor(progress * totalSegments), totalSegments - 1);
  
  // Calculate progress within the current segment (0-1)
  const segmentProgress = (progress * totalSegments) % 1;
  
  // Get current and next points from the route
  const currentSegmentPoint = points[targetSegmentIndex];
  const nextSegmentPoint = points[targetSegmentIndex + 1];
  
  // Smoothly interpolate between current and next point
  const interpolatedPoint: [number, number] = [
    currentSegmentPoint[0] + (nextSegmentPoint[0] - currentSegmentPoint[0]) * segmentProgress,
    currentSegmentPoint[1] + (nextSegmentPoint[1] - currentSegmentPoint[1]) * segmentProgress
  ];
  
  return { 
    currentPoint: interpolatedPoint, 
    nextPoint: nextSegmentPoint, 
    pointIndex: targetSegmentIndex 
  };
};

/**
 * Find cultural region for a point
 */
export const findCulturalRegion = (point: [number, number]): CulturalRegion | null => {
  for (const region of CULTURAL_REGIONS) {
    const [sw, ne] = region.bounds;
    if (
      point[0] >= sw[0] && point[0] <= ne[0] &&
      point[1] >= sw[1] && point[1] <= ne[1]
    ) {
      return region;
    }
  }
  return null;
};

/**
 * Determine speed modifier based on map context
 */
export const getSpeedModifier = (
  currentPoint: [number, number],
  nextPoint: [number, number] | null,
  destinations: Destination[],
  routePOIClusters: Record<string, RouteCluster>,
  cityDetectionRadius: number,
  routePoiClusterRadius: number
): number => {
  let speedModifier = 1.0;
  
  // Check if near any cities
  const nearestCity = destinations.find(dest => {
    if (!dest.coordinates) return false;
    const distance = turf.distance(
      turf.point(currentPoint),
      turf.point(dest.coordinates as [number, number]),
      { units: 'kilometers' }
    );
    return distance < cityDetectionRadius;
  });

  if (nearestCity) {
    console.log('Speed modified: Approaching city', nearestCity.name);
    speedModifier *= SPEED_MODIFIERS.CITY_APPROACH;
  }

  // Check if in cultural region
  const culturalRegion = findCulturalRegion(currentPoint);
  if (culturalRegion) {
    console.log('Speed modified: In cultural region', culturalRegion.name);
    speedModifier *= SPEED_MODIFIERS.CULTURAL_REGION;
  }

  // Check if near POI clusters
  const nearbyCluster = Object.values(routePOIClusters).find(cluster => {
    if (!cluster.center) return false;
    const distance = turf.distance(
      turf.point(currentPoint),
      turf.point(cluster.center),
      { units: 'kilometers' }
    );
    return distance < routePoiClusterRadius;
  });

  if (nearbyCluster) {
    speedModifier *= SPEED_MODIFIERS.POI_CLUSTER;
  }

  // Speed up in long transit segments
  if (!nearestCity && !culturalRegion && !nearbyCluster) {
    speedModifier *= 1.5;
  }

  return speedModifier;
};

/**
 * Calculate bearing between two points
 */
export const calculateBearing = (point1: [number, number], point2: [number, number]): number => {
  const start = turf.point(point1);
  const end = turf.point(point2);
  return turf.bearing(start, end);
};

/**
 * Helper function to check if we're in a significant cultural region
 */
export const isInSignificantCulturalRegion = (point: [number, number]): boolean => {
  // Define regions for different cultural significance
  // Atlas Mountains
  if (
    point[0] >= -9 && point[0] <= -5 &&
    point[1] >= 31 && point[1] <= 33
  ) {
    return true;
  }
  
  // Sahara Desert
  if (
    point[0] >= -7 && point[0] <= -2 &&
    point[1] >= 29 && point[1] <= 31
  ) {
    return true;
  }
  
  return false;
};

/**
 * Determines the terrain type based on coordinates
 */
export function determineTerrainType(coordinates: [number, number]): TerrainTypeEnum {
  // Check if near any predefined cities
  const nearestCity = findNearestCity(coordinates);
  if (nearestCity) {
    return TerrainType.CITY;
  }

  // Check if in Atlas Mountains region (rough approximation)
  const [lng, lat] = coordinates;
  if (lat >= 30 && lat <= 35 && lng >= -9 && lng <= 0) {
    return TerrainType.MOUNTAIN;
  }

  // Check if on major highways (simplified check)
  if (isOnHighway(coordinates)) {
    return TerrainType.HIGHWAY;
  }

  // Check if in scenic regions
  if (isInScenicRegion(coordinates)) {
    return TerrainType.SCENIC;
  }

  return TerrainType.DEFAULT;
}

// Helper functions
function isOnHighway(coordinates: [number, number]): boolean {
  // Implement highway detection logic
  // For now, return false as placeholder
  return false;
}

function isInScenicRegion(coordinates: [number, number]): boolean {
  // Implement scenic region detection logic
  // For now, return false as placeholder
  return false;
}

/**
 * Find the nearest city to a given point
 */
function findNearestCity(coordinates: [number, number]): { name: string; coordinates: [number, number] } | null {
  // Define major cities in Morocco with their coordinates
  const majorCities = [
    { name: 'Casablanca', coordinates: [-7.5898, 33.5731] as [number, number] },
    { name: 'Marrakech', coordinates: [-8.0083, 31.6295] as [number, number] },
    { name: 'Fes', coordinates: [-5.0078, 34.0181] as [number, number] },
    { name: 'Tangier', coordinates: [-5.8326, 35.7595] as [number, number] },
    { name: 'Rabat', coordinates: [-6.8498, 34.0209] as [number, number] },
    { name: 'Agadir', coordinates: [-9.5982, 30.4278] as [number, number] },
    { name: 'Meknes', coordinates: [-5.5338, 33.8935] as [number, number] },
    { name: 'Oujda', coordinates: [-1.9072, 34.6820] as [number, number] },
    { name: 'Chefchaouen', coordinates: [-5.2636, 35.1683] as [number, number] },
    { name: 'Essaouira', coordinates: [-9.7681, 31.5085] as [number, number] }
  ];

  let nearestCity = null;
  let shortestDistance = Infinity;

  for (const city of majorCities) {
    const distance = turf.distance(
      turf.point(coordinates),
      turf.point(city.coordinates),
      { units: 'kilometers' }
    );
    if (distance < shortestDistance) {
      shortestDistance = distance;
      nearestCity = city;
    }
  }

  // Only return if city is within 20km
  return shortestDistance < 20 ? nearestCity : null;
}

// -------------------------------------------------------------
// CAMERA CONTROL - Following map-camera-rules
// -------------------------------------------------------------

/**
 * Implements contextual camera rhythm for transitions during journey
 */
export const implementContextualRhythm = (
  map: MapInstance,
  currentPoint: [number, number], 
  elapsedTime: number,
  lastContextZoomTimeRef: React.MutableRefObject<number>,
  currentCulturalRegionRef: React.MutableRefObject<string | null>,
  vehicleBearing: number = 0,
  recentPOIs: any[] = [],
  isNearDestination: boolean = false
) => {
  // Prevent context zooming too frequently
  const MIN_CONTEXT_ZOOM_INTERVAL = 60000; // 60 seconds between context zooms
  
  // Check if enough time has passed for another context zoom
  if (elapsedTime - lastContextZoomTimeRef.current < MIN_CONTEXT_ZOOM_INTERVAL) {
    return;
  }
  
  // Get current region
  let newRegion = null;
  
  // Check for Atlas Mountains region
  if (
    currentPoint[0] >= -9 && currentPoint[0] <= -5 &&
    currentPoint[1] >= 31 && currentPoint[1] <= 33
  ) {
    newRegion = 'Atlas Mountains';
  }
  
  // Check for Sahara Desert region
  else if (
    currentPoint[0] >= -7 && currentPoint[0] <= -2 &&
    currentPoint[1] >= 29 && currentPoint[1] <= 31
  ) {
    newRegion = 'Sahara Desert';
  }
  
  // If we've entered a new region, do a contextual zoom
  if (newRegion && newRegion !== currentCulturalRegionRef.current) {
    // Don't zoom if near destination
    if (isNearDestination) {
      currentCulturalRegionRef.current = newRegion;
      return;
    }
    
    // Perform a contextual zoom to highlight the significance of the area
    console.log(`Cultural region transition: ${currentCulturalRegionRef.current || 'None'} -> ${newRegion}`);
    
    // Apply a smooth camera transition
    console.log('🗺️ TEMPORARILY DISABLING COUNTRY BORDER REMOVAL FOR DEBUGGING');
    
    // Update refs
    currentCulturalRegionRef.current = newRegion;
    lastContextZoomTimeRef.current = elapsedTime;
  }
};

/**
 * Apply contextual rhythm to camera based on context
 */
export const applyContextualRhythm = (
  map: MapInstance,
  currentPoint: [number, number],
  elapsed: number,
  lastContextZoomTimeRef: React.MutableRefObject<number>,
  currentCulturalRegionRef: React.MutableRefObject<string | null>,
  vehicleBearing: number = 0,
  recentPOIs: any[] = [],
  isNearDestination: boolean = false
) => {
  implementContextualRhythm(
    map,
    currentPoint,
    elapsed,
    lastContextZoomTimeRef,
    currentCulturalRegionRef,
    vehicleBearing,
    recentPOIs,
    isNearDestination
  );
};

/**
 * Preprocess route points for smoother animation
 */
export const preprocessRouteForAnimation = (routePoints: Array<[number, number]>): Array<[number, number]> => {
  if (!routePoints || routePoints.length < 2) {
    console.warn('Cannot preprocess route with less than 2 points');
    return routePoints || [];
  }
  
  console.log(`Preprocessing route for animation - original points: ${routePoints.length}`);
  
  const enhancedRoute: Array<[number, number]> = [];
  
  // Determine how many points to add between segments based on route length
  // - For very short routes (2-3 points): Add many interpolation points (50+)
  // - For medium routes (4-20 points): Add moderate interpolation (20-30)
  // - For long routes (21+ points): Add fewer interpolation points (10-15)
  const getInterpolationPoints = (routeLength: number, segmentDistance: number): number => {
    if (routeLength <= 3) {
      return Math.max(50, Math.floor(segmentDistance * 5)); // At least 50 points for very short routes
    } else if (routeLength <= 20) {
      return Math.max(20, Math.floor(segmentDistance * 2)); // At least 20 points for medium routes
    } else {
      return Math.max(10, Math.floor(segmentDistance)); // At least 10 points for long routes
    }
  };
  
  // For each segment, add interpolated points
  for (let i = 0; i < routePoints.length - 1; i++) {
    const startPoint = routePoints[i];
    const endPoint = routePoints[i + 1];
    
    // Add the start point
    enhancedRoute.push(startPoint);
    
    // Calculate the distance between points
    let distance = 0;
    try {
      distance = turf.distance(
        turf.point(startPoint),
        turf.point(endPoint),
        { units: 'kilometers' }
      );
    } catch (error) {
      console.error('Error calculating distance in preprocessRouteForAnimation:', error);
      distance = 10; // Default to a reasonable value
    }
    
    // Determine how many points to add for this segment
    const pointsToAdd = getInterpolationPoints(routePoints.length, distance);
    
    console.log(`Adding ${pointsToAdd} interpolation points between ${startPoint} and ${endPoint} (distance: ${distance.toFixed(2)}km)`);
    
    // Add intermediate points using linear interpolation
    for (let j = 1; j < pointsToAdd; j++) {
      const fraction = j / pointsToAdd;
      const lat = startPoint[1] + (endPoint[1] - startPoint[1]) * fraction;
      const lng = startPoint[0] + (endPoint[0] - startPoint[0]) * fraction;
      enhancedRoute.push([lng, lat]);
    }
    
    // Last point of the route is added in the next iteration
    // except for the final endpoint
    if (i === routePoints.length - 2) {
      enhancedRoute.push(endPoint);
    }
  }
  
  console.log(`Enhanced route with ${enhancedRoute.length} points (added ${enhancedRoute.length - routePoints.length} intermediate points)`);
  
  // CRITICAL: Make sure we have at least 100 points for short routes
  if (enhancedRoute.length < 100 && routePoints.length < 5) {
    console.log(`Route still has too few points (${enhancedRoute.length}), adding more interpolation...`);
    return preprocessRouteForAnimation(enhancedRoute);
  }
  
  return enhancedRoute;
};

/**
 * Removes country borders and labels from the map
 * TEMPORARILY DISABLED FOR DEBUGGING
 */
export const removeCountryBordersAndLabels = (map: MapInstance): void => {
  if (!map || !map.isStyleLoaded()) {
    console.log('Map style not loaded yet, deferring country border removal');
    return;
  }
  
  console.log('🗺️ TEMPORARILY DISABLING COUNTRY BORDER REMOVAL FOR DEBUGGING');
  
  /* 
  // TEMPORARILY COMMENTING OUT BORDER REMOVAL CODE FOR DEBUGGING
  try {
    // Disable admin boundaries visibility
    const adminLayers = [
      'admin-0-boundary',
      'admin-0-boundary-disputed',
      'admin-0-boundary-bg',
      'admin-1-boundary',
      'admin-1-boundary-bg',
      'admin-1-boundary-disputed',
      'admin-0-boundary-maritime'
    ];
    
    // Hide all administrative boundaries
    adminLayers.forEach(layer => {
      if (map.getLayer(layer)) {
        map.setLayoutProperty(layer, 'visibility', 'none');
      }
    });
    
    // For Western Sahara specific filtering
    const filterLayers = ['country-label', 'settlement-label'];
    filterLayers.forEach(layer => {
      if (map.getLayer(layer)) {
        map.setFilter(layer, ['!=', ['get', 'iso_3166_1'], 'EH']);
      }
    });
    
    console.log('Country borders and labels removed');
  } catch (error) {
    console.error('Error removing country borders:', error);
  }
  */
  
  // Add logging about map style
  console.log('Map style information:', {
    styleLoaded: map.isStyleLoaded(),
    style: map.getStyle()?.name || 'unknown style',
    zoom: map.getZoom(),
    center: map.getCenter(),
    availableLayers: Object.keys(map.getStyle()?.layers || {})
      .filter(layer => layer.includes('admin') || layer.includes('border') || layer.includes('label'))
  });
}; 