import React from 'react';
import mapboxgl from 'mapbox-gl';
import type { Destination, PointOfInterest } from '@/types/POITypes';

// Define CSS animations
const styleSheet = document.createElement("style");
styleSheet.type = "text/css";
styleSheet.textContent = `
  @keyframes pulse {
    0% {
      transform: translate(-50%, -50%) scale(0.95);
      box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
    }
    
    70% {
      transform: translate(-50%, -50%) scale(1);
      box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
    }
    
    100% {
      transform: translate(-50%, -50%) scale(0.95);
      box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
  }
  
  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0) translateX(-50%);
    }
    40% {
      transform: translateY(-10px) translateX(-50%);
    }
    60% {
      transform: translateY(-5px) translateX(-50%);
    }
  }
  
  @keyframes bubble-pop {
    0% {
      transform: scale(0.7);
      opacity: 0;
    }
    50% {
      transform: scale(1.05);
    }
    70% {
      transform: scale(0.95);
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }
  
  @keyframes fadeOut {
    from {
      opacity: 1;
    }
    to {
      opacity: 0;
    }
  }
`;
document.head.appendChild(styleSheet);

// Function to get POI type color
export const getPOITypeColor = (type: string): string => {
  switch (type.toLowerCase()) {
    case 'landmark': return '#F97316'; // Orange
    case 'historical': return '#8B5CF6'; // Purple
    case 'cultural': return '#EC4899'; // Pink
    case 'natural': return '#10B981'; // Green
    case 'restaurant': return '#EF4444'; // Red
    case 'shopping': return '#F59E0B'; // Amber
    case 'activity': return '#3B82F6'; // Blue
    default: return '#6B7280'; // Gray
  }
};

// Function to get POI type icon
export const getPOITypeIcon = (type: string): string => {
  switch (type.toLowerCase()) {
    case 'landmark': return '🏛️';
    case 'historical': return '🏰';
    case 'cultural': return '🎭';
    case 'natural': return '🌄';
    case 'restaurant': return '🍽️';
    case 'shopping': return '🛍️';
    case 'activity': return '🏄';
    default: return '📍';
  }
};

/**
 * Show notification for an approaching POI
 */
export const showApproachingPOI = (
  map: mapboxgl.Map,
  poi: PointOfInterest,
  vehiclePosition: [number, number], 
  onPOIDiscovered: (poi: PointOfInterest) => void,
  distance: number = 0
): void => {
  if (!map || !poi.coordinates) return;
  
  console.log(`[NotificationManager] Showing approaching POI: ${poi.name} (${distance}m away)`);
  
  // Create bubble element
  const bubbleEl = document.createElement('div');
  bubbleEl.className = 'approaching-poi-bubble';
  bubbleEl.style.position = 'absolute';
  bubbleEl.style.zIndex = '900';
  bubbleEl.style.backgroundColor = 'rgba(0, 100, 255, 0.9)';
  bubbleEl.style.color = 'white';
  bubbleEl.style.padding = '15px';
  bubbleEl.style.borderRadius = '10px';
  bubbleEl.style.boxShadow = '0 3px 10px rgba(0, 0, 0, 0.2)';
  bubbleEl.style.maxWidth = '260px';
  bubbleEl.style.transform = 'translate(-50%, -100%)';
  bubbleEl.style.animation = 'bubble-pop 0.5s ease-out forwards';
  bubbleEl.style.cursor = 'pointer';
  bubbleEl.style.display = 'flex';
  bubbleEl.style.flexDirection = 'column';
  bubbleEl.style.gap = '10px';
  
  // Bubble content - title
  const titleEl = document.createElement('div');
  titleEl.style.fontSize = '16px';
  titleEl.style.fontWeight = 'bold';
  titleEl.textContent = poi.name;
  
  // Bubble content - type badge
  const typeEl = document.createElement('div');
  typeEl.style.fontSize = '12px';
  typeEl.style.backgroundColor = 'rgba(255, 255, 255, 0.3)';
  typeEl.style.padding = '3px 8px';
  typeEl.style.borderRadius = '10px';
  typeEl.style.alignSelf = 'flex-start';
  typeEl.textContent = poi.category.charAt(0).toUpperCase() + poi.category.slice(1);
  
  // Bubble content - distance
  const distanceEl = document.createElement('div');
  distanceEl.style.fontSize = '14px';
  distanceEl.style.opacity = '0.9';
  
  // Format distance nicely
  const formattedDistance = distance > 1000 ? 
    `${(distance/1000).toFixed(1)}km away` : 
    `${Math.round(distance)}m away`;
  
  distanceEl.textContent = `${formattedDistance}`;
  
  // Close button
  const closeBtn = document.createElement('button');
  closeBtn.id = 'close-bubble-btn';
  closeBtn.style.position = 'absolute';
  closeBtn.style.top = '5px';
  closeBtn.style.right = '5px';
  closeBtn.style.backgroundColor = 'transparent';
  closeBtn.style.border = 'none';
  closeBtn.style.color = 'white';
  closeBtn.style.fontSize = '16px';
  closeBtn.style.cursor = 'pointer';
  closeBtn.style.opacity = '0.7';
  closeBtn.innerHTML = '&times;';
  closeBtn.addEventListener('click', (e) => {
    e.stopPropagation();
    if (bubbleEl.parentNode) {
      bubbleEl.parentNode.removeChild(bubbleEl);
    }
  });
  
  // Assemble bubble
  bubbleEl.appendChild(titleEl);
  bubbleEl.appendChild(typeEl);
  bubbleEl.appendChild(distanceEl);
  bubbleEl.appendChild(closeBtn);
  
  // Direction arrow pointing to the POI
  if (poi.coordinates) {
    // Calculate direction
    const poiCoords = poi.coordinates as [number, number];
    const dx = poiCoords[0] - vehiclePosition[0];
    const dy = poiCoords[1] - vehiclePosition[1];
    const angle = Math.atan2(dy, dx) * (180 / Math.PI);
    
    // Create arrow
    const arrowEl = document.createElement('div');
    arrowEl.style.position = 'absolute';
    arrowEl.style.bottom = '-15px';
    arrowEl.style.left = '50%';
    arrowEl.style.width = '0';
    arrowEl.style.height = '0';
    arrowEl.style.transform = 'translateX(-50%)';
    arrowEl.style.borderLeft = '10px solid transparent';
    arrowEl.style.borderRight = '10px solid transparent';
    arrowEl.style.borderTop = '15px solid rgba(0, 100, 255, 0.9)';
    
    bubbleEl.appendChild(arrowEl);
  }
  
  // Add bubble to map container
  map.getContainer().appendChild(bubbleEl);
  
  // Position bubble - place it over the vehicle
  const point = map.project(vehiclePosition);
  bubbleEl.style.left = `${point.x}px`;
  bubbleEl.style.top = `${point.y - 20}px`;
  
  // Add discover handler to the bubble itself
  bubbleEl.addEventListener('click', (e) => {
    // Don't trigger if clicking the close button
    if ((e.target as HTMLElement).id === 'close-bubble-btn') {
      return;
    }
    
    // Trigger POI discovery
    onPOIDiscovered(poi);
    
    // Calculate a bounding box that includes both the POI and some surrounding area
    const poiCoords = poi.coordinates as [number, number];
    const bounds = new mapboxgl.LngLatBounds(
      [
        Math.min(poiCoords[0] - 0.05, poiCoords[0]),
        Math.min(poiCoords[1] - 0.05, poiCoords[1])
      ],
      [
        Math.max(poiCoords[0] + 0.05, poiCoords[0]),
        Math.max(poiCoords[1] + 0.05, poiCoords[1])
      ]
    );
    
    // Fit the map to these bounds
    map.fitBounds(bounds, {
      padding: { top: 50, bottom: 50, left: 50, right: 50 },
      duration: 1500,
      bearing: 0 // Always keep north up
    });
    
    // Remove bubble
    if (bubbleEl.parentNode) {
      bubbleEl.parentNode.removeChild(bubbleEl);
    }
    
    // Remove POI marker
    const poiMarker = document.querySelector('.poi-temp-marker') as HTMLElement;
    if (poiMarker && poiMarker.parentNode) {
      poiMarker.style.opacity = '0';
      setTimeout(() => {
        if (poiMarker.parentNode) {
          poiMarker.parentNode.removeChild(poiMarker);
        }
      }, 300);
    }
  });
  
  // Add close button handler
  closeBtn.addEventListener('click', () => {
    if (bubbleEl.parentNode) {
      bubbleEl.parentNode.removeChild(bubbleEl);
    }
  });
  
  // Remove after 8 seconds if not interacted with
  setTimeout(() => {
    // Check if bubble still exists
    if (bubbleEl.parentNode) {
      // Fade out animation
      bubbleEl.style.transition = 'opacity 0.5s ease-out';
      bubbleEl.style.opacity = '0';
      
      // Remove after animation completes
      setTimeout(() => {
        if (bubbleEl.parentNode) {
          bubbleEl.parentNode.removeChild(bubbleEl);
        }
      }, 500);
    }
  }, 8000);
};

// Function to present area entry notification as a persistent sidebar
export const presentAreaEntry = (
  map: mapboxgl.Map,
  city: Destination, 
  areaPOIs: PointOfInterest[],
  onPOIDiscovered: (poi: PointOfInterest) => void,
  pauseAnimation: () => void,
  resumeAnimation: () => void,
  isPaused: boolean,
  onContinueJourney: () => void
) => {
  if (!map) return;
  
  // Remove any existing area sidebar
  const existingSidebar = document.getElementById('area-poi-sidebar');
  if (existingSidebar && existingSidebar.parentNode) {
    existingSidebar.parentNode.removeChild(existingSidebar);
  }
  
  // Create sidebar container
  const sidebarEl = document.createElement('div');
  sidebarEl.id = 'area-poi-sidebar';
  sidebarEl.className = 'area-poi-sidebar';
  sidebarEl.style.position = 'absolute';
  sidebarEl.style.left = '20px';
  sidebarEl.style.top = '100px';
  sidebarEl.style.bottom = '100px';
  sidebarEl.style.width = '320px';
  sidebarEl.style.backgroundColor = 'rgba(255, 255, 255, 0.85)';
  sidebarEl.style.backdropFilter = 'blur(8px)';
  sidebarEl.style.borderRadius = '12px';
  sidebarEl.style.padding = '16px';
  sidebarEl.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)';
  sidebarEl.style.zIndex = '3000';
  sidebarEl.style.overflowY = 'auto';
  sidebarEl.style.maxHeight = 'calc(100vh - 200px)';
  sidebarEl.style.display = 'flex';
  sidebarEl.style.flexDirection = 'column';
  sidebarEl.style.animation = 'bubble-pop 0.5s ease-out forwards';
  sidebarEl.style.opacity = '0';
  
  // Animate the sidebar in
  setTimeout(() => {
    sidebarEl.style.transition = 'opacity 0.3s ease';
    sidebarEl.style.opacity = '1';
  }, 100);
  
  // Track currently highlighted POI and markers
  let currentHighlightIndex = -1;
  let highlightMarkers: HTMLElement[] = [];
  let filteredPOIs = [...areaPOIs];
  let activeFilters: string[] = [];
  
  // Group POIs by type for filtering
  const poiTypeGroups = areaPOIs.reduce((groups: {[key: string]: number}, poi) => {
    const type = poi.category || 'other';
    groups[type] = (groups[type] || 0) + 1;
    return groups;
  }, {});
  
  // Create header
  const headerEl = document.createElement('div');
  headerEl.style.marginBottom = '16px';
  headerEl.innerHTML = `
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
      <div style="font-weight: 700; font-size: 20px;">
        Discover ${city.name}
      </div>
      <button id="close-sidebar-btn" style="
        background: none;
        border: none;
        font-size: 20px;
        cursor: pointer;
        opacity: 0.6;
      ">×</button>
    </div>
    <div style="font-size: 14px; opacity: 0.8; margin-bottom: 12px;">
      ${areaPOIs.length} points of interest nearby
    </div>
  `;
  
  // Create filter section
  const filterSectionEl = document.createElement('div');
  filterSectionEl.style.marginBottom = '16px';
  
  const filterLabelEl = document.createElement('div');
  filterLabelEl.style.fontSize = '14px';
  filterLabelEl.style.fontWeight = '600';
  filterLabelEl.style.marginBottom = '8px';
  filterLabelEl.textContent = 'Filter by type:';
  
  const filterChipsEl = document.createElement('div');
  filterChipsEl.style.display = 'flex';
  filterChipsEl.style.flexWrap = 'wrap';
  filterChipsEl.style.gap = '8px';
  
  const applyFilters = () => {
    // Apply filters to POIs
    if (activeFilters.length === 0) {
      filteredPOIs = [...areaPOIs];
    } else {
      filteredPOIs = areaPOIs.filter(poi => 
        activeFilters.includes(poi.category?.toLowerCase() || 'other')
      );
    }
    
    // Update POI list
    renderPOIList();
    
    // Clear and update highlights
    highlightMarkers.forEach(marker => {
      if (marker && marker.parentNode) {
        marker.parentNode.removeChild(marker);
      }
    });
    highlightMarkers = [];
    
    // Highlight the first POI if available
    if (filteredPOIs.length > 0) {
      highlightPOI(0);
    }
  };
  
  // Add filter chips for each POI type
  Object.entries(poiTypeGroups).forEach(([type, count]) => {
    const chipEl = document.createElement('div');
    chipEl.style.padding = '4px 10px';
    chipEl.style.borderRadius = '16px';
    chipEl.style.backgroundColor = 'rgba(0, 0, 0, 0.05)';
    chipEl.style.fontSize = '13px';
    chipEl.style.cursor = 'pointer';
    chipEl.style.display = 'flex';
    chipEl.style.alignItems = 'center';
    chipEl.style.gap = '4px';
    chipEl.dataset.poiType = type.toLowerCase();
    
    chipEl.innerHTML = `
      <span>${getPOITypeIcon(type)}</span>
      <span>${type} (${count})</span>
    `;
    
    // Add click handler for filtering
    chipEl.addEventListener('click', () => {
      const typeValue = chipEl.dataset.poiType || '';
      
      if (activeFilters.includes(typeValue)) {
        // Remove filter
        activeFilters = activeFilters.filter(t => t !== typeValue);
        chipEl.style.backgroundColor = 'rgba(0, 0, 0, 0.05)';
        chipEl.style.color = 'inherit';
      } else {
        // Add filter
        activeFilters.push(typeValue);
        chipEl.style.backgroundColor = getPOITypeColor(type);
        chipEl.style.color = 'white';
      }
      
      applyFilters();
    });
    
    filterChipsEl.appendChild(chipEl);
  });
  
  filterSectionEl.appendChild(filterLabelEl);
  filterSectionEl.appendChild(filterChipsEl);
  
  // Add search input
  const searchInputEl = document.createElement('div');
  searchInputEl.style.marginBottom = '16px';
  
  const searchInput = document.createElement('input');
  searchInput.type = 'text';
  searchInput.placeholder = 'Search points of interest...';
  searchInput.style.width = '100%';
  searchInput.style.padding = '8px 12px';
  searchInput.style.borderRadius = '8px';
  searchInput.style.border = '1px solid rgba(0, 0, 0, 0.1)';
  searchInput.style.fontSize = '14px';
  
  searchInput.addEventListener('input', () => {
    const searchTerm = searchInput.value.toLowerCase();
    
    if (searchTerm.length > 0) {
      // Apply both search term and active filters
      if (activeFilters.length === 0) {
        filteredPOIs = areaPOIs.filter(poi => 
          poi.name.toLowerCase().includes(searchTerm)
        );
      } else {
        filteredPOIs = areaPOIs.filter(poi => 
          poi.name.toLowerCase().includes(searchTerm) && 
          activeFilters.includes(poi.category?.toLowerCase() || 'other')
        );
      }
    } else {
      // Only apply active filters
      applyFilters();
    }
    
    renderPOIList();
  });
  
  searchInputEl.appendChild(searchInput);
  
  // Create POI list container
  const poiListEl = document.createElement('div');
  poiListEl.style.display = 'flex';
  poiListEl.style.flexDirection = 'column';
  poiListEl.style.gap = '10px';
  poiListEl.style.overflowY = 'auto';
  poiListEl.style.flexGrow = '1';
  poiListEl.style.paddingRight = '8px';
  
  // Function to create highlight marker for a POI
  const createHighlightMarker = (poi: PointOfInterest): HTMLElement | null => {
    if (!poi.coordinates) return null;
    
    // Create a pulsing marker to highlight the POI on the map
    const [lng, lat] = poi.coordinates as [number, number];
    const point = map.project([lng, lat]);
    
    const markerEl = document.createElement('div');
    markerEl.className = 'poi-highlight-marker';
    markerEl.style.position = 'absolute';
    markerEl.style.left = `${point.x}px`;
    markerEl.style.top = `${point.y}px`;
    markerEl.style.width = '30px';
    markerEl.style.height = '30px';
    markerEl.style.borderRadius = '50%';
    markerEl.style.backgroundColor = getPOITypeColor(poi.category || 'default');
    markerEl.style.transform = 'translate(-50%, -50%)';
    markerEl.style.zIndex = '2800';
    markerEl.style.animation = 'pulse 1.5s infinite';
    markerEl.style.boxShadow = '0 0 0 rgba(255, 255, 255, 0.7)';
    
    // Add bouncing arrow above the marker
    const arrowEl = document.createElement('div');
    arrowEl.style.position = 'absolute';
    arrowEl.style.top = '-30px';
    arrowEl.style.left = '50%';
    arrowEl.style.transform = 'translateX(-50%)';
    arrowEl.style.width = '20px';
    arrowEl.style.height = '20px';
    arrowEl.style.color = 'white';
    arrowEl.style.textAlign = 'center';
    arrowEl.style.fontSize = '20px';
    arrowEl.style.animation = 'bounce 1s infinite';
    arrowEl.innerHTML = '↓'; // Down arrow
    
    markerEl.appendChild(arrowEl);
    
    return markerEl;
  };
  
  // Function to update highlight marker positions on map move
  const updateMarkerPositions = () => {
    highlightMarkers.forEach((marker, i) => {
      if (i >= filteredPOIs.length) return;
      const poi = filteredPOIs[i];
      if (!poi.coordinates) return;
      
      const [lng, lat] = poi.coordinates as [number, number];
      const point = map.project([lng, lat]);
      marker.style.left = `${point.x}px`;
      marker.style.top = `${point.y}px`;
    });
  };
  
  // Function to highlight a specific POI
  const highlightPOI = (index: number) => {
    // Clear previous highlight
    if (currentHighlightIndex >= 0 && currentHighlightIndex < highlightMarkers.length) {
      const prevMarker = highlightMarkers[currentHighlightIndex];
      if (prevMarker && prevMarker.parentNode) {
        prevMarker.parentNode.removeChild(prevMarker);
      }
      
      // Remove active class from previous POI element
      const prevPoiEl = document.querySelector(`.poi-item[data-index="${currentHighlightIndex}"]`);
      if (prevPoiEl) {
        prevPoiEl.classList.remove('active');
        (prevPoiEl as HTMLElement).style.backgroundColor = 'rgba(0, 0, 0, 0.05)';
      }
    }
    
    currentHighlightIndex = index;
    
    // Create new highlight
    if (index >= 0 && index < filteredPOIs.length) {
      const marker = createHighlightMarker(filteredPOIs[index]);
      if (marker) {
        map.getContainer().appendChild(marker);
        highlightMarkers[index] = marker;
        
        // Add active class to current POI element
        const poiEl = document.querySelector(`.poi-item[data-index="${index}"]`);
        if (poiEl) {
          poiEl.classList.add('active');
          (poiEl as HTMLElement).style.backgroundColor = 'rgba(59, 130, 246, 0.1)';
          poiEl.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
        
        // Pan map to center on the POI
        map.panTo(filteredPOIs[index].coordinates as [number, number], {
          duration: 1000
        });
      }
    }
  };
  
  const renderPOIList = () => {
    // Clear current POI list
    poiListEl.innerHTML = '';
    
    if (filteredPOIs.length === 0) {
      const emptyStateEl = document.createElement('div');
      emptyStateEl.style.textAlign = 'center';
      emptyStateEl.style.padding = '20px';
      emptyStateEl.style.opacity = '0.7';
      emptyStateEl.innerHTML = `
        <div style="font-size: 40px; margin-bottom: 16px;">🔍</div>
        <div style="font-weight: 600; margin-bottom: 8px;">No points of interest found</div>
        <div style="font-size: 14px;">Try adjusting your filters or search term</div>
      `;
      poiListEl.appendChild(emptyStateEl);
      return;
    }
    
    // Add POIs to the list
    filteredPOIs.forEach((poi, index) => {
      const poiEl = document.createElement('div');
      poiEl.className = 'poi-item';
      poiEl.dataset.index = `${index}`;
      poiEl.style.display = 'flex';
      poiEl.style.padding = '12px';
      poiEl.style.borderRadius = '8px';
      poiEl.style.backgroundColor = 'rgba(0, 0, 0, 0.05)';
      poiEl.style.cursor = 'pointer';
      poiEl.style.transition = 'background-color 0.2s ease';
      
      // POI icon and category
      const iconEl = document.createElement('div');
      iconEl.style.width = '40px';
      iconEl.style.height = '40px';
      iconEl.style.borderRadius = '50%';
      iconEl.style.backgroundColor = getPOITypeColor(poi.category || 'default');
      iconEl.style.display = 'flex';
      iconEl.style.justifyContent = 'center';
      iconEl.style.alignItems = 'center';
      iconEl.style.fontSize = '18px';
      iconEl.style.color = 'white';
      iconEl.style.marginRight = '12px';
      iconEl.style.flexShrink = '0';
      iconEl.textContent = getPOITypeIcon(poi.category || 'default');
      
      // POI details
      const detailsEl = document.createElement('div');
      detailsEl.style.flexGrow = '1';
      detailsEl.style.overflow = 'hidden';
      
      const nameEl = document.createElement('div');
      nameEl.style.fontWeight = '600';
      nameEl.style.fontSize = '15px';
      nameEl.style.marginBottom = '4px';
      nameEl.style.whiteSpace = 'nowrap';
      nameEl.style.overflow = 'hidden';
      nameEl.style.textOverflow = 'ellipsis';
      nameEl.textContent = poi.name;
      
      const typeEl = document.createElement('div');
      typeEl.style.fontSize = '13px';
      typeEl.style.opacity = '0.7';
      typeEl.textContent = poi.category || 'Point of Interest';
      
      detailsEl.appendChild(nameEl);
      detailsEl.appendChild(typeEl);
      
      // Add action button
      const actionEl = document.createElement('div');
      actionEl.style.display = 'flex';
      actionEl.style.alignItems = 'center';
      
      const discoverBtn = document.createElement('button');
      discoverBtn.style.backgroundColor = '#3B82F6';
      discoverBtn.style.color = 'white';
      discoverBtn.style.border = 'none';
      discoverBtn.style.borderRadius = '4px';
      discoverBtn.style.padding = '6px 10px';
      discoverBtn.style.fontSize = '13px';
      discoverBtn.style.cursor = 'pointer';
      discoverBtn.textContent = 'Discover';
      
      discoverBtn.addEventListener('click', (e) => {
        e.stopPropagation(); // Prevent POI from being highlighted
        onPOIDiscovered(poi);
        
        // Fly to POI with zoom
        map.flyTo({
          center: poi.coordinates as [number, number],
          zoom: 14,
          duration: 1500
        });
      });
      
      actionEl.appendChild(discoverBtn);
      
      // Assemble POI item
      poiEl.appendChild(iconEl);
      poiEl.appendChild(detailsEl);
      poiEl.appendChild(actionEl);
      
      // Add hover and click effects
      poiEl.addEventListener('mouseenter', () => {
        highlightPOI(index);
      });
      
      poiEl.addEventListener('click', () => {
        highlightPOI(index);
      });
      
      poiListEl.appendChild(poiEl);
    });
  };
  
  // Assemble sidebar
  sidebarEl.appendChild(headerEl);
  sidebarEl.appendChild(filterSectionEl);
  sidebarEl.appendChild(searchInputEl);
  sidebarEl.appendChild(poiListEl);
  
  // Add close button functionality
  const closeBtn = headerEl.querySelector('#close-sidebar-btn') as HTMLButtonElement;
  if (closeBtn) {
    closeBtn.addEventListener('click', () => {
      // Clear highlight markers
      highlightMarkers.forEach(marker => {
        if (marker && marker.parentNode) {
          marker.parentNode.removeChild(marker);
        }
      });
      
      // Remove map event listeners
      map.off('move', updateMarkerPositions);
      
      // Hide sidebar with animation
      sidebarEl.style.opacity = '0';
      setTimeout(() => {
        if (sidebarEl.parentNode) {
          sidebarEl.parentNode.removeChild(sidebarEl);
        }
      }, 300);
      
      // Call the continue journey handler
      onContinueJourney();
    });
  }
  
  // Add to map container
  map.getContainer().appendChild(sidebarEl);
  
  // Add event listener for map movement to update marker positions
  map.on('move', updateMarkerPositions);
  
  // Initial render of POI list
  renderPOIList();
  
  // Pause animation while showing area sidebar
  pauseAnimation();
  
  // Automatically highlight the first POI
  if (filteredPOIs.length > 0) {
    setTimeout(() => {
      highlightPOI(0);
    }, 500);
  }
}; 