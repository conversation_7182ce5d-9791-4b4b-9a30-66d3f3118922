/**
 * ZIndexManager.ts
 * 
 * A utility module that provides consistent z-index management across the application.
 * This ensures UI elements stack correctly without hard-coded values scattered throughout the codebase.
 */

import { ANIMATION_CONSTANTS } from './constants';

/**
 * Supported UI layer types
 */
export type UILayerType = 
  | 'map_base'
  | 'map_markers'
  | 'vehicle_marker'
  | 'notification_indicators'
  | 'ui_controls'
  | 'notification_panel'
  | 'sidebar'
  | 'modal';

/**
 * Get a consistent z-index for the specified UI layer
 */
export const getZIndex = (layer: UILayerType): number => {
  switch (layer) {
    case 'map_base':
      return ANIMATION_CONSTANTS.Z_INDEX.MAP_BASE;
    case 'map_markers':
      return ANIMATION_CONSTANTS.Z_INDEX.MAP_MARKERS;
    case 'vehicle_marker':
      return ANIMATION_CONSTANTS.Z_INDEX.VEHICLE_MARKER;
    case 'notification_indicators':
      return ANIMATION_CONSTANTS.Z_INDEX.NOTIFICATION_INDICATORS;
    case 'ui_controls':
      return ANIMATION_CONSTANTS.Z_INDEX.UI_CONTROLS;
    case 'notification_panel':
      return ANIMATION_CONSTANTS.Z_INDEX.NOTIFICATION_PANEL;
    case 'sidebar':
      return ANIMATION_CONSTANTS.Z_INDEX.SIDEBAR;
    case 'modal':
      return ANIMATION_CONSTANTS.Z_INDEX.MODAL;
    default:
      console.warn(`Unknown UI layer type: ${layer}, using default z-index`);
      return 1;
  }
};

interface ConflictItem {
  zIndex: number;
  elements: Array<{ id: string; classNames: string }>;
}

/**
 * Check for z-index conflicts between elements
 * For debugging purposes
 */
export const findZIndexConflicts = (): void => {
  // Get all elements with explicit z-index
  const elementsWithZIndex = Array.from(document.querySelectorAll('*'))
    .filter(el => {
      const style = window.getComputedStyle(el);
      return style.zIndex !== 'auto' && style.position !== 'static';
    })
    .map(el => ({
      element: el,
      zIndex: parseInt(window.getComputedStyle(el).zIndex, 10),
      id: (el as HTMLElement).id || 'unknown',
      classNames: (el as HTMLElement).className,
    }))
    .sort((a, b) => a.zIndex - b.zIndex);
  
  console.log('Elements with z-index:', elementsWithZIndex);
  
  // Check for potential conflicts
  const conflicts: ConflictItem[] = [];
  const zIndexMap = new Map<number, Array<{id: string, classNames: string}>>();
  
  elementsWithZIndex.forEach(item => {
    if (!zIndexMap.has(item.zIndex)) {
      zIndexMap.set(item.zIndex, []);
    }
    zIndexMap.get(item.zIndex)?.push({
      id: item.id,
      classNames: item.classNames,
    });
  });
  
  // Find elements with the same z-index
  zIndexMap.forEach((items, zIndex) => {
    if (items.length > 1) {
      conflicts.push({
        zIndex,
        elements: items,
      });
    }
  });
  
  if (conflicts.length > 0) {
    console.warn('Potential z-index conflicts found:', conflicts);
  } else {
    console.log('No z-index conflicts found');
  }
};

// Add the function to window debug object for easy access
if (typeof window !== 'undefined') {
  (window as any).findZIndexConflicts = findZIndexConflicts;
} 