/**
 * Utility functions for mathematical operations related to map animations
 */

/**
 * <PERSON>arly interpolate between two angles (in degrees), taking the shortest path
 * around the circle. This prevents jitter when transitioning across the 0/360 boundary.
 */
export const lerpAngle = (start: number, end: number, t: number): number => {
  // Normalize angles to [0, 360)
  start = ((start % 360) + 360) % 360;
  end = ((end % 360) + 360) % 360;
  
  // Find the shortest direction to interpolate
  let delta = end - start;
  if (Math.abs(delta) > 180) {
    delta = delta > 0 ? delta - 360 : delta + 360;
  }
  
  // Calculate interpolated angle and normalize result
  return ((start + delta * t) % 360 + 360) % 360;
};

/**
 * Apply smoothing with hysteresis to prevent small jitters
 * @param current Current value
 * @param target Target value
 * @param factor Smoothing factor (0-1, higher = more smoothing)
 * @param threshold Minimum change threshold (hysteresis)
 */
export const smoothWithHysteresis = (
  current: number, 
  target: number, 
  factor: number, 
  threshold: number
): number => {
  // If change is smaller than threshold, ignore it to prevent jitter
  if (Math.abs(target - current) < threshold) {
    return current;
  }
  
  // Apply smoothing factor
  return current + (target - current) * factor;
};

/**
 * Calculate exponential smoothing for camera movement
 * Provides smoother deceleration as values approach the target
 */
export const exponentialSmoothing = (
  current: number, 
  target: number, 
  factor: number
): number => {
  const delta = target - current;
  const smoothedDelta = delta * (1 - Math.exp(-factor));
  return current + smoothedDelta;
};

/**
 * Apply critical damping for camera movement
 * Provides smooth but responsive movement without oscillation
 */
export const criticalDamp = (
  current: number, 
  target: number, 
  velocity: number, 
  dampingRatio: number, 
  dt: number
): { value: number, velocity: number } => {
  const spring = 1.0;
  const damping = 2 * Math.sqrt(spring) * dampingRatio;
  
  // Calculate acceleration
  const acceleration = spring * (target - current) - damping * velocity;
  
  // Update velocity
  const newVelocity = velocity + acceleration * dt;
  
  // Update position
  const newValue = current + newVelocity * dt;
  
  return { value: newValue, velocity: newVelocity };
}; 