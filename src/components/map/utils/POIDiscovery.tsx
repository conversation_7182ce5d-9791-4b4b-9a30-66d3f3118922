import * as turf from '@turf/turf';
import { Destination } from '@/types/destination';
import { PointOfInterest } from '@/types/poi';
import { calculatePOIImportance } from '@/utils/poi-importance';
import { findCulturalRegion } from '@/utils/cultural-regions';

// Add cooldown tracking for notifications
let lastNotificationTime = 0;
const NOTIFICATION_COOLDOWN = 15000; // 15 seconds
const CITY_NOTIFICATION_COOLDOWN = 30000; // 30 seconds for cities - doubled to reduce jittering
const MARRAKECH_NOTIFICATION_COOLDOWN = 45000; // 45 seconds specifically for Marrakech - even longer cooldown

// Add distance and time tracking references for area change hysteresis
const prevDistanceRef = { current: null as number | null };
const lastAreaChangeTime = { current: Date.now() };

// Function to check nearby POIs from the current position
export const checkNearbyPOIs = (
  currentPoint: [number, number],
  pois: PointOfInterest[],
  discoveredPOIs: Set<string>,
  detectionRadius: number,
  userInterests: string[] = [],
  selectedCategories: string[] = [],
  currentArea: string | null = null
): PointOfInterest[] => {
  if (!pois || !currentPoint) return [];
  
  // Check cooldown with special handling for cities
  const now = Date.now();
  // TEMP DISABLED: Treat Marrakech just like other cities
  const isMarrakech = false; // currentArea === 'Marrakech';
  const isCity = currentArea !== null; // Any named area is considered a city
  
  // Apply different cooldowns based on context
  const effectiveCooldown = isCity ? CITY_NOTIFICATION_COOLDOWN : NOTIFICATION_COOLDOWN;
  
  if (now - lastNotificationTime < effectiveCooldown) {
    return [];
  }
  
  try {
    // Find POIs within detection radius that match selected categories
    const nearbyPOIs = pois.filter(poi => {
      if (!poi.coordinates || discoveredPOIs.has(poi.id)) return false;
      
      // Check if POI matches selected categories
      if (selectedCategories.length > 0 && !selectedCategories.includes(poi.type)) {
        return false;
      }
      
      const distance = turf.distance(
        turf.point(currentPoint),
        turf.point(poi.coordinates as [number, number]),
        { units: 'kilometers' }
      );
      
      // Apply a smaller detection radius when in cities to prevent too many POIs
      const effectiveRadius = isCity ? Math.min(detectionRadius, 3) : detectionRadius;
      
      return distance <= effectiveRadius && !discoveredPOIs.has(poi.id);
    });
    
    // Sort by importance and distance
    nearbyPOIs.sort((a, b) => {
      const importanceA = calculatePOIImportance(a, { interests: userInterests });
      const importanceB = calculatePOIImportance(b, { interests: userInterests });
      
      if (importanceA !== importanceB) return importanceB - importanceA;
      
      // If same importance, sort by distance
      const distA = turf.distance(
        turf.point(currentPoint),
        turf.point(a.coordinates as [number, number]),
        { units: 'kilometers' }
      );
      
      const distB = turf.distance(
        turf.point(currentPoint),
        turf.point(b.coordinates as [number, number]),
        { units: 'kilometers' }
      );
      
      return distA - distB;
    });
    
    // Update notification cooldown if we found POIs
    if (nearbyPOIs.length > 0) {
      lastNotificationTime = now;
      console.log(`Found ${nearbyPOIs.length} POIs near ${currentArea || 'current position'}. Next check in ${effectiveCooldown/1000}s`);
    }
    
    // For cities, especially Marrakech, limit the number of POIs returned at once
    if (isCity) {
      const limit = isMarrakech ? 1 : 2; // Only return 1 POI at a time in Marrakech, 2 in other cities
      return nearbyPOIs.slice(0, limit);
    }
    
    // Return all matching POIs for non-city areas
    return nearbyPOIs;
  } catch (error) {
    console.error('Error checking nearby POIs:', error);
    return [];
  }
};

// Function to check if we're in a cultural region
export const checkCulturalRegions = (
  currentPoint: [number, number],
  currentRegionRef: React.MutableRefObject<string | null>
): string | null => {
  try {
    // Check if we're in a significant cultural region
    const region = findCulturalRegion(currentPoint);
    
    // If entering a new region
    if (region && region.name !== currentRegionRef.current) {
      currentRegionRef.current = region.name;
      console.log(`Entered cultural region: ${region.name}`);
      return region.name;
    }
    
    return null;
  } catch (error) {
    console.error('Error checking cultural regions:', error);
    return null;
  }
};

// Enhanced area change detection with approach warning and hysteresis buffer
export const checkAreaChange = (
  currentPoint: [number, number],
  allDestinations: Destination[] | undefined,
  currentArea: string | null,
  detectionRadius: number,
  approachRadius: number = 10 // km before city to start warning
): { 
  newArea: boolean; 
  city: Destination | null; 
  distance: number;
  isApproaching: boolean;
} => {
  if (!allDestinations || !allDestinations.length) {
    return { newArea: false, city: null, distance: 0, isApproaching: false };
  }
  
  // Find the nearest city to current position
  let nearestCity: Destination | null = null;
  let shortestDistance = Infinity;
  
  allDestinations.forEach(dest => {
    if (!dest.coordinates) return;
    
    const distance = turf.distance(
      turf.point(currentPoint),
      turf.point(dest.coordinates as [number, number]),
      { units: 'kilometers' }
    );
    
    if (distance < shortestDistance) {
      shortestDistance = distance;
      nearestCity = dest;
    }
  });
  
  // Check if we're approaching or entering a new area with improved hysteresis
  if (nearestCity) {
    // Add city-specific thresholds
    let entryThreshold = detectionRadius;              // Standard detection radius for entry
    let exitThreshold = detectionRadius + 10;          // Add 10km buffer for exit to prevent oscillation
    let minimumChangeInterval = 3000;                  // 3 seconds minimum between area changes
    
    // Special handling for Marrakech to prevent jittering
    if (nearestCity.name === 'Marrakech' || currentArea === 'Marrakech') {
      // TEMP DISABLED: Treat Marrakech like any other city
      entryThreshold = detectionRadius;            
      exitThreshold = detectionRadius + 10;            
      minimumChangeInterval = 3000;                   
    }
    
    // Store previous distance for trend detection
    const distanceIsTrending = prevDistanceRef.current !== null ? 
      shortestDistance - prevDistanceRef.current : 0;
    
    // Update the previous distance for next time
    prevDistanceRef.current = shortestDistance;
    
    // Check time since last area change
    const now = Date.now();
    const timeSinceLastChange = now - lastAreaChangeTime.current;
    
    // If we're already in an area, use the exit threshold
    // If we're not in an area or in a different area, use the entry threshold
    const effectiveThreshold = 
      (currentArea === nearestCity.name) ? exitThreshold : entryThreshold;
    
    // Determine if we're entering a new area based on the appropriate threshold
    // and ensure we're not rapidly oscillating
    let isNewArea = false;
    
    if (timeSinceLastChange > minimumChangeInterval) {
      if (currentArea !== nearestCity.name && shortestDistance <= entryThreshold) {
        // Only consider entering if we're steadily approaching or within the inner threshold
        if (distanceIsTrending <= 0 || shortestDistance <= entryThreshold - 2) {
          isNewArea = true;
          lastAreaChangeTime.current = now;
          console.log(`Entering ${nearestCity.name} (distance: ${shortestDistance.toFixed(2)}km)`);
        }
      } else if (currentArea === nearestCity.name && shortestDistance > exitThreshold) {
        // Only consider exiting if we're steadily moving away and well past the outer threshold
        if (distanceIsTrending >= 0 && shortestDistance >= exitThreshold + 2) {
          isNewArea = true;
          lastAreaChangeTime.current = now;
          console.log(`Exiting ${currentArea} (distance: ${shortestDistance.toFixed(2)}km)`);
        }
      }
    }
    
    const isApproaching = shortestDistance <= approachRadius;
    
    // Log state changes for debugging
    if (isNewArea) {
      console.log(`Area change: ${currentArea || 'None'} -> ${nearestCity.name} (distance: ${shortestDistance.toFixed(2)}km, threshold: ${effectiveThreshold}km, trend: ${distanceIsTrending.toFixed(2)}km)`);
    }
    
    return { 
      newArea: isNewArea, 
      city: nearestCity,
      distance: shortestDistance,
      isApproaching
    };
  }
  
  return { 
    newArea: false, 
    city: null,
    distance: 0,
    isApproaching: false
  };
};

// Function to get POIs in an area
export const getPOIsInArea = (
  areaName: string,
  radius: number,
  allDestinations: Destination[] | undefined,
  pois: PointOfInterest[],
  userInterests: string[] = []
): PointOfInterest[] => {
  if (!allDestinations || !pois.length) return [];
  
  // Find the city/area
  const area = allDestinations.find(dest => dest.name === areaName);
  if (!area || !area.coordinates) return [];
  
  // Find POIs within radius
  const areaPOIs = pois.filter(poi => {
    if (!poi.coordinates) return false;
    
    const distance = turf.distance(
      turf.point(area.coordinates as [number, number]),
      turf.point(poi.coordinates as [number, number]),
      { units: 'kilometers' }
    );
    
    return distance <= radius;
  });
  
  // Sort by importance
  areaPOIs.sort((a, b) => {
    const importanceA = calculatePOIImportance(a, { interests: userInterests });
    const importanceB = calculatePOIImportance(b, { interests: userInterests });
    return importanceB - importanceA;
  });
  
  return areaPOIs;
};

// Function to find approaching POIs
export const findApproachingPOIs = (
  currentPoint: [number, number],
  pois: PointOfInterest[],
  discoveredPOIs: Set<string>,
  minDistance: number = 3,
  maxDistance: number = 8,
  userInterests: string[] = []
): { poi: PointOfInterest; distance: number } | null => {
  if (!pois || !currentPoint) return null;
  
  try {
    console.log(`[findApproachingPOIs] Searching among ${pois.length} POIs`);
    console.log(`[findApproachingPOIs] Current position: ${currentPoint[0]}, ${currentPoint[1]}`);
    console.log(`[findApproachingPOIs] Distance range: ${minDistance}km - ${maxDistance}km`);
    
    // Find POIs that are in the approaching distance range
    const approachingPOIs = pois.filter(poi => {
      if (!poi.coordinates || discoveredPOIs.has(poi.id)) return false;
      
      const distance = turf.distance(
        turf.point(currentPoint),
        turf.point(poi.coordinates as [number, number]),
        { units: 'kilometers' }
      );
      
      // Log each POI's distance for debugging
      if (distance < maxDistance * 1.5) {
        console.log(`[findApproachingPOIs] POI ${poi.name}: ${distance.toFixed(2)}km away (${minDistance < distance && distance < maxDistance ? 'IN RANGE' : 'out of range'})`);
      }
      
      // Only consider POIs within the specified distance range
      return distance > minDistance && distance < maxDistance && !discoveredPOIs.has(poi.id);
    });

    console.log(`[findApproachingPOIs] Found ${approachingPOIs.length} POIs in approaching distance`);
    
    if (approachingPOIs.length === 0) return null;
    
    // Sort by importance and distance
    approachingPOIs.sort((a, b) => {
      const importanceA = calculatePOIImportance(a, { interests: userInterests });
      const importanceB = calculatePOIImportance(b, { interests: userInterests });
      
      // If importance is different, prioritize based on importance
      if (importanceA !== importanceB) return importanceB - importanceA;
      
      // Otherwise prioritize by distance
      const distA = turf.distance(
        turf.point(currentPoint),
        turf.point(a.coordinates as [number, number]),
        { units: 'kilometers' }
      );
      
      const distB = turf.distance(
        turf.point(currentPoint),
        turf.point(b.coordinates as [number, number]),
        { units: 'kilometers' }
      );
      
      return distA - distB;
    });
    
    // Get the best match
    const bestMatch = approachingPOIs[0];
    const distance = turf.distance(
      turf.point(currentPoint),
      turf.point(bestMatch.coordinates as [number, number]),
      { units: 'kilometers' }
    );
    
    console.log(`[findApproachingPOIs] Best match: ${bestMatch.name} (${distance.toFixed(2)}km away)`);
    
    return {
      poi: bestMatch,
      distance
    };
  } catch (error) {
    console.error('Error finding approaching POIs:', error);
    return null;
  }
}; 