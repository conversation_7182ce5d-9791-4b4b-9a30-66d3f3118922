import mapboxgl from 'mapbox-gl';
import { calculateBearing } from './MapHelpers';

/**
 * Creates and displays a visual direction indicator pointing from the vehicle to a POI
 * 
 * @param map - The Mapbox map instance
 * @param vehiclePosition - Current vehicle position [lng, lat]
 * @param poiCoordinates - POI position to point towards [lng, lat]
 * @param duration - How long to show the indicator (ms)
 */
export const showDirectionIndicator = (
  map: mapboxgl.Map,
  vehiclePosition: [number, number],
  poiCoordinates: [number, number],
  duration: number = 3000
): void => {
  if (!map || !vehiclePosition) return;
  
  // Remove any existing indicator
  const existingIndicator = document.getElementById('direction-indicator');
  if (existingIndicator && existingIndicator.parentNode) {
    existingIndicator.parentNode.removeChild(existingIndicator);
  }
  
  // Calculate direction from vehicle to POI
  const bearing = calculateBearing(
    vehiclePosition,
    poiCoordinates
  );
  
  // Create indicator element
  const indicatorEl = document.createElement('div');
  indicatorEl.id = 'direction-indicator';
  indicatorEl.className = 'direction-indicator';
  indicatorEl.style.position = 'absolute';
  indicatorEl.style.width = '40px';
  indicatorEl.style.height = '40px';
  indicatorEl.style.borderRadius = '50%';
  indicatorEl.style.background = 'rgba(255, 255, 255, 0.8)';
  indicatorEl.style.display = 'flex';
  indicatorEl.style.justifyContent = 'center';
  indicatorEl.style.alignItems = 'center';
  indicatorEl.style.animation = 'pulse 1s infinite';
  indicatorEl.style.zIndex = '2500';
  
  // Add arrow SVG based on bearing
  indicatorEl.innerHTML = `
    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 5V19M12 5L6 11M12 5L18 11" stroke="#000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" 
        transform="rotate(${90 - bearing}, 12, 12)"/>
    </svg>
  `;
  
  // Position at edge of viewport in direction of POI
  // Calculate position based on viewport center and bearing
  const mapContainer = map.getContainer();
  const centerX = mapContainer.offsetWidth / 2;
  const centerY = mapContainer.offsetHeight / 2;
  const radius = Math.min(centerX, centerY) * 0.8;
  
  const x = centerX + radius * Math.sin(bearing * Math.PI / 180);
  const y = centerY - radius * Math.cos(bearing * Math.PI / 180);
  
  indicatorEl.style.left = `${x - 20}px`;
  indicatorEl.style.top = `${y - 20}px`;
  
  // Add to map container
  map.getContainer().appendChild(indicatorEl);
  
  // Remove after specified duration
  setTimeout(() => {
    if (indicatorEl.parentNode) {
      indicatorEl.style.animation = 'fadeOut 0.3s ease-in forwards';
      setTimeout(() => {
        if (indicatorEl.parentNode) {
          indicatorEl.parentNode.removeChild(indicatorEl);
        }
      }, 300);
    }
  }, duration);
}; 