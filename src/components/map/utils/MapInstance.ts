/**
 * MapInstance.ts
 * 
 * This file provides a singleton access point to the map instance.
 * It allows modules that don't have direct access to the map prop
 * to still interact with the map when necessary.
 */

import mapboxgl from 'mapbox-gl';

// Global map instance reference
let mapInstance: mapboxgl.Map | null = null;

/**
 * Set the map instance for global access
 */
export const setMapInstance = (map: mapboxgl.Map | null) => {
  mapInstance = map;
  console.log(`🗺️ [${Date.now()}] Map instance ${map ? 'set' : 'cleared'}`);
};

/**
 * Get the current map instance
 */
export const getMapInstance = (): mapboxgl.Map | null => {
  return mapInstance;
};

/**
 * Check if the map instance exists and is loaded
 */
export const isMapReady = (): boolean => {
  return !!mapInstance && mapInstance.loaded();
};

/**
 * Update the map camera to focus on a specific location
 */
export const updateMapCamera = (
  center: [number, number],
  zoom: number,
  pitch: number = 45,
  bearing: number = 0,
  duration: number = 1000
) => {
  if (!mapInstance) {
    console.warn(`⚠️ [${Date.now()}] Cannot update camera: Map instance not available`);
    return false;
  }
  
  try {
    mapInstance.easeTo({
      center,
      zoom,
      pitch,
      bearing,
      duration,
      essential: true
    });
    return true;
  } catch (error) {
    console.error(`❌ [${Date.now()}] Error updating map camera:`, error);
    return false;
  }
}; 