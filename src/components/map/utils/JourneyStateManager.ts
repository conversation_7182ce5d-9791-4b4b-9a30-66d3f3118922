/**
 * JourneyStateManager.ts
 * 
 * Provides utilities for persisting and recovering journey state.
 * Implements the state persistence enhancement from our architecture roadmap.
 */

import { JourneyPhase } from './types';

/**
 * Structure for journey state snapshots used for persistence
 * and recovery mechanisms
 */
export interface JourneyStateSnapshot {
  timestamp: number;
  phase: JourneyPhase;
  progress: number;
  position: [number, number] | null;
  route: Array<[number, number]> | null;
  pointIndex: number;
  destinationIds: string[];
  discoveredPOIs: string[];
  lastUpdateTime: number;
}

/**
 * Persistence key for localStorage
 */
const JOURNEY_STATE_KEY = 'morocco_journey_state';

/**
 * Create a journey state snapshot with current state
 */
export const createJourneyStateSnapshot = (
  phase: JourneyPhase,
  progress: number,
  position: [number, number] | null,
  route: Array<[number, number]> | null,
  pointIndex: number,
  destinationIds: string[],
  discoveredPOIs: string[]
): JourneyStateSnapshot => {
  return {
    timestamp: Date.now(),
    phase,
    progress,
    position,
    route,
    pointIndex,
    destinationIds,
    discoveredPOIs,
    lastUpdateTime: Date.now()
  };
};

/**
 * Persist journey state to localStorage
 */
export const persistJourneyState = (state: JourneyStateSnapshot): void => {
  try {
    localStorage.setItem(JOURNEY_STATE_KEY, JSON.stringify(state));
    console.log(`💾 [${Date.now()}] Persisting journey state`);
  } catch (error) {
    console.error('Failed to persist journey state:', error);
  }
};

/**
 * Recover journey state from localStorage
 */
export const recoverJourneyState = (): JourneyStateSnapshot | null => {
  try {
    const savedState = localStorage.getItem(JOURNEY_STATE_KEY);
    if (!savedState) {
      console.log('No saved journey state found');
      return null;
    }
    
    const parsedState = JSON.parse(savedState) as JourneyStateSnapshot;
    console.log(`🔄 [${Date.now()}] Recovering journey state`);
    return parsedState;
  } catch (error) {
    console.error('Failed to recover journey state:', error);
    return null;
  }
};

/**
 * Clear persisted journey state
 */
export const clearJourneyState = (): void => {
  try {
    localStorage.removeItem(JOURNEY_STATE_KEY);
    console.log('Journey state cleared');
  } catch (error) {
    console.error('Failed to clear journey state:', error);
  }
};

/**
 * Check if a journey state snapshot is valid for recovery
 */
export const isValidJourneyState = (state: JourneyStateSnapshot | null): boolean => {
  if (!state) return false;
  
  // State must have required properties
  if (!state.phase || state.progress === undefined || !state.timestamp) {
    return false;
  }
  
  // State must not be too old (24 hours)
  const MAX_AGE_MS = 24 * 60 * 60 * 1000;
  const now = Date.now();
  const age = now - state.timestamp;
  if (age > MAX_AGE_MS) {
    console.log(`Journey state is too old (${age / 1000 / 60 / 60} hours)`);
    return false;
  }
  
  return true;
};

/**
 * Check if journey can be resumed
 */
export const canResumeJourney = (): boolean => {
  return isValidJourneyState(recoverJourneyState());
};

/**
 * Get journey state debug information
 */
export const getJourneyStateDebugInfo = (): any => {
  const state = recoverJourneyState();
  if (!state) return { exists: false };
  
  const now = Date.now();
  return {
    exists: true,
    phase: state.phase,
    progress: state.progress,
    age: now - state.timestamp,
    lastUpdate: now - state.lastUpdateTime,
    destinationCount: state.destinationIds.length,
    discoveredPOICount: state.discoveredPOIs.length
  };
};

/**
 * Setup auto-save mechanism for journey state
 * Returns a cleanup function to remove the interval
 */
export const setupAutoSave = (
  getStateSnapshot: () => JourneyStateSnapshot,
  intervalMs: number = 10000
): () => void => {
  console.log(`Setting up journey state auto-save (${intervalMs}ms interval)`);
  
  const intervalId = setInterval(() => {
    const state = getStateSnapshot();
    persistJourneyState(state);
  }, intervalMs);
  
  return () => {
    clearInterval(intervalId);
    console.log('Journey state auto-save disabled');
  };
}; 