/**
 * Animation Constants
 * 
 * This file provides standard animation durations and timing values
 * for consistent animation behavior throughout the application.
 */

// Base animation durations
export const ANIMATION_CONSTANTS = {
  // Core animation durations
  BASE_ROUTE_ANIMATION_DURATION: 180000, // 3 minutes for full route animation
  
  // UI animation durations
  UI_TRANSITION_DURATION: 500, // Standard 500ms for UI transitions
  
  // Camera animation durations
  CAMERA_TRANSITION_SHORT: 500,
  CAMERA_TRANSITION_MEDIUM: 1000,
  CAMERA_TRANSITION_LONG: 2000,
  
  // Frame timing
  ANIMATION_FRAME_DELAY: 100, // 100ms delay between animation frames
  
  // Zoom levels
  ZOOM_ROUTE_TRAVEL: 8,
  ZOOM_CITY_EXPLORATION: 14,
  ZOOM_POI_DETAIL: 16,
  
  // z-index values for consistent stacking
  Z_INDEX: {
    MAP_BASE: 1,
    MAP_MARKERS: 100,
    VEHICLE_MARKER: 200,
    NOTIFICATION_INDICATORS: 500,
    UI_CONTROLS: 800,
    NOTIFICATION_PANEL: 1000,
    SIDEBAR: 1200,
    MOD<PERSON>: 5000
  }
};

// Animation speed modifiers
export const SPEED_MODIFIERS = {
  DEFAULT: 1.0,
  CITY_APPROACH: 0.7, // Slow down when approaching cities
  POI_CLUSTER: 0.8, // Slow down when near POI clusters
  CULTURAL_REGION: 0.9, // Slight slowdown in cultural regions
  MOUNTAIN: 1.3, // Slower in mountains
  DESERT: 0.9, // Slightly slower in deserts
  HIGHWAY: 0.7, // Faster on highways
};

// Performance monitoring thresholds
export const PERFORMANCE_THRESHOLDS = {
  LOW_FPS_THRESHOLD: 20,
  MEMORY_WARNING_THRESHOLD: 100 * 1024 * 1024, // 100MB
  STALLED_ANIMATION_THRESHOLD_MS: 2000, // 2 seconds without frame updates
  RESOURCE_CLEANUP_DELAY_MS: 5000, // 5 seconds after animation completes
}; 