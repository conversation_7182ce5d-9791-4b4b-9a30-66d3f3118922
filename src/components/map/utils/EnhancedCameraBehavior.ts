/**
 * EnhancedCameraBehavior.ts
 * 
 * Provides optimized camera behaviors with:
 * - Position averaging for jitter-free camera movement
 * - Look-ahead logic for smoother vehicle following
 * - Adaptive zoom based on terrain and context
 * - Proper easing functions for natural transitions
 * 
 * This follows the Map Camera Rules for proper zoom levels and transitions.
 */

import mapboxgl from 'mapbox-gl';
import AnimationDebugTools from '../animation/AnimationDebugTools';
import * as turf from '@turf/turf';
import { determineTerrainType } from './MapHelpers';

// Define CameraContext interface
interface CameraContext {
  terrain?: string;
  inCity?: boolean;
  cityName?: string;
  speed?: number; 
  forceUpdate?: boolean;
  isDiscoveringPOI?: boolean;
}

interface FollowVehicleOptions extends CameraContext {
  leftPanelWidth?: number;
  rightPanelWidth?: number;
  topPadding?: number;
  bottomPadding?: number;
}

// Camera constants based on Map Camera Rules
const ZOOM_LEVELS = {
  ROUTE_TRAVEL: 8,       // For route travel animations
  CITY_EXPLORATION: 14,  // For city exploration and POI discovery
  POI_DETAIL: 16,        // When focusing on a specific POI
  MOUNTAIN_VIEW: 10,     // For mountain terrain
  DESERT_VIEW: 9,        // For desert terrain
  COASTAL_VIEW: 11,      // For coastal areas
  TRANSITION_START: 6,   // When starting a major transition
};

const CAMERA_SETTINGS = {
  DEFAULT_PITCH: 45,     // Default pitch for 3D perspective
  MOUNTAIN_PITCH: 60,    // Higher pitch for mountain views
  CITY_PITCH: 50,        // Medium-high pitch for urban views
  MIN_TRANSITION_DURATION: 500,  // Minimum transition duration in ms
  DRAMATIC_TRANSITION_DURATION: 2000, // For dramatic transitions
};

// Types and interfaces
export interface CameraState {
  center: [number, number];
  zoom: number;
  bearing: number;
  pitch: number;
  transitionDuration: number;
  terrain: string;
}

export interface LookAheadOptions {
  enabled: boolean;
  distance: number;  // How far to look ahead (0-1 as fraction of screen)
  adaptiveMode: 'fixed' | 'speed-based' | 'terrain-based';
}

interface MapTransitionOptions {
  center: [number, number];
  zoom: number;
  bearing: number;
  pitch: number;
  duration: number;
  essential: boolean;
  easing?: (t: number) => number;
  padding?: mapboxgl.PaddingOptions;
}

interface CameraTransitionOptions {
  duration?: number;
  easing?: (t: number) => number;
  essential?: boolean;
  animate?: boolean;
  maxDuration?: number;
}

export class EnhancedCameraBehavior {
  private static instance: EnhancedCameraBehavior;
  
  // State tracking
  private map: mapboxgl.Map | null = null;
  private currentState: CameraState = {
    center: [0, 0],
    zoom: ZOOM_LEVELS.ROUTE_TRAVEL,
    bearing: 0,
    pitch: CAMERA_SETTINGS.DEFAULT_PITCH,
    transitionDuration: CAMERA_SETTINGS.MIN_TRANSITION_DURATION,
    terrain: 'default'
  };
  private positionHistory: Array<[number, number]> = [];
  private lastTransitionTime: number = 0;
  private isZooming: boolean = false;
  private lookAheadOptions: LookAheadOptions = {
    enabled: true,
    distance: 0.2,  // Look ahead 20% of the distance
    adaptiveMode: 'terrain-based'
  };
  private transitionCount: number = 0;
  private lastCityName: string | null = null;
  
  private constructor() {}
  
  public static getInstance(): EnhancedCameraBehavior {
    if (!this.instance) {
      this.instance = new EnhancedCameraBehavior();
    }
    return this.instance;
  }
  
  /**
   * Initialize the camera behavior with a map instance
   */
  public initialize(map: mapboxgl.Map): void {
    this.map = map;
    this.positionHistory = [];
    AnimationDebugTools.log('info', 'EnhancedCameraBehavior initialized');
  }
  
  /**
   * Update camera to follow vehicle position with enhanced behavior
   */
  public followVehicle(position: [number, number], bearing: number, context: FollowVehicleOptions = {}): void {
    AnimationDebugTools.log('info', '[[ EnhancedCameraBehavior.followVehicle CALLED ]] -- CHECKING IF ACTIVE', { position, bearing, context, isZooming: this.isZooming, mapExists: !!this.map });
    if (!this.map) {
        AnimationDebugTools.log('error', 'EnhancedCameraBehavior: Cannot update camera - map not initialized');
        return;
    }
    if (this.isZooming && !context.forceUpdate) {
      return;
    }
    this.positionHistory.push(position);
    if (this.positionHistory.length > 5) {
      this.positionHistory.shift();
    }
    const terrain = context.terrain || determineTerrainType(position);
    const enteringNewCity = context.cityName && context.cityName !== this.lastCityName;
    if (enteringNewCity) {
      this.lastCityName = context.cityName || null;
    }
    const targetZoom = this.calculateTargetZoom(context);
    const targetPitch = this.calculateTargetPitch(terrain, context);
    // FORCE BEARING TO 0 ALWAYS
    const targetBearing = 0;
    const cameraCenter = this.calculateCameraCenter(position, bearing, context);
    const isMajorTransition = 
      enteringNewCity || 
      context.forceUpdate || 
      Math.abs(targetZoom - this.currentState.zoom) > 2;
    const transitionDuration = this.calculateTransitionDuration(isMajorTransition, context);
    const now = performance.now();
    const timeSinceLastTransition = now - this.lastTransitionTime;

    // Define padding based on panel widths passed in context
    const padding: mapboxgl.PaddingOptions = {
      top: context.topPadding || 60, // Default top padding for header
      bottom: context.bottomPadding || 20,
      left: context.leftPanelWidth || 0,
      right: context.rightPanelWidth || 0
    };

    if (timeSinceLastTransition < 200 && !context.forceUpdate) {
      return;
    }
    const transitionOptions: MapTransitionOptions = {
      center: cameraCenter,
      zoom: targetZoom,
      bearing: targetBearing, // ALWAYS 0
      pitch: targetPitch,
      duration: transitionDuration,
      essential: true,
      padding: padding // Apply padding here
    };
    try {
      if (isMajorTransition) {
        transitionOptions.easing = (t: number) => {
          return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
        };
      } else {
        transitionOptions.easing = (t: number) => {
          return t * (2 - t);
        };
      }
      this.map.easeTo(transitionOptions);
      this.currentState = {
        center: cameraCenter,
        zoom: targetZoom,
        bearing: targetBearing, // ALWAYS 0
        pitch: targetPitch,
        transitionDuration,
        terrain
      };
      this.lastTransitionTime = now;
      this.transitionCount++;
      if (isMajorTransition) {
        AnimationDebugTools.log('info', `Major camera transition: ${JSON.stringify({
          zoom: targetZoom,
          pitch: targetPitch,
          bearing: targetBearing,
          duration: transitionDuration,
          terrain,
          city: context.cityName
        })}`);
      }
    } catch (error) {
      AnimationDebugTools.log('error', `Error in camera transition: ${error}`);
    }
  }
  
  /**
   * Apply a dramatic two-step zoom for significant transitions
   */
  public dramaticZoom(
    finalPosition: [number, number],
    options: {
      initialZoom?: number;
      finalZoom?: number;
      initialDuration?: number;
      finalDuration?: number;
      onComplete?: () => void;
    } = {}
  ): void {
    if (!this.map) return;
    
    // Set flags to prevent regular updates during dramatic zoom
    this.isZooming = true;
    
    // Step 1: Zoom out for context
    this.map.easeTo({
      center: finalPosition,
      zoom: options.initialZoom || ZOOM_LEVELS.TRANSITION_START,
      pitch: 30, // Lower pitch for wider context
      bearing: 0, // North orientation
      duration: options.initialDuration || 1500,
      easing: (t: number) => {
        // Ease out cubic - fast start, gradual end
        return 1 - Math.pow(1 - t, 3);
      },
      essential: true
    });
    
    // Step 2: Zoom in to destination
    setTimeout(() => {
      if (!this.map) return;
      
      this.map.easeTo({
        center: finalPosition,
        zoom: options.finalZoom || ZOOM_LEVELS.CITY_EXPLORATION,
        pitch: CAMERA_SETTINGS.CITY_PITCH,
        bearing: 0,
        duration: options.finalDuration || 2000,
        easing: (t: number) => {
          // Ease in out cubic - smooth acceleration and deceleration
          return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
        },
        essential: true
      });
      
      // Reset flags and call completion callback
      setTimeout(() => {
        this.isZooming = false;
        if (options.onComplete) {
          options.onComplete();
        }
      }, (options.finalDuration || 2000) + 100);
    }, (options.initialDuration || 1500) + 100);
  }
  
  /**
   * Configure look-ahead behavior
   */
  public setLookAhead(options: Partial<LookAheadOptions>): void {
    this.lookAheadOptions = {
      ...this.lookAheadOptions,
      ...options
    };
  }
  
  /**
   * Get current camera state
   */
  public getState(): CameraState {
    return { ...this.currentState };
  }
  
  /**
   * Reset camera behavior state
   */
  public reset(): void {
    this.positionHistory = [];
    this.lastTransitionTime = 0;
    this.isZooming = false;
    this.transitionCount = 0;
    this.lastCityName = null;
  }
  
  /**
   * Calculate target zoom level based on context
   */
  private calculateTargetZoom(options: any): number {
    // POI discovery takes highest priority
    if (options.isDiscoveringPOI) {
      return ZOOM_LEVELS.POI_DETAIL;
    }
    
    // City exploration takes second priority
    if (options.inCity) {
      return ZOOM_LEVELS.CITY_EXPLORATION;
    }
    
    // Terrain-based zoom levels
    switch (options.terrain) {
      case 'mountain':
        return ZOOM_LEVELS.MOUNTAIN_VIEW;
      case 'desert':
        return ZOOM_LEVELS.DESERT_VIEW;
      case 'coastal':
        return ZOOM_LEVELS.COASTAL_VIEW;
      default:
        return ZOOM_LEVELS.ROUTE_TRAVEL;
    }
  }
  
  /**
   * Calculate target pitch based on terrain and context
   */
  private calculateTargetPitch(terrain: string, options: any): number {
    if (options.isDiscoveringPOI) {
      return 55; // Higher pitch for POI views
    }
    
    if (options.inCity) {
      return CAMERA_SETTINGS.CITY_PITCH;
    }
    
    switch (terrain) {
      case 'mountain':
        return CAMERA_SETTINGS.MOUNTAIN_PITCH;
      case 'desert':
        return 40; // Lower pitch for vast desert views
      case 'coastal':
        return 50; // Higher pitch for coastal views
      default:
        return CAMERA_SETTINGS.DEFAULT_PITCH;
    }
  }
  
  /**
   * Calculate smoothed camera center with look-ahead
   */
  private calculateCameraCenter(
    position: [number, number],
    bearing: number,
    options: any
  ): [number, number] {
    // If not enough history or look-ahead disabled, use current position
    if (this.positionHistory.length < 2 || !this.lookAheadOptions.enabled) {
      return position;
    }
    
    // Calculate smoothed position first (weighted average)
    const weights = this.positionHistory.map((_, i) => i + 1);
    const totalWeight = weights.reduce((sum, w) => sum + w, 0);
    
    const smoothedLng = this.positionHistory.reduce(
      (sum, pos, i) => sum + pos[0] * weights[i], 0
    ) / totalWeight;
    
    const smoothedLat = this.positionHistory.reduce(
      (sum, pos, i) => sum + pos[1] * weights[i], 0
    ) / totalWeight;
    
    const smoothedPosition: [number, number] = [smoothedLng, smoothedLat];
    
    // Determine look-ahead distance
    let lookAheadDistance = this.lookAheadOptions.distance;
    
    // Adjust based on adaptive mode
    if (this.lookAheadOptions.adaptiveMode === 'speed-based' && options.speed) {
      // Increase look-ahead with speed
      lookAheadDistance = Math.min(0.5, lookAheadDistance * (1 + options.speed / 10));
    } else if (this.lookAheadOptions.adaptiveMode === 'terrain-based') {
      // Adjust based on terrain
      switch (options.terrain) {
        case 'mountain':
          lookAheadDistance *= 1.5; // More look-ahead in mountains for smoother turns
          break;
        case 'desert':
          lookAheadDistance *= 0.8; // Less look-ahead in deserts (straighter paths)
          break;
      }
    }
    
    // In cities, reduce look-ahead to keep focus on the city itself
    if (options.inCity) {
      lookAheadDistance *= 0.5;
    }
    
    // Calculate movement direction
    const lastPos = this.positionHistory[this.positionHistory.length - 2];
    const direction = [
      position[0] - lastPos[0],
      position[1] - lastPos[1]
    ];
    
    // Normalize direction and apply look-ahead
    const distance = Math.sqrt(direction[0] * direction[0] + direction[1] * direction[1]);
    
    // If no movement, just use smoothed position
    if (distance === 0) {
      return smoothedPosition;
    }
    
    // Calculate look-ahead point
    const normalizedDirection = [
      direction[0] / distance,
      direction[1] / distance
    ];
    
    // Apply look-ahead to smoothed position
    return [
      smoothedPosition[0] + normalizedDirection[0] * lookAheadDistance * 0.01, // Scale factor for degrees
      smoothedPosition[1] + normalizedDirection[1] * lookAheadDistance * 0.01
    ];
  }
  
  /**
   * Calculate appropriate transition duration
   */
  private calculateTransitionDuration(isMajorTransition: boolean, options: any): number {
    if (isMajorTransition) {
      return CAMERA_SETTINGS.DRAMATIC_TRANSITION_DURATION;
    }
    
    // Shorter durations for frequent updates
    if (this.transitionCount % 5 === 0) {
      // Every 5th transition is slightly longer for periodic adjustments
      return 750;
    }
    
    return CAMERA_SETTINGS.MIN_TRANSITION_DURATION;
  }
}

// Export singleton instance
export const enhancedCameraBehavior = EnhancedCameraBehavior.getInstance(); 