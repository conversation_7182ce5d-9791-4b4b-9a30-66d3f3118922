/**
 * POIDiscovery.ts
 * 
 * Utilities for discovering points of interest along a route
 */

import * as turf from '@turf/turf';
import type { PointOfInterest } from '@/types/POITypes';

// Constants
const DETECTION_RADIUS_DEFAULT = 1000; // meters
const DETECTION_RADIUS_CITY = 300;     // meters in city areas
const DETECTION_RADIUS_SCENIC = 2000;  // meters in scenic areas

/**
 * Checks if the vehicle is approaching any POIs within detection radius
 * 
 * @param position Current vehicle position [longitude, latitude]
 * @param pois Array of points of interest
 * @param discoveredPOIs Array of already discovered POI IDs
 * @param onDiscovery Callback when a POI is discovered
 * @param options Optional configuration
 * @returns Array of discovered POI IDs
 */
export function checkForApproachingPOIs(
  position: [number, number],
  pois: PointOfInterest[],
  discoveredPOIs: string[] = [],
  onDiscovery: (poi: PointOfInterest) => void,
  options = { 
    detectionRadius: DETECTION_RADIUS_DEFAULT,
    maxDiscoveries: 1 
  }
): string[] {
  if (!position || !pois || pois.length === 0) {
    return [];
  }

  // Create a point from the current position
  const currentPoint = turf.point(position);
  const newDiscoveries: string[] = [];

  // Filter POIs that haven't been discovered yet
  const undiscoveredPOIs = pois.filter(poi => !discoveredPOIs.includes(poi.id));

  // If we have no undiscovered POIs, return early
  if (undiscoveredPOIs.length === 0) {
    return [];
  }

  // Check each POI to see if it's within detection radius
  for (const poi of undiscoveredPOIs) {
    if (!poi.coordinates || poi.coordinates.length !== 2) {
      console.warn(`Invalid POI coordinates for: ${poi.name || 'Unknown POI'}`);
      continue;
    }

    // Create a point from the POI coordinates
    const poiPoint = turf.point(poi.coordinates as [number, number]);
    
    // Calculate distance in meters
    const distance = turf.distance(currentPoint, poiPoint, { units: 'meters' });
    
    // Check if the POI is within detection radius
    if (distance <= options.detectionRadius) {
      // Add to new discoveries
      newDiscoveries.push(poi.id);
      
      // Call the discovery callback
      if (onDiscovery) {
        onDiscovery(poi);
      }
      
      // Limit the number of discoveries per check
      if (newDiscoveries.length >= options.maxDiscoveries) {
        break;
      }
    }
  }
  
  // Log discoveries if any were found
  if (newDiscoveries.length > 0) {
    console.log(`🔍 [${new Date().toISOString()}] Discovered ${newDiscoveries.length} new POIs`);
  }
  
  return newDiscoveries;
}

/**
 * Checks if the vehicle has entered a new area
 * 
 * @param position Current vehicle position [longitude, latitude]
 * @param destinations List of possible destinations/areas
 * @param currentArea Currently active area
 * @param detectionRadius Detection radius in kilometers
 * @returns Object with area change information
 */
export function checkAreaChange(
  position: [number, number],
  destinations: any[],
  currentArea: string | null,
  detectionRadius: number = 10
) {
  // Default return value
  const result = {
    areaChanged: false,
    previousArea: currentArea,
    newArea: null,
    timestamp: Date.now()
  };
  
  if (!position || !destinations || destinations.length === 0) {
    return result;
  }
  
  // Create a point from the current position
  const currentPoint = turf.point(position);
  
  // Check each destination to see if we're within its radius
  for (const destination of destinations) {
    if (!destination.coordinates || destination.coordinates.length !== 2) {
      continue;
    }
    
    // Create a point from the destination coordinates
    const destinationPoint = turf.point(destination.coordinates as [number, number]);
    
    // Calculate distance in kilometers
    const distance = turf.distance(currentPoint, destinationPoint, { units: 'kilometers' });
    
    // Check if we're within the detection radius and it's a different area
    if (distance <= detectionRadius && destination.name !== currentArea) {
      // Update result with new area information
      result.areaChanged = true;
      result.newArea = destination.name;
      
      console.log(`🌍 [${new Date().toISOString()}] Area changed to: ${destination.name}`);
      
      return result;
    }
  }
  
  return result;
}

/**
 * Gets POIs in a specific area
 * 
 * @param areaName Name of the area
 * @param radius Radius to consider in kilometers
 * @param destinations List of all destinations
 * @param pois List of all POIs
 * @param userInterests Optional list of user interests for filtering
 * @returns Array of POIs in the area
 */
export function getPOIsInArea(
  areaName: string,
  radius: number,
  destinations: any[],
  pois: PointOfInterest[],
  userInterests: string[] = []
): PointOfInterest[] {
  if (!areaName || !destinations || !pois) {
    return [];
  }
  
  // Find the destination coordinates by name
  const destination = destinations.find(d => d.name === areaName);
  if (!destination || !destination.coordinates) {
    return [];
  }
  
  // Create a point from the destination coordinates
  const destinationPoint = turf.point(destination.coordinates as [number, number]);
  
  // Filter POIs that are within the radius
  const areaPOIs = pois.filter(poi => {
    if (!poi.coordinates || poi.coordinates.length !== 2) {
      return false;
    }
    
    // Create a point from the POI coordinates
    const poiPoint = turf.point(poi.coordinates as [number, number]);
    
    // Calculate distance in kilometers
    const distance = turf.distance(destinationPoint, poiPoint, { units: 'kilometers' });
    
    // Check if the POI is within the radius
    return distance <= radius;
  });
  
  // If user interests are provided, prioritize POIs that match interests
  if (userInterests.length > 0) {
    // Filter POIs that match user interests
    const priorityPOIs = areaPOIs.filter(poi => 
      // Check if the POI's category (if it exists) is included in userInterests (case-insensitive)
      poi.category && userInterests.includes(poi.category.toLowerCase())
    );
    
    // If priority POIs are found, use them; otherwise, fall back to all POIs in the area
    if (priorityPOIs.length > 0) {
      return [...priorityPOIs, ...areaPOIs.filter(poi => !priorityPOIs.includes(poi))];
    }
  }
  
  return areaPOIs;
} 