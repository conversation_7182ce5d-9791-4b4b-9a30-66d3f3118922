/**
 * JourneyManager.ts
 * 
 * Manages journey phases, transitions, and state for the Morocco travel experience.
 * Provides utilities for handling journey phase changes, area transitions, and state management.
 */

import { JourneyPhase } from './types';
import { Destination } from '@/types/destination';
import AnimationDebugTools from '../animation/AnimationDebugTools';

// Area change management
const AREA_CHANGE_COOLDOWN = 10000; // 10 seconds minimum between area changes

export interface JourneyManagerState {
  journeyPhase: JourneyPhase;
  currentArea: string | null;
  areaChangeLock: boolean;
  lastAreaChangeTime: number;
}

export interface AreaChangeResult {
  newArea: boolean;
  city?: Destination;
}

/**
 * Handler for journey phase transitions
 */
export const logJourneyPhaseChange = (
  oldPhase: JourneyPhase | undefined,
  newPhase: JourneyPhase
): void => {
  console.log(`🧭 [${Date.now()}] Journey phase changing from "${oldPhase}" to "${newPhase}"`);
  
  // Log the phase change to AnimationDebugTools for debugging
  AnimationDebugTools.log('info', `Journey Phase Change: ${oldPhase} to ${newPhase}`, {
    from: oldPhase,
    to: newPhase,
    timestamp: Date.now()
  });
  
  // Dispatch a custom event for other components to listen to
  document.dispatchEvent(new CustomEvent('journey-phase-changed', { 
    detail: { phase: newPhase } 
  }));
};

/**
 * Checks if an area change should be allowed based on cooldown
 */
export const shouldAllowAreaChange = (state: JourneyManagerState): boolean => {
  const now = Date.now();
  return !state.areaChangeLock && (now - state.lastAreaChangeTime > AREA_CHANGE_COOLDOWN);
};

/**
 * Updates current area with locking to prevent rapid area changes
 */
export const handleAreaChange = (
  state: JourneyManagerState, 
  areaChangeResult: AreaChangeResult,
  updateCurrentArea: (area: string | null) => void,
  setAreaChangeLock: (locked: boolean) => void
): JourneyManagerState => {
  const now = Date.now();
  
  // Skip area changes if locked or if cooldown hasn't passed
  if (state.areaChangeLock || now - state.lastAreaChangeTime < AREA_CHANGE_COOLDOWN) {
    console.log(`Ignoring area change request - Lock: ${state.areaChangeLock}, Time since last: ${(now - state.lastAreaChangeTime) / 1000}s`);
    return state;
  }
  
  if (areaChangeResult.newArea && areaChangeResult.city) {
    // Log for debugging
    console.log(`Area changed to: ${areaChangeResult.city.name} (LOCKED)`);
    
    // Apply area state locking to prevent oscillation
    setAreaChangeLock(true);
    updateCurrentArea(areaChangeResult.city.name);
    
    // Create updated state
    const newState = {
      ...state,
      currentArea: areaChangeResult.city.name,
      areaChangeLock: true,
      lastAreaChangeTime: now
    };
    
    // Schedule unlock after extended period
    setTimeout(() => {
      setAreaChangeLock(false);
      console.log('Area change lock released');
    }, AREA_CHANGE_COOLDOWN * 2);
    
    return newState;
  }
  
  return state;
};

/**
 * Sets initial journey phase and current area
 */
export const initializeJourneyState = (
  destinations: Destination[],
  currentArea: string | null,
  journeyPhase: JourneyPhase | undefined,
  areaChangeLock: boolean,
  lastAreaChangeTime: number,
  setJourneyPhase: (phase: JourneyPhase) => void,
  setCurrentArea: (area: string | null) => void,
  setAreaChangeLock: (locked: boolean) => void
): void => {
  console.log('Setting initial journey phase and current area...');
  
  // Only set the journey phase once
  if (!journeyPhase) {
    setJourneyPhase('initial_city');
  }
  
  // Only change area if not locked and cooldown has passed
  const now = Date.now();
  if (!areaChangeLock && now - lastAreaChangeTime > AREA_CHANGE_COOLDOWN) {
    if (destinations.length > 0 && !currentArea) {
      // Apply area state locking to prevent oscillation
      setAreaChangeLock(true);
      setCurrentArea(destinations[0].name);
      
      console.log(`Setting current area to: ${destinations[0].name} (LOCKED)`);
      
      // Unlock after extended period
      setTimeout(() => {
        setAreaChangeLock(false);
        console.log('Area change lock released');
      }, AREA_CHANGE_COOLDOWN * 2);
    }
  } else {
    console.log(`Area change skipped - Lock: ${areaChangeLock}, Time since last: ${(now - lastAreaChangeTime) / 1000}s`);
  }
};

export default {
  logJourneyPhaseChange,
  shouldAllowAreaChange,
  handleAreaChange,
  initializeJourneyState
}; 