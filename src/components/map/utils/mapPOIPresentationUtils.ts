import { PointOfInterest } from '@/types/POITypes'; // Corrected import, ensuring old one is removed
import { 
  Camera, Utensils, BadgeInfo, Building, Landmark, 
  Mountain, Plane, ShoppingBag, Ticket, Umbrella, Users,
  LucideIcon
} from 'lucide-react';

// Get icon component based on POI type
export const getPOIIcon = (poiType: string): LucideIcon => {
  switch (poiType.toLowerCase()) {
    case 'historical':
      return Landmark;
    case 'cultural':
      return Users;
    case 'natural':
      return Mountain;
    case 'beach':
      return Umbrella; 
    case 'entertainment':
      return Ticket;
    case 'shopping':
      return ShoppingBag;
    case 'food':
    case 'restaurant':
      return Utensils;
    case 'attraction':
    case 'landmark':
      return Camera;
    case 'hotel':
    case 'accommodation':
      return Building;
    case 'airport':
      return Plane;
    case 'activity':
      return Ticket;
    default:
      return BadgeInfo;
  }
};

// Get color class based on POI type
export const getPOIColorClass = (poiType: string) => {
  switch (poiType.toLowerCase()) {
    case 'landmark':
      return 'poi-icon-landmark';
    case 'restaurant':
      return 'poi-icon-restaurant';
    case 'activity':
      return 'poi-icon-activity';
    case 'accommodation':
      return 'poi-icon-accommodation';
    case 'historical':
      return 'poi-icon-landmark';
    case 'cultural':
      return 'poi-icon-activity';
    case 'natural':
    case 'beach':
      return 'poi-icon-landmark';
    case 'entertainment':
    case 'food':
    case 'shopping':
      return 'poi-icon-restaurant';
    default:
      return 'poi-icon-landmark';
  }
};

// Find nearest destination to a POI
export const findNearestDestination = (
  poi: PointOfInterest, 
  selectedDestinations: [number, number][]
): [number, number] | undefined => {
  if (selectedDestinations.length === 0) return undefined;
  
  let nearestDest = selectedDestinations[0];
  let minDistance = Infinity;
  
  selectedDestinations.forEach(dest => {
    const dx = poi.coordinates[0] - dest[0];
    const dy = poi.coordinates[1] - dest[1];
    const distance = Math.sqrt(dx * dx + dy * dy);
    
    if (distance < minDistance) {
      minDistance = distance;
      nearestDest = dest;
    }
  });
  
  return nearestDest;
};
