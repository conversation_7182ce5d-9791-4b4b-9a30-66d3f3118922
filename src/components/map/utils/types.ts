import type { Destination, PointOfInterest } from '@/types/POITypes';
import mapboxgl from 'mapbox-gl';
import { ClusterFeature } from './cluster-types';

// Camera settings for different modes
export const CAMERA_SETTINGS = {
  DEFAULT: {
    pitch: 40,           // Moderate pitch for normal travel
    bearing: 0,          // Default north orientation
    zoom: 8,             // Standard zoom for route travel (documentation standard)
    duration: 1000,
    easing: (t: number) => {
      // Smooth cubic bezier easing
      return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }
  },
  CONTINUE_JOURNEY: {
    pitch: 45,           // Moderate pitch for journey continuation
    bearing: 0,          // Fixed north orientation - keep this at 0
    zoom: 8,             // Standard zoom for route travel (documentation standard)
    duration: 1500,
    easing: (t: number) => {
      // Simple smooth easing without dramatic effects
      return t * t * (3 - 2 * t);
    }
  },
  POI_DISCOVERY: {
    pitch: 40,           // Moderate pitch for POI focus
    bearing: 30,         // Angled view for better 3D effect
    zoom: 16,            // Detailed zoom for POI discovery (documentation standard)
    duration: 2000,
    easing: (t: number) => {
      // Smoother easing for POI views
      return 1 - Math.pow(1 - t, 3);
    }
  },
  CITY_APPROACH: {
    pitch: 45,           // Higher pitch for city views
    bearing: 0,          // Will be set to vehicle direction
    zoom: 14,            // Standard zoom for city exploration (documentation standard)
    duration: 2000,
    easing: (t: number) => {
      // Ease out cubic - smoother deceleration
      return 1 - Math.pow(1 - t, 3);
    }
  },
  BROAD_CONTEXT: {
    pitch: 0,            // Flat view for broader context
    bearing: 0,          // North orientation
    zoom: 5,             // Wider view for context
    duration: 3000,
    easing: (t: number) => {
      // Smoother easing for context views
      return t === 1 ? 1 : 1 - Math.pow(2, -10 * t);
    }
  }
} as const;

// Route cluster definition
export interface RouteCluster {
  id: string;
  center: [number, number];
  pois: PointOfInterest[];
  segmentIndex: number;
}

// Extended PointOfInterest interface with optional city property
export interface ExtendedPointOfInterest extends PointOfInterest {
  city?: string;
}

// Main props interface for TravelAnimator
export interface TravelAnimatorProps {
  map: mapboxgl.Map;
  destinations: Destination[];
  allDestinations?: Destination[];
  pois: PointOfInterest[];
  isPaused: boolean;
  onPOIDiscovered: (poi: PointOfInterest) => void;
  discoveredPOIs: Set<string>;
  onAnimationComplete: () => void;
  onProgressUpdate: (progress: number) => void;
  selectedCategories?: string[];
  userInterests?: string[]; // Add user interests for importance scoring
}

// Interface for rhythmParams returned by implementContextualRhythm
export interface RhythmParams {
  zoom?: number;
  duration?: number;
  center?: [number, number];
  bearing?: number;
}

// Interface for route segment information
export interface RouteSegmentInfo {
  startPoint: [number, number];
  endPoint: [number, number];
  estimatedDriveTime: number; // in minutes
  recommendedStopover: boolean;
  terrain: 'urban' | 'mountain' | 'desert' | 'coastal';
}

// Interface for the position during animation
export interface PositionInfo {
  currentPoint: [number, number];
  nextPoint: [number, number] | null;
  pointIndex: number;
}

// Constants
export const ANIMATION_DURATION = 500; // ms
export const POI_DETECTION_RADIUS = 10; // km - for POI discovery during animation
export const CITY_RADIUS_KM = 25; // km - for identifying POIs within cities
export const POI_CLUSTER_RADIUS = 3; // km - radius for clustering nearby POIs
export const ROUTE_POI_CLUSTER_RADIUS = 5; // km - radius for clustering POIs along the route

// Zoom levels
export const BROAD_CONTEXT_ZOOM = 4.0; // Country in relation to surroundings
export const REGIONAL_CONTEXT_ZOOM = 5.5; // Regional view
export const CITY_APPROACH_ZOOM = 7.0; // Approaching a major city
export const POI_DETAIL_ZOOM = 9.0; // Detailed view of a specific POI
export const MAX_ZOOM_DEFAULT = 5.2; // Default max zoom level
export const MAX_ZOOM_POI_CLUSTER = 7.5; // Zoom level for POI clusters

// Center of Morocco for context views
export const MOROCCO_CENTER: [number, number] = [-5.0, 31.794];

export const MAPBOX_ACCESS_TOKEN = "pk.eyJ1IjoiYW1lZW5zYWlmMSIsImEiOiJjbHlodmpsNncwN2ttMmtueHd0NmhnM3N4In0.0GioVJqjXtnEOFqrVMiI8Q";

// POI categories extracted from PointOfInterest.type
export const POI_CATEGORIES = ['monument', 'landmark', 'nature', 'activity'];

// Speed modifiers for different contexts
export const SPEED_MODIFIERS = {
  DEFAULT: 1.0,       // Normal speed
  CITY_APPROACH: 0.5, // Slow down when approaching cities
  CULTURAL_REGION: 0.3, // Very slow in cultural regions
  POI_CLUSTER: 0.6,   // Slow down near clusters of POIs
  MOUNTAIN: 0.8,      // Slightly slower in mountains
  DESERT: 1.2,        // Faster in open desert
  HIGHWAY: 1.5,       // Faster on highways
};

// Constants for animation and interaction
export const CITY_DETECTION_RADIUS = 10; // km
export const POI_NOTIFICATION_DISTANCE = 5; // km

// Enhanced vehicle appearance
export const VEHICLE_STYLES = {
  DEFAULT: {
    color: '#FF4500',
    size: 24,
    pulseColor: 'rgba(255, 69, 0, 0.2)',
    pulseSize: 40,
  },
  CITY: {
    color: '#3366FF',
    size: 28,
    pulseColor: 'rgba(51, 102, 255, 0.3)',
    pulseSize: 48,
  },
  CULTURAL: {
    color: '#9900CC',
    size: 28, 
    pulseColor: 'rgba(153, 0, 204, 0.3)',
    pulseSize: 48,
  },
  SCENIC: {
    color: '#00CC66',
    size: 28,
    pulseColor: 'rgba(0, 204, 102, 0.3)',
    pulseSize: 48,
  }
};

// Cluster radius constants (in kilometers)
export const CLUSTER_CONSTANTS = {
  CITY_RADIUS: 25,         // 25km radius for city (in km)
  ROUTE_RADIUS: 5,         // 5km radius for route (in km)
  POI_GROUP_RADIUS: 3,     // 3km radius for POI groups (in km)
  
  // Source and layer names for clusters
  ROUTE_CLUSTER_SOURCE: 'route-clusters',
  CITY_CLUSTER_SOURCE: 'cities-cluster',
  POI_CLUSTER_SOURCE: 'poi-cluster',
  POI_CLUSTER_LAYER: 'poi-cluster-layer',
  CITY_CLUSTER_LAYER: 'city-cluster-layer',
  ROUTE_CLUSTER_LAYER: 'route-cluster-layer',
  ROUTE_COUNT_LAYER: 'route-count-layer',
  CITY_COUNT_LAYER: 'city-count-layer',
  POI_COUNT_LAYER: 'poi-count-layer',
  
  // Additional constants
  CITY_RADIUS_METERS: 50000,  // 50km radius for city clusters (in meters)
  ROUTE_RADIUS_METERS: 10000, // 10km radius for route clusters (in meters)
  MIN_POIS: 3                // Minimum POIs to form a cluster
} as const;

// City cluster definition
export interface CityCluster {
  id: string;
  city: string;
  pois: PointOfInterest[];
  center: [number, number];
}

export interface ClusterGenerationConfig {
  routePoints: [number, number][];
  pois: PointOfInterest[];
  cityClusterRadius?: number;
  routeClusterRadius?: number;
  poiGroupRadius?: number;
}

export interface ClusterGenerationResult {
  cityClusters: Record<string, CityCluster>;
  routeClusters: Record<string, RouteCluster>;
}

// Animation constants
export const ANIMATION_CONSTANTS = {
  INITIAL_ZOOM: 5,
  FINAL_ZOOM: 14,
  ANIMATION_DURATION: 30000,
  VEHICLE_SPEED: 0.5,
  VEHICLE_ROTATION_SPEED: 0.1,
  VEHICLE_SCALE: 0.5,
  VEHICLE_OFFSET: 0.0002,
};

// Vehicle style constants
export const VEHICLE_STYLE_CONSTANTS = {
  DEFAULT_COLOR: '#FF0000',
  HIGHLIGHT_COLOR: '#00FF00',
  DEFAULT_OPACITY: 0.8,
  HIGHLIGHT_OPACITY: 1.0,
  DEFAULT_SCALE: 1.0,
  HIGHLIGHT_SCALE: 1.2,
};

/**
 * Journey State Management Types
 * Controls the flow between city exploration and route journey
 */
export type JourneyPhase = 
  | 'not_started'      // Journey hasn't been initialized yet
  | 'initial_city'     // First city (Marrakech) exploration
  | 'selecting_pois'   // User is selecting POIs in current city
  | 'ready_to_start'   // User has confirmed POI selection
  | 'journey'          // Actively traveling on route
  | 'approaching_city' // Approaching a new city
  | 'new_city'        // Arrived at a new city
  | 'completed';       // Journey completed

export interface JourneyStateConfig {
  autoShowApproachingPOIs: boolean; // Whether to auto-show approaching POIs
  cityApproachDistance: number;   // Distance (km) to trigger city approach
  notificationCooldown: number;   // Time (ms) between notifications
}

export interface JourneyControls {
  canContinueJourney: boolean;
  canToggleDiscovery: boolean;
  discoveryEnabled: boolean;
  currentPhase: JourneyPhase;
  selectedPOICount: number;
}