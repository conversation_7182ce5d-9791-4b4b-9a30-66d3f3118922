import * as turf from '@turf/turf';
import { PointOfInterest } from '@/types/poi';
import { 
  CLUSTER_CONSTANTS,
  CityCluster,
  RouteCluster,
  ClusterGenerationConfig,
  ClusterGenerationResult
} from './types';

/**
 * Generate clusters for both city POIs and route POIs
 */
export const generateClusters = ({
  routePoints,
  pois,
  cityClusterRadius = CLUSTER_CONSTANTS.CITY_RADIUS_METERS / 1000, // Convert from meters to kilometers
  routeClusterRadius = CLUSTER_CONSTANTS.ROUTE_RADIUS_METERS / 1000, // Convert from meters to kilometers
  poiGroupRadius = CLUSTER_CONSTANTS.POI_GROUP_RADIUS
}: ClusterGenerationConfig): ClusterGenerationResult => {
  console.log(`Generating clusters with ${pois.length} POIs and ${routePoints.length} route points`);
  
  const cityClusters: Record<string, CityCluster> = {};
  const routeClusters: Record<string, RouteCluster> = {};
  
  // Track which POIs have been assigned to cities
  const assignedToCityPOIs = new Set<string>();
  
  // First, assign POIs to cities
  routePoints.forEach((cityCoords, cityIndex) => {
    const nearbyPOIs = pois.filter(poi => {
      if (!poi.coordinates || assignedToCityPOIs.has(poi.id)) return false;
      
      const distance = turf.distance(
        turf.point(cityCoords),
        turf.point([poi.coordinates[0], poi.coordinates[1]]),
        { units: 'kilometers' }
      );
      
      if (distance <= cityClusterRadius) {
        assignedToCityPOIs.add(poi.id);
        return true;
      }
      return false;
    });
    
    if (nearbyPOIs.length > 0) {
      const cityId = `city-${cityIndex}`;
      cityClusters[cityId] = {
        id: cityId,
        city: `City ${cityIndex + 1}`,
        pois: nearbyPOIs,
        center: cityCoords
      };
    }
  });
  
  // Then handle route POIs (those not assigned to cities)
  const remainingPOIs = pois.filter(poi => !assignedToCityPOIs.has(poi.id));
  
  // Group remaining POIs by route segments
  for (let i = 0; i < routePoints.length - 1; i++) {
    const start = routePoints[i];
    const end = routePoints[i + 1];
    const line = turf.lineString([start, end]);
    
    const segmentPOIs = remainingPOIs.filter(poi => {
      if (!poi.coordinates) return false;
      
      const point = turf.point([poi.coordinates[0], poi.coordinates[1]]);
      const distance = turf.pointToLineDistance(point, line, { units: 'kilometers' });
      
      return distance <= routeClusterRadius;
    });
    
    if (segmentPOIs.length > 0) {
      // Calculate center point of the segment
      const center = turf.midpoint(
        turf.point(start),
        turf.point(end)
      ).geometry.coordinates as [number, number];
      
      const clusterId = `route-segment-${i}`;
      routeClusters[clusterId] = {
        id: clusterId,
        center,
        pois: segmentPOIs,
        segmentIndex: i
      };
    }
  }
  
  console.log(`Generated ${Object.keys(cityClusters).length} city clusters and ${Object.keys(routeClusters).length} route clusters`);
  
  return {
    cityClusters,
    routeClusters
  };
};

/**
 * Filter POIs by categories
 */
export const filterPOIsByCategories = (
  pois: PointOfInterest[],
  categories: string[]
): PointOfInterest[] => {
  if (!categories.length) return pois;
  
  return pois.filter(poi => 
    categories.includes(poi.type) || 
    poi.tags?.some(tag => categories.includes(tag))
  );
}; 