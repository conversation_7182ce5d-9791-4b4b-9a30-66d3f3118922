import { PointOfInterest } from '@/types/poi';
import { LngLatBounds } from 'mapbox-gl';

// Extended PointOfInterest interface with lng/lat properties
export interface ExtendedPointOfInterest extends PointOfInterest {
  lng: number;
  lat: number;
  city?: string;
}

export interface ClusterFeature {
  id: string;
  type: 'Feature';
  geometry: {
    type: 'Point';
    coordinates: [number, number];
  };
  properties: {
    cluster: boolean;
    cluster_id?: number;
    point_count: number;
    point_count_abbreviated: string;
    category?: string;
    name?: string;
    description?: string;
  };
}

export interface RouteCluster {
  id: string;
  pois: ExtendedPointOfInterest[];
  center: [number, number];
  bounds?: LngLatBounds;
}

export interface CityCluster {
  id: string;
  city: string;
  pois: ExtendedPointOfInterest[];
  center: [number, number];
  bounds?: LngLatBounds;
}

export interface ClusterGenerationConfig {
  routePoints: [number, number][];
  pois: ExtendedPointOfInterest[];
  cityClusterRadius?: number;
  routeClusterRadius?: number;
  poiGroupRadius?: number;
}

export interface ClusterGenerationResult {
  cityClusters: Record<string, CityCluster>;
  routeClusters: Record<string, RouteCluster>;
}

export interface ClusterPointOfInterest extends PointOfInterest {
  lng: number;
  lat: number;
  city?: string;
  // No need to add categories explicitly as it's inherited from PointOfInterest
} 