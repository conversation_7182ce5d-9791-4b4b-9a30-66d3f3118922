import * as turf from '@turf/turf';
import { findCulturalRegion } from '@/utils/cultural-regions';
import { getZIndex } from './ZIndexManager';
import { shouldUseReactButton } from '../animation/UIComponentManager';

// Add TypeScript declaration for Window
declare global {
  interface Window {
    _lastContinueButtonTimestamp?: number;
  }
}

/**
 * Calculate animation progress based on elapsed time and duration
 */
export const calculateProgress = (elapsedTime: number, duration: number = 60000): number => {
  return Math.min(elapsedTime / duration, 1);
};

/**
 * Calculate distances between segments of a route
 */
export const calculateSegmentDistances = (points: Array<[number, number]>): number[] => {
  const distances: number[] = [];
  for (let i = 0; i < points.length - 1; i++) {
    distances.push(
      turf.distance(turf.point(points[i]), turf.point(points[i + 1]), { units: 'kilometers' })
    );
  }
  return distances;
};

/**
 * Get current position on route based on progress
 */
export const getCurrentPosition = (
  points: Array<[number, number]>, 
  progress: number
): { currentPoint: [number, number]; nextPoint: [number, number] | null; pointIndex: number } => {
  if (!points || points.length === 0) {
    return { currentPoint: [0, 0], nextPoint: null, pointIndex: 0 };
  }
  
  const pointIndex = Math.min(
    Math.floor(progress * points.length),
    points.length - 1
  );
  
  const currentPoint = points[pointIndex];
  const nextPoint = pointIndex < points.length - 1 ? points[pointIndex + 1] : null;
  
  return { currentPoint, nextPoint, pointIndex };
};

/**
 * Calculate bearing between two geographical points
 */
export const calculateBearing = (start: [number, number], end: [number, number]): number => {
  try {
    return turf.bearing(
      turf.point(start),
      turf.point(end)
    );
  } catch (error) {
    console.error('Error calculating bearing:', error);
    return 0;
  }
};

/**
 * Calculate distance between a point and a line segment
 */
export const distanceToSegment = (
  point: [number, number], 
  segmentStart: [number, number], 
  segmentEnd: [number, number]
): number => {
  // Create a line segment
  const line = turf.lineString([segmentStart, segmentEnd]);
  
  // Calculate the distance from the point to the line
  return turf.pointToLineDistance(turf.point(point), line, { units: 'kilometers' });
};

/**
 * Calculate midpoint between two points
 */
export const midpoint = (pointA: [number, number], pointB: [number, number]): [number, number] => {
  const midpoint = turf.midpoint(turf.point(pointA), turf.point(pointB));
  return midpoint.geometry.coordinates as [number, number];
};

/**
 * Check if a given point is in a cultural region and return the region name
 */
export const checkCulturalRegion = (currentPoint: [number, number]): string | null => {
  const region = findCulturalRegion(currentPoint);
  return region ? region.name : null;
};

/**
 * Create a contextual rhythm handler for camera movements
 */
export const createContextualRhythmHandler = () => {
  let lastContextZoomTime = 0;
  let currentCulturalRegion: string | null = null;
  
  return (currentPoint: [number, number], elapsed: number) => {
    // Only adjust camera every 10 seconds
    if (elapsed - lastContextZoomTime < 10000) {
      return null;
    }
    
    // Check for cultural regions
    const regionName = checkCulturalRegion(currentPoint);
    
    // If we entered a new region or it's time for a rhythm change
    if ((regionName && regionName !== currentCulturalRegion) || 
        (elapsed - lastContextZoomTime > 20000)) {
      
      lastContextZoomTime = elapsed;
      currentCulturalRegion = regionName;
      
      // Return camera adjustment parameters
      return {
        center: currentPoint,
        zoom: regionName ? 8 : 6, // Zoom in for cultural regions, out for general travel
        duration: 2000,
        easing: (t: number) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t
      };
    }
    
    return null;
  };
};

/**
 * Calculate speed modifier based on context (near cities, POIs, etc.)
 */
export const getSpeedModifier = (
  currentPoint: [number, number],
  nextPoint: [number, number],
  destinations: any[] | undefined,
  clusters: any
): number => {
  // Default speed
  let speedModifier = 1.0;
  
  // Slow down near destinations/cities
  if (destinations && destinations.length > 0) {
    destinations.forEach(dest => {
      const distance = turf.distance(
        turf.point(currentPoint),
        turf.point(dest.coordinates as [number, number]),
        { units: 'kilometers' }
      );
      
      // Slow down progressively as we get closer to cities
      if (distance < 20) {
        speedModifier = Math.min(speedModifier, 0.5 + (distance / 40));
      }
    });
  }
  
  // Slow down near POI clusters
  if (clusters) {
    Object.values(clusters).forEach((cluster: any) => {
      if (cluster.center) {
        const distance = turf.distance(
          turf.point(currentPoint),
          turf.point(cluster.center as [number, number]),
          { units: 'kilometers' }
        );
        
        // Slow down progressively as we get closer to POI clusters
        if (distance < 10) {
          speedModifier = Math.min(speedModifier, 0.6 + (distance / 25));
        }
      }
    });
  }
  
  // Check for cultural regions too
  const region = findCulturalRegion(currentPoint);
  if (region && region.significance === 'high') {
    speedModifier = Math.min(speedModifier, 0.7);
  }
  
  return speedModifier;
};

/**
 * Creates a "Begin Adventure" button that allows users to start the journey.
 * 
 * NOTE: This function is being maintained for legacy purposes. The preferred
 * approach is to use the React button in TravelAnimator.tsx. This function
 * will check if a React button already exists and will not create a duplicate
 * if one is found.
 */
export const createContinueJourneyButton = (map, destinations, journeyPhase, internalJourneyPhase, setIsAnimating, handleContinueJourney) => {
  // First check if we should use React button instead
  if (shouldUseReactButton()) {
    console.log('React-based button detected, createContinueJourneyButton will not create DOM button');
    return;
  }

  if (!map || !destinations.length) return;

  const currentJourneyPhase = journeyPhase || internalJourneyPhase;
  console.log('Creating direct DOM-based Begin Adventure button with:', {
    destinationsCount: destinations.length,
    journeyPhase: currentJourneyPhase
  });

  // Track button creation to prevent duplicates
  if (window._lastContinueButtonTimestamp && Date.now() - window._lastContinueButtonTimestamp < 2000) {
    console.log('Skipping button creation - too soon after previous button');
    return;
  }
  window._lastContinueButtonTimestamp = Date.now();

  // Remove any existing buttons (cleanup)
  document.querySelectorAll('.begin-adventure-button').forEach(el => {
    el.remove();
  });

  // Create button element
  const button = document.createElement('button');
  button.className = 'begin-adventure-button';
  button.innerHTML = '<span style="font-size: 14px; margin-right: 4px;">▶</span>Begin Adventure';
  
  // Apply styles
  Object.assign(button.style, {
    position: 'fixed',
    bottom: '40px',
    left: '50%',
    transform: 'translateX(-50%)',
    backgroundColor: '#10b981',
    color: 'white',
    padding: '12px 24px',
    borderRadius: '8px',
    fontWeight: 'bold',
    boxShadow: '0 4px 14px rgba(0,0,0,0.25)',
    border: '2px solid white',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '8px',
    minWidth: '200px',
    cursor: 'pointer',
    fontSize: '16px',
    zIndex: getZIndex('ui_controls')
  });

  // Add pulsing animation
  const styleTag = document.createElement('style');
  styleTag.textContent = `
    @keyframes pulse {
      0% {
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
        transform: translateX(-50%) scale(1);
      }
      
      70% {
        box-shadow: 0 0 0 15px rgba(16, 185, 129, 0);
        transform: translateX(-50%) scale(1.05);
      }
      
      100% {
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
        transform: translateX(-50%) scale(1);
      }
    }

    .begin-adventure-button {
      animation: pulse 2s infinite;
    }
  `;
  document.head.appendChild(styleTag);

  // Add event listener
  const handleClick = () => {
    console.log(`Begin Adventure button clicked, phase: ${currentJourneyPhase}`);
    
    // Change button appearance to show it's been clicked
    button.style.backgroundColor = '#059669'; // Darker green
    button.style.animation = 'none';
    button.style.transform = 'scale(0.98)';
    button.textContent = 'Starting Adventure...';
    
    // Set animation state
    setIsAnimating(true);
    
    // Call the handler
    if (handleContinueJourney) {
      handleContinueJourney('adventure');
    }
    
    // Remove the button after a short delay
    setTimeout(() => {
      if (button.parentNode) {
        button.parentNode.removeChild(button);
      }
    }, 500);
  };
  
  button.addEventListener('click', handleClick);
}; 