// src/components/map/utils/useAnimationState.ts
import { useRef } from 'react';
import type { PointOfInterest } from '@/types/POITypes';

/**
 * Custom hook to manage animation-related refs
 * Following the Animation Architecture Rule of using refs for animation-critical state
 */
export const useAnimationState = () => {
  const lastInitializedCityRef = useRef<string | null>(null);
  const inJourneyTransitionRef = useRef<boolean>(false);
  const animationFrameRef = useRef<number>();
  const currentPointRef = useRef<[number, number] | null>(null);
  const lastTimestampRef = useRef<number>(0);
  const speedRef = useRef<number>(1);
  const lastPointRef = useRef<[number, number] | null>(null);
  const ensureRouteDataRef = useRef<() => Promise<[number, number][]>>();
  const startDirectAnimationLoopRef = useRef<(firstPoint: [number, number], routeData: Array<[number, number]>) => void>();
  const setJourneyPhaseWithLoggingRef = useRef<(phase: string) => void>();
  const animationTimerRef = useRef<number | null>(null);
  const animationStartRef = useRef<number | null>(null);
  const totalPausedTimeRef = useRef<number>(0);
  const pauseStartTimeRef = useRef<number | null>(null);
  const currentPOIRef = useRef<PointOfInterest | null>(null);
  const lastContextZoomTimeRef = useRef<number>(performance.now());
  const currentCulturalRegionRef = useRef<string | null>(null);
  const recentlyDiscoveredPOIsRef = useRef<any[]>([]);
  const lastTimeRef = useRef<number | null>(null);
  const startTimeRef = useRef<number | null>(null);
  const pointIndexRef = useRef<number>(0);
  const isAnimatingRef = useRef<boolean>(false);
  const lastNotificationTimeRef = useRef<number>(0);
  const animateRefInternal = useRef<(timestamp: number) => void>(() => {});
  const isFramePendingRef = useRef<boolean>(false);
  const lastPOICheckTimeRef = useRef<number>(Date.now());
  const inCityAnnouncementMadeRef = useRef<boolean>(false);
  const lastVehicleUpdateRef = useRef<number>(Date.now());

  return {
    lastInitializedCityRef,
    inJourneyTransitionRef,
    animationFrameRef,
    currentPointRef,
    lastTimestampRef,
    speedRef,
    lastPointRef,
    ensureRouteDataRef,
    startDirectAnimationLoopRef,
    setJourneyPhaseWithLoggingRef,
    animationTimerRef,
    animationStartRef,
    totalPausedTimeRef,
    pauseStartTimeRef,
    currentPOIRef,
    lastContextZoomTimeRef,
    currentCulturalRegionRef,
    recentlyDiscoveredPOIsRef,
    lastTimeRef,
    startTimeRef,
    pointIndexRef,
    isAnimatingRef,
    lastNotificationTimeRef,
    animateRefInternal,
    isFramePendingRef,
    lastPOICheckTimeRef,
    inCityAnnouncementMadeRef,
    lastVehicleUpdateRef,
  };
};