/**
 * SimplifiedTravelAnimator.tsx
 * 
 * SIMPLIFIED TRAVEL ANIMATOR
 * Replaces the complex TravelAnimator with a clean, maintainable component
 */

import React, { forwardRef, useImperativeHandle, useCallback } from 'react';
import mapboxgl from 'mapbox-gl';
import { useUnifiedAnimation, RoutePoint } from '@/hooks/useUnifiedAnimation';
import { Position } from '@/types/Position';

export interface SimplifiedTravelAnimatorProps {
  map?: mapboxgl.Map | null;
  routePoints: RoutePoint[];
  animationDuration?: number;
  autoPlay?: boolean;
  onAnimationStart?: () => void;
  onAnimationPause?: () => void;
  onAnimationResume?: () => void;
  onAnimationComplete?: () => void;
  onProgressUpdate?: (progress: number, currentPosition: Position, bearing: number) => void;
  onError?: (error: Error) => void;
}

export interface SimplifiedTravelAnimatorHandles {
  startAnimation: (routePoints?: RoutePoint[], duration?: number) => Promise<boolean>;
  pauseAnimation: (shouldPause?: boolean) => void;
  stopAnimation: () => void;
  getProgress: () => number;
  isPlaying: () => boolean;
  isPaused: () => boolean;
}

/**
 * Simplified Travel Animator Component
 * Provides a clean interface for route animation with minimal complexity
 */
const SimplifiedTravelAnimator = forwardRef<
  SimplifiedTravelAnimatorHandles,
  SimplifiedTravelAnimatorProps
>(({
  map,
  routePoints,
  animationDuration = 30000,
  autoPlay = false,
  onAnimationStart,
  onAnimationPause,
  onAnimationResume,
  onAnimationComplete,
  onProgressUpdate,
  onError
}, ref) => {
  
  // Use the unified animation hook
  const {
    isReady,
    isPlaying,
    isPaused,
    progress,
    currentPosition,
    currentBearing,
    startAnimation: startAnimationHook,
    pauseAnimation: pauseAnimationHook,
    stopAnimation: stopAnimationHook
  } = useUnifiedAnimation(map, {
    onProgress: useCallback((state) => {
      if (state.currentPosition && onProgressUpdate) {
        onProgressUpdate(state.progress, state.currentPosition, state.currentBearing);
      }
    }, [onProgressUpdate]),
    
    onComplete: useCallback(() => {
      onAnimationComplete?.();
    }, [onAnimationComplete]),
    
    onError: useCallback((error) => {
      onError?.(error);
    }, [onError])
  });
  
  // Start animation function
  const startAnimation = useCallback(async (
    externalRoutePoints?: RoutePoint[],
    duration?: number
  ): Promise<boolean> => {
    const points = externalRoutePoints || routePoints;
    const animDuration = duration || animationDuration;
    
    if (!points || points.length < 2) {
      const error = new Error('Need at least 2 route points for animation');
      onError?.(error);
      return false;
    }
    
    try {
      const success = await startAnimationHook(points, animDuration);
      if (success) {
        onAnimationStart?.();
      }
      return success;
    } catch (error) {
      onError?.(error as Error);
      return false;
    }
  }, [routePoints, animationDuration, startAnimationHook, onAnimationStart, onError]);
  
  // Pause animation function
  const pauseAnimation = useCallback((shouldPause: boolean = true) => {
    pauseAnimationHook(shouldPause);
    
    if (shouldPause) {
      onAnimationPause?.();
    } else {
      onAnimationResume?.();
    }
  }, [pauseAnimationHook, onAnimationPause, onAnimationResume]);
  
  // Stop animation function
  const stopAnimation = useCallback(() => {
    stopAnimationHook();
  }, [stopAnimationHook]);
  
  // Auto-play effect
  React.useEffect(() => {
    if (autoPlay && isReady && routePoints.length >= 2 && !isPlaying) {
      console.log('[SimplifiedTravelAnimator] Auto-starting animation');
      startAnimation();
    }
  }, [autoPlay, isReady, routePoints.length, isPlaying, startAnimation]);
  
  // Expose imperative API via ref
  useImperativeHandle(ref, () => ({
    startAnimation,
    pauseAnimation,
    stopAnimation,
    getProgress: () => progress,
    isPlaying: () => isPlaying,
    isPaused: () => isPaused
  }), [startAnimation, pauseAnimation, stopAnimation, progress, isPlaying, isPaused]);
  
  // This component doesn't render anything - it's purely for animation control
  return null;
});

SimplifiedTravelAnimator.displayName = 'SimplifiedTravelAnimator';

export default SimplifiedTravelAnimator;
