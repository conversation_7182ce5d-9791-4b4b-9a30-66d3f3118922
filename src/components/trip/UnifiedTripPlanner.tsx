import React, { useState, useEffect } from 'react';
import { Calendar, MapPin, Clock, Thermometer, Sun, Cloud, CloudRain, X, Plus, Minus, Move, GripVertical, Settings, Zap } from 'lucide-react';
import { City, POI } from '../../types';

interface UnifiedTripPlannerProps {
    selectedCities: City[];
    selectedPOIs: POI[];
    totalDays: number;
    cityDayAllocations: Record<string, number>;
    onDayAllocationChange: (allocations: Record<string, number>) => void;
    onPOIRemove?: (poi: POI) => void;
    onPOIReorder?: (fromDay: number, toDay: number, poiId: string) => void;
    onDayReorder?: (fromDay: number, toDay: number) => void;
    expandDayForNewPOI?: number; // Day number to expand when a new POI is added
}

interface DayPlan {
    dayNumber: number;
    city: string;
    isFirstDayInCity: boolean;
    pois: POI[];
    weather: {
        condition: 'sunny' | 'cloudy' | 'rainy';
        temperature: number;
    };
    intensity: 'relaxed' | 'busy' | 'too-many';
    activities: string[];
}

const UnifiedTripPlanner: React.FC<UnifiedTripPlannerProps> = ({
    selectedCities,
    selectedPOIs,
    totalDays,
    cityDayAllocations,
    onDayAllocationChange,
    onPOIRemove,
    onPOIReorder,
    onDayReorder,
    expandDayForNewPOI
}) => {
    const [draggedPOI, setDraggedPOI] = useState<POI | null>(null);
    const [draggedDay, setDraggedDay] = useState<number | null>(null);
    const [expandedDays, setExpandedDays] = useState<Set<number>>(new Set());

    // Auto-expand day when a new POI is added
    useEffect(() => {
        if (expandDayForNewPOI && expandDayForNewPOI > 0) {
            setExpandedDays(prev => new Set([...prev, expandDayForNewPOI]));
        }
    }, [expandDayForNewPOI]);

    // Generate day-by-day itinerary
    const generateDayByDayItinerary = (): DayPlan[] => {
        const itinerary: DayPlan[] = [];
        let currentDay = 1;

        selectedCities.forEach((city, cityIndex) => {
            const daysInCity = cityDayAllocations[city.name] || Math.ceil(totalDays / selectedCities.length);
            // Enhanced POI assignment logic with multiple matching strategies
            const cityPOIs = selectedPOIs.filter(poi => {
                // Strategy 1: Direct location field match
                if (poi.location) {
                    const poiCity = poi.location.includes(',') ? poi.location.split(',')[0].trim() : poi.location.trim();
                    const normalizedPoiCity = poiCity.toLowerCase();
                    const normalizedCityName = city.name.toLowerCase();

                    // Handle common spelling variations
                    const cityVariations = [
                        normalizedCityName,
                        normalizedCityName.replace('marrakech', 'marrakesh'),
                        normalizedCityName.replace('marrakesh', 'marrakech'),
                        normalizedCityName.replace('fes', 'fez'),
                        normalizedCityName.replace('fez', 'fes')
                    ];

                    if (cityVariations.includes(normalizedPoiCity)) {
                        return true;
                    }
                }

                // Strategy 2: Geographic proximity (if no location field or no match)
                if (poi.coordinates && city.coordinates) {
                    const distance = Math.sqrt(
                        Math.pow(poi.coordinates[0] - city.coordinates[0], 2) +
                        Math.pow(poi.coordinates[1] - city.coordinates[1], 2)
                    );
                    // Roughly 50km radius in degrees (very approximate)
                    return distance < 0.5;
                }

                return false;
            });

            for (let dayInCity = 0; dayInCity < daysInCity && currentDay <= totalDays; dayInCity++) {
                const poisForDay = cityPOIs.slice(
                    dayInCity * Math.ceil(cityPOIs.length / daysInCity),
                    (dayInCity + 1) * Math.ceil(cityPOIs.length / daysInCity)
                );

                const intensity = poisForDay.length <= 2 ? 'relaxed' : 
                                poisForDay.length <= 4 ? 'busy' : 'too-many';

                itinerary.push({
                    dayNumber: currentDay,
                    city: city.name,
                    isFirstDayInCity: dayInCity === 0,
                    pois: poisForDay,
                    weather: {
                        condition: ['sunny', 'cloudy', 'rainy'][Math.floor(Math.random() * 3)] as any,
                        temperature: Math.floor(Math.random() * 15) + 20
                    },
                    intensity,
                    activities: dayInCity === 0 ? [`Arrive in ${city.name}`, 'Check into accommodation'] : []
                });

                currentDay++;
            }
        });

        return itinerary;
    };

    const dayByDayItinerary = generateDayByDayItinerary();

    const getWeatherIcon = (condition: string) => {
        switch (condition) {
            case 'sunny': return React.createElement(Sun, { size: 14, color: '#f59e0b' });
            case 'cloudy': return React.createElement(Cloud, { size: 14, color: '#6b7280' });
            case 'rainy': return React.createElement(CloudRain, { size: 14, color: '#3b82f6' });
            default: return React.createElement(Sun, { size: 14, color: '#f59e0b' });
        }
    };

    const getIntensityColor = (intensity: string) => {
        switch (intensity) {
            case 'relaxed': return '#10b981';
            case 'busy': return '#f59e0b';
            case 'too-many': return '#ef4444';
            default: return '#6b7280';
        }
    };

    const getIntensityIcon = (intensity: string) => {
        switch (intensity) {
            case 'relaxed': return '😌';
            case 'busy': return '🏃';
            case 'too-many': return '😵';
            default: return '😐';
        }
    };

    const handleDayAllocationChange = (cityName: string, newDays: number) => {
        const newAllocations = { ...cityDayAllocations };
        newAllocations[cityName] = Math.max(1, Math.min(totalDays, newDays));
        onDayAllocationChange(newAllocations);
    };

    const toggleDayExpansion = (dayNumber: number) => {
        const newExpanded = new Set(expandedDays);
        if (newExpanded.has(dayNumber)) {
            newExpanded.delete(dayNumber);
        } else {
            newExpanded.add(dayNumber);
        }
        setExpandedDays(newExpanded);
    };

    return (
        <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '1px solid var(--border-light)',
            overflow: 'hidden'
        }}>
            {/* Header */}
            <div style={{
                padding: '1rem',
                borderBottom: '1px solid var(--border-light)',
                background: 'linear-gradient(to right, rgba(0, 71, 171, 0.05), rgba(139, 26, 24, 0.05))'
            }}>
                <h3 style={{
                    margin: 0,
                    fontSize: '1.25rem',
                    fontWeight: 600,
                    color: 'var(--morocco-blue)',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem'
                }}>
                    <Calendar size={20} />
                    Trip Planner ({totalDays} days)
                </h3>
                <p style={{
                    margin: '0.5rem 0 0',
                    fontSize: '0.875rem',
                    color: 'var(--text-secondary)'
                }}>
                    Organize your journey, set days per city, and manage your itinerary
                </p>
            </div>



            {/* Day-by-Day Itinerary */}
            <div style={{ padding: '1rem' }}>
                <h4 style={{
                    margin: '0 0 0.75rem',
                    fontSize: '1rem',
                    fontWeight: 600,
                    color: 'var(--text-primary)',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem'
                }}>
                    <Clock size={16} />
                    Daily Itinerary
                </h4>
                
                {dayByDayItinerary.length > 0 ? (
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
                        {dayByDayItinerary.map((day) => (
                            <div
                                key={day.dayNumber}
                                style={{
                                    border: '1px solid var(--border-light)',
                                    borderRadius: '8px',
                                    backgroundColor: day.isFirstDayInCity ? 'rgba(0, 71, 171, 0.02)' : 'white',
                                    borderLeft: `3px solid ${getIntensityColor(day.intensity)}`,
                                    overflow: 'hidden'
                                }}
                            >
                                {/* Day Header - Clickable */}
                                <div 
                                    onClick={() => toggleDayExpansion(day.dayNumber)}
                                    style={{
                                        padding: '0.75rem',
                                        cursor: 'pointer',
                                        borderBottom: expandedDays.has(day.dayNumber) ? '1px solid var(--border-light)' : 'none'
                                    }}
                                >
                                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                                            <span style={{
                                                fontSize: '0.875rem',
                                                fontWeight: 600,
                                                color: 'var(--morocco-blue)',
                                                backgroundColor: 'white',
                                                padding: '0.25rem 0.5rem',
                                                borderRadius: '4px',
                                                border: '1px solid var(--border-light)'
                                            }}>
                                                Day {day.dayNumber}
                                            </span>
                                            {day.isFirstDayInCity && (
                                                <span style={{
                                                    fontSize: '0.75rem',
                                                    fontWeight: 600,
                                                    color: 'var(--morocco-red)',
                                                    backgroundColor: 'rgba(139, 26, 24, 0.1)',
                                                    padding: '0.25rem 0.5rem',
                                                    borderRadius: '4px'
                                                }}>
                                                    {day.city}
                                                </span>
                                            )}
                                        </div>

                                        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                                            {getWeatherIcon(day.weather.condition)}
                                            <span style={{ fontSize: '0.75rem', color: 'var(--text-secondary)' }}>
                                                {day.weather.temperature}°C
                                            </span>
                                            <div style={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                gap: '0.25rem',
                                                color: getIntensityColor(day.intensity),
                                                fontSize: '0.75rem',
                                                fontWeight: 500
                                            }}>
                                                <span>{getIntensityIcon(day.intensity)}</span>
                                                <span style={{ textTransform: 'capitalize' }}>
                                                    {day.intensity === 'too-many' ? 'Too Many' : day.intensity}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Expandable Content */}
                                {expandedDays.has(day.dayNumber) && (
                                    <div style={{ padding: '0.75rem' }}>
                                        {/* Activities */}
                                        {day.activities.length > 0 && (
                                            <div style={{ marginBottom: '0.75rem' }}>
                                                {day.activities.map((activity, actIndex) => (
                                                    <div key={actIndex} style={{
                                                        fontSize: '0.75rem',
                                                        color: 'var(--text-secondary)',
                                                        fontStyle: 'italic',
                                                        marginBottom: '0.25rem'
                                                    }}>
                                                        • {activity}
                                                    </div>
                                                ))}
                                            </div>
                                        )}

                                        {/* POIs for this day */}
                                        {day.pois.length > 0 ? (
                                            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                                                {day.pois.map((poi) => (
                                                    <div key={poi.id} style={{
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'space-between',
                                                        padding: '0.5rem',
                                                        backgroundColor: 'rgba(0, 71, 171, 0.05)',
                                                        borderRadius: '4px',
                                                        border: '1px solid rgba(0, 71, 171, 0.1)'
                                                    }}>
                                                        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', flex: 1 }}>
                                                            <GripVertical size={14} color="var(--text-secondary)" style={{ cursor: 'grab' }} />
                                                            <div>
                                                                <div style={{
                                                                    fontSize: '0.875rem',
                                                                    fontWeight: 500,
                                                                    color: 'var(--text-primary)',
                                                                    marginBottom: '0.25rem'
                                                                }}>
                                                                    {poi.name}
                                                                </div>
                                                                <div style={{
                                                                    fontSize: '0.75rem',
                                                                    color: 'var(--text-secondary)',
                                                                    textTransform: 'capitalize'
                                                                }}>
                                                                    {poi.category}
                                                                </div>
                                                            </div>
                                                        </div>
                                                        {onPOIRemove && (
                                                            <button
                                                                onClick={() => onPOIRemove(poi)}
                                                                style={{
                                                                    background: 'none',
                                                                    border: 'none',
                                                                    cursor: 'pointer',
                                                                    padding: '0.25rem',
                                                                    borderRadius: '50%',
                                                                    color: 'var(--morocco-red)',
                                                                    display: 'flex',
                                                                    alignItems: 'center',
                                                                    justifyContent: 'center'
                                                                }}
                                                            >
                                                                <X size={14} />
                                                            </button>
                                                        )}
                                                    </div>
                                                ))}
                                            </div>
                                        ) : (
                                            <div style={{
                                                textAlign: 'center',
                                                padding: '1rem',
                                                color: 'var(--text-secondary)',
                                                fontSize: '0.75rem',
                                                fontStyle: 'italic'
                                            }}>
                                                No POIs planned for this day
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>
                ) : (
                    <div style={{
                        textAlign: 'center',
                        padding: '2rem',
                        color: 'var(--text-secondary)'
                    }}>
                        <Calendar size={32} style={{ marginBottom: '1rem', opacity: 0.5 }} />
                        <p style={{ margin: 0, fontSize: '0.875rem' }}>
                            Select cities and POIs to start building your itinerary
                        </p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default UnifiedTripPlanner;
