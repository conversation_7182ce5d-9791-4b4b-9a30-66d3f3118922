import React from 'react';
import { cn } from "@/lib/utils";

export interface ProgressIndicatorProps {
  currentStep: number;
  totalSteps: number;
  labels?: string[];
  className?: string;
  onStepClick?: (step: number) => void;
  allowInteraction?: boolean;
}

/**
 * Progress Indicator component for multi-step processes
 * Shows a horizontal stepper with the current position in the flow
 */
export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  currentStep,
  totalSteps,
  labels,
  className,
  onStepClick,
  allowInteraction = true,
}) => {
  // Ensure current step is within bounds
  const safeCurrentStep = Math.max(1, Math.min(currentStep, totalSteps));

  // Handle step click
  const handleStepClick = (step: number) => {
    if (onStepClick && allowInteraction) {
      // Only allow clicking on completed steps or the next available step
      if (step <= safeCurrentStep || step === safeCurrentStep + 1) {
        onStepClick(step);
      }
    }
  };
  
  return (
    <div className={cn("w-full flex flex-col items-center space-y-2", className)}>
      <div className="w-full flex items-center justify-between relative">
        {/* Progress bar background */}
        <div className="absolute top-1/2 left-0 right-0 h-1 -translate-y-1/2 bg-gray-200 rounded-full" />
        
        {/* Active progress bar */}
        <div 
          className="absolute top-1/2 left-0 h-1 -translate-y-1/2 bg-morocco-terracotta rounded-full transition-all duration-300 ease-in-out"
          style={{ width: `${((safeCurrentStep - 1) / (totalSteps - 1)) * 100}%` }}
        />
        
        {/* Step circles */}
        {Array.from({ length: totalSteps }).map((_, index) => {
          const stepNumber = index + 1;
          const isCompleted = stepNumber < safeCurrentStep;
          const isActive = stepNumber === safeCurrentStep;
          const isClickable = allowInteraction && onStepClick && (stepNumber <= safeCurrentStep || stepNumber === safeCurrentStep + 1);
          
          return (
            <div 
              key={`step-${stepNumber}`}
              className={cn(
                "w-8 h-8 rounded-full flex items-center justify-center z-10 font-medium text-sm transition-all",
                isCompleted ? "bg-morocco-terracotta text-white" : 
                  isActive ? "bg-morocco-terracotta text-white ring-4 ring-morocco-sand" : 
                    "bg-white border-2 border-gray-300 text-gray-500",
                isClickable ? "cursor-pointer hover:ring-2 hover:ring-morocco-terracotta/50" : "",
              )}
              onClick={() => handleStepClick(stepNumber)}
              role={isClickable ? "button" : undefined}
              tabIndex={isClickable ? 0 : undefined}
              aria-current={isActive ? "step" : undefined}
            >
              {isCompleted ? (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              ) : (
                stepNumber
              )}
            </div>
          );
        })}
      </div>
      
      {/* Labels */}
      {labels && (
        <div className="w-full flex items-center justify-between px-2">
          {labels.map((label, index) => {
            const stepNumber = index + 1;
            const isCompleted = stepNumber < safeCurrentStep;
            const isActive = stepNumber === safeCurrentStep;
            const isClickable = allowInteraction && onStepClick && (stepNumber <= safeCurrentStep || stepNumber === safeCurrentStep + 1);
            
            return (
              <div 
                key={`label-${stepNumber}`}
                className={cn(
                  "text-xs sm:text-sm font-medium text-center transition-colors max-w-[70px] sm:max-w-none whitespace-normal",
                  isCompleted || isActive ? "text-morocco-terracotta" : "text-gray-500",
                  isClickable ? "cursor-pointer hover:text-morocco-terracotta" : ""
                )}
                onClick={() => handleStepClick(stepNumber)}
                role={isClickable ? "button" : undefined}
                tabIndex={isClickable ? 0 : undefined}
              >
                {label}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

/**
 * Mobile-friendly compact version of the progress indicator
 */
export const CompactProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  currentStep,
  totalSteps,
  className,
  onStepClick,
  allowInteraction = false,
}) => {
  // Handle step click 
  const handleStepClick = () => {
    if (onStepClick && allowInteraction) {
      // For compact version, clicking just cycles through steps
      const nextStep = currentStep < totalSteps ? currentStep + 1 : 1;
      onStepClick(nextStep);
    }
  };

  return (
    <div 
      className={cn(
        "flex items-center space-x-1 p-2 bg-white rounded-full shadow-sm", 
        onStepClick && allowInteraction ? "cursor-pointer hover:bg-gray-50" : "",
        className
      )}
      onClick={handleStepClick}
      role={onStepClick && allowInteraction ? "button" : undefined}
      tabIndex={onStepClick && allowInteraction ? 0 : undefined}
    >
      {Array.from({ length: totalSteps }).map((_, index) => {
        const stepNumber = index + 1;
        const isActive = stepNumber === currentStep;
        const isCompleted = stepNumber < currentStep;
        
        return (
          <div 
            key={`compact-step-${stepNumber}`}
            className={cn(
              "rounded-full transition-all duration-300",
              isActive ? "w-6 h-2 bg-morocco-terracotta" : 
                isCompleted ? "w-2 h-2 bg-morocco-terracotta" : 
                  "w-2 h-2 bg-gray-300"
            )}
          />
        );
      })}
      <span className="text-xs font-medium text-gray-600 ml-1">
        {currentStep}/{totalSteps}
      </span>
    </div>
  );
}; 