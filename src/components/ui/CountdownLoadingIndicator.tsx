import React, { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';

interface CountdownLoadingIndicatorProps {
  message?: string;
  onComplete?: () => void;
  isVisible?: boolean;
  duration?: number;
  startFrom?: number;
}

interface CountdownLoadingIndicatorComponent extends React.FC<CountdownLoadingIndicatorProps> {
  show: (options: {
    container?: HTMLElement;
    message?: string;
    onComplete?: () => void;
    duration?: number;
    startFrom?: number;
  }) => () => void;
}

const CountdownLoadingIndicator: CountdownLoadingIndicatorComponent = ({
  message = 'Preparing your journey...',
  onComplete,
  isVisible = true,
  duration = 3000,
  startFrom = 3
}) => {
  const [count, setCount] = useState<number>(startFrom);
  const [showCountdown, setShowCountdown] = useState<boolean>(isVisible);
  const [container, setContainer] = useState<HTMLElement | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Create container for the countdown
    const countdownContainer = document.createElement('div');
    countdownContainer.className = 'countdown-container';
    countdownContainer.style.position = 'absolute';
    countdownContainer.style.top = '0';
    countdownContainer.style.left = '0';
    countdownContainer.style.width = '100%';
    countdownContainer.style.height = '100%';
    countdownContainer.style.display = 'flex';
    countdownContainer.style.alignItems = 'center';
    countdownContainer.style.justifyContent = 'center';
    countdownContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    countdownContainer.style.color = 'white';
    countdownContainer.style.fontSize = '80px';
    countdownContainer.style.fontWeight = 'bold';
    countdownContainer.style.zIndex = '1000';
    
    // Find a suitable container
    const targetContainer = document.querySelector('.mapboxgl-map') || document.body;
    targetContainer.appendChild(countdownContainer);
    setContainer(countdownContainer);
    
    // Start countdown
    if (isVisible) {
      startCountdown(startFrom);
    }
    
    // Cleanup function
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
      if (countdownContainer && countdownContainer.parentNode) {
        countdownContainer.parentNode.removeChild(countdownContainer);
      }
    };
  }, []);

  useEffect(() => {
    if (isVisible) {
      startCountdown(startFrom);
    }
  }, [isVisible]);

  const startCountdown = (initialCount: number) => {
    setCount(initialCount);
    
    const countDown = (currentCount: number) => {
      if (currentCount <= 0) {
        // Countdown complete
        setShowCountdown(false);
        if (onComplete) {
          onComplete();
        }
        return;
      }
      
      // Update count and continue countdown
      setCount(currentCount);
      timerRef.current = setTimeout(() => countDown(currentCount - 1), 1000);
    };
    
    // Start the countdown sequence
    countDown(initialCount);
  };

  if (!showCountdown || !container) {
    return null;
  }

  // Use React portal to render into the container
  return createPortal(
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column', 
      alignItems: 'center', 
      justifyContent: 'center',
      height: '100%'
    }}>
      <div style={{ fontSize: '120px', marginBottom: '20px' }}>{count}</div>
      {message && <div style={{ fontSize: '24px' }}>{message}</div>}
    </div>,
    container
  );
};

// Static show method for imperative usage
CountdownLoadingIndicator.show = ({
  container = document.body,
  message = 'Preparing your journey...',
  onComplete,
  duration = 3000,
  startFrom = 3
}) => {
  // Create a container for the countdown
  const countdownEl = document.createElement('div');
  countdownEl.className = 'countdown-container';
  countdownEl.style.position = 'absolute';
  countdownEl.style.top = '0';
  countdownEl.style.left = '0';
  countdownEl.style.width = '100%';
  countdownEl.style.height = '100%';
  countdownEl.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
  countdownEl.style.color = 'white';
  countdownEl.style.display = 'flex';
  countdownEl.style.alignItems = 'center';
  countdownEl.style.justifyContent = 'center';
  countdownEl.style.fontSize = '80px';
  countdownEl.style.fontWeight = 'bold';
  countdownEl.style.zIndex = '1000';
  
  container.appendChild(countdownEl);
  
  let currentCount = startFrom;
  
  // Display initial count
  countdownEl.textContent = currentCount.toString();
  
  // Set up the countdown interval
  const interval = setInterval(() => {
    currentCount--;
    countdownEl.textContent = currentCount.toString();
    
    if (currentCount <= 0) {
      // Clean up and complete
      clearInterval(interval);
      
      // Add fade-out animation
      countdownEl.style.transition = 'opacity 0.5s';
      countdownEl.style.opacity = '0';
      
      // Remove element after animation
      setTimeout(() => {
        if (countdownEl.parentNode) {
          countdownEl.parentNode.removeChild(countdownEl);
        }
        if (onComplete) {
          onComplete();
        }
      }, 500);
    }
  }, 1000);
  
  // Return cleanup function
  return () => {
    clearInterval(interval);
    if (countdownEl.parentNode) {
      countdownEl.parentNode.removeChild(countdownEl);
    }
  };
};

export default CountdownLoadingIndicator; 