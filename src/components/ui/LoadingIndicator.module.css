/* This is a placeholder CSS file since we've moved to inline styles */
.loadingContainer {
  /* Base container styles */
}

.fullscreen {
  /* Fullscreen overlay styles */
}

.loading {
  /* Loading state styles */
}

.success {
  /* Success state styles */
}

.error {
  /* Error state styles */
}

.countdown {
  /* Countdown state styles */
}

/* Make these available for legacy code that might still reference them */ 