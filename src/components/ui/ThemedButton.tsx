/**
 * ThemedButton.tsx
 * 
 * A button component that uses the client theme
 */

import React from 'react';

export interface ThemedButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'text';
  size?: 'small' | 'medium' | 'large';
  className?: string;
  onClick?: () => void;
  disabled?: boolean;
  style?: React.CSSProperties;
}

const ThemedButton: React.FC<ThemedButtonProps> = ({ 
  children, 
  variant = 'primary', 
  size = 'medium',
  className = '',
  onClick,
  disabled = false,
  style = {}
}) => {
  // Base styles for all buttons
  const baseStyles = {
    padding: size === 'small' ? '6px 12px' : size === 'medium' ? '8px 16px' : '12px 24px',
    borderRadius: '8px',
    fontWeight: 600,
    fontSize: size === 'small' ? '0.875rem' : size === 'medium' ? '1rem' : '1.125rem',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '8px',
    cursor: disabled ? 'not-allowed' : 'pointer',
    transition: 'all 0.2s ease',
    opacity: disabled ? 0.7 : 1,
    ...style
  };
  
  // Variant specific styles
  const variantStyles = {
    primary: {
      backgroundColor: '#2563EB',
      color: 'white',
      border: 'none',
      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
    },
    secondary: {
      backgroundColor: '#6B7280',
      color: 'white',
      border: 'none',
    },
    outline: {
      backgroundColor: 'transparent',
      color: '#374151',
      border: '1px solid #D1D5DB',
    },
    text: {
      backgroundColor: 'transparent',
      color: '#374151',
      border: 'none',
    }
  };
  
  // Combine styles for the current variant
  const combinedStyles = {
    ...baseStyles,
    ...variantStyles[variant]
  };
  
  return (
    <button
      className={`themed-button ${variant} ${size} ${className}`}
      style={combinedStyles}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
};

export default ThemedButton; 