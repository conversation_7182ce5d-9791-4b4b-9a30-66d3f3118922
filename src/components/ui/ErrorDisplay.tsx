import React from 'react';

interface ErrorDisplayProps {
  message: string;
  details?: string;
  onRetry?: () => void;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  message,
  details,
  onRetry
}) => {
  return (
    <div style={{
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      color: 'white',
      padding: '20px',
      borderRadius: '10px',
      textAlign: 'center',
      maxWidth: '80%',
      zIndex: 1000,
      border: '2px solid #e74c3c'
    }}>
      <div style={{
        color: '#e74c3c',
        fontSize: '48px',
        marginBottom: '10px'
      }}>
        ⚠️
      </div>
      <div style={{
        fontSize: '18px',
        fontWeight: 'bold',
        marginBottom: '10px'
      }}>
        {message}
      </div>
      {details && (
        <div style={{
          fontSize: '14px',
          color: '#aaa',
          marginBottom: '15px',
          maxHeight: '80px',
          overflowY: 'auto',
          padding: '5px',
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          borderRadius: '5px'
        }}>
          {details}
        </div>
      )}
      {onRetry && (
        <button 
          onClick={onRetry}
          style={{
            backgroundColor: '#e74c3c',
            color: 'white',
            border: 'none',
            padding: '8px 15px',
            borderRadius: '5px',
            cursor: 'pointer',
            fontWeight: 'bold',
            marginTop: '10px'
          }}
        >
          Try Again
        </button>
      )}
    </div>
  );
};

export default ErrorDisplay; 