/**
 * ThemedOverlay.tsx
 * 
 * A themed overlay component for displaying content with client-specific styling
 */

import React, { FC, ReactNode } from 'react';
import { useTheme } from '@/hooks/useTheme';
import ThemedButton from './ThemedButton';
import { X } from 'lucide-react';

interface ThemedOverlayProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: ReactNode;
  footer?: ReactNode;
  width?: 'small' | 'medium' | 'large' | 'full';
  position?: 'center' | 'left' | 'right';
  showCloseButton?: boolean;
}

const ThemedOverlay: FC<ThemedOverlayProps> = ({
  isOpen,
  onClose,
  title,
  children,
  footer,
  width = 'medium',
  position = 'center',
  showCloseButton = true
}) => {
  const { theme } = useTheme();
  
  if (!isOpen) return null;
  
  // Width classes based on the width prop
  const widthClasses = {
    small: 'max-w-md',
    medium: 'max-w-xl',
    large: 'max-w-4xl',
    full: 'max-w-full'
  };
  
  // Position classes based on the position prop
  const positionClasses = {
    center: 'items-center justify-center',
    left: 'items-center justify-start',
    right: 'items-center justify-end'
  };
  
  const overlayStyle = {
    backgroundColor: theme.overlays?.background || 'rgba(0, 0, 0, 0.7)',
    color: theme.textColor || '#ffffff'
  };
  
  const headerStyle = {
    backgroundColor: theme.overlays?.headerBackground || theme.primaryColor || '#1a202c',
    borderBottom: `1px solid ${theme.overlays?.borderColor || '#3a4151'}`
  };
  
  const bodyStyle = {
    backgroundColor: theme.overlays?.background || 'transparent'
  };
  
  const footerStyle = {
    borderTop: `1px solid ${theme.overlays?.borderColor || '#3a4151'}`
  };
  
  return (
    <div 
      className="fixed inset-0 z-50 flex overflow-y-auto p-4"
      style={overlayStyle}
      onClick={(e) => {
        // Close when clicking the backdrop
        if (e.target === e.currentTarget) onClose();
      }}
    >
      <div className={`m-auto rounded-lg shadow-xl overflow-hidden ${widthClasses[width]} w-full`}>
        {/* Header */}
        {title && (
          <div 
            className="px-6 py-4 flex items-center justify-between"
            style={headerStyle}
          >
            <h2 className="text-xl font-semibold">{title}</h2>
            {showCloseButton && (
              <button 
                onClick={onClose}
                className="focus:outline-none hover:opacity-80 transition-opacity"
              >
                <X size={20} />
              </button>
            )}
          </div>
        )}
        
        {/* Body */}
        <div 
          className="p-6"
          style={bodyStyle}
        >
          {children}
        </div>
        
        {/* Footer */}
        {footer && (
          <div 
            className="px-6 py-4 flex justify-end gap-3"
            style={footerStyle}
          >
            {footer}
          </div>
        )}
      </div>
    </div>
  );
};

export default ThemedOverlay; 