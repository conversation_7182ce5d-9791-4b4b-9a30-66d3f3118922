/**
 * Travel Insights Panel Component
 * 
 * Displays intelligent travel insights including balance analysis,
 * time estimates, recommendations, and optimization suggestions.
 * Mobile-optimized with collapsible sections.
 */

import React, { useState, useEffect } from 'react';
import { 
  BarChart3, 
  Clock, 
  MapPin, 
  TrendingUp, 
  Lightbulb,
  Calendar,
  DollarSign,
  Star,
  ChevronDown,
  ChevronUp,
  Target,
  Route,
  Sun,
  Users
} from 'lucide-react';
import { TravelInsights } from '../../services/TravelInsightsEngine';

interface TravelInsightsPanelProps {
  insights: TravelInsights | null;
  isLoading?: boolean;
  className?: string;
  isMobile?: boolean;
}

const TravelInsightsPanel: React.FC<TravelInsightsPanelProps> = ({
  insights,
  isLoading = false,
  className = '',
  isMobile = false
}) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(['overview', 'balance'])
  );

  const toggleSection = (section: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(section)) {
        newSet.delete(section);
      } else {
        newSet.add(section);
      }
      return newSet;
    });
  };

  if (isLoading) {
    return (
      <div className={`p-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!insights) {
    return (
      <div className={`p-4 text-center text-gray-500 ${className}`}>
        <Lightbulb size={48} className="mx-auto mb-3 text-gray-300" />
        <p>Select destinations and POIs to see travel insights</p>
      </div>
    );
  }

  const BalanceBar: React.FC<{ 
    label: string; 
    value: number; 
    leftLabel: string; 
    rightLabel: string;
    color?: string;
  }> = ({ label, value, leftLabel, rightLabel, color = 'blue' }) => (
    <div className="mb-4">
      <div className="flex justify-between text-sm text-gray-600 mb-1">
        <span>{leftLabel}</span>
        <span className="font-medium">{label}</span>
        <span>{rightLabel}</span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className={`bg-${color}-500 h-2 rounded-full transition-all duration-500`}
          style={{ width: `${value}%` }}
        />
      </div>
      <div className="text-center mt-1">
        <span className="text-xs text-gray-500">{Math.round(value)}%</span>
      </div>
    </div>
  );

  const ScoreCircle: React.FC<{ score: number; size?: 'sm' | 'md' | 'lg' }> = ({ 
    score, 
    size = 'md' 
  }) => {
    const sizeClasses = {
      sm: 'w-12 h-12 text-sm',
      md: 'w-16 h-16 text-lg',
      lg: 'w-20 h-20 text-xl'
    };

    const getScoreColor = (score: number) => {
      if (score >= 80) return 'text-green-600 border-green-600';
      if (score >= 60) return 'text-blue-600 border-blue-600';
      if (score >= 40) return 'text-yellow-600 border-yellow-600';
      return 'text-red-600 border-red-600';
    };

    return (
      <div className={`${sizeClasses[size]} rounded-full border-4 ${getScoreColor(score)} flex items-center justify-center font-bold`}>
        {score}
      </div>
    );
  };

  const SectionHeader: React.FC<{ 
    title: string; 
    icon: React.ReactNode; 
    sectionKey: string;
    count?: number;
  }> = ({ title, icon, sectionKey, count }) => (
    <button
      onClick={() => toggleSection(sectionKey)}
      className="w-full flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 transition-colors rounded-lg mb-3"
    >
      <div className="flex items-center space-x-2">
        {icon}
        <span className="font-semibold text-gray-900">{title}</span>
        {count !== undefined && (
          <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
            {count}
          </span>
        )}
      </div>
      {expandedSections.has(sectionKey) ? 
        <ChevronUp size={20} className="text-gray-500" /> : 
        <ChevronDown size={20} className="text-gray-500" />
      }
    </button>
  );

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Overview Section */}
      <div>
        <SectionHeader 
          title="Overview" 
          icon={<Target size={20} className="text-blue-600" />}
          sectionKey="overview"
        />
        {expandedSections.has('overview') && (
          <div className="p-4 bg-white rounded-lg border">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Itinerary Score</h3>
                <p className="text-sm text-gray-600">Overall optimization rating</p>
              </div>
              <ScoreCircle score={insights.overallScore} size="lg" />
            </div>
            
            {insights.improvements.length > 0 && (
              <div className="mt-4">
                <h4 className="font-medium text-gray-900 mb-2">Quick Improvements</h4>
                <ul className="space-y-1">
                  {insights.improvements.slice(0, 3).map((improvement, index) => (
                    <li key={index} className="text-sm text-gray-600 flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      {improvement}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Balance Analysis */}
      <div>
        <SectionHeader 
          title="Balance Analysis" 
          icon={<BarChart3 size={20} className="text-green-600" />}
          sectionKey="balance"
        />
        {expandedSections.has('balance') && (
          <div className="p-4 bg-white rounded-lg border">
            <BalanceBar
              label="Urban vs Rural"
              value={insights.balance.urbanVsRural}
              leftLabel="Rural"
              rightLabel="Urban"
              color="green"
            />
            <BalanceBar
              label="Cultural vs Nature"
              value={insights.balance.culturalVsNature}
              leftLabel="Nature"
              rightLabel="Cultural"
              color="purple"
            />
            <BalanceBar
              label="Active vs Relaxed"
              value={insights.balance.activeVsRelaxed}
              leftLabel="Relaxed"
              rightLabel="Active"
              color="orange"
            />
            
            {/* Budget Distribution */}
            <div className="mt-4 pt-4 border-t">
              <h4 className="font-medium text-gray-900 mb-3">Budget Distribution</h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex justify-between">
                  <span>Free:</span>
                  <span className="font-medium">{Math.round(insights.balance.budgetDistribution.free)}%</span>
                </div>
                <div className="flex justify-between">
                  <span>Budget:</span>
                  <span className="font-medium">{Math.round(insights.balance.budgetDistribution.budget)}%</span>
                </div>
                <div className="flex justify-between">
                  <span>Moderate:</span>
                  <span className="font-medium">{Math.round(insights.balance.budgetDistribution.moderate)}%</span>
                </div>
                <div className="flex justify-between">
                  <span>Premium:</span>
                  <span className="font-medium">{Math.round(insights.balance.budgetDistribution.premium)}%</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Time Estimates */}
      <div>
        <SectionHeader 
          title="Time Planning" 
          icon={<Clock size={20} className="text-blue-600" />}
          sectionKey="time"
        />
        {expandedSections.has('time') && (
          <div className="p-4 bg-white rounded-lg border">
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {Math.round(insights.timeEstimates.totalDrivingTime)}h
                </div>
                <div className="text-sm text-gray-600">Driving Time</div>
              </div>
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {Math.round(insights.timeEstimates.totalVisitTime)}h
                </div>
                <div className="text-sm text-gray-600">Visit Time</div>
              </div>
            </div>
            
            <div className="text-center p-3 bg-purple-50 rounded-lg">
              <div className="text-xl font-bold text-purple-600">
                {insights.timeEstimates.recommendedDuration} days
              </div>
              <div className="text-sm text-gray-600">Recommended Duration</div>
            </div>
          </div>
        )}
      </div>

      {/* Seasonal Recommendations */}
      <div>
        <SectionHeader 
          title="Best Time to Visit" 
          icon={<Sun size={20} className="text-yellow-600" />}
          sectionKey="seasonal"
        />
        {expandedSections.has('seasonal') && (
          <div className="p-4 bg-white rounded-lg border">
            <div className="mb-4">
              <h4 className="font-medium text-gray-900 mb-2">Best Months</h4>
              <div className="flex flex-wrap gap-2">
                {insights.seasonalRecommendations.bestMonths.map(month => (
                  <span key={month} className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">
                    {month}
                  </span>
                ))}
              </div>
            </div>
            
            <div className="mb-4">
              <h4 className="font-medium text-gray-900 mb-2">Weather Considerations</h4>
              <ul className="space-y-1">
                {insights.seasonalRecommendations.weatherConsiderations.map((consideration, index) => (
                  <li key={index} className="text-sm text-gray-600 flex items-start">
                    <span className="text-yellow-500 mr-2">•</span>
                    {consideration}
                  </li>
                ))}
              </ul>
            </div>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Crowd Level:</span>
                <span className={`ml-2 font-medium ${
                  insights.seasonalRecommendations.crowdLevels === 'low' ? 'text-green-600' :
                  insights.seasonalRecommendations.crowdLevels === 'moderate' ? 'text-yellow-600' :
                  'text-red-600'
                }`}>
                  {insights.seasonalRecommendations.crowdLevels}
                </span>
              </div>
              <div>
                <span className="text-gray-600">Pricing:</span>
                <span className={`ml-2 font-medium ${
                  insights.seasonalRecommendations.pricingImpact === 'budget' ? 'text-green-600' :
                  insights.seasonalRecommendations.pricingImpact === 'standard' ? 'text-blue-600' :
                  'text-purple-600'
                }`}>
                  {insights.seasonalRecommendations.pricingImpact}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Personalized Recommendations */}
      <div>
        <SectionHeader 
          title="Recommendations" 
          icon={<Lightbulb size={20} className="text-purple-600" />}
          sectionKey="recommendations"
          count={insights.personalizedRecommendations.additionalPOIs.length}
        />
        {expandedSections.has('recommendations') && (
          <div className="p-4 bg-white rounded-lg border">
            {insights.personalizedRecommendations.additionalPOIs.length > 0 && (
              <div className="mb-4">
                <h4 className="font-medium text-gray-900 mb-2">Suggested POIs</h4>
                <div className="space-y-2">
                  {insights.personalizedRecommendations.additionalPOIs.slice(0, 3).map(poi => (
                    <div key={poi.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <div>
                        <div className="font-medium text-sm">{poi.name}</div>
                        <div className="text-xs text-gray-600">{poi.location}</div>
                      </div>
                      {poi.rating && (
                        <div className="flex items-center text-xs text-yellow-600">
                          <Star size={12} className="fill-current mr-1" />
                          {poi.rating}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {insights.personalizedRecommendations.experienceEnhancements.length > 0 && (
              <div className="mb-4">
                <h4 className="font-medium text-gray-900 mb-2">Experience Tips</h4>
                <ul className="space-y-1">
                  {insights.personalizedRecommendations.experienceEnhancements.slice(0, 3).map((tip, index) => (
                    <li key={index} className="text-sm text-gray-600 flex items-start">
                      <span className="text-purple-500 mr-2">•</span>
                      {tip}
                    </li>
                  ))}
                </ul>
              </div>
            )}
            
            {insights.personalizedRecommendations.budgetTips.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Budget Tips</h4>
                <ul className="space-y-1">
                  {insights.personalizedRecommendations.budgetTips.slice(0, 2).map((tip, index) => (
                    <li key={index} className="text-sm text-gray-600 flex items-start">
                      <span className="text-green-500 mr-2">•</span>
                      {tip}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default TravelInsightsPanel;
