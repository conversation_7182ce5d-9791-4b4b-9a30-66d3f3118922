/**
 * AI-Powered POI Recommendation Panel
 * Displays intelligent suggestions with user-friendly selection mechanism
 */

import React, { useState, useEffect } from 'react';
import { Sparkles, ThumbsUp, ThumbsDown, Plus, Info } from 'lucide-react';
import { POI, Destination } from '../../types/Destination';
import POIRecommendationService from '../../services/ai/POIRecommendationService';

interface POIRecommendation {
  poi: POI;
  score: number;
  reason: string;
  category: string;
  priority: 'high' | 'medium' | 'low';
}

interface AIRecommendationPanelProps {
  selectedCities: Destination[];
  selectedPOIs: POI[];
  availablePOIs: POI[];
  userInterests: string[];
  travelStyle: 'luxury' | 'budget' | 'adventure' | 'cultural' | 'romantic';
  tripDuration: number;
  onPOISelect: (poi: POI) => void;
  onPOIDeselect: (poi: POI) => void;
}

const AIRecommendationPanel: React.FC<AIRecommendationPanelProps> = ({
  selectedCities,
  selectedPOIs,
  availablePOIs,
  userInterests,
  travelStyle,
  tripDuration,
  onPOISelect,
  onPOIDeselect
}) => {
  const [recommendations, setRecommendations] = useState<POIRecommendation[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [categories, setCategories] = useState<string[]>([]);
  const [feedback, setFeedback] = useState<Record<string, 'like' | 'dislike'>>({});

  // Get recommendations when dependencies change
  useEffect(() => {
    if (selectedCities.length > 0 && userInterests.length > 0) {
      getRecommendations();
    }
  }, [selectedCities, userInterests, travelStyle, tripDuration]);

  const getRecommendations = async () => {
    setIsLoading(true);
    try {
      const service = POIRecommendationService.getInstance();
      const response = await service.getRecommendations(
        {
          interests: userInterests,
          selectedCities,
          tripDuration,
          travelStyle,
          groupSize: 2 // Default, could be made configurable
        },
        availablePOIs
      );

      setRecommendations(response.recommendations);
      setCategories(['all', ...response.categories]);
    } catch (error) {
      console.error('Failed to get AI recommendations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredRecommendations = selectedCategory === 'all' 
    ? recommendations 
    : recommendations.filter(rec => rec.category === selectedCategory);

  const handlePOIAction = (recommendation: POIRecommendation) => {
    const isSelected = selectedPOIs.some(poi => poi.id === recommendation.poi.id);
    
    if (isSelected) {
      onPOIDeselect(recommendation.poi);
    } else {
      onPOISelect(recommendation.poi);
    }
  };

  const handleFeedback = (poiId: string, type: 'like' | 'dislike') => {
    setFeedback(prev => ({
      ...prev,
      [poiId]: type
    }));
    
    // TODO: Send feedback to backend for learning
    console.log(`User ${type}d POI: ${poiId}`);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  if (selectedCities.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">
        <Sparkles className="mx-auto mb-2 h-8 w-8 opacity-50" />
        <p>Select cities to get AI-powered recommendations</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center gap-2 mb-3">
          <Sparkles className="h-5 w-5 text-purple-600" />
          <h3 className="font-semibold text-gray-900">AI Recommendations</h3>
          {isLoading && (
            <div className="animate-spin h-4 w-4 border-2 border-purple-600 border-t-transparent rounded-full"></div>
          )}
        </div>

        {/* Category Filter */}
        <div className="flex gap-2 overflow-x-auto">
          {categories.map(category => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-3 py-1 rounded-full text-sm font-medium whitespace-nowrap transition-colors ${
                selectedCategory === category
                  ? 'bg-purple-100 text-purple-700'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              {category === 'all' ? 'All' : category}
            </button>
          ))}
        </div>
      </div>

      {/* Recommendations List */}
      <div className="max-h-96 overflow-y-auto">
        {isLoading ? (
          <div className="p-8 text-center">
            <div className="animate-pulse space-y-4">
              {[1, 2, 3].map(i => (
                <div key={i} className="h-20 bg-gray-100 rounded"></div>
              ))}
            </div>
          </div>
        ) : filteredRecommendations.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <Info className="mx-auto mb-2 h-8 w-8 opacity-50" />
            <p>No recommendations found for your preferences</p>
            <button 
              onClick={getRecommendations}
              className="mt-2 text-purple-600 hover:text-purple-700 text-sm"
            >
              Try again
            </button>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {filteredRecommendations.map((recommendation) => {
              const isSelected = selectedPOIs.some(poi => poi.id === recommendation.poi.id);
              const userFeedback = feedback[recommendation.poi.id];

              return (
                <div key={recommendation.poi.id} className="p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-start justify-between gap-3">
                    <div className="flex-1 min-w-0">
                      {/* POI Name and Priority */}
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-gray-900 truncate">
                          {recommendation.poi.name}
                        </h4>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(recommendation.priority)}`}>
                          {recommendation.priority}
                        </span>
                      </div>

                      {/* AI Reason */}
                      <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                        {recommendation.reason}
                      </p>

                      {/* Score and Category */}
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <span>Score: {recommendation.score}/100</span>
                        <span>Category: {recommendation.category}</span>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-2 flex-shrink-0">
                      {/* Feedback Buttons */}
                      <button
                        onClick={() => handleFeedback(recommendation.poi.id, 'like')}
                        className={`p-1 rounded transition-colors ${
                          userFeedback === 'like' 
                            ? 'text-green-600 bg-green-50' 
                            : 'text-gray-400 hover:text-green-600'
                        }`}
                      >
                        <ThumbsUp className="h-4 w-4" />
                      </button>
                      
                      <button
                        onClick={() => handleFeedback(recommendation.poi.id, 'dislike')}
                        className={`p-1 rounded transition-colors ${
                          userFeedback === 'dislike' 
                            ? 'text-red-600 bg-red-50' 
                            : 'text-gray-400 hover:text-red-600'
                        }`}
                      >
                        <ThumbsDown className="h-4 w-4" />
                      </button>

                      {/* Add/Remove Button */}
                      <button
                        onClick={() => handlePOIAction(recommendation)}
                        className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                          isSelected
                            ? 'bg-red-100 text-red-700 hover:bg-red-200'
                            : 'bg-purple-100 text-purple-700 hover:bg-purple-200'
                        }`}
                      >
                        {isSelected ? 'Remove' : 'Add'}
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Footer */}
      {filteredRecommendations.length > 0 && (
        <div className="p-3 border-t border-gray-200 bg-gray-50 text-center">
          <p className="text-xs text-gray-500">
            Showing {filteredRecommendations.length} AI-powered recommendations
          </p>
        </div>
      )}
    </div>
  );
};

export default AIRecommendationPanel;
