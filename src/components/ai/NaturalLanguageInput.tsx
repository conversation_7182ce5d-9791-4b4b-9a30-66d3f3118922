/**
 * Natural Language Trip Planning Input
 * Allows users to describe their trip in natural language and auto-selects cities/POIs
 */

import React, { useState, useRef } from 'react';
import { Search, Sparkles, Loader2, Send, Mic, X } from 'lucide-react';
import { Destination, POI } from '../../types/Destination';
import NaturalLanguagePlanner from '../../services/ai/NaturalLanguagePlanner';

interface NaturalLanguageInputProps {
  availableCities: Destination[];
  availablePOIs: POI[];
  onCitiesSelected: (cities: Destination[]) => void;
  onPOIsSelected: (pois: POI[]) => void;
  onItineraryGenerated: (itinerary: any[]) => void;
  placeholder?: string;
  className?: string;
}

const NaturalLanguageInput: React.FC<NaturalLanguageInputProps> = ({
  availableCities,
  availablePOIs,
  onCitiesSelected,
  onPOIsSelected,
  onItineraryGenerated,
  placeholder = "Describe your dream trip... e.g., '7-day romantic journey through Morocco with mountain views'",
  className = ""
}) => {
  const [input, setInput] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [lastResult, setLastResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isProcessing) return;

    setIsProcessing(true);
    setError(null);

    try {
      const planner = NaturalLanguagePlanner.getInstance();
      const result = await planner.planTrip(input.trim(), availableCities, availablePOIs);

      setLastResult(result);

      // Auto-select the suggested cities and POIs
      if (result.suggestedCities.length > 0) {
        onCitiesSelected(result.suggestedCities);
      }

      if (result.suggestedPOIs.length > 0) {
        onPOIsSelected(result.suggestedPOIs);
      }

      if (result.itinerary.length > 0) {
        onItineraryGenerated(result.itinerary);
      }

      // Show success feedback
      setIsExpanded(true);
      
    } catch (err) {
      console.error('Natural language planning failed:', err);
      setError(err instanceof Error ? err.message : 'Failed to process your request. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleClear = () => {
    setInput('');
    setLastResult(null);
    setError(null);
    setIsExpanded(false);
    inputRef.current?.focus();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const examplePrompts = [
    "5-day adventure trip with hiking and cultural sites",
    "Romantic weekend in historic cities with great food",
    "Family-friendly 7-day journey with nature and museums",
    "Luxury 10-day tour with premium experiences"
  ];

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center gap-2 mb-2">
          <Sparkles className="h-5 w-5 text-purple-600" />
          <h3 className="font-semibold text-gray-900">AI Trip Planner</h3>
        </div>
        <p className="text-sm text-gray-600">
          Describe your ideal trip and let AI plan it for you
        </p>
      </div>

      {/* Input Form */}
      <form onSubmit={handleSubmit} className="p-4">
        <div className="relative">
          <textarea
            ref={inputRef}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            onFocus={() => setIsExpanded(true)}
            placeholder={placeholder}
            className="w-full p-3 pr-12 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
            rows={isExpanded ? 3 : 1}
            disabled={isProcessing}
          />
          
          {/* Action Buttons */}
          <div className="absolute right-2 top-2 flex items-center gap-1">
            {input && (
              <button
                type="button"
                onClick={handleClear}
                className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                disabled={isProcessing}
              >
                <X className="h-4 w-4" />
              </button>
            )}
            
            <button
              type="submit"
              disabled={!input.trim() || isProcessing}
              className="p-1 text-purple-600 hover:text-purple-700 disabled:text-gray-400 transition-colors"
            >
              {isProcessing ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </button>
          </div>
        </div>

        {/* Example Prompts */}
        {isExpanded && !input && (
          <div className="mt-3">
            <p className="text-xs text-gray-500 mb-2">Try these examples:</p>
            <div className="flex flex-wrap gap-2">
              {examplePrompts.map((prompt, index) => (
                <button
                  key={index}
                  type="button"
                  onClick={() => setInput(prompt)}
                  className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                >
                  {prompt}
                </button>
              ))}
            </div>
          </div>
        )}
      </form>

      {/* Error Display */}
      {error && (
        <div className="px-4 pb-4">
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-700">{error}</p>
          </div>
        </div>
      )}

      {/* Results Display */}
      {lastResult && (
        <div className="px-4 pb-4">
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Sparkles className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-800">
                AI Planning Complete!
              </span>
              <span className="text-xs text-green-600">
                {lastResult.confidence}% confidence
              </span>
            </div>
            
            <div className="text-sm text-green-700 space-y-1">
              <p>
                <strong>Trip:</strong> {lastResult.intent.duration} days, {lastResult.intent.travelStyle} style
              </p>
              <p>
                <strong>Cities:</strong> {lastResult.suggestedCities.map((c: Destination) => c.name).join(', ')}
              </p>
              <p>
                <strong>POIs:</strong> {lastResult.suggestedPOIs.length} recommendations added
              </p>
              {lastResult.intent.interests.length > 0 && (
                <p>
                  <strong>Interests:</strong> {lastResult.intent.interests.join(', ')}
                </p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Processing Indicator */}
      {isProcessing && (
        <div className="px-4 pb-4">
          <div className="flex items-center gap-3 p-3 bg-purple-50 border border-purple-200 rounded-lg">
            <Loader2 className="h-4 w-4 animate-spin text-purple-600" />
            <span className="text-sm text-purple-700">
              AI is analyzing your request and planning your trip...
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default NaturalLanguageInput;
