import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  Wand2, 
  MessageSquare, 
  MapPin, 
  Star, 
  Clock,
  Users,
  Heart,
  Compass,
  Lightbulb,
  ChevronRight,
  Loader2,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import NaturalLanguageInput from './NaturalLanguageInput';
import AIRecommendationPanel from './AIRecommendationPanel';
import { Destination, POI } from '../../types/Destination';
import { PointOfInterest } from '@/types/POITypes';

interface EnhancedAIPanelProps {
  selectedCities: any[];
  selectedPOIs: PointOfInterest[];
  availablePOIs: PointOfInterest[];
  userInterests: string[];
  travelStyle: 'luxury' | 'budget' | 'adventure' | 'cultural' | 'romantic';
  tripDuration: number;
  onCitiesSelected: (cities: Destination[]) => void;
  onPOIsSelected: (pois: POI[]) => void;
  onItineraryGenerated: (itinerary: any[]) => void;
  onPOISelect: (poi: PointOfInterest) => void;
  onPOIDeselect: (poi: PointOfInterest) => void;
}

const EnhancedAIPanel: React.FC<EnhancedAIPanelProps> = ({
  selectedCities,
  selectedPOIs,
  availablePOIs,
  userInterests,
  travelStyle,
  tripDuration,
  onCitiesSelected,
  onPOIsSelected,
  onItineraryGenerated,
  onPOISelect,
  onPOIDeselect
}) => {
  const [activeTab, setActiveTab] = useState<'planner' | 'recommendations' | 'insights'>('planner');
  const [aiStatus, setAiStatus] = useState<'idle' | 'thinking' | 'ready' | 'error'>('idle');
  const [lastAIAction, setLastAIAction] = useState<string>('');

  // Quick action suggestions based on current state
  const quickActions = [
    {
      id: 'cultural-tour',
      title: 'Cultural Heritage Tour',
      description: 'Discover Morocco\'s rich history and traditions',
      icon: <Compass size={16} />,
      prompt: 'I want to explore Morocco\'s cultural heritage, including historical sites, traditional markets, and local crafts'
    },
    {
      id: 'adventure-trip',
      title: 'Adventure Experience',
      description: 'Outdoor activities and thrilling experiences',
      icon: <Users size={16} />,
      prompt: 'Plan an adventure trip with hiking, desert experiences, and outdoor activities in Morocco'
    },
    {
      id: 'luxury-escape',
      title: 'Luxury Getaway',
      description: 'Premium accommodations and exclusive experiences',
      icon: <Star size={16} />,
      prompt: 'Create a luxury Morocco experience with premium riads, fine dining, and exclusive tours'
    },
    {
      id: 'romantic-journey',
      title: 'Romantic Journey',
      description: 'Perfect for couples and special occasions',
      icon: <Heart size={16} />,
      prompt: 'Design a romantic Morocco trip for couples with intimate settings and special experiences'
    }
  ];

  const handleQuickAction = (action: typeof quickActions[0]) => {
    setLastAIAction(action.title);
    setAiStatus('thinking');
    // The NaturalLanguageInput component will handle the actual processing
  };

  const AIStatusIndicator = () => (
    <div className="flex items-center space-x-2 mb-4">
      <div className={`w-3 h-3 rounded-full ${
        aiStatus === 'idle' ? 'bg-gray-300' :
        aiStatus === 'thinking' ? 'bg-yellow-400 animate-pulse' :
        aiStatus === 'ready' ? 'bg-green-400' :
        'bg-red-400'
      }`} />
      <span className="text-sm text-gray-600">
        {aiStatus === 'idle' && 'AI Assistant Ready'}
        {aiStatus === 'thinking' && 'AI is planning your trip...'}
        {aiStatus === 'ready' && 'Recommendations ready!'}
        {aiStatus === 'error' && 'AI temporarily unavailable'}
      </span>
      {aiStatus === 'thinking' && <Loader2 size={16} className="animate-spin text-blue-600" />}
    </div>
  );

  const TabButton: React.FC<{
    id: string;
    label: string;
    icon: React.ReactNode;
    active: boolean;
    onClick: () => void;
  }> = ({ id, label, icon, active, onClick }) => (
    <button
      onClick={onClick}
      className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all ${
        active 
          ? 'bg-blue-600 text-white shadow-md' 
          : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
      }`}
    >
      {icon}
      <span>{label}</span>
    </button>
  );

  return (
    <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200">
      {/* Header */}
      <div className="text-center mb-6">
        <div className="flex items-center justify-center space-x-2 mb-2">
          <div className="p-2 bg-blue-600 rounded-lg">
            <Sparkles className="text-white" size={24} />
          </div>
          <h2 className="text-xl font-bold text-gray-900">AI Travel Assistant</h2>
        </div>
        <p className="text-sm text-gray-600">
          Let AI create your perfect Moroccan adventure with smart recommendations
        </p>
      </div>

      {/* AI Status */}
      <AIStatusIndicator />

      {/* Tab Navigation */}
      <div className="flex space-x-2 mb-6 bg-gray-50 p-1 rounded-lg">
        <TabButton
          id="planner"
          label="Smart Planner"
          icon={<Brain size={16} />}
          active={activeTab === 'planner'}
          onClick={() => setActiveTab('planner')}
        />
        <TabButton
          id="recommendations"
          label="Recommendations"
          icon={<Lightbulb size={16} />}
          active={activeTab === 'recommendations'}
          onClick={() => setActiveTab('recommendations')}
        />
        <TabButton
          id="insights"
          label="Insights"
          icon={<MessageSquare size={16} />}
          active={activeTab === 'insights'}
          onClick={() => setActiveTab('insights')}
        />
      </div>

      {/* Tab Content */}
      {activeTab === 'planner' && (
        <div className="space-y-6">
          {/* Natural Language Input */}
          <div>
            <h3 className="text-sm font-semibold text-gray-700 mb-3 flex items-center">
              <MessageSquare size={16} className="mr-2" />
              Describe Your Dream Trip
            </h3>
            <NaturalLanguageInput
              availableCities={selectedCities.map(city => ({
                id: city.id,
                name: city.name,
                description: city.description || '',
                coordinates: city.coordinates || [0, 0],
                region: city.region || '',
                country: 'Morocco',
                tags: city.tags || [],
                images: city.images || [],
                position: city.position
              }))}
              availablePOIs={availablePOIs.map(poi => ({
                id: poi.id,
                name: poi.name,
                description: poi.description,
                category: poi.category,
                tags: poi.tags,
                location: poi.location,
                region: poi.region,
                coordinates: poi.coordinates
              }))}
              onCitiesSelected={onCitiesSelected}
              onPOIsSelected={onPOIsSelected}
              onItineraryGenerated={onItineraryGenerated}
              placeholder="Tell me about your ideal Morocco trip..."
              className="w-full"
            />
          </div>

          {/* Quick Actions */}
          <div>
            <h3 className="text-sm font-semibold text-gray-700 mb-3 flex items-center">
              <Wand2 size={16} className="mr-2" />
              Quick Trip Ideas
            </h3>
            <div className="grid grid-cols-1 gap-3">
              {quickActions.map(action => (
                <button
                  key={action.id}
                  onClick={() => handleQuickAction(action)}
                  className="p-3 bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all text-left group"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      <div className="p-2 bg-blue-100 rounded-lg text-blue-600 group-hover:bg-blue-200">
                        {action.icon}
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900 text-sm">{action.title}</h4>
                        <p className="text-xs text-gray-600 mt-1">{action.description}</p>
                      </div>
                    </div>
                    <ChevronRight size={16} className="text-gray-400 group-hover:text-blue-600" />
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'recommendations' && (
        <div>
          {selectedCities.length > 0 ? (
            <AIRecommendationPanel
              selectedCities={selectedCities.map(city => ({
                id: city.id,
                name: city.name,
                description: city.description || '',
                coordinates: city.coordinates || [0, 0],
                region: city.region || '',
                country: 'Morocco',
                tags: city.tags || [],
                images: city.images || [],
                position: city.position
              }))}
              selectedPOIs={selectedPOIs.map(poi => ({
                id: poi.id,
                name: poi.name,
                description: poi.description,
                category: poi.category,
                tags: poi.tags,
                location: poi.location,
                region: poi.region,
                coordinates: poi.coordinates
              }))}
              availablePOIs={availablePOIs.map(poi => ({
                id: poi.id,
                name: poi.name,
                description: poi.description,
                category: poi.category,
                tags: poi.tags,
                location: poi.location,
                region: poi.region,
                coordinates: poi.coordinates
              }))}
              userInterests={userInterests}
              travelStyle={travelStyle}
              tripDuration={tripDuration}
              onPOISelect={onPOISelect}
              onPOIDeselect={onPOIDeselect}
            />
          ) : (
            <div className="text-center py-8">
              <MapPin size={48} className="mx-auto text-gray-300 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Select Cities First</h3>
              <p className="text-gray-600 text-sm">
                Choose your destinations to get personalized AI recommendations
              </p>
            </div>
          )}
        </div>
      )}

      {activeTab === 'insights' && (
        <div className="space-y-4">
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <h3 className="font-medium text-gray-900 mb-2 flex items-center">
              <CheckCircle size={16} className="mr-2 text-green-600" />
              Trip Analysis
            </h3>
            <div className="space-y-2 text-sm text-gray-600">
              <p>• {selectedCities.length} cities selected</p>
              <p>• {selectedPOIs.length} attractions in itinerary</p>
              <p>• {tripDuration} day trip duration</p>
              <p>• {travelStyle} travel style</p>
            </div>
          </div>

          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <h3 className="font-medium text-gray-900 mb-2 flex items-center">
              <Lightbulb size={16} className="mr-2 text-yellow-600" />
              AI Suggestions
            </h3>
            <div className="space-y-2 text-sm text-gray-600">
              {selectedCities.length < 2 && (
                <p className="flex items-start">
                  <AlertCircle size={14} className="mr-2 text-orange-500 mt-0.5 flex-shrink-0" />
                  Consider adding more cities for a diverse experience
                </p>
              )}
              {selectedPOIs.length < 3 && (
                <p className="flex items-start">
                  <AlertCircle size={14} className="mr-2 text-orange-500 mt-0.5 flex-shrink-0" />
                  Add more attractions to make the most of your trip
                </p>
              )}
              {tripDuration > 10 && (
                <p className="flex items-start">
                  <CheckCircle size={14} className="mr-2 text-green-500 mt-0.5 flex-shrink-0" />
                  Great trip length for exploring Morocco thoroughly
                </p>
              )}
            </div>
          </div>

          {lastAIAction && (
            <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
              <h3 className="font-medium text-blue-900 mb-2">Last AI Action</h3>
              <p className="text-sm text-blue-700">{lastAIAction}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default EnhancedAIPanel;
