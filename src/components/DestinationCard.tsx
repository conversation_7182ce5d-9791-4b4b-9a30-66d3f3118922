
import React from 'react';
import { Destination } from '@/data/destinations';
import { Check, Clock, MapPin } from 'lucide-react';
import { formatDuration } from '@/utils/routeUtils';

interface DestinationCardProps {
  destination: Destination;
  isSelected: boolean;
  onSelect: () => void;
  displayStyle?: 'grid' | 'list';
}

const DestinationCard: React.FC<DestinationCardProps> = ({
  destination,
  isSelected,
  onSelect,
  displayStyle = 'grid',
}) => {
  if (displayStyle === 'list') {
    return (
      <div
        className={`relative flex rounded-lg overflow-hidden shadow-sm border transition-all hover:shadow-md cursor-pointer ${
          isSelected ? 'ring-2 ring-morocco-terracotta border-morocco-terracotta' : 'border-transparent'
        }`}
        onClick={onSelect}
      >
        <div className="flex-shrink-0 w-1/3 relative">
          <img
            src={destination.image}
            alt={destination.name}
            className="h-full w-full object-cover"
          />
          {isSelected && (
            <div className="absolute top-2 right-2 bg-morocco-terracotta text-white rounded-full p-1">
              <Check className="h-4 w-4" />
            </div>
          )}
        </div>
        <div className="p-4 flex-grow">
          <div className="flex justify-between items-start mb-1">
            <h3 className="font-bold">{destination.name}</h3>
            <span className="bg-morocco-sand text-morocco-dark text-xs px-2 py-1 rounded-full ml-2">
              {destination.type}
            </span>
          </div>
          <p className="text-sm text-gray-600 mb-2 line-clamp-2">{destination.description}</p>
          <div className="flex items-center text-xs text-gray-500">
            <Clock className="mr-1 h-3 w-3" />
            <span>Recommended: {formatDuration(destination.suggestedDuration)}</span>
          </div>
          <div className="flex items-center text-xs text-gray-500 mt-1">
            <MapPin className="mr-1 h-3 w-3" />
            <span>{destination.type}</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`destination-card relative rounded-lg overflow-hidden shadow-sm border transition-all hover:shadow-md cursor-pointer ${
        isSelected ? 'ring-2 ring-morocco-terracotta border-morocco-terracotta' : 'border-transparent'
      }`}
      onClick={onSelect}
    >
      <div className="relative h-32">
        <img
          src={destination.image}
          alt={destination.name}
          className="h-full w-full object-cover"
        />
        {isSelected && (
          <div className="absolute top-2 right-2 bg-morocco-terracotta text-white rounded-full p-1">
            <Check className="h-4 w-4" />
          </div>
        )}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-2">
          <span className="text-white font-semibold">{destination.name}</span>
        </div>
      </div>
      <div className="p-3">
        <div className="flex justify-between items-center mb-1">
          <span className="bg-morocco-sand text-morocco-dark text-xs px-2 py-0.5 rounded-full">
            {destination.type}
          </span>
          <div className="flex items-center text-xs text-gray-500">
            <Clock className="mr-1 h-3 w-3" />
            <span>{formatDuration(destination.suggestedDuration)}</span>
          </div>
        </div>
        <p className="text-xs text-gray-600 line-clamp-2 mt-1">{destination.description}</p>
      </div>
    </div>
  );
};

export default DestinationCard;
