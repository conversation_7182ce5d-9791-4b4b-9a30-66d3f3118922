import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { CalendarIcon, Clock, MapPin, Users, Route as RouteIcon, Calendar as CalendarRange, CheckCircle2 } from 'lucide-react';
import type { Destination, PointOfInterest } from '@/types/POITypes';
import { Vehicle } from '@/data/vehicles';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { submitQuoteRequest } from '@/utils/supabaseHelpers';
import { formatDuration } from '@/utils/routeUtils';
import { sendQuoteRequestToAdmin, sendQuoteConfirmationToCustomer } from '@/services/emailService';
import { useToast } from '@/components/ui/use-toast';
import { Alert, AlertDescription } from '@/components/ui/alert';

const formSchema = z.object({
  fullName: z.string().min(2, { message: 'Please enter your full name' }),
  email: z.string().email({ message: 'Please enter a valid email address' }),
  phone: z.string().optional(),
  travelDate: z.date().optional(),
  numTravelers: z.coerce.number().int().min(1, { message: 'At least 1 traveler is required' }),
  specialRequests: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface QuoteFormModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedDestinations: Destination[];
  selectedVehicle: Vehicle | null;
  selectedPOIs: PointOfInterest[];
  routeDetails: {
    totalDistance: number;
    totalDuration: number;
    recommendedDays: number;
  } | null;
  onSubmit: () => void;
}

const QuoteFormModal = ({
  open,
  onOpenChange,
  selectedDestinations,
  selectedVehicle,
  selectedPOIs,
  routeDetails,
  onSubmit,
}: QuoteFormModalProps) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [submitting, setSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: user?.user_metadata?.full_name || '',
      email: user?.email || '',
      phone: '',
      numTravelers: 2,
      specialRequests: '',
    },
  });

  const handleSubmit = async (values: FormValues) => {
    if (!user) {
      navigate('/auth');
      return;
    }

    if (!routeDetails) {
      return;
    }

    setSubmitting(true);
    setSubmitError(null);

    try {
      // 1. Submit to database first
      const { data, error } = await submitQuoteRequest(
        user.id,
        values.fullName,
        values.email,
        values.phone || null,
        values.travelDate ? format(values.travelDate, 'yyyy-MM-dd') : null,
        values.numTravelers,
        values.specialRequests || null,
        {
          destinations: selectedDestinations,
          vehicle: selectedVehicle,
          points_of_interest: selectedPOIs,
          route_stats: {
            totalDistance: routeDetails.totalDistance,
            totalDuration: routeDetails.totalDuration,
            recommendedDays: routeDetails.recommendedDays
          }
        }
      );

      if (error) throw error;
      
      // 2. Send emails
      try {
        // Prepare email data
        const emailData = {
          fullName: values.fullName,
          email: values.email,
          phone: values.phone,
          travelDate: values.travelDate,
          numTravelers: values.numTravelers,
          specialRequests: values.specialRequests,
          destinations: selectedDestinations,
          vehicle: selectedVehicle,
          pointsOfInterest: selectedPOIs,
          routeStats: {
            totalDistance: routeDetails.totalDistance,
            totalDuration: routeDetails.totalDuration,
            recommendedDays: routeDetails.recommendedDays
          }
        };

        // Send email to admin
        await sendQuoteRequestToAdmin(emailData);
        
        // Send confirmation email to customer
        await sendQuoteConfirmationToCustomer(emailData);
        
        // Show success message
        setSubmitSuccess(true);
        
        toast({
          title: 'Quote request sent!',
          description: 'We have received your request and will be in touch soon.',
        });
        
        // Reset form
        setTimeout(() => {
          form.reset();
          onSubmit();
          onOpenChange(false);
          setSubmitSuccess(false);
        }, 2000);
        
      } catch (emailError: any) {
        console.error('Error sending emails:', emailError);
        // Even if email fails, the quote was still saved in the database
        setSubmitSuccess(true);
        toast({
          title: 'Quote request saved',
          description: 'Your request was saved but there was an issue sending confirmation emails.',
        });
      }
    } catch (error: any) {
      console.error('Error submitting quote request:', error);
      setSubmitError('There was a problem submitting your request. Please try again.');
      toast({
        title: 'Error',
        description: 'There was a problem submitting your request. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setSubmitting(false);
    }
  };

  if (submitSuccess) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[500px]">
          <div className="flex flex-col items-center justify-center py-8">
            <div className="bg-green-100 rounded-full p-4 mb-6">
              <CheckCircle2 className="h-12 w-12 text-green-600" />
            </div>
            <h2 className="text-2xl font-bold mb-4">Request Submitted!</h2>
            <p className="text-center text-gray-600 mb-6">
              Thank you for your quote request. We've sent a confirmation to your email and our team will be in touch with you shortly.
            </p>
            <Button 
              className="bg-morocco-terracotta hover:bg-morocco-terracotta/90"
              onClick={() => {
                onOpenChange(false);
                setSubmitSuccess(false);
              }}
            >
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Request a Personalized Quote</DialogTitle>
          <DialogDescription>
            Fill out the form below and our travel experts will create a personalized quote for your Morocco trip.
          </DialogDescription>
        </DialogHeader>

        {submitError && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{submitError}</AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 my-6">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-lg mb-4">Your Itinerary</h3>
            
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-sm text-gray-500 mb-2 flex items-center">
                  <MapPin className="h-4 w-4 mr-1 text-morocco-terracotta" /> Destinations
                </h4>
                <ul className="space-y-2">
                  {selectedDestinations.map((dest, index) => (
                    <li key={dest.id} className="flex items-center">
                      <span className="bg-morocco-terracotta text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-2">
                        {index + 1}
                      </span>
                      <span>{dest.name}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {selectedVehicle && (
                <div>
                  <h4 className="font-medium text-sm text-gray-500 mb-2 flex items-center">
                    <Users className="h-4 w-4 mr-1 text-morocco-terracotta" /> Transportation
                  </h4>
                  <p className="ml-7">{selectedVehicle.name} ({selectedVehicle.type})</p>
                </div>
              )}

              {selectedPOIs.length > 0 && (
                <div>
                  <h4 className="font-medium text-sm text-gray-500 mb-2 flex items-center">
                    <MapPin className="h-4 w-4 mr-1 text-morocco-terracotta" /> Points of Interest
                  </h4>
                  <ul className="space-y-1 ml-7">
                    {selectedPOIs.map((poi) => (
                      <li key={poi.id} className="text-sm">
                        {poi.name}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {routeDetails && (
                <div className="border-t border-gray-200 pt-3 mt-4">
                  <div className="flex items-center mb-2">
                    <RouteIcon className="h-4 w-4 mr-1 text-morocco-terracotta" />
                    <span className="text-sm">{routeDetails.totalDistance} km</span>
                  </div>
                  <div className="flex items-center mb-2">
                    <Clock className="h-4 w-4 mr-1 text-morocco-terracotta" />
                    <span className="text-sm">{formatDuration(routeDetails.totalDuration)}</span>
                  </div>
                  <div className="flex items-center">
                    <CalendarRange className="h-4 w-4 mr-1 text-morocco-terracotta" />
                    <span className="text-sm">{routeDetails.recommendedDays} days recommended</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          <div>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="fullName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Full Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter your name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Address</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number (optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="****** 567 890" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="travelDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Estimated Travel Date</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>Select date</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) =>
                                date < new Date()
                              }
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="numTravelers"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Number of Travelers</FormLabel>
                        <FormControl>
                          <Input type="number" min="1" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <FormField
                  control={form.control}
                  name="specialRequests"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Additional Requests (optional)</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Any special requirements or questions..." 
                          className="min-h-[100px]" 
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <DialogFooter className="mt-6">
                  <Button 
                    type="submit" 
                    className="w-full bg-morocco-terracotta hover:bg-morocco-terracotta/90"
                    disabled={submitting}
                  >
                    {submitting ? 'Submitting...' : 'Submit Quote Request'}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default QuoteFormModal;
