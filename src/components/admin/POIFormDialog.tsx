
import React from 'react';
import { 
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { PointOfInterest } from '@/components/PointOfInterestCard';
import POIForm, { POIFormValues } from './POIForm';

interface POIFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  isEditMode: boolean;
  initialData: PointOfInterest | null;
  onSubmit: (data: PointOfInterest) => Promise<void>;
}

const defaultPOI: PointOfInterest = {
  id: '',
  name: '',
  description: '',
  location: '',
  type: 'landmark',
  cost: 0,
  duration: undefined,
  image: 'https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?q=80&w=1000',
  coordinates: [0, 0],
  tags: []
};

const POIFormDialog: React.FC<POIFormDialogProps> = ({
  open,
  onOpenChange,
  isEditMode,
  initialData,
  onSubmit
}) => {
  // Create the POI data object for the form
  const poiData: PointOfInterest = initialData || defaultPOI;

  // Handle form submission
  const handleSubmit = async (formData: any) => {
    // Parse tags from the form
    const tagsInput = formData.tags || '';
    const tags = tagsInput ? 
      (typeof tagsInput === 'string' ? 
        tagsInput.split(',').map((tag: string) => tag.trim()) : 
        tagsInput) : 
      [];
    
    // Create the POI object with all needed properties
    const poiToSubmit: PointOfInterest = {
      id: formData.id || poiData.id,
      name: formData.name,
      description: formData.description,
      location: formData.location,
      type: formData.type as 'landmark' | 'accommodation' | 'activity' | 'restaurant',
      cost: typeof formData.cost === 'number' ? formData.cost : 0,
      duration: formData.duration === '' ? undefined : Number(formData.duration),
      image: formData.image || defaultPOI.image,
      coordinates: [
        parseFloat(formData.coordinates[0] || 0), 
        parseFloat(formData.coordinates[1] || 0)
      ],
      tags
    };
    
    await onSubmit(poiToSubmit);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{isEditMode ? 'Edit' : 'Add'} Point of Interest</DialogTitle>
          <DialogDescription>
            {isEditMode ? 'Edit details for the selected POI.' : 'Add a new point of interest to the database.'}
          </DialogDescription>
        </DialogHeader>
        
        {/* Use the refactored POIForm component */}
        {open && (
          <POIForm 
            initialData={{
              id: poiData.id,
              name: poiData.name,
              description: poiData.description,
              location: poiData.location,
              type: poiData.type,
              cost: poiData.cost,
              duration: poiData.duration,
              image: poiData.image,
              coordinates: poiData.coordinates,
              tags: poiData.tags
            }}
            onSubmit={handleSubmit}
            isEditing={isEditMode}
          />
        )}
      </DialogContent>
    </Dialog>
  );
};

export default POIFormDialog;
