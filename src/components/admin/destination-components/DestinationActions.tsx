
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Edit, Trash2 } from 'lucide-react';
import { Destination } from '@/data/destinations';

interface DestinationActionsProps {
  destination: Destination;
  onEdit: (destination: Destination) => void;
  onDelete: (destination: Destination) => void;
}

const DestinationActions: React.FC<DestinationActionsProps> = ({ 
  destination, 
  onEdit, 
  onDelete 
}) => {
  return (
    <div className="flex justify-end space-x-2">
      <Button 
        variant="outline" 
        size="icon" 
        onClick={() => onEdit(destination)}
        className="h-8 w-8"
      >
        <Edit className="h-4 w-4" />
      </Button>
      <Button 
        variant="outline" 
        size="icon" 
        onClick={() => onDelete(destination)}
        className="h-8 w-8 text-destructive hover:text-destructive"
      >
        <Trash2 className="h-4 w-4" />
      </Button>
    </div>
  );
};

export default DestinationActions;
