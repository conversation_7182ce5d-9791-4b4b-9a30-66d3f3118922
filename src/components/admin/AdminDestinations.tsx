
import React from 'react';
import { useDestinationManagement } from '@/hooks/useDestinationManagement';
import DestinationList from './DestinationList';
import DestinationFormDialog from './DestinationFormDialog';
import DestinationHeader from './DestinationHeader';
import LoadingState from './destination-components/LoadingState';

const AdminDestinations = () => {
  const {
    destinations,
    loading,
    selectedDestination,
    isEditMode,
    open,
    handleDelete,
    handleEdit,
    handleFormSubmit,
    handleOpenChange,
    handleAddNew
  } = useDestinationManagement();

  return (
    <div className="container mx-auto py-8">
      <DestinationHeader onAddNew={handleAddNew} />

      {loading ? (
        <LoadingState />
      ) : (
        <DestinationList 
          destinations={destinations} 
          onEdit={handleEdit} 
          onDelete={handleDelete} 
        />
      )}

      <DestinationFormDialog
        open={open}
        onOpenChange={handleOpenChange}
        isEditMode={isEditMode}
        initialData={selectedDestination}
        onSubmit={handleFormSubmit}
      />
    </div>
  );
};

export default AdminDestinations;
