
import React from 'react';
import { 
  Sheet, 
  Sheet<PERSON>ontent, 
  SheetD<PERSON><PERSON>, 
  She<PERSON><PERSON><PERSON>er, 
  SheetTitle 
} from "@/components/ui/sheet";
import { Form } from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Destination } from "@/data/destinations";
import { destinationSchema, DestinationFormValues } from './schemas/destinationSchema';
import DestinationBasicInfoFields from './form-sections/DestinationBasicInfoFields';
import DestinationLocationFields from './form-sections/DestinationLocationFields';
import DestinationDetailsFields from './form-sections/DestinationDetailsFields';
import FormActions from './form-sections/FormActions';

interface DestinationFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  isEditMode: boolean;
  initialData: Destination | null;
  onSubmit: (data: Destination) => void;
}

const DestinationFormDialog: React.FC<DestinationFormDialogProps> = ({
  open,
  onOpenChange,
  isEditMode,
  initialData,
  onSubmit
}) => {
  const form = useForm<DestinationFormValues>({
    resolver: zodResolver(destinationSchema),
    defaultValues: initialData || {
      name: "",
      description: "",
      image: "",
      coordinates: [0, 0],
      type: "city",
      suggestedDuration: 0,
      cost: 0
    }
  });

  React.useEffect(() => {
    if (initialData) {
      form.reset(initialData);
    } else {
      form.reset({
        name: "",
        description: "",
        image: "",
        coordinates: [0, 0],
        type: "city",
        suggestedDuration: 0,
        cost: 0
      });
    }
  }, [initialData, form]);

  const handleSubmit = (data: DestinationFormValues) => {
    onSubmit(data as Destination);
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-[600px] overflow-y-auto bg-background z-50">
        <SheetHeader>
          <SheetTitle>{isEditMode ? "Edit Destination" : "Add Destination"}</SheetTitle>
          <SheetDescription>
            {isEditMode 
              ? "Update the destination details." 
              : "Add a new destination to your Morocco travel guide."}
          </SheetDescription>
        </SheetHeader>
        
        <div className="py-4">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
              <DestinationBasicInfoFields control={form.control} />
              
              <DestinationLocationFields control={form.control} />
              
              <DestinationDetailsFields control={form.control} />
              
              <FormActions 
                isEditMode={isEditMode} 
                onCancel={() => onOpenChange(false)} 
              />
            </form>
          </Form>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default DestinationFormDialog;
