
import React from 'react';
import { Destination } from '@/data/destinations';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import DestinationActions from './destination-components/DestinationActions';
import EmptyState from './destination-components/EmptyState';

interface DestinationListProps {
  destinations: Destination[];
  onEdit: (destination: Destination) => void;
  onDelete: (destination: Destination) => void;
}

const DestinationList: React.FC<DestinationListProps> = ({ destinations, onEdit, onDelete }) => {
  if (destinations.length === 0) {
    return <EmptyState />;
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[250px]">Name</TableHead>
            <TableHead className="w-[100px]">Type</TableHead>
            <TableHead className="w-[150px]">Duration (hrs)</TableHead>
            <TableHead className="w-[100px]">Cost ($)</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {destinations.map((destination) => (
            <TableRow key={destination.id}>
              <TableCell className="font-medium">{destination.name}</TableCell>
              <TableCell className="capitalize">{destination.type}</TableCell>
              <TableCell>{destination.suggestedDuration}</TableCell>
              <TableCell>${destination.cost}</TableCell>
              <TableCell className="text-right">
                <DestinationActions 
                  destination={destination} 
                  onEdit={onEdit} 
                  onDelete={onDelete} 
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default DestinationList;
