import React from 'react';
import { FeatureFlags } from '@/types/ClientTypes';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';

interface ClientFeatureFlagsFormProps {
  featureFlags: FeatureFlags;
  onSave: (featureFlags: FeatureFlags) => void;
  isSaving: boolean;
}

const ClientFeatureFlagsForm: React.FC<ClientFeatureFlagsFormProps> = ({ 
  featureFlags, 
  onSave, 
  isSaving 
}) => {
  const [formData, setFormData] = React.useState<FeatureFlags>(featureFlags);

  React.useEffect(() => {
    setFormData(featureFlags);
  }, [featureFlags]);

  const handleToggle = (key: keyof FeatureFlags, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [key]: checked
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  // Feature descriptions for better context
  const featureDescriptions: Record<keyof FeatureFlags, string> = {
    routeAnimation: 'Enable animated transitions when navigating between route points',
    poiDiscovery: 'Allow users to discover nearby points of interest',
    itineraryBuilder: 'Enable users to create and customize travel itineraries',
    userAccounts: 'Enable user registration and account management',
    routeSaving: 'Allow users to save and share their routes',
    analytics: 'Collect anonymous usage data to improve the experience'
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">User Experience Features</h3>
          
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="pt-0.5">
                <Switch
                  id="routeAnimation"
                  checked={formData.routeAnimation}
                  onCheckedChange={(checked) => handleToggle('routeAnimation', checked)}
                />
              </div>
              <div className="space-y-1">
                <Label htmlFor="routeAnimation" className="font-medium">Route Animation</Label>
                <p className="text-sm text-gray-500">{featureDescriptions.routeAnimation}</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="pt-0.5">
                <Switch
                  id="poiDiscovery"
                  checked={formData.poiDiscovery}
                  onCheckedChange={(checked) => handleToggle('poiDiscovery', checked)}
                />
              </div>
              <div className="space-y-1">
                <Label htmlFor="poiDiscovery" className="font-medium">POI Discovery</Label>
                <p className="text-sm text-gray-500">{featureDescriptions.poiDiscovery}</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="pt-0.5">
                <Switch
                  id="itineraryBuilder"
                  checked={formData.itineraryBuilder}
                  onCheckedChange={(checked) => handleToggle('itineraryBuilder', checked)}
                />
              </div>
              <div className="space-y-1">
                <Label htmlFor="itineraryBuilder" className="font-medium">Itinerary Builder</Label>
                <p className="text-sm text-gray-500">{featureDescriptions.itineraryBuilder}</p>
              </div>
            </div>
          </div>
        </div>
        
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Account & Data Features</h3>
          
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="pt-0.5">
                <Switch
                  id="userAccounts"
                  checked={formData.userAccounts}
                  onCheckedChange={(checked) => handleToggle('userAccounts', checked)}
                />
              </div>
              <div className="space-y-1">
                <Label htmlFor="userAccounts" className="font-medium">User Accounts</Label>
                <p className="text-sm text-gray-500">{featureDescriptions.userAccounts}</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="pt-0.5">
                <Switch
                  id="routeSaving"
                  checked={formData.routeSaving}
                  onCheckedChange={(checked) => handleToggle('routeSaving', checked)}
                />
              </div>
              <div className="space-y-1">
                <Label htmlFor="routeSaving" className="font-medium">Route Saving</Label>
                <p className="text-sm text-gray-500">{featureDescriptions.routeSaving}</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="pt-0.5">
                <Switch
                  id="analytics"
                  checked={formData.analytics}
                  onCheckedChange={(checked) => handleToggle('analytics', checked)}
                />
              </div>
              <div className="space-y-1">
                <Label htmlFor="analytics" className="font-medium">Analytics</Label>
                <p className="text-sm text-gray-500">{featureDescriptions.analytics}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-end mt-6">
        <Button type="submit" disabled={isSaving}>
          {isSaving ? 'Saving...' : 'Save Feature Flags'}
        </Button>
      </div>
    </form>
  );
};

export default ClientFeatureFlagsForm;