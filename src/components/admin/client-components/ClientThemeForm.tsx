import React from 'react';
import { ThemeConfig } from '@/types/ClientTypes';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Palette, Type, Mouse, Map, Smartphone } from 'lucide-react';

interface ClientThemeFormProps {
  theme: ThemeConfig;
  onSave: (theme: ThemeConfig) => void;
  isSaving: boolean;
}

const ClientThemeForm: React.FC<ClientThemeFormProps> = ({ theme, onSave, isSaving }) => {
  const [formData, setFormData] = React.useState<ThemeConfig>(theme);

  React.useEffect(() => {
    setFormData(theme);
  }, [theme]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  const handleNestedChange = (section: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section as keyof ThemeConfig],
        [field]: value
      }
    }));
  };

  const handleColorChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const ColorInput = ({ label, value, onChange }: { label: string; value: string; onChange: (value: string) => void }) => (
    <div className="space-y-2">
      <Label>{label}</Label>
      <div className="flex space-x-2">
        <Input
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="flex-1"
        />
        <input
          type="color"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="w-10 h-10 p-1 border rounded"
        />
      </div>
    </div>
  );

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Tabs defaultValue="colors" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="colors" className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            Colors
          </TabsTrigger>
          <TabsTrigger value="typography" className="flex items-center gap-2">
            <Type className="h-4 w-4" />
            Typography
          </TabsTrigger>
          <TabsTrigger value="components" className="flex items-center gap-2">
            <Mouse className="h-4 w-4" />
            Components
          </TabsTrigger>
          <TabsTrigger value="map" className="flex items-center gap-2">
            <Map className="h-4 w-4" />
            Map & Markers
          </TabsTrigger>
          <TabsTrigger value="responsive" className="flex items-center gap-2">
            <Smartphone className="h-4 w-4" />
            Mobile
          </TabsTrigger>
        </TabsList>

        {/* Colors Tab */}
        <TabsContent value="colors" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Primary Colors</CardTitle>
              <CardDescription>Main brand colors for your travel application</CardDescription>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <ColorInput
                label="Primary Color"
                value={formData.primaryColor}
                onChange={(value) => handleColorChange('primaryColor', value)}
              />
              <ColorInput
                label="Secondary Color"
                value={formData.secondaryColor}
                onChange={(value) => handleColorChange('secondaryColor', value)}
              />
              <ColorInput
                label="Accent Color"
                value={formData.accentColor}
                onChange={(value) => handleColorChange('accentColor', value)}
              />
              <ColorInput
                label="Background Color"
                value={formData.backgroundColor}
                onChange={(value) => handleColorChange('backgroundColor', value)}
              />
              <ColorInput
                label="Text Color"
                value={formData.textColor}
                onChange={(value) => handleColorChange('textColor', value)}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Typography Tab */}
        <TabsContent value="typography" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Font Settings</CardTitle>
              <CardDescription>Typography configuration for your brand</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Display Font (Headings)</Label>
                <Select
                  value={formData.fontFamily}
                  onValueChange={(value) => handleColorChange('fontFamily', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Inter, sans-serif">Inter (Modern)</SelectItem>
                    <SelectItem value="Playfair Display, serif">Playfair Display (Elegant)</SelectItem>
                    <SelectItem value="Bebas Neue, cursive">Bebas Neue (Bold)</SelectItem>
                    <SelectItem value="Crimson Text, serif">Crimson Text (Classic)</SelectItem>
                    <SelectItem value="Russo One, sans-serif">Russo One (Strong)</SelectItem>
                    <SelectItem value="Oswald, sans-serif">Oswald (Condensed)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="logoUrl">Logo URL</Label>
                <Input
                  id="logoUrl"
                  value={formData.logoUrl}
                  onChange={(e) => handleColorChange('logoUrl', e.target.value)}
                  placeholder="https://example.com/logo.svg"
                />
              </div>

              {formData.logoUrl && (
                <div className="p-4 border rounded flex justify-center items-center bg-gray-50">
                  <img
                    src={formData.logoUrl}
                    alt="Client Logo"
                    className="max-h-20 object-contain"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = '/assets/placeholder-logo.svg';
                    }}
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Components Tab */}
        <TabsContent value="components" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Button Styling</CardTitle>
              <CardDescription>Customize button appearance and behavior</CardDescription>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <ColorInput
                label="Primary Button Background"
                value={formData.buttons?.primary?.background || formData.primaryColor}
                onChange={(value) => handleNestedChange('buttons', 'primary', {
                  ...formData.buttons?.primary,
                  background: value
                })}
              />
              <ColorInput
                label="Primary Button Text"
                value={formData.buttons?.primary?.text || '#ffffff'}
                onChange={(value) => handleNestedChange('buttons', 'primary', {
                  ...formData.buttons?.primary,
                  text: value
                })}
              />
              <ColorInput
                label="Secondary Button Background"
                value={formData.buttons?.secondary?.background || formData.backgroundColor}
                onChange={(value) => handleNestedChange('buttons', 'secondary', {
                  ...formData.buttons?.secondary,
                  background: value
                })}
              />
              <ColorInput
                label="Secondary Button Text"
                value={formData.buttons?.secondary?.text || formData.primaryColor}
                onChange={(value) => handleNestedChange('buttons', 'secondary', {
                  ...formData.buttons?.secondary,
                  text: value
                })}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Map Tab */}
        <TabsContent value="map" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Map Markers</CardTitle>
              <CardDescription>Customize map markers and route colors</CardDescription>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <ColorInput
                label="Destination Marker"
                value={formData.map?.markerColors?.destination || formData.primaryColor}
                onChange={(value) => handleNestedChange('map', 'markerColors', {
                  ...formData.map?.markerColors,
                  destination: value
                })}
              />
              <ColorInput
                label="POI Marker"
                value={formData.map?.markerColors?.poi || formData.secondaryColor}
                onChange={(value) => handleNestedChange('map', 'markerColors', {
                  ...formData.map?.markerColors,
                  poi: value
                })}
              />
              <ColorInput
                label="Selected POI Marker"
                value={formData.map?.markerColors?.selectedPoi || formData.accentColor}
                onChange={(value) => handleNestedChange('map', 'markerColors', {
                  ...formData.map?.markerColors,
                  selectedPoi: value
                })}
              />
              <ColorInput
                label="Route Color"
                value={formData.map?.routeColor || formData.primaryColor}
                onChange={(value) => handleNestedChange('map', 'routeColor', value)}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Mobile Tab */}
        <TabsContent value="responsive" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Mobile Optimization</CardTitle>
              <CardDescription>Settings specific to mobile devices</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-4 border rounded bg-blue-50">
                <p className="text-sm text-blue-800">
                  Mobile-specific styling is automatically applied based on your theme colors.
                  The system ensures touch-friendly interfaces and proper responsive behavior.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Theme Preview */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Live Preview</CardTitle>
          <CardDescription>See how your theme will look to users</CardDescription>
        </CardHeader>
        <CardContent>
          <div
            className="p-6 rounded shadow-sm border"
            style={{
              backgroundColor: formData.backgroundColor,
              color: formData.textColor,
              fontFamily: formData.fontFamily.replace(/"/g, ''),
            }}
          >
            <div className="flex items-center space-x-4 mb-4">
              {formData.logoUrl && (
                <img
                  src={formData.logoUrl}
                  alt="Logo"
                  className="h-10 w-auto"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = '/assets/placeholder-logo.svg';
                  }}
                />
              )}
              <h2 style={{ color: formData.primaryColor }} className="text-xl font-bold">
                Your Travel App
              </h2>
            </div>
            <p className="mb-4">This is a preview of how your theme will look to users.</p>
            <div className="flex flex-wrap gap-2">
              <button
                className="px-4 py-2 rounded transition-colors"
                style={{
                  backgroundColor: formData.buttons?.primary?.background || formData.primaryColor,
                  color: formData.buttons?.primary?.text || 'white'
                }}
              >
                Begin Journey
              </button>
              <button
                className="px-4 py-2 rounded border transition-colors"
                style={{
                  backgroundColor: formData.buttons?.secondary?.background || formData.backgroundColor,
                  color: formData.buttons?.secondary?.text || formData.primaryColor,
                  borderColor: formData.primaryColor
                }}
              >
                Explore POIs
              </button>
              <button
                className="px-4 py-2 rounded border transition-colors"
                style={{
                  borderColor: formData.accentColor,
                  color: formData.accentColor,
                  backgroundColor: 'transparent'
                }}
              >
                View Details
              </button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="pt-4 border-t flex justify-end">
        <Button type="submit" disabled={isSaving}>
          {isSaving ? 'Saving...' : 'Save Complete Theme'}
        </Button>
      </div>
    </form>
  );
};

export default ClientThemeForm;