/**
 * ClientDestinationsForm.tsx
 * 
 * Admin form for managing client destinations/cities
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Trash2, Plus, MapPin, Edit, Save, X } from 'lucide-react';
import type { Destination } from '@/types/Destination';

interface ClientDestinationsFormProps {
  initialDestinations?: Destination[];
  onSave: (destinations: Destination[]) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const ClientDestinationsForm: React.FC<ClientDestinationsFormProps> = ({
  initialDestinations = [],
  onSave,
  onCancel,
  isLoading = false
}) => {
  const [destinations, setDestinations] = useState<Destination[]>(initialDestinations);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [newDestination, setNewDestination] = useState<Partial<Destination>>({
    name: '',
    description: '',
    coordinates: [0, 0],
    type: 'city',
    priority: 1,
    isCapital: false
  });

  const handleAddDestination = () => {
    if (!newDestination.name || !newDestination.coordinates) return;

    const destination: Destination = {
      id: `dest-${Date.now()}`,
      name: newDestination.name,
      description: newDestination.description || '',
      coordinates: newDestination.coordinates as [number, number],
      type: newDestination.type as 'city' | 'town' | 'village' | 'landmark',
      priority: newDestination.priority || 1,
      isCapital: newDestination.isCapital || false,
      region: newDestination.region,
      population: newDestination.population,
      imageUrl: newDestination.imageUrl,
      tags: newDestination.tags || []
    };

    setDestinations(prev => [...prev, destination]);
    setNewDestination({
      name: '',
      description: '',
      coordinates: [0, 0],
      type: 'city',
      priority: 1,
      isCapital: false
    });
  };

  const handleEditDestination = (id: string, field: keyof Destination, value: any) => {
    setDestinations(prev => prev.map(dest => 
      dest.id === id ? { ...dest, [field]: value } : dest
    ));
  };

  const handleDeleteDestination = (id: string) => {
    setDestinations(prev => prev.filter(dest => dest.id !== id));
  };

  const handleCoordinateChange = (id: string, index: 0 | 1, value: string) => {
    const numValue = parseFloat(value);
    if (isNaN(numValue)) return;

    setDestinations(prev => prev.map(dest => {
      if (dest.id === id) {
        const newCoords = [...dest.coordinates] as [number, number];
        newCoords[index] = numValue;
        return { ...dest, coordinates: newCoords };
      }
      return dest;
    }));
  };

  const handleNewCoordinateChange = (index: 0 | 1, value: string) => {
    const numValue = parseFloat(value);
    if (isNaN(numValue)) return;

    const newCoords = [...(newDestination.coordinates || [0, 0])] as [number, number];
    newCoords[index] = numValue;
    setNewDestination(prev => ({ ...prev, coordinates: newCoords }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(destinations);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Client Destinations
          </CardTitle>
          <CardDescription>
            Manage cities and destinations available for this client
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Add New Destination */}
          <div className="border rounded-lg p-4 bg-muted/50">
            <h4 className="font-medium mb-4">Add New Destination</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Name *</Label>
                <Input
                  value={newDestination.name}
                  onChange={(e) => setNewDestination(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="e.g., Marrakech"
                />
              </div>

              <div className="space-y-2">
                <Label>Type</Label>
                <Select
                  value={newDestination.type}
                  onValueChange={(value) => setNewDestination(prev => ({ ...prev, type: value as any }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="city">City</SelectItem>
                    <SelectItem value="town">Town</SelectItem>
                    <SelectItem value="village">Village</SelectItem>
                    <SelectItem value="landmark">Landmark</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Longitude *</Label>
                <Input
                  type="number"
                  step="any"
                  value={newDestination.coordinates?.[0] || ''}
                  onChange={(e) => handleNewCoordinateChange(0, e.target.value)}
                  placeholder="e.g., -7.9811"
                />
              </div>

              <div className="space-y-2">
                <Label>Latitude *</Label>
                <Input
                  type="number"
                  step="any"
                  value={newDestination.coordinates?.[1] || ''}
                  onChange={(e) => handleNewCoordinateChange(1, e.target.value)}
                  placeholder="e.g., 31.6295"
                />
              </div>

              <div className="space-y-2">
                <Label>Priority</Label>
                <Input
                  type="number"
                  min="1"
                  max="10"
                  value={newDestination.priority}
                  onChange={(e) => setNewDestination(prev => ({ ...prev, priority: parseInt(e.target.value) }))}
                />
              </div>

              <div className="space-y-2">
                <Label>Region</Label>
                <Input
                  value={newDestination.region || ''}
                  onChange={(e) => setNewDestination(prev => ({ ...prev, region: e.target.value }))}
                  placeholder="e.g., Marrakech-Safi"
                />
              </div>

              <div className="md:col-span-2 space-y-2">
                <Label>Description</Label>
                <Textarea
                  value={newDestination.description}
                  onChange={(e) => setNewDestination(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Brief description of the destination..."
                  rows={2}
                />
              </div>

              <div className="md:col-span-2">
                <Button
                  type="button"
                  onClick={handleAddDestination}
                  disabled={!newDestination.name || !newDestination.coordinates}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Destination
                </Button>
              </div>
            </div>
          </div>

          {/* Existing Destinations */}
          <div className="space-y-4">
            <h4 className="font-medium">Current Destinations ({destinations.length})</h4>
            {destinations.length === 0 ? (
              <p className="text-muted-foreground text-center py-8">
                No destinations added yet. Add your first destination above.
              </p>
            ) : (
              <div className="space-y-3">
                {destinations.map((destination) => (
                  <div key={destination.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          {editingId === destination.id ? (
                            <Input
                              value={destination.name}
                              onChange={(e) => handleEditDestination(destination.id, 'name', e.target.value)}
                              className="font-medium"
                            />
                          ) : (
                            <div>
                              <h5 className="font-medium">{destination.name}</h5>
                              <div className="flex items-center gap-2 mt-1">
                                <Badge variant="outline" className="text-xs">
                                  {destination.type}
                                </Badge>
                                <Badge variant="secondary" className="text-xs">
                                  Priority {destination.priority}
                                </Badge>
                                {destination.isCapital && (
                                  <Badge variant="default" className="text-xs">
                                    Capital
                                  </Badge>
                                )}
                              </div>
                            </div>
                          )}
                        </div>

                        <div className="text-sm text-muted-foreground">
                          <div>Coordinates:</div>
                          <div className="flex gap-2">
                            {editingId === destination.id ? (
                              <>
                                <Input
                                  type="number"
                                  step="any"
                                  value={destination.coordinates[0]}
                                  onChange={(e) => handleCoordinateChange(destination.id, 0, e.target.value)}
                                  className="w-24 text-xs"
                                />
                                <Input
                                  type="number"
                                  step="any"
                                  value={destination.coordinates[1]}
                                  onChange={(e) => handleCoordinateChange(destination.id, 1, e.target.value)}
                                  className="w-24 text-xs"
                                />
                              </>
                            ) : (
                              <span className="font-mono">
                                {destination.coordinates[0].toFixed(4)}, {destination.coordinates[1].toFixed(4)}
                              </span>
                            )}
                          </div>
                        </div>

                        <div className="text-sm">
                          {editingId === destination.id ? (
                            <Textarea
                              value={destination.description}
                              onChange={(e) => handleEditDestination(destination.id, 'description', e.target.value)}
                              rows={2}
                              className="text-xs"
                            />
                          ) : (
                            <p className="text-muted-foreground line-clamp-2">
                              {destination.description || 'No description'}
                            </p>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center gap-2 ml-4">
                        {editingId === destination.id ? (
                          <>
                            <Button
                              type="button"
                              size="sm"
                              onClick={() => setEditingId(null)}
                            >
                              <Save className="h-4 w-4" />
                            </Button>
                            <Button
                              type="button"
                              size="sm"
                              variant="outline"
                              onClick={() => setEditingId(null)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </>
                        ) : (
                          <>
                            <Button
                              type="button"
                              size="sm"
                              variant="outline"
                              onClick={() => setEditingId(destination.id)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              type="button"
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDeleteDestination(destination.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Saving...' : 'Save Destinations'}
        </Button>
      </div>
    </form>
  );
};

export default ClientDestinationsForm;
