/**
 * ClientJourneysForm.tsx
 * 
 * Admin form for managing client pre-arranged journeys
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Trash2, Plus, Route, Edit, Save, X, MapPin, Clock, Star } from 'lucide-react';
import type { PreArrangedJourney } from '@/types/PreArrangedJourney';

interface ClientJourneysFormProps {
  initialJourneys?: PreArrangedJourney[];
  onSave: (journeys: PreArrangedJourney[]) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const ClientJourneysForm: React.FC<ClientJourneysFormProps> = ({
  initialJourneys = [],
  onSave,
  onCancel,
  isLoading = false
}) => {
  const [journeys, setJourneys] = useState<PreArrangedJourney[]>(initialJourneys);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [newJourney, setNewJourney] = useState<Partial<PreArrangedJourney>>({
    name: '',
    description: '',
    duration: 7,
    difficulty: 'moderate',
    pace: 'balanced-explorer',
    style: 'cultural-deep-dive',
    cities: [],
    highlights: [],
    tags: []
  });

  const [newCity, setNewCity] = useState('');
  const [newHighlight, setNewHighlight] = useState('');
  const [newTag, setNewTag] = useState('');

  const handleAddJourney = () => {
    if (!newJourney.name || !newJourney.description) return;

    const journey: PreArrangedJourney = {
      id: `journey-${Date.now()}`,
      name: newJourney.name,
      description: newJourney.description,
      duration: newJourney.duration || 7,
      difficulty: newJourney.difficulty as 'easy' | 'moderate' | 'challenging',
      pace: newJourney.pace as any,
      style: newJourney.style as any,
      cities: newJourney.cities || [],
      highlights: newJourney.highlights || [],
      imageUrl: newJourney.imageUrl,
      price: newJourney.price,
      seasonality: newJourney.seasonality || [],
      tags: newJourney.tags || []
    };

    setJourneys(prev => [...prev, journey]);
    setNewJourney({
      name: '',
      description: '',
      duration: 7,
      difficulty: 'moderate',
      pace: 'balanced-explorer',
      style: 'cultural-deep-dive',
      cities: [],
      highlights: [],
      tags: []
    });
  };

  const handleEditJourney = (id: string, field: keyof PreArrangedJourney, value: any) => {
    setJourneys(prev => prev.map(journey => 
      journey.id === id ? { ...journey, [field]: value } : journey
    ));
  };

  const handleDeleteJourney = (id: string) => {
    setJourneys(prev => prev.filter(journey => journey.id !== id));
  };

  const handleAddCity = () => {
    if (newCity.trim()) {
      setNewJourney(prev => ({
        ...prev,
        cities: [...(prev.cities || []), newCity.trim()]
      }));
      setNewCity('');
    }
  };

  const handleAddHighlight = () => {
    if (newHighlight.trim()) {
      setNewJourney(prev => ({
        ...prev,
        highlights: [...(prev.highlights || []), newHighlight.trim()]
      }));
      setNewHighlight('');
    }
  };

  const handleAddTag = () => {
    if (newTag.trim()) {
      setNewJourney(prev => ({
        ...prev,
        tags: [...(prev.tags || []), newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const handleRemoveFromArray = (journeyId: string, field: 'cities' | 'highlights' | 'tags', index: number) => {
    setJourneys(prev => prev.map(journey => {
      if (journey.id === journeyId) {
        const newArray = [...(journey[field] || [])];
        newArray.splice(index, 1);
        return { ...journey, [field]: newArray };
      }
      return journey;
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(journeys);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Route className="h-5 w-5" />
            Pre-Arranged Journeys
          </CardTitle>
          <CardDescription>
            Manage ready-made journey templates that appear at the bottom of the map
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Add New Journey */}
          <div className="border rounded-lg p-4 bg-muted/50">
            <h4 className="font-medium mb-4">Add New Journey</h4>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Journey Name *</Label>
                  <Input
                    value={newJourney.name}
                    onChange={(e) => setNewJourney(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="e.g., Imperial Cities Classic"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Duration (days)</Label>
                  <Input
                    type="number"
                    min="1"
                    max="30"
                    value={newJourney.duration}
                    onChange={(e) => setNewJourney(prev => ({ ...prev, duration: parseInt(e.target.value) }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Difficulty</Label>
                  <Select
                    value={newJourney.difficulty}
                    onValueChange={(value) => setNewJourney(prev => ({ ...prev, difficulty: value as any }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="easy">Easy</SelectItem>
                      <SelectItem value="moderate">Moderate</SelectItem>
                      <SelectItem value="challenging">Challenging</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Travel Pace</Label>
                  <Select
                    value={newJourney.pace}
                    onValueChange={(value) => setNewJourney(prev => ({ ...prev, pace: value as any }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="slow-immersive">Slow & Immersive</SelectItem>
                      <SelectItem value="balanced-explorer">Balanced Explorer</SelectItem>
                      <SelectItem value="fast-highlights">Fast Highlights</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Description *</Label>
                <Textarea
                  value={newJourney.description}
                  onChange={(e) => setNewJourney(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe this journey experience..."
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label>Image URL</Label>
                <Input
                  value={newJourney.imageUrl || ''}
                  onChange={(e) => setNewJourney(prev => ({ ...prev, imageUrl: e.target.value }))}
                  placeholder="https://example.com/journey-image.jpg"
                />
              </div>

              {/* Cities */}
              <div className="space-y-2">
                <Label>Cities</Label>
                <div className="flex gap-2">
                  <Input
                    value={newCity}
                    onChange={(e) => setNewCity(e.target.value)}
                    placeholder="Add city..."
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddCity())}
                  />
                  <Button type="button" onClick={handleAddCity} variant="outline" size="sm">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {newJourney.cities?.map((city, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      {city}
                      <button
                        type="button"
                        onClick={() => {
                          const newCities = [...(newJourney.cities || [])];
                          newCities.splice(index, 1);
                          setNewJourney(prev => ({ ...prev, cities: newCities }));
                        }}
                        className="ml-1 hover:text-destructive"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Highlights */}
              <div className="space-y-2">
                <Label>Highlights</Label>
                <div className="flex gap-2">
                  <Input
                    value={newHighlight}
                    onChange={(e) => setNewHighlight(e.target.value)}
                    placeholder="Add highlight..."
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddHighlight())}
                  />
                  <Button type="button" onClick={handleAddHighlight} variant="outline" size="sm">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {newJourney.highlights?.map((highlight, index) => (
                    <Badge key={index} variant="outline" className="flex items-center gap-1">
                      <Star className="h-3 w-3" />
                      {highlight}
                      <button
                        type="button"
                        onClick={() => {
                          const newHighlights = [...(newJourney.highlights || [])];
                          newHighlights.splice(index, 1);
                          setNewJourney(prev => ({ ...prev, highlights: newHighlights }));
                        }}
                        className="ml-1 hover:text-destructive"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              </div>

              <Button
                type="button"
                onClick={handleAddJourney}
                disabled={!newJourney.name || !newJourney.description}
                className="w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Journey
              </Button>
            </div>
          </div>

          {/* Existing Journeys */}
          <div className="space-y-4">
            <h4 className="font-medium">Current Journeys ({journeys.length})</h4>
            {journeys.length === 0 ? (
              <p className="text-muted-foreground text-center py-8">
                No journeys added yet. Add your first journey above.
              </p>
            ) : (
              <div className="space-y-4">
                {journeys.map((journey) => (
                  <div key={journey.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h5 className="font-medium">{journey.name}</h5>
                          <Badge variant="outline" className="text-xs">
                            <Clock className="h-3 w-3 mr-1" />
                            {journey.duration} days
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            {journey.difficulty}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-3">
                          {journey.description}
                        </p>
                        
                        <div className="space-y-2">
                          <div>
                            <span className="text-sm font-medium">Cities: </span>
                            <span className="text-sm text-muted-foreground">
                              {journey.cities.join(', ')}
                            </span>
                          </div>
                          <div>
                            <span className="text-sm font-medium">Highlights: </span>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {journey.highlights.map((highlight, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {highlight}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2 ml-4">
                        <Button
                          type="button"
                          size="sm"
                          variant="outline"
                          onClick={() => setEditingId(journey.id)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          type="button"
                          size="sm"
                          variant="destructive"
                          onClick={() => handleDeleteJourney(journey.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Saving...' : 'Save Journeys'}
        </Button>
      </div>
    </form>
  );
};

export default ClientJourneysForm;
