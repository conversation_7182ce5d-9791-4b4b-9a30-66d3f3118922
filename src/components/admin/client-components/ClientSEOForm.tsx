/**
 * ClientSEOForm.tsx
 * 
 * Admin form for managing client SEO metadata
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { X, Plus, Globe, Search, Tag, Image } from 'lucide-react';

interface SEOMetadata {
  title: string;
  description: string;
  keywords: string[];
  favicon: string;
  ogImage?: string;
  ogTitle?: string;
  ogDescription?: string;
  twitterCard?: string;
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
}

interface ClientSEOFormProps {
  initialData?: SEOMetadata;
  onSave: (data: SEOMetadata) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const ClientSEOForm: React.FC<ClientSEOFormProps> = ({
  initialData,
  onSave,
  onCancel,
  isLoading = false
}) => {
  const [formData, setFormData] = useState<SEOMetadata>({
    title: '',
    description: '',
    keywords: [],
    favicon: '',
    ogImage: '',
    ogTitle: '',
    ogDescription: '',
    twitterCard: 'summary_large_image',
    twitterTitle: '',
    twitterDescription: '',
    twitterImage: '',
    ...initialData
  });

  const [newKeyword, setNewKeyword] = useState('');

  const handleInputChange = (field: keyof SEOMetadata, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAddKeyword = () => {
    if (newKeyword.trim() && !formData.keywords.includes(newKeyword.trim())) {
      setFormData(prev => ({
        ...prev,
        keywords: [...prev.keywords, newKeyword.trim()]
      }));
      setNewKeyword('');
    }
  };

  const handleRemoveKeyword = (keyword: string) => {
    setFormData(prev => ({
      ...prev,
      keywords: prev.keywords.filter(k => k !== keyword)
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic SEO */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Basic SEO
          </CardTitle>
          <CardDescription>
            Essential SEO metadata for search engines
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Page Title</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              placeholder="e.g., Come To Morocco - Discover Authentic Travel Experiences"
              maxLength={60}
            />
            <p className="text-sm text-muted-foreground">
              {formData.title.length}/60 characters (recommended)
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Meta Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Brief description of your travel service..."
              maxLength={160}
              rows={3}
            />
            <p className="text-sm text-muted-foreground">
              {formData.description.length}/160 characters (recommended)
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="favicon">Favicon URL</Label>
            <Input
              id="favicon"
              value={formData.favicon}
              onChange={(e) => handleInputChange('favicon', e.target.value)}
              placeholder="/favicon.ico or https://example.com/favicon.ico"
            />
          </div>

          <div className="space-y-2">
            <Label>Keywords</Label>
            <div className="flex gap-2">
              <Input
                value={newKeyword}
                onChange={(e) => setNewKeyword(e.target.value)}
                placeholder="Add keyword..."
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddKeyword())}
              />
              <Button type="button" onClick={handleAddKeyword} variant="outline" size="sm">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2 mt-2">
              {formData.keywords.map((keyword) => (
                <Badge key={keyword} variant="secondary" className="flex items-center gap-1">
                  <Tag className="h-3 w-3" />
                  {keyword}
                  <button
                    type="button"
                    onClick={() => handleRemoveKeyword(keyword)}
                    className="ml-1 hover:text-destructive"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Open Graph */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Open Graph (Facebook)
          </CardTitle>
          <CardDescription>
            How your site appears when shared on Facebook and other social platforms
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="ogTitle">OG Title</Label>
            <Input
              id="ogTitle"
              value={formData.ogTitle}
              onChange={(e) => handleInputChange('ogTitle', e.target.value)}
              placeholder="Leave empty to use page title"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="ogDescription">OG Description</Label>
            <Textarea
              id="ogDescription"
              value={formData.ogDescription}
              onChange={(e) => handleInputChange('ogDescription', e.target.value)}
              placeholder="Leave empty to use meta description"
              rows={2}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="ogImage">OG Image URL</Label>
            <Input
              id="ogImage"
              value={formData.ogImage}
              onChange={(e) => handleInputChange('ogImage', e.target.value)}
              placeholder="https://example.com/og-image.jpg"
            />
            <p className="text-sm text-muted-foreground">
              Recommended: 1200x630px
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Twitter Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Image className="h-5 w-5" />
            Twitter Card
          </CardTitle>
          <CardDescription>
            How your site appears when shared on Twitter/X
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="twitterTitle">Twitter Title</Label>
            <Input
              id="twitterTitle"
              value={formData.twitterTitle}
              onChange={(e) => handleInputChange('twitterTitle', e.target.value)}
              placeholder="Leave empty to use page title"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="twitterDescription">Twitter Description</Label>
            <Textarea
              id="twitterDescription"
              value={formData.twitterDescription}
              onChange={(e) => handleInputChange('twitterDescription', e.target.value)}
              placeholder="Leave empty to use meta description"
              rows={2}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="twitterImage">Twitter Image URL</Label>
            <Input
              id="twitterImage"
              value={formData.twitterImage}
              onChange={(e) => handleInputChange('twitterImage', e.target.value)}
              placeholder="https://example.com/twitter-image.jpg"
            />
            <p className="text-sm text-muted-foreground">
              Recommended: 1200x600px
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Saving...' : 'Save SEO Settings'}
        </Button>
      </div>
    </form>
  );
};

export default ClientSEOForm;
