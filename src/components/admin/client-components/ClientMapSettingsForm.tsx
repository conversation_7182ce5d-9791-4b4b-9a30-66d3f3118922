import React from 'react';
import { MapConfig } from '@/types/ClientTypes';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';

interface ClientMapSettingsFormProps {
  mapSettings: MapConfig;
  onSave: (mapSettings: MapConfig) => void;
  isSaving: boolean;
}

const ClientMapSettingsForm: React.FC<ClientMapSettingsFormProps> = ({ 
  mapSettings, 
  onSave, 
  isSaving 
}) => {
  const [formData, setFormData] = React.useState<MapConfig>(mapSettings);

  React.useEffect(() => {
    setFormData(mapSettings);
  }, [mapSettings]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: parseFloat(value)
    }));
  };

  const handleBoundsChange = (index: number, coordIndex: number, value: string) => {
    const newBounds = [...formData.initialBounds];
    newBounds[index][coordIndex] = parseFloat(value);
    setFormData(prev => ({
      ...prev,
      initialBounds: newBounds
    }));
  };

  const handleCenterChange = (coordIndex: number, value: string) => {
    const newCenter = [...formData.defaultCenter];
    newCenter[coordIndex] = parseFloat(value);
    setFormData(prev => ({
      ...prev,
      defaultCenter: newCenter
    }));
  };

  const handlePaddingChange = (paddingKey: keyof MapConfig['padding'], value: string) => {
    if (!formData.padding) return;
    
    setFormData(prev => ({
      ...prev,
      padding: {
        ...prev.padding!,
        [paddingKey]: parseInt(value)
      }
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Map Boundaries</h3>
          
          <div className="space-y-2">
            <Label>Initial Bounds (SW and NE corners)</Label>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="sw-lng">SW Longitude</Label>
                <Input
                  id="sw-lng"
                  type="number"
                  step="0.0001"
                  value={formData.initialBounds[0][0]}
                  onChange={(e) => handleBoundsChange(0, 0, e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="sw-lat">SW Latitude</Label>
                <Input
                  id="sw-lat"
                  type="number"
                  step="0.0001"
                  value={formData.initialBounds[0][1]}
                  onChange={(e) => handleBoundsChange(0, 1, e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="ne-lng">NE Longitude</Label>
                <Input
                  id="ne-lng"
                  type="number"
                  step="0.0001"
                  value={formData.initialBounds[1][0]}
                  onChange={(e) => handleBoundsChange(1, 0, e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="ne-lat">NE Latitude</Label>
                <Input
                  id="ne-lat"
                  type="number"
                  step="0.0001"
                  value={formData.initialBounds[1][1]}
                  onChange={(e) => handleBoundsChange(1, 1, e.target.value)}
                />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label>Default Center</Label>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="center-lng">Longitude</Label>
                <Input
                  id="center-lng"
                  type="number"
                  step="0.0001"
                  value={formData.defaultCenter[0]}
                  onChange={(e) => handleCenterChange(0, e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="center-lat">Latitude</Label>
                <Input
                  id="center-lat"
                  type="number"
                  step="0.0001"
                  value={formData.defaultCenter[1]}
                  onChange={(e) => handleCenterChange(1, e.target.value)}
                />
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Map Display Settings</h3>
          
          <div className="space-y-2">
            <Label htmlFor="defaultZoom">Default Zoom Level</Label>
            <div className="flex items-center space-x-4">
              <Slider
                id="defaultZoom"
                min={0}
                max={22}
                step={0.5}
                value={[formData.defaultZoom]}
                onValueChange={(values) => {
                  setFormData(prev => ({
                    ...prev,
                    defaultZoom: values[0]
                  }));
                }}
                className="flex-1"
              />
              <span className="w-12 text-center">{formData.defaultZoom}</span>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="minZoom">Min Zoom</Label>
              <Input
                id="minZoom"
                name="minZoom"
                type="number"
                min={0}
                max={22}
                step={0.5}
                value={formData.minZoom}
                onChange={handleNumberChange}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="maxZoom">Max Zoom</Label>
              <Input
                id="maxZoom"
                name="maxZoom"
                type="number"
                min={0}
                max={22}
                step={0.5}
                value={formData.maxZoom}
                onChange={handleNumberChange}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="style">Map Style URL</Label>
            <Input
              id="style"
              name="style"
              type="text"
              value={formData.style}
              onChange={handleChange}
            />
            <p className="text-sm text-gray-500">Example: mapbox://styles/mapbox/streets-v11</p>
          </div>

          <div className="space-y-2">
            <Label>Map Padding</Label>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="padding-top">Top</Label>
                <Input
                  id="padding-top"
                  type="number"
                  value={formData.padding?.top || 0}
                  onChange={(e) => handlePaddingChange('top', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="padding-right">Right</Label>
                <Input
                  id="padding-right"
                  type="number"
                  value={formData.padding?.right || 0}
                  onChange={(e) => handlePaddingChange('right', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="padding-bottom">Bottom</Label>
                <Input
                  id="padding-bottom"
                  type="number"
                  value={formData.padding?.bottom || 0}
                  onChange={(e) => handlePaddingChange('bottom', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="padding-left">Left</Label>
                <Input
                  id="padding-left"
                  type="number"
                  value={formData.padding?.left || 0}
                  onChange={(e) => handlePaddingChange('left', e.target.value)}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="pt-4 border-t flex justify-end">
        <Button type="submit" disabled={isSaving}>
          {isSaving ? 'Saving...' : 'Save Map Settings'}
        </Button>
      </div>
    </form>
  );
};

export default ClientMapSettingsForm;