import React from 'react';
import { POIConfig, P<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FilterOption } from '@/types/ClientTypes';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent } from '@/components/ui/card';
import { Trash, Plus } from 'lucide-react';

interface ClientPOISettingsFormProps {
  poiSettings: POIConfig;
  onSave: (poiSettings: POIConfig) => void;
  isSaving: boolean;
}

const ClientPOISettingsForm: React.FC<ClientPOISettingsFormProps> = ({ 
  poiSettings, 
  onSave, 
  isSaving 
}) => {
  const [formData, setFormData] = React.useState<POIConfig>(poiSettings);

  React.useEffect(() => {
    setFormData(poiSettings);
  }, [poiSettings]);

  const handleCategoryChange = (index: number, field: keyof POICategory, value: string) => {
    const newCategories = [...formData.categories];
    newCategories[index] = {
      ...newCategories[index],
      [field]: value
    };
    
    setFormData(prev => ({
      ...prev,
      categories: newCategories
    }));
  };

  const handleAddCategory = () => {
    const newCategory: POICategory = {
      id: `category-${formData.categories.length + 1}`,
      name: 'New Category',
      icon: 'map-pin',
      color: '#000000'
    };
    
    setFormData(prev => ({
      ...prev,
      categories: [...prev.categories, newCategory]
    }));
  };

  const handleRemoveCategory = (index: number) => {
    const newCategories = [...formData.categories];
    newCategories.splice(index, 1);
    
    setFormData(prev => ({
      ...prev,
      categories: newCategories
    }));
  };

  const handleFilterChange = (index: number, field: keyof POIFilter, value: string) => {
    const newFilters = [...formData.filters];
    newFilters[index] = {
      ...newFilters[index],
      [field]: value
    };
    
    setFormData(prev => ({
      ...prev,
      filters: newFilters
    }));
  };

  const handleFilterOptionChange = (filterIndex: number, optionIndex: number, field: keyof FilterOption, value: string) => {
    const newFilters = [...formData.filters];
    newFilters[filterIndex].options[optionIndex] = {
      ...newFilters[filterIndex].options[optionIndex],
      [field]: value
    };
    
    setFormData(prev => ({
      ...prev,
      filters: newFilters
    }));
  };

  const handleAddFilter = () => {
    const newFilter: POIFilter = {
      id: `filter-${formData.filters.length + 1}`,
      name: 'New Filter',
      field: 'new_field',
      options: [{ value: 'option1', label: 'Option 1' }]
    };
    
    setFormData(prev => ({
      ...prev,
      filters: [...prev.filters, newFilter]
    }));
  };

  const handleAddFilterOption = (filterIndex: number) => {
    const newFilters = [...formData.filters];
    newFilters[filterIndex].options.push({
      value: `option-${newFilters[filterIndex].options.length + 1}`,
      label: `Option ${newFilters[filterIndex].options.length + 1}`
    });
    
    setFormData(prev => ({
      ...prev,
      filters: newFilters
    }));
  };

  const handleRemoveFilterOption = (filterIndex: number, optionIndex: number) => {
    const newFilters = [...formData.filters];
    newFilters[filterIndex].options.splice(optionIndex, 1);
    
    setFormData(prev => ({
      ...prev,
      filters: newFilters
    }));
  };

  const handleRemoveFilter = (index: number) => {
    const newFilters = [...formData.filters];
    newFilters.splice(index, 1);
    
    setFormData(prev => ({
      ...prev,
      filters: newFilters
    }));
  };

  const handleClusteringChange = (field: keyof typeof formData.clustering, value: any) => {
    setFormData(prev => ({
      ...prev,
      clustering: {
        ...prev.clustering,
        [field]: typeof value === 'string' ? parseFloat(value) : value
      }
    }));
  };

  const handleDisplayRulesChange = (field: keyof typeof formData.displayRules, value: any) => {
    setFormData(prev => ({
      ...prev,
      displayRules: {
        ...prev.displayRules,
        [field]: typeof value === 'string' && field !== 'priorityField' ? parseFloat(value) : value
      }
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* POI Categories */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">POI Categories</h3>
            <Button 
              type="button" 
              variant="outline" 
              size="sm" 
              onClick={handleAddCategory}
            >
              <Plus className="h-4 w-4 mr-2" /> Add Category
            </Button>
          </div>
          
          {formData.categories.map((category, index) => (
            <Card key={index} className="overflow-hidden">
              <CardContent className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium">Category {index + 1}</h4>
                  <Button 
                    type="button" 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => handleRemoveCategory(index)}
                  >
                    <Trash className="h-4 w-4 text-red-500" />
                  </Button>
                </div>
                
                <div className="grid grid-cols-2 gap-2">
                  <div className="space-y-2">
                    <Label htmlFor={`category-${index}-id`}>ID</Label>
                    <Input
                      id={`category-${index}-id`}
                      value={category.id}
                      onChange={(e) => handleCategoryChange(index, 'id', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`category-${index}-name`}>Name</Label>
                    <Input
                      id={`category-${index}-name`}
                      value={category.name}
                      onChange={(e) => handleCategoryChange(index, 'name', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`category-${index}-icon`}>Icon</Label>
                    <Input
                      id={`category-${index}-icon`}
                      value={category.icon}
                      onChange={(e) => handleCategoryChange(index, 'icon', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`category-${index}-color`}>Color</Label>
                    <div className="flex space-x-2">
                      <Input
                        id={`category-${index}-color`}
                        value={category.color}
                        onChange={(e) => handleCategoryChange(index, 'color', e.target.value)}
                        className="flex-1"
                      />
                      <input
                        type="color"
                        value={category.color}
                        onChange={(e) => handleCategoryChange(index, 'color', e.target.value)}
                        className="w-10 h-10 p-1 border rounded"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* POI Filters */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">POI Filters</h3>
            <Button 
              type="button" 
              variant="outline" 
              size="sm" 
              onClick={handleAddFilter}
            >
              <Plus className="h-4 w-4 mr-2" /> Add Filter
            </Button>
          </div>
          
          {formData.filters.map((filter, filterIndex) => (
            <Card key={filterIndex} className="overflow-hidden">
              <CardContent className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium">Filter {filterIndex + 1}</h4>
                  <Button 
                    type="button" 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => handleRemoveFilter(filterIndex)}
                  >
                    <Trash className="h-4 w-4 text-red-500" />
                  </Button>
                </div>
                
                <div className="grid grid-cols-2 gap-2 mb-4">
                  <div className="space-y-2">
                    <Label htmlFor={`filter-${filterIndex}-id`}>ID</Label>
                    <Input
                      id={`filter-${filterIndex}-id`}
                      value={filter.id}
                      onChange={(e) => handleFilterChange(filterIndex, 'id', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`filter-${filterIndex}-name`}>Name</Label>
                    <Input
                      id={`filter-${filterIndex}-name`}
                      value={filter.name}
                      onChange={(e) => handleFilterChange(filterIndex, 'name', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`filter-${filterIndex}-field`}>Field</Label>
                    <Input
                      id={`filter-${filterIndex}-field`}
                      value={filter.field}
                      onChange={(e) => handleFilterChange(filterIndex, 'field', e.target.value)}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label>Options</Label>
                    <Button 
                      type="button" 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => handleAddFilterOption(filterIndex)}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  {filter.options.map((option, optionIndex) => (
                    <div key={optionIndex} className="flex items-center space-x-2">
                      <Input
                        placeholder="Value"
                        value={option.value}
                        onChange={(e) => handleFilterOptionChange(filterIndex, optionIndex, 'value', e.target.value)}
                        className="flex-1"
                      />
                      <Input
                        placeholder="Label"
                        value={option.label}
                        onChange={(e) => handleFilterOptionChange(filterIndex, optionIndex, 'label', e.target.value)}
                        className="flex-1"
                      />
                      <Button 
                        type="button" 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => handleRemoveFilterOption(filterIndex, optionIndex)}
                      >
                        <Trash className="h-4 w-4 text-red-500" />
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
        {/* Clustering Options */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Clustering Options</h3>
          
          <div className="flex items-center space-x-2">
            <Switch
              id="clustering-enabled"
              checked={formData.clustering.enabled}
              onCheckedChange={(checked) => handleClusteringChange('enabled', checked)}
            />
            <Label htmlFor="clustering-enabled">Enable Clustering</Label>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="clustering-radius">Cluster Radius</Label>
            <Input
              id="clustering-radius"
              type="number"
              min="1"
              value={formData.clustering.radius}
              onChange={(e) => handleClusteringChange('radius', e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="clustering-maxZoom">Max Zoom Level for Clustering</Label>
            <Input
              id="clustering-maxZoom"
              type="number"
              min="1"
              max="22"
              value={formData.clustering.maxZoom}
              onChange={(e) => handleClusteringChange('maxZoom', e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="clustering-minPoints">Minimum Points for Cluster</Label>
            <Input
              id="clustering-minPoints"
              type="number"
              min="2"
              value={formData.clustering.minPoints}
              onChange={(e) => handleClusteringChange('minPoints', e.target.value)}
            />
          </div>
        </div>

        {/* Display Rules */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Display Rules</h3>
          
          <div className="space-y-2">
            <Label htmlFor="displayRules-minZoom">Minimum Zoom Level to Show POIs</Label>
            <Input
              id="displayRules-minZoom"
              type="number"
              min="1"
              max="22"
              value={formData.displayRules.minZoom}
              onChange={(e) => handleDisplayRulesChange('minZoom', e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="displayRules-maxDistance">Maximum Distance (meters)</Label>
            <Input
              id="displayRules-maxDistance"
              type="number"
              min="0"
              value={formData.displayRules.maxDistance}
              onChange={(e) => handleDisplayRulesChange('maxDistance', e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="displayRules-priorityField">Priority Field</Label>
            <Input
              id="displayRules-priorityField"
              type="text"
              value={formData.displayRules.priorityField}
              onChange={(e) => handleDisplayRulesChange('priorityField', e.target.value)}
            />
          </div>
        </div>
      </div>

      <div className="flex justify-end mt-6">
        <Button type="submit" disabled={isSaving}>
          {isSaving ? 'Saving...' : 'Save POI Settings'}
        </Button>
      </div>
    </form>
  );
};

export default ClientPOISettingsForm;