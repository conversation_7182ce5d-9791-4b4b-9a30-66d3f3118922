
import { useState, useEffect } from "react";
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON> 
} from "@/components/ui/card";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>itle,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertTriangle } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { Destination } from "@/data/destinations";
import { Vehicle } from "@/data/vehicles";
import { PointOfInterest } from "@/components/PointOfInterestCard";

interface QuoteRequest {
  id: string;
  user_id: string;
  full_name: string;
  email: string;
  phone: string | null;
  travel_date: string | null;
  num_travelers: number;
  special_requests: string | null;
  status: string | null;
  created_at: string | null;
  route_details: {
    destinations: Destination[];
    vehicle: Vehicle | null;
    points_of_interest: PointOfInterest[];
    route_stats: {
      totalDistance: number;
      totalDuration: number;
      recommendedDays: number;
    };
  };
}

const AdminQuotes = () => {
  const [quotes, setQuotes] = useState<QuoteRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedQuote, setSelectedQuote] = useState<QuoteRequest | null>(null);
  const [viewDetailsOpen, setViewDetailsOpen] = useState(false);

  useEffect(() => {
    const fetchQuotes = async () => {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from('quote_requests')
          .select('*');
          
        if (error) throw error;
        
        // Parse the route_details JSON for each quote
        const parsedData = data?.map(quote => ({
          ...quote,
          route_details: typeof quote.route_details === 'string' 
            ? JSON.parse(quote.route_details) 
            : quote.route_details
        })) || [];
        
        setQuotes(parsedData);
      } catch (error: any) {
        console.error('Error fetching quotes:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchQuotes();
  }, []);

  const handleViewDetails = (quote: QuoteRequest) => {
    setSelectedQuote(quote);
    setViewDetailsOpen(true);
  };

  const updateQuoteStatus = async (quoteId: string, status: string) => {
    try {
      const { error } = await supabase
        .from('quote_requests')
        .update({ status })
        .eq('id', quoteId);
        
      if (error) throw error;
      
      // Update local state
      setQuotes(prev => 
        prev.map(q => q.id === quoteId ? { ...q, status } : q)
      );
      
      if (selectedQuote && selectedQuote.id === quoteId) {
        setSelectedQuote({ ...selectedQuote, status });
      }
    } catch (error: any) {
      console.error('Error updating quote status:', error);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Quote Requests</CardTitle>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {loading ? (
          <div className="text-center py-4">Loading quote requests...</div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Client</TableHead>
                <TableHead>Date Requested</TableHead>
                <TableHead>Travel Date</TableHead>
                <TableHead>Travelers</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {quotes.length > 0 ? (
                quotes.map((quote) => (
                  <TableRow key={quote.id}>
                    <TableCell className="font-medium">{quote.full_name}</TableCell>
                    <TableCell>{quote.created_at ? new Date(quote.created_at).toLocaleDateString() : 'N/A'}</TableCell>
                    <TableCell>{quote.travel_date ? new Date(quote.travel_date).toLocaleDateString() : 'Not specified'}</TableCell>
                    <TableCell>{quote.num_travelers}</TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded text-xs ${
                        quote.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                        quote.status === 'approved' ? 'bg-green-100 text-green-800' : 
                        quote.status === 'rejected' ? 'bg-red-100 text-red-800' : 
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {quote.status || 'Unknown'}
                      </span>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="outline" size="sm" className="mr-2" onClick={() => handleViewDetails(quote)}>
                        View
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-4">No quote requests found</TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        )}
      </CardContent>

      <Sheet open={viewDetailsOpen} onOpenChange={setViewDetailsOpen}>
        <SheetContent className="w-[400px] sm:w-[600px] overflow-y-auto">
          <SheetHeader>
            <SheetTitle>Quote Request Details</SheetTitle>
          </SheetHeader>
          
          {selectedQuote && (
            <div className="py-4 space-y-6">
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">Client Information</h3>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <p className="text-sm text-gray-500">Name</p>
                    <p>{selectedQuote.full_name}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <p>{selectedQuote.email}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Phone</p>
                    <p>{selectedQuote.phone || 'Not provided'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Travelers</p>
                    <p>{selectedQuote.num_travelers}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Travel Date</p>
                    <p>{selectedQuote.travel_date ? new Date(selectedQuote.travel_date).toLocaleDateString() : 'Not specified'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Status</p>
                    <p>{selectedQuote.status}</p>
                  </div>
                </div>
              </div>
              
              {selectedQuote.special_requests && (
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold">Special Requests</h3>
                  <p className="whitespace-pre-line">{selectedQuote.special_requests}</p>
                </div>
              )}
              
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">Route Details</h3>
                
                <div>
                  <p className="text-sm text-gray-500">Destinations</p>
                  <ul className="list-disc list-inside">
                    {selectedQuote.route_details?.destinations?.map((dest, i) => (
                      <li key={i}>{dest.name}</li>
                    )) || 'No destinations'}
                  </ul>
                </div>
                
                <div>
                  <p className="text-sm text-gray-500">Vehicle</p>
                  <p>{selectedQuote.route_details?.vehicle?.name || 'Not specified'}</p>
                </div>
                
                <div>
                  <p className="text-sm text-gray-500">Route Stats</p>
                  <p>Distance: {selectedQuote.route_details?.route_stats?.totalDistance || 0} km</p>
                  <p>Duration: {selectedQuote.route_details?.route_stats?.totalDuration || 0} hours</p>
                  <p>Recommended Days: {selectedQuote.route_details?.route_stats?.recommendedDays || 0}</p>
                </div>
              </div>
              
              <div className="pt-4 flex space-x-2">
                <Button 
                  variant="outline" 
                  className="flex-1"
                  onClick={() => updateQuoteStatus(selectedQuote.id, 'rejected')}
                >
                  Reject
                </Button>
                <Button 
                  className="flex-1"
                  onClick={() => updateQuoteStatus(selectedQuote.id, 'approved')}
                >
                  Approve
                </Button>
              </div>
            </div>
          )}
        </SheetContent>
      </Sheet>
    </Card>
  );
};

export default AdminQuotes;
