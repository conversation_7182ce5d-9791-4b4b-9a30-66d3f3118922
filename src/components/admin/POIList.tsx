
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import { PointOfInterest } from "@/components/PointOfInterestCard";

interface POIListProps {
  pois: PointOfInterest[];
  onEdit: (poi: PointOfInterest) => void;
  onDelete: (poi: PointOfInterest) => void;
}

const POIList = ({ pois, onEdit, onDelete }: POIListProps) => {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Name</TableHead>
          <TableHead>Type</TableHead>
          <TableHead>Location</TableHead>
          <TableHead>Coordinates</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {pois.map((poi) => (
          <TableRow key={poi.id}>
            <TableCell className="font-medium">{poi.name}</TableCell>
            <TableCell>{poi.type}</TableCell>
            <TableCell>{poi.location}</TableCell>
            <TableCell>
              {poi.coordinates ? 
                `${poi.coordinates[0]?.toFixed(4) || '0.0000'}, ${poi.coordinates[1]?.toFixed(4) || '0.0000'}` : 
                '0.0000, 0.0000'
              }
            </TableCell>
            <TableCell className="text-right space-x-2">
              <Button variant="outline" size="sm" onClick={() => onEdit(poi)}>
                Edit
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => onDelete(poi)}
                className="bg-destructive/10 hover:bg-destructive/20 text-destructive"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

export default POIList;
