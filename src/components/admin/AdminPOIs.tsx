
import React from 'react';
import { usePOIManagement } from '@/hooks/usePOIManagement';
import POIList from './POIList';
import POIFormDialog from './POIFormDialog';
import POIHeader from './POIHeader';

const AdminPOIs = () => {
  const {
    pois,
    loading,
    selectedPoi,
    isEditMode,
    open,
    handleDelete,
    handleEdit,
    handleFormSubmit,
    handleOpenChange,
    handleAddNew
  } = usePOIManagement();

  return (
    <div className="container mx-auto py-8">
      <POIHeader onAddNew={handleAddNew} />

      {loading ? (
        <p>Loading POIs...</p>
      ) : (
        <POIList 
          pois={pois} 
          onEdit={handleEdit} 
          onDelete={handleDelete} 
        />
      )}

      <POIFormDialog
        open={open}
        onOpenChange={handleOpenChange}
        isEditMode={isEditMode}
        initialData={selectedPoi}
        onSubmit={handleFormSubmit}
      />
    </div>
  );
};

export default AdminPOIs;
