
import { useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { 
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage
} from "@/components/ui/form";
import { 
  SheetFooter
} from "@/components/ui/sheet";
import { PointOfInterest } from "@/components/PointOfInterestCard";
import BasicInfoFields from "./form-sections/BasicInfoFields";
import LocationFields from "./form-sections/LocationFields";
import DetailsFields from "./form-sections/DetailsFields";

export type POIFormValues = {
  id: string;
  name: string;
  description: string;
  type: "landmark" | "accommodation" | "activity" | "restaurant";
  image: string;
  duration?: number;
  cost: number;
  location: string;
  coordinates: [number, number];
  tags: string[];
}

interface POIFormProps {
  initialData: POIFormValues;
  onSubmit: (data: POIFormValues) => Promise<void>;
  isEditing: boolean;
}

const POIForm = ({ initialData, onSubmit, isEditing }: POIFormProps) => {
  const form = useForm<POIFormValues>({
    defaultValues: initialData
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <BasicInfoFields control={form.control} />
        <LocationFields control={form.control} />
        <DetailsFields control={form.control} />
        
        <SheetFooter>
          <Button type="submit">{isEditing ? "Update" : "Add"} POI</Button>
        </SheetFooter>
      </form>
    </Form>
  );
};

export default POIForm;
