
import { z } from "zod";

export const destinationSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  description: z.string().min(10, { message: "Description must be at least 10 characters" }),
  image: z.string().url({ message: "Please enter a valid image URL" }),
  coordinates: z.tuple([
    z.number({ message: "Longitude must be a number" }),
    z.number({ message: "Latitude must be a number" })
  ]),
  type: z.enum(["city", "landmark", "nature", "cultural"]),
  suggestedDuration: z.coerce.number().min(1, { message: "Duration must be at least 1 hour" }),
  cost: z.coerce.number().min(0, { message: "Cost cannot be negative" })
});

export type DestinationFormValues = z.infer<typeof destinationSchema>;
