/**
 * AdminClients.tsx
 * 
 * Placeholder component for managing client configurations.
 * This will allow administrators to view, add, edit, and delete client-specific settings.
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Input,
  Chip,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Tabs,
  Tab,
  User,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Spacer,
  Divider
} from '@heroui/react';
import { Plus, Edit, Trash2, Settings, Palette, MapPin, AlertTriangle, Eye, MoreVertical } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import ClientSEOForm from './client-components/ClientSEOForm';
import ClientDestinationsForm from './client-components/ClientDestinationsForm';
import ClientJourneysForm from './client-components/ClientJourneysForm';
import ClientThemeForm from './client-components/ClientThemeForm';

interface Client {
  id: string;
  name: string;
  slug: string;
  logo_url: string | null;
  status: string;
  created_at: string | null;
  updated_at: string | null;
}

const AdminClients: React.FC = () => {
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [newClient, setNewClient] = useState({
    name: '',
    slug: '',
    logo_url: '',
    status: 'active'
  });

  // HeroUI modal hooks
  const { isOpen: isAddModalOpen, onOpen: onAddModalOpen, onClose: onAddModalClose } = useDisclosure();
  const { isOpen: isConfigModalOpen, onOpen: onConfigModalOpen, onClose: onConfigModalClose } = useDisclosure();

  // Fetch clients from database
  const fetchClients = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('clients')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setClients(data || []);
    } catch (error: any) {
      console.error('Error fetching clients:', error);
      setError(error.message);
      toast.error('Failed to load clients');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchClients();
  }, []);

  // Add new client
  const handleAddClient = async () => {
    try {
      if (!newClient.name || !newClient.slug) {
        toast.error('Name and slug are required');
        return;
      }

      const { data, error } = await supabase
        .from('clients')
        .insert([{
          name: newClient.name,
          slug: newClient.slug,
          logo_url: newClient.logo_url || null,
          status: newClient.status
        }])
        .select()
        .single();

      if (error) throw error;

      setClients(prev => [data, ...prev]);
      setNewClient({ name: '', slug: '', logo_url: '', status: 'active' });
      onAddModalClose();
      toast.success('Client added successfully');
    } catch (error: any) {
      console.error('Error adding client:', error);
      toast.error('Failed to add client: ' + error.message);
    }
  };

  // Update client status
  const handleUpdateStatus = async (clientId: string, status: string) => {
    try {
      const { error } = await supabase
        .from('clients')
        .update({ status })
        .eq('id', clientId);

      if (error) throw error;

      setClients(prev => prev.map(client =>
        client.id === clientId ? { ...client, status } : client
      ));
      toast.success('Client status updated');
    } catch (error: any) {
      console.error('Error updating client status:', error);
      toast.error('Failed to update status');
    }
  };

  // Delete client
  const handleDeleteClient = async (clientId: string) => {
    if (!confirm('Are you sure you want to delete this client? This will also delete all associated data.')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('clients')
        .delete()
        .eq('id', clientId);

      if (error) throw error;

      setClients(prev => prev.filter(client => client.id !== clientId));
      toast.success('Client deleted successfully');
    } catch (error: any) {
      console.error('Error deleting client:', error);
      toast.error('Failed to delete client');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'inactive': return 'default';
      case 'suspended': return 'danger';
      default: return 'default';
    }
  };

  const handleConfigureClient = (client: Client) => {
    setSelectedClient(client);
    onConfigModalOpen();
  };

  if (loading) {
    return (
      <Card className="w-full">
        <CardBody className="p-6">
          <div className="text-center">Loading clients...</div>
        </CardBody>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="w-full">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="space-y-1">
            <h2 className="text-2xl font-bold tracking-tight">Client Management</h2>
            <p className="text-muted-foreground">
              Manage client configurations, themes, and content for your white-label travel platform
            </p>
          </div>
          <Button
            color="primary"
            startContent={<Plus className="h-4 w-4" />}
            onPress={onAddModalOpen}
          >
            Add Client
          </Button>
        </CardHeader>
        <CardBody>
          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <h4 className="font-medium text-red-800">Error</h4>
              </div>
              <p className="text-red-700 mt-1">{error}</p>
            </div>
          )}

          {clients.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-default-500">No clients found. Add your first client to get started.</p>
            </div>
          ) : (
            <Table aria-label="Client management table">
              <TableHeader>
                <TableColumn>CLIENT</TableColumn>
                <TableColumn>SLUG</TableColumn>
                <TableColumn>STATUS</TableColumn>
                <TableColumn>CREATED</TableColumn>
                <TableColumn>ACTIONS</TableColumn>
              </TableHeader>
              <TableBody>
                {clients.map((client) => (
                  <TableRow key={client.id}>
                    <TableCell>
                      <User
                        avatarProps={{
                          src: client.logo_url || undefined,
                          name: client.name.charAt(0),
                          size: "sm"
                        }}
                        description={client.id}
                        name={client.name}
                      />
                    </TableCell>
                    <TableCell>
                      <code className="text-sm bg-default-100 px-2 py-1 rounded">{client.slug}</code>
                    </TableCell>
                    <TableCell>
                      <Chip color={getStatusColor(client.status)} variant="flat" size="sm">
                        {client.status}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      {client.created_at ? new Date(client.created_at).toLocaleDateString() : 'N/A'}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          isIconOnly
                          size="sm"
                          variant="light"
                          onPress={() => handleConfigureClient(client)}
                        >
                          <Settings className="h-4 w-4" />
                        </Button>
                        <Dropdown>
                          <DropdownTrigger>
                            <Button isIconOnly size="sm" variant="light">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownTrigger>
                          <DropdownMenu>
                            <DropdownItem
                              key="toggle-status"
                              onPress={() => handleUpdateStatus(client.id, client.status === 'active' ? 'inactive' : 'active')}
                            >
                              {client.status === 'active' ? 'Deactivate' : 'Activate'}
                            </DropdownItem>
                            <DropdownItem
                              key="delete"
                              className="text-danger"
                              color="danger"
                              onPress={() => handleDeleteClient(client.id)}
                            >
                              Delete Client
                            </DropdownItem>
                          </DropdownMenu>
                        </Dropdown>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardBody>
      </Card>

      {/* Add Client Modal */}
      <Modal isOpen={isAddModalOpen} onClose={onAddModalClose} size="2xl">
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <h2 className="text-xl font-semibold">Add New Client</h2>
            <p className="text-sm text-default-500">Create a new client configuration for your travel platform</p>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <Input
                label="Client Name"
                placeholder="e.g., Morocco Heritage Tours"
                value={newClient.name}
                onChange={(e) => setNewClient(prev => ({ ...prev, name: e.target.value }))}
                isRequired
              />
              <Input
                label="Slug"
                placeholder="e.g., morocco-heritage"
                value={newClient.slug}
                onChange={(e) => setNewClient(prev => ({
                  ...prev,
                  slug: e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, '-')
                }))}
                isRequired
              />
              <Input
                label="Logo URL"
                placeholder="https://example.com/logo.svg"
                value={newClient.logo_url}
                onChange={(e) => setNewClient(prev => ({ ...prev, logo_url: e.target.value }))}
              />
            </div>
          </ModalBody>
          <ModalFooter>
            <Button color="danger" variant="light" onPress={onAddModalClose}>
              Cancel
            </Button>
            <Button color="primary" onPress={handleAddClient}>
              Add Client
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Client Configuration Modal */}
      <Modal
        isOpen={isConfigModalOpen}
        onClose={onConfigModalClose}
        size="5xl"
        scrollBehavior="inside"
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <h2 className="text-xl font-semibold">Configure {selectedClient?.name}</h2>
            <p className="text-sm text-default-500">
              Manage all aspects of this client's travel platform configuration
            </p>
          </ModalHeader>
          <ModalBody>
            <Tabs defaultValue="theme" className="w-full">
              <div className="flex w-full flex-col">
                <div className="flex space-x-1 border-b border-default-200">
                  <Tab key="theme" title={
                    <div className="flex items-center space-x-2">
                      <Palette className="h-4 w-4" />
                      <span>Theme</span>
                    </div>
                  } />
                  <Tab key="destinations" title={
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-4 w-4" />
                      <span>Destinations</span>
                    </div>
                  } />
                  <Tab key="journeys" title={
                    <div className="flex items-center space-x-2">
                      <Settings className="h-4 w-4" />
                      <span>Journeys</span>
                    </div>
                  } />
                  <Tab key="seo" title={
                    <div className="flex items-center space-x-2">
                      <Eye className="h-4 w-4" />
                      <span>SEO</span>
                    </div>
                  } />
                </div>

                <div className="mt-4">
                  <Card>
                    <CardHeader>
                      <h3 className="text-lg font-semibold">Theme Configuration</h3>
                      <p className="text-sm text-default-500">
                        Customize colors, fonts, and visual elements for {selectedClient?.name}
                      </p>
                    </CardHeader>
                    <CardBody>
                    <ClientThemeForm
                      theme={{
                        primaryColor: '#2563eb',
                        secondaryColor: '#64748b',
                        accentColor: '#0ea5e9',
                        backgroundColor: '#ffffff',
                        textColor: '#1e293b',
                        fontFamily: 'Inter, sans-serif',
                        logoUrl: selectedClient?.logo_url || ''
                      }}
                      onSave={async (themeData) => {
                        try {
                          const { error } = await supabase
                            .from('client_themes')
                            .upsert({
                              client_id: selectedClient?.id,
                              theme_config: themeData,
                              is_active: true
                            });

                          if (error) throw error;
                          toast.success('Theme saved successfully');
                        } catch (error: any) {
                          toast.error('Failed to save theme: ' + error.message);
                        }
                      }}
                    />
                    </CardBody>
                  </Card>
                </div>
              </div>
            </Tabs>
          </ModalBody>
          <ModalFooter>
            <Button color="danger" variant="light" onPress={onConfigModalClose}>
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default AdminClients;