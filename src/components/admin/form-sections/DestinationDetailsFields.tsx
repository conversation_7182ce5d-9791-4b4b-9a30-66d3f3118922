
import React from 'react';
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Control } from "react-hook-form";
import { z } from "zod";
import { destinationSchema } from '../schemas/destinationSchema';

type DestinationFormValues = z.infer<typeof destinationSchema>;

interface DestinationDetailsFieldsProps {
  control: Control<DestinationFormValues>;
}

const DestinationDetailsFields: React.FC<DestinationDetailsFieldsProps> = ({ control }) => {
  return (
    <div className="grid grid-cols-2 gap-4">
      <FormField
        control={control}
        name="suggestedDuration"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Suggested Duration (hours)</FormLabel>
            <FormControl>
              <Input 
                type="number" 
                {...field} 
                onChange={(e) => field.onChange(parseInt(e.target.value))} 
                value={field.value} 
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      
      <FormField
        control={control}
        name="cost"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Average Cost ($)</FormLabel>
            <FormControl>
              <Input 
                type="number" 
                {...field} 
                onChange={(e) => field.onChange(parseInt(e.target.value))} 
                value={field.value} 
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default DestinationDetailsFields;
