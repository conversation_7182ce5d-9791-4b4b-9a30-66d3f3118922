
import React from 'react';
import { But<PERSON> } from "@/components/ui/button";

interface FormActionsProps {
  isEditMode: boolean;
  onCancel: () => void;
}

const FormActions: React.FC<FormActionsProps> = ({ isEditMode, onCancel }) => {
  return (
    <div className="flex justify-end pt-4 space-x-2">
      <Button type="button" variant="outline" onClick={onCancel}>
        Cancel
      </Button>
      <Button type="submit">
        {isEditMode ? "Update" : "Add"} Destination
      </Button>
    </div>
  );
};

export default FormActions;
