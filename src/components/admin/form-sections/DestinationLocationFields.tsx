
import React from 'react';
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Control } from "react-hook-form";
import { z } from "zod";
import { destinationSchema } from '../schemas/destinationSchema';

type DestinationFormValues = z.infer<typeof destinationSchema>;

interface DestinationLocationFieldsProps {
  control: Control<DestinationFormValues>;
}

const DestinationLocationFields: React.FC<DestinationLocationFieldsProps> = ({ control }) => {
  return (
    <div className="grid grid-cols-2 gap-4">
      <FormField
        control={control}
        name="coordinates.0"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Longitude</FormLabel>
            <FormControl>
              <Input 
                type="number" 
                step="0.0001" 
                {...field} 
                onChange={(e) => field.onChange(parseFloat(e.target.value))} 
                value={field.value} 
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      
      <FormField
        control={control}
        name="coordinates.1"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Latitude</FormLabel>
            <FormControl>
              <Input 
                type="number" 
                step="0.0001" 
                {...field} 
                onChange={(e) => field.onChange(parseFloat(e.target.value))} 
                value={field.value} 
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default DestinationLocationFields;
