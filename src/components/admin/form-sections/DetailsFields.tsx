
import { Control } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { 
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage
} from "@/components/ui/form";
import { POIFormValues } from "../POIForm";

interface DetailsFieldsProps {
  control: Control<POIFormValues>;
}

const DetailsFields = ({ control }: DetailsFieldsProps) => {
  return (
    <div className="grid grid-cols-2 gap-4">
      <FormField
        control={control}
        name="duration"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Duration (hours)</FormLabel>
            <FormControl>
              <Input 
                type="number" 
                step="0.5" 
                {...field} 
                onChange={e => field.onChange(e.target.value === '' ? undefined : parseFloat(e.target.value))} 
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      
      <FormField
        control={control}
        name="cost"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Cost ($)</FormLabel>
            <FormControl>
              <Input 
                type="number" 
                step="1" 
                {...field} 
                onChange={e => field.onChange(e.target.value === '' ? 0 : parseFloat(e.target.value))}
                required
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default DetailsFields;
