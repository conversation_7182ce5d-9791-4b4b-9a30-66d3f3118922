
import React from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';

interface DestinationHeaderProps {
  onAddNew: () => void;
}

const DestinationHeader: React.FC<DestinationHeaderProps> = ({ onAddNew }) => {
  return (
    <div className="flex items-center justify-between mb-4">
      <h1 className="text-2xl font-bold">Manage Destinations</h1>
      <Button variant="outline" onClick={onAddNew}>
        <Plus className="mr-2 h-4 w-4" /> Add Destination
      </Button>
    </div>
  );
};

export default DestinationHeader;
