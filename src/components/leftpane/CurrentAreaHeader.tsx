import React from 'react';

interface CurrentAreaHeaderProps {
  areaName: string;
}

const CurrentAreaHeader: React.FC<CurrentAreaHeaderProps> = ({ areaName }) => {
  return (
    <div className="current-area-header" style={{ padding: '1rem', borderBottom: '1px solid #e0e0e0' }}>
      <h3 style={{ margin: 0, fontSize: '1.25rem', fontWeight: 'bold' }}>{areaName}</h3>
    </div>
  );
};

export default CurrentAreaHeader;
