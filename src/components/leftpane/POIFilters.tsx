import React from 'react';
import { FaLandmark, FaUtensils, FaBed, FaCoffee, FaStore, FaQuestionCircle } from 'react-icons/fa'; // Example icons

// Updated categories for Discovery/Exploration Focus
export const CATEGORIES = [
  { id: 'landmark', label: 'Historic Landmarks', icon: FaLandmark },
  { id: 'nature', label: 'Natural Wonders', icon: FaLandmark }, // Using FaLandmark as placeholder
  { id: 'cultural', label: 'Cultural Sites', icon: FaLandmark },
  { id: 'adventure', label: 'Adventure Activities', icon: FaLandmark },
  { id: 'scenic', label: 'Scenic Routes', icon: FaLandmark },
  { id: 'photography', label: 'Photo Spots', icon: FaLandmark },
  { id: 'hidden-gem', label: 'Hidden Gems', icon: FaLandmark },
  { id: 'local-experience', label: 'Local Experiences', icon: FaLandmark },
  { id: 'other', label: 'Other', icon: FaQuestionCircle },
];

interface POIFiltersProps {
  categories: Array<{ id: string; icon: React.ElementType; label: string }>;
  selectedCategories: string[];
  onCategoryChange: (categories: string[]) => void;
  // Future props: duration, cost filters
}

const POIFilters: React.FC<POIFiltersProps> = ({ categories, selectedCategories, onCategoryChange }) => {
  const toggleCategory = (categoryId: string) => {
    const newSelection = selectedCategories.includes(categoryId)
      ? selectedCategories.filter(c => c !== categoryId)
      : [...selectedCategories, categoryId];
    onCategoryChange(newSelection);
  };

  return (
    <div className="poi-filters" style={{ padding: '0.5rem 1rem', borderBottom: '1px solid #e0e0e0', display: 'flex', flexWrap: 'wrap', gap: '0.5rem' }}>
      {categories.map(category => (
        <button
          key={category.id}
          onClick={() => toggleCategory(category.id)}
          style={{
            padding: '0.25rem 0.75rem',
            borderRadius: '1rem',
            border: '1px solid #ccc',
            backgroundColor: selectedCategories.includes(category.id) ? '#007bff' : '#fff',
            color: selectedCategories.includes(category.id) ? '#fff' : '#333',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '0.25rem',
            fontSize: '0.8rem'
          }}
        >
          <category.icon />
          {category.label}
        </button>
      ))}
    </div>
  );
};

export default POIFilters;
