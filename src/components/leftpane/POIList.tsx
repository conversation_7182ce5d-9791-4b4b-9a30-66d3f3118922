import React from 'react';
import POICard from './POICard';
import { PointOfInterest } from '@/types'; // Assuming PointOfInterest type is available at this path

interface POIListProps {
  pois: PointOfInterest[];
  onPoiSelect: (poi: PointOfInterest) => void;
  onAddToItinerary: (poiId: string) => void;
  selectedItineraryPois: Set<string>;
}

const POIList: React.FC<POIListProps> = ({ pois, onPoiSelect, onAddToItinerary, selectedItineraryPois }) => {
  if (!pois || pois.length === 0) {
    return <p style={{ padding: '1rem', textAlign: 'center', color: '#777' }}>No points of interest found for the selected criteria.</p>;
  }

  return (
    <div className="poi-list" style={{ paddingTop: '0.5rem' }}>
      {pois.map(poi => (
        <POICard
          key={poi.id}
          poi={poi}
          onSelect={onPoiSelect}
          onAddToItinerary={onAddToItinerary}
          isInItinerary={selectedItineraryPois.has(poi.id)}
        />
      ))}
    </div>
  );
};

export default POIList;
