import React from 'react';
import { PointOfInterest } from '@/types'; // Assuming PointOfInterest type
import { FaMapMarkerAlt, FaPlus, FaCheck, FaUtensils, FaLandmark, FaBed, FaCoffee, FaStore, FaQuestionCircle } from 'react-icons/fa'; // Example icons

// Helper to get an icon based on category - this can be expanded or made more robust
const getCategoryIcon = (category?: string): React.ReactElement => {
  if (!category) return <FaQuestionCircle />;
  switch (category.toLowerCase()) {
    case 'landmark': return <FaLandmark />;
    case 'restaurant': return <FaUtensils />;
    case 'hotel': return <FaBed />;
    case 'cafe': return <FaCoffee />;
    case 'shop': return <FaStore />;
    default: return <FaQuestionCircle />;
  }
};

interface POICardProps {
  poi: PointOfInterest;
  onSelect: (poi: PointOfInterest) => void;
  onAddToItinerary: (poiId: string) => void;
  isInItinerary: boolean;
}

const POICard: React.FC<POICardProps> = ({ poi, onSelect, onAddToItinerary, isInItinerary }) => {
  const handleAddClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click event when clicking the button
    onAddToItinerary(poi.id);
  };

  const handleCardClick = () => {
    onSelect(poi);
  };

  const CategoryIcon = getCategoryIcon(poi.category);

  return (
    <div 
      onClick={handleCardClick}
      className="poi-card" 
      style={{ 
        padding: '0.75rem 1rem', 
        borderBottom: '1px solid #eee', 
        cursor: 'pointer', 
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        transition: 'background-color 0.2s ease',
      }}
      onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = '#f9f9f9')}
      onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = 'transparent')}
    >
      <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
        <span style={{ color: '#007bff' }}>{CategoryIcon}</span>
        <div>
          <h4 style={{ margin: 0, fontWeight: 'bold', fontSize: '0.9rem' }}>{poi.name}</h4>
          <p style={{ margin: 0, fontSize: '0.8rem', color: '#555' }}>{poi.category || 'Uncategorized'}</p>
        </div>
      </div>
      <button 
        onClick={handleAddClick}
        style={{
          padding: '0.3rem 0.6rem',
          borderRadius: '0.25rem',
          border: `1px solid ${isInItinerary ? '#28a745' : '#007bff'}`,
          backgroundColor: isInItinerary ? '#28a745' : '#fff',
          color: isInItinerary ? '#fff' : '#007bff',
          cursor: 'pointer',
          fontSize: '0.8rem',
          display: 'flex',
          alignItems: 'center',
          gap: '0.25rem'
        }}
      >
        {isInItinerary ? <FaCheck /> : <FaPlus />}
        {isInItinerary ? 'Added' : 'Add'}
      </button>
    </div>
  );
};

export default POICard;
