import React from 'react';
import { Button } from '@/components/ui/button';
import { Search } from 'lucide-react';
import { type Destination, type PointOfInterest, type POICategory } from '@/types/POITypes';
import { type Position, type PositionObject } from '@/types/Position';

interface SearchFiltersProps {
  showFilters: boolean;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  selectedTypes: string[];
  toggleType: (type: string) => void;
  filteredItems: (Destination | PointOfInterest)[];
  toggleItem: (item: Destination | PointOfInterest) => void;
  selectedItems: (Destination | PointOfInterest)[];
  poiTypes: POICategory[];
}

const SearchFilters: React.FC<SearchFiltersProps> = ({
  showFilters,
  searchTerm,
  setSearchTerm,
  selectedTypes,
  toggleType,
  filteredItems,
  toggleItem,
  selectedItems,
  poiTypes
}) => {
  return (
    <div className="flex flex-col gap-6 p-4 md:p-6">
      {/* Category Section */}
      <div>
        <h3 className="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Category</h3>
        <div className="flex flex-wrap gap-2">
          {poiTypes.map((type) => (
            <button
              key={type}
              onClick={() => toggleType(type)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                selectedTypes.includes(type)
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              {type.charAt(0).toUpperCase() + type.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* POI Cards Section */}
      <div className="flex flex-col gap-4">
        {filteredItems.map((item: any) => (
          <div
            key={item.id}
            className="rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-sm flex flex-col sm:flex-row items-stretch overflow-hidden"
            style={{ minHeight: 100 }}
          >
            {item.images && item.images[0] && (
              <img
                src={item.images[0]}
                alt={item.name}
                className="object-cover w-full sm:w-32 h-32 sm:h-auto max-h-40 sm:max-h-none"
                style={{ flexShrink: 0 }}
              />
            )}
            <div className="flex-1 flex flex-col p-3 gap-2">
              <div className="font-semibold text-base text-gray-900 dark:text-gray-100">{item.name}</div>
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">{item.description?.slice(0, 60)}{item.description?.length > 60 ? '...' : ''}</div>
              <div className="flex flex-row items-center gap-2 mt-auto">
                <span className="text-xs text-gray-400">{item.duration ? `${item.duration} min` : 'Quick visit'}</span>
                <button
                  className={`ml-auto px-3 py-1 rounded-full text-xs font-medium transition-colors ${selectedItems.some((i: any) => i.id === item.id)
                    ? 'bg-green-500 text-white' : 'bg-morocco-terracotta text-white'}`}
                  onClick={() => toggleItem(item)}
                >
                  {selectedItems.some((i: any) => i.id === item.id) ? 'Added' : 'Add to Journey'}
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Duration Section */}
      <div>
        <h3 className="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Duration</h3>
        <div className="space-y-3">
          <button className="w-full text-left px-4 py-3 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
            Quick visit
          </button>
          <button className="w-full text-left px-4 py-3 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
            Half day
          </button>
          <button className="w-full text-left px-4 py-3 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
            Full day
          </button>
        </div>
      </div>

      {/* Pricing Section */}
      <div>
        <h3 className="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Pricing</h3>
        <div className="space-y-3">
          <button className="w-full text-left px-4 py-3 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
            Budget
          </button>
          <button className="w-full text-left px-4 py-3 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
            Mid-range
          </button>
          <button className="w-full text-left px-4 py-3 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
            Luxury
          </button>
        </div>
      </div>

      {/* Sort by Section */}
      <div>
        <h3 className="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Sort by</h3>
        <div className="space-y-3">
          <button className="w-full text-left px-4 py-3 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
            Recommended
          </button>
          <button className="w-full text-left px-4 py-3 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
            Distance
          </button>
          <button className="w-full text-left px-4 py-3 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
            Rating
          </button>
        </div>
      </div>
    </div>
  );
};

export default SearchFilters;
