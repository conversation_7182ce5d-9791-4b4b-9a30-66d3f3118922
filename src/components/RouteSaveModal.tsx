
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { saveRoute } from '@/utils/supabaseHelpers';
import { Destination } from '@/data/destinations';
import { Vehicle } from '@/data/vehicles';
import { PointOfInterest } from '@/components/PointOfInterestCard';
import { Save } from 'lucide-react';

interface RouteSaveModalProps {
  selectedDestinations: Destination[];
  selectedVehicle: Vehicle | null;
  selectedPOIs: PointOfInterest[];
  routeDetails: {
    totalDistance: number;
    totalDuration: number;
    recommendedDays: number;
  } | null;
}

const RouteSaveModal = ({ selectedDestinations, selectedVehicle, selectedPOIs, routeDetails }: RouteSaveModalProps) => {
  const [open, setOpen] = useState(false);
  const [routeName, setRouteName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { user } = useAuth();
  const navigate = useNavigate();

  const handleSaveRoute = async () => {
    if (!user) {
      navigate('/auth');
      return;
    }

    if (!routeName.trim()) {
      return;
    }

    if (!routeDetails) {
      return;
    }

    setIsSubmitting(true);
    try {
      const { data, error } = await saveRoute(
        user.id,
        routeName,
        selectedDestinations,
        selectedVehicle,
        routeDetails.totalDistance,
        routeDetails.totalDuration,
        routeDetails.recommendedDays,
        selectedPOIs
      );

      if (error) throw error;

      console.log("Route saved successfully");
      setOpen(false);
      setRouteName('');
    } catch (error: any) {
      console.error('Error saving route:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="bg-white">
          <Save className="mr-2 h-4 w-4" /> Save Route
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Save Your Route</DialogTitle>
          <DialogDescription>
            Save your current route to access it later or request a quote.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="route-name" className="text-right">
              Route Name
            </Label>
            <Input
              id="route-name"
              value={routeName}
              onChange={(e) => setRouteName(e.target.value)}
              className="col-span-3"
              placeholder="My Morocco Adventure"
            />
          </div>
          {selectedDestinations.length > 0 && (
            <div className="grid grid-cols-4 items-start gap-4">
              <Label className="text-right">Destinations</Label>
              <div className="col-span-3">
                <ul className="text-sm text-muted-foreground">
                  {selectedDestinations.map((dest, index) => (
                    <li key={dest.id}>
                      {index + 1}. {dest.name}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}
        </div>
        <DialogFooter>
          <Button 
            onClick={handleSaveRoute} 
            disabled={isSubmitting || !routeName.trim()}
            className="bg-morocco-terracotta hover:bg-morocco-terracotta/90"
          >
            {isSubmitting ? 'Saving...' : 'Save Route'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RouteSaveModal;
