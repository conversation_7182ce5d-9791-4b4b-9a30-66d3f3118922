import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Facebook, Instagram, Twitter, Mail, Phone, MapPin } from 'lucide-react';

const Footer: React.FC = () => {
  return (
    <footer className="bg-gradient-to-r from-teal-800 to-teal-900 text-white py-8">
      <div className="container mx-auto px-4">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          {/* Company Info */}
          <div>
            <h3 className="text-xl font-bold mb-4">ComeToMorocco</h3>
            <p className="text-gray-300 mb-4">Your premier travel companion for exploring the beauty and culture of Morocco and beyond.</p>
            <div className="flex space-x-4">
              <a href="https://facebook.com" className="text-white hover:text-teal-300 transition-colors">
                <Facebook size={20} />
              </a>
              <a href="https://instagram.com" className="text-white hover:text-teal-300 transition-colors">
                <Instagram size={20} />
              </a>
              <a href="https://twitter.com" className="text-white hover:text-teal-300 transition-colors">
                <Twitter size={20} />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-gray-300 hover:text-white transition-colors">Home</Link>
              </li>
              <li>
                <Link to="/about" className="text-gray-300 hover:text-white transition-colors">About Us</Link>
              </li>
              <li>
                <Link to="/destinations" className="text-gray-300 hover:text-white transition-colors">Destinations</Link>
              </li>
              <li>
                <Link to="/client-demo" className="text-gray-300 hover:text-white transition-colors">Client Demo</Link>
              </li>
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Resources</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/payment-plans" className="text-gray-300 hover:text-white transition-colors">Payment Plans</Link>
              </li>
              <li>
                <Link to="/developers" className="text-gray-300 hover:text-white transition-colors">Developers</Link>
              </li>
              <li>
                <Link to="/faq" className="text-gray-300 hover:text-white transition-colors">FAQ</Link>
              </li>
              <li>
                <Link to="/privacy-policy" className="text-gray-300 hover:text-white transition-colors">Privacy Policy</Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Contact Us</h3>
            <ul className="space-y-3">
              <li className="flex items-center">
                <MapPin size={18} className="mr-2 text-teal-400" />
                <span className="text-gray-300">123 Travel Street, Marrakech, Morocco</span>
              </li>
              <li className="flex items-center">
                <Phone size={18} className="mr-2 text-teal-400" />
                <span className="text-gray-300">+************</span>
              </li>
              <li className="flex items-center">
                <Mail size={18} className="mr-2 text-teal-400" />
                <Link to="/contact" className="text-gray-300 hover:text-white transition-colors"><EMAIL></Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="pt-6 border-t border-teal-700 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-gray-400 mb-4 md:mb-0">© {new Date().getFullYear()} ComeToMorocco. All rights reserved.</p>
          <div className="flex space-x-6">
            <Link to="/terms" className="text-sm text-gray-400 hover:text-white transition-colors">Terms of Service</Link>
            <Link to="/privacy-policy" className="text-sm text-gray-400 hover:text-white transition-colors">Privacy Policy</Link>
            <Link to="/cookies" className="text-sm text-gray-400 hover:text-white transition-colors">Cookie Policy</Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;