import React, { useState } from 'react';
import type { PointOfInterest } from '@/types';
import { MapPin, Clock, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface PointOfInterestCardProps {
  poi: PointOfInterest;
  onAdd: () => void;
  isAdded?: boolean;
}

const PointOfInterestCard: React.FC<PointOfInterestCardProps> = ({ 
  poi, 
  onAdd,
  isAdded = false 
}) => {
  const getTypeColor = (type?: string) => {
    switch (type) {
      case 'accommodation':
        return 'bg-blue-100 text-blue-800';
      case 'activity':
        return 'bg-green-100 text-green-800';
      case 'restaurant':
        return 'bg-yellow-100 text-yellow-800';
      case 'landmark':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCost = (cost?: number) => {
    if (cost === undefined || cost === 0) return 'Free';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(cost);
  };
  
  const formatDuration = (hours?: number) => {
    if (!hours) return 'Flexible';
    if (hours < 1) return `${Math.round(hours * 60)} min`;
    return `${hours} hr${hours !== 1 ? 's' : ''}`;
  };

  return (
    <div className="bg-white rounded-lg shadow hover:shadow-md transition-all duration-300 overflow-hidden animate-fade-in">
      <div className="relative h-40">
        <img 
          src={poi.images?.[0] || ''}
          alt={poi.name || 'Point of Interest'} 
          className="w-full h-full object-cover"
        />
        <div className="absolute top-2 right-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(poi.category)}`}>
            {poi.category}
          </span>
        </div>
      </div>
      <div className="p-4">
        <h3 className="text-lg font-semibold line-clamp-1">{poi.name}</h3>
        
        <div className="flex items-center text-sm text-gray-500 mt-1">
          <MapPin className="w-3 h-3 mr-1" />
          <span className="line-clamp-1">{poi.location || poi.name}</span>
        </div>
        
        <div className="flex justify-between mt-2">
          <div className="flex items-center text-sm text-gray-500">
            <Clock className="w-3 h-3 mr-1" />
            <span>{formatDuration(poi.duration)}</span>
          </div>
          <div className="text-sm font-medium text-morocco-terracotta">
            {formatCost(poi.cost)}
          </div>
        </div>
        
        <p className="text-sm text-gray-600 mt-2 line-clamp-2">
          {poi.description}
        </p>
        
        <div className="flex flex-wrap gap-1 mt-3">
          {poi.tags?.slice(0, 3).map((tag: string, index: number) => (
            <span 
              key={index}
              className="px-2 py-0.5 bg-gray-100 rounded-full text-xs text-gray-600"
            >
              {tag}
            </span>
          ))}
          {poi.tags && poi.tags.length > 3 && (
            <span className="px-2 py-0.5 bg-gray-100 rounded-full text-xs text-gray-600">
              +{poi.tags.length - 3}
            </span>
          )}
        </div>
        
        <Button
          onClick={onAdd}
          disabled={isAdded}
          className={`w-full mt-4 ${
            isAdded 
              ? 'bg-green-500 hover:bg-green-600' 
              : 'bg-morocco-terracotta hover:bg-morocco-terracotta/90'
          }`}
          size="sm"
        >
          {isAdded ? 'Added to Itinerary' : (
            <span className="flex items-center">
              <Plus className="w-4 h-4 mr-1" />
              Add to Itinerary
            </span>
          )}
        </Button>
      </div>
    </div>
  );
};

export default PointOfInterestCard;
