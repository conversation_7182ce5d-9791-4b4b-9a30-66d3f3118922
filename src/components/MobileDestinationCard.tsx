import React from 'react';
import type { Destination } from '@/types/POITypes';
import { Map, Clock, Tag } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { formatDuration } from '@/utils/routeUtils';

interface MobileDestinationCardProps {
  destination: Destination;
  isSelected: boolean;
  onSelect: () => void;
  onRemove: () => void;
}

const MobileDestinationCard: React.FC<MobileDestinationCardProps> = ({
  destination,
  isSelected,
  onSelect,
  onRemove,
}) => {
  return (
    <div 
      className={`mobile-card animate-fade-in stagger-item ${
        isSelected ? 'border-morocco-terracotta border-2' : 'border border-gray-200'
      }`}
    >
      <div className="flex flex-row">
        <div className="w-24 h-24 rounded-md overflow-hidden mr-3 flex-shrink-0">
          <img 
            src={destination.image} 
            alt={destination.name} 
            className="w-full h-full object-cover"
          />
        </div>
        <div className="flex-grow">
          <h4 className="font-bold text-sm">{destination.name}</h4>
          <div className="flex items-center text-xs text-gray-500 mt-1">
            <Map className="w-3 h-3 mr-1" />
            <span>{destination.type}</span>
          </div>
          <div className="flex items-center text-xs text-gray-500 mt-1">
            <Clock className="w-3 h-3 mr-1" />
            <span>{formatDuration(destination.suggestedDuration ?? 0)}</span>
          </div>
          <div className="flex items-center text-xs text-gray-500 mt-1">
            <Tag className="w-3 h-3 mr-1" />
            <span>{destination.type}</span>
          </div>
          <div className="mt-2">
            {isSelected ? (
              <Button 
                variant="outline" 
                size="sm" 
                className="text-xs h-7 bg-morocco-terracotta/10 text-morocco-terracotta border-morocco-terracotta"
                onClick={onRemove}
              >
                Remove
              </Button>
            ) : (
              <Button 
                variant="outline" 
                size="sm" 
                className="text-xs h-7 hover:bg-morocco-terracotta/10 hover:text-morocco-terracotta hover:border-morocco-terracotta"
                onClick={onSelect}
              >
                Add to Route
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileDestinationCard;
