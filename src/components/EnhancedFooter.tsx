import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Facebook, Instagram, Twitter, Mail, Phone, MapPin, Send } from 'lucide-react';

const EnhancedFooter: React.FC = () => {
  const [email, setEmail] = useState('');

  const handleSubscribe = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle newsletter subscription
    console.log('Subscribing email:', email);
    alert(`Thank you for subscribing with ${email}!`);
    setEmail('');
  };

  return (
    <footer className="bg-gradient-to-r from-teal-800 to-teal-900 bg-[url('/subtle-pattern.png')] bg-blend-overlay text-white py-10">
      <div className="container mx-auto px-4">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          {/* Company Info */}
          <div>
            <h3 className="text-xl font-bold mb-4 relative pl-3 before:content-[''] before:absolute before:left-0 before:top-0 before:h-full before:w-1 before:bg-teal-400 before:rounded">
              ComeToMorocco
            </h3>
            <p className="text-gray-300 mb-4">Your premier travel companion for exploring the beauty and culture of Morocco and beyond.</p>
            <div className="flex space-x-3">
              <a href="https://facebook.com" className="text-white hover:text-teal-300 bg-teal-700/30 p-2 rounded-full hover:bg-teal-700/50 transition-all duration-300 flex items-center justify-center">
                <Facebook size={18} />
              </a>
              <a href="https://instagram.com" className="text-white hover:text-teal-300 bg-teal-700/30 p-2 rounded-full hover:bg-teal-700/50 transition-all duration-300 flex items-center justify-center">
                <Instagram size={18} />
              </a>
              <a href="https://twitter.com" className="text-white hover:text-teal-300 bg-teal-700/30 p-2 rounded-full hover:bg-teal-700/50 transition-all duration-300 flex items-center justify-center">
                <Twitter size={18} />
              </a>
            </div>

            {/* Newsletter Subscription */}
            <div className="mt-6">
              <h4 className="text-sm font-semibold mb-2 text-teal-200">Subscribe to our newsletter</h4>
              <form onSubmit={handleSubscribe} className="flex">
                <input 
                  type="email" 
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Your email address" 
                  required
                  className="px-3 py-2 bg-teal-700/30 text-white placeholder:text-teal-200/50 rounded-l-md focus:outline-none focus:ring-1 focus:ring-teal-400 w-full max-w-xs"
                />
                <button 
                  type="submit"
                  className="bg-teal-500 hover:bg-teal-400 text-teal-900 font-medium px-3 rounded-r-md transition-colors flex items-center"
                >
                  <Send size={16} />
                </button>
              </form>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4 relative pl-3 before:content-[''] before:absolute before:left-0 before:top-0 before:h-full before:w-1 before:bg-teal-400 before:rounded">
              Quick Links
            </h3>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-gray-300 hover:text-white transition-colors flex items-center hover:translate-x-1 duration-200">
                  <span className="w-1.5 h-1.5 bg-teal-400 rounded-full mr-2"></span>
                  Home
                </Link>
              </li>
              <li>
                <Link to="/about" className="text-gray-300 hover:text-white transition-colors flex items-center hover:translate-x-1 duration-200">
                  <span className="w-1.5 h-1.5 bg-teal-400 rounded-full mr-2"></span>
                  About Us
                </Link>
              </li>
              <li>
                <Link to="/destinations" className="text-gray-300 hover:text-white transition-colors flex items-center hover:translate-x-1 duration-200">
                  <span className="w-1.5 h-1.5 bg-teal-400 rounded-full mr-2"></span>
                  Destinations
                </Link>
              </li>
              <li>
                <Link to="/client-demo" className="text-gray-300 hover:text-white transition-colors flex items-center hover:translate-x-1 duration-200">
                  <span className="w-1.5 h-1.5 bg-teal-400 rounded-full mr-2"></span>
                  Client Demo
                </Link>
              </li>
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h3 className="text-lg font-semibold mb-4 relative pl-3 before:content-[''] before:absolute before:left-0 before:top-0 before:h-full before:w-1 before:bg-teal-400 before:rounded">
              Resources
            </h3>
            <ul className="space-y-2">
              <li>
                <Link to="/payment-plans" className="text-gray-300 hover:text-white transition-colors flex items-center hover:translate-x-1 duration-200">
                  <span className="w-1.5 h-1.5 bg-teal-400 rounded-full mr-2"></span>
                  Payment Plans
                </Link>
              </li>
              <li>
                <Link to="/developers" className="text-gray-300 hover:text-white transition-colors flex items-center hover:translate-x-1 duration-200">
                  <span className="w-1.5 h-1.5 bg-teal-400 rounded-full mr-2"></span>
                  Developers
                </Link>
              </li>
              <li>
                <Link to="/faq" className="text-gray-300 hover:text-white transition-colors flex items-center hover:translate-x-1 duration-200">
                  <span className="w-1.5 h-1.5 bg-teal-400 rounded-full mr-2"></span>
                  FAQ
                </Link>
              </li>
              <li>
                <Link to="/privacy-policy" className="text-gray-300 hover:text-white transition-colors flex items-center hover:translate-x-1 duration-200">
                  <span className="w-1.5 h-1.5 bg-teal-400 rounded-full mr-2"></span>
                  Privacy Policy
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4 relative pl-3 before:content-[''] before:absolute before:left-0 before:top-0 before:h-full before:w-1 before:bg-teal-400 before:rounded">
              Contact Us
            </h3>
            <ul className="space-y-3">
              <li className="flex items-center group hover:bg-teal-800/30 p-2 rounded-md transition-colors">
                <MapPin size={18} className="mr-2 text-teal-400 group-hover:scale-110 transition-transform" />
                <span className="text-gray-300 group-hover:text-white transition-colors">123 Travel Street, Marrakech, Morocco</span>
              </li>
              <li className="flex items-center group hover:bg-teal-800/30 p-2 rounded-md transition-colors">
                <Phone size={18} className="mr-2 text-teal-400 group-hover:scale-110 transition-transform" />
                <span className="text-gray-300 group-hover:text-white transition-colors">+************</span>
              </li>
              <li className="flex items-center group hover:bg-teal-800/30 p-2 rounded-md transition-colors">
                <Mail size={18} className="mr-2 text-teal-400 group-hover:scale-110 transition-transform" />
                <Link to="/contact" className="text-gray-300 group-hover:text-white transition-colors"><EMAIL></Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="pt-6 mt-6 border-t border-teal-700/50 flex flex-col md:flex-row justify-between items-center bg-teal-900/30 p-4 rounded-lg">
          <p className="text-sm text-gray-400 mb-4 md:mb-0">© {new Date().getFullYear()} ComeToMorocco. All rights reserved.</p>
          <div className="flex space-x-6">
            <Link to="/terms" className="text-sm text-gray-400 hover:text-white transition-colors hover:underline">
              Terms of Service
            </Link>
            <Link to="/privacy-policy" className="text-sm text-gray-400 hover:text-white transition-colors hover:underline">
              Privacy Policy
            </Link>
            <Link to="/cookies" className="text-sm text-gray-400 hover:text-white transition-colors hover:underline">
              Cookie Policy
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default EnhancedFooter;