/**
 * UniversalFramework.tsx
 * 
 * Central framework component that contains all UX logic and functionality.
 * This is the "skeleton" that all themes inherit from.
 * All UX improvements and backend logic updates should be made here.
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { ClientProvider } from '@/contexts/ClientContext';
import { ThemeProvider } from '../../providers/ThemeProvider';
import NeutralLayout from '../layout/NeutralLayout';
import MapComponent from '../map/MapComponent';
import { Destination, PointOfInterest } from '../../types';

interface UniversalFrameworkProps {
  // Theme configuration
  clientId: string;
  
  // Data configuration
  destinations: Destination[];
  pointsOfInterest: PointOfInterest[];
  
  // Regional settings
  regionName: string;
  availableRegions?: string[];
  mapCenter?: [number, number];
  mapBounds?: [[number, number], [number, number]];
  
  // Optional customizations
  customTitle?: string;
  customDescription?: string;
  
  // Additional theme-specific CSS classes
  className?: string;
}

const UniversalFramework: React.FC<UniversalFrameworkProps> = ({
  clientId,
  destinations,
  pointsOfInterest,
  regionName,
  availableRegions = [regionName],
  mapCenter,
  mapBounds,
  customTitle,
  customDescription,
  className = ''
}) => {
  // Map reference
  const mapRef = useRef<any>(null);
  
  // Journey state
  const [isAnimating, setIsAnimating] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [journeyProgress, setJourneyProgress] = useState(0);
  const [currentRegion, setCurrentRegion] = useState(regionName);

  // Selection state
  const [selectedCities, setSelectedCities] = useState<Destination[]>([]);
  const [selectedPOIs, setSelectedPOIs] = useState<PointOfInterest[]>([]);

  // UI state
  const [isMobile, setIsMobile] = useState(false);

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      const isMobileView = window.innerWidth < 768;
      setIsMobile(isMobileView);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Handle region change
  const handleRegionChange = useCallback((region: string) => {
    setCurrentRegion(region);
    console.log(`Changed region to ${region}`);
  }, []);

  // Handle city selection
  const handleCitySelect = useCallback((city: Destination) => {
    setSelectedCities(prev => {
      const exists = prev.some(c => c.id === city.id);
      if (exists) {
        return prev.filter(c => c.id !== city.id);
      } else {
        return [...prev, city];
      }
    });
  }, []);

  // Handle city deselection
  const handleCityDeselect = useCallback((city: Destination) => {
    setSelectedCities(prev => prev.filter(c => c.id !== city.id));
  }, []);

  // Handle POI selection
  const handlePOISelect = useCallback((poi: PointOfInterest) => {
    // Zoom to POI location on map
    if (mapRef.current && poi.coordinates) {
      mapRef.current.flyTo({
        center: poi.coordinates,
        zoom: 15,
        duration: 1500,
        essential: true
      });
    }
  }, []);

  // Handle adding POI to trip
  const handleAddPOI = useCallback((poi: PointOfInterest) => {
    setSelectedPOIs(current => {
      const isSelected = current.some(p => p.id === poi.id);
      if (isSelected) {
        return current.filter(p => p.id !== poi.id);
      } else {
        return [...current, poi];
      }
    });
  }, []);

  // Handle journey start
  const handleStartJourney = useCallback(() => {
    console.log('🚀 Begin Journey clicked!', {
      cities: selectedCities.length,
      pois: selectedPOIs.length
    });

    if (selectedCities.length < 2) {
      console.warn('Cannot start journey: Need at least 2 cities selected');
      return;
    }

    setIsAnimating(true);
    setJourneyProgress(0);
    
    // Simulate journey progress
    const progressInterval = setInterval(() => {
      setJourneyProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          setIsAnimating(false);
          return 100;
        }
        return prev + 1;
      });
    }, 100);
  }, [selectedCities, selectedPOIs]);

  // Handle journey pause
  const handlePauseJourney = useCallback(() => {
    setIsPaused(!isPaused);
  }, [isPaused]);

  // Journey readiness check
  const canBeginJourney = selectedCities.length >= 2 && selectedPOIs.length >= 2;
  const showBeginJourneyButton = canBeginJourney;

  return (
    <ClientProvider initialClientId={clientId}>
      <ThemeProvider>
        <div className={`universal-framework theme-${clientId} h-screen ${className}`}>
          <NeutralLayout
            currentRegion={currentRegion}
            availableRegions={availableRegions}
            onRegionChange={handleRegionChange}
            isAnimating={isAnimating}
            isPaused={isPaused}
            journeyProgress={journeyProgress}
            onStartJourney={handleStartJourney}
            onPauseJourney={handlePauseJourney}
            itineraryPOIs={selectedPOIs}
            availableCities={destinations}
            selectedCities={selectedCities}
            onCitySelect={handleCitySelect}
            onCityDeselect={handleCityDeselect}
            allPOIs={pointsOfInterest}
            onSelectPOI={handlePOISelect}
            onAddPOI={handleAddPOI}
            canBeginJourney={canBeginJourney}
            showBeginJourneyButton={showBeginJourneyButton}
            onBeginJourney={handleStartJourney}
          >
            <MapComponent
              ref={mapRef}
              destinations={destinations}
              pointsOfInterest={pointsOfInterest}
              selectedCities={selectedCities}
              selectedPOIs={selectedPOIs}
              onCitySelect={handleCitySelect}
              onPOISelect={handlePOISelect}
              isAnimating={isAnimating}
              journeyProgress={journeyProgress}
              mapCenter={mapCenter}
              mapBounds={mapBounds}
            />
          </NeutralLayout>
        </div>
      </ThemeProvider>
    </ClientProvider>
  );
};

export default UniversalFramework;
