import React from 'react';
import { Check } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';
import { Vehicle } from '@/data/vehicles';

interface VehicleSelectorProps {
  vehicles: Vehicle[];
  selectedVehicleId: string | null;
  onVehicleSelect: (vehicleId: string) => void;
  isVisible?: boolean;
}

const VehicleSelector: React.FC<VehicleSelectorProps> = ({
  vehicles,
  selectedVehicleId,
  onVehicleSelect,
  isVisible = true
}) => {
  const isMobile = useIsMobile();
  
  // Don't render if not visible
  if (!isVisible) {
    return null;
  }
  
  return (
    <div className="vehicle-selector w-full bg-white p-4 rounded-lg shadow-sm border border-gray-100">
      <h2 className="text-xl font-semibold text-gray-800 mb-4">Select Your Vehicle</h2>
      
      <div className={cn(
        "grid gap-4",
        isMobile ? "grid-cols-1" : "grid-cols-2 lg:grid-cols-3"
      )}>
        {vehicles.map((vehicle) => (
          <div
            key={vehicle.id}
            className={cn(
              "border rounded-lg p-4 cursor-pointer transition-all",
              selectedVehicleId === vehicle.id
                ? "border-morocco-terracotta bg-morocco-terracotta/5"
                : "border-gray-200 hover:border-morocco-terracotta/50 hover:bg-morocco-terracotta/5"
            )}
            onClick={() => onVehicleSelect(vehicle.id)}
          >
            <div className="flex items-start justify-between mb-2">
              <div>
                <h3 className="font-medium text-gray-900">{vehicle.name}</h3>
                <p className="text-sm text-gray-600">{vehicle.type}</p>
              </div>
              
              {selectedVehicleId === vehicle.id && (
                <div className="w-6 h-6 rounded-full bg-morocco-terracotta flex items-center justify-center">
                  <Check className="h-4 w-4 text-white" />
                </div>
              )}
            </div>
            
            {vehicle.image && (
              <div className="h-32 rounded-md overflow-hidden mb-3">
                <img
                  src={vehicle.image}
                  alt={vehicle.name}
                  className="w-full h-full object-cover"
                />
              </div>
            )}
            
            <div className="flex items-center justify-between">
              <span className="text-sm bg-gray-100 px-2 py-1 rounded-full">
                Up to {vehicle.capacity} people
              </span>
              
              <span className="text-sm text-gray-600">
                {vehicle.features.length} features
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default VehicleSelector;
