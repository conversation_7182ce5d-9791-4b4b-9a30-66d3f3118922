import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { Destination } from '@/data/destinations';
import { PointOfInterest } from '@/components/PointOfInterestCard';
import { Vehicle } from '@/data/vehicles';
import { vehicles } from '@/data/vehicles';
import { ArrowRight, CalendarRange, Clock, Route, ChevronRight, MapPin, ArrowRightIcon, CarFrontIcon } from 'lucide-react';
import { formatDuration, calculateRoute } from '@/utils/routeUtils';
import VehicleSelector from '@/components/VehicleSelector';
import QuoteFormModal from '@/components/QuoteFormModal';
import { useAuth } from '@/contexts/AuthContext';
import RouteSaveModal from '@/components/RouteSaveModal';

interface DestinationWithDays extends Destination {
  daysToSpend?: number;
}

interface JourneyFooterProps {
  selectedDestinations: DestinationWithDays[];
  selectedPOIs: PointOfInterest[];
  onProceed: () => void;
}

const JourneyFooter: React.FC<JourneyFooterProps> = ({
  selectedDestinations,
  selectedPOIs,
  onProceed
}) => {
  const [currentStep, setCurrentStep] = useState<'destinations' | 'vehicle'>('destinations');
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null);
  const [quoteModalOpen, setQuoteModalOpen] = useState(false);
  const { user } = useAuth();

  // Calculate route details
  const routeDetails = selectedDestinations.length >= 2
    ? calculateRoute(selectedDestinations, selectedVehicle || vehicles[0])
    : null;

  const handleVehicleSelect = (vehicle: Vehicle) => {
    setSelectedVehicle(vehicle);
    console.log(`Vehicle selected: ${vehicle.name}`);
  };

  const handleNextStep = () => {
    if (currentStep === 'destinations') {
      setCurrentStep('vehicle');
    } else {
      setQuoteModalOpen(true);
    }
  };

  const handleQuoteSubmit = () => {
    setQuoteModalOpen(false);
  };
  
  // Listen for showQuoteModal event
  React.useEffect(() => {
    const handleShowQuoteModal = (e: Event) => {
      const customEvent = e as CustomEvent;
      if (customEvent.detail && customEvent.detail.vehicleId) {
        // Find the selected vehicle
        const vehicle = vehicles.find(v => v.id === customEvent.detail.vehicleId);
        if (vehicle) {
          setSelectedVehicle(vehicle);
          // Show the quote modal
          setTimeout(() => {
            setQuoteModalOpen(true);
          }, 300);
        }
      }
    };
    
    window.addEventListener('showQuoteModal', handleShowQuoteModal);
    
    return () => {
      window.removeEventListener('showQuoteModal', handleShowQuoteModal);
    };
  }, []);

  // Calculate the total kilometers if there are at least 2 destinations
  const totalKm = selectedDestinations.length >= 2 
    ? Math.floor(Math.random() * 1000) + 500 // Placeholder for actual calculation
    : 0;
  
  // Calculate the total days for the journey
  const totalDays = selectedDestinations.reduce((total, dest) => 
    total + (dest.daysToSpend || 1), 0);
  
  // Calculate the recommended days (slightly more than total days)
  const recommendedDays = Math.ceil(totalDays * 1.2);

  // Only show footer if destinations are selected
  if (selectedDestinations.length === 0) {
    return null;
  }

  return (
    <>
      <div className="journey-footer fixed bottom-0 left-0 right-0 bg-white shadow-md border-t border-gray-200 p-4 flex items-center justify-between">
        <div className="journey-stats flex items-center space-x-4">
          <div className="stat flex flex-col">
            <span className="text-xs text-gray-500">Destinations</span>
            <span className="font-semibold text-lg">{selectedDestinations.length}</span>
          </div>
          <div className="stat flex flex-col">
            <span className="text-xs text-gray-500">Activities</span>
            <span className="font-semibold text-lg">{selectedPOIs.length}</span>
          </div>
        </div>
        
        <Button 
          onClick={onProceed}
          className="bg-morocco-terracotta hover:bg-morocco-terracotta/90 text-white"
        >
          <CarFrontIcon className="w-4 h-4 mr-2" />
          Proceed to Vehicle Selection
          <ArrowRightIcon className="w-4 h-4 ml-2" />
        </Button>
      </div>

      {/* Quote Form Modal */}
      <QuoteFormModal
        open={quoteModalOpen}
        onOpenChange={setQuoteModalOpen}
        selectedDestinations={selectedDestinations}
        selectedVehicle={selectedVehicle}
        selectedPOIs={selectedPOIs}
        routeDetails={{
          totalDistance: routeDetails?.totalDistance || 0,
          totalDuration: routeDetails?.totalDuration || 0,
          recommendedDays: recommendedDays
        }}
        onSubmit={handleQuoteSubmit}
      />
    </>
  );
};

export default JourneyFooter;
