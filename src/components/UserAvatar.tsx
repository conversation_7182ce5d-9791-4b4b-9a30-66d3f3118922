
import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { UserCircle, MapPin, MessageSquare, LogOut, ShieldAlert } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

export const UserAvatar = () => {
  const { user, signOut, isAdmin } = useAuth();
  const navigate = useNavigate();
  
  if (!user) return null;
  
  // Get initials from user's email or full name if available
  const getInitials = () => {
    const fullName = user.user_metadata?.full_name;
    if (fullName) {
      return fullName
        .split(' ')
        .map(name => name[0])
        .join('')
        .toUpperCase()
        .substring(0, 2);
    }
    return user.email?.substring(0, 2).toUpperCase() || 'U';
  };

  // For testing only - grant admin rights to the current user
  const makeAdmin = async () => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ is_admin: true })
        .eq('id', user.id);
      
      if (error) throw error;
      
      console.log("Admin rights granted. Please log out and log back in for the changes to take effect.");
    } catch (error: any) {
      console.error("Failed to grant admin rights:", error.message);
    }
  };
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Avatar className="cursor-pointer border-2 border-morocco-terracotta hover:opacity-90 transition-opacity">
          <AvatarImage src={user.user_metadata?.avatar_url} />
          <AvatarFallback className="bg-morocco-terracotta text-white">
            {getInitials()}
          </AvatarFallback>
        </Avatar>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>My Account</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => navigate('/profile')} className="cursor-pointer">
          <UserCircle className="h-4 w-4 mr-2" />
          Profile
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => navigate('/my-routes')} className="cursor-pointer">
          <MapPin className="h-4 w-4 mr-2" />
          My Routes
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => navigate('/my-quotes')} className="cursor-pointer">
          <MessageSquare className="h-4 w-4 mr-2" />
          Quote Requests
        </DropdownMenuItem>
        
        {isAdmin && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => navigate('/admin')} className="cursor-pointer">
              <ShieldAlert className="h-4 w-4 mr-2" />
              Admin Dashboard
            </DropdownMenuItem>
          </>
        )}
        
        {/* For development purposes only - allow self-promotion to admin */}
        {!isAdmin && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={makeAdmin} className="cursor-pointer text-amber-600">
              <ShieldAlert className="h-4 w-4 mr-2" />
              Make Admin (Dev Only)
            </DropdownMenuItem>
          </>
        )}
        
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => signOut()} className="cursor-pointer">
          <LogOut className="h-4 w-4 mr-2" />
          Sign out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
