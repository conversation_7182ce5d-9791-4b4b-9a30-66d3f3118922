import React from 'react';
import { Destination } from '@/data/destinations';
import { PointOfInterest } from '@/components/PointOfInterestCard';
import MapComponent from '@/components/map/MapComponent';

interface MapSectionProps {
  destinations: Destination[];
  selectedDestinations: Destination[];
  onSelectDestination: (destination: Destination) => void;
  routePOIs: PointOfInterest[];
  selectedPOIs: PointOfInterest[];
  onSelectPoi?: (poi: PointOfInterest) => void;
  currentStep: 'destinations' | 'vehicle';
}

// Wrap the component in React.memo to prevent unnecessary re-renders
const MapSection: React.FC<MapSectionProps> = React.memo(({
  destinations,
  selectedDestinations,
  onSelectDestination,
  routePOIs = [],
  selectedPOIs = [],
  onSelectPoi,
  currentStep
}) => {
  // Only allow selection in the destinations step
  const handleSelectDestination = React.useCallback((destination: Destination) => {
    if (currentStep === 'destinations') {
      onSelectDestination(destination);
    }
  }, [currentStep, onSelectDestination]);

  // Handle POI selection - optional
  const handleSelectPoi = React.useCallback((poi: PointOfInterest) => {
    if (onSelectPoi) {
      onSelectPoi(poi);
    }
  }, [onSelectPoi]);

  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <h2 className="font-bold text-lg mb-4">Morocco Map</h2>
      <div className="h-[500px]">
        <MapComponent 
          destinations={destinations}
          selectedDestinations={selectedDestinations}
          onSelectDestination={handleSelectDestination}
          routePOIs={routePOIs}
          selectedPOIs={selectedPOIs}
          onSelectPoi={handleSelectPoi}
        />
      </div>
    </div>
  );
}, (prevProps, nextProps) => {
  // Only re-render if these specific props change
  return (
    prevProps.currentStep === nextProps.currentStep &&
    prevProps.destinations === nextProps.destinations &&
    JSON.stringify(prevProps.selectedDestinations.map(d => d.id)) === 
      JSON.stringify(nextProps.selectedDestinations.map(d => d.id)) &&
    JSON.stringify(prevProps.selectedPOIs.map(p => p.id)) === 
      JSON.stringify(nextProps.selectedPOIs.map(p => p.id)) &&
    JSON.stringify(prevProps.routePOIs?.map(p => p.id) || []) ===
      JSON.stringify(nextProps.routePOIs?.map(p => p.id) || [])
  );
});

// Set display name for debugging
MapSection.displayName = 'MapSection';

export default MapSection;
