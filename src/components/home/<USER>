
import React from 'react';
import { Destination } from '@/data/destinations';
import DestinationCard from '@/components/DestinationCard';

interface DestinationsListProps {
  destinations: Destination[];
  selectedDestinations: Destination[];
  onSelectDestination: (destination: Destination) => void;
}

const DestinationsList: React.FC<DestinationsListProps> = ({
  destinations,
  selectedDestinations,
  onSelectDestination
}) => {
  return (
    <div className="mt-6 bg-white rounded-lg shadow-md p-4">
      <h2 className="font-bold text-lg mb-4">Popular Destinations</h2>
      <div className="grid grid-cols-1 gap-4">
        {destinations.map(destination => (
          <DestinationCard
            key={destination.id}
            destination={destination}
            isSelected={selectedDestinations.some(d => d.id === destination.id)}
            onSelect={() => onSelectDestination(destination)}
            displayStyle="list"
          />
        ))}
      </div>
    </div>
  );
};

export default DestinationsList;
