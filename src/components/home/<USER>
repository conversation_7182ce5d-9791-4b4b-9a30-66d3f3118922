
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Destination } from '@/data/destinations';
import { Vehicle } from '@/data/vehicles';
import { PointOfInterest } from '@/components/PointOfInterestCard';
import { ArrowRight, CalendarRange, Clock, Route, MapPin, Save } from 'lucide-react';
import { formatDuration } from '@/utils/routeUtils';
import { useAuth } from '@/contexts/AuthContext';
import RouteSaveModal from '@/components/RouteSaveModal';
import QuoteFormModal from '@/components/QuoteFormModal';

interface RouteFooterProps {
  routeDetails: {
    totalDistance: number;
    totalDuration: number;
    recommendedDays: number;
  } | null;
  selectedDestinations: Destination[];
  selectedVehicle: Vehicle | null;
  selectedPOIs: PointOfInterest[];
  currentStep: 'destinations' | 'vehicle';
}

const RouteFooter: React.FC<RouteFooterProps> = ({
  routeDetails,
  selectedDestinations,
  selectedVehicle,
  selectedPOIs,
  currentStep
}) => {
  const [quoteModalOpen, setQuoteModalOpen] = useState(false);
  const { user } = useAuth();

  const handleQuoteSubmit = () => {
    setQuoteModalOpen(false);
  };

  if (!routeDetails || selectedDestinations.length < 2) {
    return null;
  }

  return (
    <>
      <div className="fixed bottom-0 left-0 right-0 bg-morocco-dark text-white py-3 px-4 shadow-lg z-30">
        <div className="container mx-auto">
          <div className="flex flex-wrap justify-around md:justify-between items-center">
            <div className="flex items-center md:mr-4 mb-2 md:mb-0">
              <Route className="h-5 w-5 mr-2" />
              <span className="font-medium">{routeDetails.totalDistance} km</span>
            </div>
            
            <div className="flex items-center md:mr-4 mb-2 md:mb-0">
              <Clock className="h-5 w-5 mr-2" />
              <span className="font-medium">{formatDuration(routeDetails.totalDuration)}</span>
            </div>
            
            <div className="flex items-center md:mr-4 mb-2 md:mb-0">
              <CalendarRange className="h-5 w-5 mr-2" />
              <span className="font-medium">
                {routeDetails.recommendedDays} days recommended
              </span>
            </div>
            
            <div className="flex space-x-2">
              {user && (
                <RouteSaveModal 
                  selectedDestinations={selectedDestinations}
                  selectedVehicle={selectedVehicle}
                  selectedPOIs={selectedPOIs}
                  routeDetails={routeDetails}
                />
              )}
              
              {currentStep === 'vehicle' && selectedVehicle && (
                <Button 
                  className="bg-morocco-terracotta hover:bg-morocco-terracotta/90 text-white"
                  onClick={() => setQuoteModalOpen(true)}
                >
                  Request Quote <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
      
      <QuoteFormModal
        open={quoteModalOpen}
        onOpenChange={setQuoteModalOpen}
        selectedDestinations={selectedDestinations}
        selectedVehicle={selectedVehicle}
        selectedPOIs={selectedPOIs}
        routeDetails={routeDetails}
        onSubmit={handleQuoteSubmit}
      />
    </>
  );
};

export default RouteFooter;
