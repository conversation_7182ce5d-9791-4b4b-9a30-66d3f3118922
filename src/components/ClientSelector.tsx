/**
 * ClientSelector.tsx
 * 
 * Component for selecting the current client
 */

import React from 'react';
import { useClient } from '../contexts/ClientContext';
import { getAvailableClientIds } from '../config/clientConfigs';

/**
 * Props for the ClientSelector component
 */
interface ClientSelectorProps {
  className?: string;
}

/**
 * Component for selecting the current client
 */
const ClientSelector: React.FC<ClientSelectorProps> = ({ className = '' }) => {
  const { clientId, setClientId } = useClient();
  const availableClientIds = getAvailableClientIds();

  const handleChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newClientId = event.target.value;
    setClientId(newClientId);
    
    // Store in localStorage for persistence
    localStorage.setItem('clientId', newClientId);
  };

  return (
    <div className={`client-selector ${className}`}>
      <label htmlFor="client-select" className="client-selector__label">
        Client:
      </label>
      <select
        id="client-select"
        className="client-selector__select"
        value={clientId}
        onChange={handleChange}
      >
        {availableClientIds.map(id => (
          <option key={id} value={id}>
            {id.charAt(0).toUpperCase() + id.slice(1)}
          </option>
        ))}
      </select>
    </div>
  );
};

export default ClientSelector;