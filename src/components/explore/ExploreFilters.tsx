
import React from 'react';
import { Button } from '@/components/ui/button';
import { Search, X } from 'lucide-react';
import { Destination } from '@/data/destinations';

interface ExploreFiltersProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  selectedTypes: string[];
  toggleType: (type: string) => void;
  filteredDestinations: Destination[];
  toggleDestination: (destination: Destination) => void;
  selectedDestinations: Destination[];
  poiTypes: string[];
}

const ExploreFilters: React.FC<ExploreFiltersProps> = ({
  searchTerm,
  setSearchTerm,
  selectedTypes,
  toggleType,
  filteredDestinations,
  toggleDestination,
  selectedDestinations,
  poiTypes
}) => {
  return (
    <div className="sticky top-0 bg-white z-10 p-4 border-b border-gray-100">
      <div className="relative">
        <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
        <input
          type="text"
          placeholder="Search for destinations, activities..."
          className="w-full pl-10 pr-4 py-2 rounded-full bg-gray-50 text-gray-800 placeholder-gray-500 border border-gray-200 focus:outline-none focus:ring-2 focus:ring-morocco-terracotta/30"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>
      
      <div className="mt-4">
        <p className="font-medium text-sm text-gray-600 mb-2">Select destinations:</p>
        <div className="flex flex-wrap gap-1.5">
          {filteredDestinations.map(destination => (
            <Button
              key={destination.id}
              variant={selectedDestinations.some(d => d.id === destination.id) ? "default" : "outline"}
              size="sm"
              onClick={() => toggleDestination(destination)}
              className={`text-xs capitalize ${
                selectedDestinations.some(d => d.id === destination.id) 
                  ? 'bg-morocco-terracotta hover:bg-morocco-terracotta/90' 
                  : 'border-gray-200'
              }`}
            >
              {destination.name}
            </Button>
          ))}
        </div>
      </div>
      
      {selectedDestinations.length > 0 && (
        <div className="mt-4">
          <p className="font-medium text-sm text-gray-600 mb-2">Filter by type:</p>
          <div className="flex flex-wrap gap-1.5">
            {poiTypes.map(type => (
              <Button
                key={type}
                variant={selectedTypes.includes(type) ? "default" : "outline"}
                size="sm"
                onClick={() => toggleType(type)}
                className={`text-xs capitalize ${
                  selectedTypes.includes(type) ? 'bg-morocco-blue hover:bg-morocco-blue/90' : 'border-gray-200'
                }`}
              >
                {type}
              </Button>
            ))}
            {selectedTypes.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => toggleType('')}
                className="text-xs"
              >
                <X className="h-3 w-3 mr-1" /> Clear
              </Button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ExploreFilters;
