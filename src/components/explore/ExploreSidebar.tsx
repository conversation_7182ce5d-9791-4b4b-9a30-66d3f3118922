
import React from 'react';
import { Destination } from '@/data/destinations';
import { PointOfInterest } from '@/components/PointOfInterestCard';
import ExploreFilters from './ExploreFilters';
import ExplorePOIList from './ExplorePOIList';

interface ExploreSidebarProps {
  isFullMapView: boolean;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  selectedTypes: string[];
  toggleType: (type: string) => void;
  filteredDestinations: Destination[];
  toggleDestination: (destination: Destination) => void;
  selectedDestinations: Destination[];
  poiTypes: string[];
  filteredPOIs: PointOfInterest[];
  addToItinerary: (poi: PointOfInterest) => void;
  selectedPOIs: PointOfInterest[];
}

const ExploreSidebar: React.FC<ExploreSidebarProps> = ({
  isFullMapView,
  searchTerm,
  setSearchTerm,
  selectedTypes,
  toggleType,
  filteredDestinations,
  toggleDestination,
  selectedDestinations,
  poiTypes,
  filteredPOIs,
  addToItinerary,
  selectedPOIs
}) => {
  return (
    <div className={`md:w-96 bg-white border-r border-gray-200 md:h-[calc(100vh-132px)] md:overflow-y-auto transition-all duration-300 ${
      isFullMapView ? 'hidden md:block md:w-0 md:border-r-0 md:opacity-0' : 'w-full'
    }`}>
      <ExploreFilters
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        selectedTypes={selectedTypes}
        toggleType={toggleType}
        filteredDestinations={filteredDestinations}
        toggleDestination={toggleDestination}
        selectedDestinations={selectedDestinations}
        poiTypes={poiTypes}
      />
      
      <div className="p-4">
        <ExplorePOIList
          selectedDestinations={selectedDestinations}
          filteredPOIs={filteredPOIs}
          addToItinerary={addToItinerary}
          selectedPOIs={selectedPOIs}
        />
      </div>
    </div>
  );
};

export default ExploreSidebar;
