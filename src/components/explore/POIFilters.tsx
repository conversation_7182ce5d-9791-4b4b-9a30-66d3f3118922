import React from 'react';
import type { POICategory } from '@/types/POITypes';
import { Button } from '@/components/ui/button';

interface POIFiltersProps {
  availableCategories: POICategory[];
  activeFilters: POICategory[];
  onFilterChange: (category: POICategory | 'all') => void;
}

const POIFilters: React.FC<POIFiltersProps> = ({
  availableCategories,
  activeFilters,
  onFilterChange,
}) => {
  const handleFilterClick = (category: POICategory | 'all') => {
    onFilterChange(category);
  };

  return (
    <div className="flex flex-wrap gap-2 p-2 items-center">
      <Button
        variant={activeFilters.length === 0 || activeFilters.includes('all' as POICategory) ? 'default' : 'outline'} // Ensure 'all' can be active
        size="sm"
        onClick={() => handleFilterClick('all')}
        className="text-xs px-3 py-1 rounded-full"
      >
        All
      </Button>
      {availableCategories.map((category: POICategory) => (
        <Button
          key={category}
          variant={activeFilters.includes(category) ? 'default' : 'outline'}
          size="sm"
          onClick={() => handleFilterClick(category)}
          className="text-xs px-3 py-1 rounded-full"
        >
          {category.charAt(0).toUpperCase() + category.slice(1)}
        </Button>
      ))}
    </div>
  );
};

export default POIFilters; 