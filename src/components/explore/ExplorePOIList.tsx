
import React from 'react';
import { PointOfInterest } from '@/components/PointOfInterestCard';
import PointOfInterestCard from '@/components/PointOfInterestCard';
import { MapPin, Search } from 'lucide-react';

interface ExplorePOIListProps {
  selectedDestinations: any[];
  filteredPOIs: PointOfInterest[];
  addToItinerary: (poi: PointOfInterest) => void;
  selectedPOIs: PointOfInterest[];
}

const ExplorePOIList: React.FC<ExplorePOIListProps> = ({
  selectedDestinations,
  filteredPOIs,
  addToItinerary,
  selectedPOIs
}) => {
  if (selectedDestinations.length === 0) {
    return (
      <div className="text-center py-8">
        <MapPin className="h-10 w-10 text-gray-300 mx-auto mb-3" />
        <h3 className="text-lg font-medium text-gray-600">Select a destination</h3>
        <p className="text-sm text-gray-500 mt-1 max-w-xs mx-auto">
          Choose a destination to discover points of interest and activities.
        </p>
      </div>
    );
  }
  
  if (filteredPOIs.length === 0) {
    return (
      <div className="text-center py-8">
        <Search className="h-10 w-10 text-gray-300 mx-auto mb-3" />
        <h3 className="text-lg font-medium text-gray-600">No results found</h3>
        <p className="text-sm text-gray-500 mt-1 max-w-xs mx-auto">
          Try adjusting your search or filters to find activities.
        </p>
      </div>
    );
  }
  
  return (
    <div className="grid grid-cols-1 gap-4">
      {filteredPOIs.map(poi => (
        <PointOfInterestCard
          key={poi.id}
          poi={poi}
          onAdd={() => addToItinerary(poi)}
          isAdded={selectedPOIs.some(p => p.id === poi.id)}
        />
      ))}
    </div>
  );
};

export default ExplorePOIList;
