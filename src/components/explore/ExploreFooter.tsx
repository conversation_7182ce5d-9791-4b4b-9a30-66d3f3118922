
import React from 'react';
import { Button } from '@/components/ui/button';
import { PointOfInterest } from '@/components/PointOfInterestCard';

interface ExploreFooterProps {
  selectedPOIs: PointOfInterest[];
}

const ExploreFooter: React.FC<ExploreFooterProps> = ({ selectedPOIs }) => {
  if (selectedPOIs.length === 0) {
    return null;
  }
  
  return (
    <div className="fixed bottom-20 md:bottom-6 left-1/2 transform -translate-x-1/2 bg-morocco-dark text-white py-3 px-6 rounded-full shadow-lg z-20">
      <div className="flex items-center space-x-3">
        <span>{selectedPOIs.length} items selected</span>
        <Button className="bg-white text-morocco-dark hover:bg-white/90">
          Add to Itinerary
        </Button>
      </div>
    </div>
  );
};

export default ExploreFooter;
