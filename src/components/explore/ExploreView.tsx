import React, { useState, useMemo } from 'react';
import type { PointOfInterest, Destination, POICategory } from '@/types/POITypes';
// import ExploreMap from '../map/ExploreMap'; // Assuming a map component
import POIFilters from './POIFilters'; // The component we just recreated
import PointOfInterestCard from '../PointOfInterestCard'; // Assuming a card component

interface ExploreViewProps {
  destinations: Destination[];
  pointsOfInterest: PointOfInterest[];
  initialSelectedCategories?: POICategory[];
}

const ExploreView: React.FC<ExploreViewProps> = ({
  destinations,
  pointsOfInterest,
  initialSelectedCategories = [],
}) => {
  const [activeCategories, setActiveCategories] = useState<POICategory[]>(initialSelectedCategories);
  const [searchTerm, setSearchTerm] = useState<string>('');

  const allPoiCategories = useMemo(() => {
    const categories = new Set<POICategory>();
    pointsOfInterest.forEach(poi => {
      if (poi.category) {
        categories.add(poi.category as POICategory); // Cast as POICategory is an enum/string literal union
      }
    });
    return Array.from(categories);
  }, [pointsOfInterest]);

  const filteredPOIs = useMemo(() => {
    return pointsOfInterest.filter(poi => {
      const categoryMatch = activeCategories.length === 0 || (poi.category && activeCategories.includes(poi.category as POICategory));
      const searchMatch = !searchTerm || 
                          poi.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
                          (poi.description && poi.description.toLowerCase().includes(searchTerm.toLowerCase()));
      return categoryMatch && searchMatch;
    });
  }, [pointsOfInterest, activeCategories, searchTerm]);

  const handleFilterChange = (category: POICategory | 'all') => {
    if (category === 'all') {
      setActiveCategories([]);
    } else {
      setActiveCategories(prev => 
        prev.includes(category) ? prev.filter(c => c !== category) : [...prev, category]
      );
    }
  };

  // Placeholder for adding POI to itinerary - replace with actual logic
  const handleAddPoiToItinerary = (poi: PointOfInterest) => {
    console.log("Add to itinerary:", poi.name);
    // Implement actual logic to add POI to a shared state or call an API
  };

  return (
    <div className="flex flex-col h-screen">
      {/* Filters and Search Section */}
      <div className="p-4 bg-gray-50 border-b">
        <input 
          type="text"
          placeholder="Search by name or description..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full p-2 border rounded mb-4"
        />
        <POIFilters 
          availableCategories={allPoiCategories}
          activeFilters={activeCategories}
          onFilterChange={handleFilterChange}
        />
      </div>

      {/* Main Content Area (e.g., Map and POI List) */}
      <div className="flex-grow flex overflow-hidden">
        {/* Map Area - Placeholder */}
        {/* <div className="w-2/3 h-full">
          <ExploreMap destinations={destinations} pointsOfInterest={filteredPOIs} />
        </div> */}
        
        {/* POI List Area */}
        <div className="w-full md:w-1/3 h-full overflow-y-auto p-4 space-y-4">
          {filteredPOIs.length > 0 ? (
            filteredPOIs.map(poi => (
              <PointOfInterestCard 
                key={poi.id}
                poi={poi} 
                onAdd={() => handleAddPoiToItinerary(poi)}
                // isAdded prop might depend on whether it's in the current itinerary
              />
            ))
          ) : (
            <p className="text-gray-500 text-center">No points of interest match your criteria.</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default ExploreView; 