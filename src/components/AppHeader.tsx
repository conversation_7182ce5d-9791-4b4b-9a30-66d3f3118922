import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { UserAvatar } from '@/components/UserAvatar';
import { useAuth } from '@/contexts/AuthContext';
import { LogIn, MapPin } from 'lucide-react';


const AppHeader = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  
  return (
    <header className="bg-morocco-terracotta text-white p-4 shadow-md" style={{ position: 'relative', zIndex: 3000000 }}>
      <div className="container mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <Link to="/" className="mb-2 md:mb-0">
            <div className="flex items-center">
              <div className="text-white text-3xl font-bold tracking-tighter">
                <span style={{ fontFamily: "'Montserrat', sans-serif" }}>ex-plore</span>
              </div>
            </div>
          </Link>
          
          <div className="flex items-center gap-4">
            <Link to="/full-map">
              <Button className="bg-white text-morocco-terracotta hover:bg-white/90">
                <MapPin className="mr-1 h-4 w-4" /> Full Map View
              </Button>
            </Link>
            
            {user ? (
              <UserAvatar />
            ) : (
              <Button 
                className="bg-white text-morocco-terracotta hover:bg-white/90"
                onClick={() => navigate('/auth')}
              >
                <LogIn className="mr-1 h-4 w-4" /> Sign In
              </Button>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default AppHeader;
