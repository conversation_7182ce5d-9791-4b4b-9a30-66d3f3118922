import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Filter, ChevronRight, ChevronLeft, MapPin, Search, Globe, Navigation, ChevronDown, Clock, Menu } from 'lucide-react';

interface MainHeaderProps {
  showFilters: boolean;
  setShowFilters: (show: boolean) => void;
  showSidebar: boolean;
  setShowSidebar: (show: boolean) => void;
  journeyInfo?: {
    from: string;
    to: string;
    duration: string;
  };
  currentRegion?: string;
  availableRegions?: string[];
  onRegionChange?: (region: string) => void;
  currentContext?: {
    type: 'planning' | 'exploring' | 'route' | 'settings';
    data?: {
      cityName?: string;
      origin?: string;
      destination?: string;
    };
  };
  searchTerm?: string;
  onSearchTermChange?: (term: string) => void;
  mobileToggleButton?: React.ReactNode;
}

const MainHeader: React.FC<MainHeaderProps> = ({
  showFilters,
  setShowFilters,
  showSidebar,
  setShowSidebar,
  journeyInfo,
  currentRegion = "Morocco",
  availableRegions = ["Morocco"],
  onRegionChange,
  currentContext = { type: 'planning' },
  searchTerm,
  onSearchTermChange,
  mobileToggleButton
}) => {
  const [showRegionSelector, setShowRegionSelector] = useState(false);
  const [internalSearchTerm, setInternalSearchTerm] = useState(searchTerm || '');

  // Sync internal search term if prop changes
  React.useEffect(() => {
    if (searchTerm !== undefined) {
      setInternalSearchTerm(searchTerm);
    }
  }, [searchTerm]);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInternalSearchTerm(event.target.value);
    onSearchTermChange?.(event.target.value);
  };

  // Debug log for props
  console.log('MainHeader props:', { currentRegion, availableRegions });

  const getContextualTitle = () => {
    switch (currentContext.type) {
      case 'exploring':
        return `Exploring: ${currentContext.data?.cityName || 'Morocco'}`;
      case 'route':
        return `En Route: ${currentContext.data?.origin || ''} → ${currentContext.data?.destination || ''}`;
      case 'settings':
        return 'Account Settings';
      case 'planning':
      default:
        return `Plan Your ${currentRegion} Trip`;
    }
  };

  return (
    <div className="bg-morocco-terracotta text-white py-3 px-4 shadow-md" style={{ position: 'relative', zIndex: 3000000 }}>
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-8">
          <a href="/" className="flex items-center">
            <div className="text-white text-3xl font-bold tracking-tighter">
              <span style={{ fontFamily: "'Montserrat', sans-serif" }}>ex<span style={{ fontSize: '0.6em', verticalAlign: 'middle', margin: '0 2px' }}>➤</span>plore</span>
            </div>
          </a>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Button 
              variant="ghost" 
              size="sm" 
              className="text-white hover:bg-white/10 font-medium"
              onClick={() => setShowRegionSelector(!showRegionSelector)}
              // disabled={availableRegions.length <= 1} // Always enabled for debugging
            >
              <Globe className="h-4 w-4 mr-2" />
              {currentRegion}
              {availableRegions.length > 1 && <ChevronDown className="h-4 w-4 ml-1" />}
            </Button>
            
            {showRegionSelector && (
              <div className="absolute top-full left-0 mt-1 bg-white rounded-md shadow-lg z-50 border-2 border-red-500" style={{ minWidth: 120 }}>
                {availableRegions.map(region => (
                  <button
                    key={region}
                    className="block w-full text-left px-4 py-2 text-gray-800 hover:bg-gray-100"
                    onClick={() => {
                      onRegionChange?.(region);
                      setShowRegionSelector(false);
                    }}
                  >
                    {region}
                  </button>
                ))}
              </div>
            )}
          </div>
          
          {/* <div className="text-white font-medium">
            {getContextualTitle()}
          </div> */}
        </div>
        
        <div className="flex items-center space-x-2">
          {mobileToggleButton}
          
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input 
              type="text"
              placeholder="Search destinations..."
              value={internalSearchTerm}
              onChange={handleSearchChange}
              className="bg-white/10 placeholder-white/70 text-white rounded-md py-1.5 pl-10 pr-3 text-sm focus:ring-2 focus:ring-white/50 focus:outline-none"
            />
          </div>
          
          <Button
            variant="outline"
            size="sm"
            className="text-white border-white/20 hover:bg-white/10"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="h-4 w-4 mr-2" /> Filters
          </Button>
        </div>
      </div>
    </div>
  );
};

export default MainHeader;
