import React from 'react';
import type { PointOfInterest, Destination } from '@/types/POITypes'; // Assuming POITypes is 2 levels up

// Define a simple structure for what a journey item might be
interface JourneyItem {
  id: string;
  type: 'poi' | 'travel';
  name: string;
  details?: string; // e.g., POI category or travel mode
  duration?: number; // in hours
  poi?: PointOfInterest;
  destination?: Destination;
}

interface JourneyDay {
  dayNumber: number;
  title?: string;
  items: JourneyItem[];
}

interface JourneySummaryProps {
  itinerary: JourneyDay[];
  onSelectItem?: (item: JourneyItem) => void;
}

const JourneySummary: React.FC<JourneySummaryProps> = ({
  itinerary,
  onSelectItem,
}) => {
  if (!itinerary || itinerary.length === 0) {
    return <p className="text-gray-500">No itinerary to display.</p>;
  }

  return (
    <div className="space-y-6 p-4 bg-gray-50 rounded-lg shadow">
      <h2 className="text-xl font-semibold text-gray-800">Journey Summary</h2>
      {itinerary.map((day, dayIndex: number) => (
        <div key={day.dayNumber} className="p-4 bg-white rounded shadow-sm">
          <h3 className="text-lg font-medium text-morocco-blue mb-2">
            Day {day.dayNumber} {day.title ? `- ${day.title}` : ''}
          </h3>
          <ul className="space-y-2">
            {day.items.map((item, itemIndex: number) => (
              <li 
                key={`${day.dayNumber}-${item.id}-${itemIndex}`}
                className={`p-3 rounded ${onSelectItem ? 'cursor-pointer hover:bg-gray-100' : ''}`}
                onClick={() => onSelectItem && onSelectItem(item)}
              >
                <div className="font-medium">{item.name}</div>
                {item.details && <div className="text-sm text-gray-600">{item.details}</div>}
                {item.duration && <div className="text-xs text-gray-500">Estimated: {item.duration}hr</div>}
              </li>
            ))}
          </ul>
        </div>
      ))}
    </div>
  );
};

export default JourneySummary; 