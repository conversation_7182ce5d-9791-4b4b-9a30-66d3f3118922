
import React from 'react';
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { MapPin, Calendar, Clock, Route as RouteIcon, Car, Trash2 } from 'lucide-react';
import { formatDuration } from '@/utils/routeUtils';

export interface SavedRoute {
  id: string;
  name: string;
  destinations: {
    id: string;
    name: string;
    type: string;
    image?: string;
    coordinates?: [number, number];
  }[];
  vehicle: {
    id: string;
    name: string;
    type: string;
  } | null;
  distance: number;
  duration: number;
  recommended_days: number;
  created_at: string;
  points_of_interest: any[];
}

interface RouteCardProps {
  route: SavedRoute;
  onDelete: (routeId: string) => Promise<void>;
}

const RouteCard = ({ route, onDelete }: RouteCardProps) => {
  const navigate = useNavigate();

  return (
    <Card key={route.id} className="h-full flex flex-col">
      <CardHeader>
        <CardTitle className="text-xl">{route.name}</CardTitle>
        <CardDescription>
          Created on {new Date(route.created_at).toLocaleDateString()}
        </CardDescription>
      </CardHeader>

      <CardContent className="flex-1">
        <div className="space-y-4">
          <div>
            <h4 className="font-medium text-sm text-gray-500 mb-2 flex items-center">
              <MapPin className="h-4 w-4 mr-1 text-morocco-terracotta" /> Destinations
            </h4>
            <ul className="space-y-1 ml-6">
              {route.destinations.map((dest, index) => (
                <li key={dest.id} className="flex items-center">
                  <span className="bg-morocco-terracotta text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-2">
                    {index + 1}
                  </span>
                  <span>{dest.name}</span>
                </li>
              ))}
            </ul>
          </div>

          {route.vehicle && (
            <div className="flex items-center">
              <Car className="h-4 w-4 mr-2 text-morocco-terracotta" />
              <span>{route.vehicle.name} ({route.vehicle.type})</span>
            </div>
          )}

          <div className="pt-4 border-t border-gray-100">
            <div className="flex items-center mb-2">
              <RouteIcon className="h-4 w-4 mr-2 text-morocco-terracotta" />
              <span className="text-sm">{route.distance} km</span>
            </div>
            <div className="flex items-center mb-2">
              <Clock className="h-4 w-4 mr-2 text-morocco-terracotta" />
              <span className="text-sm">{formatDuration(route.duration)}</span>
            </div>
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-2 text-morocco-terracotta" />
              <span className="text-sm">{route.recommended_days} days recommended</span>
            </div>
          </div>
        </div>
      </CardContent>

      <CardFooter className="border-t pt-4 flex justify-between">
        <Button 
          variant="outline" 
          onClick={() => navigate('/')}
          className="flex-1 mr-2"
        >
          View
        </Button>

        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button 
              variant="outline" 
              className="border-red-300 text-red-600 hover:bg-red-50 flex-1"
            >
              <Trash2 className="h-4 w-4 mr-1" /> Delete
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete your saved route.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction 
                className="bg-red-600 hover:bg-red-700"
                onClick={() => onDelete(route.id)}
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardFooter>
    </Card>
  );
};

export default RouteCard;
