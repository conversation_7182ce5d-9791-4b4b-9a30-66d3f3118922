/**
 * DevDashboard.tsx
 * 
 * DEVELOPMENT DASHBOARD
 * Provides debugging tools and performance monitoring for development
 */

import React, { useState, useEffect } from 'react';
import { 
  Monitor, 
  Activity, 
  Database, 
  Zap, 
  Bug, 
  Settings,
  Eye,
  EyeOff,
  Trash2,
  Download
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { performanceMonitor } from '@/utils/performance';

// ========================================
// TYPES AND INTERFACES
// ========================================

interface DevDashboardProps {
  isVisible: boolean;
  onToggle: () => void;
}

interface PerformanceMetrics {
  renderCount: number;
  lastRenderTime: number;
  averageRenderTime: number;
  memoryUsage?: {
    used: number;
    total: number;
    limit: number;
  };
}

// ========================================
// PERFORMANCE MONITOR COMPONENT
// ========================================

const PerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderCount: 0,
    lastRenderTime: 0,
    averageRenderTime: 0
  });

  useEffect(() => {
    const updateMetrics = () => {
      const performanceMetrics = performanceMonitor.getMetrics();
      const renderMetrics = performanceMetrics.filter(m => m.name.includes('render'));
      
      const renderCount = renderMetrics.length;
      const lastRender = renderMetrics[renderMetrics.length - 1];
      const averageRenderTime = renderMetrics.reduce((sum, m) => sum + (m.duration || 0), 0) / renderCount;

      let memoryUsage;
      if (typeof performance !== 'undefined' && (performance as any).memory) {
        const memory = (performance as any).memory;
        memoryUsage = {
          used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
          total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
          limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
        };
      }

      setMetrics({
        renderCount,
        lastRenderTime: lastRender?.duration || 0,
        averageRenderTime: averageRenderTime || 0,
        memoryUsage
      });
    };

    const interval = setInterval(updateMetrics, 1000);
    updateMetrics();

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Render Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-xs text-gray-600">Count:</span>
                <Badge variant="outline">{metrics.renderCount}</Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-xs text-gray-600">Last:</span>
                <Badge variant={metrics.lastRenderTime > 16 ? "destructive" : "default"}>
                  {metrics.lastRenderTime.toFixed(2)}ms
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-xs text-gray-600">Average:</span>
                <Badge variant={metrics.averageRenderTime > 16 ? "destructive" : "default"}>
                  {metrics.averageRenderTime.toFixed(2)}ms
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {metrics.memoryUsage && (
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Memory Usage</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-xs text-gray-600">Used:</span>
                  <Badge variant="outline">{metrics.memoryUsage.used} MB</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-xs text-gray-600">Total:</span>
                  <Badge variant="outline">{metrics.memoryUsage.total} MB</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-xs text-gray-600">Limit:</span>
                  <Badge variant="outline">{metrics.memoryUsage.limit} MB</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm">Performance Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {performanceMonitor.getMetrics().slice(-10).map((metric, index) => (
              <div key={index} className="flex justify-between items-center text-xs">
                <span className="truncate">{metric.name}</span>
                <Badge variant={metric.duration && metric.duration > 100 ? "destructive" : "outline"}>
                  {metric.duration?.toFixed(2)}ms
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// ========================================
// CONSOLE LOGGER COMPONENT
// ========================================

const ConsoleLogger: React.FC = () => {
  const [logs, setLogs] = useState<Array<{ type: string; message: string; timestamp: Date }>>([]);

  useEffect(() => {
    const originalConsole = {
      log: console.log,
      warn: console.warn,
      error: console.error,
      info: console.info
    };

    const interceptConsole = (type: string) => (originalMethod: any) => (...args: any[]) => {
      originalMethod.apply(console, args);
      setLogs(prev => [...prev.slice(-49), {
        type,
        message: args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)).join(' '),
        timestamp: new Date()
      }]);
    };

    console.log = interceptConsole('log')(originalConsole.log);
    console.warn = interceptConsole('warn')(originalConsole.warn);
    console.error = interceptConsole('error')(originalConsole.error);
    console.info = interceptConsole('info')(originalConsole.info);

    return () => {
      console.log = originalConsole.log;
      console.warn = originalConsole.warn;
      console.error = originalConsole.error;
      console.info = originalConsole.info;
    };
  }, []);

  const clearLogs = () => setLogs([]);

  const exportLogs = () => {
    const logData = logs.map(log => ({
      timestamp: log.timestamp.toISOString(),
      type: log.type,
      message: log.message
    }));
    
    const blob = new Blob([JSON.stringify(logData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `console-logs-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const getLogColor = (type: string) => {
    switch (type) {
      case 'error': return 'text-red-600';
      case 'warn': return 'text-yellow-600';
      case 'info': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-sm font-medium">Console Logs</h3>
        <div className="flex gap-2">
          <Button size="sm" variant="outline" onClick={clearLogs}>
            <Trash2 className="w-3 h-3 mr-1" />
            Clear
          </Button>
          <Button size="sm" variant="outline" onClick={exportLogs}>
            <Download className="w-3 h-3 mr-1" />
            Export
          </Button>
        </div>
      </div>
      
      <Card>
        <CardContent className="p-3">
          <div className="space-y-1 max-h-60 overflow-y-auto font-mono text-xs">
            {logs.length === 0 ? (
              <div className="text-gray-500 text-center py-4">No logs yet</div>
            ) : (
              logs.map((log, index) => (
                <div key={index} className="flex gap-2">
                  <span className="text-gray-400 flex-shrink-0">
                    {log.timestamp.toLocaleTimeString()}
                  </span>
                  <Badge variant="outline" className="flex-shrink-0 text-xs">
                    {log.type}
                  </Badge>
                  <span className={`truncate ${getLogColor(log.type)}`}>
                    {log.message}
                  </span>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// ========================================
// MAIN DEV DASHBOARD COMPONENT
// ========================================

export const DevDashboard: React.FC<DevDashboardProps> = ({ isVisible, onToggle }) => {
  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={onToggle}
          size="sm"
          variant="outline"
          className="bg-white shadow-lg"
        >
          <Bug className="w-4 h-4 mr-2" />
          Dev Tools
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 w-96 max-h-96 bg-white border rounded-lg shadow-xl z-50">
      <div className="flex items-center justify-between p-3 border-b">
        <div className="flex items-center gap-2">
          <Bug className="w-4 h-4" />
          <span className="font-medium text-sm">Dev Dashboard</span>
        </div>
        <Button size="sm" variant="ghost" onClick={onToggle}>
          <EyeOff className="w-4 h-4" />
        </Button>
      </div>

      <Tabs defaultValue="performance" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="performance" className="text-xs">
            <Activity className="w-3 h-3 mr-1" />
            Performance
          </TabsTrigger>
          <TabsTrigger value="console" className="text-xs">
            <Monitor className="w-3 h-3 mr-1" />
            Console
          </TabsTrigger>
          <TabsTrigger value="debug" className="text-xs">
            <Settings className="w-3 h-3 mr-1" />
            Debug
          </TabsTrigger>
        </TabsList>

        <div className="max-h-80 overflow-y-auto">
          <TabsContent value="performance" className="p-3">
            <PerformanceMonitor />
          </TabsContent>

          <TabsContent value="console" className="p-3">
            <ConsoleLogger />
          </TabsContent>

          <TabsContent value="debug" className="p-3">
            <div className="space-y-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">Debug Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="w-full justify-start"
                    onClick={() => performanceMonitor.clear()}
                  >
                    <Trash2 className="w-3 h-3 mr-2" />
                    Clear Performance Metrics
                  </Button>
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="w-full justify-start"
                    onClick={() => window.location.reload()}
                  >
                    <Zap className="w-3 h-3 mr-2" />
                    Force Reload
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">Environment</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-1 text-xs">
                    <div className="flex justify-between">
                      <span>Mode:</span>
                      <Badge variant="outline">{process.env.NODE_ENV}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>React:</span>
                      <Badge variant="outline">{React.version}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>User Agent:</span>
                      <span className="truncate max-w-32" title={navigator.userAgent}>
                        {navigator.userAgent.split(' ')[0]}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
};

export default DevDashboard;
