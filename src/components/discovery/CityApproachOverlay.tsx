/**
 * City Approach Overlay Component
 * 
 * Displays when approaching major cities during the journey,
 * showing top POIs and giving users the option to explore or continue.
 * Optimized for both mobile and desktop with smooth animations.
 */

import React, { useState, useEffect } from 'react';
import { 
  MapPin, 
  Clock, 
  Star, 
  Users,
  Camera,
  Play,
  X,
  ChevronRight,
  Navigation,
  Building2,
  Compass
} from 'lucide-react';
import { PointOfInterest } from '@/types/POITypes';

interface CityApproachOverlayProps {
  cityName: string;
  distance: number;
  topPOIs: PointOfInterest[];
  isVisible: boolean;
  remainingTime: number;
  onExploreCity: () => void;
  onContinue: () => void;
  onPOISelect: (poi: PointOfInterest) => void;
  onClose: () => void;
  isMobile?: boolean;
  className?: string;
}

const CityApproachOverlay: React.FC<CityApproachOverlayProps> = ({
  cityName,
  distance,
  topPOIs,
  isVisible,
  remainingTime,
  onExploreCity,
  onContinue,
  onPOISelect,
  onClose,
  isMobile = false,
  className = ''
}) => {
  const [timeLeft, setTimeLeft] = useState(remainingTime);
  const [isAnimatingIn, setIsAnimatingIn] = useState(false);

  useEffect(() => {
    if (isVisible) {
      setIsAnimatingIn(true);
      const timer = setTimeout(() => setIsAnimatingIn(false), 600);
      return () => clearTimeout(timer);
    }
  }, [isVisible]);

  useEffect(() => {
    if (remainingTime > 0) {
      const interval = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1000) {
            clearInterval(interval);
            return 0;
          }
          return prev - 1000;
        });
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [remainingTime]);

  if (!isVisible) return null;

  const formatTime = (ms: number) => {
    const seconds = Math.ceil(ms / 1000);
    return `${seconds}s`;
  };

  const getCityIcon = (cityName: string) => {
    const cityIcons: Record<string, React.ReactNode> = {
      'marrakech': <Building2 className="text-red-600" size={24} />,
      'casablanca': <Building2 className="text-blue-600" size={24} />,
      'rabat': <Building2 className="text-green-600" size={24} />,
      'fez': <Building2 className="text-purple-600" size={24} />,
      'chefchaouen': <Building2 className="text-cyan-600" size={24} />,
    };
    return cityIcons[cityName.toLowerCase()] || <Building2 className="text-gray-600" size={24} />;
  };

  const getPOIIcon = (category: string) => {
    switch (category?.toLowerCase()) {
      case 'landmark': return <MapPin className="text-red-500" size={16} />;
      case 'restaurant': return <Users className="text-orange-500" size={16} />;
      case 'museum': return <Camera className="text-purple-500" size={16} />;
      case 'mosque': return <Star className="text-blue-500" size={16} />;
      default: return <MapPin className="text-gray-500" size={16} />;
    }
  };

  const POICard: React.FC<{ poi: PointOfInterest; onClick: () => void }> = ({ poi, onClick }) => (
    <div 
      onClick={onClick}
      className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
    >
      {getPOIIcon(poi.category)}
      <div className="flex-1 min-w-0">
        <h4 className="font-medium text-gray-900 truncate">{poi.name}</h4>
        <div className="flex items-center space-x-2 mt-1">
          {poi.rating && (
            <div className="flex items-center space-x-1">
              <Star size={12} className="text-yellow-500 fill-current" />
              <span className="text-xs text-gray-600">{poi.rating}</span>
            </div>
          )}
          {poi.duration_minutes && (
            <div className="flex items-center space-x-1">
              <Clock size={12} className="text-gray-500" />
              <span className="text-xs text-gray-600">{poi.duration_minutes}min</span>
            </div>
          )}
        </div>
      </div>
      <ChevronRight size={16} className="text-gray-400" />
    </div>
  );

  return (
    <div className={`fixed inset-0 z-50 pointer-events-none ${className}`}>
      {/* Backdrop */}
      <div 
        className={`absolute inset-0 bg-black transition-opacity duration-500 ${
          isVisible ? 'opacity-20' : 'opacity-0'
        }`}
      />

      {/* Mobile Layout */}
      {isMobile ? (
        <div className={`absolute top-0 left-0 right-0 pointer-events-auto transform transition-transform duration-500 ease-out ${
          isVisible ? 'translate-y-0' : '-translate-y-full'
        }`}>
          <div className="bg-white rounded-b-3xl shadow-2xl border-b border-gray-200">
            {/* Header */}
            <div className="px-6 pt-6 pb-4">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  {getCityIcon(cityName)}
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">Approaching {cityName}</h3>
                    <p className="text-sm text-gray-600">{distance.toFixed(1)}km away</p>
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X size={20} />
                </button>
              </div>

              {/* Auto-resume timer */}
              {timeLeft > 0 && (
                <div className="flex items-center space-x-2 mb-4 p-3 bg-blue-50 rounded-lg">
                  <Clock size={16} className="text-blue-600" />
                  <span className="text-sm text-blue-700">
                    Auto-continuing in {formatTime(timeLeft)}
                  </span>
                </div>
              )}

              {/* Top POIs */}
              <div className="mb-4">
                <h4 className="font-semibold text-gray-900 mb-3">
                  Top attractions in {cityName}
                </h4>
                <div className="space-y-2">
                  {topPOIs.slice(0, 3).map((poi) => (
                    <POICard 
                      key={poi.id} 
                      poi={poi} 
                      onClick={() => onPOISelect(poi)} 
                    />
                  ))}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={onExploreCity}
                  className="flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 transition-colors"
                >
                  <Compass size={18} />
                  <span>Explore City</span>
                </button>
                <button
                  onClick={onContinue}
                  className="flex items-center justify-center space-x-2 px-4 py-3 bg-gray-100 text-gray-700 rounded-xl font-medium hover:bg-gray-200 transition-colors"
                >
                  <Play size={18} />
                  <span>Continue</span>
                </button>
              </div>
            </div>

            {/* Handle */}
            <div className="flex justify-center pb-3">
              <div className="w-12 h-1 bg-gray-300 rounded-full" />
            </div>
          </div>
        </div>
      ) : (
        /* Desktop Layout */
        <div className={`absolute top-20 right-6 pointer-events-auto transform transition-all duration-500 ease-out ${
          isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'
        } ${isAnimatingIn ? 'animate-pulse' : ''}`}>
          <div className="bg-white rounded-2xl shadow-2xl border border-gray-200 w-80">
            {/* Header */}
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-3">
                  {getCityIcon(cityName)}
                  <div>
                    <h3 className="text-lg font-bold text-gray-900">Approaching {cityName}</h3>
                    <p className="text-gray-600">{distance.toFixed(1)}km away</p>
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors rounded-lg hover:bg-gray-100"
                >
                  <X size={20} />
                </button>
              </div>

              {/* Auto-resume timer */}
              {timeLeft > 0 && (
                <div className="flex items-center space-x-2 p-3 bg-blue-50 rounded-lg">
                  <Clock size={16} className="text-blue-600" />
                  <span className="text-sm text-blue-700">
                    Auto-continuing in {formatTime(timeLeft)}
                  </span>
                </div>
              )}
            </div>

            {/* Content */}
            <div className="p-6">
              {/* Top POIs */}
              <div className="mb-6">
                <h4 className="font-semibold text-gray-900 mb-3">
                  Top attractions in {cityName}
                </h4>
                <div className="space-y-2">
                  {topPOIs.slice(0, 4).map((poi) => (
                    <POICard 
                      key={poi.id} 
                      poi={poi} 
                      onClick={() => onPOISelect(poi)} 
                    />
                  ))}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={onExploreCity}
                  className="flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors"
                >
                  <Compass size={18} />
                  <span>Explore</span>
                </button>
                <button
                  onClick={onContinue}
                  className="flex items-center justify-center space-x-2 px-4 py-3 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors"
                >
                  <Play size={18} />
                  <span>Continue</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CityApproachOverlay;
