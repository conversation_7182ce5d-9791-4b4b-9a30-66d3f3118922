/**
 * POI Discovery Overlay Component
 * 
 * The cornerstone innovation of our travel platform - a non-invasive,
 * mobile-optimized overlay that appears when significant POIs are discovered
 * during cinematic journey animation.
 * 
 * Key Features:
 * - Automatic pause with exploration options
 * - Mobile-first design with touch-friendly controls
 * - Smooth animations and transitions
 * - Real travel insights and recommendations
 */

import React, { useState, useEffect } from 'react';
import { 
  MapPin, 
  Clock, 
  Star, 
  Camera, 
  Heart,
  Plus,
  Play,
  X,
  ChevronRight,
  Info,
  Navigation,
  Users
} from 'lucide-react';
import { PointOfInterest } from '@/types/POITypes';

interface POIDiscoveryOverlayProps {
  poi: PointOfInterest;
  distance: number;
  isVisible: boolean;
  pauseReason: 'poi' | 'city';
  remainingTime: number;
  onExplore: () => void;
  onContinue: () => void;
  onAddToItinerary: () => void;
  onClose: () => void;
  isMobile?: boolean;
  className?: string;
}

const POIDiscoveryOverlay: React.FC<POIDiscoveryOverlayProps> = ({
  poi,
  distance,
  isVisible,
  pauseReason,
  remainingTime,
  onExplore,
  onContinue,
  onAddToItinerary,
  onClose,
  isMobile = false,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [timeLeft, setTimeLeft] = useState(remainingTime);
  const [isAnimatingIn, setIsAnimatingIn] = useState(false);
  const [touchStartY, setTouchStartY] = useState<number | null>(null);
  const [isDragging, setIsDragging] = useState(false);

  useEffect(() => {
    if (isVisible) {
      setIsAnimatingIn(true);

      // Haptic feedback for mobile devices
      if (isMobile && 'vibrate' in navigator) {
        navigator.vibrate(50); // Short vibration
      }

      const timer = setTimeout(() => setIsAnimatingIn(false), 500);
      return () => clearTimeout(timer);
    }
  }, [isVisible, isMobile]);

  useEffect(() => {
    if (remainingTime > 0) {
      const interval = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1000) {
            clearInterval(interval);
            return 0;
          }
          return prev - 1000;
        });
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [remainingTime]);

  // Touch gesture handlers for mobile
  const handleTouchStart = (e: React.TouchEvent) => {
    if (isMobile) {
      setTouchStartY(e.touches[0].clientY);
      setIsDragging(false);
    }
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (isMobile && touchStartY !== null) {
      const currentY = e.touches[0].clientY;
      const deltaY = currentY - touchStartY;

      if (Math.abs(deltaY) > 10) {
        setIsDragging(true);
      }
    }
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (isMobile && touchStartY !== null && isDragging) {
      const currentY = e.changedTouches[0].clientY;
      const deltaY = currentY - touchStartY;

      // Swipe down to dismiss (for mobile bottom overlay)
      if (deltaY > 100) {
        onClose();
      }
      // Swipe up to expand (for mobile bottom overlay)
      else if (deltaY < -50) {
        setIsExpanded(true);
      }
    }

    setTouchStartY(null);
    setIsDragging(false);
  };

  if (!isVisible) return null;

  const formatTime = (ms: number) => {
    const seconds = Math.ceil(ms / 1000);
    return `${seconds}s`;
  };

  const getPOIIcon = (category: string) => {
    switch (category?.toLowerCase()) {
      case 'landmark': return <MapPin className="text-red-500" size={20} />;
      case 'restaurant': return <Users className="text-orange-500" size={20} />;
      case 'museum': return <Camera className="text-purple-500" size={20} />;
      case 'mosque': return <Star className="text-blue-500" size={20} />;
      default: return <MapPin className="text-gray-500" size={20} />;
    }
  };

  const getImportanceColor = (importance: number) => {
    if (importance >= 8) return 'text-red-500 bg-red-50';
    if (importance >= 6) return 'text-orange-500 bg-orange-50';
    if (importance >= 4) return 'text-blue-500 bg-blue-50';
    return 'text-gray-500 bg-gray-50';
  };

  return (
    <div className={`fixed inset-0 z-50 pointer-events-none ${className}`}>
      {/* Backdrop */}
      <div 
        className={`absolute inset-0 bg-black transition-opacity duration-500 ${
          isVisible ? 'opacity-20' : 'opacity-0'
        }`}
      />

      {/* Mobile Layout */}
      {isMobile ? (
        <div className={`absolute bottom-0 left-0 right-0 pointer-events-auto transform transition-transform duration-500 ease-out ${
          isVisible ? 'translate-y-0' : 'translate-y-full'
        } ${isDragging ? 'transition-none' : ''}`}>
          <div
            className="bg-white rounded-t-3xl shadow-2xl border-t border-gray-200"
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
          >
            {/* Handle */}
            <div className="flex justify-center pt-3 pb-2">
              <div className="w-12 h-1 bg-gray-300 rounded-full" />
            </div>

            {/* Header */}
            <div className="px-6 pb-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-3">
                  {getPOIIcon(poi.category)}
                  <div>
                    <h3 className="text-lg font-bold text-gray-900">{poi.name}</h3>
                    <p className="text-sm text-gray-600">{distance.toFixed(1)}km away</p>
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X size={20} />
                </button>
              </div>

              {/* Auto-resume timer */}
              {timeLeft > 0 && (
                <div className="flex items-center space-x-2 mb-4 p-3 bg-blue-50 rounded-lg">
                  <Clock size={16} className="text-blue-600" />
                  <span className="text-sm text-blue-700">
                    Auto-continuing in {formatTime(timeLeft)}
                  </span>
                </div>
              )}

              {/* Description */}
              <p className="text-gray-700 text-sm mb-4 line-clamp-2">
                {poi.description || 'Discover this amazing point of interest on your journey through Morocco.'}
              </p>

              {/* Quick Info */}
              <div className="flex items-center space-x-4 mb-4">
                {poi.rating && (
                  <div className="flex items-center space-x-1">
                    <Star size={14} className="text-yellow-500 fill-current" />
                    <span className="text-sm font-medium">{poi.rating}</span>
                  </div>
                )}
                {poi.duration_minutes && (
                  <div className="flex items-center space-x-1">
                    <Clock size={14} className="text-gray-500" />
                    <span className="text-sm text-gray-600">{poi.duration_minutes}min</span>
                  </div>
                )}
                <div className={`px-2 py-1 rounded-full text-xs font-medium ${getImportanceColor(poi.importance || 5)}`}>
                  Must-see
                </div>
              </div>

              {/* Action Buttons - Mobile optimized */}
              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={() => {
                    if (isMobile && 'vibrate' in navigator) navigator.vibrate(30);
                    onExplore();
                  }}
                  className={`flex items-center justify-center space-x-2 px-4 ${isMobile ? 'py-4' : 'py-3'} bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 active:bg-blue-800 transition-colors ${isMobile ? 'min-h-[48px]' : ''}`}
                >
                  <Info size={18} />
                  <span>Explore</span>
                </button>
                <button
                  onClick={() => {
                    if (isMobile && 'vibrate' in navigator) navigator.vibrate(30);
                    onAddToItinerary();
                  }}
                  className={`flex items-center justify-center space-x-2 px-4 ${isMobile ? 'py-4' : 'py-3'} bg-green-600 text-white rounded-xl font-medium hover:bg-green-700 active:bg-green-800 transition-colors ${isMobile ? 'min-h-[48px]' : ''}`}
                >
                  <Plus size={18} />
                  <span>Add to Trip</span>
                </button>
              </div>

              {/* Continue Button - Mobile optimized */}
              <button
                onClick={() => {
                  if (isMobile && 'vibrate' in navigator) navigator.vibrate(30);
                  onContinue();
                }}
                className={`w-full mt-3 flex items-center justify-center space-x-2 px-4 ${isMobile ? 'py-4' : 'py-3'} bg-gray-100 text-gray-700 rounded-xl font-medium hover:bg-gray-200 active:bg-gray-300 transition-colors ${isMobile ? 'min-h-[48px]' : ''}`}
              >
                <Play size={18} />
                <span>Continue Journey</span>
              </button>
            </div>
          </div>
        </div>
      ) : (
        /* Desktop Layout */
        <div className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-auto transition-all duration-500 ease-out ${
          isVisible ? 'scale-100 opacity-100' : 'scale-95 opacity-0'
        } ${isAnimatingIn ? 'animate-pulse' : ''}`}>
          <div className="bg-white rounded-2xl shadow-2xl border border-gray-200 max-w-md w-full mx-4">
            {/* Header */}
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-3">
                  {getPOIIcon(poi.category)}
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">{poi.name}</h3>
                    <p className="text-gray-600">{distance.toFixed(1)}km away</p>
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors rounded-lg hover:bg-gray-100"
                >
                  <X size={20} />
                </button>
              </div>

              {/* Auto-resume timer */}
              {timeLeft > 0 && (
                <div className="flex items-center space-x-2 p-3 bg-blue-50 rounded-lg">
                  <Clock size={16} className="text-blue-600" />
                  <span className="text-sm text-blue-700">
                    Auto-continuing in {formatTime(timeLeft)}
                  </span>
                </div>
              )}
            </div>

            {/* Content */}
            <div className="p-6">
              <p className="text-gray-700 mb-4">
                {poi.description || 'Discover this amazing point of interest on your journey through Morocco.'}
              </p>

              {/* Quick Info */}
              <div className="flex items-center space-x-4 mb-6">
                {poi.rating && (
                  <div className="flex items-center space-x-1">
                    <Star size={16} className="text-yellow-500 fill-current" />
                    <span className="font-medium">{poi.rating}</span>
                  </div>
                )}
                {poi.duration_minutes && (
                  <div className="flex items-center space-x-1">
                    <Clock size={16} className="text-gray-500" />
                    <span className="text-gray-600">{poi.duration_minutes}min visit</span>
                  </div>
                )}
                <div className={`px-3 py-1 rounded-full text-sm font-medium ${getImportanceColor(poi.importance || 5)}`}>
                  Must-see
                </div>
              </div>

              {/* Action Buttons */}
              <div className="grid grid-cols-2 gap-3 mb-3">
                <button
                  onClick={onExplore}
                  className="flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors"
                >
                  <Info size={18} />
                  <span>Explore</span>
                </button>
                <button
                  onClick={onAddToItinerary}
                  className="flex items-center justify-center space-x-2 px-4 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors"
                >
                  <Plus size={18} />
                  <span>Add to Trip</span>
                </button>
              </div>

              {/* Continue Button */}
              <button
                onClick={onContinue}
                className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors"
              >
                <Play size={18} />
                <span>Continue Journey</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default POIDiscoveryOverlay;
