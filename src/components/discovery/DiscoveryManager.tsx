/**
 * Discovery Manager Component
 * 
 * Central coordinator for all POI discovery and city approach overlays.
 * Listens to animation events and manages the display of discovery UI.
 * Handles mobile detection and responsive behavior.
 */

import React, { useState, useEffect, useCallback } from 'react';
import POIDiscoveryOverlay from './POIDiscoveryOverlay';
import CityApproachOverlay from './CityApproachOverlay';
import { PointOfInterest } from '@/types/POITypes';
import { POIDiscoveryManager } from '../map/animation/POIDiscoveryManager';

interface DiscoveryState {
  type: 'poi' | 'city' | null;
  poi?: PointOfInterest;
  cityName?: string;
  distance: number;
  remainingTime: number;
  isVisible: boolean;
  topPOIs?: PointOfInterest[];
}

interface DiscoveryManagerProps {
  availablePOIs: PointOfInterest[];
  onPOISelect: (poi: PointOfInterest) => void;
  onPOIAddToItinerary: (poi: PointOfInterest) => void;
  onExploreCity?: (cityName: string) => void;
  className?: string;
}

const DiscoveryManager: React.FC<DiscoveryManagerProps> = ({
  availablePOIs,
  onPOISelect,
  onPOIAddToItinerary,
  onExploreCity,
  className = ''
}) => {
  const [discoveryState, setDiscoveryState] = useState<DiscoveryState>({
    type: null,
    distance: 0,
    remainingTime: 0,
    isVisible: false
  });

  const [isMobile, setIsMobile] = useState(false);

  // Detect mobile device with enhanced detection
  useEffect(() => {
    const checkMobile = () => {
      const isMobileDevice = window.innerWidth <= 768 ||
                           /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                           ('ontouchstart' in window) ||
                           (navigator.maxTouchPoints > 0);
      setIsMobile(isMobileDevice);

      // Notify POI Discovery Manager about mobile state
      const mobileEvent = new CustomEvent('mobile-optimization-toggle', {
        detail: { enabled: isMobileDevice }
      });
      document.dispatchEvent(mobileEvent);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    window.addEventListener('orientationchange', checkMobile);
    return () => {
      window.removeEventListener('resize', checkMobile);
      window.removeEventListener('orientationchange', checkMobile);
    };
  }, []);

  // Listen for automatic pause events
  useEffect(() => {
    const handleAutomaticPause = (event: CustomEvent) => {
      const { reason, data, pauseDuration } = event.detail;
      
      if (reason === 'poi' && data.poi) {
        setDiscoveryState({
          type: 'poi',
          poi: data.poi,
          distance: data.distance,
          remainingTime: pauseDuration,
          isVisible: true
        });
      } else if (reason === 'city' && data.cityName) {
        // Get top POIs for the city
        const cityPOIs = availablePOIs
          .filter(poi => poi.location?.toLowerCase().includes(data.cityName.toLowerCase()))
          .sort((a, b) => (b.importance || 0) - (a.importance || 0))
          .slice(0, 5);

        setDiscoveryState({
          type: 'city',
          cityName: data.cityName,
          distance: data.distance,
          remainingTime: pauseDuration,
          isVisible: true,
          topPOIs: cityPOIs
        });
      }
    };

    const handleAutomaticResume = () => {
      setDiscoveryState(prev => ({
        ...prev,
        isVisible: false
      }));
    };

    // Listen for discovery events
    document.addEventListener('automatic-pause', handleAutomaticPause as EventListener);
    document.addEventListener('automatic-resume', handleAutomaticResume);

    return () => {
      document.removeEventListener('automatic-pause', handleAutomaticPause as EventListener);
      document.removeEventListener('automatic-resume', handleAutomaticResume);
    };
  }, [availablePOIs]);

  // Handle POI exploration
  const handlePOIExplore = useCallback(() => {
    if (discoveryState.poi) {
      // Track exploration start
      const poiManager = POIDiscoveryManager.getInstance();
      poiManager.trackPOIExplorationStart(discoveryState.poi.id);

      onPOISelect(discoveryState.poi);
      // Keep overlay open for exploration
    }
  }, [discoveryState.poi, onPOISelect]);

  // Handle POI add to itinerary
  const handlePOIAddToItinerary = useCallback(() => {
    if (discoveryState.poi) {
      // Track itinerary addition
      const poiManager = POIDiscoveryManager.getInstance();
      poiManager.trackPOIAddedToItinerary(discoveryState.poi.id);

      onPOIAddToItinerary(discoveryState.poi);
      // Close overlay after adding
      handleContinue();
    }
  }, [discoveryState.poi, onPOIAddToItinerary]);

  // Handle city exploration
  const handleCityExplore = useCallback(() => {
    if (discoveryState.cityName && onExploreCity) {
      onExploreCity(discoveryState.cityName);
      // Keep overlay open for exploration
    }
  }, [discoveryState.cityName, onExploreCity]);

  // Handle continue journey
  const handleContinue = useCallback(() => {
    // Resume animation
    const resumeEvent = new CustomEvent('force-resume-animation');
    document.dispatchEvent(resumeEvent);

    // Hide overlay
    setDiscoveryState(prev => ({
      ...prev,
      isVisible: false
    }));
  }, []);

  // Handle close overlay
  const handleClose = useCallback(() => {
    // Track exploration end if POI was being explored
    if (discoveryState.poi) {
      const poiManager = POIDiscoveryManager.getInstance();
      poiManager.trackPOIExplorationEnd(discoveryState.poi.id);
    }
    handleContinue();
  }, [handleContinue, discoveryState.poi]);

  // Handle POI selection from city overlay
  const handleCityPOISelect = useCallback((poi: PointOfInterest) => {
    onPOISelect(poi);
    // Optionally close city overlay and show POI details
  }, [onPOISelect]);

  return (
    <div className={className}>
      {/* POI Discovery Overlay */}
      {discoveryState.type === 'poi' && discoveryState.poi && (
        <POIDiscoveryOverlay
          poi={discoveryState.poi}
          distance={discoveryState.distance}
          isVisible={discoveryState.isVisible}
          pauseReason="poi"
          remainingTime={discoveryState.remainingTime}
          onExplore={handlePOIExplore}
          onContinue={handleContinue}
          onAddToItinerary={handlePOIAddToItinerary}
          onClose={handleClose}
          isMobile={isMobile}
        />
      )}

      {/* City Approach Overlay */}
      {discoveryState.type === 'city' && discoveryState.cityName && (
        <CityApproachOverlay
          cityName={discoveryState.cityName}
          distance={discoveryState.distance}
          topPOIs={discoveryState.topPOIs || []}
          isVisible={discoveryState.isVisible}
          remainingTime={discoveryState.remainingTime}
          onExploreCity={handleCityExplore}
          onContinue={handleContinue}
          onPOISelect={handleCityPOISelect}
          onClose={handleClose}
          isMobile={isMobile}
        />
      )}
    </div>
  );
};

export default DiscoveryManager;
