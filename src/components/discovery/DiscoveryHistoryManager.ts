/**
 * Discovery History Manager
 * 
 * Tracks and manages the history of POI discoveries during journeys.
 * Provides insights, statistics, and recommendations based on discovery patterns.
 */

import { PointOfInterest } from '@/types/POITypes';
import { Position } from '@/types/Position';

export interface DiscoveryEntry {
  id: string;
  poi: PointOfInterest;
  discoveredAt: Date;
  position: Position;
  distance: number;
  sessionId: string;
  wasExplored: boolean;
  wasAddedToItinerary: boolean;
  timeSpentExploring?: number; // milliseconds
}

export interface SessionStatistics {
  sessionId: string;
  startTime: Date;
  endTime?: Date;
  totalDistance: number;
  poisDiscovered: number;
  poisExplored: number;
  poisAddedToItinerary: number;
  citiesVisited: string[];
  discoveryRate: number; // POIs per km
  explorationRate: number; // % of discovered POIs that were explored
  conversionRate: number; // % of discovered POIs added to itinerary
  averageTimePerPOI: number; // milliseconds
  topCategories: { category: string; count: number }[];
}

export interface TravelInsights {
  totalJourneys: number;
  totalPOIsDiscovered: number;
  favoriteCategories: string[];
  explorationStyle: 'explorer' | 'focused' | 'selective' | 'comprehensive';
  averageDiscoveryRate: number;
  recommendedImprovements: string[];
  personalizedRecommendations: string[];
}

export class DiscoveryHistoryManager {
  private static instance: DiscoveryHistoryManager | null = null;
  
  private discoveryHistory: DiscoveryEntry[] = [];
  private sessionHistory: SessionStatistics[] = [];
  private currentSessionId: string | null = null;
  private currentSessionStart: Date | null = null;
  private explorationStartTimes: Map<string, Date> = new Map();
  
  private readonly STORAGE_KEY_HISTORY = 'travelz_discovery_history';
  private readonly STORAGE_KEY_SESSIONS = 'travelz_session_history';
  private readonly MAX_HISTORY_ENTRIES = 1000;
  private readonly MAX_SESSION_HISTORY = 50;

  private constructor() {
    this.loadFromStorage();
  }

  public static getInstance(): DiscoveryHistoryManager {
    if (!DiscoveryHistoryManager.instance) {
      DiscoveryHistoryManager.instance = new DiscoveryHistoryManager();
    }
    return DiscoveryHistoryManager.instance;
  }

  // =====================================================
  // SESSION MANAGEMENT
  // =====================================================

  /**
   * Start a new discovery session
   */
  public startSession(): string {
    this.currentSessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.currentSessionStart = new Date();
    
    console.log(`[DiscoveryHistory] Started new session: ${this.currentSessionId}`);
    return this.currentSessionId;
  }

  /**
   * End the current discovery session
   */
  public endSession(): SessionStatistics | null {
    if (!this.currentSessionId || !this.currentSessionStart) {
      return null;
    }

    const sessionEntries = this.discoveryHistory.filter(entry => entry.sessionId === this.currentSessionId);
    const endTime = new Date();
    const duration = endTime.getTime() - this.currentSessionStart.getTime();

    // Calculate statistics
    const totalDistance = this.calculateSessionDistance(sessionEntries);
    const poisDiscovered = sessionEntries.length;
    const poisExplored = sessionEntries.filter(entry => entry.wasExplored).length;
    const poisAddedToItinerary = sessionEntries.filter(entry => entry.wasAddedToItinerary).length;
    const citiesVisited = [...new Set(sessionEntries.map(entry => entry.poi.location || 'Unknown'))];
    
    const sessionStats: SessionStatistics = {
      sessionId: this.currentSessionId,
      startTime: this.currentSessionStart,
      endTime,
      totalDistance,
      poisDiscovered,
      poisExplored,
      poisAddedToItinerary,
      citiesVisited,
      discoveryRate: totalDistance > 0 ? poisDiscovered / totalDistance : 0,
      explorationRate: poisDiscovered > 0 ? (poisExplored / poisDiscovered) * 100 : 0,
      conversionRate: poisDiscovered > 0 ? (poisAddedToItinerary / poisDiscovered) * 100 : 0,
      averageTimePerPOI: poisExplored > 0 ? duration / poisExplored : 0,
      topCategories: this.calculateTopCategories(sessionEntries)
    };

    this.sessionHistory.push(sessionStats);
    this.trimSessionHistory();
    this.saveToStorage();

    console.log(`[DiscoveryHistory] Ended session with stats:`, sessionStats);

    // Reset current session
    this.currentSessionId = null;
    this.currentSessionStart = null;

    return sessionStats;
  }

  // =====================================================
  // DISCOVERY TRACKING
  // =====================================================

  /**
   * Record a POI discovery
   */
  public recordDiscovery(poi: PointOfInterest, position: Position, distance: number): void {
    if (!this.currentSessionId) {
      this.startSession();
    }

    const entry: DiscoveryEntry = {
      id: `discovery_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      poi,
      discoveredAt: new Date(),
      position,
      distance,
      sessionId: this.currentSessionId!,
      wasExplored: false,
      wasAddedToItinerary: false
    };

    this.discoveryHistory.push(entry);
    this.trimDiscoveryHistory();
    this.saveToStorage();

    console.log(`[DiscoveryHistory] Recorded discovery: ${poi.name}`);
  }

  /**
   * Record POI exploration start
   */
  public recordExplorationStart(poiId: string): void {
    this.explorationStartTimes.set(poiId, new Date());
  }

  /**
   * Record POI exploration end
   */
  public recordExplorationEnd(poiId: string): void {
    const startTime = this.explorationStartTimes.get(poiId);
    if (!startTime) return;

    const endTime = new Date();
    const timeSpent = endTime.getTime() - startTime.getTime();

    // Update the discovery entry
    const entry = this.discoveryHistory.find(e => e.poi.id === poiId);
    if (entry) {
      entry.wasExplored = true;
      entry.timeSpentExploring = timeSpent;
      this.saveToStorage();
    }

    this.explorationStartTimes.delete(poiId);
    console.log(`[DiscoveryHistory] Recorded exploration: ${poiId} (${timeSpent}ms)`);
  }

  /**
   * Record POI added to itinerary
   */
  public recordAddedToItinerary(poiId: string): void {
    const entry = this.discoveryHistory.find(e => e.poi.id === poiId);
    if (entry) {
      entry.wasAddedToItinerary = true;
      this.saveToStorage();
      console.log(`[DiscoveryHistory] Recorded itinerary addition: ${poiId}`);
    }
  }

  // =====================================================
  // ANALYTICS & INSIGHTS
  // =====================================================

  /**
   * Get current session statistics
   */
  public getCurrentSessionStats(): Partial<SessionStatistics> | null {
    if (!this.currentSessionId || !this.currentSessionStart) {
      return null;
    }

    const sessionEntries = this.discoveryHistory.filter(entry => entry.sessionId === this.currentSessionId);
    const totalDistance = this.calculateSessionDistance(sessionEntries);

    return {
      sessionId: this.currentSessionId,
      startTime: this.currentSessionStart,
      totalDistance,
      poisDiscovered: sessionEntries.length,
      poisExplored: sessionEntries.filter(entry => entry.wasExplored).length,
      poisAddedToItinerary: sessionEntries.filter(entry => entry.wasAddedToItinerary).length,
      citiesVisited: [...new Set(sessionEntries.map(entry => entry.poi.location || 'Unknown'))],
      topCategories: this.calculateTopCategories(sessionEntries)
    };
  }

  /**
   * Generate travel insights based on history
   */
  public generateTravelInsights(): TravelInsights {
    const totalJourneys = this.sessionHistory.length;
    const totalPOIsDiscovered = this.discoveryHistory.length;
    
    // Calculate favorite categories
    const categoryCount = new Map<string, number>();
    this.discoveryHistory.forEach(entry => {
      const category = entry.poi.category || 'Other';
      categoryCount.set(category, (categoryCount.get(category) || 0) + 1);
    });
    
    const favoriteCategories = Array.from(categoryCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([category]) => category);

    // Determine exploration style
    const avgExplorationRate = this.sessionHistory.reduce((sum, session) => sum + session.explorationRate, 0) / Math.max(totalJourneys, 1);
    const avgConversionRate = this.sessionHistory.reduce((sum, session) => sum + session.conversionRate, 0) / Math.max(totalJourneys, 1);
    
    let explorationStyle: 'explorer' | 'focused' | 'selective' | 'comprehensive';
    if (avgExplorationRate > 80 && avgConversionRate > 60) {
      explorationStyle = 'comprehensive';
    } else if (avgExplorationRate > 60) {
      explorationStyle = 'explorer';
    } else if (avgConversionRate > 70) {
      explorationStyle = 'focused';
    } else {
      explorationStyle = 'selective';
    }

    // Calculate average discovery rate
    const avgDiscoveryRate = this.sessionHistory.reduce((sum, session) => sum + session.discoveryRate, 0) / Math.max(totalJourneys, 1);

    // Generate recommendations
    const recommendedImprovements = this.generateRecommendations(avgExplorationRate, avgConversionRate, avgDiscoveryRate);
    const personalizedRecommendations = this.generatePersonalizedRecommendations(favoriteCategories, explorationStyle);

    return {
      totalJourneys,
      totalPOIsDiscovered,
      favoriteCategories,
      explorationStyle,
      averageDiscoveryRate: avgDiscoveryRate,
      recommendedImprovements,
      personalizedRecommendations
    };
  }

  // =====================================================
  // HELPER METHODS
  // =====================================================

  private calculateSessionDistance(entries: DiscoveryEntry[]): number {
    if (entries.length < 2) return 0;
    
    // Simple distance calculation between discovery points
    let totalDistance = 0;
    for (let i = 1; i < entries.length; i++) {
      // This is a simplified calculation - in reality you'd use proper distance calculation
      totalDistance += entries[i].distance;
    }
    return totalDistance;
  }

  private calculateTopCategories(entries: DiscoveryEntry[]): { category: string; count: number }[] {
    const categoryCount = new Map<string, number>();
    entries.forEach(entry => {
      const category = entry.poi.category || 'Other';
      categoryCount.set(category, (categoryCount.get(category) || 0) + 1);
    });
    
    return Array.from(categoryCount.entries())
      .map(([category, count]) => ({ category, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);
  }

  private generateRecommendations(explorationRate: number, conversionRate: number, discoveryRate: number): string[] {
    const recommendations: string[] = [];
    
    if (explorationRate < 40) {
      recommendations.push("Try exploring more POIs when you discover them - you might find hidden gems!");
    }
    
    if (conversionRate < 30) {
      recommendations.push("Consider adding more discovered POIs to your itinerary for a richer travel experience.");
    }
    
    if (discoveryRate < 2) {
      recommendations.push("Slow down during your journey to discover more points of interest along the way.");
    }
    
    return recommendations;
  }

  private generatePersonalizedRecommendations(favoriteCategories: string[], style: string): string[] {
    const recommendations: string[] = [];
    
    if (favoriteCategories.includes('landmark')) {
      recommendations.push("Based on your interest in landmarks, consider exploring UNESCO World Heritage sites.");
    }
    
    if (favoriteCategories.includes('restaurant')) {
      recommendations.push("You seem to enjoy culinary experiences - look for local food markets and cooking classes.");
    }
    
    if (style === 'explorer') {
      recommendations.push("As an explorer, you might enjoy off-the-beaten-path destinations and hidden local spots.");
    }
    
    return recommendations;
  }

  private trimDiscoveryHistory(): void {
    if (this.discoveryHistory.length > this.MAX_HISTORY_ENTRIES) {
      this.discoveryHistory = this.discoveryHistory.slice(-this.MAX_HISTORY_ENTRIES);
    }
  }

  private trimSessionHistory(): void {
    if (this.sessionHistory.length > this.MAX_SESSION_HISTORY) {
      this.sessionHistory = this.sessionHistory.slice(-this.MAX_SESSION_HISTORY);
    }
  }

  private saveToStorage(): void {
    try {
      localStorage.setItem(this.STORAGE_KEY_HISTORY, JSON.stringify(this.discoveryHistory));
      localStorage.setItem(this.STORAGE_KEY_SESSIONS, JSON.stringify(this.sessionHistory));
    } catch (error) {
      console.warn('[DiscoveryHistory] Failed to save to localStorage:', error);
    }
  }

  private loadFromStorage(): void {
    try {
      const historyData = localStorage.getItem(this.STORAGE_KEY_HISTORY);
      const sessionData = localStorage.getItem(this.STORAGE_KEY_SESSIONS);
      
      if (historyData) {
        this.discoveryHistory = JSON.parse(historyData);
      }
      
      if (sessionData) {
        this.sessionHistory = JSON.parse(sessionData);
      }
    } catch (error) {
      console.warn('[DiscoveryHistory] Failed to load from localStorage:', error);
    }
  }

  // =====================================================
  // PUBLIC GETTERS
  // =====================================================

  public getDiscoveryHistory(): DiscoveryEntry[] {
    return [...this.discoveryHistory];
  }

  public getSessionHistory(): SessionStatistics[] {
    return [...this.sessionHistory];
  }

  public getCurrentSessionId(): string | null {
    return this.currentSessionId;
  }
}

export default DiscoveryHistoryManager;
