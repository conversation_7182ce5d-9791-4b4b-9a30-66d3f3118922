import React, { useState, useRef, useEffect } from 'react';
import TopBar from './TopBar';
import LeftPane from './LeftPane';
import RightPane from './RightPane';
import '../../styles/moroccan-theme.css';
import useAnimationManager from '@/hooks/useAnimationManager';
import { JourneyPhase } from '../map/animation/types/travelAnimatorTypes';
import { AnimationEventType } from '@/types/AnimationEventTypes';
import mapboxgl from 'mapbox-gl';
import { PointOfInterest, Destination } from '@/types/POITypes';
import { ChevronRight, ChevronLeft } from 'lucide-react';

interface MoroccanLayoutProps {
  children?: React.ReactNode;
  mapInstance?: mapboxgl.Map | null;
  // Additional props for animation state and controls
  currentRegion?: string;
  availableRegions?: string[];
  onRegionChange?: (region: string) => void;
  isAnimating?: boolean;
  isPaused?: boolean;
  journeyProgress?: number;
  onStartJourney?: () => void;
  onPauseJourney?: () => void;
  itineraryPOIs?: PointOfInterest[];
  // City selection props
  availableCities?: Destination[];
  selectedCities?: Destination[];
  onCitySelect?: (city: Destination) => void;
  onCityDeselect?: (city: Destination) => void;
  // POI data props
  allPOIs?: PointOfInterest[];
  onSelectPOI?: (poi: PointOfInterest) => void;
  // Journey control props
  canBeginJourney?: boolean;
  showBeginJourneyButton?: boolean;
  onBeginJourney?: () => void;
  onAddPOI?: (poi: PointOfInterest) => void;
}

const MoroccanLayout: React.FC<MoroccanLayoutProps> = ({
  children,
  mapInstance,
  currentRegion = 'Morocco',
  availableRegions = ['Morocco'],
  onRegionChange,
  isAnimating = false,
  isPaused = false,
  journeyProgress = 0,
  onStartJourney,
  onPauseJourney,
  itineraryPOIs = [],
  availableCities = [],
  selectedCities = [],
  onCitySelect,
  onCityDeselect,
  allPOIs = [],
  onSelectPOI,
  canBeginJourney = false,
  showBeginJourneyButton = false,
  onBeginJourney,
  onAddPOI
}) => {
  // Minimum POIs needed to start journey
  const MIN_POIS_REQUIRED = 2;
  
  // Check if we have enough POIs selected to start journey
  const canStartJourney = itineraryPOIs.length >= MIN_POIS_REQUIRED;
  
  // Left pane visibility state
  const [showLeftPane, setShowLeftPane] = useState(true);
  
  // Right pane visibility state
  const [showRightPane, setShowRightPane] = useState(true);
  
  // Theme state (light/dark)
  const [isDarkTheme, setIsDarkTheme] = useState(false);
  
  // Toggle theme
  const toggleTheme = () => {
    setIsDarkTheme(!isDarkTheme);
    // Here you would apply actual theme changes to the app
  };
  
  // Mobile menu visibility
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  
  // Toggle left pane
  const toggleLeftPane = () => {
    setShowLeftPane(!showLeftPane);
  };
  
  // Toggle right pane
  const toggleRightPane = () => {
    setShowRightPane(!showRightPane);
  };
  
  // Toggle mobile menu
  const toggleMobileMenu = () => {
    setShowMobileMenu(!showMobileMenu);
  };
  
  return (
    <div className={`moroccan-layout ${isDarkTheme ? 'dark-theme' : 'light-theme'}`}>
      {/* Top navigation bar */}
      <TopBar
        currentRegion={currentRegion}
        availableRegions={availableRegions}
        onRegionChange={onRegionChange}
        isAnimating={isAnimating}
        onToggleTheme={toggleTheme}
        onStartJourney={canStartJourney ? onStartJourney : undefined}
        onPauseJourney={onPauseJourney}
        journeyProgress={journeyProgress}
        availableCities={availableCities}
        selectedCities={selectedCities}
        onCitySelect={onCitySelect}
        onCityDeselect={onCityDeselect}
        canBeginJourney={canBeginJourney}
        showBeginJourneyButton={showBeginJourneyButton}
        onBeginJourney={onBeginJourney}
      />
      
      {/* Main content area with map and panes */}
      <div className="main-content">
        {/* Left pane */}
        <div 
          className={`left-pane-wrapper ${showLeftPane ? 'visible' : 'hidden'}`}
          style={{ 
            width: '300px', 
            zIndex: 20, 
            boxShadow: '0 4px 10px rgba(0, 0, 0, 0.1)'
          }}
        >
          <LeftPane
            isAnimating={isAnimating}
            onTogglePane={toggleLeftPane}
            selectedPOIs={itineraryPOIs}
            canStartJourney={canStartJourney}
            minPoisRequired={MIN_POIS_REQUIRED}
            allPois={allPOIs}
            onSelectPOI={onSelectPOI}
            onAddPOI={onAddPOI}
          />
          
          {/* Left pane toggle button */}
          <button 
            onClick={toggleLeftPane}
            className="pane-toggle-button left"
            style={{
              position: 'absolute',
              top: '50%',
              right: '-12px',
              transform: 'translateY(-50%)',
              backgroundColor: 'white',
              border: '1px solid var(--border-light)',
              borderRadius: '50%',
              width: '24px',
              height: '24px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              zIndex: 30,
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}
            aria-label={showLeftPane ? 'Hide left panel' : 'Show left panel'}
          >
            <ChevronLeft size={16} color="var(--morocco-red)" style={{ transform: showLeftPane ? 'rotate(180deg)' : 'none' }} />
          </button>
        </div>
        
        {/* Map container */}
        <div 
          className="map-container"
          style={{ 
            flex: 1, 
            position: 'relative', 
            zIndex: 5 
          }}
        >
          {children}
          {/* Vehicle marker container - required for animation system */}
          <div 
            id="vehicle-marker-container"
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              pointerEvents: 'none',
              zIndex: 1000
            }}
          />
        </div>
        
        {/* Right pane */}
        <div 
          className={`right-pane-wrapper ${showRightPane ? 'visible' : 'hidden'}`}
          style={{ 
            width: '320px', 
            zIndex: 20,
            boxShadow: '0 4px 10px rgba(0, 0, 0, 0.1)'
          }}
        >
          <RightPane
            isAnimating={isAnimating}
            isPaused={isPaused}
            onTogglePane={toggleRightPane}
            journeyProgress={journeyProgress}
            selectedCities={selectedCities}
            itineraryPOIs={itineraryPOIs}
            tripDuration={8}
            onTripDurationChange={(days) => console.log('Trip duration changed to:', days)}
            onRemovePOI={(poi) => console.log('Remove POI:', poi.name)}
            onRemoveCity={(city) => console.log('Remove city:', city.name)}
            onExportItinerary={() => console.log('Export itinerary')}
          />
          
          {/* Right pane toggle button */}
          <button 
            onClick={toggleRightPane}
            className="pane-toggle-button right"
            style={{
              position: 'absolute',
              top: '50%',
              left: '-12px',
              transform: 'translateY(-50%)',
              backgroundColor: 'white',
              border: '1px solid var(--border-light)',
              borderRadius: '50%',
              width: '24px',
              height: '24px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              zIndex: 30,
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}
            aria-label={showRightPane ? 'Hide right panel' : 'Show right panel'}
          >
            <ChevronRight size={16} color="var(--morocco-blue)" style={{ transform: showRightPane ? 'rotate(180deg)' : 'none' }} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default MoroccanLayout;