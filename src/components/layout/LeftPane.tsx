import React, { useState, useMemo, useEffect } from 'react';
import { PointOfInterest, POICategory } from '@/types/POITypes'; // Direct import from POITypes with correct path
import { TravelInterest } from '@/types/ItineraryParameters';
import CurrentAreaHeader from '../leftpane/CurrentAreaHeader';
import POIFilters, { CATEGORIES } from '../leftpane/POIFilters'; // Assuming CATEGORIES is exported from POIFilters
import POIList from '../leftpane/POIList';
import { ScrollArea } from '@/components/ui/scroll-area'; // Assuming this path is correct
import { MapPin, Filter, Clock, Star, DollarSign, Hash, Tag, Heart } from 'lucide-react';


// Extending PointOfInterest for mock data with optional clientId
interface ExtendedPointOfInterest extends PointOfInterest {
  clientId?: string;
}

// Updated categories for Discovery/Exploration Focus
const POI_CATEGORIES = [
  { id: 'landmark', label: 'Historic Landmarks', icon: <Star size={12} className="mr-1" /> },
  { id: 'nature', label: 'Natural Wonders', icon: <Mountain size={12} className="mr-1" /> },
  { id: 'cultural', label: 'Cultural Sites', icon: <Landmark size={12} className="mr-1" /> },
  { id: 'adventure', label: 'Adventure Activities', icon: <Mountain size={12} className="mr-1" /> },
  { id: 'scenic', label: 'Scenic Routes', icon: <Star size={12} className="mr-1" /> },
  { id: 'photography', label: 'Photo Spots', icon: <Star size={12} className="mr-1" /> },
  { id: 'hidden-gem', label: 'Hidden Gems', icon: <Star size={12} className="mr-1" /> },
  { id: 'local-experience', label: 'Local Experiences', icon: <Utensils size={12} className="mr-1" /> }
];

// Import missing icons
import { Landmark, Mountain, Utensils, Plus, Minus, ChevronDown, ChevronUp } from 'lucide-react';

// Travel Interests for the filter section
const TRAVEL_INTERESTS: { value: TravelInterest; label: string; icon: string }[] = [
  { value: 'landmarks', label: 'Historic Landmarks', icon: '🏛️' },
  { value: 'nature', label: 'Natural Wonders', icon: '🏔️' },
  { value: 'culture', label: 'Cultural Sites', icon: '🎭' },
  { value: 'adventure', label: 'Adventure', icon: '🧗' },
  { value: 'photography', label: 'Photography', icon: '📸' },
  { value: 'architecture', label: 'Architecture', icon: '🏗️' },
  { value: 'local-experience', label: 'Local Experiences', icon: '🎪' },
  { value: 'hidden-gems', label: 'Hidden Gems', icon: '💎' },
  { value: 'scenic-drives', label: 'Scenic Drives', icon: '🛣️' },
  { value: 'viewpoints', label: 'Viewpoints', icon: '👁️' }
];

// Sample POIs for development/testing if not passed via props
const MOCK_POIS: ExtendedPointOfInterest[] = [
  { 
    id: 'poi1', 
    name: 'Ait Benhaddou Kasbah', 
    category: 'landmark' as POICategory, 
    position: { lat: 31.0478, lng: -7.1319 }, 
    coordinates: [-7.1319, 31.0478],
    description: 'UNESCO World Heritage site and stunning example of Moroccan earthen clay architecture', 
    clientId: 'client1',
    images: [],
    tags: [] 
  },
  { 
    id: 'poi2', 
    name: 'Marrakech Medina', 
    category: 'cultural' as POICategory, 
    position: { lat: 31.6295, lng: -7.9811 }, 
    coordinates: [-7.9811, 31.6295],
    description: 'Historic city center with vibrant souks and traditional Moroccan crafts', 
    clientId: 'client1',
    images: [],
    tags: [] 
  },
  { 
    id: 'poi3', 
    name: 'Atlas Coffee House', 
    category: 'restaurant' as POICategory, // Changed from 'cafe' to 'restaurant'
    position: { lat: 31.1345, lng: -7.9177 }, 
    coordinates: [-7.9177, 31.1345],
    description: 'Traditional Moroccan coffee and mint tea with mountain views', 
    clientId: 'generic',
    images: [],
    tags: [] 
  },
  { 
    id: 'poi4', 
    name: 'Riad Fes', 
    category: 'hotel' as POICategory, 
    position: { lat: 34.0349, lng: -4.9998 }, 
    coordinates: [-4.9998, 34.0349],
    description: 'Luxury riad with authentic Moroccan architecture and decor', 
    clientId: 'client2',
    images: [],
    tags: [] 
  },
  { 
    id: 'poi5', 
    name: 'Bab Mansour', 
    category: 'landmark' as POICategory, 
    position: { lat: 33.8935, lng: -5.5721 }, 
    coordinates: [-5.5721, 33.8935],
    description: 'One of the most beautiful gates in Morocco, located in Meknes', 
    clientId: 'generic',
    images: [],
    tags: [] 
  },
  { 
    id: 'poi6', 
    name: 'Berber Village', 
    category: 'cultural' as POICategory, 
    position: { lat: 31.2061, lng: -7.8775 }, 
    coordinates: [-7.8775, 31.2061],
    description: 'Traditional indigenous village showcasing local culture', 
    clientId: 'client1',
    images: [],
    tags: [] 
  },
  { 
    id: 'poi7', 
    name: 'Majorelle Garden', 
    category: 'nature' as POICategory, // Changed from 'natural' to 'nature'
    position: { lat: 31.6423, lng: -8.0019 }, 
    coordinates: [-8.0019, 31.6423],
    description: 'Stunning botanical garden with vibrant blue structures', 
    clientId: 'generic',
    images: [],
    tags: [] 
  },
];

interface LeftPaneProps {
  allPois?: PointOfInterest[]; // Changed to use PointOfInterest directly
  currentAreaName?: string;
  isAnimating?: boolean; // Flag to indicate animation is running
  onTogglePane?: () => void; // Callback to toggle panel visibility
  selectedPOIs?: PointOfInterest[]; // Selected POIs for itinerary
  canStartJourney?: boolean; // Whether we can start the journey
  minPoisRequired?: number; // Minimum POIs required to start
  onSelectPOI?: (poi: PointOfInterest) => void; // Callback for POI selection
  onAddPOI?: (poi: PointOfInterest) => void; // Callback for adding POI to trip
  // Travel Interests props
  selectedInterests?: TravelInterest[];
  onInterestToggle?: (interest: TravelInterest) => void;
  // Mobile city selection props
  isMobile?: boolean;
  availableCities?: any[]; // Destination type
  selectedCities?: any[]; // Destination type
  onCitySelect?: (city: any) => void;
  onCityDeselect?: (city: any) => void;
  cityDayAllocations?: Record<string, number>;
  onCityDayAllocationChange?: (cityName: string, days: number) => void;
  // AI-related props
  enableAI?: boolean;
  userInterests?: string[];
  travelStyle?: 'luxury' | 'budget' | 'adventure' | 'cultural' | 'romantic';
  tripDuration?: number;
  onAICitiesSelected?: (cities: Destination[]) => void;
  onAIPOIsSelected?: (pois: POI[]) => void;
  onAIItineraryGenerated?: (itinerary: any[]) => void;
  // onAddToItinerary and onShowPoidetail would be actual props from parent
}

const LeftPane: React.FC<LeftPaneProps> = (props) => {
  const {
    allPois = MOCK_POIS,
    currentAreaName,
    isAnimating = false,
    onTogglePane,
    selectedPOIs = [],
    canStartJourney = false,
    minPoisRequired = 2,
    onSelectPOI,
    onAddPOI,
    selectedInterests = [],
    onInterestToggle,
    // Mobile city selection props
    isMobile = false,
    availableCities = [],
    selectedCities = [],
    onCitySelect,
    onCityDeselect,
    cityDayAllocations = {},
    onCityDayAllocationChange,
    // AI props
    enableAI = false,
    userInterests = [],
    travelStyle = 'cultural',
    tripDuration = 7,
    onAICitiesSelected,
    onAIPOIsSelected,
    onAIItineraryGenerated
  } = props;

  // Debug logging disabled for production readiness
  // useEffect(() => {
  //   console.log('LeftPane component rendered');
  // }, []);

  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [activeFilter, setActiveFilter] = useState<'all' | 'popular' | 'nearby'>('all');
  const [itineraryPoiIds, setItineraryPoiIds] = useState<Set<string>>(new Set());
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 5]);
  const [minRating, setMinRating] = useState(0);

  // Smart collapsing state for mobile
  const [showFilters, setShowFilters] = useState(false);
  const [showCitySelection, setShowCitySelection] = useState(true);

  // Smart collapsing logic:
  // - When no cities selected: show city selection expanded, filters collapsed
  // - When cities selected: show POI list expanded, city selection collapsed, filters collapsed until user expands
  useEffect(() => {
    if (isMobile) {
      if (selectedCities.length === 0) {
        setShowCitySelection(true);
        setShowFilters(false);
      } else {
        setShowCitySelection(false);
        setShowFilters(false); // Keep collapsed until user decides to filter
      }
    }
  }, [isMobile, selectedCities.length]);

  // Update itinerary POI IDs from prop
  useEffect(() => {
    if (selectedPOIs && selectedPOIs.length > 0) {
      setItineraryPoiIds(new Set(selectedPOIs.map(poi => poi.id)));
    }
  }, [selectedPOIs]);

  const handleCategoryChange = (newCategories: string[]) => {
    setSelectedCategories(newCategories);
  };

  const handleAddToItinerary = (poi: PointOfInterest) => {
    const isCurrentlySelected = itineraryPoiIds.has(poi.id);

    setItineraryPoiIds(prevIds => {
      const newIds = new Set(prevIds);
      if (newIds.has(poi.id)) {
        newIds.delete(poi.id);
      } else {
        newIds.add(poi.id);
      }
      return newIds;
    });

    // Call the parent callback if provided
    if (onAddPOI && !isCurrentlySelected) {
      onAddPOI(poi);
    }

    console.log('Itinerary updated:', poi.name, !isCurrentlySelected ? 'added' : 'removed');
  };

  // Helper function to determine if POI is "Our Selection"
  const isOurSelection = (poiId: string) => {
    // Top POIs that are "Our Selection"
    return ['poi1', 'poi2', 'poi7', 'poi5'].includes(poiId);
  };

  // Helper function to get mock rating for POI
  const getPOIRating = (poiId: string) => {
    const ratings: { [key: string]: number } = {
      'poi1': 4.8,
      'poi2': 4.9,
      'poi3': 4.2,
      'poi4': 4.6,
      'poi5': 4.7,
      'poi6': 4.4,
      'poi7': 4.9
    };
    return ratings[poiId] || 4.0;
  };

  const handlePoiSelect = (poi: PointOfInterest) => {
    console.log('Selected POI (for detail view):', poi);
    if (onSelectPOI) {
      onSelectPOI(poi);
    }
  };

  const toggleAdvancedFilters = () => {
    setShowAdvancedFilters(!showAdvancedFilters);
  };

  const filteredPois = useMemo(() => {
    if (!allPois) return [];

    console.log('Filtering POIs:', {
      totalPOIs: allPois.length,
      selectedCategories,
      activeFilter,
      showAdvancedFilters,
      priceRange,
      minRating
    });

    return allPois.filter(poi => {
      // Category filter - handle both old and new category formats
      if (selectedCategories.length > 0) {
        const poiCategory = poi.category || '';
        const poiType = (poi as any).type || '';

        // Check if POI matches any selected category
        const matchesCategory = selectedCategories.some(selectedCat => {
          // Direct match
          if (poiCategory === selectedCat || poiType === selectedCat) {
            return true;
          }

          // Map legacy types to new categories
          const categoryMappings: Record<string, string[]> = {
            'landmark': ['landmark', 'monument', 'historic'],
            'nature': ['nature', 'scenic', 'viewpoint'],
            'cultural': ['cultural', 'museum', 'art'],
            'adventure': ['adventure', 'activity', 'hiking'],
            'scenic': ['scenic', 'viewpoint', 'photography'],
            'photography': ['photography', 'scenic', 'viewpoint'],
            'hidden-gem': ['hidden-gem', 'local'],
            'local-experience': ['local-experience', 'market', 'restaurant', 'shopping']
          };

          return categoryMappings[selectedCat]?.includes(poiCategory) ||
                 categoryMappings[selectedCat]?.includes(poiType);
        });

        if (!matchesCategory) {
          return false;
        }
      }

      // Filter type (all, popular, nearby)
      if (activeFilter === 'popular') {
        // Use rating or specific POI IDs for popular filter
        const rating = poi.rating || getPOIRating(poi.id);
        return rating >= 4.5 || ['jardin-majorelle', 'koutoubia-mosque', 'ait-benhaddou'].includes(poi.id);
      } else if (activeFilter === 'nearby') {
        // For demo, use specific POI IDs or tags
        return poi.tags?.includes('nearby') || ['test-poi-1', 'test-poi-2', 'test-poi-3'].includes(poi.id);
      }

      // Advanced filters
      if (showAdvancedFilters) {
        // Price filter (if applicable)
        if (poi.cost !== undefined) {
          if (poi.cost < priceRange[0] || poi.cost > priceRange[1]) {
            return false;
          }
        }

        // Rating filter (if applicable)
        const rating = poi.rating || getPOIRating(poi.id);
        if (rating < minRating) {
          return false;
        }
      }

      return true;
    });
  }, [allPois, selectedCategories, activeFilter, showAdvancedFilters, priceRange, minRating]);



  // Don't show left pane during animation
  if (isAnimating) {
    return null;
  }

  return (
    <div
      className="left-pane morocco-pattern-2"
      style={{
        width: '100%', // Take full width of container
        backgroundColor: 'var(--morocco-sand-lightest)',
        height: '100vh',
        borderRight: '1px solid var(--border-light)',
        display: 'flex',
        flexDirection: 'column',
        boxShadow: 'var(--shadow-soft)',
        zIndex: 10,
        position: 'relative',
      }}
    >
      {/* Mobile City Selection Section */}
      {isMobile && (
        <div style={{ borderBottom: '1px solid var(--border-light)' }}>
          {/* City Selection Header */}
          <button
            onClick={() => setShowCitySelection(!showCitySelection)}
            style={{
              width: '100%',
              padding: '0.75rem 1rem',
              border: 'none',
              background: 'none',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              cursor: 'pointer',
              fontSize: '0.875rem',
              fontWeight: 600,
              color: 'var(--morocco-blue)',
              fontFamily: 'var(--font-body)'
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              <MapPin size={16} />
              <span>Cities ({selectedCities.length})</span>
            </div>
            {showCitySelection ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
          </button>

          {/* City Selection Content */}
          {showCitySelection && (
            <div style={{ padding: '0 1rem 0.75rem' }}>
              <div style={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: '0.5rem',
                marginBottom: '0.75rem'
              }}>
                {availableCities.map(city => {
                  const isSelected = selectedCities.some(c => c.id === city.id);
                  const selectionOrder = selectedCities.findIndex(c => c.id === city.id) + 1;
                  const cityDays = cityDayAllocations[city.name] || 2;

                  return (
                    <div key={city.id} style={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '0.25rem',
                      alignItems: 'center'
                    }}>
                      <button
                        onClick={() => {
                          if (isSelected) {
                            onCityDeselect && onCityDeselect(city);
                          } else {
                            onCitySelect && onCitySelect(city);
                          }
                        }}
                        style={{
                          padding: '0.375rem 0.75rem',
                          border: isSelected ? '2px solid var(--morocco-blue)' : '1px solid rgba(0, 71, 171, 0.2)',
                          borderRadius: 'var(--radius-full)',
                          background: isSelected ? 'rgba(0, 71, 171, 0.1)' : 'white',
                          cursor: 'pointer',
                          fontSize: '0.75rem',
                          fontWeight: isSelected ? 600 : 500,
                          color: isSelected ? 'var(--morocco-blue)' : 'var(--text-secondary)',
                          textAlign: 'center',
                          transition: 'all 0.2s ease',
                          position: 'relative',
                          textTransform: 'uppercase',
                          letterSpacing: '0.5px',
                          whiteSpace: 'nowrap'
                        }}
                      >
                        {city.name}
                        {isSelected && (
                          <div style={{
                            position: 'absolute',
                            top: '-4px',
                            right: '-4px',
                            width: '16px',
                            height: '16px',
                            backgroundColor: 'var(--morocco-red)',
                            borderRadius: '50%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            border: '2px solid white',
                            boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
                          }}>
                            <span style={{
                              color: 'white',
                              fontSize: '0.625rem',
                              fontWeight: 700,
                              fontFamily: 'var(--font-heading)'
                            }}>
                              {selectionOrder}
                            </span>
                          </div>
                        )}
                      </button>

                      {/* Day Allocation Controls - Only show for selected cities */}
                      {isSelected && onCityDayAllocationChange && (
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.25rem',
                          backgroundColor: 'rgba(255, 255, 255, 0.9)',
                          borderRadius: 'var(--radius-sm)',
                          padding: '0.25rem 0.5rem',
                          border: '1px solid rgba(0, 71, 171, 0.2)'
                        }}>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              if (cityDays > 1) {
                                onCityDayAllocationChange(city.name, cityDays - 1);
                              }
                            }}
                            style={{
                              width: '16px',
                              height: '16px',
                              borderRadius: '50%',
                              border: '1px solid var(--morocco-blue)',
                              backgroundColor: 'white',
                              color: 'var(--morocco-blue)',
                              cursor: 'pointer',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              fontSize: '10px'
                            }}
                          >
                            <Minus size={8} />
                          </button>
                          <span style={{
                            fontSize: '0.7rem',
                            fontWeight: 600,
                            color: 'var(--morocco-blue)',
                            minWidth: '20px',
                            textAlign: 'center'
                          }}>
                            {cityDays}d
                          </span>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              if (cityDays < 15) {
                                onCityDayAllocationChange(city.name, cityDays + 1);
                              }
                            }}
                            style={{
                              width: '16px',
                              height: '16px',
                              borderRadius: '50%',
                              border: '1px solid var(--morocco-blue)',
                              backgroundColor: 'white',
                              color: 'var(--morocco-blue)',
                              cursor: 'pointer',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              fontSize: '10px'
                            }}
                          >
                            <Plus size={8} />
                          </button>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>

              {selectedCities.length === 0 && (
                <p style={{
                  fontSize: '0.75rem',
                  color: 'var(--text-secondary)',
                  textAlign: 'center',
                  margin: 0,
                  fontStyle: 'italic'
                }}>
                  Select cities to explore their points of interest
                </p>
              )}
            </div>
          )}
        </div>
      )}

      {/* Compact Filter Tabs */}
      <div style={{ padding: '0.5rem 1rem', borderBottom: '1px solid var(--border-light)' }}>
        <div
          style={{
            display: 'flex',
            gap: '0.25rem',
            background: 'rgba(209, 196, 169, 0.15)',
            padding: '0.125rem',
            borderRadius: 'var(--radius-full)'
          }}
        >
          <button
            key="all-btn"
            onClick={() => setActiveFilter('all')}
            style={{
              flex: 1,
              padding: '0.375rem 0.75rem',
              border: 'none',
              borderRadius: 'var(--radius-full)',
              fontFamily: 'var(--font-body)',
              fontWeight: 600,
              fontSize: '0.75rem',
              cursor: 'pointer',
              backgroundColor: activeFilter === 'all' ? 'white' : 'transparent',
              color: activeFilter === 'all' ? 'var(--morocco-red)' : 'var(--text-secondary)',
              boxShadow: activeFilter === 'all' ? 'var(--shadow-soft)' : 'none',
              transition: 'all 0.2s ease',
              textTransform: 'uppercase',
              letterSpacing: '0.5px'
            }}
          >
            All
          </button>
          <button
            key="popular-btn"
            onClick={() => setActiveFilter('popular')}
            style={{
              flex: 1,
              padding: '0.375rem 0.75rem',
              border: 'none',
              borderRadius: 'var(--radius-full)',
              fontFamily: 'var(--font-body)',
              fontWeight: 600,
              fontSize: '0.75rem',
              cursor: 'pointer',
              backgroundColor: activeFilter === 'popular' ? 'white' : 'transparent',
              color: activeFilter === 'popular' ? 'var(--morocco-red)' : 'var(--text-secondary)',
              boxShadow: activeFilter === 'popular' ? 'var(--shadow-soft)' : 'none',
              transition: 'all 0.2s ease',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '0.25rem',
              textTransform: 'uppercase',
              letterSpacing: '0.5px'
            }}
          >
            <Star size={12} fill={activeFilter === 'popular' ? 'currentColor' : 'none'} />
            Top
          </button>
          <button
            key="nearby-btn"
            onClick={() => setActiveFilter('nearby')}
            style={{
              flex: 1,
              padding: '0.375rem 0.75rem',
              border: 'none',
              borderRadius: 'var(--radius-full)',
              fontFamily: 'var(--font-body)',
              fontWeight: 600,
              fontSize: '0.75rem',
              cursor: 'pointer',
              backgroundColor: activeFilter === 'nearby' ? 'white' : 'transparent',
              color: activeFilter === 'nearby' ? 'var(--morocco-red)' : 'var(--text-secondary)',
              boxShadow: activeFilter === 'nearby' ? 'var(--shadow-soft)' : 'none',
              transition: 'all 0.2s ease',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '0.25rem',
              textTransform: 'uppercase',
              letterSpacing: '0.5px'
            }}
          >
            <MapPin size={12} />
            Near
          </button>
        </div>
      </div>

      {/* Travel Interests Section */}
      <div style={{ borderBottom: '1px solid var(--border-light)' }}>
        {/* Travel Interests Header - FIXED: No nested buttons */}
        <div style={{
          width: '100%',
          padding: '0.75rem 1rem',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          {/* Left side - Title (clickable on mobile) */}
          <div
            onClick={() => isMobile ? setShowFilters(!showFilters) : undefined}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              cursor: isMobile ? 'pointer' : 'default',
              flex: 1
            }}
          >
            <h3
              style={{
                margin: 0,
                fontSize: '0.875rem',
                fontWeight: 600,
                color: 'var(--text-secondary)',
                fontFamily: 'var(--font-body)',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}
            >
              <Heart size={14} color="var(--morocco-red)" />
              Travel Interests
            </h3>
            {isMobile && (
              showFilters ? <ChevronUp size={16} color="var(--morocco-blue)" /> : <ChevronDown size={16} color="var(--morocco-blue)" />
            )}
          </div>

          {/* Right side - More Filters button (desktop only) */}
          {!isMobile && (
            <button
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.25rem',
                background: 'none',
                border: 'none',
                fontSize: '0.75rem',
                color: 'var(--morocco-blue)',
                cursor: 'pointer',
                fontFamily: 'var(--font-body)',
                fontWeight: 500
              }}
              onClick={toggleAdvancedFilters}
            >
              <Filter size={14} />
              {showAdvancedFilters ? 'Hide Filters' : 'More Filters'}
            </button>
          )}
        </div>

        {/* Travel Interests Content - Show/Hide based on mobile state */}
        {(!isMobile || showFilters) && (
          <div style={{ padding: '0 1rem 0.75rem' }}>
            {/* Travel Interests Pills */}
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.5rem', marginBottom: '0.75rem' }}>
              {TRAVEL_INTERESTS.map((interest) => (
                <button
                  key={interest.value}
                  onClick={() => onInterestToggle && onInterestToggle(interest.value)}
                  style={{
                    padding: '0.25rem 0.75rem',
                    borderRadius: 'var(--radius-full)',
                    border: 'none',
                    backgroundColor: selectedInterests.includes(interest.value)
                      ? 'var(--morocco-blue)'
                      : 'rgba(0, 71, 171, 0.1)',
                    color: selectedInterests.includes(interest.value)
                      ? 'white'
                      : 'var(--morocco-blue)',
                    fontFamily: 'var(--font-body)',
                    fontSize: '0.75rem',
                    fontWeight: 500,
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.25rem'
                  }}
                >
                  <span>{interest.icon}</span>
                  {interest.label}
                </button>
              ))}
              {selectedInterests.length > 0 && (
                <button
                  key="clear-interests"
                  onClick={() => selectedInterests.forEach(interest => onInterestToggle && onInterestToggle(interest))}
                  className="text-xs text-morocco-blue hover:underline"
                  style={{ fontSize: '0.75rem', color: 'var(--morocco-blue)' }}
                >
                  Clear All
                </button>
              )}
            </div>

            {/* Advanced Filters for Mobile */}
            {isMobile && (
              <button
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.25rem',
                  background: 'none',
                  border: 'none',
                  fontSize: '0.75rem',
                  color: 'var(--morocco-blue)',
                  cursor: 'pointer',
                  fontFamily: 'var(--font-body)',
                  fontWeight: 500,
                  width: '100%',
                  justifyContent: 'center'
                }}
                onClick={toggleAdvancedFilters}
              >
                <Filter size={14} />
                {showAdvancedFilters ? 'Hide Advanced Filters' : 'Show Advanced Filters'}
              </button>
            )}

            {/* Advanced Filters */}
            {showAdvancedFilters && (
              <div style={{ marginTop: '1rem', padding: '0.75rem', backgroundColor: 'rgba(209, 196, 169, 0.1)', borderRadius: 'var(--radius-md)' }}>
                <h4 style={{ margin: '0 0 0.75rem', fontSize: '0.75rem', fontWeight: 600, color: 'var(--text-secondary)' }}>
                  Advanced Filters
                </h4>

                {/* Price Range Filter */}
                <div style={{ marginBottom: '0.75rem' }}>
                  <label style={{ display: 'flex', alignItems: 'center', gap: '0.25rem', fontSize: '0.75rem', marginBottom: '0.25rem' }}>
                    <DollarSign size={12} /> Price Range
                  </label>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                    <input
                      type="range"
                      min="0"
                      max="5"
                      value={priceRange[1]}
                      onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}
                      style={{ flex: 1 }}
                    />
                    <span style={{ fontSize: '0.75rem' }}>
                      {priceRange[1] === 0 ? 'Free' : '$'.repeat(priceRange[1])}
                    </span>
                  </div>
                </div>

                {/* Minimum Rating Filter */}
                <div>
                  <label style={{ display: 'flex', alignItems: 'center', gap: '0.25rem', fontSize: '0.75rem', marginBottom: '0.25rem' }}>
                    <Star size={12} /> Minimum Rating
                  </label>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                    <input
                      type="range"
                      min="0"
                      max="5"
                      step="0.5"
                      value={minRating}
                      onChange={(e) => setMinRating(parseFloat(e.target.value))}
                      style={{ flex: 1 }}
                    />
                    <span style={{ fontSize: '0.75rem' }}>
                      {minRating} ★
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>



      {/* POI List with Morocco styling */}
      <ScrollArea
        className="morocco-scrollbar"
        style={{
          flex: 1,
          overflowY: 'auto',
          padding: '0.75rem 0'
        }}
      >
        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem', padding: '0 1rem' }}>
          {filteredPois.length > 0 ? (
            filteredPois.map((poi) => (
              <div
                key={poi.id}
                className="morocco-card"
                style={{
                  padding: '0.5rem 0.75rem',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  position: 'relative',
                  overflow: 'hidden',
                  border: itineraryPoiIds.has(poi.id)
                    ? '2px solid var(--morocco-red)'
                    : '1px solid var(--border-light)',
                  backgroundColor: itineraryPoiIds.has(poi.id)
                    ? 'rgba(139, 26, 24, 0.05)'
                    : 'white',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  gap: '0.75rem'
                }}
                onClick={() => handlePoiSelect(poi)}
              >
                {/* Left Side - POI Info */}
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem',
                  flex: 1,
                  minWidth: 0 // Allow text truncation
                }}>
                  {/* POI Title */}
                  <h3
                    style={{
                      margin: 0,
                      fontFamily: 'var(--font-display)',
                      fontSize: '0.875rem',
                      fontWeight: 600,
                      color: 'var(--text-primary)',
                      flex: 1,
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis'
                    }}
                  >
                    {poi.name}
                  </h3>

                  {/* Our Selection Badge */}
                  {isOurSelection(poi.id) && (
                    <div
                      style={{
                        padding: '0.125rem 0.375rem',
                        borderRadius: 'var(--radius-sm)',
                        backgroundColor: 'var(--morocco-yellow)',
                        color: 'var(--morocco-blue)',
                        fontSize: '0.625rem',
                        fontWeight: 700,
                        textTransform: 'uppercase',
                        letterSpacing: '0.5px',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.25rem',
                        flexShrink: 0
                      }}
                    >
                      <Star size={8} fill="currentColor" />
                      Our
                    </div>
                  )}

                  {/* Star Rating */}
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.25rem',
                      fontSize: '0.75rem',
                      color: 'var(--morocco-yellow)',
                      flexShrink: 0
                    }}
                  >
                    <Star size={12} fill="currentColor" />
                    <span style={{
                      color: 'var(--text-primary)',
                      fontWeight: 600,
                      fontSize: '0.75rem'
                    }}>
                      {getPOIRating(poi.id)}
                    </span>
                  </div>
                </div>

                {/* Right Side - Add Button */}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAddToItinerary(poi);
                  }}
                  style={{
                    padding: '0.25rem 0.5rem',
                    fontSize: '0.75rem',
                    fontWeight: 600,
                    border: itineraryPoiIds.has(poi.id)
                      ? '1px solid var(--morocco-red)'
                      : '1px solid var(--morocco-blue)',
                    backgroundColor: itineraryPoiIds.has(poi.id)
                      ? 'white'
                      : 'var(--morocco-blue)',
                    color: itineraryPoiIds.has(poi.id)
                      ? 'var(--morocco-red)'
                      : 'white',
                    borderRadius: 'var(--radius-sm)',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                    minWidth: '50px',
                    flexShrink: 0
                  }}
                  onMouseEnter={(e) => {
                    if (!itineraryPoiIds.has(poi.id)) {
                      e.currentTarget.style.backgroundColor = 'var(--morocco-red)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!itineraryPoiIds.has(poi.id)) {
                      e.currentTarget.style.backgroundColor = 'var(--morocco-blue)';
                    }
                  }}
                >
                  {itineraryPoiIds.has(poi.id) ? '✓' : 'Add'}
                </button>
              </div>
            ))
          ) : (
            <div 
              style={{ 
                padding: '2rem 1rem', 
                textAlign: 'center',
                color: 'var(--text-secondary)',
                fontSize: '0.875rem'
              }}
            >
              No points of interest match your filters.
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Itinerary Summary Footer */}
      <div 
        style={{
          padding: '0.75rem 1rem', 
          borderTop: '1px solid var(--border-light)', 
          backgroundColor: 'rgba(209, 196, 169, 0.1)',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}
      >
        <p 
          style={{
            fontSize: '0.875rem', 
            color: 'var(--text-secondary)', 
            margin: 0,
            fontFamily: 'var(--font-body)'
          }}
        >
          {itineraryPoiIds.size} item{itineraryPoiIds.size !== 1 ? 's' : ''} in itinerary
        </p>
        
        {itineraryPoiIds.size > 0 && (
          <button 
            key="view-itinerary-button"
            className="morocco-button secondary"
            style={{
              padding: '0.25rem 0.75rem',
              fontSize: '0.75rem'
            }}
          >
            View Itinerary
          </button>
        )}
      </div>
    </div>
  );
};

export default LeftPane;
