import React, { useState } from 'react';
import { Sun, Moon, Info, Menu, X, Globe, ChevronDown, MapPin, Search, Calendar, Clock, Route, Filter, Star, Palette } from 'lucide-react';
import '../../styles/moroccan-theme.css';
import { Destination, PointOfInterest } from '@/types/POITypes';
import { TravelPace, JourneyStyle, TravelInterest } from '@/types/ItineraryParameters';
import { useClient } from '@/contexts/ClientContext';

// Add mobile menu styles to the head
const mobileStyles = `
  @media (max-width: 768px) {
    .mobile-menu-button {
      display: flex !important;
    }
  }
`;

interface TopBarProps {
  currentRegion?: string;
  availableRegions?: string[];
  onRegionChange?: (region: string) => void;
  isAnimating?: boolean;
  onToggleTheme?: () => void;
  onThemeChange?: (themeId: string) => void;
  onStartJourney?: () => void;
  onPauseJourney?: () => void;
  journeyProgress?: number;

  // Journey control props
  canBeginJourney?: boolean;
  showBeginJourneyButton?: boolean;
  onBeginJourney?: () => void;
  // Journey parameters props
  numberOfDays?: number;
  travelPace?: TravelPace;
  journeyStyle?: JourneyStyle;
  onNumberOfDaysChange?: (days: number) => void;
  onTravelPaceChange?: (pace: TravelPace) => void;
  onJourneyStyleChange?: (style: JourneyStyle) => void;
  // Trip overview data
  selectedCities?: Destination[];
  selectedPOIs?: PointOfInterest[];
  // Mobile panel controls
  isMobile?: boolean;
  onToggleLeftPanel?: () => void;
  onToggleRightPanel?: () => void;
}

const TopBar: React.FC<TopBarProps> = ({
  currentRegion = 'Demo Framework',
  availableRegions = ['Demo Framework'],
  onRegionChange,
  isAnimating = false,
  onToggleTheme,
  onThemeChange,
  onStartJourney,
  onPauseJourney,
  journeyProgress = 0,

  canBeginJourney = false,
  showBeginJourneyButton = false,
  onBeginJourney,
  numberOfDays = 7,
  travelPace = 'balanced-explorer',
  journeyStyle = 'cultural-deep-dive',
  onNumberOfDaysChange,
  onTravelPaceChange,
  onJourneyStyleChange,
  selectedCities = [],
  selectedPOIs = [],
  isMobile = false,
  onToggleLeftPanel,
  onToggleRightPanel
}) => {
  const { clientId, setClientId } = useClient();
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [showRegionSelector, setShowRegionSelector] = useState(false);
  const [showThemeSelector, setShowThemeSelector] = useState(false);

  // Available demo themes for switching
  const availableThemes = [
    { id: 'neutral', name: 'Framework Demo', description: 'Clean, professional framework' },
    { id: 'morocco', name: 'Morocco Demo', description: 'Cultural heritage tours' },
    { id: 'napa-valley', name: 'Napa Valley Demo', description: 'Luxury wine tours' },
    { id: 'route66', name: 'Route 66 Demo', description: 'American road trips' }
  ];

  const handleThemeToggle = () => {
    setIsDarkMode(!isDarkMode);
    if (onToggleTheme) {
      onToggleTheme();
    }
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleThemeChange = (themeId: string) => {
    // Navigate to the appropriate demo page instead of just changing clientId
    const themeRoutes = {
      'neutral': '/enhanced-neutral-demo',
      'morocco': '/moroccan-demo',
      'napa-valley': '/napa-valley-demo',
      'route66': '/route66-demo'
    };

    const route = themeRoutes[themeId as keyof typeof themeRoutes];
    if (route) {
      window.location.href = route;
    } else {
      // Fallback to clientId change for unknown themes
      setClientId(themeId);
      if (onThemeChange) {
        onThemeChange(themeId);
      }
    }
    setShowThemeSelector(false);
  };

  // Helper function to get theme-appropriate styling
  const getThemeStyle = (property: string) => {
    if (clientId === 'morocco') {
      // Preserve original Morocco styling
      switch (property) {
        case 'primaryColor': return 'var(--morocco-red)';
        case 'secondaryColor': return 'var(--morocco-blue)';
        case 'fontDisplay': return 'var(--font-display)';
        case 'fontBody': return 'var(--font-body)';
        case 'fontHeading': return 'var(--font-heading)';
        case 'textSecondary': return 'var(--text-secondary)';
        case 'headerBg': return 'rgba(249, 246, 238, 0.9)';
        case 'borderColor': return 'rgba(177, 155, 123, 0.3)';
        default: return '';
      }
    } else {
      // Use new theme variables for other themes
      switch (property) {
        case 'primaryColor': return 'var(--theme-primary-color)';
        case 'secondaryColor': return 'var(--theme-secondary-color)';
        case 'fontDisplay': return 'var(--theme-font-display)';
        case 'fontBody': return 'var(--theme-font-body)';
        case 'fontHeading': return 'var(--theme-font-heading)';
        case 'textSecondary': return 'var(--theme-header-text)';
        case 'headerBg': return 'var(--theme-header-bg)';
        case 'borderColor': return 'var(--theme-border-color)';
        default: return '';
      }
    }
  };


  
  // Add mobile styles on component mount
  React.useEffect(() => {
    const styleEl = document.createElement('style');
    styleEl.innerHTML = mobileStyles;
    document.head.appendChild(styleEl);
    
    return () => {
      document.head.removeChild(styleEl);
    };
  }, []);

  return (
    <>
      {/* Semi-transparent backdrop with theme pattern */}
      <div
        className={clientId === 'morocco' ? 'moroccan-pattern-1' : `theme-pattern-backdrop theme-${clientId}`}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '70px',
          backgroundColor: clientId === 'morocco'
            ? 'rgba(249, 246, 238, 0.9)'
            : 'var(--theme-header-bg, rgba(249, 246, 238, 0.9))',
          backdropFilter: 'blur(8px)',
          zIndex: 50,
          borderBottom: clientId === 'morocco'
            ? '1px solid rgba(177, 155, 123, 0.3)'
            : '1px solid var(--theme-border-color, rgba(177, 155, 123, 0.3))'
        }}
      />

      {/* TopBar content */}
      <header 
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '70px',
          zIndex: 100,
          padding: '0 1.5rem',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}
      >
        {/* Left Section - Logo & Region */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '1.5rem', flex: '0 0 auto' }}>
          <h1
            className={clientId === 'morocco' ? 'morocco-title' : `theme-title theme-${clientId}`}
            style={{
              margin: 0,
              fontSize: isMobile ? '1.125rem' : '1.375rem',
              color: clientId === 'morocco'
                ? 'var(--morocco-red)'
                : 'var(--theme-primary-color, #2563eb)',
              letterSpacing: '0.5px',
              fontFamily: clientId === 'morocco'
                ? 'var(--font-display)'
                : 'var(--theme-font-display, "Inter", sans-serif)',
              fontWeight: 700,
              whiteSpace: 'nowrap'
            }}
          >
            {isMobile ?
              (clientId === 'morocco' ? 'CTM' :
               clientId === 'napa-valley' ? 'NVW' :
               clientId === 'route66' ? 'R66' : 'Demo') :
              (clientId === 'morocco' ? 'Come To Morocco' :
               clientId === 'napa-valley' ? 'Napa Valley Wine' :
               clientId === 'route66' ? 'Route 66 Adventure' : 'Travel Demo')}
          </h1>

          {/* Region Selector Dropdown */}
          <div style={{ position: 'relative' }}>
            <button
              onClick={availableRegions && availableRegions.length > 1 ? () => setShowRegionSelector(!showRegionSelector) : undefined}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.25rem',
                padding: '0.25rem 0.75rem',
                backgroundColor: clientId === 'morocco'
                  ? 'rgba(139, 26, 24, 0.1)'
                  : 'rgba(37, 99, 235, 0.1)',
                borderRadius: 'var(--radius-md)',
                border: 'none',
                cursor: availableRegions && availableRegions.length > 1 ? 'pointer' : 'default',
                color: clientId === 'morocco'
                  ? 'var(--morocco-red)'
                  : 'var(--theme-primary-color)',
                fontFamily: clientId === 'morocco'
                  ? 'var(--font-body)'
                  : 'var(--theme-font-body)',
                fontWeight: 500,
                fontSize: '0.875rem'
              }}
            >
              <Globe size={16} style={{ marginRight: '4px' }} />
              {currentRegion}
              {availableRegions && availableRegions.length > 1 && <ChevronDown size={16} style={{ marginLeft: '4px' }}/>}
            </button>
            {showRegionSelector && availableRegions && availableRegions.length > 1 && (
              <div style={{
                position: 'absolute',
                top: '100%',
                left: 0,
                backgroundColor: 'white',
                borderRadius: 'var(--radius-md)',
                boxShadow: 'var(--shadow-medium)',
                zIndex: 101,
                marginTop: '4px',
                border: clientId === 'morocco'
                  ? '1px solid var(--morocco-sand)'
                  : '1px solid #e2e8f0'
              }}>
                {availableRegions.map(region => (
                  <button
                    key={region}
                    onClick={() => {
                      if (onRegionChange) onRegionChange(region);
                      setShowRegionSelector(false);
                    }}
                    style={{
                      display: 'block',
                      width: '100%',
                      padding: '0.5rem 1rem',
                      textAlign: 'left',
                      border: 'none',
                      background: 'none',
                      cursor: 'pointer',
                      color: clientId === 'morocco'
                        ? 'var(--morocco-blue)'
                        : 'var(--theme-primary-color)'
                    }}
                    className={clientId === 'morocco' ? "hover:bg-morocco-sand-lightest" : "hover:bg-gray-50"}
                  >
                    {region}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Theme Selector Dropdown */}
          <div style={{ position: 'relative' }}>
            <button
              onClick={() => setShowThemeSelector(!showThemeSelector)}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.25rem',
                padding: '0.25rem 0.75rem',
                backgroundColor: 'rgba(37, 99, 235, 0.1)',
                borderRadius: 'var(--radius-md)',
                border: 'none',
                cursor: 'pointer',
                color: '#2563eb',
                fontFamily: 'var(--font-body)',
                fontWeight: 500,
                fontSize: '0.875rem'
              }}
            >
              <Palette size={16} style={{ marginRight: '4px' }} />
              Theme
              <ChevronDown size={16} style={{ marginLeft: '4px' }}/>
            </button>
            {showThemeSelector && (
              <div style={{
                position: 'absolute',
                top: '100%',
                left: 0,
                backgroundColor: 'white',
                borderRadius: 'var(--radius-md)',
                boxShadow: 'var(--shadow-medium)',
                zIndex: 101,
                marginTop: '4px',
                border: '1px solid #e2e8f0',
                minWidth: '200px'
              }}>
                {availableThemes.map(theme => (
                  <button
                    key={theme.id}
                    onClick={() => handleThemeChange(theme.id)}
                    style={{
                      display: 'block',
                      width: '100%',
                      padding: '0.75rem 1rem',
                      textAlign: 'left',
                      border: 'none',
                      background: clientId === theme.id ? 'rgba(37, 99, 235, 0.1)' : 'none',
                      cursor: 'pointer',
                      color: clientId === theme.id ? '#2563eb' : '#475569',
                      borderBottom: '1px solid #f1f5f9'
                    }}
                    className="hover:bg-gray-50"
                  >
                    <div style={{ fontWeight: 600, fontSize: '0.875rem' }}>
                      {theme.name}
                    </div>
                    <div style={{ fontSize: '0.75rem', color: '#64748b', marginTop: '2px' }}>
                      {theme.description}
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Trip Overview Stats - Hidden on mobile to prevent overflow */}
          {!isMobile && selectedCities && selectedCities.length > 0 && (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flex: '1 1 auto',
              maxWidth: '600px',
              margin: '0 auto',
              gap: '1.5rem'
            }}>
              {/* Duration Stat */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                fontFamily: clientId === 'morocco' ? 'var(--font-body)' : 'var(--theme-font-body)',
                fontSize: '0.875rem'
              }}>
                <Calendar size={16} style={{
                  color: clientId === 'morocco' ? 'var(--morocco-blue)' : 'var(--theme-stat-duration-color)'
                }} />
                <div>
                  <div style={{
                    fontSize: '0.75rem',
                    color: clientId === 'morocco' ? 'var(--text-secondary)' : 'var(--theme-header-text)',
                    fontWeight: 500,
                    textTransform: 'uppercase',
                    letterSpacing: '0.5px',
                    fontFamily: clientId === 'morocco' ? 'var(--font-body)' : 'var(--theme-font-body)',
                    opacity: clientId === 'morocco' ? 1 : 0.8
                  }}>
                    Duration
                  </div>
                  <div style={{
                    fontSize: '0.875rem',
                    fontWeight: 600,
                    color: clientId === 'morocco' ? 'var(--morocco-blue)' : 'var(--theme-stat-duration-color)',
                    fontFamily: clientId === 'morocco' ? 'var(--font-heading)' : 'var(--theme-font-heading)'
                  }}>
                    {numberOfDays} days
                  </div>
                </div>
              </div>

              {/* Cities Stat */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                fontFamily: getThemeStyle('fontBody'),
                fontSize: '0.875rem'
              }}>
                <MapPin size={16} style={{
                  color: clientId === 'morocco' ? 'var(--morocco-red)' : 'var(--theme-stat-cities-color)'
                }} />
                <div>
                  <div style={{
                    fontSize: '0.75rem',
                    color: getThemeStyle('textSecondary'),
                    fontWeight: 500,
                    textTransform: 'uppercase',
                    letterSpacing: '0.5px',
                    fontFamily: getThemeStyle('fontBody'),
                    opacity: clientId === 'morocco' ? 1 : 0.8
                  }}>
                    Cities
                  </div>
                  <div style={{
                    fontSize: '0.875rem',
                    fontWeight: 600,
                    color: clientId === 'morocco' ? 'var(--morocco-red)' : 'var(--theme-stat-cities-color)',
                    fontFamily: getThemeStyle('fontHeading')
                  }}>
                    {selectedCities.length}
                  </div>
                </div>
              </div>

              {/* POIs Stat */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                fontFamily: getThemeStyle('fontBody'),
                fontSize: '0.875rem'
              }}>
                <Star size={16} style={{
                  color: clientId === 'morocco' ? 'var(--morocco-yellow)' : 'var(--theme-stat-pois-color)'
                }} />
                <div>
                  <div style={{
                    fontSize: '0.75rem',
                    color: getThemeStyle('textSecondary'),
                    fontWeight: 500,
                    textTransform: 'uppercase',
                    letterSpacing: '0.5px',
                    fontFamily: getThemeStyle('fontBody'),
                    opacity: clientId === 'morocco' ? 1 : 0.8
                  }}>
                    POIs
                  </div>
                  <div style={{
                    fontSize: '0.875rem',
                    fontWeight: 600,
                    color: clientId === 'morocco' ? 'var(--morocco-blue)' : 'var(--theme-stat-pois-color)',
                    fontFamily: getThemeStyle('fontHeading')
                  }}>
                    {selectedPOIs ? selectedPOIs.length : 0}
                  </div>
                </div>
              </div>

              {/* Pace Stat */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                fontFamily: getThemeStyle('fontBody'),
                fontSize: '0.875rem'
              }}>
                <Clock size={16} style={{
                  color: clientId === 'morocco' ? 'var(--morocco-teal)' : 'var(--theme-stat-pace-color)'
                }} />
                <div>
                  <div style={{
                    fontSize: '0.75rem',
                    color: getThemeStyle('textSecondary'),
                    fontWeight: 500,
                    textTransform: 'uppercase',
                    letterSpacing: '0.5px',
                    fontFamily: getThemeStyle('fontBody'),
                    opacity: clientId === 'morocco' ? 1 : 0.8
                  }}>
                    Pace
                  </div>
                  <div style={{
                    fontSize: '0.875rem',
                    fontWeight: 600,
                    color: clientId === 'morocco' ? 'var(--morocco-blue)' : 'var(--theme-stat-pace-color)',
                    textTransform: 'capitalize',
                    fontFamily: getThemeStyle('fontHeading')
                  }}>
                    {travelPace.replace('-', ' ')}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Center Section - Journey Controls (visible when appropriate) */}
        <div 
          style={{ 
            position: 'absolute', 
            left: '50%', 
            transform: 'translateX(-50%)',
            display: 'flex',
            alignItems: 'center',
            gap: '1rem'
          }}
        >
          {/* Journey button or progress */}
          {journeyProgress > 0 ? (
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
              <button
                className={clientId === 'morocco'
                  ? `morocco-journey-button ${isAnimating ? '' : 'paused'} ${journeyProgress >= 100 ? 'completed' : ''}`
                  : `theme-journey-button theme-${clientId} ${isAnimating ? '' : 'paused'} ${journeyProgress >= 100 ? 'completed' : ''}`}
                onClick={isAnimating ? onPauseJourney : onStartJourney}
                style={clientId === 'morocco' ? {} : {
                  background: 'var(--theme-journey-button-bg)',
                  color: 'var(--theme-header-text)',
                  border: 'none',
                  borderRadius: '6px',
                  padding: '0.5rem 1rem',
                  fontFamily: 'var(--theme-font-heading)',
                  fontWeight: 600,
                  fontSize: '0.875rem',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
              >
                {journeyProgress >= 100
                  ? 'Journey Complete'
                  : isAnimating
                    ? 'Pause Journey'
                    : 'Continue Journey'
                }
              </button>

              {/* Progress bar */}
              <div
                style={{
                  width: '100px',
                  height: '6px',
                  backgroundColor: clientId === 'morocco'
                    ? 'var(--morocco-sand-light)'
                    : 'var(--theme-progress-bg)',
                  borderRadius: '3px',
                  overflow: 'hidden'
                }}
              >
                <div
                  style={{
                    width: `${journeyProgress}%`,
                    height: '100%',
                    background: clientId === 'morocco'
                      ? 'var(--morocco-red)'
                      : 'var(--theme-progress-fill)',
                    transition: 'width 0.3s ease'
                  }}
                />
              </div>
            </div>
          ) : null}
        </div>

        {/* Right Section - Mobile Controls, Begin Journey, Theme Toggle & Info */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.75rem',
          flex: '0 0 auto'
        }}>
          {/* Mobile Panel Toggle Buttons - Removed in favor of floating buttons */}

          {/* Begin Journey Button */}
          {canBeginJourney && onBeginJourney && (
            <button
              onClick={onBeginJourney}
              style={{
                padding: '0.5rem 1rem',
                backgroundColor: 'var(--morocco-red)',
                color: 'white',
                border: 'none',
                borderRadius: 'var(--radius-md)',
                cursor: 'pointer',
                fontFamily: 'var(--font-heading)',
                fontWeight: 600,
                fontSize: '0.875rem',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                transition: 'all 0.2s ease',
                boxShadow: 'var(--shadow-medium)',
                textTransform: 'uppercase',
                letterSpacing: '0.5px'
              }}
            >
              <Route size={16} />
              Begin Journey
            </button>
          )}

          {/* Theme Toggle */}
          <button 
            onClick={handleThemeToggle}
            style={{ 
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              backgroundColor: isDarkMode ? 'rgba(139, 26, 24, 0.1)' : 'rgba(0, 71, 171, 0.1)'
            }}
          >
            {isDarkMode ? (
              <Sun size={20} color="var(--morocco-red)" />
            ) : (
              <Moon size={20} color="var(--morocco-blue)" />
            )}
          </button>

          {/* Info Button */}
          <button 
            style={{ 
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              backgroundColor: 'rgba(242, 192, 55, 0.1)'
            }}
          >
            <Info size={20} color="#B58700" />
          </button>

          {/* Mobile Menu Button (only visible on small screens) */}
          <button 
            className="mobile-menu-button"
            onClick={toggleMobileMenu}
            style={{ 
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              display: 'none', // Hidden by default, shown in mobile via CSS
              alignItems: 'center',
              justifyContent: 'center',
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              backgroundColor: 'rgba(139, 26, 24, 0.1)'
            }}
          >
            {isMobileMenuOpen ? (
              <X size={20} color="var(--morocco-red)" />
            ) : (
              <Menu size={20} color="var(--morocco-red)" />
            )}
          </button>
        </div>
      </header>

      {/* Mobile Menu (Displayed when isMobileMenuOpen is true) */}
      {isMobileMenuOpen && (
        <div 
          className="mobile-menu-dropdown"
          style={{
            position: 'absolute',
            top: '70px', 
            left: 0,
            right: 0,
            backgroundColor: 'rgba(249, 246, 238, 0.95)', 
            backdropFilter: 'blur(10px)',
            zIndex: 90,
            padding: '1rem',
            borderBottom: '1px solid rgba(177, 155, 123, 0.3)',
            boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
          }}
        >
          {/* Mobile menu content */}
          <button 
            onClick={handleThemeToggle}
            style={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: '0.5rem', 
              width: '100%', 
              padding: '0.75rem', 
              border: 'none', 
              background: 'none', 
              textAlign: 'left',
              color: isDarkMode ? 'var(--morocco-red)' : 'var(--morocco-blue)',
              fontFamily: 'var(--font-body)',
              fontSize: '1rem'
            }}
          >
            {isDarkMode ? <Sun size={18} /> : <Moon size={18} />}
            Switch to {isDarkMode ? 'Light' : 'Dark'} Mode
          </button>
          {/* Add other mobile menu items if needed */}
        </div>
      )}
    </>
  );
};

export default TopBar; 