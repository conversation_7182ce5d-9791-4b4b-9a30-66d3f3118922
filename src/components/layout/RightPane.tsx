import React, { useEffect, useMemo, useState } from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Calendar, Clock, MapPin, Navigation, Sun, Umbrella, Info, Sparkles, Users, Star, Settings, Plus, Minus, X, Edit3, Save, RotateCcw, Download, Cloud, CloudRain, Activity } from 'lucide-react';
import '../../styles/moroccan-theme.css';
import { PointOfInterest, Destination } from '@/types/POITypes';
import CityDayAllocator from '../itinerary/CityDayAllocator';
import DragDropItinerary from '../itinerary/DragDropItinerary';
import UnifiedTripPlanner from '../trip/UnifiedTripPlanner';
import AIRecommendationPanel from '../ai/AIRecommendationPanel';
import NaturalLanguageInput from '../ai/NaturalLanguageInput';
import EnhancedAIPanel from '../ai/EnhancedAIPanel';
import TravelInsightsPanel from '../insights/TravelInsightsPanel';
import TravelInsightsEngine, { TravelInsights } from '../../services/TravelInsightsEngine';
import { Destination as AIDestination, POI as AIPOI } from '../../types/Destination';

interface CityDayAllocation {
  cityId: string;
  cityName: string;
  days: number;
}

interface RightPaneProps {
    // Animation state
    isAnimating?: boolean;
    isPaused?: boolean;
    onTogglePane?: () => void;
    journeyProgress?: number;

    // Trip planning data
    selectedCities?: Destination[];
    itineraryPOIs?: PointOfInterest[];
    tripDuration?: number; // Now calculated from city allocations

    // Trip planning callbacks
    onTripDurationChange?: (days: number) => void; // No longer used
    onRemovePOI?: (poi: PointOfInterest) => void;
    onReorderPOI?: (fromIndex: number, toIndex: number) => void;
    onRemoveCity?: (city: Destination) => void;
    onExportItinerary?: () => void;

    // City day allocations
    cityDayAllocations?: Record<string, number>;
    onCityDayAllocationChange?: (cityName: string, days: number) => void;

    // AI-related props
    enableAI?: boolean;
    userInterests?: string[];
    travelStyle?: 'luxury' | 'budget' | 'adventure' | 'cultural' | 'romantic';
    availablePOIs?: PointOfInterest[];
    onAICitiesSelected?: (cities: AIDestination[]) => void;
    onAIPOIsSelected?: (pois: AIPOI[]) => void;
    onAIItineraryGenerated?: (itinerary: any[]) => void;
}

const RightPane: React.FC<RightPaneProps> = ({
    isAnimating = false,
    isPaused = false,
    onTogglePane,
    journeyProgress = 0,
    selectedCities = [],
    itineraryPOIs = [],
    tripDuration = 8,
    onTripDurationChange,
    onRemovePOI,
    onReorderPOI,
    onRemoveCity,
    onExportItinerary,
    cityDayAllocations: propCityDayAllocations = {},
    onCityDayAllocationChange,
    // AI props
    enableAI = false,
    userInterests = [],
    travelStyle = 'cultural',
    availablePOIs = [],
    onAICitiesSelected,
    onAIPOIsSelected,
    onAIItineraryGenerated
}) => {
    // Local state for trip customization
    const [localTripDuration, setLocalTripDuration] = useState(tripDuration);
    const [showTripSettings, setShowTripSettings] = useState(false);
    const [localCityDayAllocations, setLocalCityDayAllocations] = useState<CityDayAllocation[]>([]);

    // Travel insights state
    const [travelInsights, setTravelInsights] = useState<TravelInsights | null>(null);
    const [insightsLoading, setInsightsLoading] = useState(false);

    
    // Debug logging disabled for production readiness
    // useEffect(() => {
    //     console.log('RightPane component rendered');
    // }, []);

    // Day-by-day itinerary generator with smart POI distribution
    const dayByDayItinerary = useMemo(() => {
        if (selectedCities.length === 0 || localTripDuration === 0) {
            return [];
        }

        const days = [];
        const daysPerCity = Math.max(1, Math.floor(localTripDuration / selectedCities.length));
        const extraDays = localTripDuration % selectedCities.length;

        let currentDay = 1;

        selectedCities.forEach((city, cityIndex) => {
            const cityDays = daysPerCity + (cityIndex < extraDays ? 1 : 0);
            
            // Get POIs for this city
            const cityPOIs = itineraryPOIs.filter(poi => {
                if (!poi.coordinates || !city.coordinates) return false;
                const distance = Math.sqrt(
                    Math.pow(poi.coordinates[0] - city.coordinates[0], 2) +
                    Math.pow(poi.coordinates[1] - city.coordinates[1], 2)
                );
                return distance < 0.5; // Roughly 50km radius
            });

            // Distribute POIs across city days
            for (let dayInCity = 0; dayInCity < cityDays; dayInCity++) {
                const dayPOIs = cityPOIs.slice(dayInCity * 2, (dayInCity + 1) * 2); // Max 2 POIs per day
                const poisCount = dayPOIs.length;
                
                // Determine day intensity
                let intensity: 'relaxed' | 'busy' | 'too-many' = 'relaxed';
                if (poisCount >= 3) intensity = 'too-many';
                else if (poisCount === 2) intensity = 'busy';

                // Mock weather data
                const weatherConditions = ['sunny', 'partly-cloudy', 'cloudy'];
                const weather = {
                    condition: weatherConditions[currentDay % 3],
                    temperature: Math.floor(Math.random() * 10) + 20, // 20-30°C
                    icon: currentDay % 3 === 0 ? 'sun' : currentDay % 3 === 1 ? 'cloud' : 'cloud-rain'
                };

                days.push({
                    dayNumber: currentDay,
                    city: city.name,
                    cityId: city.id,
                    pois: dayPOIs,
                    intensity,
                    weather,
                    isFirstDayInCity: dayInCity === 0,
                    isLastDayInCity: dayInCity === cityDays - 1,
                    activities: dayInCity === 0 ? ['Arrival & Check-in'] : 
                               dayInCity === cityDays - 1 ? ['Departure'] : []
                });

                currentDay++;
            }
        });

        return days;
    }, [selectedCities, itineraryPOIs, localTripDuration]);

    // Calculate travel insights when data changes
    useEffect(() => {
        if (selectedCities && selectedCities.length > 0 && itineraryPOIs && itineraryPOIs.length > 0) {
            setInsightsLoading(true);

            // Simulate async calculation (in reality this might involve API calls)
            const timer = setTimeout(() => {
                try {
                    const insightsEngine = TravelInsightsEngine.getInstance();
                    const insights = insightsEngine.generateInsights(
                        selectedCities,
                        itineraryPOIs,
                        {
                            pace: 'moderate',
                            style: travelStyle || 'cultural',
                            interests: userInterests || ['culture'],
                            duration: localTripDuration,
                            budget: 'moderate'
                        },
                        availablePOIs || []
                    );
                    setTravelInsights(insights);
                } catch (error) {
                    console.error('Error generating travel insights:', error);
                    setTravelInsights(null);
                } finally {
                    setInsightsLoading(false);
                }
            }, 500);

            return () => clearTimeout(timer);
        } else {
            setTravelInsights(null);
            setInsightsLoading(false);
        }
    }, [selectedCities, itineraryPOIs, localTripDuration, travelStyle, userInterests, availablePOIs]);

    const mockWeatherData = [
        { day: "Day 1", condition: "Sunny", temperature: "28°C" },
        { day: "Day 2", condition: "Sunny", temperature: "29°C" },
        { day: "Day 3", condition: "Partly Cloudy", temperature: "27°C" },
        { day: "Day 4", condition: "Sunny", temperature: "30°C" },
        { day: "Day 5", condition: "Sunny", temperature: "31°C" }
    ];

    // Helper functions for day-by-day interface
    const getIntensityColor = (intensity: 'relaxed' | 'busy' | 'too-many') => {
        switch (intensity) {
            case 'relaxed': 
                return 'var(--morocco-green)';
            case 'busy': 
                return 'var(--morocco-yellow)';
            case 'too-many': 
                return 'var(--morocco-red)';
            default: 
                return 'var(--morocco-green)';
        }
    };

    const getIntensityIcon = (intensity: 'relaxed' | 'busy' | 'too-many') => {
        switch (intensity) {
            case 'relaxed': 
                return React.createElement(Users, { size: 14 });
            case 'busy': 
                return React.createElement(Activity, { size: 14 });
            case 'too-many': 
                return React.createElement(Activity, { size: 14 });
            default: 
                return React.createElement(Users, { size: 14 });
        }
    };

    const getWeatherIcon = (condition: string) => {
        switch (condition) {
            case 'sunny':
                return React.createElement(Sun, { size: 16, color: "var(--morocco-yellow)" });
            case 'partly-cloudy':
                return React.createElement(Cloud, { size: 16, color: "var(--text-secondary)" });
            case 'cloudy':
                return React.createElement(CloudRain, { size: 16, color: "var(--morocco-blue)" });
            default:
                return React.createElement(Sun, { size: 16, color: "var(--morocco-yellow)" });
        }
    };

    // Handle trip duration change
    const handleTripDurationChange = (newDuration: number) => {
        setLocalTripDuration(newDuration);
        if (onTripDurationChange) {
            onTripDurationChange(newDuration);
        }
    };

    // Handle city day allocation changes
    const handleCityDayAllocationChange = (allocations: CityDayAllocation[]) => {
        setLocalCityDayAllocations(allocations);
        // Update total trip duration based on allocations
        const totalDays = allocations.reduce((sum, allocation) => sum + allocation.days, 0);
        if (totalDays !== localTripDuration) {
            setLocalTripDuration(totalDays);
            if (onTripDurationChange) {
                onTripDurationChange(totalDays);
            }
        }
    };

    // Handle POI reordering between days
    const handlePOIReorder = (fromDay: number, toDay: number, poiId: string) => {
        console.log(`Moving POI ${poiId} from day ${fromDay} to day ${toDay}`);
        // This would typically update the POI assignments in the parent component
        if (onReorderPOI) {
            // For now, we'll use the existing reorder function
            // In a real implementation, this would be more sophisticated
            onReorderPOI(fromDay - 1, toDay - 1);
        }
    };

    // Handle day reordering
    const handleDayReorder = (fromIndex: number, toIndex: number) => {
        console.log(`Moving day from index ${fromIndex} to ${toIndex}`);
        // This would reorder the city day allocations
        setLocalCityDayAllocations(prev => {
            const newAllocations = [...prev];
            const [moved] = newAllocations.splice(fromIndex, 1);
            newAllocations.splice(toIndex, 0, moved);
            return newAllocations;
        });
    };

    // Calculate trip statistics
    const tripStats = useMemo(() => {
        const totalPOIs = itineraryPOIs.length;
        const totalCities = selectedCities.length;
        const avgPOIsPerDay = totalPOIs > 0 ? (totalPOIs / localTripDuration).toFixed(1) : '0';
        const estimatedDistance = totalCities * 150; // Rough estimate

        return {
            totalPOIs,
            totalCities,
            avgPOIsPerDay,
            estimatedDistance: `${estimatedDistance} km`
        };
    }, [itineraryPOIs, selectedCities, localTripDuration]);

    // Real data based on user selections
    const journeyData = {
        totalDuration: `${localTripDuration} days`,
        totalDistance: tripStats.estimatedDistance,
        numDestinations: selectedCities.length,
        numPOIsSelected: itineraryPOIs.length
    };

    return (
        <div
            className="right-pane morocco-pattern-2"
            style={{
                width: '100%', // Take full width of container
                backgroundColor: 'var(--morocco-sand-lightest)',
                height: '100vh',
                borderLeft: '1px solid var(--border-light)',
                display: 'flex',
                flexDirection: 'column',
                boxShadow: 'var(--shadow-soft)',
                zIndex: 10,
                position: 'relative',
            }}
        >


            {/* Fallback Header for Animation Mode */}
            {isAnimating && (
                <div style={{
                    padding: '1.25rem 1rem',
                    borderBottom: '1px solid var(--border-light)',
                    background: 'linear-gradient(to right, rgba(0, 71, 171, 0.02), rgba(139, 26, 24, 0.03))'
                }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                        <div>
                            <h2 style={{
                                margin: 0,
                                fontSize: '1.5rem',
                                color: 'var(--morocco-blue)',
                                display: 'flex',
                                alignItems: 'center',
                                gap: '0.5rem',
                                fontFamily: 'var(--font-heading)',
                                fontWeight: 600
                            }}>
                                {React.createElement(Edit3, { size: 18, color: "var(--morocco-blue)" })}
                                <span>Journey Progress</span>
                            </h2>
                            <p style={{
                                margin: '0.5rem 0 0',
                                fontSize: '0.875rem',
                                color: 'var(--text-secondary)',
                                fontFamily: 'var(--font-body)'
                            }}>
                                Follow your route across Morocco
                            </p>
                        </div>
                        <button
                            onClick={() => setShowTripSettings(!showTripSettings)}
                            style={{
                                background: 'none',
                                border: 'none',
                                cursor: 'pointer',
                                padding: '0.5rem',
                                borderRadius: '50%',
                                backgroundColor: showTripSettings ? 'var(--morocco-blue)' : 'rgba(0, 71, 171, 0.1)'
                            }}
                        >
                            {React.createElement(Settings, {
                                size: 16,
                                color: showTripSettings ? 'white' : 'var(--morocco-blue)'
                            })}
                        </button>
                    </div>
                </div>
            )}

            {/* Toggle button for mobile */}
            {onTogglePane && (
                <button
                    onClick={onTogglePane}
                    style={{
                        position: 'absolute',
                        top: '1rem',
                        right: '1rem',
                        background: 'transparent',
                        border: 'none',
                        cursor: 'pointer',
                        padding: '0.5rem',
                        zIndex: 15
                    }}
                    aria-label="Toggle pane"
                >
                    {React.createElement(X, { size: 20 })}
                </button>
            )}

            <ScrollArea style={{ flex: 1, overflowY: 'auto' }}>
                <div style={{ padding: '1rem' }}>

                    {/* Enhanced AI Trip Planning Section */}
                    {enableAI && (
                        <div style={{ marginBottom: '1.5rem' }}>
                            <EnhancedAIPanel
                                selectedCities={selectedCities}
                                selectedPOIs={itineraryPOIs}
                                availablePOIs={availablePOIs}
                                userInterests={userInterests}
                                travelStyle={travelStyle}
                                tripDuration={tripDuration}
                                onCitiesSelected={onAICitiesSelected || (() => {})}
                                onPOIsSelected={onAIPOIsSelected || (() => {})}
                                onItineraryGenerated={onAIItineraryGenerated || (() => {})}
                                onPOISelect={(poi) => onAIPOIsSelected?.([poi])}
                                onPOIDeselect={(poi) => {
                                    console.log('Deselecting POI:', poi.name);
                                }}
                            />
                        </div>
                    )}

                    {/* Trip Settings Panel */}
                    {showTripSettings && !isAnimating && (
                        <div
                            className="morocco-card"
                            style={{
                                marginBottom: '1.5rem',
                                padding: '1rem',
                                backgroundColor: 'white',
                                border: '2px solid var(--morocco-blue)',
                                borderRadius: '8px'
                            }}
                        >
                            <h3
                                style={{
                                    margin: '0 0 1rem',
                                    fontSize: '1.125rem',
                                    fontWeight: 600,
                                    color: 'var(--morocco-blue)',
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '0.5rem'
                                }}
                            >
                                {React.createElement(Settings, { size: 18 })}
                                Trip Settings
                            </h3>

                            {/* Trip Duration Selector */}
                            <div style={{ marginBottom: '1rem' }}>
                                <label style={{
                                    display: 'block',
                                    fontSize: '0.875rem',
                                    fontWeight: 500,
                                    marginBottom: '0.5rem',
                                    color: 'var(--text-primary)'
                                }}>
                                    Trip Duration
                                </label>
                                <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                                    <button
                                        onClick={() => handleTripDurationChange(Math.max(1, localTripDuration - 1))}
                                        style={{
                                            width: '32px',
                                            height: '32px',
                                            borderRadius: '50%',
                                            border: '1px solid var(--morocco-blue)',
                                            backgroundColor: 'white',
                                            color: 'var(--morocco-blue)',
                                            cursor: 'pointer',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center'
                                        }}
                                    >
                                        {React.createElement(Minus, { size: 16 })}
                                    </button>
                                    <span style={{
                                        fontSize: '1.25rem',
                                        fontWeight: 600,
                                        minWidth: '60px',
                                        textAlign: 'center',
                                        color: 'var(--morocco-blue)'
                                    }}>
                                        {localTripDuration} days
                                    </span>
                                    <button
                                        onClick={() => handleTripDurationChange(Math.min(30, localTripDuration + 1))}
                                        style={{
                                            width: '32px',
                                            height: '32px',
                                            borderRadius: '50%',
                                            border: '1px solid var(--morocco-blue)',
                                            backgroundColor: 'white',
                                            color: 'var(--morocco-blue)',
                                            cursor: 'pointer',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center'
                                        }}
                                    >
                                        {React.createElement(Plus, { size: 16 })}
                                    </button>
                                </div>
                            </div>

                            {/* Quick Actions */}
                            <div style={{ display: 'flex', gap: '0.5rem', flexWrap: 'wrap' }}>
                                <button
                                    onClick={() => {/* Reset trip */}}
                                    style={{
                                        padding: '0.5rem 0.75rem',
                                        fontSize: '0.75rem',
                                        border: '1px solid var(--morocco-red)',
                                        backgroundColor: 'white',
                                        color: 'var(--morocco-red)',
                                        borderRadius: '4px',
                                        cursor: 'pointer',
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: '0.25rem'
                                    }}
                                >
                                    {React.createElement(RotateCcw, { size: 12 })}
                                    Reset
                                </button>
                                {onExportItinerary && (
                                    <button
                                        onClick={onExportItinerary}
                                        style={{
                                            padding: '0.5rem 0.75rem',
                                            fontSize: '0.75rem',
                                            border: '1px solid var(--morocco-blue)',
                                            backgroundColor: 'var(--morocco-blue)',
                                            color: 'white',
                                            borderRadius: '4px',
                                            cursor: 'pointer',
                                            display: 'flex',
                                            alignItems: 'center',
                                            gap: '0.25rem'
                                        }}
                                    >
                                        {React.createElement(Download, { size: 12 })}
                                        Export
                                    </button>
                                )}
                            </div>
                        </div>
                    )}

                    {/* Unified Trip Planner - Replaces all old components */}
                    {!isAnimating && selectedCities.length >= 1 && (
                        <div style={{ marginBottom: '1.5rem' }}>
                            <UnifiedTripPlanner
                                selectedCities={selectedCities}
                                selectedPOIs={itineraryPOIs}
                                totalDays={tripDuration} // Use calculated total from parent
                                cityDayAllocations={propCityDayAllocations}
                                onDayAllocationChange={(allocations) => {
                                    // Update city day allocations through parent callback
                                    Object.entries(allocations).forEach(([cityName, days]) => {
                                        if (onCityDayAllocationChange) {
                                            onCityDayAllocationChange(cityName, days);
                                        }
                                    });
                                }}
                                onPOIRemove={onRemovePOI}
                                onPOIReorder={handlePOIReorder}
                                onDayReorder={handleDayReorder}
                            />
                        </div>
                    )}

                    {/* Fallback message when no cities selected */}
                    {!isAnimating && selectedCities.length === 0 && (
                        <div style={{
                            textAlign: 'center',
                            padding: '3rem 2rem',
                            color: 'var(--text-secondary)',
                            backgroundColor: 'white',
                            borderRadius: '12px',
                            border: '1px solid var(--border-light)'
                        }}>
                            {React.createElement(Calendar, { size: 48, style: { marginBottom: '1rem', opacity: 0.5 } })}
                            <h3 style={{
                                margin: '0 0 0.5rem',
                                fontSize: '1.25rem',
                                fontWeight: 600,
                                color: 'var(--morocco-blue)'
                            }}>
                                Start Planning Your Journey
                            </h3>
                            <p style={{ margin: 0, fontSize: '0.875rem' }}>
                                Select cities from the map to begin building your personalized Morocco itinerary
                            </p>
                        </div>
                    )}

                    {/* Travel Insights Panel - Show when we have data */}
                    {!isAnimating && selectedCities && selectedCities.length > 0 && itineraryPOIs && itineraryPOIs.length > 0 && (
                        <div style={{ marginBottom: '1.5rem' }}>
                            <TravelInsightsPanel
                                insights={travelInsights}
                                isLoading={insightsLoading}
                                isMobile={false} // This would come from props in a real implementation
                            />
                        </div>
                    )}

                    {/* Journey Progress - Always shown during animation, or when there's progress */}
                    {(isAnimating || journeyProgress > 0) && (
                        <div style={{
                            marginBottom: '1.5rem',
                            padding: '1rem',
                            backgroundColor: 'white',
                            borderRadius: '8px'
                        }}>
                            <h3 style={{
                                margin: '0 0 1rem',
                                fontSize: '1.125rem',
                                fontWeight: 600,
                                color: 'var(--morocco-blue)',
                                borderBottom: '1px solid var(--border-light)',
                                paddingBottom: '0.5rem',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'space-between'
                            }}>
                                <span>Journey Progress</span>
                                <span style={{ fontSize: '0.875rem', fontWeight: 400 }}>{Math.round(journeyProgress)}%</span>
                            </h3>

                            {/* Progress bar */}
                            <div style={{
                                width: '100%',
                                height: '8px',
                                backgroundColor: 'var(--morocco-sand-light)',
                                borderRadius: '4px',
                                overflow: 'hidden',
                                marginBottom: '1rem'
                            }}>
                                <div style={{
                                    width: `${journeyProgress}%`,
                                    height: '100%',
                                    backgroundColor: isPaused ? 'var(--morocco-yellow)' : 'var(--morocco-blue)',
                                    transition: 'width 0.3s ease-in-out'
                                }} />
                            </div>

                            {/* Status badge */}
                            <div style={{
                                display: 'inline-block',
                                padding: '0.25rem 0.75rem',
                                borderRadius: '999px',
                                fontSize: '0.75rem',
                                fontWeight: 500,
                                backgroundColor: isPaused ? 'rgba(242, 192, 55, 0.2)' : 'rgba(0, 71, 171, 0.1)',
                                color: isPaused ? 'var(--morocco-terracotta)' : 'var(--morocco-blue)'
                            }}>
                                {isPaused ? 'Paused' : 'In Progress'}
                            </div>
                        </div>
                    )}
                </div>
            </ScrollArea>
        </div>
    );
};

export default RightPane;
