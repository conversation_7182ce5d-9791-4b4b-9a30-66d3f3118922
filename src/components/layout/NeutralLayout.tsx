/**
 * NeutralLayout.tsx
 * 
 * Clean, professional layout component for client demos
 * Based on the refined MoroccanLayout but with neutral styling
 */

import React, { useState, useEffect, useCallback } from 'react';
import mapboxgl from 'mapbox-gl';
import { useClient } from '@/contexts/ClientContext';
import TopBar from './TopBar';
import LeftPane from './LeftPane';
import RightPane from './RightPane';
import { Destination, PointOfInterest } from '../../types';
import { TravelPace, JourneyStyle, TravelInterest, PreArrangedJourney } from '@/types/ItineraryParameters';
import POIModal from '../map/POIModal';
import POIHoverCard from '../map/POIHoverCard';
import CitySelectionOverlay from '../map/CitySelectionOverlay';
import PreArrangedJourneyLinks from '../map/PreArrangedJourneyLinks';
import { ChevronLeft, ChevronRight, Calendar, MapPin } from 'lucide-react';
import { cn } from '@/lib/utils';
import '../../styles/neutral-theme.css';
import '../../styles/theme-system.css';

interface NeutralLayoutProps {
  children?: React.ReactNode;
  mapInstance?: mapboxgl.Map | null;
  // Additional props for animation state and controls
  currentRegion?: string;
  availableRegions?: string[];
  onRegionChange?: (region: string) => void;
  isAnimating?: boolean;
  isPaused?: boolean;
  journeyProgress?: number;
  onStartJourney?: () => void;
  onPauseJourney?: () => void;
  itineraryPOIs?: PointOfInterest[];
  // City selection props
  availableCities?: Destination[];
  selectedCities?: Destination[];
  onCitySelect?: (city: Destination) => void;
  onCityDeselect?: (city: Destination) => void;
  // POI data props
  allPOIs?: PointOfInterest[];
  onSelectPOI?: (poi: PointOfInterest) => void;
  // Journey control props
  canBeginJourney?: boolean;
  showBeginJourneyButton?: boolean;
  onBeginJourney?: () => void;
  onAddPOI?: (poi: PointOfInterest) => void;

  // Advanced Journey Parameters (from Morocco demo)
  numberOfTravelDays?: number;
  onNumberOfTravelDaysChange?: (days: number) => void;
  travelPace?: TravelPace;
  onTravelPaceChange?: (pace: TravelPace) => void;
  journeyStyle?: JourneyStyle;
  onJourneyStyleChange?: (style: JourneyStyle) => void;
  travelInterests?: TravelInterest[];
  onTravelInterestToggle?: (interest: TravelInterest) => void;
  selectedPreArrangedJourney?: PreArrangedJourney | null;
  onPreArrangedJourneySelect?: (journey: PreArrangedJourney) => void;
  cityDayAllocations?: Record<string, number>;
  onCityDayAllocationChange?: (cityName: string, days: number) => void;

  // Pre-arranged journeys data
  preArrangedJourneys?: PreArrangedJourney[];

  // Map settings
  mapCenter?: [number, number];
  mapBounds?: [[number, number], [number, number]];
}

const NeutralLayout: React.FC<NeutralLayoutProps> = ({
  children,
  mapInstance,
  currentRegion = 'Demo',
  availableRegions = ['Demo'],
  onRegionChange,
  isAnimating = false,
  isPaused = false,
  journeyProgress = 0,
  onStartJourney,
  onPauseJourney,
  itineraryPOIs = [],
  availableCities = [],
  selectedCities = [],
  onCitySelect,
  onCityDeselect,
  allPOIs = [],
  onSelectPOI,
  canBeginJourney = false,
  showBeginJourneyButton = false,
  onBeginJourney,
  onAddPOI,
  // Advanced journey parameters
  numberOfTravelDays = 7,
  onNumberOfTravelDaysChange,
  travelPace = 'balanced-explorer',
  onTravelPaceChange,
  journeyStyle = 'cultural-deep-dive',
  onJourneyStyleChange,
  travelInterests = [],
  onTravelInterestToggle,
  selectedPreArrangedJourney = null,
  onPreArrangedJourneySelect,
  cityDayAllocations = {},
  onCityDayAllocationChange,
  preArrangedJourneys = [],
  mapCenter,
  mapBounds
}) => {
  // Get current client/theme
  const { clientId } = useClient();

  // Minimum POIs needed to start journey
  const MIN_POIS_REQUIRED = 2;
  
  // Check if we have enough POIs selected to start journey
  const canStartJourney = itineraryPOIs.length >= MIN_POIS_REQUIRED;
  
  // Left pane visibility state
  const [showLeftPane, setShowLeftPane] = useState(true);
  
  // Right pane visibility state
  const [showRightPane, setShowRightPane] = useState(true);
  
  // Theme state
  const [isDarkTheme, setIsDarkTheme] = useState(false);
  
  // Toggle theme function
  const toggleTheme = () => {
    setIsDarkTheme(prev => !prev);
  };

  // Handle theme change
  const handleThemeChange = (themeId: string) => {
    console.log('Theme changed to:', themeId);
    // The ClientProvider will handle the actual theme switching
  };
  
  // Toggle left pane visibility
  const toggleLeftPane = () => {
    setShowLeftPane(prev => !prev);
  };
  
  // Toggle right pane visibility
  const toggleRightPane = () => {
    setShowRightPane(prev => !prev);
  };
  
  // Handle responsive behavior
  useEffect(() => {
    const handleResize = () => {
      const isMobile = window.innerWidth < 768;
      if (isMobile) {
        // On mobile, show only one pane at a time
        if (showLeftPane && showRightPane) {
          setShowRightPane(false);
        }
      }
    };
    
    window.addEventListener('resize', handleResize);
    handleResize(); // Call once on mount
    
    return () => window.removeEventListener('resize', handleResize);
  }, [showLeftPane, showRightPane]);
  
  return (
    <div className={`neutral-layout theme-${clientId} ${isDarkTheme ? 'dark-theme' : 'light-theme'} h-screen flex flex-col`}>
      {/* Top navigation bar */}
      <TopBar
        currentRegion={currentRegion}
        availableRegions={availableRegions}
        onRegionChange={onRegionChange}
        isAnimating={isAnimating}
        onToggleTheme={toggleTheme}
        onThemeChange={handleThemeChange}
        onStartJourney={canStartJourney ? onStartJourney : undefined}
        onPauseJourney={onPauseJourney}
        journeyProgress={journeyProgress}
        selectedCities={selectedCities}
        selectedPOIs={itineraryPOIs}
        canBeginJourney={canBeginJourney}
        showBeginJourneyButton={showBeginJourneyButton}
        onBeginJourney={onBeginJourney}
      />
      
      {/* Main content area */}
      <div className="main-content flex-1 flex">
        {/* Left pane - POI discovery and filters */}
        {showLeftPane && (
          <LeftPane
            allPois={allPOIs}
            selectedCities={selectedCities}
            onSelectPOI={onSelectPOI}
            onAddPOI={onAddPOI}
            isAnimating={isAnimating}
            onTogglePane={toggleLeftPane}
          />
        )}
        
        {/* Map container */}
        <div className="map-container flex-1 relative">
          {children}
          
          {/* Mobile pane toggle buttons */}
          <div className="mobile-pane-controls">
            <button 
              className="pane-toggle-btn left"
              onClick={toggleLeftPane}
              aria-label="Toggle POI panel"
            >
              POIs
            </button>
            <button 
              className="pane-toggle-btn right"
              onClick={toggleRightPane}
              aria-label="Toggle itinerary panel"
            >
              Trip
            </button>
          </div>
        </div>
        
        {/* Right pane - Itinerary and trip planning */}
        {showRightPane && (
          <RightPane
            itineraryPOIs={itineraryPOIs}
            selectedCities={selectedCities}
            isAnimating={isAnimating}
            journeyProgress={journeyProgress}
            onTogglePane={toggleRightPane}
          />
        )}
      </div>
    </div>
  );
};

export default NeutralLayout;
