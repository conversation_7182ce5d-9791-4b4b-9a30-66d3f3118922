import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Calendar, Clock, MapPin, Route, Settings, Sparkles } from 'lucide-react';
import { TravelPace, JourneyStyle, TravelInterest, PreArrangedJourney } from '@/types/ItineraryParameters';
import { Destination } from '@/types/POITypes';

interface ItineraryPanelProps {
  // Journey Parameters
  numberOfDays: number;
  travelPace: TravelPace;
  journeyStyle: JourneyStyle;
  selectedInterests: TravelInterest[];
  
  // City Selection
  selectedCities: Destination[];
  availableCities: Destination[];
  
  // Pre-arranged Journeys
  preArrangedJourneys: PreArrangedJourney[];
  
  // Event Handlers
  onNumberOfDaysChange: (days: number) => void;
  onTravelPaceChange: (pace: TravelPace) => void;
  onJourneyStyleChange: (style: JourneyStyle) => void;
  onInterestToggle: (interest: TravelInterest) => void;
  onCitySelect: (city: Destination) => void;
  onCityRemove: (city: Destination) => void;
  onPreArrangedJourneySelect: (journey: PreArrangedJourney) => void;
  onBeginJourney: () => void;
  
  // State
  canBeginJourney: boolean;
  isCollapsed?: boolean;
}

const TRAVEL_PACE_OPTIONS: { value: TravelPace; label: string; description: string }[] = [
  { 
    value: 'slow-immersive', 
    label: 'Slow & Immersive', 
    description: '2-3 POIs per day, deep exploration' 
  },
  { 
    value: 'balanced-explorer', 
    label: 'Balanced Explorer', 
    description: '4-5 POIs per day, moderate pace' 
  },
  { 
    value: 'maximum-discovery', 
    label: 'Maximum Discovery', 
    description: '6+ POIs per day, see everything' 
  }
];

const JOURNEY_STYLE_OPTIONS: { value: JourneyStyle; label: string; description: string }[] = [
  { 
    value: 'scenic-routes', 
    label: 'Scenic Routes', 
    description: 'Beautiful drives & panoramic roads' 
  },
  { 
    value: 'cultural-deep-dive', 
    label: 'Cultural Deep-dive', 
    description: 'Authentic cultural experiences' 
  },
  { 
    value: 'adventure-seeker', 
    label: 'Adventure Seeker', 
    description: 'Outdoor activities & sports' 
  },
  { 
    value: 'photography-tour', 
    label: 'Photography Tour', 
    description: 'Best viewpoints & photo spots' 
  },
  { 
    value: 'hidden-gems', 
    label: 'Hidden Gems', 
    description: 'Off-the-beaten-path discoveries' 
  },
  { 
    value: 'local-immersion', 
    label: 'Local Immersion', 
    description: 'Authentic local experiences' 
  }
];

const TRAVEL_INTERESTS: { value: TravelInterest; label: string; icon: string }[] = [
  { value: 'landmarks', label: 'Historic Landmarks', icon: '🏛️' },
  { value: 'nature', label: 'Natural Wonders', icon: '🏔️' },
  { value: 'culture', label: 'Cultural Sites', icon: '🎭' },
  { value: 'adventure', label: 'Adventure', icon: '🧗' },
  { value: 'photography', label: 'Photography', icon: '📸' },
  { value: 'architecture', label: 'Architecture', icon: '🏗️' },
  { value: 'local-experience', label: 'Local Experiences', icon: '🎪' },
  { value: 'hidden-gems', label: 'Hidden Gems', icon: '💎' },
  { value: 'scenic-drives', label: 'Scenic Drives', icon: '🛣️' },
  { value: 'viewpoints', label: 'Viewpoints', icon: '👁️' }
];

const ItineraryPanel: React.FC<ItineraryPanelProps> = ({
  numberOfDays,
  travelPace,
  journeyStyle,
  selectedInterests,
  selectedCities,
  availableCities,
  preArrangedJourneys,
  onNumberOfDaysChange,
  onTravelPaceChange,
  onJourneyStyleChange,
  onInterestToggle,
  onCitySelect,
  onCityRemove,
  onPreArrangedJourneySelect,
  onBeginJourney,
  canBeginJourney,
  isCollapsed = false
}) => {
  const [activeTab, setActiveTab] = useState<'custom' | 'pre-arranged'>('custom');

  const handleDaysChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const days = parseInt(e.target.value) || 1;
    onNumberOfDaysChange(Math.max(1, Math.min(30, days)));
  }, [onNumberOfDaysChange]);

  if (isCollapsed) {
    return (
      <div className="w-12 bg-background border-r border-border flex flex-col items-center py-4">
        <Button variant="ghost" size="sm" className="mb-2">
          <Route className="h-4 w-4" />
        </Button>
        <div className="text-xs text-muted-foreground writing-mode-vertical">
          Itinerary
        </div>
      </div>
    );
  }

  return (
    <div className="w-80 bg-background border-r border-border flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center gap-2 mb-2">
          <Route className="h-5 w-5 text-primary" />
          <h2 className="text-lg font-semibold">Itinerary</h2>
        </div>
        
        {/* Tab Selector */}
        <div className="flex bg-muted rounded-lg p-1">
          <Button
            variant={activeTab === 'custom' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('custom')}
            className="flex-1 text-xs"
          >
            <Settings className="h-3 w-3 mr-1" />
            Custom
          </Button>
          <Button
            variant={activeTab === 'pre-arranged' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('pre-arranged')}
            className="flex-1 text-xs"
          >
            <Sparkles className="h-3 w-3 mr-1" />
            Pre-made
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {activeTab === 'custom' ? (
          <>
            {/* Journey Parameters */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Journey Parameters
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Number of Days */}
                <div className="space-y-2">
                  <Label htmlFor="days" className="text-xs font-medium">
                    Number of Days
                  </Label>
                  <Input
                    id="days"
                    type="number"
                    min="1"
                    max="30"
                    value={numberOfDays}
                    onChange={handleDaysChange}
                    className="h-8"
                  />
                </div>

                {/* Travel Pace */}
                <div className="space-y-2">
                  <Label className="text-xs font-medium">Travel Pace</Label>
                  <Select value={travelPace} onValueChange={onTravelPaceChange}>
                    <SelectTrigger className="h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {TRAVEL_PACE_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <div>
                            <div className="font-medium">{option.label}</div>
                            <div className="text-xs text-muted-foreground">
                              {option.description}
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Journey Style */}
                <div className="space-y-2">
                  <Label className="text-xs font-medium">Journey Style</Label>
                  <Select value={journeyStyle} onValueChange={onJourneyStyleChange}>
                    <SelectTrigger className="h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {JOURNEY_STYLE_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <div>
                            <div className="font-medium">{option.label}</div>
                            <div className="text-xs text-muted-foreground">
                              {option.description}
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Travel Interests */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Travel Interests</CardTitle>
                <CardDescription className="text-xs">
                  Select what interests you most
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {TRAVEL_INTERESTS.map((interest) => (
                    <Badge
                      key={interest.value}
                      variant={selectedInterests.includes(interest.value) ? 'default' : 'outline'}
                      className="cursor-pointer text-xs"
                      onClick={() => onInterestToggle(interest.value)}
                    >
                      <span className="mr-1">{interest.icon}</span>
                      {interest.label}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* City Selection */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Cities ({selectedCities.length})
                </CardTitle>
                <CardDescription className="text-xs">
                  Select cities in order of visit
                </CardDescription>
              </CardHeader>
              <CardContent>
                {selectedCities.length > 0 ? (
                  <div className="space-y-2">
                    {selectedCities.map((city, index) => (
                      <div
                        key={city.id}
                        className="flex items-center justify-between p-2 bg-muted rounded-lg"
                      >
                        <div className="flex items-center gap-2">
                          <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold">
                            {index + 1}
                          </div>
                          <span className="text-sm font-medium">{city.name}</span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onCityRemove(city)}
                          className="h-6 w-6 p-0"
                        >
                          ×
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-4 text-muted-foreground text-xs">
                    Click on cities on the map to add them to your itinerary
                  </div>
                )}
              </CardContent>
            </Card>
          </>
        ) : (
          /* Pre-arranged Journeys */
          <div className="space-y-3">
            {preArrangedJourneys.map((journey) => (
              <Card
                key={journey.id}
                className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => onPreArrangedJourneySelect(journey)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="font-semibold text-sm">{journey.name}</h3>
                    <Badge variant="secondary" className="text-xs">
                      {journey.duration} days
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground mb-3 line-clamp-2">
                    {journey.description}
                  </p>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    <span>{journey.pace.replace('-', ' ')}</span>
                    <Separator orientation="vertical" className="h-3" />
                    <span>{journey.difficulty}</span>
                  </div>
                  {journey.price && (
                    <div className="mt-2 text-xs font-medium text-primary">
                      From {journey.price.currency} {journey.price.from}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-border">
        <Button
          onClick={onBeginJourney}
          disabled={!canBeginJourney}
          className="w-full"
          size="sm"
        >
          <Route className="h-4 w-4 mr-2" />
          Begin Journey
        </Button>
        {!canBeginJourney && (
          <p className="text-xs text-muted-foreground mt-2 text-center">
            {activeTab === 'custom' 
              ? 'Select at least 2 cities to begin your journey'
              : 'Select a pre-arranged journey to begin'
            }
          </p>
        )}
      </div>
    </div>
  );
};

export default ItineraryPanel;
