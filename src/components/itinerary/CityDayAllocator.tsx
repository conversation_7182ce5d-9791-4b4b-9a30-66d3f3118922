import React, { useState, useEffect } from 'react';
import { Destination } from '@/types/POITypes';
import { MapPin, Plus, Minus, Calendar } from 'lucide-react';

interface CityDayAllocation {
  cityId: string;
  cityName: string;
  days: number;
}

interface CityDayAllocatorProps {
  selectedCities: Destination[];
  totalDays: number;
  onDayAllocationChange: (allocations: CityDayAllocation[]) => void;
  initialAllocations?: CityDayAllocation[];
}

const CityDayAllocator: React.FC<CityDayAllocatorProps> = ({
  selectedCities,
  totalDays,
  onDayAllocationChange,
  initialAllocations = []
}) => {
  const [allocations, setAllocations] = useState<CityDayAllocation[]>([]);

  // Initialize allocations when cities change
  useEffect(() => {
    if (selectedCities.length === 0) {
      setAllocations([]);
      return;
    }

    // Create allocations for new cities or use existing ones
    const newAllocations = selectedCities.map(city => {
      const existing = initialAllocations.find(a => a.cityId === city.id) || 
                      allocations.find(a => a.cityId === city.id);
      
      return {
        cityId: city.id,
        cityName: city.name,
        days: existing?.days || Math.max(1, Math.floor(totalDays / selectedCities.length))
      };
    });

    // Adjust allocations to match total days
    const currentTotal = newAllocations.reduce((sum, a) => sum + a.days, 0);
    if (currentTotal !== totalDays && newAllocations.length > 0) {
      const diff = totalDays - currentTotal;
      newAllocations[0].days = Math.max(1, newAllocations[0].days + diff);
    }

    setAllocations(newAllocations);
  }, [selectedCities, totalDays, initialAllocations]);

  // Notify parent when allocations change
  useEffect(() => {
    onDayAllocationChange(allocations);
  }, [allocations, onDayAllocationChange]);

  const updateCityDays = (cityId: string, newDays: number) => {
    if (newDays < 1) return;

    setAllocations(prev => {
      const updated = prev.map(allocation => 
        allocation.cityId === cityId 
          ? { ...allocation, days: newDays }
          : allocation
      );

      // Ensure total doesn't exceed limit
      const currentTotal = updated.reduce((sum, a) => sum + a.days, 0);
      if (currentTotal > totalDays) {
        // Reduce other cities proportionally
        const excess = currentTotal - totalDays;
        const otherCities = updated.filter(a => a.cityId !== cityId);
        
        if (otherCities.length > 0) {
          const reductionPerCity = Math.ceil(excess / otherCities.length);
          otherCities.forEach(city => {
            const reduction = Math.min(reductionPerCity, city.days - 1);
            city.days = Math.max(1, city.days - reduction);
          });
        }
      }

      return updated;
    });
  };

  const getTotalAllocatedDays = () => {
    return allocations.reduce((sum, a) => sum + a.days, 0);
  };

  const getRemainingDays = () => {
    return totalDays - getTotalAllocatedDays();
  };

  if (selectedCities.length === 0) {
    return (
      <div style={{
        padding: '2rem',
        textAlign: 'center',
        color: '#64748b',
        backgroundColor: '#f8fafc',
        borderRadius: '12px',
        border: '1px solid #e2e8f0'
      }}>
        <MapPin size={24} style={{ margin: '0 auto 0.5rem', opacity: 0.5 }} />
        <p>Select cities to allocate days</p>
      </div>
    );
  }

  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '16px',
      border: '1px solid #e2e8f0',
      padding: '1.5rem',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
    }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: '1.5rem',
        paddingBottom: '1rem',
        borderBottom: '1px solid #e2e8f0'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem'
        }}>
          <Calendar size={20} style={{ color: '#3b82f6' }} />
          <h3 style={{
            fontSize: '1.125rem',
            fontWeight: 600,
            color: '#1e293b',
            margin: 0
          }}>
            Days per City
          </h3>
        </div>
        
        <div style={{
          fontSize: '0.875rem',
          color: getRemainingDays() === 0 ? '#059669' : '#dc2626',
          fontWeight: 600
        }}>
          {getRemainingDays() === 0 
            ? '✓ All days allocated' 
            : `${Math.abs(getRemainingDays())} days ${getRemainingDays() > 0 ? 'remaining' : 'over limit'}`
          }
        </div>
      </div>

      {/* City Allocations */}
      <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
        {allocations.map((allocation, index) => (
          <div
            key={allocation.cityId}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: '1rem',
              backgroundColor: '#f8fafc',
              borderRadius: '12px',
              border: '1px solid #e2e8f0'
            }}
          >
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.75rem'
            }}>
              <div style={{
                width: '32px',
                height: '32px',
                borderRadius: '50%',
                backgroundColor: '#3b82f6',
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '0.875rem',
                fontWeight: 600
              }}>
                {index + 1}
              </div>
              
              <div>
                <div style={{
                  fontSize: '1rem',
                  fontWeight: 600,
                  color: '#1e293b'
                }}>
                  {allocation.cityName}
                </div>
                <div style={{
                  fontSize: '0.875rem',
                  color: '#64748b'
                }}>
                  {allocation.days === 1 ? '1 day' : `${allocation.days} days`}
                </div>
              </div>
            </div>

            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              backgroundColor: 'white',
              borderRadius: '8px',
              border: '1px solid #e2e8f0',
              padding: '0.25rem'
            }}>
              <button
                onClick={() => updateCityDays(allocation.cityId, allocation.days - 1)}
                disabled={allocation.days <= 1}
                style={{
                  width: '28px',
                  height: '28px',
                  borderRadius: '6px',
                  border: 'none',
                  backgroundColor: allocation.days <= 1 ? '#f1f5f9' : '#ef4444',
                  color: allocation.days <= 1 ? '#94a3b8' : 'white',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: allocation.days > 1 ? 'pointer' : 'not-allowed',
                  transition: 'all 0.2s ease'
                }}
              >
                <Minus size={14} />
              </button>
              
              <div style={{
                minWidth: '40px',
                textAlign: 'center',
                fontSize: '1rem',
                fontWeight: 700,
                color: '#1e293b'
              }}>
                {allocation.days}
              </div>
              
              <button
                onClick={() => updateCityDays(allocation.cityId, allocation.days + 1)}
                disabled={getRemainingDays() <= 0 && allocation.days >= 1}
                style={{
                  width: '28px',
                  height: '28px',
                  borderRadius: '6px',
                  border: 'none',
                  backgroundColor: (getRemainingDays() <= 0 && allocation.days >= 1) ? '#f1f5f9' : '#22c55e',
                  color: (getRemainingDays() <= 0 && allocation.days >= 1) ? '#94a3b8' : 'white',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: (getRemainingDays() <= 0 && allocation.days >= 1) ? 'not-allowed' : 'pointer',
                  transition: 'all 0.2s ease'
                }}
              >
                <Plus size={14} />
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Summary */}
      <div style={{
        marginTop: '1.5rem',
        padding: '1rem',
        backgroundColor: getRemainingDays() === 0 ? '#f0fdf4' : '#fef2f2',
        borderRadius: '8px',
        border: `1px solid ${getRemainingDays() === 0 ? '#bbf7d0' : '#fecaca'}`,
        fontSize: '0.875rem'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
          <span style={{ color: '#64748b' }}>Total days allocated:</span>
          <span style={{ fontWeight: 600, color: '#1e293b' }}>{getTotalAllocatedDays()}</span>
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <span style={{ color: '#64748b' }}>Total trip duration:</span>
          <span style={{ fontWeight: 600, color: '#1e293b' }}>{totalDays}</span>
        </div>
      </div>
    </div>
  );
};

export default CityDayAllocator;
