import React, { useState, useCallback } from 'react';
import { PointOfInterest, Destination } from '@/types/POITypes';
import { Calendar, Clock, MapPin, GripVertical, X, Star } from 'lucide-react';

interface CityDayAllocation {
  cityId: string;
  cityName: string;
  days: number;
}

interface DayItinerary {
  dayNumber: number;
  cityId: string;
  cityName: string;
  pois: PointOfInterest[];
}

interface DragDropItineraryProps {
  selectedCities: Destination[];
  selectedPOIs: PointOfInterest[];
  cityDayAllocations: CityDayAllocation[];
  onPOIRemove: (poi: PointOfInterest) => void;
  onPOIReorder: (fromDay: number, toDay: number, poiId: string) => void;
  onDayReorder: (fromIndex: number, toIndex: number) => void;
}

const DragDropItinerary: React.FC<DragDropItineraryProps> = ({
  selectedCities,
  selectedPOIs,
  cityDayAllocations,
  onPOIRemove,
  onPOIReorder,
  onDayReorder
}) => {
  const [draggedItem, setDraggedItem] = useState<{
    type: 'poi' | 'day';
    id: string;
    dayNumber?: number;
  } | null>(null);
  const [dragOverDay, setDragOverDay] = useState<number | null>(null);

  // Generate day-by-day itinerary based on city allocations
  const generateDayItinerary = useCallback((): DayItinerary[] => {
    const days: DayItinerary[] = [];
    let currentDay = 1;

    cityDayAllocations.forEach(allocation => {
      for (let i = 0; i < allocation.days; i++) {
        days.push({
          dayNumber: currentDay,
          cityId: allocation.cityId,
          cityName: allocation.cityName,
          pois: []
        });
        currentDay++;
      }
    });

    // Distribute POIs across days (simple round-robin for now)
    selectedPOIs.forEach((poi, index) => {
      if (days.length > 0) {
        const dayIndex = index % days.length;
        days[dayIndex].pois.push(poi);
      }
    });

    return days;
  }, [cityDayAllocations, selectedPOIs]);

  const dayItinerary = generateDayItinerary();

  const handleDragStart = (e: React.DragEvent, type: 'poi' | 'day', id: string, dayNumber?: number) => {
    setDraggedItem({ type, id, dayNumber });
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent, dayNumber: number) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverDay(dayNumber);
  };

  const handleDragLeave = () => {
    setDragOverDay(null);
  };

  const handleDrop = (e: React.DragEvent, targetDay: number) => {
    e.preventDefault();
    setDragOverDay(null);

    if (!draggedItem) return;

    if (draggedItem.type === 'poi' && draggedItem.dayNumber !== undefined) {
      onPOIReorder(draggedItem.dayNumber, targetDay, draggedItem.id);
    }

    setDraggedItem(null);
  };

  const getPOIRating = (poi: PointOfInterest): number => {
    return poi.rating || 4.0;
  };

  const getPOIDuration = (poi: PointOfInterest): string => {
    const duration = poi.duration || 2;
    return duration === 1 ? '1 hour' : `${duration} hours`;
  };

  if (selectedCities.length === 0) {
    return (
      <div style={{
        padding: '3rem 2rem',
        textAlign: 'center',
        color: '#64748b',
        backgroundColor: '#f8fafc',
        borderRadius: '16px',
        border: '1px solid #e2e8f0'
      }}>
        <Calendar size={32} style={{ margin: '0 auto 1rem', opacity: 0.5 }} />
        <h3 style={{ fontSize: '1.25rem', fontWeight: 600, marginBottom: '0.5rem', color: '#1e293b' }}>
          Build Your Itinerary
        </h3>
        <p style={{ fontSize: '0.875rem', lineHeight: 1.5 }}>
          Select cities and points of interest to create your personalized travel itinerary.
          You'll be able to organize activities by day and drag them around to perfect your trip.
        </p>
      </div>
    );
  }

  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '16px',
      border: '1px solid #e2e8f0',
      overflow: 'hidden',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
    }}>
      {/* Header */}
      <div style={{
        padding: '1.5rem',
        borderBottom: '1px solid #e2e8f0',
        backgroundColor: '#f8fafc'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.75rem'
        }}>
          <Calendar size={24} style={{ color: '#3b82f6' }} />
          <div>
            <h2 style={{
              fontSize: '1.25rem',
              fontWeight: 700,
              color: '#1e293b',
              margin: 0
            }}>
              Your Itinerary
            </h2>
            <p style={{
              fontSize: '0.875rem',
              color: '#64748b',
              margin: 0
            }}>
              {dayItinerary.length} days • {selectedPOIs.length} activities
            </p>
          </div>
        </div>
      </div>

      {/* Days List */}
      <div style={{
        maxHeight: 'calc(100vh - 300px)',
        overflowY: 'auto',
        padding: '1rem'
      }}>
        {dayItinerary.map((day, dayIndex) => (
          <div
            key={`day-${day.dayNumber}`}
            style={{
              marginBottom: '1.5rem',
              backgroundColor: dragOverDay === day.dayNumber ? '#f0f9ff' : 'white',
              borderRadius: '12px',
              border: `2px solid ${dragOverDay === day.dayNumber ? '#3b82f6' : '#e2e8f0'}`,
              transition: 'all 0.2s ease'
            }}
            onDragOver={(e) => handleDragOver(e, day.dayNumber)}
            onDragLeave={handleDragLeave}
            onDrop={(e) => handleDrop(e, day.dayNumber)}
          >
            {/* Day Header */}
            <div style={{
              padding: '1rem 1.25rem',
              borderBottom: day.pois.length > 0 ? '1px solid #e2e8f0' : 'none',
              backgroundColor: '#f8fafc',
              borderRadius: '10px 10px 0 0',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.75rem'
              }}>
                <div style={{
                  width: '36px',
                  height: '36px',
                  borderRadius: '50%',
                  backgroundColor: '#3b82f6',
                  color: 'white',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '1rem',
                  fontWeight: 700
                }}>
                  {day.dayNumber}
                </div>
                
                <div>
                  <div style={{
                    fontSize: '1.125rem',
                    fontWeight: 600,
                    color: '#1e293b'
                  }}>
                    Day {day.dayNumber}
                  </div>
                  <div style={{
                    fontSize: '0.875rem',
                    color: '#64748b',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.25rem'
                  }}>
                    <MapPin size={14} />
                    {day.cityName}
                  </div>
                </div>
              </div>

              <div style={{
                fontSize: '0.875rem',
                color: '#64748b',
                display: 'flex',
                alignItems: 'center',
                gap: '0.25rem'
              }}>
                <Clock size={14} />
                {day.pois.length} {day.pois.length === 1 ? 'activity' : 'activities'}
              </div>
            </div>

            {/* POIs for this day */}
            {day.pois.length > 0 && (
              <div style={{ padding: '1rem' }}>
                {day.pois.map((poi, poiIndex) => (
                  <div
                    key={poi.id}
                    draggable
                    onDragStart={(e) => handleDragStart(e, 'poi', poi.id, day.dayNumber)}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '1rem',
                      padding: '0.75rem',
                      marginBottom: poiIndex < day.pois.length - 1 ? '0.75rem' : 0,
                      backgroundColor: '#f8fafc',
                      borderRadius: '8px',
                      border: '1px solid #e2e8f0',
                      cursor: 'grab',
                      transition: 'all 0.2s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = '#f1f5f9';
                      e.currentTarget.style.borderColor = '#cbd5e1';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = '#f8fafc';
                      e.currentTarget.style.borderColor = '#e2e8f0';
                    }}
                  >
                    <GripVertical size={16} style={{ color: '#94a3b8', cursor: 'grab' }} />
                    
                    <div style={{ flex: 1 }}>
                      <div style={{
                        fontSize: '0.875rem',
                        fontWeight: 600,
                        color: '#1e293b',
                        marginBottom: '0.25rem'
                      }}>
                        {poi.name}
                      </div>
                      
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '1rem',
                        fontSize: '0.75rem',
                        color: '#64748b'
                      }}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                          <Clock size={12} />
                          {getPOIDuration(poi)}
                        </div>
                        
                        <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                          <Star size={12} fill="currentColor" style={{ color: '#fbbf24' }} />
                          {getPOIRating(poi).toFixed(1)}
                        </div>
                      </div>
                    </div>

                    <button
                      onClick={() => onPOIRemove(poi)}
                      style={{
                        width: '24px',
                        height: '24px',
                        borderRadius: '50%',
                        border: 'none',
                        backgroundColor: '#fee2e2',
                        color: '#dc2626',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#fecaca';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = '#fee2e2';
                      }}
                    >
                      <X size={12} />
                    </button>
                  </div>
                ))}
              </div>
            )}

            {/* Empty day placeholder */}
            {day.pois.length === 0 && (
              <div style={{
                padding: '2rem',
                textAlign: 'center',
                color: '#94a3b8',
                fontSize: '0.875rem',
                fontStyle: 'italic'
              }}>
                No activities planned for this day
                <br />
                <span style={{ fontSize: '0.75rem' }}>
                  Drag activities here from other days or add new ones
                </span>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default DragDropItinerary;
