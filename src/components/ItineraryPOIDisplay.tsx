import React from 'react';
import { PointOfInterest, Destination } from '@/types';
import { getNearbyPointsOfInterest } from '@/data/pointsOfInterest';
import { Button } from '@/components/ui/button';
import { Landmark, Home, Utensils, Activity, Plus, CheckCircle, MapPin } from 'lucide-react';

interface ItineraryPOIDisplayProps {
  selectedDestinations: Destination[];
  selectedPOIs: PointOfInterest[];
  onAddPOI: (poi: PointOfInterest) => void;
  onRemovePOI: (poiId: string) => void;
}

const ItineraryPOIDisplay: React.FC<ItineraryPOIDisplayProps> = ({
  selectedDestinations,
  selectedPOIs,
  onAddPOI,
  onRemovePOI
}) => {
  // Function to get POIs between two destinations
  const getPOIsBetweenDestinations = (startDest: Destination, endDest: Destination): PointOfInterest[] => {
    // Get the POIs near both destinations
    const startPOIs = getNearbyPointsOfInterest(startDest.id ?? "");
    const endPOIs = getNearbyPointsOfInterest(endDest.id ?? "");
    
    // Combine and deduplicate POIs
    const combinedPOIs = [...startPOIs, ...endPOIs];
    const uniquePOIIds = new Set();
    return combinedPOIs.filter(poi => {
      if (uniquePOIIds.has(poi.id)) return false;
      uniquePOIIds.add(poi.id);
      return true;
    });
  };

  // Get POI icon based on type
  const getPOIIcon = (type: string) => {
    switch (type) {
      case 'landmark':
        return <Landmark className="h-full w-full" />;
      case 'accommodation':
        return <Home className="h-full w-full" />;
      case 'restaurant':
        return <Utensils className="h-full w-full" />;
      case 'activity':
        return <Activity className="h-full w-full" />;
      default:
        return <MapPin className="h-full w-full" />;
    }
  };

  if (selectedDestinations.length <= 1) {
    return (
      <div className="text-center p-4 bg-gray-50 rounded-lg">
        <p className="text-gray-500 text-sm">
          Select at least two destinations to view attractions and activities along your route.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {selectedDestinations.map((destination, index) => {
        // Skip the last destination since we're showing POIs between destinations
        if (index === selectedDestinations.length - 1) return null;
        
        const nextDestination = selectedDestinations[index + 1];
        const betweenPOIs = getPOIsBetweenDestinations(destination, nextDestination);
        
        return (
          <div key={`route-${index}`} className="border border-gray-100 rounded-lg overflow-hidden">
            <div className="bg-morocco-blue/10 p-3 flex items-center justify-between">
              <div className="flex items-center">
                <div className="bg-morocco-terracotta text-white rounded-full w-6 h-6 flex items-center justify-center font-bold text-sm mr-2">
                  {index + 1}
                </div>
                <span className="font-medium">{destination.name}</span>
                <span className="mx-2 text-gray-400">→</span>
                <div className="bg-morocco-terracotta text-white rounded-full w-6 h-6 flex items-center justify-center font-bold text-sm mr-2">
                  {index + 2}
                </div>
                <span className="font-medium">{nextDestination.name}</span>
              </div>
            </div>
            
            {betweenPOIs.length > 0 ? (
              <div className="divide-y divide-gray-100">
                {betweenPOIs.map(poi => {
                  const isSelected = selectedPOIs.some(p => p.id === poi.id);
                  
                  return (
                    <div key={poi.id} className="p-3 flex items-center justify-between hover:bg-gray-50">
                      <div className="flex items-center">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 
                          ${poi.type === 'landmark' ? 'bg-purple-100 text-purple-600' : 
                            poi.type === 'accommodation' ? 'bg-blue-100 text-blue-600' : 
                            poi.type === 'restaurant' ? 'bg-yellow-100 text-yellow-600' : 
                            'bg-green-100 text-green-600'}`
                        }>
                          <div className="w-4 h-4">
                            {getPOIIcon(poi.type || 'landmark')}
                          </div>
                        </div>
                        <div>
                          <div className="font-medium text-sm">{poi.name}</div>
                          <div className="text-xs text-gray-500">
                            {poi.type} • {poi.cost ? `$${poi.cost}` : 'Free'} 
                            {poi.duration ? ` • ${poi.duration}h` : ''}
                          </div>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className={`rounded-full p-0 h-8 w-8 ${
                          isSelected ? 'text-green-500 hover:text-green-600' : 'text-morocco-terracotta hover:text-morocco-terracotta/80'
                        }`}
                        onClick={() => {
                          if (isSelected) {
                            onRemovePOI(poi.id);
                          } else {
                            onAddPOI(poi);
                          }
                        }}
                      >
                        {isSelected ? (
                          <CheckCircle className="h-5 w-5" />
                        ) : (
                          <Plus className="h-5 w-5" />
                        )}
                      </Button>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="p-3 text-center text-sm text-gray-500">
                No points of interest found between these destinations.
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default ItineraryPOIDisplay;
