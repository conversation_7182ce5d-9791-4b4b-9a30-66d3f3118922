import React, { useEffect, useState } from 'react';
import { TripRecommendation, recommendTrips } from '@/utils/trip-templates';
import { Destination } from '@/data/destinations';
import { PointOfInterest } from '@/components/PointOfInterestCard';
import { Calendar, Clock, MapPin, CheckCircle } from 'lucide-react';

interface TripRecommendationsProps {
  availableDays: number;
  userInterests: string[];
  selectedCities: string[];
  onSelectTemplate: (templateId: string) => void;
  allDestinations: Destination[];
  allPOIs: PointOfInterest[];
}

const TripRecommendations: React.FC<TripRecommendationsProps> = ({
  availableDays,
  userInterests,
  selectedCities,
  onSelectTemplate,
  allDestinations,
  allPOIs
}) => {
  const [recommendations, setRecommendations] = useState<TripRecommendation[]>([]);
  const [activeTab, setActiveTab] = useState<string>('all');
  
  useEffect(() => {
    if (availableDays > 0) {
      const recs = recommendTrips(availableDays, userInterests, selectedCities);
      setRecommendations(recs);
    }
  }, [availableDays, userInterests, selectedCities]);
  
  const filteredRecommendations = activeTab === 'all' 
    ? recommendations 
    : recommendations.filter(rec => rec.template.pace === activeTab);
  
  if (recommendations.length === 0) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md text-center">
        <h3 className="text-lg font-semibold mb-2">No Recommendations Available</h3>
        <p className="text-gray-600">
          Please select your travel duration to get personalized recommendations.
        </p>
      </div>
    );
  }
  
  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4">Recommended Trip Templates</h2>
      <p className="text-gray-600 mb-4">
        Based on your {availableDays} day{availableDays !== 1 ? 's' : ''} itinerary 
        {userInterests.length > 0 && ' and interests'}
        {selectedCities.length > 0 && ' including your selected cities'}
      </p>
      
      {/* Pace filter tabs */}
      <div className="flex border-b mb-4">
        <button 
          className={`px-4 py-2 font-medium ${activeTab === 'all' ? 'text-morocco-terracotta border-b-2 border-morocco-terracotta' : 'text-gray-500'}`}
          onClick={() => setActiveTab('all')}
        >
          All
        </button>
        <button 
          className={`px-4 py-2 font-medium ${activeTab === 'relaxed' ? 'text-morocco-terracotta border-b-2 border-morocco-terracotta' : 'text-gray-500'}`}
          onClick={() => setActiveTab('relaxed')}
        >
          Relaxed
        </button>
        <button 
          className={`px-4 py-2 font-medium ${activeTab === 'moderate' ? 'text-morocco-terracotta border-b-2 border-morocco-terracotta' : 'text-gray-500'}`}
          onClick={() => setActiveTab('moderate')}
        >
          Moderate
        </button>
        <button 
          className={`px-4 py-2 font-medium ${activeTab === 'intense' ? 'text-morocco-terracotta border-b-2 border-morocco-terracotta' : 'text-gray-500'}`}
          onClick={() => setActiveTab('intense')}
        >
          Intense
        </button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {filteredRecommendations.map((recommendation) => (
          <div key={recommendation.template.id} className="border rounded-lg overflow-hidden hover:shadow-md transition-all">
            <div className="relative h-40">
              <img 
                src={recommendation.template.imageUrl || '/images/default-trip.jpg'} 
                alt={recommendation.template.name}
                className="w-full h-full object-cover"
              />
              <div className="absolute top-0 left-0 bg-morocco-terracotta text-white px-3 py-1 rounded-br-lg">
                <span className="flex items-center text-sm">
                  <Clock className="w-3 h-3 mr-1" />
                  {recommendation.template.duration} days
                </span>
              </div>
              <div className="absolute top-0 right-0 bg-white/90 text-morocco-terracotta px-3 py-1 rounded-bl-lg font-semibold">
                {recommendation.matchScore}% match
              </div>
            </div>
            
            <div className="p-4">
              <h3 className="text-lg font-semibold">{recommendation.template.name}</h3>
              
              <div className="flex items-center text-sm text-gray-500 mt-1 mb-2">
                <MapPin className="w-3 h-3 mr-1" />
                <span className="line-clamp-1">
                  {recommendation.template.recommendedCities.slice(0, 3).join(', ')}
                  {recommendation.template.recommendedCities.length > 3 && ` +${recommendation.template.recommendedCities.length - 3} more`}
                </span>
              </div>
              
              <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                {recommendation.template.description}
              </p>
              
              <div className="flex flex-wrap gap-1 mb-3">
                {recommendation.template.idealFor.slice(0, 4).map((interest, index) => (
                  <span 
                    key={index}
                    className={`px-2 py-0.5 rounded-full text-xs ${
                      userInterests.includes(interest) 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-600'
                    }`}
                  >
                    {userInterests.includes(interest) && <CheckCircle className="w-3 h-3 inline mr-1" />}
                    {interest}
                  </span>
                ))}
              </div>
              
              {recommendation.reasons.length > 0 && (
                <div className="text-xs text-gray-500 mb-3">
                  <strong>Why this matches:</strong>
                  <ul className="list-disc list-inside ml-1 mt-1">
                    {recommendation.reasons.slice(0, 2).map((reason, index) => (
                      <li key={index}>{reason}</li>
                    ))}
                  </ul>
                </div>
              )}
              
              <button
                onClick={() => onSelectTemplate(recommendation.template.id)}
                className="w-full py-2 bg-morocco-terracotta hover:bg-morocco-terracotta/90 text-white rounded transition-colors"
              >
                Apply Template
              </button>
            </div>
          </div>
        ))}
      </div>
      
      {filteredRecommendations.length === 0 && (
        <div className="text-center py-6 text-gray-500">
          No {activeTab} pace trips match your criteria. Try another pace or adjust your preferences.
        </div>
      )}
    </div>
  );
};

export default TripRecommendations; 