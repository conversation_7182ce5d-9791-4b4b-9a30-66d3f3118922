
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useNavigate } from 'react-router-dom';
import RouteCard, { SavedRoute } from './RouteCard';

interface RouteListProps {
  routes: SavedRoute[];
  isLoading: boolean;
  onDeleteRoute: (routeId: string) => Promise<void>;
}

const RouteList = ({ routes, isLoading, onDeleteRoute }: RouteListProps) => {
  const navigate = useNavigate();

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <p className="text-muted-foreground">Loading your routes...</p>
      </div>
    );
  }

  if (routes.length === 0) {
    return (
      <Card className="text-center py-12">
        <CardContent>
          <div className="space-y-4">
            <h3 className="text-lg font-medium">You don't have any saved routes yet</h3>
            <p className="text-muted-foreground">Start planning your Morocco adventure by creating a new route.</p>
            <Button 
              className="bg-morocco-terracotta hover:bg-morocco-terracotta/90 mt-4"
              onClick={() => navigate('/')}
            >
              Create Your First Route
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {routes.map((route) => (
        <RouteCard 
          key={route.id} 
          route={route} 
          onDelete={onDeleteRoute} 
        />
      ))}
    </div>
  );
};

export default RouteList;
