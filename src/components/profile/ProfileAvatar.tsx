
import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';

interface ProfileAvatarProps {
  avatarUrl?: string;
  getInitials: () => string;
}

const ProfileAvatar: React.FC<ProfileAvatarProps> = ({ avatarUrl, getInitials }) => {
  return (
    <div className="flex flex-col items-center space-y-3">
      <Avatar className="w-24 h-24 border-2 border-morocco-terracotta">
        <AvatarImage src={avatarUrl} />
        <AvatarFallback className="bg-morocco-terracotta text-white text-xl">
          {getInitials()}
        </AvatarFallback>
      </Avatar>
      <Button 
        variant="outline" 
        className="text-sm"
        disabled={true} // Future feature
      >
        Change Picture
      </Button>
    </div>
  );
};

export default ProfileAvatar;
