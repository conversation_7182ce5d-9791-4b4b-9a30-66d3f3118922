import React, { useState, useEffect } from 'react';
import { Destination } from '@/data/destinations';
import { Vehicle } from '@/data/vehicles';
import { Button } from '@/components/ui/button';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { Route, Clock, Calendar, MapPin, Edit2, Pencil } from 'lucide-react';
import { formatDuration, recommendDaysPerDestination } from '@/utils/routeUtils';
import { Input } from '@/components/ui/input';

interface ItineraryBuilderProps {
  selectedDestinations: Destination[];
  selectedVehicle: Vehicle | null;
  onUpdateDestinations: (destinations: Destination[]) => void;
  onProceedToVehicleSelection?: () => void;
  step: 'destinations' | 'vehicle';
}

interface DestinationWithDays extends Destination {
  customDays: number;
  isEditingDays: boolean;
}

const ItineraryBuilder: React.FC<ItineraryBuilderProps> = ({
  selectedDestinations,
  selectedVehicle,
  onUpdateDestinations,
  onProceedToVehicleSelection,
  step
}) => {
  const [expanded, setExpanded] = useState(true);
  const [destinationsWithDays, setDestinationsWithDays] = useState<DestinationWithDays[]>([]);

  // Initialize or update destinations with days
  useEffect(() => {
    if (selectedDestinations.length === 0) {
      setDestinationsWithDays([]);
      return;
    }

    const recommendations = recommendDaysPerDestination(selectedDestinations);
    
    // Merge existing custom days with new destinations
    const newDestinationsWithDays = selectedDestinations.map(dest => {
      // Find existing destination
      const existing = destinationsWithDays.find(d => d.id === dest.id);
      const recommendedDays = recommendations.find(r => r.destinationId === dest.id)?.recommendedDays || 1;
      
      return {
        ...dest,
        customDays: existing ? existing.customDays : recommendedDays,
        isEditingDays: existing ? existing.isEditingDays : false
      };
    });
    
    setDestinationsWithDays(newDestinationsWithDays);
  }, [selectedDestinations]);

  // Handle destination reordering with drag and drop
  const handleDragEnd = (result: any) => {
    if (!result.destination) return;
    
    const items = Array.from(destinationsWithDays);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    
    setDestinationsWithDays(items);
    
    // Update parent component with reordered destinations
    onUpdateDestinations(items);
  };

  // Remove a destination from the itinerary
  const removeDestination = (index: number) => {
    const updatedDestinations = [...destinationsWithDays];
    updatedDestinations.splice(index, 1);
    setDestinationsWithDays(updatedDestinations);
    onUpdateDestinations(updatedDestinations);
  };

  // Toggle day editing mode
  const toggleEditingDays = (index: number) => {
    const updated = [...destinationsWithDays];
    updated[index].isEditingDays = !updated[index].isEditingDays;
    setDestinationsWithDays(updated);
  };

  // Update custom days for a destination
  const updateCustomDays = (index: number, days: number) => {
    const updated = [...destinationsWithDays];
    updated[index].customDays = Math.max(1, days); // Ensure at least 1 day
    updated[index].isEditingDays = false;
    setDestinationsWithDays(updated);
  };

  if (selectedDestinations.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 text-center">
        <MapPin className="w-12 h-12 mx-auto text-morocco-blue mb-4" />
        <h3 className="text-lg font-bold mb-2">Start Building Your Itinerary</h3>
        <p className="text-gray-600">
          Select destinations from the map or destination list to create your custom Moroccan journey.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md">
      <div 
        className="p-4 border-b border-gray-200 flex justify-between items-center cursor-pointer"
        onClick={() => setExpanded(!expanded)}
      >
        <div className="flex items-center">
          <Route className="w-5 h-5 mr-2 text-morocco-terracotta" />
          <h3 className="font-bold">Your Moroccan Journey</h3>
        </div>
        <div className="flex items-center">
          {selectedDestinations.length > 0 && (
            <span className="bg-morocco-terracotta text-white text-xs px-2 py-1 rounded-full mr-2">
              {selectedDestinations.length} stops
            </span>
          )}
          <Button variant="ghost" size="sm" className="p-1 h-auto">
            {expanded ? '−' : '+'}
          </Button>
        </div>
      </div>
      
      {expanded && (
        <div className="p-4">
          <div className="mb-4">
            {/* Info box about day editing */}
            <div className="bg-blue-50 p-3 rounded-md mb-4 text-sm border border-blue-100">
              <p className="flex items-center text-blue-700">
                <Calendar className="h-4 w-4 mr-2 text-blue-500" />
                <span className="font-medium">Plan Your Stay:</span> 
                <span className="ml-1">Click on the <Pencil className="h-3 w-3 mx-1 inline" /> icon to customize how many days you'll spend at each destination.</span>
              </p>
            </div>
            
            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="destinations">
                {(provided) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    className="space-y-3"
                  >
                    {destinationsWithDays.map((destination, index) => (
                      <Draggable
                        key={destination.id}
                        draggableId={destination.id}
                        index={index}
                      >
                        {(provided) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className="bg-gray-50 rounded-lg p-3 flex justify-between items-center"
                          >
                            <div className="flex items-center">
                              <div className="bg-morocco-terracotta text-white rounded-full w-6 h-6 flex items-center justify-center font-bold text-sm mr-3">
                                {index + 1}
                              </div>
                              <div>
                                <h4 className="font-medium">{destination.name}</h4>
                                <div className="text-xs text-gray-500 flex items-center mt-1">
                                  <Clock className="h-3 w-3 mr-1" />
                                  {formatDuration(destination.suggestedDuration)} stay
                                </div>
                              </div>
                            </div>
                            
                            <div className="flex items-center">
                              {/* Days editor section with more prominence */}
                              <div className={`flex items-center rounded-md px-2 py-1 ${destination.isEditingDays ? 'bg-morocco-blue/10 border border-morocco-blue/30' : 'hover:bg-gray-100'}`}>
                                <Calendar className="h-3.5 w-3.5 mr-1.5 text-morocco-terracotta" />
                                
                                {destination.isEditingDays ? (
                                  <div className="flex items-center">
                                    <Input
                                      type="number"
                                      className="w-14 h-6 text-sm p-1 border-morocco-blue"
                                      defaultValue={destination.customDays}
                                      min={1}
                                      max={14}
                                      autoFocus
                                      onBlur={(e) => updateCustomDays(index, parseInt(e.target.value))}
                                      onKeyDown={(e) => {
                                        if (e.key === 'Enter') {
                                          updateCustomDays(index, parseInt((e.target as HTMLInputElement).value));
                                        }
                                      }}
                                    />
                                    <span className="ml-1 text-xs">days</span>
                                  </div>
                                ) : (
                                  <div
                                    className="flex items-center cursor-pointer group"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      toggleEditingDays(index);
                                    }}
                                  >
                                    <span className="text-sm font-medium">
                                      {destination.customDays} day{destination.customDays !== 1 ? 's' : ''}
                                    </span>
                                    <button 
                                      className="ml-1.5 bg-morocco-terracotta/10 hover:bg-morocco-terracotta/20 text-morocco-terracotta p-1 rounded-full group-hover:scale-110 transition-transform"
                                    >
                                      <Pencil className="h-3 w-3" />
                                    </button>
                                  </div>
                                )}
                              </div>
                              
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0 text-gray-500 ml-2"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  removeDestination(index);
                                }}
                              >
                                ×
                              </Button>
                            </div>
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          </div>
          
          {step === 'destinations' && selectedDestinations.length >= 2 && (
            <div className="mt-4">
              <Button 
                className="w-full bg-morocco-terracotta hover:bg-morocco-terracotta/90"
                onClick={onProceedToVehicleSelection}
              >
                Next: Choose Transportation
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ItineraryBuilder;
