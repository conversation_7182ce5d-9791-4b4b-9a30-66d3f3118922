import React from 'react';

interface JourneyOverviewSectionProps {
  totalDuration?: string;
  totalDrivingTime?: string;
  numDestinations?: number;
  numPOIsSelected?: number;
}

const JourneyOverviewSection: React.FC<JourneyOverviewSectionProps> = (props) => {
  return (
    <div style={{ marginBottom: '1.5rem', paddingBottom: '1rem', borderBottom: '1px solid #e0e0e0' }}>
      <h4>Journey Overview</h4>
      <p style={{ margin: '0.25rem 0', fontSize: '0.9rem' }}>Duration: {props.totalDuration || 'N/A'}</p>
      <p style={{ margin: '0.25rem 0', fontSize: '0.9rem' }}>Driving Time: {props.totalDrivingTime || 'N/A'}</p>
      <p style={{ margin: '0.25rem 0', fontSize: '0.9rem' }}>Destinations: {props.numDestinations || 0}</p>
      <p style={{ margin: '0.25rem 0', fontSize: '0.9rem' }}>Selected POIs: {props.numPOIsSelected || 0}</p>
    </div>
  );
};

export default JourneyOverviewSection;
