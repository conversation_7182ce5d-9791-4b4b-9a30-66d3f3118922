import React from 'react';
import TimeBasedRecommendationsModule from './TimeBasedRecommendationsModule';
import ItineraryBalanceModule from './ItineraryBalanceModule';
import AlternativeRoutesModule from './AlternativeRoutesModule';

const TravelInsightsSection: React.FC = () => {
    return (
        <div style={{ marginBottom: '1.5rem', paddingBottom: '1rem', borderBottom: '1px solid #e0e0e0' }}>
            {/* Optional: A general header for "Travel Planning Insights" could go here if not in RightPane.tsx */}
            {/* For now, individual module headers are used. */}
            <TimeBasedRecommendationsModule />
            <ItineraryBalanceModule />
            <AlternativeRoutesModule />
        </div>
    );
};

export default TravelInsightsSection;
