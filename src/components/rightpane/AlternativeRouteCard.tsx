import React from 'react';

interface AlternativeRouteCardProps {
  type: string;
  description: string;
}

const AlternativeRouteCard: React.FC<AlternativeRouteCardProps> = (props) => {
  return (
    <div className="alternative-route-card" style={{ border: '1px solid #eee', padding: '8px', margin: '8px 0', backgroundColor: '#fff', borderRadius: '4px' }}>
      <h6 style={{ marginTop: 0, marginBottom: '0.25rem', fontSize: '0.9rem', fontWeight: '600' }}>{props.type}</h6>
      <p style={{ margin: 0, fontSize: '0.8rem', color: '#555' }}>{props.description}</p>
      <button 
        style={{ 
          marginTop: '0.5rem', 
          padding: '0.25rem 0.5rem', 
          fontSize: '0.75rem', 
          backgroundColor: '#007bff', 
          color: 'white', 
          border: 'none', 
          borderRadius: '3px',
          cursor: 'pointer'
        }}
        onClick={() => alert('View Route functionality not implemented yet.')}
      >
        View Route
      </button>
    </div>
  );
};

export default AlternativeRouteCard;
