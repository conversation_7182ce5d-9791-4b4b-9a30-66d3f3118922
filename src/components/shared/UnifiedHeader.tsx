/**
 * UnifiedHeader.tsx
 * 
 * CONSOLIDATED HEADER COMPONENT
 * Replaces <PERSON><PERSON><PERSON><PERSON><PERSON>, MainHeader, CurrentAreaHeader
 * Supports multiple variants and layouts through props
 */

import React from 'react';
import { useClient } from '@/contexts/ClientContext';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Menu, 
  User, 
  Settings, 
  LogOut, 
  MapPin, 
  Filter,
  Search,
  Bell,
  Home
} from 'lucide-react';
import { cn } from '@/lib/utils';

// ========================================
// TYPES AND INTERFACES
// ========================================

export type HeaderVariant = 'main' | 'app' | 'area' | 'minimal';
export type HeaderSize = 'sm' | 'md' | 'lg';

export interface UnifiedHeaderProps {
  variant?: HeaderVariant;
  size?: HeaderSize;
  title?: string;
  subtitle?: string;
  currentArea?: string;
  showLogo?: boolean;
  showNavigation?: boolean;
  showUserMenu?: boolean;
  showSearch?: boolean;
  showNotifications?: boolean;
  className?: string;
  
  // Navigation items
  navigationItems?: NavigationItem[];
  
  // Actions
  onMenuClick?: () => void;
  onSearchClick?: () => void;
  onNotificationClick?: () => void;
  onUserMenuClick?: () => void;
  onLogoClick?: () => void;
  
  // Custom content
  leftContent?: React.ReactNode;
  centerContent?: React.ReactNode;
  rightContent?: React.ReactNode;
  
  // Area-specific props
  availableRegions?: string[];
  selectedRegion?: string;
  onRegionChange?: (region: string) => void;
  filterCount?: number;
  onFilterClick?: () => void;
}

export interface NavigationItem {
  id: string;
  label: string;
  href?: string;
  onClick?: () => void;
  icon?: React.ReactNode;
  badge?: string | number;
  active?: boolean;
}

// ========================================
// UTILITY FUNCTIONS
// ========================================

const getLogoUrl = (clientId?: string): string => {
  const logoMap: Record<string, string> = {
    'morocco': '/assets/morocco/logo.svg',
    'portugal': '/assets/portugal/logo.svg',
    'route66': '/assets/route66/logo.svg',
    'neutral': '/assets/neutral/logo.svg'
  };
  
  return logoMap[clientId || 'neutral'] || '/assets/neutral/logo.svg';
};

const getThemeClasses = (clientId?: string): string => {
  const themeMap: Record<string, string> = {
    'morocco': 'bg-gradient-to-r from-red-600 to-orange-500',
    'portugal': 'bg-gradient-to-r from-blue-600 to-green-500',
    'route66': 'bg-gradient-to-r from-gray-800 to-red-600',
    'neutral': 'bg-gradient-to-r from-blue-600 to-purple-600'
  };
  
  return themeMap[clientId || 'neutral'] || themeMap['neutral'];
};

// ========================================
// MAIN COMPONENT
// ========================================

export const UnifiedHeader: React.FC<UnifiedHeaderProps> = ({
  variant = 'main',
  size = 'md',
  title,
  subtitle,
  currentArea,
  showLogo = true,
  showNavigation = true,
  showUserMenu = true,
  showSearch = false,
  showNotifications = false,
  className,
  navigationItems = [],
  onMenuClick,
  onSearchClick,
  onNotificationClick,
  onUserMenuClick,
  onLogoClick,
  leftContent,
  centerContent,
  rightContent,
  availableRegions = [],
  selectedRegion,
  onRegionChange,
  filterCount,
  onFilterClick
}) => {
  
  const { clientId, config } = useClient();
  const { user, signOut } = useAuth();
  
  // ========================================
  // SIZE CONFIGURATIONS
  // ========================================
  
  const sizeConfig = {
    sm: {
      container: 'h-12 px-3',
      logo: 'h-6',
      title: 'text-sm font-semibold',
      subtitle: 'text-xs',
      button: 'h-7 px-2 text-xs',
      icon: 'w-4 h-4'
    },
    md: {
      container: 'h-16 px-4',
      logo: 'h-8',
      title: 'text-lg font-semibold',
      subtitle: 'text-sm',
      button: 'h-8 px-3 text-sm',
      icon: 'w-5 h-5'
    },
    lg: {
      container: 'h-20 px-6',
      logo: 'h-10',
      title: 'text-xl font-bold',
      subtitle: 'text-base',
      button: 'h-10 px-4 text-base',
      icon: 'w-6 h-6'
    }
  };
  
  const config_size = sizeConfig[size];
  
  // ========================================
  // VARIANT LAYOUTS
  // ========================================
  
  const renderMainHeader = () => (
    <header className={cn(
      'flex items-center justify-between border-b bg-white shadow-sm',
      getThemeClasses(clientId),
      config_size.container,
      className
    )}>
      {/* Left Section */}
      <div className="flex items-center gap-4">
        {leftContent || (
          <>
            {onMenuClick && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onMenuClick}
                className={cn('text-white hover:bg-white/20', config_size.button)}
              >
                <Menu className={config_size.icon} />
              </Button>
            )}
            
            {showLogo && (
              <div 
                className="flex items-center gap-3 cursor-pointer"
                onClick={onLogoClick}
              >
                <img 
                  src={getLogoUrl(clientId)} 
                  alt={config?.name || 'Logo'}
                  className={cn(config_size.logo, 'object-contain')}
                />
                {title && (
                  <div>
                    <h1 className={cn(config_size.title, 'text-white')}>
                      {title}
                    </h1>
                    {subtitle && (
                      <p className={cn(config_size.subtitle, 'text-white/80')}>
                        {subtitle}
                      </p>
                    )}
                  </div>
                )}
              </div>
            )}
          </>
        )}
      </div>
      
      {/* Center Section */}
      <div className="flex-1 flex justify-center">
        {centerContent || (
          showNavigation && navigationItems.length > 0 && (
            <nav className="flex items-center gap-2">
              {navigationItems.map((item) => (
                <Button
                  key={item.id}
                  variant={item.active ? "secondary" : "ghost"}
                  size="sm"
                  onClick={item.onClick}
                  className={cn(
                    'text-white hover:bg-white/20',
                    item.active && 'bg-white/20',
                    config_size.button
                  )}
                >
                  {item.icon}
                  {item.label}
                  {item.badge && (
                    <Badge variant="secondary" className="ml-1">
                      {item.badge}
                    </Badge>
                  )}
                </Button>
              ))}
            </nav>
          )
        )}
      </div>
      
      {/* Right Section */}
      <div className="flex items-center gap-2">
        {rightContent || (
          <>
            {showSearch && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onSearchClick}
                className={cn('text-white hover:bg-white/20', config_size.button)}
              >
                <Search className={config_size.icon} />
              </Button>
            )}
            
            {showNotifications && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onNotificationClick}
                className={cn('text-white hover:bg-white/20', config_size.button)}
              >
                <Bell className={config_size.icon} />
              </Button>
            )}
            
            {showUserMenu && user && (
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onUserMenuClick}
                  className={cn('text-white hover:bg-white/20', config_size.button)}
                >
                  <User className={config_size.icon} />
                  <span className="hidden md:inline">{user.email}</span>
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={signOut}
                  className={cn('text-white hover:bg-white/20', config_size.button)}
                >
                  <LogOut className={config_size.icon} />
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </header>
  );
  
  const renderAreaHeader = () => (
    <header className={cn(
      'flex items-center justify-between bg-white border-b shadow-sm',
      config_size.container,
      className
    )}>
      {/* Left Section - Current Area */}
      <div className="flex items-center gap-3">
        <MapPin className={cn(config_size.icon, 'text-blue-600')} />
        <div>
          <h2 className={config_size.title}>
            {currentArea || selectedRegion || 'Current Area'}
          </h2>
          {subtitle && (
            <p className={cn(config_size.subtitle, 'text-gray-600')}>
              {subtitle}
            </p>
          )}
        </div>
      </div>
      
      {/* Center Section - Region Selector */}
      {availableRegions.length > 0 && (
        <div className="flex items-center gap-2">
          {availableRegions.map((region) => (
            <Button
              key={region}
              variant={region === selectedRegion ? "default" : "outline"}
              size="sm"
              onClick={() => onRegionChange?.(region)}
              className={config_size.button}
            >
              {region}
            </Button>
          ))}
        </div>
      )}
      
      {/* Right Section - Filters */}
      <div className="flex items-center gap-2">
        {onFilterClick && (
          <Button
            variant="outline"
            size="sm"
            onClick={onFilterClick}
            className={config_size.button}
          >
            <Filter className={config_size.icon} />
            Filters
            {filterCount && filterCount > 0 && (
              <Badge variant="secondary" className="ml-1">
                {filterCount}
              </Badge>
            )}
          </Button>
        )}
      </div>
    </header>
  );
  
  const renderMinimalHeader = () => (
    <header className={cn(
      'flex items-center justify-between bg-white/95 backdrop-blur-sm border-b',
      config_size.container,
      className
    )}>
      <div className="flex items-center gap-3">
        {showLogo && (
          <img 
            src={getLogoUrl(clientId)} 
            alt={config?.name || 'Logo'}
            className={cn(config_size.logo, 'object-contain')}
          />
        )}
        {title && (
          <h1 className={config_size.title}>{title}</h1>
        )}
      </div>
      
      {rightContent}
    </header>
  );
  
  // ========================================
  // RENDER BASED ON VARIANT
  // ========================================
  
  switch (variant) {
    case 'area':
      return renderAreaHeader();
    case 'minimal':
      return renderMinimalHeader();
    case 'app':
    case 'main':
    default:
      return renderMainHeader();
  }
};

export default UnifiedHeader;
