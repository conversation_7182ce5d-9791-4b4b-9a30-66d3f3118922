/**
 * ErrorBoundary.tsx
 * 
 * UNIFIED ERROR BOUNDARY SYSTEM
 * Provides consistent error handling across the application
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useClient } from '@/contexts/ClientContext';

// ========================================
// TYPES AND INTERFACES
// ========================================

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
  level?: 'page' | 'component' | 'critical';
}

interface ErrorDisplayProps {
  error: Error;
  errorInfo: ErrorInfo;
  errorId: string;
  onRetry: () => void;
  onGoHome: () => void;
  onReportBug: () => void;
  showDetails: boolean;
  level: 'page' | 'component' | 'critical';
}

// ========================================
// ERROR DISPLAY COMPONENT
// ========================================

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  errorInfo,
  errorId,
  onRetry,
  onGoHome,
  onReportBug,
  showDetails,
  level
}) => {
  const getErrorTitle = () => {
    switch (level) {
      case 'critical':
        return 'Critical System Error';
      case 'page':
        return 'Page Error';
      case 'component':
      default:
        return 'Something went wrong';
    }
  };

  const getErrorDescription = () => {
    switch (level) {
      case 'critical':
        return 'A critical error has occurred that prevents the application from functioning properly.';
      case 'page':
        return 'This page encountered an error and cannot be displayed.';
      case 'component':
      default:
        return 'This component encountered an error. You can try refreshing or return to the home page.';
    }
  };

  const getErrorSeverity = () => {
    switch (level) {
      case 'critical':
        return 'destructive';
      case 'page':
        return 'default';
      case 'component':
      default:
        return 'default';
    }
  };

  return (
    <div className="flex items-center justify-center min-h-[400px] p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <AlertTriangle 
              className={`w-16 h-16 ${
                level === 'critical' ? 'text-red-500' : 'text-orange-500'
              }`} 
            />
          </div>
          <CardTitle className="text-2xl">{getErrorTitle()}</CardTitle>
          <CardDescription className="text-base">
            {getErrorDescription()}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Error ID for support */}
          <Alert variant={getErrorSeverity() as any}>
            <AlertDescription>
              <strong>Error ID:</strong> {errorId}
              <br />
              <strong>Time:</strong> {new Date().toLocaleString()}
            </AlertDescription>
          </Alert>

          {/* Action buttons */}
          <div className="flex flex-wrap gap-3 justify-center">
            <Button onClick={onRetry} variant="default">
              <RefreshCw className="w-4 h-4 mr-2" />
              Try Again
            </Button>
            
            {level !== 'critical' && (
              <Button onClick={onGoHome} variant="outline">
                <Home className="w-4 h-4 mr-2" />
                Go Home
              </Button>
            )}
            
            <Button onClick={onReportBug} variant="outline">
              <Bug className="w-4 h-4 mr-2" />
              Report Bug
            </Button>
          </div>

          {/* Error details (collapsible) */}
          {showDetails && (
            <details className="mt-6">
              <summary className="cursor-pointer text-sm font-medium text-gray-600 hover:text-gray-800">
                Technical Details
              </summary>
              <div className="mt-3 p-4 bg-gray-50 rounded-lg text-sm font-mono">
                <div className="mb-3">
                  <strong>Error:</strong>
                  <pre className="mt-1 whitespace-pre-wrap text-red-600">
                    {error.message}
                  </pre>
                </div>
                
                <div className="mb-3">
                  <strong>Stack Trace:</strong>
                  <pre className="mt-1 whitespace-pre-wrap text-gray-700 text-xs">
                    {error.stack}
                  </pre>
                </div>
                
                <div>
                  <strong>Component Stack:</strong>
                  <pre className="mt-1 whitespace-pre-wrap text-gray-700 text-xs">
                    {errorInfo.componentStack}
                  </pre>
                </div>
              </div>
            </details>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

// ========================================
// ERROR BOUNDARY CLASS COMPONENT
// ========================================

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    // Generate unique error ID
    const errorId = `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    return {
      hasError: true,
      error,
      errorId
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Update state with error info
    this.setState({
      error,
      errorInfo
    });

    // Call custom error handler
    this.props.onError?.(error, errorInfo);

    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.group('🚨 Error Boundary Caught Error');
      console.error('Error:', error);
      console.error('Error Info:', errorInfo);
      console.error('Component Stack:', errorInfo.componentStack);
      console.groupEnd();
    }

    // Report error to monitoring service
    this.reportError(error, errorInfo);
  }

  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    // In a real application, you would send this to your error monitoring service
    // e.g., Sentry, LogRocket, Bugsnag, etc.
    
    const errorReport = {
      errorId: this.state.errorId,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      level: this.props.level || 'component'
    };

    // For now, just log to console
    console.warn('[ErrorBoundary] Error reported:', errorReport);
    
    // TODO: Implement actual error reporting
    // errorReportingService.report(errorReport);
  };

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    });
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  private handleReportBug = () => {
    const { error, errorInfo, errorId } = this.state;
    
    if (!error || !errorInfo) return;

    // Create bug report URL with pre-filled information
    const bugReportData = {
      title: `Error Report: ${error.message}`,
      body: `
**Error ID:** ${errorId}
**Time:** ${new Date().toLocaleString()}
**URL:** ${window.location.href}

**Error Message:**
\`\`\`
${error.message}
\`\`\`

**Stack Trace:**
\`\`\`
${error.stack}
\`\`\`

**Component Stack:**
\`\`\`
${errorInfo.componentStack}
\`\`\`

**Steps to Reproduce:**
1. 
2. 
3. 

**Expected Behavior:**


**Actual Behavior:**

      `.trim()
    };

    // Open bug report (you can customize this URL)
    const bugReportUrl = `mailto:<EMAIL>?subject=${encodeURIComponent(bugReportData.title)}&body=${encodeURIComponent(bugReportData.body)}`;
    window.open(bugReportUrl);
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error display
      if (this.state.error && this.state.errorInfo) {
        return (
          <ErrorDisplay
            error={this.state.error}
            errorInfo={this.state.errorInfo}
            errorId={this.state.errorId}
            onRetry={this.handleRetry}
            onGoHome={this.handleGoHome}
            onReportBug={this.handleReportBug}
            showDetails={this.props.showDetails ?? process.env.NODE_ENV === 'development'}
            level={this.props.level || 'component'}
          />
        );
      }
    }

    return this.props.children;
  }
}

// ========================================
// CONVENIENCE WRAPPERS
// ========================================

export const PageErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <ErrorBoundary level="page" showDetails={false}>
    {children}
  </ErrorBoundary>
);

export const ComponentErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <ErrorBoundary level="component" showDetails={false}>
    {children}
  </ErrorBoundary>
);

export const CriticalErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <ErrorBoundary level="critical" showDetails={true}>
    {children}
  </ErrorBoundary>
);

export default ErrorBoundary;
