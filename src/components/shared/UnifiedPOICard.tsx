/**
 * UnifiedPOICard.tsx
 * 
 * CONSOLIDATED POI CARD COMPONENT
 * Replaces PointOfInterestCard, EnhancedPointOfInterestCard, and POICard
 * Supports multiple variants and layouts through props
 */

import React from 'react';
import { PointOfInterest, getCategoryDisplayName } from '@/types';
import { 
  MapPin, 
  Clock, 
  Plus, 
  Check, 
  Star, 
  DollarSign,
  MoreHorizontal,
  Eye
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

// ========================================
// TYPES AND INTERFACES
// ========================================

export type POICardVariant = 'default' | 'enhanced' | 'compact' | 'list';
export type POICardSize = 'sm' | 'md' | 'lg';

export interface UnifiedPOICardProps {
  poi: PointOfInterest;
  variant?: POICardVariant;
  size?: POICardSize;
  isAdded?: boolean;
  isSelected?: boolean;
  showImage?: boolean;
  showActions?: boolean;
  showMetadata?: boolean;
  className?: string;
  
  // Actions
  onAdd?: () => void;
  onRemove?: () => void;
  onSelect?: () => void;
  onView?: () => void;
  
  // Custom content
  actions?: React.ReactNode;
  footer?: React.ReactNode;
}

// ========================================
// UTILITY FUNCTIONS
// ========================================

const getCategoryColor = (category: string): string => {
  const colorMap: Record<string, string> = {
    'landmark': 'bg-purple-100 text-purple-800 border-purple-200',
    'nature': 'bg-green-100 text-green-800 border-green-200',
    'cultural': 'bg-blue-100 text-blue-800 border-blue-200',
    'adventure': 'bg-orange-100 text-orange-800 border-orange-200',
    'scenic': 'bg-cyan-100 text-cyan-800 border-cyan-200',
    'hidden-gem': 'bg-pink-100 text-pink-800 border-pink-200',
    'photography': 'bg-indigo-100 text-indigo-800 border-indigo-200',
    'local-experience': 'bg-yellow-100 text-yellow-800 border-yellow-200',
    'architecture': 'bg-gray-100 text-gray-800 border-gray-200',
    'viewpoint': 'bg-teal-100 text-teal-800 border-teal-200',
    'activity': 'bg-emerald-100 text-emerald-800 border-emerald-200',
    'accommodation': 'bg-blue-100 text-blue-800 border-blue-200',
    'dining': 'bg-red-100 text-red-800 border-red-200',
    'shopping': 'bg-amber-100 text-amber-800 border-amber-200',
    'transport': 'bg-slate-100 text-slate-800 border-slate-200',
    'other': 'bg-gray-100 text-gray-800 border-gray-200'
  };
  
  return colorMap[category] || colorMap['other'];
};

const formatCost = (cost?: number): string => {
  if (cost === undefined || cost === 0) return 'Free';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(cost);
};

const formatDuration = (duration?: number): string => {
  if (!duration) return 'Flexible';
  if (duration < 60) return `${duration} min`;
  const hours = Math.floor(duration / 60);
  const minutes = duration % 60;
  if (minutes === 0) return `${hours}h`;
  return `${hours}h ${minutes}m`;
};

const renderStarRating = (rating?: number): React.ReactNode => {
  if (!rating) return null;
  
  const stars = [];
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.5;
  
  for (let i = 0; i < 5; i++) {
    if (i < fullStars) {
      stars.push(<Star key={i} className="w-3 h-3 fill-yellow-400 text-yellow-400" />);
    } else if (i === fullStars && hasHalfStar) {
      stars.push(<Star key={i} className="w-3 h-3 fill-yellow-400/50 text-yellow-400" />);
    } else {
      stars.push(<Star key={i} className="w-3 h-3 text-gray-300" />);
    }
  }
  
  return <div className="flex items-center gap-0.5">{stars}</div>;
};

// ========================================
// MAIN COMPONENT
// ========================================

export const UnifiedPOICard: React.FC<UnifiedPOICardProps> = ({
  poi,
  variant = 'default',
  size = 'md',
  isAdded = false,
  isSelected = false,
  showImage = true,
  showActions = true,
  showMetadata = true,
  className,
  onAdd,
  onRemove,
  onSelect,
  onView,
  actions,
  footer
}) => {
  
  // ========================================
  // SIZE CONFIGURATIONS
  // ========================================
  
  const sizeConfig = {
    sm: {
      container: 'p-3',
      image: 'h-24',
      title: 'text-sm font-medium',
      description: 'text-xs',
      metadata: 'text-xs',
      button: 'h-7 px-2 text-xs'
    },
    md: {
      container: 'p-4',
      image: 'h-32',
      title: 'text-base font-semibold',
      description: 'text-sm',
      metadata: 'text-xs',
      button: 'h-8 px-3 text-sm'
    },
    lg: {
      container: 'p-6',
      image: 'h-40',
      title: 'text-lg font-semibold',
      description: 'text-base',
      metadata: 'text-sm',
      button: 'h-9 px-4 text-sm'
    }
  };
  
  const config = sizeConfig[size];
  
  // ========================================
  // VARIANT LAYOUTS
  // ========================================
  
  const renderCompactLayout = () => (
    <div 
      className={cn(
        'flex items-center gap-3 p-3 bg-white rounded-lg border hover:shadow-md transition-all cursor-pointer',
        isSelected && 'ring-2 ring-blue-500 bg-blue-50',
        className
      )}
      onClick={onSelect}
    >
      {showImage && poi.images?.[0] && (
        <img 
          src={poi.images[0]} 
          alt={poi.name}
          className="w-12 h-12 rounded-lg object-cover flex-shrink-0"
        />
      )}
      
      <div className="flex-1 min-w-0">
        <h3 className="font-medium text-sm truncate">{poi.name}</h3>
        <div className="flex items-center gap-2 mt-1">
          <Badge variant="secondary" className={cn('text-xs', getCategoryColor(poi.category))}>
            {getCategoryDisplayName(poi.category)}
          </Badge>
          {poi.rating && renderStarRating(poi.rating)}
        </div>
      </div>
      
      {showActions && (
        <div className="flex items-center gap-1">
          {isAdded ? (
            <Button
              size="sm"
              variant="outline"
              onClick={(e) => {
                e.stopPropagation();
                onRemove?.();
              }}
              className="h-7 px-2"
            >
              <Check className="w-3 h-3" />
            </Button>
          ) : (
            <Button
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onAdd?.();
              }}
              className="h-7 px-2"
            >
              <Plus className="w-3 h-3" />
            </Button>
          )}
        </div>
      )}
    </div>
  );
  
  const renderListLayout = () => (
    <div 
      className={cn(
        'flex items-center justify-between p-3 border-b hover:bg-gray-50 transition-colors cursor-pointer',
        isSelected && 'bg-blue-50 border-blue-200',
        className
      )}
      onClick={onSelect}
    >
      <div className="flex items-center gap-3 flex-1 min-w-0">
        <div className={cn('w-2 h-2 rounded-full flex-shrink-0', getCategoryColor(poi.category).split(' ')[0])} />
        <div className="flex-1 min-w-0">
          <h3 className="font-medium text-sm truncate">{poi.name}</h3>
          <p className="text-xs text-gray-600 truncate">{poi.description}</p>
        </div>
      </div>
      
      {showActions && (
        <Button
          size="sm"
          variant={isAdded ? "outline" : "default"}
          onClick={(e) => {
            e.stopPropagation();
            isAdded ? onRemove?.() : onAdd?.();
          }}
          className="h-7 px-2 ml-2"
        >
          {isAdded ? <Check className="w-3 h-3" /> : <Plus className="w-3 h-3" />}
        </Button>
      )}
    </div>
  );
  
  const renderCardLayout = () => (
    <div 
      className={cn(
        'bg-white rounded-lg shadow-sm border hover:shadow-md transition-all duration-300 overflow-hidden',
        isSelected && 'ring-2 ring-blue-500',
        onSelect && 'cursor-pointer',
        className
      )}
      onClick={onSelect}
    >
      {/* Image */}
      {showImage && poi.images?.[0] && (
        <div className={cn('relative', config.image)}>
          <img 
            src={poi.images[0]} 
            alt={poi.name}
            className="w-full h-full object-cover"
          />
          {poi.rating && (
            <div className="absolute top-2 right-2 bg-white/90 backdrop-blur-sm rounded-full px-2 py-1 flex items-center gap-1">
              <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
              <span className="text-xs font-medium">{poi.rating.toFixed(1)}</span>
            </div>
          )}
        </div>
      )}
      
      {/* Content */}
      <div className={config.container}>
        {/* Header */}
        <div className="flex items-start justify-between gap-2 mb-2">
          <h3 className={cn(config.title, 'line-clamp-2')}>{poi.name}</h3>
          {showActions && !actions && (
            <Button
              size="sm"
              variant={isAdded ? "outline" : "default"}
              onClick={(e) => {
                e.stopPropagation();
                isAdded ? onRemove?.() : onAdd?.();
              }}
              className={config.button}
            >
              {isAdded ? <Check className="w-3 h-3" /> : <Plus className="w-3 h-3" />}
            </Button>
          )}
        </div>
        
        {/* Category Badge */}
        <Badge variant="secondary" className={cn('mb-2', getCategoryColor(poi.category))}>
          {getCategoryDisplayName(poi.category)}
        </Badge>
        
        {/* Description */}
        <p className={cn(config.description, 'text-gray-600 line-clamp-2 mb-3')}>
          {poi.description}
        </p>
        
        {/* Metadata */}
        {showMetadata && (
          <div className={cn('flex items-center gap-4 text-gray-500', config.metadata)}>
            {poi.duration && (
              <div className="flex items-center gap-1">
                <Clock className="w-3 h-3" />
                <span>{formatDuration(poi.duration)}</span>
              </div>
            )}
            {poi.cost !== undefined && (
              <div className="flex items-center gap-1">
                <DollarSign className="w-3 h-3" />
                <span>{formatCost(poi.cost)}</span>
              </div>
            )}
            {poi.location && (
              <div className="flex items-center gap-1">
                <MapPin className="w-3 h-3" />
                <span className="truncate">{poi.location}</span>
              </div>
            )}
          </div>
        )}
        
        {/* Custom Actions */}
        {actions && (
          <div className="mt-3 pt-3 border-t">
            {actions}
          </div>
        )}
        
        {/* Custom Footer */}
        {footer && (
          <div className="mt-3">
            {footer}
          </div>
        )}
      </div>
    </div>
  );
  
  // ========================================
  // RENDER BASED ON VARIANT
  // ========================================
  
  switch (variant) {
    case 'compact':
      return renderCompactLayout();
    case 'list':
      return renderListLayout();
    case 'enhanced':
    case 'default':
    default:
      return renderCardLayout();
  }
};

export default UnifiedPOICard;
