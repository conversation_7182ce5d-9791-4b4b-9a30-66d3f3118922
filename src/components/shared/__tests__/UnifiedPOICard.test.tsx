/**
 * UnifiedPOICard.test.tsx
 * 
 * COMPREHENSIVE TESTS FOR UNIFIED POI CARD
 * Tests all variants, props, and functionality
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { UnifiedPOICard } from '../UnifiedPOICard';
import { PointOfInterest } from '@/types';

// ========================================
// TEST DATA
// ========================================

const mockPOI: PointOfInterest = {
  id: 'test-poi-1',
  name: 'Test POI',
  description: 'A test point of interest for unit testing',
  position: { lng: -7.0926, lat: 31.7917 },
  coordinates: [-7.0926, 31.7917],
  category: 'landmark',
  images: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
  tags: ['test', 'landmark', 'historic'],
  rating: 4.5,
  reviews: 123,
  duration: 120, // 2 hours
  cost: 25,
  location: 'Test City, Test Country'
};

const mockPOIMinimal: PointOfInterest = {
  id: 'test-poi-minimal',
  name: 'Minimal POI',
  description: 'Minimal POI for testing',
  position: { lng: 0, lat: 0 },
  coordinates: [0, 0],
  category: 'other',
  images: [],
  tags: []
};

// ========================================
// MOCK FUNCTIONS
// ========================================

const mockOnAdd = vi.fn();
const mockOnRemove = vi.fn();
const mockOnSelect = vi.fn();
const mockOnView = vi.fn();

// ========================================
// TEST SETUP
// ========================================

beforeEach(() => {
  vi.clearAllMocks();
});

// ========================================
// BASIC RENDERING TESTS
// ========================================

describe('UnifiedPOICard - Basic Rendering', () => {
  it('renders with default props', () => {
    render(<UnifiedPOICard poi={mockPOI} />);
    
    expect(screen.getByText('Test POI')).toBeInTheDocument();
    expect(screen.getByText('A test point of interest for unit testing')).toBeInTheDocument();
    expect(screen.getByText('Landmarks')).toBeInTheDocument();
  });

  it('renders minimal POI correctly', () => {
    render(<UnifiedPOICard poi={mockPOIMinimal} />);
    
    expect(screen.getByText('Minimal POI')).toBeInTheDocument();
    expect(screen.getByText('Other')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(
      <UnifiedPOICard poi={mockPOI} className="custom-class" />
    );
    
    expect(container.firstChild).toHaveClass('custom-class');
  });
});

// ========================================
// VARIANT TESTS
// ========================================

describe('UnifiedPOICard - Variants', () => {
  it('renders default variant correctly', () => {
    render(<UnifiedPOICard poi={mockPOI} variant="default" />);
    
    // Should show image, full content, and metadata
    expect(screen.getByRole('img')).toBeInTheDocument();
    expect(screen.getByText('2h')).toBeInTheDocument(); // duration
    expect(screen.getByText('$25.00')).toBeInTheDocument(); // cost
  });

  it('renders enhanced variant correctly', () => {
    render(<UnifiedPOICard poi={mockPOI} variant="enhanced" />);
    
    // Enhanced variant should be similar to default
    expect(screen.getByRole('img')).toBeInTheDocument();
    expect(screen.getByText('Test POI')).toBeInTheDocument();
  });

  it('renders compact variant correctly', () => {
    render(<UnifiedPOICard poi={mockPOI} variant="compact" />);
    
    // Compact variant should show basic info
    expect(screen.getByText('Test POI')).toBeInTheDocument();
    expect(screen.getByText('Landmarks')).toBeInTheDocument();
  });

  it('renders list variant correctly', () => {
    render(<UnifiedPOICard poi={mockPOI} variant="list" />);
    
    // List variant should show minimal info
    expect(screen.getByText('Test POI')).toBeInTheDocument();
    expect(screen.getByText('A test point of interest for unit testing')).toBeInTheDocument();
  });
});

// ========================================
// SIZE TESTS
// ========================================

describe('UnifiedPOICard - Sizes', () => {
  it('renders small size correctly', () => {
    render(<UnifiedPOICard poi={mockPOI} size="sm" />);
    
    expect(screen.getByText('Test POI')).toBeInTheDocument();
    // Small size should still show all content but with smaller styling
  });

  it('renders medium size correctly', () => {
    render(<UnifiedPOICard poi={mockPOI} size="md" />);
    
    expect(screen.getByText('Test POI')).toBeInTheDocument();
  });

  it('renders large size correctly', () => {
    render(<UnifiedPOICard poi={mockPOI} size="lg" />);
    
    expect(screen.getByText('Test POI')).toBeInTheDocument();
  });
});

// ========================================
// INTERACTION TESTS
// ========================================

describe('UnifiedPOICard - Interactions', () => {
  it('calls onAdd when add button is clicked', async () => {
    render(<UnifiedPOICard poi={mockPOI} onAdd={mockOnAdd} />);
    
    const addButton = screen.getByRole('button');
    fireEvent.click(addButton);
    
    await waitFor(() => {
      expect(mockOnAdd).toHaveBeenCalledTimes(1);
    });
  });

  it('calls onRemove when remove button is clicked (when isAdded=true)', async () => {
    render(
      <UnifiedPOICard 
        poi={mockPOI} 
        isAdded={true} 
        onRemove={mockOnRemove} 
      />
    );
    
    const removeButton = screen.getByRole('button');
    fireEvent.click(removeButton);
    
    await waitFor(() => {
      expect(mockOnRemove).toHaveBeenCalledTimes(1);
    });
  });

  it('calls onSelect when card is clicked', async () => {
    render(<UnifiedPOICard poi={mockPOI} onSelect={mockOnSelect} />);
    
    const card = screen.getByText('Test POI').closest('div');
    fireEvent.click(card!);
    
    await waitFor(() => {
      expect(mockOnSelect).toHaveBeenCalledTimes(1);
    });
  });

  it('prevents event bubbling when button is clicked', async () => {
    render(
      <UnifiedPOICard 
        poi={mockPOI} 
        onAdd={mockOnAdd} 
        onSelect={mockOnSelect} 
      />
    );
    
    const addButton = screen.getByRole('button');
    fireEvent.click(addButton);
    
    await waitFor(() => {
      expect(mockOnAdd).toHaveBeenCalledTimes(1);
      expect(mockOnSelect).not.toHaveBeenCalled();
    });
  });
});

// ========================================
// CONDITIONAL RENDERING TESTS
// ========================================

describe('UnifiedPOICard - Conditional Rendering', () => {
  it('hides image when showImage=false', () => {
    render(<UnifiedPOICard poi={mockPOI} showImage={false} />);
    
    expect(screen.queryByRole('img')).not.toBeInTheDocument();
  });

  it('hides actions when showActions=false', () => {
    render(<UnifiedPOICard poi={mockPOI} showActions={false} />);
    
    expect(screen.queryByRole('button')).not.toBeInTheDocument();
  });

  it('hides metadata when showMetadata=false', () => {
    render(<UnifiedPOICard poi={mockPOI} showMetadata={false} />);
    
    expect(screen.queryByText('2h')).not.toBeInTheDocument();
    expect(screen.queryByText('$25.00')).not.toBeInTheDocument();
  });

  it('shows custom actions when provided', () => {
    const customActions = <button>Custom Action</button>;
    
    render(
      <UnifiedPOICard 
        poi={mockPOI} 
        actions={customActions} 
      />
    );
    
    expect(screen.getByText('Custom Action')).toBeInTheDocument();
  });

  it('shows custom footer when provided', () => {
    const customFooter = <div>Custom Footer</div>;
    
    render(
      <UnifiedPOICard 
        poi={mockPOI} 
        footer={customFooter} 
      />
    );
    
    expect(screen.getByText('Custom Footer')).toBeInTheDocument();
  });
});

// ========================================
// STATE TESTS
// ========================================

describe('UnifiedPOICard - State', () => {
  it('shows selected state correctly', () => {
    const { container } = render(
      <UnifiedPOICard poi={mockPOI} isSelected={true} />
    );
    
    // Should have selected styling (ring or background change)
    expect(container.firstChild).toHaveClass('ring-2');
  });

  it('shows added state correctly', () => {
    render(<UnifiedPOICard poi={mockPOI} isAdded={true} />);
    
    // Should show check icon instead of plus
    expect(screen.getByRole('button')).toBeInTheDocument();
  });
});

// ========================================
// ACCESSIBILITY TESTS
// ========================================

describe('UnifiedPOICard - Accessibility', () => {
  it('has proper ARIA labels', () => {
    render(<UnifiedPOICard poi={mockPOI} />);
    
    const image = screen.getByRole('img');
    expect(image).toHaveAttribute('alt', 'Test POI');
  });

  it('is keyboard navigable', () => {
    render(<UnifiedPOICard poi={mockPOI} onSelect={mockOnSelect} />);
    
    const card = screen.getByText('Test POI').closest('div');
    
    // Should be focusable if clickable
    if (card?.getAttribute('onClick')) {
      expect(card).toHaveAttribute('tabIndex');
    }
  });

  it('has proper button roles', () => {
    render(<UnifiedPOICard poi={mockPOI} onAdd={mockOnAdd} />);
    
    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
  });
});

// ========================================
// EDGE CASES
// ========================================

describe('UnifiedPOICard - Edge Cases', () => {
  it('handles POI without images', () => {
    const poiWithoutImages = { ...mockPOI, images: [] };
    
    render(<UnifiedPOICard poi={poiWithoutImages} />);
    
    expect(screen.queryByRole('img')).not.toBeInTheDocument();
    expect(screen.getByText('Test POI')).toBeInTheDocument();
  });

  it('handles POI without rating', () => {
    const poiWithoutRating = { ...mockPOI, rating: undefined };
    
    render(<UnifiedPOICard poi={poiWithoutRating} />);
    
    expect(screen.getByText('Test POI')).toBeInTheDocument();
    // Should not show star rating
  });

  it('handles POI without cost', () => {
    const poiWithoutCost = { ...mockPOI, cost: undefined };
    
    render(<UnifiedPOICard poi={poiWithoutCost} />);
    
    expect(screen.getByText('Free')).toBeInTheDocument();
  });

  it('handles very long descriptions', () => {
    const poiWithLongDescription = {
      ...mockPOI,
      description: 'A'.repeat(500) // Very long description
    };
    
    render(<UnifiedPOICard poi={poiWithLongDescription} />);
    
    expect(screen.getByText('Test POI')).toBeInTheDocument();
    // Description should be truncated with line-clamp
  });

  it('handles missing optional props gracefully', () => {
    expect(() => {
      render(<UnifiedPOICard poi={mockPOIMinimal} />);
    }).not.toThrow();
  });
});
