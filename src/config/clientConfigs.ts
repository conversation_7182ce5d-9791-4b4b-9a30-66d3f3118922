/**
 * clientConfigs.ts
 * 
 * Sample client configurations for development and testing
 */

import { ClientConfig } from '../types/ClientTypes';

/**
 * Map of client configurations by client ID
 */
export const clientConfigs: Record<string, ClientConfig> = {
  neutral: {
    id: 'neutral',
    name: 'Demo Framework',
    theme: {
      primaryColor: '#2563eb',
      secondaryColor: '#64748b',
      accentColor: '#0ea5e9',
      backgroundColor: '#ffffff',
      textColor: '#1e293b',
      fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      logoUrl: '/assets/neutral/logo.svg'
    },
    mapSettings: {
      initialBounds: [
        [-125.0, 25.0],  // Southwest corner (covers US)
        [-65.0, 50.0]    // Northeast corner
      ],
      defaultCenter: [-95.7129, 37.0902], // Geographic center of US
      defaultZoom: 4,
      minZoom: 3,
      maxZoom: 18,
      style: 'mapbox://styles/mapbox/streets-v11',
      padding: {
        top: 50,
        bottom: 50,
        left: 50,
        right: 50
      }
    },
    poiSettings: {
      categories: [
        { id: 'landmark', name: 'Landmarks', icon: 'landmark', color: '#2563eb' },
        { id: 'nature', name: 'Nature', icon: 'tree', color: '#059669' },
        { id: 'cultural', name: 'Cultural', icon: 'museum', color: '#7c2d12' },
        { id: 'activity', name: 'Activities', icon: 'activity', color: '#dc2626' }
      ],
      filters: [
        {
          id: 'type',
          name: 'Type',
          field: 'type',
          options: [
            { value: 'landmark', label: 'Landmark' },
            { value: 'nature', label: 'Nature' },
            { value: 'cultural', label: 'Cultural' },
            { value: 'activity', label: 'Activity' }
          ]
        }
      ],
      clustering: {
        enabled: true,
        radius: 50,
        maxZoom: 14,
        minPoints: 2
      },
      displayRules: {
        minZoom: 8,
        maxDistance: 100,
        priorityField: 'rating'
      }
    },
    featureFlags: {
      routeAnimation: true,
      poiDiscovery: true,
      itineraryBuilder: true,
      userAccounts: false,
      routeSaving: false,
      analytics: true
    }
  },
  morocco: {
    id: 'morocco',
    name: 'Morocco',
    theme: {
      primaryColor: '#c53030',
      secondaryColor: '#dd6b20',
      accentColor: '#f6ad55',
      backgroundColor: '#ffffff',
      textColor: '#1a202c',
      fontFamily: '"Roboto", sans-serif',
      logoUrl: '/assets/morocco/logo.svg'
    },
    mapSettings: {
      initialBounds: [
        [-13.168555, 27.666667],
        [-1.030511, 35.922284]
      ],
      defaultCenter: [-7.09, 31.79],
      defaultZoom: 6,
      minZoom: 5,
      maxZoom: 18,
      style: 'mapbox://styles/mapbox/streets-v11',
      padding: {
        top: 50,
        bottom: 50,
        left: 50,
        right: 50
      }
    },
    poiSettings: {
      categories: [
        {
          id: 'landmark',
          name: 'Historic Landmarks',
          icon: 'landmark',
          color: '#8B4513'
        },
        {
          id: 'nature',
          name: 'Natural Wonders',
          icon: 'mountain',
          color: '#228B22'
        },
        {
          id: 'cultural',
          name: 'Cultural Sites',
          icon: 'museum',
          color: '#4169E1'
        },
        {
          id: 'adventure',
          name: 'Adventure Activities',
          icon: 'hiking',
          color: '#FF6347'
        },
        {
          id: 'scenic',
          name: 'Scenic Routes',
          icon: 'road',
          color: '#9370DB'
        },
        {
          id: 'photography',
          name: 'Photo Spots',
          icon: 'camera',
          color: '#FF1493'
        },
        {
          id: 'hidden-gem',
          name: 'Hidden Gems',
          icon: 'gem',
          color: '#20B2AA'
        },
        {
          id: 'local-experience',
          name: 'Local Experiences',
          icon: 'users',
          color: '#DAA520'
        }
      ],
      filters: [
        {
          id: 'type',
          name: 'Type',
          field: 'type',
          options: [
            { value: 'landmark', label: 'Landmarks' },
            { value: 'restaurant', label: 'Restaurants' },
            { value: 'accommodation', label: 'Accommodations' },
            { value: 'activity', label: 'Activities' }
          ]
        },
        {
          id: 'cost',
          name: 'Price Range',
          field: 'cost',
          options: [
            { value: 'low', label: 'Budget' },
            { value: 'medium', label: 'Mid-range' },
            { value: 'high', label: 'Luxury' }
          ]
        }
      ],
      clustering: {
        enabled: true,
        radius: 50,
        maxZoom: 14,
        minPoints: 2
      },
      displayRules: {
        minZoom: 10,
        maxDistance: 5000, // meters
        priorityField: 'priority'
      }
    },
    featureFlags: {
      routeAnimation: true,
      poiDiscovery: true,
      itineraryBuilder: true,
      userAccounts: true,
      routeSaving: true,
      analytics: true
    }
  },
  portugal: {
    id: 'portugal',
    name: 'Portugal',
    theme: {
      primaryColor: '#2b6cb0',
      secondaryColor: '#4299e1',
      accentColor: '#63b3ed',
      backgroundColor: '#ffffff',
      textColor: '#1a202c',
      fontFamily: '"Roboto", sans-serif',
      logoUrl: '/assets/portugal/logo.svg'
    },
    mapSettings: {
      initialBounds: [
        [-9.5, 36.8],
        [-6.2, 42.1]
      ],
      defaultCenter: [-8.61, 39.6],
      defaultZoom: 6,
      minZoom: 5,
      maxZoom: 18,
      style: 'mapbox://styles/mapbox/streets-v11',
      padding: {
        top: 50,
        bottom: 50,
        left: 50,
        right: 50
      }
    },
    poiSettings: {
      categories: [
        {
          id: 'landmark',
          name: 'Historic Landmarks',
          icon: 'landmark',
          color: '#8B4513'
        },
        {
          id: 'nature',
          name: 'Natural Wonders',
          icon: 'mountain',
          color: '#228B22'
        },
        {
          id: 'cultural',
          name: 'Cultural Sites',
          icon: 'museum',
          color: '#4169E1'
        },
        {
          id: 'adventure',
          name: 'Adventure Activities',
          icon: 'hiking',
          color: '#FF6347'
        },
        {
          id: 'scenic',
          name: 'Scenic Routes',
          icon: 'road',
          color: '#9370DB'
        },
        {
          id: 'photography',
          name: 'Photo Spots',
          icon: 'camera',
          color: '#FF1493'
        },
        {
          id: 'hidden-gem',
          name: 'Hidden Gems',
          icon: 'gem',
          color: '#20B2AA'
        },
        {
          id: 'local-experience',
          name: 'Local Experiences',
          icon: 'users',
          color: '#DAA520'
        }
      ],
      filters: [
        {
          id: 'type',
          name: 'Type',
          field: 'type',
          options: [
            { value: 'landmark', label: 'Landmarks' },
            { value: 'restaurant', label: 'Restaurants' },
            { value: 'accommodation', label: 'Accommodations' },
            { value: 'activity', label: 'Activities' }
          ]
        },
        {
          id: 'cost',
          name: 'Price Range',
          field: 'cost',
          options: [
            { value: 'low', label: 'Budget' },
            { value: 'medium', label: 'Mid-range' },
            { value: 'high', label: 'Luxury' }
          ]
        }
      ],
      clustering: {
        enabled: true,
        radius: 50,
        maxZoom: 14,
        minPoints: 2
      },
      displayRules: {
        minZoom: 10,
        maxDistance: 5000, // meters
        priorityField: 'priority'
      }
    },
    featureFlags: {
      routeAnimation: true,
      poiDiscovery: true,
      itineraryBuilder: true,
      userAccounts: false,
      routeSaving: true,
      analytics: true
    }
  }
};

/**
 * Get a client configuration by ID
 * @param clientId - The client ID
 * @returns The client configuration or null if not found
 */
export const getClientConfig = (clientId: string): ClientConfig | null => {
  return clientConfigs[clientId] || null;
};

/**
 * Get all available client IDs
 * @returns Array of client IDs
 */
export const getAvailableClientIds = (): string[] => {
  return Object.keys(clientConfigs);
};