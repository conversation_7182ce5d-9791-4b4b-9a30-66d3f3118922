/**
 * Route 66 Theme
 * 
 * Classic Americana theme for nostalgic road trips and adventure travel
 */

import { ThemeConfig } from '@/types/ClientTypes';

const route66Theme: ThemeConfig = {
  primaryColor: '#dc2626',      // Classic red
  secondaryColor: '#1e40af',    // Patriotic blue
  accentColor: '#fbbf24',       // Highway yellow
  backgroundColor: '#ffffff',
  textColor: '#1f2937',
  fontFamily: '"Bebas Neue", "Arial Black", sans-serif',
  logoUrl: '/assets/route66/logo.svg',
  
  // UI elements theming
  buttons: {
    primary: {
      background: '#dc2626',
      text: '#ffffff',
      hover: '#b91c1c',
      active: '#991b1b'
    },
    secondary: {
      background: '#dbeafe',
      text: '#1e40af',
      hover: '#bfdbfe',
      active: '#93c5fd'
    }
  },
  
  // Map elements theming
  map: {
    markerColors: {
      destination: '#dc2626',
      poi: '#1e40af',
      selectedPoi: '#fbbf24',
      vehicle: '#1f2937'
    },
    routeColor: '#dc2626',
    poiClusterColor: '#1e40af'
  },
  
  // Animation elements theming
  animation: {
    countdownColor: '#fbbf24',
    progressBarColor: '#dc2626',
    vehicleColor: '#1e40af'
  },
  
  // Overlay elements theming
  overlays: {
    background: 'rgba(255, 255, 255, 0.95)',
    headerBackground: 'rgba(220, 38, 38, 0.05)',
    borderColor: 'rgba(220, 38, 38, 0.2)',
    tagBackground: 'rgba(220, 38, 38, 0.1)',
    tagTextColor: '#dc2626'
  },
  
  // Text elements theming
  text: {
    heading: '#1f2937',
    paragraph: '#374151',
    muted: '#6b7280',
    highlight: '#dc2626'
  }
};

export default route66Theme;
