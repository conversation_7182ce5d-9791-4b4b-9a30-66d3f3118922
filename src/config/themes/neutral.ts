/**
 * Neutral Base Theme
 * 
 * Clean, professional theme for client demos and base framework
 */

import { ThemeConfig } from '@/types/ClientTypes';

const neutralTheme: ThemeConfig = {
  primaryColor: '#2563eb',      // Professional blue
  secondaryColor: '#64748b',    // Neutral gray
  accentColor: '#0ea5e9',       // Light blue
  backgroundColor: '#ffffff',
  textColor: '#1e293b',
  fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  logoUrl: '/assets/neutral/logo.svg',
  
  // UI elements theming
  buttons: {
    primary: {
      background: '#2563eb',
      text: '#ffffff',
      hover: '#1d4ed8',
      active: '#1e40af'
    },
    secondary: {
      background: '#f8fafc',
      text: '#2563eb',
      hover: '#f1f5f9',
      active: '#e2e8f0'
    }
  },
  
  // Map elements theming
  map: {
    markerColors: {
      destination: '#2563eb',
      poi: '#64748b',
      selectedPoi: '#0ea5e9',
      vehicle: '#1e293b'
    },
    routeColor: '#2563eb',
    poiClusterColor: '#64748b'
  },
  
  // Animation elements theming
  animation: {
    countdownColor: '#0ea5e9',
    progressBarColor: '#2563eb',
    vehicleColor: '#64748b'
  },
  
  // Overlay elements theming
  overlays: {
    background: 'rgba(255, 255, 255, 0.95)',
    headerBackground: 'rgba(37, 99, 235, 0.05)',
    borderColor: 'rgba(37, 99, 235, 0.2)',
    tagBackground: 'rgba(37, 99, 235, 0.1)',
    tagTextColor: '#2563eb'
  },
  
  // Text elements theming
  text: {
    heading: '#1e293b',
    paragraph: '#475569',
    muted: '#64748b',
    highlight: '#2563eb'
  }
};

export default neutralTheme;
