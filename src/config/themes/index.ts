/**
 * Theme Registry
 * 
 * This file exports all available themes and provides a registry for theme lookup by client ID
 */

import { ThemeConfig } from '@/types/ClientTypes';
import neutralTheme from './neutral';
import moroccoTheme from './morocco';
import portugalTheme from './portugal';
import napaValleyTheme from './napa-valley';
import route66Theme from './route66';

/**
 * All available themes indexed by client ID
 */
export const themes: Record<string, ThemeConfig> = {
  neutral: neutralTheme,
  morocco: moroccoTheme,
  portugal: portugalTheme,
  'napa-valley': napaValleyTheme,
  'route66': route66Theme
};

/**
 * Get theme by client ID
 * @param clientId Client identifier
 * @returns Theme configuration or default theme if not found
 */
export const getThemeByClientId = (clientId: string): ThemeConfig => {
  return themes[clientId] || themes.morocco; // Fall back to Morocco theme
};

/**
 * Default theme - Used as fallback
 */
export const defaultTheme = moroccoTheme;

export default themes; 