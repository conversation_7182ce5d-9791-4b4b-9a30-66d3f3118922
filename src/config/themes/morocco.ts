/**
 * Morocco Theme Configuration
 * 
 * This file defines the theme configuration for the Morocco client
 */

import { ThemeConfig } from '@/types/ClientTypes';

const moroccoTheme: ThemeConfig = {
  primaryColor: '#c53030',
  secondaryColor: '#dd6b20',
  accentColor: '#f6ad55',
  backgroundColor: '#ffffff',
  textColor: '#1a202c',
  fontFamily: '"Roboto", sans-serif',
  logoUrl: '/assets/morocco/logo.svg',
  
  // UI elements theming
  buttons: {
    primary: {
      background: '#c53030',
      text: '#ffffff',
      hover: '#b92121',
      active: '#a61c1c'
    },
    secondary: {
      background: '#f8f9fa',
      text: '#c53030',
      hover: '#e9ecef',
      active: '#dee2e6'
    }
  },
  
  // Map elements theming
  map: {
    markerColors: {
      destination: '#c53030',
      poi: '#dd6b20',
      selectedPoi: '#f6ad55',
      vehicle: '#1a202c'
    },
    routeColor: '#c53030',
    poiClusterColor: '#dd6b20'
  },
  
  // Animation elements theming
  animation: {
    countdownColor: '#c53030',
    progressBarColor: '#dd6b20',
    vehicleColor: '#c53030'
  },
  
  // Overlay elements theming
  overlays: {
    background: 'rgba(255, 255, 255, 0.9)',
    headerBackground: 'rgba(197, 48, 48, 0.1)',
    borderColor: 'rgba(197, 48, 48, 0.3)',
    tagBackground: 'rgba(197, 48, 48, 0.1)',
    tagTextColor: '#c53030'
  },
  
  // Text elements theming
  text: {
    heading: '#1a202c',
    paragraph: '#4a5568',
    muted: '#718096',
    highlight: '#c53030'
  }
};

export default moroccoTheme; 