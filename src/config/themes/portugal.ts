/**
 * Portugal Theme Configuration
 * 
 * This file defines the theme configuration for the Portugal client
 */

import { ThemeConfig } from '@/types/ClientTypes';

const portugalTheme: ThemeConfig = {
  primaryColor: '#2b6cb0',
  secondaryColor: '#4299e1',
  accentColor: '#63b3ed',
  backgroundColor: '#ffffff',
  textColor: '#1a202c',
  fontFamily: '"Roboto", sans-serif',
  logoUrl: '/assets/portugal/logo.svg',
  
  // UI elements theming
  buttons: {
    primary: {
      background: '#2b6cb0',
      text: '#ffffff',
      hover: '#2563a1',
      active: '#1d4e8c'
    },
    secondary: {
      background: '#f8f9fa',
      text: '#2b6cb0',
      hover: '#e9ecef',
      active: '#dee2e6'
    }
  },
  
  // Map elements theming
  map: {
    markerColors: {
      destination: '#2b6cb0',
      poi: '#4299e1',
      selectedPoi: '#63b3ed',
      vehicle: '#1a202c'
    },
    routeColor: '#2b6cb0',
    poiClusterColor: '#4299e1'
  },
  
  // Animation elements theming
  animation: {
    countdownColor: '#2b6cb0',
    progressBarColor: '#4299e1',
    vehicleColor: '#2b6cb0'
  },
  
  // Overlay elements theming
  overlays: {
    background: 'rgba(255, 255, 255, 0.9)',
    headerBackground: 'rgba(43, 108, 176, 0.1)',
    borderColor: 'rgba(43, 108, 176, 0.3)',
    tagBackground: 'rgba(43, 108, 176, 0.1)',
    tagTextColor: '#2b6cb0'
  },
  
  // Text elements theming
  text: {
    heading: '#1a202c',
    paragraph: '#4a5568',
    muted: '#718096',
    highlight: '#2b6cb0'
  }
};

export default portugalTheme; 