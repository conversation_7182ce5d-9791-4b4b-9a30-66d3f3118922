/**
 * Napa Valley Wine Theme
 * 
 * Elegant wine country theme for luxury wine tours and culinary experiences
 */

import { ThemeConfig } from '@/types/ClientTypes';

const napaValleyTheme: ThemeConfig = {
  primaryColor: '#7c2d12',      // Deep wine red
  secondaryColor: '#a16207',    // Golden amber
  accentColor: '#dc2626',       // Rich red
  backgroundColor: '#fefdf8',   // Warm cream
  textColor: '#1c1917',
  fontFamily: '"Playfair Display", Georgia, serif',
  logoUrl: '/assets/napa-valley/logo.svg',
  
  // UI elements theming
  buttons: {
    primary: {
      background: '#7c2d12',
      text: '#ffffff',
      hover: '#991b1b',
      active: '#7f1d1d'
    },
    secondary: {
      background: '#fef3c7',
      text: '#7c2d12',
      hover: '#fde68a',
      active: '#fcd34d'
    }
  },
  
  // Map elements theming
  map: {
    markerColors: {
      destination: '#7c2d12',
      poi: '#a16207',
      selectedPoi: '#dc2626',
      vehicle: '#1c1917'
    },
    routeColor: '#7c2d12',
    poiClusterColor: '#a16207'
  },
  
  // Animation elements theming
  animation: {
    countdownColor: '#dc2626',
    progressBarColor: '#7c2d12',
    vehicleColor: '#a16207'
  },
  
  // Overlay elements theming
  overlays: {
    background: 'rgba(254, 253, 248, 0.95)',
    headerBackground: 'rgba(124, 45, 18, 0.05)',
    borderColor: 'rgba(124, 45, 18, 0.2)',
    tagBackground: 'rgba(124, 45, 18, 0.1)',
    tagTextColor: '#7c2d12'
  },
  
  // Text elements theming
  text: {
    heading: '#1c1917',
    paragraph: '#44403c',
    muted: '#78716c',
    highlight: '#7c2d12'
  }
};

export default napaValleyTheme;
