/**
 * paths.ts
 * 
 * Central definition of path aliases for the application.
 * This ensures consistent type resolution across all files.
 */

/**
 * Re-export POITypes to ensure consistent imports
 */
export type { PointOfInterest, Destination } from '@/types';

/**
 * Re-export poi.ts types to ensure consistent imports
 */
export type { PointOfInterest as POIBase } from '../types/poi';

/**
 * Re-export Position types to ensure consistent imports
 */
export type { 
  Position, 
  MapPosition, 
  GeoPosition, 
  LatLngPosition,
  PositionTuple,
  PositionObject
} from '../types/Position';

// Re-export functions from Position.ts
export { 
  isPosition,
  isMapPosition, 
  isGeoPosition,
  isLatLngPosition,
  isAnyPosition,
  toPosition,
  safeToPosition,
  createPosition,
  safeCreatePosition,
  positionToMapPosition,
  positionToGeoPosition,
  positionToLatLngPosition,
  mapPositionToPosition,
  geoPositionToPosition,
  latLngPositionToPosition
} from '../types/Position';

/**
 * Type conversion helper
 * Converts between POITypes.PointOfInterest and poi.POIBase consistently
 */
export function convertPoiTypes(poi: any): any {
  // Check which type of POI we're dealing with
  const hasPosition = 'position' in poi;
  const hasCoordinates = 'coordinates' in poi;
  
  if (hasPosition) {
    // Convert from POITypes.PointOfInterest to poi.PointOfInterest
    const position = poi.position;
    const coordinates = Array.isArray(position) 
      ? position 
      : ('lng' in position && 'lat' in position)
        ? [position.lng, position.lat]
        : [0, 0];
        
    return {
      ...poi,
      coordinates,
      type: poi.type || 'landmark',
      tags: poi.tags || []
    };
  } else if (hasCoordinates) {
    // Convert from poi.PointOfInterest to POITypes.PointOfInterest
    return {
      ...poi,
      position: poi.coordinates,
      type: poi.type || 'landmark',
      tags: poi.tags || []
    };
  }
  
  // Return as-is if we can't determine the type
  return poi;
} 