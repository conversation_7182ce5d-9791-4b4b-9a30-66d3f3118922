/**
 * themes.ts
 * 
 * Configuration file for client-specific themes
 */

import { ThemeConfig } from '../types/ClientTypes';

/**
 * Default theme configuration (now neutral)
 */
export const defaultTheme: ThemeConfig = {
  primaryColor: '#2563eb',
  secondaryColor: '#64748b',
  accentColor: '#0ea5e9',
  backgroundColor: '#ffffff',
  textColor: '#1e293b',
  fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  logoUrl: '/assets/neutral/logo.svg',
  
  // Extended theme configurations
  buttons: {
    primary: {
      background: '#2563eb',
      text: '#ffffff',
      hover: '#1d4ed8',
      active: '#1e40af'
    },
    secondary: {
      background: '#f8fafc',
      text: '#2563eb',
      hover: '#f1f5f9',
      active: '#e2e8f0'
    }
  },

  map: {
    markerColors: {
      destination: '#2563eb',
      poi: '#64748b',
      selectedPoi: '#0ea5e9',
      vehicle: '#1e293b'
    },
    routeColor: '#2563eb',
    poiClusterColor: '#64748b'
  },

  animation: {
    countdownColor: '#0ea5e9',
    progressBarColor: '#2563eb',
    vehicleColor: '#64748b'
  },
  
  overlays: {
    background: 'rgba(255, 255, 255, 0.95)',
    headerBackground: '#f7fafc',
    borderColor: '#e2e8f0',
    tagBackground: '#e2e8f0',
    tagTextColor: '#4a5568'
  },
  
  text: {
    heading: '#2d3748',
    paragraph: '#4a5568',
    muted: '#718096',
    highlight: '#c53030'
  }
};

/**
 * Morocco theme configuration
 */
export const moroccoTheme: ThemeConfig = {
  primaryColor: '#c53030',
  secondaryColor: '#dd6b20',
  accentColor: '#f6ad55',
  backgroundColor: '#ffffff',
  textColor: '#1a202c',
  fontFamily: '"Roboto", sans-serif',
  logoUrl: '/assets/morocco/logo.svg',
  
  buttons: {
    primary: {
      background: '#c53030',
      text: '#ffffff',
      hover: '#e53e3e',
      active: '#9b2c2c'
    },
    secondary: {
      background: '#f7fafc',
      text: '#1a202c',
      hover: '#edf2f7',
      active: '#e2e8f0'
    }
  },
  
  map: {
    markerColors: {
      destination: '#c53030',
      poi: '#dd6b20',
      selectedPoi: '#9b2c2c',
      vehicle: '#f6ad55'
    },
    routeColor: '#c53030',
    poiClusterColor: '#dd6b20'
  },
  
  animation: {
    countdownColor: '#f6ad55',
    progressBarColor: '#c53030',
    vehicleColor: '#dd6b20'
  },
  
  overlays: {
    background: 'rgba(255, 255, 255, 0.95)',
    headerBackground: '#f7fafc',
    borderColor: '#e2e8f0',
    tagBackground: '#edf2f7',
    tagTextColor: '#4a5568'
  },
  
  text: {
    heading: '#2d3748',
    paragraph: '#4a5568',
    muted: '#718096',
    highlight: '#c53030'
  }
};

/**
 * Portugal theme configuration
 */
export const portugalTheme: ThemeConfig = {
  primaryColor: '#2b6cb0',
  secondaryColor: '#4299e1',
  accentColor: '#63b3ed',
  backgroundColor: '#ffffff',
  textColor: '#1a202c',
  fontFamily: '"Roboto", sans-serif',
  logoUrl: '/assets/portugal/logo.svg',
  
  buttons: {
    primary: {
      background: '#2b6cb0',
      text: '#ffffff',
      hover: '#3182ce',
      active: '#2c5282'
    },
    secondary: {
      background: '#f7fafc',
      text: '#1a202c',
      hover: '#edf2f7',
      active: '#e2e8f0'
    }
  },
  
  map: {
    markerColors: {
      destination: '#2b6cb0',
      poi: '#4299e1',
      selectedPoi: '#2c5282',
      vehicle: '#63b3ed'
    },
    routeColor: '#3182ce',
    poiClusterColor: '#4299e1'
  },
  
  animation: {
    countdownColor: '#63b3ed',
    progressBarColor: '#2b6cb0',
    vehicleColor: '#4299e1'
  },
  
  overlays: {
    background: 'rgba(255, 255, 255, 0.95)',
    headerBackground: '#f7fafc',
    borderColor: '#e2e8f0',
    tagBackground: '#ebf8ff',
    tagTextColor: '#2c5282'
  },
  
  text: {
    heading: '#2d3748',
    paragraph: '#4a5568',
    muted: '#718096',
    highlight: '#2b6cb0'
  }
};

/**
 * Global app theme configuration (for multi-country app)
 */
export const globalTheme: ThemeConfig = {
  primaryColor: '#38a169',
  secondaryColor: '#48bb78',
  accentColor: '#9ae6b4',
  backgroundColor: '#ffffff',
  textColor: '#1a202c',
  fontFamily: '"Roboto", sans-serif',
  logoUrl: '/assets/global/logo.svg',
  
  buttons: {
    primary: {
      background: '#38a169',
      text: '#ffffff',
      hover: '#48bb78',
      active: '#2f855a'
    },
    secondary: {
      background: '#f7fafc',
      text: '#1a202c',
      hover: '#edf2f7',
      active: '#e2e8f0'
    }
  },
  
  map: {
    markerColors: {
      destination: '#38a169',
      poi: '#48bb78',
      selectedPoi: '#2f855a',
      vehicle: '#9ae6b4'
    },
    routeColor: '#38a169',
    poiClusterColor: '#48bb78'
  },
  
  animation: {
    countdownColor: '#9ae6b4',
    progressBarColor: '#38a169',
    vehicleColor: '#48bb78'
  },
  
  overlays: {
    background: 'rgba(255, 255, 255, 0.95)',
    headerBackground: 'rgba(37, 99, 235, 0.05)',
    borderColor: 'rgba(37, 99, 235, 0.2)',
    tagBackground: 'rgba(37, 99, 235, 0.1)',
    tagTextColor: '#2563eb'
  },

  text: {
    heading: '#1e293b',
    paragraph: '#475569',
    muted: '#64748b',
    highlight: '#2563eb'
  }
};

/**
 * Theme registry
 */
const themes: Record<string, ThemeConfig> = {
  neutral: defaultTheme,
  morocco: moroccoTheme,
  portugal: portugalTheme,
  global: globalTheme
};

/**
 * Get theme by client ID
 */
export const getThemeByClientId = (clientId: string): ThemeConfig => {
  return themes[clientId] || defaultTheme;
}; 