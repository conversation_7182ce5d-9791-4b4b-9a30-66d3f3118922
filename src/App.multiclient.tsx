/**
 * App.multiclient.tsx
 * 
 * Sample App component with multi-client architecture integration
 * This can be used as a reference for updating the main App.tsx
 */

import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ClientProvider } from './contexts/ClientContext';
import { ThemeProvider } from './providers/ThemeProvider';
import { MapReadyProvider } from './providers/MapReadyProvider';

// Import pages
import HomePage from './pages/HomePage';
import MapView from './pages/MapView';
import NotFound from './pages/NotFound';

// Get client ID from URL or environment
const getInitialClientId = (): string => {
  // Check for client ID in URL path (e.g., /morocco/...)
  const pathSegments = window.location.pathname.split('/');
  const clientIdFromPath = pathSegments[1];
  
  // Check for client ID in URL query parameter
  const urlParams = new URLSearchParams(window.location.search);
  const clientIdFromQuery = urlParams.get('client');
  
  // Check for client ID in localStorage (for persistence)
  const clientIdFromStorage = localStorage.getItem('clientId');
  
  // Return the first available client ID or default to 'morocco'
  return clientIdFromPath || clientIdFromQuery || clientIdFromStorage || 'morocco';
};

const App: React.FC = () => {
  const initialClientId = getInitialClientId();
  
  // Store client ID in localStorage for persistence
  React.useEffect(() => {
    localStorage.setItem('clientId', initialClientId);
  }, [initialClientId]);
  
  return (
    <ClientProvider initialClientId={initialClientId}>
      <ThemeProvider>
        <MapReadyProvider>
          <Router>
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/map" element={<MapView />} />
              <Route path="*" element={<NotFound />} />
            </Routes>
          </Router>
        </MapReadyProvider>
      </ThemeProvider>
    </ClientProvider>
  );
};

export default App;