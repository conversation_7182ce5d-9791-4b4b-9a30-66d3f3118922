import React from 'react';
import MoroccanLayout from './components/layout/MoroccanLayout';
import './styles/moroccan-theme.css';

const MoroccanTest = () => {
  return (
    <MoroccanLayout>
      <div style={{ 
        display: 'flex', 
        flexDirection: 'column', 
        alignItems: 'center', 
        justifyContent: 'center',
        height: '100%',
        width: '100%',
        backgroundColor: 'var(--morocco-sand-lightest)'
      }}>
        <h1 style={{ 
          fontFamily: 'var(--font-display)',
          color: 'var(--morocco-red)',
          fontSize: '3rem',
          marginBottom: '2rem'
        }}>
          Come To Morocco
        </h1>
        <h2 style={{ 
          fontFamily: 'var(--font-heading)',
          color: 'var(--morocco-blue)',
          fontSize: '2rem',
          marginBottom: '1.5rem'
        }}>
          Journey Insights
        </h2>
        <p style={{ 
          fontFamily: 'var(--font-body)',
          maxWidth: '600px',
          textAlign: 'center',
          fontSize: '1.25rem',
          lineHeight: '1.6',
          marginBottom: '2rem'
        }}>
          This test page demonstrates our new font choices for the Morocco theme:
        </p>
        <ul style={{
          fontFamily: 'var(--font-body)',
          marginBottom: '2rem',
          listStyleType: 'none',
          padding: '0',
          textAlign: 'center'
        }}>
          <li style={{ marginBottom: '1rem' }}>
            <span style={{ fontFamily: 'var(--font-display)', fontWeight: 'bold' }}>Cinzel Decorative</span> for display text
          </li>
          <li style={{ marginBottom: '1rem' }}>
            <span style={{ fontFamily: 'var(--font-heading)', fontWeight: 'bold' }}>Marcellus</span> for headings
          </li>
          <li style={{ marginBottom: '1rem' }}>
            <span style={{ fontFamily: 'var(--font-body)', fontWeight: 'bold' }}>Lato</span> for body text
          </li>
          <li>
            <span style={{ fontFamily: 'var(--font-decorative)', fontWeight: 'bold' }}>Scheherazade New</span> for decorative elements
          </li>
        </ul>
        <div style={{
          fontFamily: 'var(--font-decorative)',
          fontSize: '2.5rem',
          color: 'var(--morocco-blue)',
          marginTop: '1rem'
        }}>
          رحلة رائعة
        </div>
      </div>
    </MoroccanLayout>
  );
};

export default MoroccanTest; 