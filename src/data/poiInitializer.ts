import { PointOfInterest } from '@/types/POITypes.updated';
import { pointsOfInterest } from './pointsOfInterest';

// Transform a single POI
function transformToPOI(poi: typeof pointsOfInterest[0]): PointOfInterest {
  // Handle both coordinate formats
  const position = Array.isArray(poi.coordinates) 
    ? { lng: poi.coordinates[0], lat: poi.coordinates[1] }
    : poi.position 
      ? { lng: poi.position.lng, lat: poi.position.lat }
      : { lng: 0, lat: 0 };

  return {
    id: poi.id,
    clientId: 'default', // Default client ID
    name: poi.name,
    description: poi.description || '',
    position,
    type: poi.type,
    image: poi.image,
    rating: poi.rating || 0,
    tags: poi.tags || [],
    categories: poi.categories || [],
    duration: poi.duration || 0,
    cost: poi.cost || 0,
    location: poi.location
  };
}

// Cache for POIs by city
const poiCache: Record<string, PointOfInterest[]> = {};

// Create POI lookup by city
export function getPOIsByCity(cityName: string): PointOfInterest[] {
  const key = cityName.toLowerCase();
  
  // Return from cache if available
  if (poiCache[key]) {
    return poiCache[key];
  }

  // Initialize POIs for this city
  const cityPOIs = pointsOfInterest
    .filter(poi => {
      // Extract main city name from location
      const poiCity = poi.location.split(',')[0].trim().toLowerCase();
      return poiCity === key;
    })
    .map(transformToPOI);

  // Cache the result
  poiCache[key] = cityPOIs;
  
  return cityPOIs;
} 