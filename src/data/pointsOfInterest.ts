import { PointOfInterest } from '@/types'; // Corrected import path

export const pointsOfInterest: PointOfInterest[] = [
  // Marrakech
  {
    id: 'jardin-majorelle',
    name: '<PERSON><PERSON><PERSON>',
    description: 'A two and half acre garden created by the French Orientalist artist <PERSON> over almost forty years.',
    position: { lng: -7.9896, lat: 31.6423 },
    coordinates: [-7.9896, 31.6423],
    category: 'landmark',
    images: ['https://images.unsplash.com/photo-1582261784827-3602947afa77?q=80&w=1000'],
    tags: ['garden', 'art', 'peaceful', 'photography'],
  },
  {
    id: 'bahia-palace',
    name: 'Bahia Palace',
    description: 'A palace and set of gardens built in the late 19th century, intended to be the greatest palace of its time.',
    position: { lng: -7.9826, lat: 31.6215 },
    coordinates: [-7.9826, 31.6215],
    category: 'landmark',
    images: ['https://images.unsplash.com/photo-1626066832794-4a92ea5a5d88?q=80&w=1000'],
    tags: ['history', 'architecture', 'palace'],
  },
  {
    id: 'riad-marrakech',
    name: 'Traditional Riad Stay',
    description: 'Experience authentic Moroccan hospitality in a beautifully restored traditional house with a central courtyard.',
    position: { lng: -7.9932, lat: 31.6300 },
    coordinates: [-7.9932, 31.6300],
    category: 'accommodation',
    images: ['https://images.unsplash.com/photo-1577401239170-897942555fb3?q=80&w=1000'],
    tags: ['traditional', 'luxury', 'courtyard', 'boutique'],
    location: 'Marrakech Medina',
  },
  {
    id: 'cooking-class-marrakech',
    name: 'Moroccan Cooking Class',
    description: 'Learn to prepare traditional Moroccan dishes including tagine and couscous with local chefs.',
    position: { lng: -7.9980, lat: 31.6290 },
    coordinates: [-7.9980, 31.6290],
    category: 'activity',
    images: ['https://images.unsplash.com/photo-1511690743698-d9d85f2fbf38?q=80&w=1000'],
    tags: ['cooking', 'food', 'cultural', 'hands-on'],
    location: 'Marrakech',
  },
  
  // Fes
  {
    id: 'al-attarine-madrasa',
    name: 'Al-Attarine Madrasa',
    description: 'A historical madrasa founded in the 14th century, known for its stunning architecture and tilework.',
    position: { lng: -4.9753, lat: 34.0648 },
    coordinates: [-4.9753, 34.0648],
    category: 'landmark',
    images: ['https://images.unsplash.com/photo-1570568130941-513064167641?q=80&w=1000'],
    tags: ['history', 'architecture', 'islamic art'],
  },
  {
    id: 'chouara-tannery',
    name: 'Chouara Tannery',
    description: 'One of the oldest tanneries in the world, offering a glimpse into traditional leather making.',
    position: { lng: -4.9739, lat: 34.0675 },
    coordinates: [-4.9739, 34.0675],
    category: 'landmark',
    images: ['https://images.unsplash.com/photo-1498712681408-fdcfb0eba0f2?q=80&w=1000'],
    tags: ['craft', 'traditional', 'photography'],
  },
  {
    id: 'riad-fes',
    name: 'Luxury Riad in Fes',
    description: 'An elegant riad combining traditional Moroccan design with modern luxury amenities.',
    position: { lng: -4.9780, lat: 34.0630 },
    coordinates: [-4.9780, 34.0630],
    category: 'accommodation',
    images: ['https://images.unsplash.com/photo-1570555587227-b375f198d288?q=80&w=1000'],
    tags: ['luxury', 'pool', 'spa', 'traditional'],
    location: 'Fes Medina',
  },
  
  // Chefchaouen
  {
    id: 'chefchaouen-photography',
    name: 'Blue City Photography Tour',
    description: 'Guided photography tour through the most picturesque spots in the blue city.',
    position: { lng: -5.2636, lat: 35.1689 },
    coordinates: [-5.2636, 35.1689],
    category: 'activity',
    images: ['https://images.unsplash.com/photo-1526045602737-b92507e0d37f?q=80&w=1000'],
    tags: ['photography', 'guided tour', 'walking'],
    location: 'Chefchaouen',
  },
  {
    id: 'casa-perleta',
    name: 'Casa Perleta',
    description: 'Charming guest house in the heart of the blue city with terrace views of the medina.',
    position: { lng: -5.2650, lat: 35.1670 },
    coordinates: [-5.2650, 35.1670],
    category: 'accommodation',
    images: ['https://images.unsplash.com/photo-1553329261-91d8d9d23346?q=80&w=1000'],
    tags: ['boutique', 'views', 'terrace', 'central'],
    location: 'Chefchaouen Medina',
  },
  
  // Merzouga - Sahara Desert
  {
    id: 'desert-camp',
    name: 'Luxury Desert Camp',
    description: 'Upscale glamping experience in the Sahara with private tents and traditional entertainment.',
    position: { lng: -4.0147, lat: 31.0935 },
    coordinates: [-4.0147, 31.0935],
    category: 'accommodation',
    images: ['https://images.unsplash.com/photo-1518548419970-58e3b4079ab2?q=80&w=1000'],
    tags: ['desert', 'luxury', 'stargazing', 'unique'],
    location: 'Merzouga, Sahara Desert',
  },
  {
    id: 'camel-trek',
    name: 'Sunset Camel Trek',
    description: 'Ride camels through the Sahara dunes and watch the sunset over the golden landscape.',
    position: { lng: -4.0200, lat: 31.0950 },
    coordinates: [-4.0200, 31.0950],
    category: 'activity',
    images: ['https://images.unsplash.com/photo-1489493887464-892be6d1daae?q=80&w=1000'],
    tags: ['adventure', 'nature', 'animals', 'sunset'],
    location: 'Merzouga, Sahara Desert',
  },
  
  // Essaouira - Correctly positioned on land
  {
    id: 'essaouira-surfing',
    name: 'Wind Surfing Lesson',
    description: 'Learn wind surfing with professional instructors on the windy beaches of Essaouira.',
    position: { lng: -9.7742, lat: 31.5109 },
    coordinates: [-9.7742, 31.5109],
    category: 'activity',
    images: ['https://images.unsplash.com/photo-1602940659805-770d1b3b9911?q=80&w=1000'],
    tags: ['water sports', 'beach', 'adventure', 'outdoors'],
    location: 'Essaouira Beach',
  },
  {
    id: 'fish-market-lunch',
    name: 'Fresh Fish Market Lunch',
    description: 'Select fresh seafood from the market and have it cooked to your preference at nearby grills.',
    position: { lng: -9.7752, lat: 31.5131 },
    coordinates: [-9.7752, 31.5131],
    category: 'activity',
    images: ['https://images.unsplash.com/photo-1530260626688-048279320445?q=80&w=1000'],
    tags: ['seafood', 'local', 'fresh', 'experience'],
    location: 'Essaouira Port',
  },
  
  // Tangier POIs
  {
    id: 'kasbah-museum',
    name: 'Kasbah Museum',
    description: 'Historical museum housed in the former Sultan\'s palace with artifacts spanning Moroccan history.',
    position: { lng: -5.8113, lat: 35.7896 },
    coordinates: [-5.8113, 35.7896],
    category: 'landmark',
    images: ['https://images.unsplash.com/photo-1539020580747-300ad58880b9?q=80&w=1000'],
    tags: ['history', 'museum', 'culture', 'architecture'],
    location: 'Tangier',
  },
  {
    id: 'cafe-hafa',
    name: 'Café Hafa',
    description: 'Iconic cliff-side café established in 1921, known for its terraced seating and views of the Mediterranean.',
    position: { lng: -5.8078, lat: 35.7872 },
    coordinates: [-5.8078, 35.7872],
    category: 'landmark',
    images: ['https://images.unsplash.com/photo-1662030284069-6c997ba9082f?q=80&w=1000'],
    tags: ['cafe', 'views', 'historical', 'relaxation'],
    location: 'Tangier',
  },
  
  // NEW POIs along Marrakech-Tangier route
  {
    id: 'cascade-ouzoud',
    name: 'Ouzoud Waterfalls',
    description: 'Spectacular waterfalls near the Middle Atlas village of Tanaghmeilt, one of the most visited sites in Morocco.',
    position: { lng: -6.7188, lat: 32.0066 },
    coordinates: [-6.7188, 32.0066],
    category: 'landmark',
    images: ['https://images.unsplash.com/photo-1629037662032-efe5cc74ebd3?q=80&w=1000'],
    tags: ['nature', 'waterfall', 'hiking', 'photography'],
    location: 'On route from Marrakech to Casablanca',
  },
  {
    id: 'bin-el-ouidane',
    name: 'Bin El Ouidane Lake',
    description: 'A stunning artificial lake surrounded by mountains, perfect for relaxation and outdoor activities.',
    position: { lng: -6.4500, lat: 32.1000 },
    coordinates: [-6.4500, 32.1000],
    category: 'landmark',
    images: ['https://images.unsplash.com/photo-1569944184084-1d1180c6f384?q=80&w=1000'],
    tags: ['lake', 'nature', 'relaxation', 'views'],
    location: 'On route from Marrakech to Casablanca',
  },
  {
    id: 'roadside-berber-village',
    name: 'Traditional Berber Village',
    description: 'Authentic Berber settlement where you can experience traditional Moroccan rural life.',
    position: { lng: -7.3000, lat: 32.0000 },
    coordinates: [-7.3000, 32.0000],
    category: 'landmark',
    images: ['https://images.unsplash.com/photo-1548017840-48304ed717d4?q=80&w=1000'],
    tags: ['culture', 'village', 'traditional', 'authentic'],
    location: 'On route from Marrakech to Casablanca',
  },
  {
    id: 'settat-market',
    name: 'Settat Local Market',
    description: 'Vibrant local market where you can find fresh produce, spices, and handmade crafts.',
    position: { lng: -7.6167, lat: 32.9667 },
    coordinates: [-7.6167, 32.9667],
    category: 'landmark',
    images: ['https://images.unsplash.com/photo-1473163928189-364b2c4e1135?q=80&w=1000'],
    tags: ['market', 'shopping', 'local', 'food'],
    location: 'On route from Marrakech to Casablanca',
  },
  {
    id: 'casablanca-mosque-hassan',
    name: 'Hassan II Mosque',
    description: 'Magnificent mosque with the world\'s tallest minaret, built partly on land and partly over the sea.',
    position: { lng: -7.6325, lat: 33.6083 },
    coordinates: [-7.6325, 33.6083],
    category: 'landmark',
    images: ['https://images.unsplash.com/photo-1553543835-4be9dccb8bd3?q=80&w=1000'],
    tags: ['mosque', 'architecture', 'religious', 'seaside'],
    location: 'Casablanca',
  },
  {
    id: 'rabat-kasbah-oudaias',
    name: 'Kasbah of the Udayas',
    description: 'Ancient fortress with narrow streets, blue and white buildings, and beautiful gardens.',
    position: { lng: -6.8359, lat: 34.0342 },
    coordinates: [-6.8359, 34.0342],
    category: 'landmark',
    images: ['https://images.unsplash.com/photo-1612368574493-d196b7530c0e?q=80&w=1000'],
    tags: ['kasbah', 'history', 'architecture', 'garden'],
    location: 'On route from Casablanca to Tangier',
  },
  {
    id: 'kenitra-forest-reserve',
    name: 'Kenitra Cork Oak Forest',
    description: 'Protected forest area with rich biodiversity, perfect for hiking and nature observation.',
    position: { lng: -6.5800, lat: 34.2600 },
    coordinates: [-6.5800, 34.2600],
    category: 'landmark',
    images: ['https://images.unsplash.com/photo-1456529534944-32a31d954f03?q=80&w=1000'],
    tags: ['nature', 'forest', 'hiking', 'wildlife'],
    location: 'On route from Casablanca to Tangier',
  },
  {
    id: 'moulay-bousselham-lagoon',
    name: 'Moulay Bousselham Lagoon',
    description: 'Beautiful coastal lagoon known for birdwatching and its peaceful atmosphere.',
    position: { lng: -6.2833, lat: 34.8833 },
    coordinates: [-6.2833, 34.8833],
    category: 'landmark',
    images: ['https://images.unsplash.com/photo-1570521462033-3015e76e7432?q=80&w=1000'],
    tags: ['lagoon', 'birds', 'nature', 'peaceful'],
    location: 'On route from Casablanca to Tangier',
  },
  {
    id: 'asilah-old-town',
    name: 'Asilah Medina',
    description: 'Picturesque walled town with whitewashed buildings and vibrant street art.',
    position: { lng: -6.0333, lat: 35.4667 },
    coordinates: [-6.0333, 35.4667],
    category: 'landmark',
    images: ['https://images.unsplash.com/photo-1576482180743-a8f48b772fe3?q=80&w=1000'],
    tags: ['medina', 'art', 'coastal', 'historical'],
    location: 'On route from Casablanca to Tangier',
  },
  {
    id: 'caves-of-hercules',
    name: 'Caves of Hercules',
    description: 'Mythical cave with an opening to the sea shaped like Africa, associated with Hercules legend.',
    position: { lng: -5.9382, lat: 35.7581 },
    coordinates: [-5.9382, 35.7581],
    category: 'landmark',
    images: ['https://images.unsplash.com/photo-1596632156554-8aa2ee0595cb?q=80&w=1000'],
    tags: ['cave', 'legend', 'natural', 'coastal'],
    location: 'On route from Casablanca to Tangier',
  },
  {
    id: 'roadside-moroccan-restaurant',
    name: 'Authentic Roadside Restaurant',
    description: 'Family-owned restaurant serving traditional Moroccan cuisine with local ingredients.',
    position: { lng: -6.9000, lat: 33.2000 },
    coordinates: [-6.9000, 33.2000],
    category: 'landmark',
    images: ['https://images.unsplash.com/photo-1560844952-8f8c9127f500?q=80&w=1000'],
    tags: ['food', 'traditional', 'authentic', 'local'],
    location: 'On route from Marrakech to Tangier',
  },
  {
    id: 'test-poi-1',
    name: 'Test Roadside Cafe',
    description: 'A popular stop for travelers between Marrakech and Casablanca.',
    position: { lng: -7.5, lat: 32.0 },
    coordinates: [-7.5, 32.0],
    category: 'activity',
    images: ['https://images.unsplash.com/photo-1506744038136-46273834b3fb?q=80&w=1000'],
    tags: ['cafe', 'rest', 'roadside'],
    location: 'On route from Marrakech to Casablanca',
  },
  {
    id: 'test-poi-2',
    name: 'Test Scenic Overlook',
    description: 'A scenic viewpoint with panoramic views of the plains.',
    position: { lng: -7.4, lat: 32.1 },
    coordinates: [-7.4, 32.1],
    category: 'landmark',
    images: ['https://images.unsplash.com/photo-1465101046530-73398c7f28ca?q=80&w=1000'],
    tags: ['view', 'scenic', 'photo'],
    location: 'On route from Marrakech to Casablanca',
  },
  {
    id: 'test-poi-3',
    name: 'Test Local Market',
    description: 'A bustling market with local produce and crafts.',
    position: { lng: -7.45, lat: 32.05 },
    coordinates: [-7.45, 32.05],
    category: 'shopping',
    images: ['https://images.unsplash.com/photo-1504674900247-0877df9cc836?q=80&w=1000'],
    tags: ['market', 'local', 'shopping'],
    location: 'On route from Marrakech to Casablanca',
  },
  {
    id: 'test-poi-4',
    name: 'Test Rest Area',
    description: 'A convenient rest area for travelers.',
    position: { lng: -7.48, lat: 32.08 },
    coordinates: [-7.48, 32.08],
    category: 'other',
    images: ['https://images.unsplash.com/photo-1519125323398-675f0ddb6308?q=80&w=1000'],
    tags: ['rest', 'area', 'travel'],
    location: 'On route from Marrakech to Casablanca',
  },
  {
    id: 'test-poi-5',
    name: 'Test Art Installation',
    description: 'A modern art installation by the highway.',
    position: { lng: -7.42, lat: 32.12 },
    coordinates: [-7.42, 32.12],
    category: 'cultural',
    images: ['https://images.unsplash.com/photo-1464983953574-0892a716854b?q=80&w=1000'],
    tags: ['art', 'modern', 'installation'],
    location: 'On route from Marrakech to Casablanca',
  },
];

// Helper function to get points of interest near a destination
export function getNearbyPointsOfInterest(destId: string, radius: number = 20): PointOfInterest[] {
  // This would normally use real geospatial calculations
  // For demo purposes, we'll just return POIs based on location name
  const destination = regionData['morocco'].destinations.find(d => d.id === destId);
  if (!destination) return [];
  
  // Simple implementation returning POIs with matching location
  return pointsOfInterest.filter(poi => {
    if (!poi.location || !destination.name) return false;
    return poi.location.includes(destination.name) || destination.name.includes(poi.location);
  });
}

// Import from destinations to avoid circular dependencies
import { regionData } from './destinations';

export const portugalPointsOfInterest: PointOfInterest[] = [
  {
    id: 'crasto',
    name: 'Quinta do Crasto',
    description: 'Historic winery with panoramic views over the Douro River and award-winning port wines.',
    position: { lng: -7.7000, lat: 41.1800 },
    coordinates: [-7.7000, 41.1800],
    category: 'landmark',
    images: ['https://images.unsplash.com/photo-1506744038136-46273834b3fb?q=80&w=1000'],
    tags: ['winery', 'tasting', 'view'],
    location: 'Peso da Régua',
  },
  {
    id: 'pacheca',
    name: 'Quinta da Pacheca',
    description: 'Famous for its wine hotel and immersive vineyard experiences.',
    position: { lng: -7.8100, lat: 41.1000 },
    coordinates: [-7.8100, 41.1000],
    category: 'accommodation',
    images: ['https://images.unsplash.com/photo-1464983953574-0892a716854b?q=80&w=1000'],
    tags: ['winery', 'hotel', 'experience'],
    location: 'Lamego',
  },
  {
    id: 'pinhao-cruise',
    name: 'Pinhão River Cruise',
    description: 'Scenic boat tour along the Douro River, passing terraced vineyards and quintas.',
    position: { lng: -7.5662, lat: 41.1895 },
    coordinates: [-7.5662, 41.1895],
    category: 'activity',
    images: ['https://images.unsplash.com/photo-1502082553048-f009c37129b9?q=80&w=1000'],
    tags: ['cruise', 'river', 'scenic'],
    location: 'Pinhão',
  },
  {
    id: 'mateus',
    name: 'Mateus Palace',
    description: 'Baroque palace with beautiful gardens, famous for its rosé wine label.',
    position: { lng: -7.7333, lat: 41.3000 },
    coordinates: [-7.7333, 41.3000],
    category: 'landmark',
    images: ['https://images.unsplash.com/photo-1465101046530-73398c7f28ca?q=80&w=1000'],
    tags: ['palace', 'garden', 'history'],
    location: 'Vila Real',
  },
  {
    id: 'pesqueira-miradouro',
    name: 'Miradouro de São Salvador do Mundo',
    description: 'Stunning viewpoint over the Douro River and terraced vineyards.',
    position: { lng: -7.4000, lat: 41.1500 },
    coordinates: [-7.4000, 41.1500],
    category: 'landmark',
    images: ['https://images.unsplash.com/photo-1500534314209-a25ddb2bd429?q=80&w=1000'],
    tags: ['viewpoint', 'scenic', 'nature'],
    location: 'São João da Pesqueira',
  },
  {
    id: 'sabrosa-winery',
    name: 'Quinta do Portal',
    description: 'Modern winery with innovative architecture and excellent port tastings.',
    position: { lng: -7.5700, lat: 41.2700 },
    coordinates: [-7.5700, 41.2700],
    category: 'landmark',
    images: ['https://images.unsplash.com/photo-1519125323398-675f0ddb6308?q=80&w=1000'],
    tags: ['winery', 'modern', 'tasting'],
    location: 'Sabrosa',
  },
  {
    id: 'tabuaco-walk',
    name: 'Tabuaço Vineyard Walk',
    description: 'Guided walk through the terraced vineyards and traditional wine cellars.',
    position: { lng: -7.5667, lat: 41.1167 },
    coordinates: [-7.5667, 41.1167],
    category: 'activity',
    images: ['https://images.unsplash.com/photo-1506744038136-46273834b3fb?q=80&w=1000'],
    tags: ['walk', 'vineyard', 'culture'],
    location: 'Tabuaço',
  },
  {
    id: 'alijo-olive',
    name: 'Alijó Olive Oil Experience',
    description: 'Discover the olive oil tradition of the Douro with tastings and tours.',
    position: { lng: -7.4681, lat: 41.2747 },
    coordinates: [-7.4681, 41.2747],
    category: 'activity',
    images: ['https://images.unsplash.com/photo-1465101178521-c1a9136a3b99?q=80&w=1000'],
    tags: ['olive oil', 'tasting', 'tradition'],
    location: 'Alijó',
  },
];
