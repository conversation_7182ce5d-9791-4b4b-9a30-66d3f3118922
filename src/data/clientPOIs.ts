/**
 * clientPOIs.ts
 * 
 * Sample client-specific POI data for development and testing
 */

import { PointOfInterest } from '../types/POITypes.updated';
import { Position } from '../types/Position';

/**
 * Sample POI data for Morocco client
 */
export const moroccoPOIs: PointOfInterest[] = [
  {
    id: 'mor-001',
    clientId: 'morocco',
    name: '<PERSON><PERSON><PERSON>-<PERSON>',
    description: 'Famous square and market place in Marrakesh\'s medina quarter.',
    position: [-7.9892, 31.6258] as Position,
    type: 'landmark',
    image: '/assets/morocco/pois/jemaa-el-fnaa.jpg',
    rating: 4.5,
    tags: ['market', 'historic', 'cultural'],
    categories: ['landmark', 'cultural'],
    duration: 2,
    cost: 0,
    location: 'Marrakesh',
    metadata: {
      clientId: 'morocco',
      priority: 10,
      customFields: {
        bestTimeToVisit: 'Evening',
        localTips: 'Try the fresh orange juice from the vendors'
      }
    }
  },
  {
    id: 'mor-002',
    clientId: 'morocco',
    name: 'Hassan II Mosque',
    description: 'The largest mosque in Morocco and the 7th largest in the world.',
    position: [-7.6326, 33.6085] as Position,
    type: 'landmark',
    image: '/assets/morocco/pois/hassan-ii-mosque.jpg',
    rating: 4.8,
    tags: ['religious', 'architecture', 'cultural'],
    categories: ['landmark', 'religious'],
    duration: 1.5,
    cost: 120,
    location: 'Casablanca',
    metadata: {
      clientId: 'morocco',
      priority: 9,
      customFields: {
        bestTimeToVisit: 'Morning',
        localTips: 'Non-Muslims can only enter with guided tours'
      }
    }
  },
  {
    id: 'mor-003',
    clientId: 'morocco',
    name: 'Majorelle Garden',
    description: 'Botanical garden and artist\'s landscape garden in Marrakesh.',
    position: [-7.9886, 31.6423] as Position,
    type: 'activity',
    image: '/assets/morocco/pois/majorelle-garden.jpg',
    rating: 4.6,
    tags: ['garden', 'art', 'peaceful'],
    categories: ['activity', 'nature'],
    duration: 2,
    cost: 70,
    location: 'Marrakesh',
    metadata: {
      clientId: 'morocco',
      priority: 8,
      customFields: {
        bestTimeToVisit: 'Morning',
        localTips: 'Visit early to avoid crowds'
      }
    }
  }
];

/**
 * Sample POI data for Portugal client
 */
export const portugalPOIs: PointOfInterest[] = [
  {
    id: 'por-001',
    clientId: 'portugal',
    name: 'Belém Tower',
    description: 'UNESCO World Heritage Site and ceremonial gateway to Lisbon.',
    position: [-9.2159, 38.6916] as Position,
    type: 'landmark',
    image: '/assets/portugal/pois/belem-tower.jpg',
    rating: 4.6,
    tags: ['historic', 'architecture', 'unesco'],
    categories: ['landmark', 'cultural'],
    duration: 1,
    cost: 6,
    location: 'Lisbon',
    metadata: {
      clientId: 'portugal',
      priority: 10,
      customFields: {
        bestTimeToVisit: 'Morning',
        localTips: 'Buy a combined ticket with Jerónimos Monastery'
      }
    }
  },
  {
    id: 'por-002',
    clientId: 'portugal',
    name: 'Pena Palace',
    description: 'Romanticist castle in the municipality of Sintra.',
    position: [-9.3905, 38.7873] as Position,
    type: 'landmark',
    image: '/assets/portugal/pois/pena-palace.jpg',
    rating: 4.7,
    tags: ['castle', 'architecture', 'historic'],
    categories: ['landmark', 'cultural'],
    duration: 3,
    cost: 14,
    location: 'Sintra',
    metadata: {
      clientId: 'portugal',
      priority: 9,
      customFields: {
        bestTimeToVisit: 'Weekday morning',
        localTips: 'Take the bus from Sintra station to avoid the steep walk'
      }
    }
  },
  {
    id: 'por-003',
    clientId: 'portugal',
    name: 'Douro Valley',
    description: 'UNESCO World Heritage Site known for its stunning landscapes and wine production.',
    position: [-7.7349, 41.1579] as Position,
    type: 'activity',
    image: '/assets/portugal/pois/douro-valley.jpg',
    rating: 4.9,
    tags: ['wine', 'scenic', 'nature'],
    categories: ['activity', 'nature'],
    duration: 8,
    cost: 100,
    location: 'Douro Valley',
    metadata: {
      clientId: 'portugal',
      priority: 8,
      customFields: {
        bestTimeToVisit: 'September (harvest season)',
        localTips: 'Book a river cruise for the best views'
      }
    }
  }
];

/**
 * Combined POI data for all clients
 */
export const allClientPOIs: PointOfInterest[] = [
  ...moroccoPOIs,
  ...portugalPOIs
];

/**
 * Get POIs for a specific client
 * @param clientId - The client ID
 * @returns Array of POIs for the specified client
 */
export const getClientPOIs = (clientId: string): PointOfInterest[] => {
  return allClientPOIs.filter(poi => poi.clientId === clientId);
};