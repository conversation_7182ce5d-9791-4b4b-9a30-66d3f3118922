
export interface Vehicle {
  id: string;
  name: string;
  description: string;
  image: string;
  capacity: number;
  features: string[];
  type: 'economy' | 'standard' | 'luxury' | 'adventure';
}

export const vehicles: Vehicle[] = [
  {
    id: 'sedan',
    name: 'Sedan',
    description: 'Comfortable sedan for city travel and shorter routes.',
    image: 'https://images.unsplash.com/photo-1549317661-bd32c8ce0db2?q=80&w=1000',
    capacity: 4,
    features: ['Air conditioning', 'Bottled water', 'Local driver/guide'],
    type: 'economy'
  },
  {
    id: 'suv',
    name: 'SUV',
    description: 'Spacious SUV suitable for families and small groups.',
    image: 'https://images.unsplash.com/photo-1533473359331-0135ef1b58bf?q=80&w=1000',
    capacity: 6,
    features: ['Air conditioning', 'Bottled water', 'Local driver/guide', 'Extra luggage space'],
    type: 'standard'
  },
  {
    id: '4x4',
    name: '4x4 Off-Road',
    description: 'Adventure-ready 4x4 vehicle perfect for desert and mountain excursions.',
    image: 'https://images.unsplash.com/photo-1565991336112-8f0a2016e764?q=80&w=1000',
    capacity: 4,
    features: ['Air conditioning', 'Bottled water', 'Local driver/guide', 'Off-road capabilities', 'Roof rack'],
    type: 'adventure'
  },
  {
    id: 'van',
    name: 'Luxury Van',
    description: 'Premium van with extra comfort for longer journeys.',
    image: 'https://images.unsplash.com/photo-1609152612768-173856ea0b41?q=80&w=1000',
    capacity: 8,
    features: ['Premium leather seats', 'Air conditioning', 'Bottled water', 'Professional driver/guide', 'Onboard WiFi', 'Refreshments'],
    type: 'luxury'
  }
];

// For backward compatibility
export const availableVehicles = vehicles;
