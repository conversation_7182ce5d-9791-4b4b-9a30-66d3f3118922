import { PreArrangedJourney } from '@/types/ItineraryParameters';

/**
 * Pre-arranged journey templates for Morocco
 * These can be easily customized for different clients/regions
 */
export const moroccoPreArrangedJourneys: PreArrangedJourney[] = [
  {
    id: 'imperial-cities-classic',
    name: 'Imperial Cities Classic',
    description: 'Discover Morocco\'s four imperial cities with their rich history, stunning architecture, and vibrant culture. Perfect for first-time visitors.',
    duration: 8,
    difficulty: 'easy',
    pace: 'balanced-explorer',
    style: 'cultural-deep-dive',
    cities: ['Casablanca', 'Rabat', 'Meknes', 'Fez', 'Marrakech'],
    highlights: [
      'Hassan II Mosque',
      'Kasbah of the Udayas',
      'Bab Mansour Gate',
      'Al-Qarawiyyin University',
      'Jamaa el-Fna Square',
      'Majorelle Garden'
    ],
    imageUrl: '/images/journeys/imperial-cities.jpg',
    price: {
      from: 1200,
      currency: 'EUR'
    },
    seasonality: ['spring', 'fall', 'winter'],
    tags: ['culture', 'history', 'architecture', 'first-time', 'classic']
  },
  {
    id: 'sahara-adventure',
    name: 'Sahara Desert Adventure',
    description: 'Experience the magic of the Sahara Desert with camel trekking, desert camps, and stunning dune landscapes. An unforgettable adventure.',
    duration: 6,
    difficulty: 'moderate',
    pace: 'balanced-explorer',
    style: 'adventure-seeker',
    cities: ['Marrakech', 'Ouarzazate', 'Merzouga', 'Erfoud'],
    highlights: [
      'Erg Chebbi Dunes',
      'Camel Trekking',
      'Desert Camp Experience',
      'Aït Benhaddou Kasbah',
      'Todra Gorge',
      'Atlas Mountains'
    ],
    imageUrl: '/images/journeys/sahara-adventure.jpg',
    price: {
      from: 950,
      currency: 'EUR'
    },
    seasonality: ['fall', 'winter', 'spring'],
    tags: ['adventure', 'desert', 'nature', 'camping', 'photography']
  },
  {
    id: 'atlas-mountains-trek',
    name: 'Atlas Mountains Trek',
    description: 'Explore the High Atlas Mountains with guided treks, Berber villages, and breathtaking mountain scenery. For nature and adventure lovers.',
    duration: 5,
    difficulty: 'challenging',
    pace: 'slow-immersive',
    style: 'adventure-seeker',
    cities: ['Marrakech', 'Imlil', 'Ourika Valley'],
    highlights: [
      'Mount Toubkal Base Camp',
      'Berber Villages',
      'Ourika Valley',
      'Atlas Mountain Views',
      'Traditional Guesthouses',
      'Mountain Trekking'
    ],
    imageUrl: '/images/journeys/atlas-mountains.jpg',
    price: {
      from: 750,
      currency: 'EUR'
    },
    seasonality: ['spring', 'summer', 'fall'],
    tags: ['trekking', 'mountains', 'nature', 'berber', 'challenging']
  },
  {
    id: 'coastal-discovery',
    name: 'Atlantic Coast Discovery',
    description: 'Discover Morocco\'s beautiful Atlantic coastline with charming coastal towns, fresh seafood, and stunning ocean views.',
    duration: 7,
    difficulty: 'easy',
    pace: 'slow-immersive',
    style: 'scenic-routes',
    cities: ['Casablanca', 'El Jadida', 'Essaouira', 'Agadir', 'Taghazout'],
    highlights: [
      'Essaouira Medina',
      'Portuguese Cistern',
      'Coastal Drives',
      'Surfing Spots',
      'Fresh Seafood',
      'Argan Oil Cooperatives'
    ],
    imageUrl: '/images/journeys/atlantic-coast.jpg',
    price: {
      from: 850,
      currency: 'EUR'
    },
    seasonality: ['spring', 'summer', 'fall'],
    tags: ['coast', 'relaxation', 'seafood', 'surfing', 'scenic']
  },
  {
    id: 'photography-expedition',
    name: 'Photography Expedition',
    description: 'Capture Morocco\'s most photogenic locations with expert guidance. Perfect for photography enthusiasts seeking the best shots.',
    duration: 10,
    difficulty: 'moderate',
    pace: 'balanced-explorer',
    style: 'photography-tour',
    cities: ['Marrakech', 'Aït Benhaddou', 'Chefchaouen', 'Fez', 'Merzouga'],
    highlights: [
      'Blue City of Chefchaouen',
      'Aït Benhaddou at Sunset',
      'Sahara Sunrise',
      'Fez Medina',
      'Atlas Mountain Vistas',
      'Traditional Markets'
    ],
    imageUrl: '/images/journeys/photography-tour.jpg',
    price: {
      from: 1400,
      currency: 'EUR'
    },
    seasonality: ['spring', 'fall'],
    tags: ['photography', 'instagram', 'scenic', 'sunrise', 'sunset']
  },
  {
    id: 'hidden-gems-explorer',
    name: 'Hidden Gems Explorer',
    description: 'Discover Morocco\'s best-kept secrets and off-the-beaten-path destinations. For adventurous travelers seeking authentic experiences.',
    duration: 9,
    difficulty: 'moderate',
    pace: 'balanced-explorer',
    style: 'hidden-gems',
    cities: ['Chefchaouen', 'Akchour', 'Azrou', 'Ifrane', 'Midelt'],
    highlights: [
      'Akchour Waterfalls',
      'Cedar Forest of Azrou',
      'Switzerland of Morocco',
      'Berber Villages',
      'Mountain Lakes',
      'Local Markets'
    ],
    imageUrl: '/images/journeys/hidden-gems.jpg',
    price: {
      from: 1100,
      currency: 'EUR'
    },
    seasonality: ['spring', 'summer', 'fall'],
    tags: ['hidden-gems', 'authentic', 'nature', 'waterfalls', 'mountains']
  },
  {
    id: 'grand-morocco-ultimate',
    name: 'Grand Morocco Ultimate',
    description: 'The ultimate Morocco experience covering all major regions from the Rif Mountains to the Sahara Desert. A comprehensive journey.',
    duration: 15,
    difficulty: 'moderate',
    pace: 'balanced-explorer',
    style: 'cultural-deep-dive',
    cities: [
      'Casablanca', 'Rabat', 'Chefchaouen', 'Fez', 'Ifrane', 
      'Merzouga', 'Ouarzazate', 'Marrakech', 'Essaouira'
    ],
    highlights: [
      'All Imperial Cities',
      'Blue City Chefchaouen',
      'Sahara Desert Experience',
      'Atlas Mountains',
      'Atlantic Coast',
      'UNESCO World Heritage Sites',
      'Traditional Crafts',
      'Local Cuisine'
    ],
    imageUrl: '/images/journeys/grand-morocco.jpg',
    price: {
      from: 2200,
      currency: 'EUR'
    },
    seasonality: ['spring', 'fall'],
    tags: ['comprehensive', 'luxury', 'culture', 'nature', 'adventure', 'ultimate']
  },
  {
    id: 'weekend-marrakech',
    name: 'Marrakech Weekend Escape',
    description: 'A perfect weekend getaway to experience the magic of Marrakech. Ideal for short trips and city breaks.',
    duration: 3,
    difficulty: 'easy',
    pace: 'maximum-discovery',
    style: 'cultural-deep-dive',
    cities: ['Marrakech'],
    highlights: [
      'Jamaa el-Fna Square',
      'Majorelle Garden',
      'Bahia Palace',
      'Saadian Tombs',
      'Marrakech Medina',
      'Traditional Hammam'
    ],
    imageUrl: '/images/journeys/marrakech-weekend.jpg',
    price: {
      from: 350,
      currency: 'EUR'
    },
    seasonality: ['all year'],
    tags: ['weekend', 'city-break', 'short-trip', 'culture', 'shopping']
  }
];

/**
 * Get pre-arranged journeys by duration
 */
export const getJourneysByDuration = (minDays: number, maxDays: number): PreArrangedJourney[] => {
  return moroccoPreArrangedJourneys.filter(
    journey => journey.duration >= minDays && journey.duration <= maxDays
  );
};

/**
 * Get pre-arranged journeys by style
 */
export const getJourneysByStyle = (style: string): PreArrangedJourney[] => {
  return moroccoPreArrangedJourneys.filter(journey => journey.style === style);
};

/**
 * Get pre-arranged journeys by difficulty
 */
export const getJourneysByDifficulty = (difficulty: string): PreArrangedJourney[] => {
  return moroccoPreArrangedJourneys.filter(journey => journey.difficulty === difficulty);
};

/**
 * Get pre-arranged journeys by tags
 */
export const getJourneysByTags = (tags: string[]): PreArrangedJourney[] => {
  return moroccoPreArrangedJourneys.filter(journey =>
    tags.some(tag => journey.tags.includes(tag))
  );
};

/**
 * Get recommended journeys based on user preferences
 */
export const getRecommendedJourneys = (
  days: number,
  interests: string[],
  difficulty?: string
): PreArrangedJourney[] => {
  let filtered = moroccoPreArrangedJourneys;

  // Filter by duration (within 2 days of preference)
  filtered = filtered.filter(journey => 
    Math.abs(journey.duration - days) <= 2
  );

  // Filter by difficulty if specified
  if (difficulty) {
    filtered = filtered.filter(journey => journey.difficulty === difficulty);
  }

  // Score by interests match
  const scored = filtered.map(journey => ({
    journey,
    score: journey.tags.filter(tag => interests.includes(tag)).length
  }));

  // Sort by score and return top matches
  return scored
    .sort((a, b) => b.score - a.score)
    .slice(0, 6)
    .map(item => item.journey);
};

export default moroccoPreArrangedJourneys;
