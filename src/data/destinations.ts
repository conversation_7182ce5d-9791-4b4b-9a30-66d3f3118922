import { PointOfInterest, Destination } from '@/types';
import { pointsOfInterest } from './pointsOfInterest';
import { convertPOIToPointOfInterest } from '../utils/poiUtils';
import { portugalPOIs as importedPortugalPOIsData } from './portugalPOIs';
import { douroValleyPOIs } from './douroValleyPOIs';
import { portugalPointsOfInterest as additionalPortugalPOIsData } from './pointsOfInterest';

// Define OldPOI type for backward compatibility
interface OldPOI {
  id: string;
  name: string;
  description: string;
  latitude: number;
  longitude: number;
  type: string;
  image?: string;
  rating?: number;
  visitDuration?: number;
  duration?: number;
  cost?: number;
  location?: string;
}

// --- Portugal POIs (local definition, to be converted) ---
const portugalPOIsLocalDefinition: OldPOI[] = [
  {
    id: 'belem-tower',
    name: 'Belém Tower',
    description: 'Iconic 16th-century fortification on the Tagus River in Lisbon.',
    latitude: 38.6916,
    longitude: -9.2166,
    type: 'landmark',
    image: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?q=80&w=1000',
    rating: 4.7,
    // visitDuration: 1.5, // from OldPOI, PointOfInterest uses 'duration'
    duration: 1.5, // Assuming visitDuration maps to duration in hours
    cost: 6, // Added cost as per PointOfInterest, assuming a value
    location: 'Lisbon'
  },
  {
    id: 'porto-wine-cellar-local',
    name: 'Port Wine Cellar (Local)',
    description: 'Famous wine cellars along the Douro River in Porto, offering tastings and tours.',
    latitude: 41.1406,
    longitude: -8.6110,
    type: 'activity',
    image: 'https://images.unsplash.com/photo-1464983953574-0892a716854b?q=80&w=1000',
    rating: 4.8,
    // visitDuration: 2, // from OldPOI
    duration: 2, // Assuming visitDuration maps to duration in hours
    cost: 15, // Added cost
    location: 'Porto'
  },
  {
    id: 'pena-palace-local',
    name: 'Pena Palace (Local)',
    description: 'Colorful Romanticist castle on a hilltop in Sintra, near Lisbon.',
    latitude: 38.7876,
    longitude: -9.3904,
    type: 'landmark',
    image: 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?q=80&w=1000',
    rating: 4.9,
    // visitDuration: 2, // from OldPOI
    duration: 2, // Assuming visitDuration maps to duration in hours
    cost: 14, // Added cost
    location: 'Sintra'
  },
];

// --- Morocco Destinations ---
const moroccoDestinations: Destination[] = [
  {
    id: 'marrakech',
    name: 'Marrakech',
    description: 'The Red City known for its vibrant souks, historic medina, and stunning palaces.',
    position: { lat: 31.6295, lng: -7.9811 },
    coordinates: [-7.9811, 31.6295],
    images: ['https://images.unsplash.com/photo-1597212720158-a87cadcbfcba?q=80&w=1000'],
    tags: ['medina', 'historic', 'cultural'],
  },
  {
    id: 'fes',
    name: 'Fes',
    description: "Morocco's cultural and spiritual center with the oldest medina in the world.",
    position: { lat: 34.0181, lng: -5.0078 },
    coordinates: [-5.0078, 34.0181],
    images: ['https://images.unsplash.com/photo-1570096964808-c55f2a6b7ecd?q=80&w=1000'],
    tags: ['medina', 'historic', 'cultural'],
  },
  {
    id: 'casablanca',
    name: 'Casablanca',
    description: "Morocco's largest city and economic center with modern architecture and the Hassan II Mosque.",
    position: { lat: 33.5731, lng: -7.5898 },
    coordinates: [-7.5898, 33.5731],
    images: ['https://images.unsplash.com/photo-1579017331263-ef82f3574c5a?q=80&w=1000'],
    tags: ['modern', 'coastal', 'business'],
  },
  {
    id: 'rabat',
    name: 'Rabat',
    description: "The capital city of Morocco, known for its historic landmarks and coastal charm.",
    position: { lat: 34.020882, lng: -6.84165 },
    coordinates: [-6.84165, 34.020882],
    images: ['https://images.unsplash.com/photo-YOUR_IMAGE_ID_RABAT?q=80&w=1000'],
    tags: ['capital', 'historic', 'coastal'],
  },
  {
    id: 'chefchaouen',
    name: 'Chefchaouen',
    description: "The Blue Pearl of Morocco, famous for its blue-washed buildings in the Rif Mountains.",
    position: { lat: 35.1718, lng: -5.2697 },
    coordinates: [-5.2697, 35.1718],
    images: ['https://images.unsplash.com/photo-YOUR_IMAGE_ID_CHEFCHAOUEN?q=80&w=1000'],
    tags: ['blue city', 'mountains', 'picturesque'],
  },
  {
    id: 'essaouira',
    name: 'Essaouira',
    description: "A charming coastal city known for its historic medina, strong winds, and vibrant arts scene.",
    position: { lat: 31.5125, lng: -9.7700 },
    coordinates: [-9.7700, 31.5125],
    images: ['https://images.unsplash.com/photo-YOUR_IMAGE_ID_ESSAOUIRA?q=80&w=1000'],
    tags: ['coastal', 'medina', 'artsy'],
  },
  {
    id: 'merzouga',
    name: 'Merzouga',
    description: "A gateway to the Sahara Desert, famous for its towering Erg Chebbi dunes and camel treks.",
    position: { lat: 31.0906, lng: -4.0119 },
    coordinates: [-4.0119, 31.0906],
    images: ['https://images.unsplash.com/photo-YOUR_IMAGE_ID_MERZOUGA?q=80&w=1000'],
    tags: ['desert', 'dunes', 'adventure'],
  }
];

// --- Portugal Destinations ---
const portugalDestinations: Destination[] = [
  {
    id: 'lisbon',
    name: 'Lisbon',
    description: "Portugal's hilly, coastal capital city known for its cafe culture and soulful Fado music.",
    position: { lat: 38.7223, lng: -9.1393 },
    coordinates: [-9.1393, 38.7223],
    images: ['https://images.unsplash.com/photo-1506744038136-46273834b3fb?q=80&w=1000'],
    tags: ['capital', 'historic', 'coastal'],
  },
  {
    id: 'porto',
    name: 'Porto',
    description: 'A coastal city in northwest Portugal known for its stately bridges and port wine production.',
    position: { lat: 41.1496, lng: -8.6110 },
    coordinates: [-8.6110, 41.1496],
    images: ['https://images.unsplash.com/photo-1464983953574-0892a716854b?q=80&w=1000'],
    tags: ['wine', 'river', 'historic'],
  },
  {
    id: 'sintra',
    name: 'Sintra',
    description: "A picturesque town in the foothills of Portugal's Sintra Mountains, near Lisbon.",
    position: { lat: 38.8039, lng: -9.3904 },
    coordinates: [-9.3904, 38.8039],
    images: ['https://images.unsplash.com/photo-1465101046530-73398c7f28ca?q=80&w=1000'],
    tags: ['palace', 'mountain', 'romantic'],
  },
  {
    id: 'algarve',
    name: 'Algarve Region',
    description: "Portugal's southernmost region, known for its Atlantic beaches and golf resorts.",
    position: { lat: 37.0179, lng: -7.9304 },
    coordinates: [-7.9304, 37.0179],
    images: ['https://images.unsplash.com/photo-YOUR_IMAGE_ID_ALGARVE?q=80&w=1000'],
    tags: ['beaches', 'coastal', 'resorts'],
  },
  {
    id: 'coimbra',
    name: 'Coimbra',
    description: "A riverfront city in central Portugal and the country's former capital, home to a historic university.",
    position: { lat: 40.2033, lng: -8.4103 },
    coordinates: [-8.4103, 40.2033],
    images: ['https://images.unsplash.com/photo-YOUR_IMAGE_ID_COIMBRA?q=80&w=1000'],
    tags: ['historic', 'university', 'river'],
  }
];

// Combine all Portugal POIs, ensuring they are of PointOfInterest type
const localPortugalPOIsConverted: PointOfInterest[] = portugalPOIsLocalDefinition.map(convertPOIToPointOfInterest);

const allPortugalPOIs: PointOfInterest[] = [
  ...localPortugalPOIsConverted,
  ...importedPortugalPOIsData,
  ...douroValleyPOIs,
  ...additionalPortugalPOIsData
];

// Deduplicate POIs by ID
const uniquePortugalPOIs = Array.from(new Map(allPortugalPOIs.map(poi => [poi.id, poi])).values());

// Create Morocco POIs from the pointsOfInterest data
// Include all POIs that are geographically in Morocco or have Morocco-related locations
const moroccoPOIs: PointOfInterest[] = pointsOfInterest.filter(poi => {
  // Check location field
  const locationMatch = poi.location?.toLowerCase().includes('morocco') ||
    poi.location?.toLowerCase().includes('marrakech') ||
    poi.location?.toLowerCase().includes('fes') ||
    poi.location?.toLowerCase().includes('casablanca') ||
    poi.location?.toLowerCase().includes('chefchaouen') ||
    poi.location?.toLowerCase().includes('essaouira') ||
    poi.location?.toLowerCase().includes('merzouga') ||
    poi.location?.toLowerCase().includes('tangier') ||
    poi.location?.toLowerCase().includes('rabat') ||
    poi.location?.toLowerCase().includes('sahara');

  // Check coordinates - Morocco is roughly between -17 to 0 longitude and 21 to 36 latitude
  const coordinatesMatch = poi.coordinates &&
    poi.coordinates[0] >= -17 && poi.coordinates[0] <= 0 &&
    poi.coordinates[1] >= 21 && poi.coordinates[1] <= 36;

  // Check position object
  const positionMatch = poi.position &&
    poi.position.lng >= -17 && poi.position.lng <= 0 &&
    poi.position.lat >= 21 && poi.position.lat <= 36;

  return locationMatch || coordinatesMatch || positionMatch;
});

// No need to convert as they're already in the correct format
const moroccoPOIsConverted: PointOfInterest[] = moroccoPOIs;

// Debug logging
console.log('Morocco POIs found:', moroccoPOIs.length);
console.log('Sample Morocco POI:', moroccoPOIs[0]);

export const regionData: Record<string, {
  destinations: Destination[];
  pointsOfInterest: PointOfInterest[]; // Changed from POI[] to PointOfInterest[]
  mapCenter: [number, number];
  mapZoom: number;
  maxBounds: [[number, number], [number, number]];
}> = {
  morocco: {
    destinations: moroccoDestinations,
    pointsOfInterest: moroccoPOIsConverted,
    mapCenter: [-6.8498, 33.9716], // Rabat
    mapZoom: 6,
    maxBounds: [[-15.0, 25.0], [2.0, 37.0]],
  },
  portugal: {
    destinations: portugalDestinations,
    pointsOfInterest: uniquePortugalPOIs,
    mapCenter: [-8.2245, 39.3999], // Center of Portugal
    mapZoom: 6,
    maxBounds: [[-10.0, 36.0], [-6.0, 43.0]],
  },
};

// Usage: import { regionData } from '@/data/destinations';
// regionData['portugal'].destinations, regionData['morocco'].pointsOfInterest, etc.

export { portugalDestinations };
