import { PointOfInterest } from '@/types';

export const mockUsers = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'admin',
    createdAt: '2024-01-01',
    lastLogin: '2024-02-15',
    status: 'active'
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'editor',
    createdAt: '2024-01-15',
    lastLogin: '2024-02-14',
    status: 'active'
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'viewer',
    createdAt: '2024-02-01',
    lastLogin: '2024-02-10',
    status: 'inactive'
  }
];

export const mockPOIs: PointOfInterest[] = [
  {
    id: 'jemaa-el-fna',
    name: '<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>',
    description: 'Famous square and marketplace in Marrakech\'s medina quarter.',
    type: 'landmark',
    image: 'https://images.unsplash.com/photo-1597212720158-a87cadcbfcba',
    coordinates: [31.6287, -7.9892],
    duration: 3,
    cost: 0,
    rating: 4.8,
    location: 'Marrakech',
    tags: ['market', 'culture', 'food']
  },
  {
    id: 'bahia-palace',
    name: 'Bahia Palace',
    description: 'Grand 19th-century palace with ornate Moroccan architecture.',
    type: 'landmark',
    image: 'https://images.unsplash.com/photo-1548759806-821cafe3c1cb',
    coordinates: [31.6216, -7.9833],
    duration: 2,
    cost: 70,
    rating: 4.6,
    location: 'Marrakech',
    tags: ['palace', 'architecture', 'history']
  },
  {
    id: 'porto-cathedral',
    name: 'Porto Cathedral',
    description: 'Romanesque cathedral with Gothic cloisters and stunning views.',
    type: 'landmark',
    image: 'https://images.unsplash.com/photo-1555881400-74d7acaacd8b',
    coordinates: [41.1430, -8.6144],
    duration: 1.5,
    cost: 8,
    rating: 4.5,
    location: 'Porto',
    tags: ['church', 'architecture', 'history']
  }
];

export const mockGuideRequests = [
  {
    id: '1',
    name: 'Sarah Wilson',
    email: '<EMAIL>',
    experience: '5 years as a local tour guide',
    languages: ['English', 'French', 'Arabic'],
    status: 'pending',
    submittedAt: '2024-02-01'
  },
  {
    id: '2',
    name: 'Hassan Ahmed',
    email: '<EMAIL>',
    experience: '8 years in tourism industry',
    languages: ['Arabic', 'English', 'Spanish'],
    status: 'approved',
    submittedAt: '2024-01-15'
  },
  {
    id: '3',
    name: 'Maria Santos',
    email: '<EMAIL>',
    experience: '3 years as cultural guide',
    languages: ['Portuguese', 'English'],
    status: 'rejected',
    submittedAt: '2024-01-20'
  }
];