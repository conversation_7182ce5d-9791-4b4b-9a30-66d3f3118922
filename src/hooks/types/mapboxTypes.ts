/**
 * Types for use-mapbox.tsx hook
 */

// Define Map readiness states 
export enum MapReadinessState {
  NOT_INITIALIZED = 'not_initialized',
  INITIALIZING = 'initializing',
  LOADED = 'loaded',                 // Basic map object and its main definition is ready (was MAP_LOADED)
  STYLE_LOADED = 'style_loaded',     // Style is fully loaded
  STYLES_SOURCES_LOADED = 'styles_sources_loaded', // Style and sources are loaded
  FULLY_READY = 'fully_ready',        // Everything is ready for DOM operations, including custom checks
  ERROR = 'error'                     // An error occurred during initialization
}

export interface MapboxOptions {
  initialCenter?: [number, number];
  initialZoom?: number;
  minZoom?: number;
  maxZoom?: number;
  style?: string;
  containerStyle?: React.CSSProperties;
  containerClassName?: string;
  interactive?: boolean;
  onMapLoad?: (map: mapboxgl.Map) => void; // mapboxgl.Map needs to be imported or defined if not global
  onMapClick?: (e: any) => void; // e: mapboxgl.MapMouseEvent needs mapboxgl types
  onMapMove?: (e: any) => void; // e: mapboxgl.MapMouseEvent needs mapboxgl types
} 