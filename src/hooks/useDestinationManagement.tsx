
import { useState, useEffect } from 'react';
import { Destination, regionData } from '@/data/destinations';
import { toast } from 'sonner';

export const useDestinationManagement = () => {
  const [destinations, setDestinations] = useState<Destination[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedDestination, setSelectedDestination] = useState<Destination | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [open, setOpen] = useState(false);

  const fetchDestinations = () => {
    setLoading(true);
    // Using destinations from regionData
    const allDestinations = [...regionData.morocco.destinations, ...regionData.portugal.destinations];
    setDestinations(allDestinations);
    setLoading(false);
    toast.success("Destinations loaded successfully");
  };

  const handleDelete = (destination: Destination) => {
    setLoading(true);
    setDestinations(destinations.filter(d => d.id !== destination.id));
    setLoading(false);
    toast.success("Destination deleted successfully");
  };

  const handleEdit = (destination: Destination) => {
    setSelectedDestination(destination);
    setIsEditMode(true);
    setOpen(true);
  };

  const handleFormSubmit = (formData: Destination) => {
    setLoading(true);
    
    if (isEditMode && selectedDestination) {
      // Update existing destination in local state
      setDestinations(destinations.map(dest => 
        dest.id === selectedDestination.id ? { ...formData, id: selectedDestination.id } : dest
      ));
      toast.success("Destination updated successfully");
      } else {
        // Add new destination with mock ID
        const newDestination = {
          ...formData,
          id: Math.max(...destinations.map(d => d.id), 0) + 1
        };
        setDestinations([...destinations, newDestination]);
        toast.success("Destination added successfully");
      }
      
      resetForm();
      setLoading(false);
  };

  const resetForm = () => {
    setSelectedDestination(null);
    setIsEditMode(false);
    setOpen(false);
  };

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (!newOpen) resetForm();
  };

  const handleAddNew = () => {
    setIsEditMode(false);
    setSelectedDestination(null);
    setOpen(true);
  };

  useEffect(() => {
    fetchDestinations();
  }, []);

  return {
    destinations,
    loading,
    selectedDestination,
    isEditMode,
    open,
    handleDelete,
    handleEdit,
    handleFormSubmit,
    handleOpenChange,
    handleAddNew
  };
};
