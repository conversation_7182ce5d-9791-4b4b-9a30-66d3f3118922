/**
 * useClientPOI.tsx
 * 
 * Hook for accessing client-specific POI data
 */

import { useState, useEffect, useMemo } from 'react';
import { useClient } from '../contexts/ClientContext';
import { PointOfInterest } from '../types/POITypes.updated';
import { createClientPOIService, ClientPOIService } from '../services/clientPOIService';

/**
 * Hook return type
 */
interface UseClientPOIResult {
  pois: PointOfInterest[];
  loading: boolean;
  error: Error | null;
  filterPOIs: (filters: Record<string, any>) => Promise<PointOfInterest[]>;
  getPOIById: (id: string) => Promise<PointOfInterest | null>;
  refreshPOIs: () => Promise<void>;
}

/**
 * Hook for accessing client-specific POI data
 * @returns Object with POI data and methods
 */
export const useClientPOI = (): UseClientPOIResult => {
  const { clientId, config } = useClient();
  const [pois, setPois] = useState<PointOfInterest[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  // Create POI service instance
  const poiService = useMemo(() => {
    return createClientPOIService(clientId, config);
  }, [clientId, config]);

  // Load POIs when client changes
  useEffect(() => {
    const loadPOIs = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await poiService.getPOIs();
        setPois(data);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to load POIs'));
      } finally {
        setLoading(false);
      }
    };

    loadPOIs();
  }, [poiService]);

  /**
   * Filter POIs based on criteria
   * @param filters - The filter criteria
   * @returns Promise resolving to filtered POIs
   */
  const filterPOIs = async (filters: Record<string, any>): Promise<PointOfInterest[]> => {
    try {
      return await poiService.filterPOIs(filters);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to filter POIs'));
      return [];
    }
  };

  /**
   * Get a single POI by ID
   * @param id - The POI ID
   * @returns Promise resolving to a POI or null if not found
   */
  const getPOIById = async (id: string): Promise<PointOfInterest | null> => {
    try {
      return await poiService.getPOIById(id);
    } catch (err) {
      setError(err instanceof Error ? err : new Error(`Failed to get POI with ID: ${id}`));
      return null;
    }
  };

  /**
   * Refresh POIs from the service
   */
  const refreshPOIs = async (): Promise<void> => {
    try {
      setLoading(true);
      setError(null);
      const data = await poiService.getPOIs();
      setPois(data);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to refresh POIs'));
    } finally {
      setLoading(false);
    }
  };

  return {
    pois,
    loading,
    error,
    filterPOIs,
    getPOIById,
    refreshPOIs
  };
};