/**
 * useMapbox.ts
 * 
 * UNIFIED MAPBOX HOOK
 * Consolidates useMapControls, useMapPositioning, useMapboxReadiness, and use-mapbox
 * Provides a single interface for all Mapbox-related functionality
 */

import { useState, useEffect, useRef, useCallback } from 'react';
import mapboxgl from 'mapbox-gl';
import { Position } from '@/types';
import { useClient } from '@/contexts/ClientContext';

// ========================================
// TYPES AND INTERFACES
// ========================================

export interface MapboxConfig {
  accessToken: string;
  style?: string;
  center?: [number, number];
  zoom?: number;
  bounds?: [[number, number], [number, number]];
  pitch?: number;
  bearing?: number;
  interactive?: boolean;
  attributionControl?: boolean;
  logoControl?: boolean;
  navigationControl?: boolean;
  scaleControl?: boolean;
  fullscreenControl?: boolean;
}

export interface MapboxState {
  map: mapboxgl.Map | null;
  isReady: boolean;
  isLoaded: boolean;
  error: string | null;
  center: [number, number] | null;
  zoom: number;
  bounds: mapboxgl.LngLatBounds | null;
  bearing: number;
  pitch: number;
}

export interface MapboxControls {
  flyTo: (options: mapboxgl.FlyToOptions) => void;
  fitBounds: (bounds: [[number, number], [number, number]], options?: mapboxgl.FitBoundsOptions) => void;
  setCenter: (center: [number, number], zoom?: number) => void;
  setZoom: (zoom: number) => void;
  setBearing: (bearing: number) => void;
  setPitch: (pitch: number) => void;
  addMarker: (position: Position, options?: mapboxgl.MarkerOptions) => mapboxgl.Marker;
  removeMarker: (marker: mapboxgl.Marker) => void;
  addPopup: (position: Position, content: string | HTMLElement) => mapboxgl.Popup;
  removePopup: (popup: mapboxgl.Popup) => void;
  project: (lngLat: [number, number]) => mapboxgl.Point;
  unproject: (point: mapboxgl.Point) => mapboxgl.LngLat;
  getBounds: () => mapboxgl.LngLatBounds | null;
  getCenter: () => [number, number] | null;
  getZoom: () => number;
}

export interface UseMapboxOptions {
  container: string | HTMLElement;
  config?: Partial<MapboxConfig>;
  onReady?: (map: mapboxgl.Map) => void;
  onLoad?: (map: mapboxgl.Map) => void;
  onError?: (error: Error) => void;
  onMove?: (center: [number, number], zoom: number) => void;
  onZoom?: (zoom: number) => void;
  onClick?: (event: mapboxgl.MapMouseEvent) => void;
}

export interface UseMapboxReturn {
  state: MapboxState;
  controls: MapboxControls;
  map: mapboxgl.Map | null;
  containerRef: React.RefObject<HTMLDivElement>;
}

// ========================================
// DEFAULT CONFIGURATIONS
// ========================================

const DEFAULT_CONFIG: MapboxConfig = {
  accessToken: process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN || '',
  style: 'mapbox://styles/mapbox/outdoors-v12',
  center: [-7.0926, 31.7917], // Morocco center
  zoom: 6,
  pitch: 0,
  bearing: 0,
  interactive: true,
  attributionControl: true,
  logoControl: false,
  navigationControl: true,
  scaleControl: true,
  fullscreenControl: false
};

// ========================================
// MAIN HOOK
// ========================================

export function useMapbox(options: UseMapboxOptions): UseMapboxReturn {
  const { config: clientConfig } = useClient();
  const containerRef = useRef<HTMLDivElement>(null);
  const mapRef = useRef<mapboxgl.Map | null>(null);
  const markersRef = useRef<Set<mapboxgl.Marker>>(new Set());
  const popupsRef = useRef<Set<mapboxgl.Popup>>(new Set());
  
  // ========================================
  // STATE MANAGEMENT
  // ========================================
  
  const [state, setState] = useState<MapboxState>({
    map: null,
    isReady: false,
    isLoaded: false,
    error: null,
    center: null,
    zoom: 6,
    bounds: null,
    bearing: 0,
    pitch: 0
  });
  
  // ========================================
  // CONFIGURATION MERGING
  // ========================================
  
  const mergedConfig: MapboxConfig = {
    ...DEFAULT_CONFIG,
    ...clientConfig?.mapSettings,
    ...options.config
  };
  
  // ========================================
  // MAP INITIALIZATION
  // ========================================
  
  useEffect(() => {
    if (!containerRef.current || mapRef.current) return;
    
    try {
      // Set access token
      mapboxgl.accessToken = mergedConfig.accessToken;
      
      // Create map instance
      const map = new mapboxgl.Map({
        container: containerRef.current,
        style: mergedConfig.style!,
        center: mergedConfig.center!,
        zoom: mergedConfig.zoom!,
        pitch: mergedConfig.pitch!,
        bearing: mergedConfig.bearing!,
        interactive: mergedConfig.interactive!,
        attributionControl: mergedConfig.attributionControl!,
        logoControl: mergedConfig.logoControl!
      });
      
      // Add controls
      if (mergedConfig.navigationControl) {
        map.addControl(new mapboxgl.NavigationControl(), 'top-right');
      }
      
      if (mergedConfig.scaleControl) {
        map.addControl(new mapboxgl.ScaleControl(), 'bottom-left');
      }
      
      if (mergedConfig.fullscreenControl) {
        map.addControl(new mapboxgl.FullscreenControl(), 'top-right');
      }
      
      // Set initial bounds if provided
      if (mergedConfig.bounds) {
        map.fitBounds(mergedConfig.bounds, { padding: 50 });
      }
      
      // Event listeners
      map.on('load', () => {
        setState(prev => ({
          ...prev,
          map,
          isReady: true,
          isLoaded: true,
          center: [map.getCenter().lng, map.getCenter().lat],
          zoom: map.getZoom(),
          bounds: map.getBounds(),
          bearing: map.getBearing(),
          pitch: map.getPitch()
        }));
        
        options.onReady?.(map);
        options.onLoad?.(map);
      });
      
      map.on('move', () => {
        const center: [number, number] = [map.getCenter().lng, map.getCenter().lat];
        const zoom = map.getZoom();
        
        setState(prev => ({
          ...prev,
          center,
          zoom,
          bounds: map.getBounds(),
          bearing: map.getBearing(),
          pitch: map.getPitch()
        }));
        
        options.onMove?.(center, zoom);
      });
      
      map.on('zoom', () => {
        const zoom = map.getZoom();
        setState(prev => ({ ...prev, zoom }));
        options.onZoom?.(zoom);
      });
      
      map.on('click', (e) => {
        options.onClick?.(e);
      });
      
      map.on('error', (e) => {
        const error = new Error(`Mapbox error: ${e.error?.message || 'Unknown error'}`);
        setState(prev => ({ ...prev, error: error.message }));
        options.onError?.(error);
      });
      
      mapRef.current = map;
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to initialize map';
      setState(prev => ({ ...prev, error: errorMessage }));
      options.onError?.(error as Error);
    }
    
    // Cleanup
    return () => {
      if (mapRef.current) {
        // Remove all markers
        markersRef.current.forEach(marker => marker.remove());
        markersRef.current.clear();
        
        // Remove all popups
        popupsRef.current.forEach(popup => popup.remove());
        popupsRef.current.clear();
        
        // Remove map
        mapRef.current.remove();
        mapRef.current = null;
      }
    };
  }, [options.container]);
  
  // ========================================
  // CONTROL FUNCTIONS
  // ========================================
  
  const controls: MapboxControls = {
    flyTo: useCallback((options: mapboxgl.FlyToOptions) => {
      mapRef.current?.flyTo(options);
    }, []),
    
    fitBounds: useCallback((bounds: [[number, number], [number, number]], options?: mapboxgl.FitBoundsOptions) => {
      mapRef.current?.fitBounds(bounds, options);
    }, []),
    
    setCenter: useCallback((center: [number, number], zoom?: number) => {
      if (zoom !== undefined) {
        mapRef.current?.flyTo({ center, zoom });
      } else {
        mapRef.current?.setCenter(center);
      }
    }, []),
    
    setZoom: useCallback((zoom: number) => {
      mapRef.current?.setZoom(zoom);
    }, []),
    
    setBearing: useCallback((bearing: number) => {
      mapRef.current?.setBearing(bearing);
    }, []),
    
    setPitch: useCallback((pitch: number) => {
      mapRef.current?.setPitch(pitch);
    }, []),
    
    addMarker: useCallback((position: Position, options?: mapboxgl.MarkerOptions) => {
      if (!mapRef.current) throw new Error('Map not ready');
      
      const marker = new mapboxgl.Marker(options)
        .setLngLat(position)
        .addTo(mapRef.current);
      
      markersRef.current.add(marker);
      return marker;
    }, []),
    
    removeMarker: useCallback((marker: mapboxgl.Marker) => {
      marker.remove();
      markersRef.current.delete(marker);
    }, []),
    
    addPopup: useCallback((position: Position, content: string | HTMLElement) => {
      if (!mapRef.current) throw new Error('Map not ready');
      
      const popup = new mapboxgl.Popup()
        .setLngLat(position)
        .setHTML(typeof content === 'string' ? content : content.outerHTML)
        .addTo(mapRef.current);
      
      popupsRef.current.add(popup);
      return popup;
    }, []),
    
    removePopup: useCallback((popup: mapboxgl.Popup) => {
      popup.remove();
      popupsRef.current.delete(popup);
    }, []),
    
    project: useCallback((lngLat: [number, number]) => {
      if (!mapRef.current) throw new Error('Map not ready');
      return mapRef.current.project(lngLat);
    }, []),
    
    unproject: useCallback((point: mapboxgl.Point) => {
      if (!mapRef.current) throw new Error('Map not ready');
      return mapRef.current.unproject(point);
    }, []),
    
    getBounds: useCallback(() => {
      return mapRef.current?.getBounds() || null;
    }, []),
    
    getCenter: useCallback((): [number, number] | null => {
      if (!mapRef.current) return null;
      const center = mapRef.current.getCenter();
      return [center.lng, center.lat];
    }, []),
    
    getZoom: useCallback(() => {
      return mapRef.current?.getZoom() || 0;
    }, [])
  };
  
  return {
    state,
    controls,
    map: mapRef.current,
    containerRef
  };
}
