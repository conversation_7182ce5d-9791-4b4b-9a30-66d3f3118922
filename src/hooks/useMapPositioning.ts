import { useCallback } from 'react';

// Helper function to convert geo coordinates to map coordinates
export const useMapPositioning = () => {
  // Use useCallback instead of useMemo to memoize the function
  const getMapPosition = useCallback((coordinates: [number, number]) => {
    // This is a simplified conversion for Morocco's rough bounds:
    // Longitude: -13.2 to -1.0
    // Latitude: 27.7 to 35.9
    const longRange = 12.2; // -1.0 - (-13.2)
    const latRange = 8.2;  // 35.9 - 27.7
    
    const x = ((coordinates[0] - (-13.2)) / longRange) * 100;
    const y = 100 - ((coordinates[1] - 27.7) / latRange) * 100;
    
    return {
      left: `${x}%`,
      top: `${y}%`,
    };
  }, []); // Empty dependency array means this function is created only once

  return { getMapPosition };
};
