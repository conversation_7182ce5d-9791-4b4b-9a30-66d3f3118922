
import { useState, useCallback } from 'react';
import mapboxgl from 'mapbox-gl';

export const useMapControls = (map: mapboxgl.Map | null) => {
  const [zoom, setZoom] = useState(5.5);

  const zoomIn = useCallback(() => {
    if (!map) return;
    
    const newZoom = map.getZoom ? map.getZoom() + 1 : zoom + 1;
    if (map.easeTo) {
      map.easeTo({ zoom: newZoom, duration: 300 });
    }
    setZoom(newZoom);
  }, [map, zoom]);

  const zoomOut = useCallback(() => {
    if (!map) return;
    
    const newZoom = Math.max(0, map.getZoom ? map.getZoom() - 1 : zoom - 1);
    if (map.easeTo) {
      map.easeTo({ zoom: newZoom, duration: 300 });
    }
    setZoom(newZoom);
  }, [map, zoom]);

  const centerOnDestinations = useCallback((destinations: [number, number][]) => {
    if (!map || destinations.length === 0) return;
    
    if (destinations.length === 1) {
      if (map.easeTo) {
        map.easeTo({
          center: destinations[0],
          zoom: 8,
          duration: 1000
        });
      }
      return;
    }
    
    // If map has fitBounds method (real Mapbox)
    if (map.fitBounds && mapboxgl.LngLatBounds) {
      // Calculate bounds for all destinations
      const bounds = new mapboxgl.LngLatBounds();
      destinations.forEach(coord => bounds.extend(coord));
      
      map.fitBounds(bounds, {
        padding: 100,
        duration: 1000
      });
    }
  }, [map]);

  return {
    zoom,
    zoomIn,
    zoomOut,
    centerOnDestinations
  };
};
