/**
 * CORE ANIMATION HOOK - UNIFIED INTERFACE
 * Replaces: useAnimationManager.ts, useAnimationManager.tsx
 * Single hook for all animation functionality
 */

import { useRef, useEffect, useState, useCallback } from 'react';
import mapboxgl from 'mapbox-gl';
import { CoreAnimationEngine, Coordinate, AnimationState } from '../components/map/animation/CoreAnimationEngine';

export interface CoreAnimationHookResult {
  // State
  isInitialized: boolean;
  animationState: AnimationState;

  // Actions
  startAnimation: (route: Coordinate[], duration?: number) => Promise<boolean>;
  stopAnimation: () => void;
  pauseAnimation: () => void;
  resumeAnimation: () => void;
  setPOIs: (pois: any[]) => void;

  // Callbacks
  setProgressCallback: (callback: (progress: number, position: Coordinate, bearing: number) => void) => void;
  setCompleteCallback: (callback: () => void) => void;
  setErrorCallback: (callback: (error: Error) => void) => void;
}

export function useCoreAnimation(map: mapboxgl.Map | null): CoreAnimationHookResult {
  const engineRef = useRef<CoreAnimationEngine | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [animationState, setAnimationState] = useState<AnimationState>({
    isPlaying: false,
    isPaused: false,
    progress: 0,
    currentPosition: null,
    currentBearing: 0,
    startTime: 0,
    duration: 30000
  });

  // Initialize engine when map is available
  useEffect(() => {
    if (!map) return;

    console.log('🔄 [useCoreAnimation] Initializing animation engine...');
    
    try {
      // Create new engine
      const engine = new CoreAnimationEngine();
      engine.initialize(map);
      
      // Set up state synchronization
      engine.setCallbacks({
        onProgress: (progress, position, bearing) => {
          setAnimationState(prev => ({
            ...prev,
            progress,
            currentPosition: position,
            currentBearing: bearing
          }));
        },
        onComplete: () => {
          setAnimationState(prev => ({
            ...prev,
            isPlaying: false,
            progress: 1
          }));
        },
        onError: (error) => {
          console.error('[useCoreAnimation] Animation error:', error);
          setAnimationState(prev => ({
            ...prev,
            isPlaying: false,
            isPaused: false
          }));
        }
      });

      engineRef.current = engine;
      setIsInitialized(true);
      
      console.log('✅ [useCoreAnimation] Animation engine initialized');
      
    } catch (error) {
      console.error('[useCoreAnimation] Failed to initialize engine:', error);
      setIsInitialized(false);
    }

    // Cleanup on unmount
    return () => {
      if (engineRef.current) {
        engineRef.current.dispose();
        engineRef.current = null;
      }
      setIsInitialized(false);
    };
  }, [map]);

  // Start animation
  const startAnimation = useCallback(async (route: Coordinate[], duration: number = 30000): Promise<boolean> => {
    if (!engineRef.current || !isInitialized) {
      console.warn('[useCoreAnimation] Engine not initialized');
      return false;
    }

    try {
      console.log(`🚀 [useCoreAnimation] Starting animation with ${route.length} points`);
      
      // Set route and start
      engineRef.current.setRoute(route);
      engineRef.current.startAnimation(duration);
      
      // Update state
      setAnimationState(prev => ({
        ...prev,
        isPlaying: true,
        isPaused: false,
        progress: 0,
        duration,
        startTime: performance.now()
      }));
      
      return true;
      
    } catch (error) {
      console.error('[useCoreAnimation] Failed to start animation:', error);
      return false;
    }
  }, [isInitialized]);

  // Stop animation
  const stopAnimation = useCallback(() => {
    if (!engineRef.current) return;
    
    engineRef.current.stopAnimation();
    setAnimationState(prev => ({
      ...prev,
      isPlaying: false,
      isPaused: false,
      progress: 0
    }));
    
    console.log('⏹️ [useCoreAnimation] Animation stopped');
  }, []);

  // Pause animation (placeholder - can be implemented later)
  const pauseAnimation = useCallback(() => {
    console.log('⏸️ [useCoreAnimation] Pause not implemented yet');
  }, []);

  // Resume animation (placeholder - can be implemented later)
  const resumeAnimation = useCallback(() => {
    console.log('▶️ [useCoreAnimation] Resume not implemented yet');
  }, []);

  // Set progress callback
  const setProgressCallback = useCallback((callback: (progress: number, position: Coordinate, bearing: number) => void) => {
    if (engineRef.current) {
      engineRef.current.setCallbacks({
        onProgress: callback
      });
    }
  }, []);

  // Set complete callback
  const setCompleteCallback = useCallback((callback: () => void) => {
    if (engineRef.current) {
      engineRef.current.setCallbacks({
        onComplete: callback
      });
    }
  }, []);

  // Set error callback
  const setErrorCallback = useCallback((callback: (error: Error) => void) => {
    if (engineRef.current) {
      engineRef.current.setCallbacks({
        onError: callback
      });
    }
  }, []);

  // Set POIs for discovery
  const setPOIs = useCallback((pois: any[]) => {
    if (engineRef.current) {
      engineRef.current.setPOIs(pois);
    }
  }, []);

  return {
    isInitialized,
    animationState,
    startAnimation,
    stopAnimation,
    pauseAnimation,
    resumeAnimation,
    setPOIs,
    setProgressCallback,
    setCompleteCallback,
    setErrorCallback
  };
}

// Utility function to convert existing Position arrays to Coordinate type
export function convertToCoordinates(positions: [number, number][]): Coordinate[] {
  return positions.map(pos => [pos[0], pos[1]] as Coordinate);
}

// Utility function to validate coordinates
export function validateCoordinates(coords: Coordinate[]): boolean {
  return coords.every(coord => 
    Array.isArray(coord) && 
    coord.length === 2 && 
    typeof coord[0] === 'number' && 
    typeof coord[1] === 'number' &&
    isFinite(coord[0]) && 
    isFinite(coord[1]) &&
    coord[0] >= -180 && coord[0] <= 180 &&
    coord[1] >= -90 && coord[1] <= 90
  );
}
