import React, { useEffect, useState, useRef, useCallback } from 'react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { injectMapboxMarkerCSS } from '../utils/mapUtils';
import { forceFixAllMarkerPositions } from '../components/map/DestinationMarker';
import { removeCountryBordersAndLabels } from '../utils/mapUtils';
import { MapReadinessState, MapboxOptions } from './types/mapboxTypes';

// Create a simple logger for map events
const logger = {
  logMapInit: (message: string) => console.log(`Map Init: ${message}`),
  logMapEvent: (message: string, data?: any) => {} // console.log(`Map Event: ${message}`, data || '') // Commented out to reduce noise
};

// Initialize mapbox token from environment variable
const MAPBOX_TOKEN = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN || '';

// Set the access token directly
mapboxgl.accessToken = MAPBOX_TOKEN;

// Use global map instance to maintain across renders
const globalMapInstance: mapboxgl.Map | null = null;
const globalMapContainer: string | null = null;

interface UseMapboxResult {
  map: mapboxgl.Map | null;
  isLoaded: boolean;
  isMapFullyReady: boolean;
  mapReadinessState: MapReadinessState;
  safeRemoveLayer: (layerId: string) => boolean;
  safeRemoveSource: (sourceId: string) => boolean;
  safeAddSource: (sourceId: string, source: mapboxgl.AnySourceData) => boolean;
  safeAddLayer: (layer: mapboxgl.AnyLayer, beforeId?: string) => boolean;
}

export const useMapbox = (
  containerId: string,
  initialCenter: [number, number] = [-5.0, 31.0],
  initialZoom: number = 5.5,
  maxBounds: [[number, number], [number, number]] = [[-15.0, 25.0], [2.0, 37.0]]
): UseMapboxResult => {
  const [map, setMap] = useState<mapboxgl.Map | null>(null);
  const [isLoaded, setIsLoaded] = useState<boolean>(false);
  const [mapReadinessState, setMapReadinessState] = useState<MapReadinessState>(MapReadinessState.NOT_INITIALIZED);
  const [isMapFullyReady, setIsMapFullyReady] = useState<boolean>(false);
  const sourcesRef = useRef<Set<string>>(new Set());
  const layersRef = useRef<Set<string>>(new Set());
  const mapRef = useRef<mapboxgl.Map | null>(null);
  const readinessCheckTimerRef = useRef<number | null>(null);

  // Function to check map readiness in a reliable way
  const checkMapFullyReady = useCallback((mapInstance: mapboxgl.Map) => {
    // Clear any existing timer
    if (readinessCheckTimerRef.current !== null) {
      window.clearTimeout(readinessCheckTimerRef.current);
      readinessCheckTimerRef.current = null;
    }

    const verifyReadiness = () => {
      try {
        if (!mapInstance || !mapInstance.isStyleLoaded() || !mapInstance.loaded()) {
          // Basic checks failed, not ready
          if (!mapInstance) console.warn('[verifyReadiness] mapInstance is null');
          else if (!mapInstance.loaded()) console.log('[verifyReadiness] Map not fully loaded yet');
          else if (!mapInstance.isStyleLoaded()) console.log('[verifyReadiness] Map style not fully loaded yet');
          return false;
        }

        // Additional checks to ensure map is truly ready (source/layer operations)
        try {
          const testSourceId = '_readiness_test_source';
          const testLayerId = '_readiness_test_layer';

          if (!mapInstance.getSource(testSourceId)) {
            mapInstance.addSource(testSourceId, {
              type: 'geojson',
              data: { type: 'Feature', geometry: { type: 'Point', coordinates: [0, 0] }, properties: {} }
            });
          }
          if (!mapInstance.getLayer(testLayerId)) {
            mapInstance.addLayer({
              id: testLayerId, type: 'circle', source: testSourceId,
              paint: { 'circle-radius': 0, 'circle-opacity': 0 }
            });
          }
          if (mapInstance.getLayer(testLayerId)) mapInstance.removeLayer(testLayerId);
          if (mapInstance.getSource(testSourceId)) mapInstance.removeSource(testSourceId);
        } catch (error) {
          console.warn('[verifyReadiness] Map not ready for full operations (source/layer test failed):', error);
          return false;
        }
        return true;
      } catch (error) {
        console.error('[verifyReadiness] Error during map readiness check:', error);
        return false;
      }
    };

    if (verifyReadiness()) {
      logger.logMapEvent('✅ MAP IS FULLY READY FOR ALL OPERATIONS (verified by checkMapFullyReady)');
      setMapReadinessState(MapReadinessState.FULLY_READY);
      setIsMapFullyReady(true);
      window.dispatchEvent(new CustomEvent('mapbox-fully-ready', { detail: { map: mapInstance } }));
      return true;
    } else {
      readinessCheckTimerRef.current = window.setTimeout(() => {
        logger.logMapEvent('Retrying checkMapFullyReady...');
        checkMapFullyReady(mapInstance);
      }, 250); // Increased retry timeout slightly
      return false;
    }
  }, [setMapReadinessState, setIsMapFullyReady]);

  // Initialize map
  useEffect(() => {
    if (mapRef.current) {
      logger.logMapInit('Map instance already exists in ref, re-evaluating readiness.');
      setMap(mapRef.current);
      if (mapRef.current.isStyleLoaded() && mapRef.current.loaded()) {
        // If map was already there and seems loaded, attempt full readiness check
        setMapReadinessState(MapReadinessState.STYLES_SOURCES_LOADED); // Assume this if loaded() and isStyleLoaded() are true
        removeCountryBordersAndLabels(mapRef.current); // Attempt border removal
        checkMapFullyReady(mapRef.current); // Then verify full operational readiness
      } else {
        // If it exists but isn't fully loaded, let the event handlers below manage it.
        setMapReadinessState(MapReadinessState.INITIALIZING); // Or an appropriate intermediate state
      }
      return;
    }

    if (!containerId) {
      console.warn('[useMapbox] Container ID is not provided. Map cannot be initialized.');
      setMapReadinessState(MapReadinessState.NOT_INITIALIZED);
      return;
    }

    logger.logMapInit(`Initializing new map. ContainerId: ${containerId}`);
    logger.logMapInit(`Map Init Props: initialCenter=${JSON.stringify(initialCenter)}, initialZoom=${initialZoom}, maxBounds=${JSON.stringify(maxBounds)}`);

    setMapReadinessState(MapReadinessState.INITIALIZING);
    try {
      const container = document.getElementById(containerId);
      if (!container) {
        console.error(`Container with id "${containerId}" not found`);
        setMapReadinessState(MapReadinessState.ERROR);
        return;
      }
      if (container.innerHTML !== '') {
        logger.logMapInit('Map container is not empty, clearing it.');
        container.innerHTML = '';
      }

      const mapInstance = new mapboxgl.Map({
        container: containerId,
        style: 'mapbox://styles/mapbox/light-v11', // Consider making this a prop
        center: initialCenter,
        zoom: initialZoom,
        attributionControl: false,
        renderWorldCopies: false,
        dragRotate: false,
        pitchWithRotate: false,
        boxZoom: true,
        trackResize: true,
        cooperativeGestures: false,
        projection: 'mercator',
        maxBounds: maxBounds,
        interactive: true, // Ensure map is interactive
        fadeDuration: 0 // Disable fade animations for faster perceived load
      });

      mapRef.current = mapInstance;
      setMap(mapInstance);
      sourcesRef.current.clear();
      layersRef.current.clear();

      mapInstance.on('error', (e: any) => {
        logger.logMapEvent('Map error:', e.error || e);
        setMapReadinessState(MapReadinessState.ERROR);
      });

      mapInstance.on('load', () => {
        logger.logMapEvent('Map "load" event fired.');
        setMapReadinessState(MapReadinessState.LOADED);
        setIsLoaded(true); // Set original isLoaded flag

        // Perform operations that depend on the map being loaded
        if (mapInstance.isStyleLoaded()) { // Double check style is loaded
            logger.logMapEvent('Style is loaded on "load" event, removing borders.');
            removeCountryBordersAndLabels(mapInstance);
        } else {
            logger.logMapEvent('Style not yet loaded on "load" event, will wait for "styledata".');
            // Fallback: if style isn't loaded here, 'styledata' handler should catch it.
        }
        
        // After basic load and style ops, check for full operational readiness
        checkMapFullyReady(mapInstance);
      });
      
      mapInstance.on('styledata', () => {
        logger.logMapEvent('Map "styledata" event fired.');
        // This event can fire multiple times. Ensure style is actually loaded.
        if (mapInstance.isStyleLoaded() && mapInstance.loaded()) {
            logger.logMapEvent('Style is loaded on "styledata" event. Re-checking border removal and full readiness.');
            setMapReadinessState(MapReadinessState.STYLES_SOURCES_LOADED);
            removeCountryBordersAndLabels(mapInstance); // Ensure borders are removed if style changes
            checkMapFullyReady(mapInstance); // Re-verify full readiness
        } else {
            logger.logMapEvent('Style or map not fully loaded on "styledata", deferring operations.');
        }
      });


      mapInstance.on('idle', () => {
        logger.logMapEvent('Map "idle" event fired.');
        // Map is idle, good time to ensure full readiness if not already set
        if (!isMapFullyReady) {
            logger.logMapEvent('Map idle, attempting to confirm full readiness.');
            checkMapFullyReady(mapInstance);
        }
      });
      
      // Inject custom marker CSS
      injectMapboxMarkerCSS();

    } catch (error) {
      console.error('Failed to initialize map:', error);
      setMapReadinessState(MapReadinessState.ERROR);
    }

    return () => {
      logger.logMapInit('Cleaning up map instance.');
      if (readinessCheckTimerRef.current !== null) {
        window.clearTimeout(readinessCheckTimerRef.current);
      }
      if (mapRef.current) {
        mapRef.current.remove();
        mapRef.current = null;
      }
      setMap(null);
      setIsLoaded(false);
      setIsMapFullyReady(false);
      setMapReadinessState(MapReadinessState.NOT_INITIALIZED);
    };
  }, [containerId, initialCenter, initialZoom, JSON.stringify(maxBounds), checkMapFullyReady]); // Stringify maxBounds for stable dependency

  // Return the hook result
  return {
    map,
    isLoaded,
    isMapFullyReady,
    mapReadinessState,
    safeRemoveLayer: useCallback((layerId: string): boolean => {
      if (!map || !isMapFullyReady) return false;
      
      try {
        if (map.getLayer(layerId)) {
          map.removeLayer(layerId);
          layersRef.current.delete(layerId);
          return true;
        }
      } catch (error) {
        console.error(`Error removing layer ${layerId}:`, error);
      }
      
      return false;
    }, [map, isMapFullyReady]),
    
    safeRemoveSource: useCallback((sourceId: string): boolean => {
      if (!map || !isMapFullyReady) return false;
      
      try {
        if (map.getSource(sourceId)) {
          map.removeSource(sourceId);
          sourcesRef.current.delete(sourceId);
          return true;
        }
      } catch (error) {
        console.error(`Error removing source ${sourceId}:`, error);
      }
      
      return false;
    }, [map, isMapFullyReady]),
    
    safeAddSource: useCallback((sourceId: string, source: mapboxgl.AnySourceData): boolean => {
      if (!map || !isMapFullyReady) return false;
      
      try {
        if (!map.getSource(sourceId)) {
          map.addSource(sourceId, source);
          sourcesRef.current.add(sourceId);
          return true;
        }
      } catch (error) {
        console.error(`Error adding source ${sourceId}:`, error);
      }
      
      return false;
    }, [map, isMapFullyReady]),
    
    safeAddLayer: useCallback((layer: mapboxgl.AnyLayer, beforeId?: string): boolean => {
      if (!map || !isMapFullyReady) return false;
      
      try {
        if (!map.getLayer(layer.id)) {
          if (beforeId && map.getLayer(beforeId)) {
            map.addLayer(layer, beforeId);
          } else {
            map.addLayer(layer);
          }
          layersRef.current.add(layer.id);
          return true;
        }
      } catch (error) {
        console.error(`Error adding layer ${layer.id}:`, error);
      }
      
      return false;
    }, [map, isMapFullyReady])
  };
};
