
import { useState, useEffect } from 'react';
import { Destination } from '@/data/destinations';
import { PointOfInterest } from '@/components/PointOfInterestCard';
import { getNearbyPointsOfInterest } from '@/data/pointsOfInterest';

export const useRoutePOIs = (selectedDestinations: Destination[]) => {
  const [routePOIs, setRoutePOIs] = useState<PointOfInterest[]>([]);

  // Get POIs for the route whenever selected destinations change
  useEffect(() => {
    if (selectedDestinations.length <= 1) {
      setRoutePOIs([]);
      return;
    }
    
    // Get POIs between all consecutive destination pairs
    const pois: PointOfInterest[] = [];
    const uniquePOIIds = new Set<string>();
    
    for (let i = 0; i < selectedDestinations.length - 1; i++) {
      const startPOIs = getNearbyPointsOfInterest(selectedDestinations[i].id);
      const endPOIs = getNearbyPointsOfInterest(selectedDestinations[i + 1].id);
      
      // Combine POIs without duplicates
      [...startPOIs, ...endPOIs].forEach(poi => {
        if (!uniquePOIIds.has(poi.id)) {
          uniquePOIIds.add(poi.id);
          pois.push(poi);
        }
      });
    }
    
    setRoutePOIs(pois);
  }, [selectedDestinations]);

  return routePOIs;
};
