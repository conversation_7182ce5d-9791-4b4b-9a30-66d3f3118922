
import { useState, useEffect } from 'react';
import { pointsOfInterest as initialPOIs } from '@/data/pointsOfInterest';
import { PointOfInterest } from '@/components/PointOfInterestCard';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export const usePOIManagement = () => {
  const [pois, setPois] = useState<PointOfInterest[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPoi, setSelectedPoi] = useState<PointOfInterest | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [open, setOpen] = useState(false);

  const fetchPOIs = async () => {
    setLoading(true);
    try {
      // Try to fetch from Supabase first
      const { data, error } = await supabase.from('points_of_interest').select('*');
      
      if (error) {
        console.error("Error fetching POIs from Supabase:", error);
        // Fallback to local data if there's an error
        setPois(initialPOIs);
        toast.error("Failed to load points of interest from database");
      } else if (data && data.length > 0) {
        // Transform database data to match PointOfInterest interface
        const formattedData = data.map(poi => ({
          id: poi.id,
          name: poi.name,
          description: poi.description,
          location: poi.location,
          type: poi.type as 'landmark' | 'accommodation' | 'activity' | 'restaurant',
          cost: poi.cost || 0,
          duration: poi.duration,
          image: poi.image || 'https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?q=80&w=1000',
          coordinates: poi.coordinates as [number, number],
          tags: poi.tags || []
        }));
        setPois(formattedData);
        toast.success("Points of interest loaded successfully");
      } else {
        // If no data in Supabase yet, seed with local data
        console.log("No POIs found in database, using local data");
        setPois(initialPOIs);
      }
    } catch (error: any) {
      console.error("Error during POI fetch:", error.message);
      setPois(initialPOIs);
      toast.error("An error occurred while loading data");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (poi: PointOfInterest) => {
    try {
      setLoading(true);
      
      // Delete POI from Supabase
      const { error } = await supabase
        .from('points_of_interest')
        .delete()
        .eq('id', poi.id);
        
      if (error) throw error;
      
      // Remove from local state after successful delete
      setPois(pois.filter(p => p.id !== poi.id));
      toast.success("Point of interest deleted successfully");
    } catch (error: any) {
      console.error("Error deleting POI:", error.message);
      toast.error(`Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (poi: PointOfInterest) => {
    setSelectedPoi(poi);
    setIsEditMode(true);
    setOpen(true);
  };

  const handleFormSubmit = async (formData: PointOfInterest) => {
    try {
      setLoading(true);
      
      if (isEditMode && selectedPoi) {
        // Update existing POI in Supabase
        const { error } = await supabase
          .from('points_of_interest')
          .update({
            name: formData.name,
            description: formData.description,
            location: formData.location,
            type: formData.type,
            cost: formData.cost,
            duration: formData.duration,
            image: formData.image,
            tags: formData.tags,
            coordinates: formData.coordinates,
            updated_at: new Date().toISOString()
          })
          .eq('id', selectedPoi.id);
          
        if (error) throw error;
        
        // Update local state after successful update
        setPois(pois.map(poi => 
          poi.id === selectedPoi.id ? { ...formData, id: selectedPoi.id } : poi
        ));
        
        toast.success("Point of interest updated successfully");
      } else {
        // Add new POI to Supabase
        const newPoiId = `poi-${Date.now()}`;
        const newPoi = { ...formData, id: newPoiId };
        
        const { error } = await supabase
          .from('points_of_interest')
          .insert({
            id: newPoiId,
            name: formData.name,
            description: formData.description,
            location: formData.location,
            type: formData.type,
            cost: formData.cost,
            duration: formData.duration,
            image: formData.image,
            tags: formData.tags,
            coordinates: formData.coordinates
          });
          
        if (error) throw error;
        
        // Add to local state after successful insert
        setPois([...pois, newPoi]);
        toast.success("Point of interest added successfully");
      }

      resetForm();
    } catch (error: any) {
      console.error("Error during submission:", error.message);
      toast.error(`Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setSelectedPoi(null);
    setIsEditMode(false);
    setOpen(false);
  };

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (!newOpen) resetForm();
  };

  const handleAddNew = () => {
    setIsEditMode(false);
    setSelectedPoi(null);
    setOpen(true);
  };

  useEffect(() => {
    fetchPOIs();
  }, []);

  return {
    pois,
    loading,
    selectedPoi,
    isEditMode,
    open,
    handleDelete,
    handleEdit,
    handleFormSubmit,
    handleOpenChange,
    handleAddNew
  };
};
