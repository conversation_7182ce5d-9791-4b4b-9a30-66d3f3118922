import { useEffect, useState } from 'react';
import mapboxgl from 'mapbox-gl';

/**
 * useMapboxReadiness
 * Returns true when the map and style are fully ready for overlays/markers.
 *
 * @param map mapboxgl.Map | null
 * @returns isMapFullyReady: boolean
 */
export function useMapboxReadiness(map: mapboxgl.Map | null): boolean {
  const [isMapFullyReady, setIsMapFullyReady] = useState(false);

  useEffect(() => {
    if (!map) {
      setIsMapFullyReady(false);
      return;
    }
    let cancelled = false;
    let pollInterval: NodeJS.Timeout | null = null;
    let timeout: NodeJS.Timeout | null = null;
    let warned = false;

    const checkReady = () => {
      if (map.isStyleLoaded()) {
        if (!cancelled) setIsMapFullyReady(true);
        if (pollInterval) clearInterval(pollInterval);
        if (timeout) clearTimeout(timeout);
      }
    };

    // Start polling after 'load' event
    const onLoad = () => {
      checkReady();
      pollInterval = setInterval(checkReady, 100);
      // Warn if not ready after 3 seconds
      timeout = setTimeout(() => {
        if (!map.isStyleLoaded() && !warned) {
          warned = true;
          console.warn('[useMapboxReadiness] Map style still not ready after 3s.');
        }
      }, 3000);
    };

    // Listen for 'idle' as a fallback
    const onIdle = () => {
      if (!cancelled && map.isStyleLoaded()) {
        setIsMapFullyReady(true);
        if (pollInterval) clearInterval(pollInterval);
        if (timeout) clearTimeout(timeout);
      }
    };

    map.on('load', onLoad);
    map.on('idle', onIdle);

    // If map is already loaded, start polling immediately
    if (map.isStyleLoaded()) {
      setIsMapFullyReady(true);
    } else if ((map as any)._loaded) {
      onLoad();
    }

    return () => {
      cancelled = true;
      map.off('load', onLoad);
      map.off('idle', onIdle);
      if (pollInterval) clearInterval(pollInterval);
      if (timeout) clearTimeout(timeout);
    };
  }, [map]);

  return isMapFullyReady;
} 