/**
 * useTheme.tsx
 * 
 * Custom hook for accessing the theme with additional utilities
 */

import { useContext, useMemo } from 'react';
import ThemeContext from '../providers/ThemeProvider';
import { useClient } from '../contexts/ClientContext';
import { getThemeByClientId } from '../config/themes';

/**
 * Hook for accessing the theme context with extended functionality
 */
export const useTheme = () => {
  const themeContext = useContext(ThemeContext);
  const { clientId } = useClient();

  if (themeContext === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }

  // Additional utility functions for generating dynamic styles
  const utils = useMemo(() => {
    // Ensure theme is defined
    const theme = themeContext.theme || getThemeByClientId(clientId);
    
    return {
      /**
       * Get CSS variable value for use in inline styles
       * @param variableName CSS variable name without '--' prefix
       * @param fallback Fallback value if variable is not defined
       */
      getCssVar: (variableName: string, fallback?: string): string => {
        if (typeof window === 'undefined') return fallback || '';
        return getComputedStyle(document.documentElement)
          .getPropertyValue(`--${variableName}`)
          .trim() || fallback || '';
      },
      
      /**
       * Generate CSS for overlay component based on theme
       */
      getOverlayStyle: () => ({
        backgroundColor: theme?.overlays?.background || 'rgba(255, 255, 255, 0.9)',
        borderColor: theme?.overlays?.borderColor || 'rgba(229, 231, 235, 0.8)',
        boxShadow: '0 6px 24px rgba(0, 0, 0, 0.15)'
      }),
      
      /**
       * Generate CSS for tag/badge component based on theme
       */
      getTagStyle: (isActive: boolean = false) => ({
        backgroundColor: isActive 
          ? theme?.primaryColor || '#c53030' 
          : theme?.overlays?.tagBackground || 'rgba(197, 48, 48, 0.1)',
        color: isActive
          ? '#ffffff'
          : theme?.overlays?.tagTextColor || '#c53030'
      }),
      
      /**
       * Generate CSS for button component based on theme
       */
      getButtonStyle: (variant: 'primary' | 'secondary' = 'primary', state: 'default' | 'hover' | 'active' = 'default') => {
        const buttonTheme = theme?.buttons?.[variant];
        if (!buttonTheme) {
          return variant === 'primary' 
            ? { backgroundColor: '#c53030', color: '#ffffff' }
            : { backgroundColor: '#f8f9fa', color: '#c53030' };
        }

        switch (state) {
          case 'hover':
            return { 
              backgroundColor: buttonTheme.hover,
              color: buttonTheme.text
            };
          case 'active':
            return { 
              backgroundColor: buttonTheme.active,
              color: buttonTheme.text
            };
          default:
            return { 
              backgroundColor: buttonTheme.background,
              color: buttonTheme.text
            };
        }
      },
      
      /**
       * Generate CSS for map elements based on theme
       */
      getMapColors: () => ({
        destinationMarker: theme?.map?.markerColors.destination || '#c53030',
        poiMarker: theme?.map?.markerColors.poi || '#dd6b20',
        selectedPoiMarker: theme?.map?.markerColors.selectedPoi || '#f6ad55',
        vehicleMarker: theme?.map?.markerColors.vehicle || '#1a202c',
        routeLine: theme?.map?.routeColor || '#c53030',
        poiCluster: theme?.map?.poiClusterColor || '#dd6b20'
      }),
      
      /**
       * Get header style for overlay components
       */
      getHeaderStyle: () => ({
        backgroundColor: theme?.overlays?.headerBackground || 'rgba(197, 48, 48, 0.1)',
        borderColor: theme?.overlays?.borderColor || 'rgba(197, 48, 48, 0.3)'
      }),
      
      /**
       * Get text styles based on theme
       */
      getTextStyle: (variant: 'heading' | 'paragraph' | 'muted' | 'highlight' = 'paragraph') => {
        if (!theme?.text) return {};
        switch (variant) {
          case 'heading':
            return { color: theme.text.heading };
          case 'muted':
            return { color: theme.text.muted };
          case 'highlight':
            return { color: theme.text.highlight };
          default:
            return { color: theme.text.paragraph };
        }
      }
    };
  }, [themeContext.theme, clientId]);

  return {
    ...themeContext,
    utils
  };
};

export default useTheme; 