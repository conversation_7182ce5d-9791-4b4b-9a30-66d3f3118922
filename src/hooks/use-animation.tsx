
import React from 'react';

export function useAnimationOnMount(
  ref: React.RefObject<HTMLElement>,
  animationClass: string,
  delay: number = 0
) {
  React.useEffect(() => {
    const element = ref.current;
    if (!element) return;
    
    const timer = setTimeout(() => {
      element.classList.add(animationClass);
    }, delay);
    
    return () => {
      clearTimeout(timer);
      if (element) {
        element.classList.remove(animationClass);
      }
    };
  }, [ref, animationClass, delay]);
}

export function useStaggeredAnimation(
  containerRef: React.RefObject<HTMLElement>,
  childSelector: string,
  animationClass: string,
  baseDelay: number = 100
) {
  React.useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    
    const children = container.querySelectorAll(childSelector);
    
    children.forEach((child, index) => {
      setTimeout(() => {
        child.classList.add(animationClass);
      }, baseDelay * (index + 1));
    });
    
    return () => {
      children.forEach((child) => {
        child.classList.remove(animationClass);
      });
    };
  }, [containerRef, childSelector, animationClass, baseDelay]);
}
