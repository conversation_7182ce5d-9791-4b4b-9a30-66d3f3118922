/**
 * useTheme.ts
 * 
 * Custom hook for accessing theme information and utilities
 */

import { useState, useEffect } from 'react';

export type ThemeType = 'light' | 'dark';

interface ThemeUtils {
  getTextColor: () => string;
  getBgColor: () => string;
  getBorderColor: () => string;
  getAccentColor: () => string;
}

interface UseThemeReturn {
  theme: ThemeType;
  toggleTheme: () => void;
  utils: ThemeUtils;
}

const useTheme = (): UseThemeReturn => {
  const [theme, setTheme] = useState<ThemeType>('light');

  useEffect(() => {
    // Check local storage or user preference on mount
    const savedTheme = localStorage.getItem('theme') as ThemeType | null;
    if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {
      setTheme(savedTheme);
    } else if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      setTheme('dark');
    }
  }, []);

  useEffect(() => {
    // Update document element when theme changes
    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
    
    // Save to localStorage
    localStorage.setItem('theme', theme);
  }, [theme]);

  const toggleTheme = () => {
    setTheme(prevTheme => (prevTheme === 'light' ? 'dark' : 'light'));
  };

  // Theme-based utilities
  const utils: ThemeUtils = {
    getTextColor: () => (theme === 'dark' ? '#F9FAFB' : '#111827'),
    getBgColor: () => (theme === 'dark' ? '#111827' : '#FFFFFF'),
    getBorderColor: () => (theme === 'dark' ? '#374151' : '#E5E7EB'),
    getAccentColor: () => '#10B981'
  };

  return { theme, toggleTheme, utils };
};

export default useTheme; 