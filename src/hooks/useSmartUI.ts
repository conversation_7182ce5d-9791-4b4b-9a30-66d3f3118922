import { useState, useEffect, useCallback } from 'react';
import { Destination, PointOfInterest } from '@/types';

export enum UIMode {
  DISCOVER = 'discover',    // Exploring cities/POIs
  PLAN = 'plan',           // Building itinerary  
  REVIEW = 'review',       // Finalizing trip
  JOURNEY = 'journey'      // During animation
}

export interface UIState {
  mode: UIMode;
  leftPaneVisible: boolean;
  rightPaneVisible: boolean;
  leftPaneSize: 'full' | 'mini' | 'hidden';
  rightPaneSize: 'full' | 'mini' | 'hidden';
  citySwatchesVisible: boolean;
  journeyParametersVisible: boolean;
  autoHideEnabled: boolean;
}

interface UseSmartUIProps {
  selectedCities: Destination[];
  selectedPOIs: PointOfInterest[];
  isAnimating: boolean;
  isMobile: boolean;
}

export const useSmartUI = ({
  selectedCities,
  selectedPOIs,
  isAnimating,
  isMobile
}: UseSmartUIProps) => {
  const [uiState, setUIState] = useState<UIState>({
    mode: UIMode.DISCOVER,
    leftPaneVisible: true,
    rightPaneVisible: false,
    leftPaneSize: 'full',
    rightPaneSize: 'hidden',
    citySwatchesVisible: true,
    journeyParametersVisible: true,
    autoHideEnabled: true
  });

  // Auto-transition between UI modes based on user actions
  useEffect(() => {
    if (isAnimating) {
      setUIState(prev => ({
        ...prev,
        mode: UIMode.JOURNEY,
        leftPaneSize: 'hidden',
        rightPaneSize: 'mini',
        citySwatchesVisible: false,
        journeyParametersVisible: false
      }));
      return;
    }

    // DISCOVER → PLAN: When first city selected
    if (selectedCities.length === 1 && selectedPOIs.length === 0 && uiState.mode === UIMode.DISCOVER) {
      setUIState(prev => ({
        ...prev,
        mode: UIMode.PLAN,
        rightPaneVisible: true,
        rightPaneSize: 'full',
        leftPaneSize: isMobile ? 'mini' : 'full'
      }));
    }
    
    // PLAN → REVIEW: When 2+ cities + POIs selected
    else if (selectedCities.length >= 2 && selectedPOIs.length >= 2 && uiState.mode === UIMode.PLAN) {
      setUIState(prev => ({
        ...prev,
        mode: UIMode.REVIEW,
        rightPaneSize: 'full',
        leftPaneSize: isMobile ? 'hidden' : 'mini',
        citySwatchesVisible: true,
        journeyParametersVisible: true
      }));
    }
    
    // Back to DISCOVER: When no cities selected
    else if (selectedCities.length === 0 && uiState.mode !== UIMode.DISCOVER) {
      setUIState(prev => ({
        ...prev,
        mode: UIMode.DISCOVER,
        leftPaneVisible: true,
        rightPaneVisible: false,
        leftPaneSize: 'full',
        rightPaneSize: 'hidden',
        citySwatchesVisible: true,
        journeyParametersVisible: true
      }));
    }
  }, [selectedCities.length, selectedPOIs.length, isAnimating, isMobile, uiState.mode]);

  // Manual UI controls
  const toggleLeftPane = useCallback(() => {
    setUIState(prev => ({
      ...prev,
      leftPaneVisible: !prev.leftPaneVisible,
      leftPaneSize: prev.leftPaneVisible ? 'hidden' : 'full'
    }));
  }, []);

  const toggleRightPane = useCallback(() => {
    setUIState(prev => ({
      ...prev,
      rightPaneVisible: !prev.rightPaneVisible,
      rightPaneSize: prev.rightPaneVisible ? 'hidden' : 'full'
    }));
  }, []);

  const setLeftPaneSize = useCallback((size: 'full' | 'mini' | 'hidden') => {
    setUIState(prev => ({
      ...prev,
      leftPaneSize: size,
      leftPaneVisible: size !== 'hidden'
    }));
  }, []);

  const setRightPaneSize = useCallback((size: 'full' | 'mini' | 'hidden') => {
    setUIState(prev => ({
      ...prev,
      rightPaneSize: size,
      rightPaneVisible: size !== 'hidden'
    }));
  }, []);

  const toggleAutoHide = useCallback(() => {
    setUIState(prev => ({
      ...prev,
      autoHideEnabled: !prev.autoHideEnabled
    }));
  }, []);

  // Get CSS classes for panels based on current state
  const getLeftPaneClasses = useCallback(() => {
    const baseClasses = "transition-all duration-300 ease-in-out";
    
    if (isMobile) {
      return `${baseClasses} ${
        uiState.leftPaneVisible 
          ? "fixed left-0 top-0 w-4/5 sm:w-3/5 md:w-80 opacity-100 pointer-events-auto z-30"
          : "fixed -left-full top-0 w-4/5 sm:w-3/5 md:w-80 opacity-0 pointer-events-none z-30"
      }`;
    }

    switch (uiState.leftPaneSize) {
      case 'full':
        return `${baseClasses} relative w-80 opacity-100 z-20`;
      case 'mini':
        return `${baseClasses} relative w-16 opacity-100 z-20`;
      case 'hidden':
        return `${baseClasses} relative w-0 opacity-0 pointer-events-none z-20`;
      default:
        return baseClasses;
    }
  }, [uiState.leftPaneSize, uiState.leftPaneVisible, isMobile]);

  const getRightPaneClasses = useCallback(() => {
    const baseClasses = "transition-all duration-300 ease-in-out";
    
    if (isMobile) {
      return `${baseClasses} ${
        uiState.rightPaneVisible
          ? "fixed right-0 top-0 w-4/5 sm:w-3/5 md:w-80 opacity-100 pointer-events-auto z-30"
          : "fixed -right-full top-0 w-4/5 sm:w-3/5 md:w-80 opacity-0 pointer-events-none z-30"
      }`;
    }

    switch (uiState.rightPaneSize) {
      case 'full':
        return `${baseClasses} relative opacity-100 z-20`;
      case 'mini':
        return `${baseClasses} relative w-16 opacity-100 z-20`;
      case 'hidden':
        return `${baseClasses} relative w-0 opacity-0 pointer-events-none z-20`;
      default:
        return baseClasses;
    }
  }, [uiState.rightPaneSize, uiState.rightPaneVisible, isMobile]);

  return {
    uiState,
    toggleLeftPane,
    toggleRightPane,
    setLeftPaneSize,
    setRightPaneSize,
    toggleAutoHide,
    getLeftPaneClasses,
    getRightPaneClasses
  };
};
