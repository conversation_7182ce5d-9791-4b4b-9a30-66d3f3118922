/**
 * Global type declarations for the application
 */

// Fix for @mapbox/point-geometry import in mapbox-gl
declare module '@mapbox/point-geometry' {
  class Point {
    x: number;
    y: number;
    
    constructor(x: number, y: number);
    
    clone(): Point;
    add(p: Point): Point;
    sub(p: Point): Point;
    mult(k: number): Point;
    div(k: number): Point;
    rotate(a: number): Point;
    matMult(m: number[]): Point;
    distSqr(p: Point): number;
    dist(p: Point): number;
    mag(): number;
    equals(p: Point): boolean;
    toString(): string;
    convert(a: any): Point;
  }
  
  export = Point;
}

// Fix mapbox-gl module issues
declare module 'mapbox-gl' {
  const mapboxgl: any;
  export default mapboxgl;
  export = mapboxgl;
}

// Fix for @/utils/mapbox-imports
declare module '@/utils/mapbox-imports' {
  import * as mapboxgl from 'mapbox-gl';
  
  // Re-export the mapboxgl namespace
  const mapbox: typeof mapboxgl;
  export default mapbox;
  
  // Utility functions
  export function createMap(options: any): any;
  export function destroyMap(map: any): void;
}