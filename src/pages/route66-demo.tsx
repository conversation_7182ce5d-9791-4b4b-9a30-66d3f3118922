/**
 * Route 66 Adventure Demo Page
 * 
 * Classic Americana theme showcasing historic landmarks, diners, and road trip culture
 */

import React, { useState, useEffect, useRef } from 'react';
import { ClientProvider } from '../contexts/ClientContext';
import { ThemeProvider } from '../providers/ThemeProvider';
import NeutralLayout from '../components/layout/NeutralLayout';
import MapComponent from '../components/map/MapComponent';
import { Destination, PointOfInterest } from '../types';
import '../styles/route66-theme.css';

// Route 66 map settings (spans across USA)
const ROUTE66_CENTER: [number, number] = [-98.5795, 39.8283]; // Geographic center of USA
const ROUTE66_BOUNDS: [[number, number], [number, number]] = [
  [-125.0, 25.0], // Southwest coordinates (covers full route)
  [-65.0, 50.0]   // Northeast coordinates
];

// Route 66 destinations
const ROUTE66_DESTINATIONS: Destination[] = [
  {
    id: 'chicago',
    name: 'Chicago',
    description: 'Starting point of the Mother Road with iconic skyline and deep-dish pizza',
    position: [-87.6298, 41.8781],
    image: '/assets/route66/cities/chicago.jpg',
    type: 'city'
  },
  {
    id: 'st-louis',
    name: 'St. Louis',
    description: 'Gateway to the West with the iconic Gateway Arch',
    position: [-90.1994, 38.6270],
    image: '/assets/route66/cities/st-louis.jpg',
    type: 'city'
  },
  {
    id: 'oklahoma-city',
    name: 'Oklahoma City',
    description: 'Heart of Route 66 with cowboy culture and oil heritage',
    position: [-97.5164, 35.4676],
    image: '/assets/route66/cities/oklahoma-city.jpg',
    type: 'city'
  },
  {
    id: 'amarillo',
    name: 'Amarillo',
    description: 'Texas panhandle town famous for Cadillac Ranch and big steaks',
    position: [-101.8313, 35.2220],
    image: '/assets/route66/cities/amarillo.jpg',
    type: 'city'
  },
  {
    id: 'albuquerque',
    name: 'Albuquerque',
    description: 'Southwest charm with Native American culture and desert landscapes',
    position: [-106.6504, 35.0844],
    image: '/assets/route66/cities/albuquerque.jpg',
    type: 'city'
  },
  {
    id: 'flagstaff',
    name: 'Flagstaff',
    description: 'Mountain town gateway to Grand Canyon and Lowell Observatory',
    position: [-111.6513, 35.1983],
    image: '/assets/route66/cities/flagstaff.jpg',
    type: 'city'
  },
  {
    id: 'los-angeles',
    name: 'Los Angeles',
    description: 'End of the road at Santa Monica Pier with beaches and Hollywood',
    position: [-118.2437, 34.0522],
    image: '/assets/route66/cities/los-angeles.jpg',
    type: 'city'
  }
];

// Route 66 POIs - Americana focused
const ROUTE66_POIS: PointOfInterest[] = [
  {
    id: 'route66-sign-chicago',
    name: 'Route 66 Begin Sign',
    description: 'Historic starting point of America\'s Mother Road',
    position: [-87.6298, 41.8781],
    type: 'landmark',
    image: '/assets/route66/pois/route66-begin-sign.jpg',
    rating: 4.8,
    tags: ['historic', 'iconic', 'photo-op', 'beginning'],
    categories: ['landmark', 'historic'],
    duration: 0.5,
    cost: 0,
    location: 'Chicago'
  },
  {
    id: 'gateway-arch',
    name: 'Gateway Arch',
    description: '630-foot stainless steel arch symbolizing westward expansion',
    position: [-90.1847, 38.6247],
    type: 'landmark',
    image: '/assets/route66/pois/gateway-arch.jpg',
    rating: 4.7,
    tags: ['iconic', 'architecture', 'views', 'national-park'],
    categories: ['landmark', 'architecture'],
    duration: 2,
    cost: 15,
    location: 'St. Louis'
  },
  {
    id: 'cadillac-ranch',
    name: 'Cadillac Ranch',
    description: 'Art installation of buried Cadillacs in Texas wheat field',
    position: [-101.9871, 35.1872],
    type: 'art',
    image: '/assets/route66/pois/cadillac-ranch.jpg',
    rating: 4.5,
    tags: ['art', 'quirky', 'instagram', 'unique', 'free'],
    categories: ['art', 'quirky'],
    duration: 1,
    cost: 0,
    location: 'Amarillo'
  },
  {
    id: 'big-texan-steak-ranch',
    name: 'Big Texan Steak Ranch',
    description: 'Home of the famous 72oz steak challenge',
    position: [-101.8313, 35.2220],
    type: 'restaurant',
    image: '/assets/route66/pois/big-texan.jpg',
    rating: 4.3,
    tags: ['steak', 'challenge', 'texas', 'iconic', 'cowboy'],
    categories: ['restaurant', 'experience'],
    duration: 2,
    cost: 75,
    location: 'Amarillo'
  },
  {
    id: 'blue-whale-catoosa',
    name: 'Blue Whale of Catoosa',
    description: 'Giant blue whale roadside attraction and swimming hole',
    position: [-95.7489, 36.1898],
    type: 'attraction',
    image: '/assets/route66/pois/blue-whale.jpg',
    rating: 4.2,
    tags: ['roadside', 'quirky', 'swimming', 'family', 'free'],
    categories: ['attraction', 'family'],
    duration: 1,
    cost: 0,
    location: 'Catoosa'
  },
  {
    id: 'wigwam-motel',
    name: 'Wigwam Motel',
    description: 'Sleep in a concrete teepee at this iconic motor lodge',
    position: [-109.8898, 35.0272],
    type: 'accommodation',
    image: '/assets/route66/pois/wigwam-motel.jpg',
    rating: 4.1,
    tags: ['motel', 'teepee', 'vintage', 'unique', 'historic'],
    categories: ['accommodation', 'historic'],
    duration: 12,
    cost: 89,
    location: 'Holbrook'
  },
  {
    id: 'petrified-forest',
    name: 'Petrified Forest National Park',
    description: 'Ancient fossilized trees and colorful badlands',
    position: [-109.7877, 35.0794],
    type: 'nature',
    image: '/assets/route66/pois/petrified-forest.jpg',
    rating: 4.6,
    tags: ['national-park', 'fossils', 'hiking', 'geology', 'scenic'],
    categories: ['nature', 'national-park'],
    duration: 4,
    cost: 15,
    location: 'Holbrook'
  },
  {
    id: 'santa-monica-pier',
    name: 'Santa Monica Pier',
    description: 'End of Route 66 with amusement park and Pacific Ocean',
    position: [-118.4912, 34.0089],
    type: 'landmark',
    image: '/assets/route66/pois/santa-monica-pier.jpg',
    rating: 4.4,
    tags: ['pier', 'amusement-park', 'beach', 'end-point', 'iconic'],
    categories: ['landmark', 'entertainment'],
    duration: 3,
    cost: 25,
    location: 'Santa Monica'
  },
  {
    id: 'route66-end-sign',
    name: 'Route 66 End Sign',
    description: 'Official end of the Mother Road at Santa Monica Pier',
    position: [-118.4912, 34.0089],
    type: 'landmark',
    image: '/assets/route66/pois/route66-end-sign.jpg',
    rating: 4.7,
    tags: ['historic', 'end-point', 'photo-op', 'completion'],
    categories: ['landmark', 'historic'],
    duration: 0.5,
    cost: 0,
    location: 'Santa Monica'
  },
  {
    id: 'meteor-crater',
    name: 'Meteor Crater',
    description: 'Best preserved meteorite impact site on Earth',
    position: [-111.0225, 35.0280],
    type: 'nature',
    image: '/assets/route66/pois/meteor-crater.jpg',
    rating: 4.5,
    tags: ['crater', 'space', 'geology', 'unique', 'educational'],
    categories: ['nature', 'science'],
    duration: 2,
    cost: 22,
    location: 'Winslow'
  }
];

const Route66Demo: React.FC = () => {
  // State management
  const [selectedCities, setSelectedCities] = useState<Destination[]>([]);
  const [selectedPOIs, setSelectedPOIs] = useState<PointOfInterest[]>([]);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [journeyProgress, setJourneyProgress] = useState(0);
  
  // Map reference
  const mapRef = useRef<any>(null);
  
  // Handle city selection
  const handleCitySelect = (city: Destination) => {
    if (!selectedCities.find(c => c.id === city.id)) {
      setSelectedCities(prev => [...prev, city]);
    }
  };
  
  // Handle city deselection
  const handleCityDeselect = (city: Destination) => {
    setSelectedCities(prev => prev.filter(c => c.id !== city.id));
  };
  
  // Handle POI selection
  const handlePOISelect = (poi: PointOfInterest) => {
    console.log('POI selected:', poi.name);
  };
  
  // Handle adding POI to itinerary
  const handleAddPOI = (poi: PointOfInterest) => {
    if (!selectedPOIs.find(p => p.id === poi.id)) {
      setSelectedPOIs(prev => [...prev, poi]);
    }
  };
  
  // Handle journey start
  const handleStartJourney = () => {
    setIsAnimating(true);
    setIsPaused(false);
    // Journey animation logic would go here
  };
  
  // Handle journey pause
  const handlePauseJourney = () => {
    setIsPaused(!isPaused);
  };
  
  // Handle region change
  const handleRegionChange = (region: string) => {
    console.log('Region changed to:', region);
  };
  
  return (
    <ClientProvider initialClientId="route66">
      <ThemeProvider>
        <div className="route66-demo h-screen">
          <NeutralLayout
            currentRegion="Route 66"
            availableRegions={['Route 66']}
            onRegionChange={handleRegionChange}
            isAnimating={isAnimating}
            isPaused={isPaused}
            journeyProgress={journeyProgress}
            onStartJourney={handleStartJourney}
            onPauseJourney={handlePauseJourney}
            itineraryPOIs={selectedPOIs}
            availableCities={ROUTE66_DESTINATIONS}
            selectedCities={selectedCities}
            onCitySelect={handleCitySelect}
            onCityDeselect={handleCityDeselect}
            allPOIs={ROUTE66_POIS}
            onSelectPOI={handlePOISelect}
            onAddPOI={handleAddPOI}
            canBeginJourney={selectedPOIs.length >= 2}
            showBeginJourneyButton={selectedPOIs.length >= 2}
            onBeginJourney={handleStartJourney}
          >
            <MapComponent
              ref={mapRef}
              destinations={ROUTE66_DESTINATIONS}
              pointsOfInterest={ROUTE66_POIS}
              selectedCities={selectedCities}
              selectedPOIs={selectedPOIs}
              onCitySelect={handleCitySelect}
              onPOISelect={handlePOISelect}
              isAnimating={isAnimating}
              journeyProgress={journeyProgress}
              mapCenter={ROUTE66_CENTER}
              mapBounds={ROUTE66_BOUNDS}
            />
          </NeutralLayout>
        </div>
      </ThemeProvider>
    </ClientProvider>
  );
};

export default Route66Demo;
