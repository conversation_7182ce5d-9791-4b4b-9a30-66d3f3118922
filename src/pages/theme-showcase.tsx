/**
 * Theme Showcase Page
 * 
 * Demonstrates theme switching functionality and portfolio
 */

import React, { useState } from 'react';
import { ClientProvider } from '../contexts/ClientContext';
import { ThemeProvider } from '../providers/ThemeProvider';
import { useClient } from '../contexts/ClientContext';
import { Palette, Eye, Code, Zap } from 'lucide-react';

const ThemeCard: React.FC<{
  themeId: string;
  name: string;
  description: string;
  colors: { primary: string; secondary: string; accent: string };
  isActive: boolean;
  onSelect: () => void;
}> = ({ themeId, name, description, colors, isActive, onSelect }) => {
  return (
    <div 
      onClick={onSelect}
      style={{
        border: isActive ? '2px solid #2563eb' : '1px solid #e2e8f0',
        borderRadius: '12px',
        padding: '1.5rem',
        cursor: 'pointer',
        backgroundColor: isActive ? 'rgba(37, 99, 235, 0.05)' : 'white',
        transition: 'all 0.2s ease',
        boxShadow: isActive ? '0 4px 12px rgba(37, 99, 235, 0.15)' : '0 2px 4px rgba(0, 0, 0, 0.1)'
      }}
      className="hover:shadow-lg"
    >
      <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '1rem' }}>
        <div style={{ display: 'flex', gap: '4px' }}>
          <div style={{ 
            width: '16px', 
            height: '16px', 
            borderRadius: '50%', 
            backgroundColor: colors.primary 
          }} />
          <div style={{ 
            width: '16px', 
            height: '16px', 
            borderRadius: '50%', 
            backgroundColor: colors.secondary 
          }} />
          <div style={{ 
            width: '16px', 
            height: '16px', 
            borderRadius: '50%', 
            backgroundColor: colors.accent 
          }} />
        </div>
        <h3 style={{ 
          margin: 0, 
          fontSize: '1.125rem', 
          fontWeight: 600,
          color: isActive ? '#2563eb' : '#1e293b'
        }}>
          {name}
        </h3>
      </div>
      <p style={{ 
        margin: 0, 
        color: '#64748b', 
        fontSize: '0.875rem',
        lineHeight: '1.5'
      }}>
        {description}
      </p>
      {isActive && (
        <div style={{
          marginTop: '1rem',
          padding: '0.5rem',
          backgroundColor: 'rgba(37, 99, 235, 0.1)',
          borderRadius: '6px',
          fontSize: '0.75rem',
          color: '#2563eb',
          fontWeight: 500,
          textAlign: 'center'
        }}>
          ✓ Currently Active
        </div>
      )}
    </div>
  );
};

const ThemeShowcaseContent: React.FC = () => {
  const { clientId, setClientId } = useClient();

  const themes = [
    {
      id: 'neutral',
      name: 'Professional Demo',
      description: 'Clean, professional theme perfect for client presentations and business demos.',
      colors: { primary: '#2563eb', secondary: '#64748b', accent: '#0ea5e9' }
    },
    {
      id: 'morocco',
      name: 'Morocco Heritage',
      description: 'Rich cultural theme inspired by Moroccan architecture and desert landscapes.',
      colors: { primary: '#8B1A18', secondary: '#D1C4A9', accent: '#41B3A3' }
    },
    {
      id: 'napa-valley',
      name: 'Napa Valley Wine',
      description: 'Elegant wine country theme for luxury tours and culinary experiences.',
      colors: { primary: '#7c2d12', secondary: '#a16207', accent: '#dc2626' }
    },
    {
      id: 'route66',
      name: 'Route 66 Adventure',
      description: 'Classic Americana theme for nostalgic road trips and adventure travel.',
      colors: { primary: '#dc2626', secondary: '#1e40af', accent: '#fbbf24' }
    }
  ];

  const handleThemeSelect = (themeId: string) => {
    setClientId(themeId);
  };

  return (
    <div style={{ 
      minHeight: '100vh', 
      backgroundColor: '#f8fafc',
      padding: '2rem'
    }}>
      <div style={{ 
        maxWidth: '1200px', 
        margin: '0 auto'
      }}>
        {/* Header */}
        <div style={{ textAlign: 'center', marginBottom: '3rem' }}>
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center', 
            gap: '0.75rem',
            marginBottom: '1rem'
          }}>
            <Palette size={32} color="#2563eb" />
            <h1 style={{ 
              margin: 0, 
              fontSize: '2.5rem', 
              fontWeight: 700,
              color: '#1e293b'
            }}>
              Theme Portfolio
            </h1>
          </div>
          <p style={{ 
            fontSize: '1.125rem', 
            color: '#64748b',
            maxWidth: '600px',
            margin: '0 auto',
            lineHeight: '1.6'
          }}>
            Explore our collection of industry-specific themes. Each theme is carefully crafted 
            to match different travel business types and target audiences.
          </p>
        </div>

        {/* Current Theme Display */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          padding: '2rem',
          marginBottom: '3rem',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          border: '1px solid #e2e8f0'
        }}>
          <h2 style={{ 
            margin: '0 0 1rem 0', 
            fontSize: '1.5rem', 
            fontWeight: 600,
            color: '#1e293b'
          }}>
            Currently Active: {themes.find(t => t.id === clientId)?.name || 'Unknown'}
          </h2>
          <div style={{ display: 'flex', gap: '2rem', alignItems: 'center' }}>
            <div>
              <p style={{ margin: '0 0 1rem 0', color: '#64748b' }}>
                {themes.find(t => t.id === clientId)?.description}
              </p>
              <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
                {clientId === 'neutral' && (
                  <a
                    href="/neutral-demo"
                    style={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      padding: '0.5rem 1rem',
                      backgroundColor: '#2563eb',
                      color: 'white',
                      textDecoration: 'none',
                      borderRadius: '6px',
                      fontSize: '0.875rem',
                      fontWeight: 500
                    }}
                  >
                    <Eye size={16} />
                    View Demo
                  </a>
                )}
                {clientId === 'morocco' && (
                  <a
                    href="/moroccan-demo"
                    style={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      padding: '0.5rem 1rem',
                      backgroundColor: '#8B1A18',
                      color: 'white',
                      textDecoration: 'none',
                      borderRadius: '6px',
                      fontSize: '0.875rem',
                      fontWeight: 500
                    }}
                  >
                    <Eye size={16} />
                    View Demo
                  </a>
                )}
                {clientId === 'napa-valley' && (
                  <a
                    href="/napa-valley-demo"
                    style={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      padding: '0.5rem 1rem',
                      backgroundColor: '#7c2d12',
                      color: 'white',
                      textDecoration: 'none',
                      borderRadius: '6px',
                      fontSize: '0.875rem',
                      fontWeight: 500
                    }}
                  >
                    <Eye size={16} />
                    View Demo
                  </a>
                )}
                {clientId === 'route66' && (
                  <a
                    href="/route66-demo"
                    style={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      padding: '0.5rem 1rem',
                      backgroundColor: '#dc2626',
                      color: 'white',
                      textDecoration: 'none',
                      borderRadius: '6px',
                      fontSize: '0.875rem',
                      fontWeight: 500
                    }}
                  >
                    <Eye size={16} />
                    View Demo
                  </a>
                )}
                <a
                  href="/theme-showcase"
                  style={{
                    display: 'inline-flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    padding: '0.5rem 1rem',
                    backgroundColor: '#f1f5f9',
                    color: '#2563eb',
                    textDecoration: 'none',
                    borderRadius: '6px',
                    fontSize: '0.875rem',
                    fontWeight: 500,
                    border: '1px solid #e2e8f0'
                  }}
                >
                  <Code size={16} />
                  All Themes
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Theme Grid */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '1.5rem',
          marginBottom: '3rem'
        }}>
          {themes.map(theme => (
            <ThemeCard
              key={theme.id}
              themeId={theme.id}
              name={theme.name}
              description={theme.description}
              colors={theme.colors}
              isActive={clientId === theme.id}
              onSelect={() => handleThemeSelect(theme.id)}
            />
          ))}
        </div>

        {/* Features */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          padding: '2rem',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          border: '1px solid #e2e8f0'
        }}>
          <h2 style={{ 
            margin: '0 0 2rem 0', 
            fontSize: '1.5rem', 
            fontWeight: 600,
            color: '#1e293b',
            textAlign: 'center'
          }}>
            Framework Features
          </h2>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '2rem'
          }}>
            <div style={{ textAlign: 'center' }}>
              <Zap size={32} color="#2563eb" style={{ margin: '0 auto 1rem' }} />
              <h3 style={{ margin: '0 0 0.5rem 0', fontSize: '1.125rem', fontWeight: 600 }}>
                5-Minute Deployment
              </h3>
              <p style={{ margin: 0, color: '#64748b', fontSize: '0.875rem' }}>
                Switch themes instantly with our CSS variable architecture
              </p>
            </div>
            <div style={{ textAlign: 'center' }}>
              <Palette size={32} color="#2563eb" style={{ margin: '0 auto 1rem' }} />
              <h3 style={{ margin: '0 0 0.5rem 0', fontSize: '1.125rem', fontWeight: 600 }}>
                Industry-Specific
              </h3>
              <p style={{ margin: 0, color: '#64748b', fontSize: '0.875rem' }}>
                Themes designed for specific travel business types
              </p>
            </div>
            <div style={{ textAlign: 'center' }}>
              <Code size={32} color="#2563eb" style={{ margin: '0 auto 1rem' }} />
              <h3 style={{ margin: '0 0 0.5rem 0', fontSize: '1.125rem', fontWeight: 600 }}>
                Fully Customizable
              </h3>
              <p style={{ margin: 0, color: '#64748b', fontSize: '0.875rem' }}>
                Easy customization with CSS variables and configuration
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const ThemeShowcase: React.FC = () => {
  return (
    <ClientProvider initialClientId="neutral">
      <ThemeProvider>
        <ThemeShowcaseContent />
      </ThemeProvider>
    </ClientProvider>
  );
};

export default ThemeShowcase;
