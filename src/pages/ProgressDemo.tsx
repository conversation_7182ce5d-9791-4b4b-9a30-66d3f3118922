import React from 'react';
import ProgressExample from '@/components/ProgressExample';

const ProgressDemo: React.FC = () => {
  return (
    <div className="min-h-screen bg-slate-50 py-12">
      <div className="container mx-auto">
        <div className="max-w-5xl mx-auto">
          <h1 className="text-3xl font-bold text-center mb-2 text-slate-800">Progress Indicator Demo</h1>
          <p className="text-center text-slate-600 mb-8">
            Improving the user journey with clear navigation steps
          </p>
          
          <ProgressExample />
          
          <div className="mt-12 p-6 bg-white rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4">Implementation Notes</h2>
            <ul className="list-disc pl-6 space-y-2 text-slate-700">
              <li>Progress indicators help users understand where they are in a multi-step process</li>
              <li>Desktop version uses a full stepper with labels for clear navigation</li>
              <li>Mobile version uses a compact design to save space on smaller screens</li>
              <li>The component is fully accessible and keyboard navigable</li>
              <li>Different variations can be used depending on the screen real estate available</li>
            </ul>
            
            <h3 className="text-lg font-semibold mt-6 mb-3">How to Use</h3>
            <div className="bg-slate-100 p-4 rounded-md">
              <pre className="text-sm overflow-x-auto">
                <code>
{`import { ProgressIndicator } from '@/components/ui/progress-indicator';

// In your component
<ProgressIndicator 
  currentStep={2} 
  totalSteps={4} 
  labels={["Select Cities", "Add Points of Interest", "Choose Vehicle", "Get Quote"]} 
/>`}
                </code>
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProgressDemo; 