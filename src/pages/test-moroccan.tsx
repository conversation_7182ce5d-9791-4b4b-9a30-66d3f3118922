import React, { useEffect } from 'react';
import MoroccanLayout from '../components/layout/MoroccanLayout';

const TestMoroccan = () => {
  useEffect(() => {
    console.log('TestMoroccan component mounted');
  }, []);

  return (
    <MoroccanLayout>
      <div style={{ 
        display: 'flex', 
        flexDirection: 'column', 
        alignItems: 'center', 
        justifyContent: 'center',
        height: '100%',
        width: '100%'
      }}>
        <h1 style={{ 
          fontFamily: 'var(--font-display)',
          color: 'var(--morocco-red)',
          marginBottom: '2rem'
        }}>
          Testing Moroccan Layout
        </h1>
        <p style={{ 
          fontFamily: 'var(--font-body)',
          maxWidth: '600px',
          textAlign: 'center',
          marginBottom: '2rem'
        }}>
          This is a simple test page to verify that the Moroccan Layout component is working with the new fonts.
        </p>
        <div style={{
          fontFamily: 'var(--font-decorative)',
          fontSize: '2rem',
          color: 'var(--morocco-blue)'
        }}>
          رحلة رائعة
        </div>
      </div>
    </MoroccanLayout>
  );
};

export default TestMoroccan; 