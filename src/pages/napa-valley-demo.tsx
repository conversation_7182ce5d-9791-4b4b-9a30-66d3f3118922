/**
 * Napa Valley Wine Demo Page
 * 
 * Luxury wine country theme showcasing wineries, tastings, and culinary experiences
 */

import React, { useState, useEffect, useRef } from 'react';
import { ClientProvider } from '../contexts/ClientContext';
import { ThemeProvider } from '../providers/ThemeProvider';
import NeutralLayout from '../components/layout/NeutralLayout';
import MapComponent from '../components/map/MapComponent';
import { Destination, PointOfInterest } from '../types';
import '../styles/napa-valley-theme.css';

// Napa Valley map settings
const NAPA_CENTER: [number, number] = [-122.4194, 38.4404];
const NAPA_BOUNDS: [[number, number], [number, number]] = [
  [-122.7, 38.1], // Southwest coordinates
  [-122.1, 38.8]  // Northeast coordinates
];

// Napa Valley destinations
const NAPA_DESTINATIONS: Destination[] = [
  {
    id: 'napa-city',
    name: 'Na<PERSON>',
    description: 'Gateway to wine country with charming downtown and riverside dining',
    position: [-122.2869, 38.2975],
    image: '/assets/napa-valley/cities/napa-city.jpg',
    type: 'city'
  },
  {
    id: 'st-helena',
    name: 'St. Helena',
    description: 'Historic wine town with world-class wineries and Michelin dining',
    position: [-122.4703, 38.5052],
    image: '/assets/napa-valley/cities/st-helena.jpg',
    type: 'city'
  },
  {
    id: 'calistoga',
    name: 'Calistoga',
    description: 'Charming spa town with hot springs and boutique wineries',
    position: [-122.5797, 38.5788],
    image: '/assets/napa-valley/cities/calistoga.jpg',
    type: 'city'
  },
  {
    id: 'yountville',
    name: 'Yountville',
    description: 'Culinary capital with world-renowned restaurants and luxury resorts',
    position: [-122.3616, 38.4016],
    image: '/assets/napa-valley/cities/yountville.jpg',
    type: 'city'
  }
];

// Napa Valley POIs - Wine focused
const NAPA_POIS: PointOfInterest[] = [
  {
    id: 'opus-one',
    name: 'Opus One Winery',
    description: 'Iconic Bordeaux-style blend winery with architectural excellence',
    position: [-122.4089, 38.4234],
    type: 'winery',
    image: '/assets/napa-valley/pois/opus-one.jpg',
    rating: 4.9,
    tags: ['luxury', 'bordeaux-blend', 'architecture', 'premium'],
    categories: ['winery', 'luxury'],
    duration: 2,
    cost: 85,
    location: 'Oakville'
  },
  {
    id: 'domaine-chandon',
    name: 'Domaine Chandon',
    description: 'French-owned sparkling wine house with elegant tastings',
    position: [-122.3616, 38.4016],
    type: 'winery',
    image: '/assets/napa-valley/pois/domaine-chandon.jpg',
    rating: 4.7,
    tags: ['sparkling', 'french', 'elegant', 'champagne-method'],
    categories: ['winery', 'sparkling'],
    duration: 1.5,
    cost: 45,
    location: 'Yountville'
  },
  {
    id: 'french-laundry',
    name: 'The French Laundry',
    description: 'Thomas Keller\'s legendary 3-Michelin-star restaurant',
    position: [-122.3616, 38.4016],
    type: 'restaurant',
    image: '/assets/napa-valley/pois/french-laundry.jpg',
    rating: 4.8,
    tags: ['michelin-star', 'fine-dining', 'thomas-keller', 'luxury'],
    categories: ['restaurant', 'fine-dining'],
    duration: 4,
    cost: 350,
    location: 'Yountville'
  },
  {
    id: 'castello-di-amorosa',
    name: 'Castello di Amorosa',
    description: '13th-century Tuscan castle winery with medieval architecture',
    position: [-122.5797, 38.5788],
    type: 'winery',
    image: '/assets/napa-valley/pois/castello-di-amorosa.jpg',
    rating: 4.6,
    tags: ['castle', 'tuscan', 'medieval', 'unique'],
    categories: ['winery', 'historic'],
    duration: 2.5,
    cost: 65,
    location: 'Calistoga'
  },
  {
    id: 'schramsberg',
    name: 'Schramsberg Vineyards',
    description: 'Historic hillside caves producing premium sparkling wines',
    position: [-122.5797, 38.5788],
    type: 'winery',
    image: '/assets/napa-valley/pois/schramsberg.jpg',
    rating: 4.8,
    tags: ['historic', 'caves', 'sparkling', 'hillside'],
    categories: ['winery', 'historic'],
    duration: 2,
    cost: 75,
    location: 'Calistoga'
  },
  {
    id: 'auberge-du-soleil',
    name: 'Auberge du Soleil',
    description: 'Luxury resort with panoramic valley views and Michelin dining',
    position: [-122.4089, 38.4234],
    type: 'resort',
    image: '/assets/napa-valley/pois/auberge-du-soleil.jpg',
    rating: 4.9,
    tags: ['luxury', 'resort', 'views', 'michelin', 'spa'],
    categories: ['resort', 'luxury'],
    duration: 8,
    cost: 800,
    location: 'Rutherford'
  },
  {
    id: 'napa-valley-wine-train',
    name: 'Napa Valley Wine Train',
    description: 'Vintage train journey through wine country with gourmet dining',
    position: [-122.2869, 38.2975],
    type: 'experience',
    image: '/assets/napa-valley/pois/wine-train.jpg',
    rating: 4.5,
    tags: ['train', 'scenic', 'dining', 'vintage', 'experience'],
    categories: ['experience', 'scenic'],
    duration: 3,
    cost: 150,
    location: 'Napa'
  },
  {
    id: 'oxbow-public-market',
    name: 'Oxbow Public Market',
    description: 'Artisanal food market with local vendors and wine tastings',
    position: [-122.2869, 38.2975],
    type: 'market',
    image: '/assets/napa-valley/pois/oxbow-market.jpg',
    rating: 4.4,
    tags: ['market', 'artisanal', 'local', 'food', 'casual'],
    categories: ['market', 'food'],
    duration: 2,
    cost: 30,
    location: 'Napa'
  }
];

const NapaValleyDemo: React.FC = () => {
  // State management
  const [selectedCities, setSelectedCities] = useState<Destination[]>([]);
  const [selectedPOIs, setSelectedPOIs] = useState<PointOfInterest[]>([]);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [journeyProgress, setJourneyProgress] = useState(0);
  
  // Map reference
  const mapRef = useRef<any>(null);
  
  // Handle city selection
  const handleCitySelect = (city: Destination) => {
    if (!selectedCities.find(c => c.id === city.id)) {
      setSelectedCities(prev => [...prev, city]);
    }
  };
  
  // Handle city deselection
  const handleCityDeselect = (city: Destination) => {
    setSelectedCities(prev => prev.filter(c => c.id !== city.id));
  };
  
  // Handle POI selection
  const handlePOISelect = (poi: PointOfInterest) => {
    console.log('POI selected:', poi.name);
  };
  
  // Handle adding POI to itinerary
  const handleAddPOI = (poi: PointOfInterest) => {
    if (!selectedPOIs.find(p => p.id === poi.id)) {
      setSelectedPOIs(prev => [...prev, poi]);
    }
  };
  
  // Handle journey start
  const handleStartJourney = () => {
    setIsAnimating(true);
    setIsPaused(false);
    // Journey animation logic would go here
  };
  
  // Handle journey pause
  const handlePauseJourney = () => {
    setIsPaused(!isPaused);
  };
  
  // Handle region change
  const handleRegionChange = (region: string) => {
    console.log('Region changed to:', region);
  };
  
  return (
    <ClientProvider initialClientId="napa-valley">
      <ThemeProvider>
        <div className="napa-valley-demo h-screen">
          <NeutralLayout
            currentRegion="Napa Valley"
            availableRegions={['Napa Valley']}
            onRegionChange={handleRegionChange}
            isAnimating={isAnimating}
            isPaused={isPaused}
            journeyProgress={journeyProgress}
            onStartJourney={handleStartJourney}
            onPauseJourney={handlePauseJourney}
            itineraryPOIs={selectedPOIs}
            availableCities={NAPA_DESTINATIONS}
            selectedCities={selectedCities}
            onCitySelect={handleCitySelect}
            onCityDeselect={handleCityDeselect}
            allPOIs={NAPA_POIS}
            onSelectPOI={handlePOISelect}
            onAddPOI={handleAddPOI}
            canBeginJourney={selectedPOIs.length >= 2}
            showBeginJourneyButton={selectedPOIs.length >= 2}
            onBeginJourney={handleStartJourney}
          >
            <MapComponent
              ref={mapRef}
              destinations={NAPA_DESTINATIONS}
              pointsOfInterest={NAPA_POIS}
              selectedCities={selectedCities}
              selectedPOIs={selectedPOIs}
              onCitySelect={handleCitySelect}
              onPOISelect={handlePOISelect}
              isAnimating={isAnimating}
              journeyProgress={journeyProgress}
              mapCenter={NAPA_CENTER}
              mapBounds={NAPA_BOUNDS}
            />
          </NeutralLayout>
        </div>
      </ThemeProvider>
    </ClientProvider>
  );
};

export default NapaValleyDemo;
