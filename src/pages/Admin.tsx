import { useAuth } from "@/contexts/AuthContext";
import { useEffect, useState } from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useNavigate, useSearchParams } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import AdminDestinations from "@/components/admin/AdminDestinations";
import AdminPOIs from "@/components/admin/AdminPOIs";
import AdminUsers from "@/components/admin/AdminUsers";
import AdminQuotes from "@/components/admin/AdminQuotes";
import AdminClients from "@/components/admin/AdminClients";
import AdminThemes from "@/components/admin/AdminThemes";

const Admin = () => {
  const { user, isAdmin, isLoading } = useAuth();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState(searchParams.get('tab') || 'destinations');

  // Determine if the user is a SuperAdmin (placeholder logic)
  // In a real implementation, check user.role === 'superadmin'
  const isSuperAdmin = isAdmin; // Replace this with actual role check later

  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  useEffect(() => {
    // Only run checks when loading is complete
    if (!isLoading) {
      if (!user) {
        console.error("Unauthorized: Please login to access admin features");
        navigate("/auth");
        return;
      }

      if (!isAdmin) {
        console.error("Access Denied: You don't have admin privileges");
        navigate("/");
      }
    }
  }, [user, isAdmin, isLoading, navigate]); // Add all dependencies properly

  // Show loading state while auth is being checked
  if (isLoading) {
    return (
      <div className="container mx-auto py-8 flex items-center justify-center h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Loading...</h1>
        </div>
      </div>
    );
  }

  // Return null during redirect to prevent rendering the admin content
  if (!user || !isAdmin) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/admin/dashboard')}
                className="p-2 text-gray-600 hover:bg-gray-50 rounded-md transition-colors"
              >
                <ArrowLeft size={20} />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Admin Management</h1>
                <p className="text-gray-600">Manage content, users, and system settings</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="destinations">Destinations</TabsTrigger>
          <TabsTrigger value="pois">Points of Interest</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="quotes">Quote Requests</TabsTrigger>
          {/* Conditionally render SuperAdmin tabs */}
          {isSuperAdmin && (
            <>
              <TabsTrigger value="clients">Clients</TabsTrigger>
              <TabsTrigger value="themes">Themes</TabsTrigger>
            </>
          )}
        </TabsList>
        
        <TabsContent value="destinations">
          <AdminDestinations />
        </TabsContent>
        
        <TabsContent value="pois">
          <AdminPOIs />
        </TabsContent>
        
        <TabsContent value="users">
          <AdminUsers />
        </TabsContent>
        
        <TabsContent value="quotes">
          <AdminQuotes />
        </TabsContent>
        
        {/* Conditionally render SuperAdmin content */}
        {isSuperAdmin && (
          <>
            <TabsContent value="clients">
              <AdminClients />
            </TabsContent>
            
            <TabsContent value="themes">
              <AdminThemes />
            </TabsContent>
          </>
        )}
        </Tabs>
      </div>
    </div>
  );
};

export default Admin;
