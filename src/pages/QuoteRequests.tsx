
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, MapPin, Calendar, Users, MessageSquare, Clock } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

// Updated interface to better match the data structure in the database
interface QuoteRequest {
  id: string;
  full_name: string;
  email: string;
  phone: string | null;
  travel_date: string | null;
  num_travelers: number;
  special_requests: string | null;
  status: string;
  created_at: string;
  route_details: {
    destinations: {
      id: string;
      name: string;
      type?: string;
      coordinates?: [number, number];
    }[];
    vehicle: {
      id: string;
      name: string;
      type: string;
    } | null;
    points_of_interest: {
      id: string;
      name: string;
      type?: string;
    }[];
    route_stats: {
      totalDistance: number;
      totalDuration: number;
      recommendedDays: number;
    } | null;
  };
}

const QuoteRequests = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [quoteRequests, setQuoteRequests] = useState<QuoteRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!user) {
      navigate('/auth');
      return;
    }

    const fetchQuoteRequests = async () => {
      setIsLoading(true);
      try {
        const { data, error } = await supabase
          .from('quote_requests')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });

        if (error) throw error;

        // Parse the JSON data
        const parsedData = data?.map(request => {
          // Ensure route_details has the expected structure
          const routeDetails = request.route_details as any;
          
          return {
            ...request,
            route_details: {
              destinations: routeDetails?.destinations || [],
              vehicle: routeDetails?.vehicle || null,
              points_of_interest: routeDetails?.points_of_interest || [],
              route_stats: routeDetails?.route_stats || null
            }
          };
        }) as QuoteRequest[];

        setQuoteRequests(parsedData || []);
      } catch (error: any) {
        console.error('Error fetching quote requests:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchQuoteRequests();
  }, [user, navigate]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="container mx-auto py-8 px-4 min-h-screen pb-20">
      <Button 
        variant="ghost" 
        className="mb-6 p-0" 
        onClick={() => navigate('/')}
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Home
      </Button>

      <div className="mb-6">
        <h1 className="text-2xl font-bold text-morocco-terracotta">Your Quote Requests</h1>
        <p className="text-muted-foreground">View and track the status of your travel quote requests</p>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <p className="text-muted-foreground">Loading your quote requests...</p>
        </div>
      ) : quoteRequests.length === 0 ? (
        <Card className="text-center py-12">
          <CardContent>
            <div className="space-y-4">
              <h3 className="text-lg font-medium">You don't have any quote requests yet</h3>
              <p className="text-muted-foreground">Start planning your Morocco adventure by creating a route and requesting a quote.</p>
              <Button 
                className="bg-morocco-terracotta hover:bg-morocco-terracotta/90 mt-4"
                onClick={() => navigate('/')}
              >
                Plan Your Trip
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {quoteRequests.map((request) => (
            <Card key={request.id}>
              <CardHeader>
                <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-2">
                  <div>
                    <CardTitle className="text-xl">{request.full_name}'s Morocco Trip</CardTitle>
                    <CardDescription>
                      Requested on {new Date(request.created_at).toLocaleDateString()}
                    </CardDescription>
                  </div>
                  <Badge className={`${getStatusColor(request.status)} capitalize self-start md:self-auto`}>
                    {request.status}
                  </Badge>
                </div>
              </CardHeader>

              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-medium text-sm text-gray-500 mb-2 flex items-center">
                        <MapPin className="h-4 w-4 mr-1 text-morocco-terracotta" /> 
                        Destinations
                      </h3>
                      <ul className="space-y-1 ml-6">
                        {request.route_details.destinations.map((dest, index) => (
                          <li key={dest.id} className="flex items-center">
                            <span className="bg-morocco-terracotta text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-2">
                              {index + 1}
                            </span>
                            <span>{dest.name}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {request.travel_date && (
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-morocco-terracotta" />
                        <span>
                          {new Date(request.travel_date).toLocaleDateString('en-US', { 
                            year: 'numeric', 
                            month: 'long', 
                            day: 'numeric' 
                          })}
                        </span>
                      </div>
                    )}

                    <div className="flex items-center">
                      <Users className="h-4 w-4 mr-2 text-morocco-terracotta" />
                      <span>{request.num_travelers} travelers</span>
                    </div>

                    {request.special_requests && (
                      <div>
                        <h3 className="font-medium text-sm text-gray-500 mb-2 flex items-center">
                          <MessageSquare className="h-4 w-4 mr-1 text-morocco-terracotta" /> 
                          Special Requests
                        </h3>
                        <p className="text-sm ml-6 text-gray-600">{request.special_requests}</p>
                      </div>
                    )}
                  </div>

                  <div>
                    {request.route_details.route_stats && (
                      <div className="space-y-2 border-l pl-6 border-gray-200 hidden md:block">
                        <h3 className="font-medium text-sm text-gray-500 mb-3">Trip Details</h3>
                        
                        <div className="flex items-center mb-2">
                          <Clock className="h-4 w-4 mr-2 text-morocco-terracotta" />
                          <span className="text-sm">
                            Duration: {Math.floor(request.route_details.route_stats.totalDuration / 60)} hours {request.route_details.route_stats.totalDuration % 60} minutes driving time
                          </span>
                        </div>
                        
                        <div className="flex items-center mb-2">
                          <MapPin className="h-4 w-4 mr-2 text-morocco-terracotta" />
                          <span className="text-sm">
                            Distance: {request.route_details.route_stats.totalDistance} km
                          </span>
                        </div>
                        
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-2 text-morocco-terracotta" />
                          <span className="text-sm">
                            Recommended: {request.route_details.route_stats.recommendedDays} days
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default QuoteRequests;
