import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import mapboxgl from 'mapbox-gl';
import { ClientProvider } from '../contexts/ClientContext';
import { ThemeProvider } from '../providers/ThemeProvider';
import ExploreMap from '../components/map/ExploreMap';
import POIModal from '../components/map/POIModal';
import POIHoverCard from '../components/map/POIHoverCard';
import ItineraryPanel from '../components/itinerary/ItineraryPanel';
import LeftPane from '../components/layout/LeftPane';
import RightPane from '../components/layout/RightPane';
import TopBar from '../components/layout/TopBar';

// Use neutral styling instead of Morocco
import '../styles/neutral-theme.css';
import 'mapbox-gl/dist/mapbox-gl.css';
import { Destination, PointOfInterest, POICategory } from '@/types';
import { Position, PositionTuple, toPositionObject, PositionObject } from '../types/Position';
import { JourneyPhase } from '../components/map/animation/types/travelAnimatorTypes';
import { ButtonState } from '../types/TravelAnimatorTypes';
import { TravelPace, JourneyStyle, TravelInterest, PreArrangedJourney } from '../types/ItineraryParameters';
import { ChevronLeft, ChevronRight, Calendar, MapPin, Sun, Info, Star, Activity } from 'lucide-react';
import CitySelectionOverlay from '../components/map/CitySelectionOverlay';
import { cn } from '@/lib/utils';
import PreArrangedJourneyLinks from '../components/map/PreArrangedJourneyLinks';
import { useSmartUI, UIMode } from '../hooks/useSmartUI';
import { useClient } from '../contexts/ClientContext';
import { regionData } from '../data/destinations';
import { moroccoPreArrangedJourneys } from '../data/preArrangedJourneys';
import DatabaseService from '../services/database/DatabaseService';

// Neutral demo data (generic, not region-specific)
const DEMO_CENTER: [number, number] = [-98.5795, 39.8283]; // Center of USA
const DEMO_BOUNDS: [[number, number], [number, number]] = [
  [-125.0, 25.0], // Southwest coordinates
  [-65.0, 50.0]   // Northeast coordinates
];

// Available regions for neutral demo (framework demo is global)
const AVAILABLE_REGIONS = ['Global Framework'];

// Sample neutral destinations
const DEMO_DESTINATIONS: Destination[] = [
  {
    id: 'demo-city-1',
    name: 'Metropolitan City',
    description: 'A vibrant urban center with rich culture and history',
    coordinates: [-74.006, 40.7128], // New York coordinates as example
    image: '/assets/neutral/cities/metro-city.jpg',
    type: 'city'
  },
  {
    id: 'demo-city-2',
    name: 'Coastal Town',
    description: 'Charming seaside destination with beautiful beaches',
    coordinates: [-118.2437, 34.0522], // Los Angeles coordinates as example
    image: '/assets/neutral/cities/coastal-town.jpg',
    type: 'city'
  },
  {
    id: 'demo-city-3',
    name: 'Mountain Village',
    description: 'Peaceful mountain retreat with stunning views',
    coordinates: [-105.2705, 40.0150], // Boulder, Colorado coordinates
    image: '/assets/neutral/cities/mountain-village.jpg',
    type: 'city'
  },
  {
    id: 'demo-city-4',
    name: 'Historic District',
    description: 'Rich historical heritage and cultural landmarks',
    coordinates: [-71.0589, 42.3601], // Boston coordinates
    image: '/assets/neutral/cities/historic-district.jpg',
    type: 'city'
  }
];

// Sample neutral POIs
const DEMO_POIS: PointOfInterest[] = [
  {
    id: 'demo-poi-1',
    name: 'Central Museum',
    description: 'World-class museum with extensive collections',
    coordinates: [-74.006, 40.7128],
    category: 'landmark',
    image: '/assets/neutral/pois/museum.jpg',
    tags: ['culture', 'history', 'art'],
    rating: 4.8,
    cost: 'medium'
  },
  {
    id: 'demo-poi-2',
    name: 'Waterfront Park',
    description: 'Beautiful park with scenic water views',
    coordinates: [-118.2437, 34.0522],
    category: 'nature',
    image: '/assets/neutral/pois/park.jpg',
    tags: ['nature', 'relaxation', 'views'],
    rating: 4.6,
    cost: 'low'
  },
  {
    id: 'demo-poi-3',
    name: 'Gourmet Restaurant',
    description: 'Fine dining with locally sourced ingredients',
    coordinates: [-105.2705, 40.0150],
    category: 'restaurant',
    image: '/assets/neutral/pois/restaurant.jpg',
    tags: ['dining', 'local cuisine', 'fine dining'],
    rating: 4.9,
    cost: 'high'
  },
  {
    id: 'demo-poi-4',
    name: 'Adventure Center',
    description: 'Outdoor activities and adventure sports',
    coordinates: [-71.0589, 42.3601],
    category: 'activity',
    image: '/assets/neutral/pois/adventure.jpg',
    tags: ['adventure', 'outdoor', 'sports'],
    rating: 4.7,
    cost: 'medium'
  }
];

// Sample route through demo cities
const ROUTE_POINTS: PositionTuple[] = [
  [-74.006, 40.7128],   // Metropolitan City (New York)
  [-118.2437, 34.0522], // Coastal Town (Los Angeles)
  [-105.2705, 40.0150], // Mountain Village (Boulder)
  [-71.0589, 42.3601]   // Historic District (Boston)
];

// Route converted to Position objects for animation
const ROUTE: Position[] = ROUTE_POINTS.map(point => {
  const pos = toPositionObject(point);
  if (!pos) {
    return { lng: point[0], lat: point[1] };
  }
  return pos;
});

// Sample pre-arranged journeys for neutral demo
const DEMO_PRE_ARRANGED_JOURNEYS: PreArrangedJourney[] = [
  {
    id: 'demo-journey-1',
    name: 'Urban Explorer',
    description: 'Discover vibrant city life and cultural landmarks',
    duration: 5,
    difficulty: 'easy',
    pace: 'balanced-explorer',
    style: 'cultural-deep-dive',
    cities: ['Metropolitan City', 'Historic District'],
    highlights: ['Central Museum', 'Historic landmarks', 'Urban culture'],
    imageUrl: '/images/journeys/urban-explorer.jpg',
    price: {
      from: 800,
      currency: 'USD'
    },
    seasonality: ['spring', 'summer', 'fall'],
    tags: ['culture', 'urban', 'museums', 'landmarks', 'city-break']
  },
  {
    id: 'demo-journey-2',
    name: 'Nature & Adventure',
    description: 'Experience outdoor adventures and natural beauty',
    duration: 7,
    difficulty: 'moderate',
    pace: 'adventure-seeker',
    style: 'nature-focused',
    cities: ['Mountain Village', 'Coastal Town'],
    highlights: ['Waterfront Park', 'Adventure Center', 'Scenic views'],
    imageUrl: '/images/journeys/nature-adventure.jpg',
    price: {
      from: 1200,
      currency: 'USD'
    },
    seasonality: ['spring', 'summer', 'fall'],
    tags: ['nature', 'adventure', 'outdoor', 'scenic', 'active']
  }
];

// Production-ready function to get client-specific data
const getClientData = async (clientId: string) => {
  try {
    // Try to get data from database first
    const databaseService = DatabaseService.getInstance();
    const dbData = await databaseService.getClientData(clientId);

    if (dbData.client) {
      console.log(`✅ [getClientData] Using database data for client: ${clientId}`);
      return {
        destinations: dbData.destinations,
        pois: dbData.pois,
        preArrangedJourneys: dbData.journeyTemplates,
        center: calculateCenterFromDestinations(dbData.destinations),
        bounds: calculateBoundsFromDestinations(dbData.destinations)
      };
    }
  } catch (error) {
    console.warn(`⚠️ [getClientData] Database unavailable for ${clientId}, using fallback:`, error);
  }

  // Fallback to mock data with proper error handling
  console.log(`🔄 [getClientData] Using mock data fallback for client: ${clientId}`);

  switch (clientId) {
    case 'morocco':
      const moroccoData = regionData['morocco'];
      if (!moroccoData) {
        throw new Error(`No data available for client: ${clientId}`);
      }
      return {
        destinations: moroccoData.destinations || [],
        pois: moroccoData.pointsOfInterest || [],
        preArrangedJourneys: moroccoPreArrangedJourneys,
        center: moroccoData.mapCenter || [-7.09, 31.79] as [number, number],
        bounds: moroccoData.maxBounds || [[-13.168555, 27.666667], [-1.030511, 35.922284]] as [[number, number], [number, number]]
      };
    case 'portugal':
      const portugalData = regionData['portugal'];
      if (!portugalData) {
        throw new Error(`No data available for client: ${clientId}`);
      }
      return {
        destinations: portugalData.destinations || [],
        pois: portugalData.pointsOfInterest || [],
        preArrangedJourneys: [], // Will be populated from database
        center: portugalData.mapCenter || [-8.61, 39.6] as [number, number],
        bounds: portugalData.maxBounds || [[-9.5, 36.8], [-6.2, 42.1]] as [[number, number], [number, number]]
      };
    default:
      // For unknown clients, return minimal safe data
      console.warn(`⚠️ [getClientData] Unknown client: ${clientId}, returning minimal data`);
      return {
        destinations: [],
        pois: [],
        preArrangedJourneys: [],
        center: [0, 0] as [number, number],
        bounds: [[-180, -90], [180, 90]] as [[number, number], [number, number]]
      };
  }
};

// Helper functions for dynamic bounds calculation
const calculateCenterFromDestinations = (destinations: Destination[]): [number, number] => {
  if (destinations.length === 0) return [0, 0];

  const totalLng = destinations.reduce((sum, dest) => sum + (dest.coordinates?.[0] || 0), 0);
  const totalLat = destinations.reduce((sum, dest) => sum + (dest.coordinates?.[1] || 0), 0);

  return [totalLng / destinations.length, totalLat / destinations.length];
};

const calculateBoundsFromDestinations = (destinations: Destination[]): [[number, number], [number, number]] => {
  if (destinations.length === 0) return [[-180, -90], [180, 90]];

  let minLng = destinations[0].coordinates?.[0] || 0;
  let maxLng = destinations[0].coordinates?.[0] || 0;
  let minLat = destinations[0].coordinates?.[1] || 0;
  let maxLat = destinations[0].coordinates?.[1] || 0;

  destinations.forEach(dest => {
    if (dest.coordinates) {
      minLng = Math.min(minLng, dest.coordinates[0]);
      maxLng = Math.max(maxLng, dest.coordinates[0]);
      minLat = Math.min(minLat, dest.coordinates[1]);
      maxLat = Math.max(maxLat, dest.coordinates[1]);
    }
  });

  // Add padding (10% of the range)
  const lngPadding = (maxLng - minLng) * 0.1;
  const latPadding = (maxLat - minLat) * 0.1;

  return [
    [minLng - lngPadding, minLat - latPadding],
    [maxLng + lngPadding, maxLat + latPadding]
  ];
};

// Enhanced Neutral Demo with all Morocco demo logic but neutral styling
const EnhancedNeutralDemo: React.FC = () => {
  // Get current client context
  const { clientId } = useClient();

  // Client data state
  const [clientData, setClientData] = useState({
    destinations: [],
    pois: [],
    preArrangedJourneys: [],
    center: [0, 0] as [number, number],
    bounds: [[-180, -90], [180, 90]] as [[number, number], [number, number]]
  });
  const [isLoadingClientData, setIsLoadingClientData] = useState(true);

  // Map state (same as Morocco demo)
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const markersRef = useRef<mapboxgl.Marker[]>([]);
  const exploreMapRef = useRef<any>(null);
  
  // Journey state (same as Morocco demo)
  const [destinations, setDestinations] = useState<Destination[]>(clientData.destinations);
  const [route, setRoute] = useState<Position[]>(ROUTE);
  const [journeyPhase, setJourneyPhase] = useState<JourneyPhase>(JourneyPhase.IDLE);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [journeyProgress, setJourneyProgress] = useState(0);
  const [currentRegion, setCurrentRegion] = useState(clientId === 'morocco' ? 'Morocco' : clientId === 'portugal' ? 'Portugal' : 'Global Framework');

  // New Journey Parameters State (same as Morocco demo)
  const [numberOfTravelDays, setNumberOfTravelDays] = useState<number>(7);
  const [travelPace, setTravelPace] = useState<TravelPace>('balanced-explorer');
  const [journeyStyle, setJourneyStyle] = useState<JourneyStyle>('cultural-deep-dive');
  const [travelInterests, setTravelInterests] = useState<TravelInterest[]>([]);
  const [selectedPreArrangedJourney, setSelectedPreArrangedJourney] = useState<PreArrangedJourney | null>(null);

  // UI State (same as Morocco demo)
  const [isLeftPanelCollapsed, setIsLeftPanelCollapsed] = useState(false);
  const [isRightPanelCollapsed, setIsRightPanelCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [showMobileLeftPanel, setShowMobileLeftPanel] = useState(false);
  const [showMobileRightPanel, setShowMobileRightPanel] = useState(false);

  // Panel visibility state for cinematic mode
  const [panelsHidden, setPanelsHidden] = useState(false);
  const [panelTransition, setPanelTransition] = useState(false);

  // Animation control state
  const [animationSpeed, setAnimationSpeed] = useState(1);
  const [showAnimationControls, setShowAnimationControls] = useState(false);
  const [animationProgress, setAnimationProgress] = useState(0);

  // Mobile detection (same as Morocco demo)
  useEffect(() => {
    const checkMobile = () => {
      const isMobileView = window.innerWidth < 768;
      setIsMobile(isMobileView);

      if (!isMobileView) {
        setShowMobileLeftPanel(false);
        setShowMobileRightPanel(false);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Load client data when client changes
  useEffect(() => {
    const loadClientData = async () => {
      setIsLoadingClientData(true);
      try {
        console.log(`🔄 [EnhancedNeutralDemo] Loading data for client: ${clientId}`);
        const newClientData = await getClientData(clientId);

        setClientData(newClientData);
        setDestinations(newClientData.destinations);
        setCurrentRegion(clientId === 'morocco' ? 'Morocco' : clientId === 'portugal' ? 'Portugal' : 'Global Framework');

        // Reset selections when switching clients
        setSelectedCities([]);
        setSelectedPOIs([]);
        setItineraryPOIs([]);
        setSelectedDestination(null);
        setCanBeginJourney(false);
        setShowBeginJourneyButton(false);

        // Update map bounds and center for new region
        if (map.current && newClientData.destinations.length > 0) {
          console.log('🗺️ Updating map bounds for client:', clientId, newClientData.bounds);
          map.current.fitBounds(newClientData.bounds, {
            padding: { top: 50, bottom: 50, left: 50, right: 50 },
            duration: 1500,
            essential: true
          });
        }

        console.log(`✅ [EnhancedNeutralDemo] Client data loaded:`, {
          destinations: newClientData.destinations.length,
          pois: newClientData.pois.length,
          journeys: newClientData.preArrangedJourneys.length
        });
      } catch (error) {
        console.error(`❌ [EnhancedNeutralDemo] Failed to load client data:`, error);
        // Set minimal fallback data
        setClientData({
          destinations: [],
          pois: [],
          preArrangedJourneys: [],
          center: [0, 0] as [number, number],
          bounds: [[-180, -90], [180, 90]] as [[number, number], [number, number]]
        });
      } finally {
        setIsLoadingClientData(false);
      }
    };

    loadClientData();
  }, [clientId]);

  // POI state (same as Morocco demo)
  const [selectedDestination, setSelectedDestination] = useState<Destination | null>(null);
  const [showPOIOverlay, setShowPOIOverlay] = useState(false);
  const [selectedPOIs, setSelectedPOIs] = useState<PointOfInterest[]>([]);
  const [itineraryPOIs, setItineraryPOIs] = useState<PointOfInterest[]>([]);

  // City selection state (same as Morocco demo)
  const [selectedCities, setSelectedCities] = useState<Destination[]>([]);
  const [showCityOverlay, setShowCityOverlay] = useState(false);
  const [cityDayAllocations, setCityDayAllocations] = useState<Record<string, number>>({});

  // Journey planning state (same as Morocco demo)
  const [canBeginJourney, setCanBeginJourney] = useState(false);
  const [showBeginJourneyButton, setShowBeginJourneyButton] = useState(false);
  const [buttonState, setButtonState] = useState<ButtonState>('disabled');
  const [buttonError, setButtonError] = useState<string | null>(null);

  // Update begin journey state when selections change
  useEffect(() => {
    const hasEnoughCities = selectedCities.length >= 2;
    const hasPreArrangedJourney = selectedPreArrangedJourney !== null;
    const hasEnoughPOIs = selectedPOIs.length >= 2;

    const canBegin = hasEnoughCities || hasPreArrangedJourney || hasEnoughPOIs;
    setCanBeginJourney(canBegin);
    setShowBeginJourneyButton(canBegin);
    setButtonState(canBegin ? 'default' : 'disabled');
  }, [selectedCities, selectedPreArrangedJourney, selectedPOIs]);

  // Resize map when panels are hidden/shown
  useEffect(() => {
    if (map.current) {
      // Small delay to allow CSS transitions to complete
      const timer = setTimeout(() => {
        map.current.resize();

        // Re-fit bounds to ensure all destinations are visible (but not during animation)
        if (selectedCities.length >= 2 && !isAnimating) {
          const coordinates = selectedCities.map(city => city.coordinates).filter(coord => {
            return coord && coord.length === 2 && !isNaN(coord[0]) && !isNaN(coord[1]);
          });

          if (coordinates.length >= 2) {
            const bounds = coordinates.reduce((bounds, coord) => {
              return bounds.extend(coord);
            }, new mapboxgl.LngLatBounds(coordinates[0], coordinates[0]));

            try {
              map.current.fitBounds(bounds, {
                padding: panelsHidden ?
                  { top: 100, bottom: 100, left: 100, right: 100 } :
                  { top: 100, bottom: 100, left: 150, right: 150 },
                duration: 500,
                essential: true
              });
            } catch (error) {
              console.warn('Map bounds fitting failed:', error);
            }
          }
        }
      }, 600); // Wait for panel transition to complete

      return () => clearTimeout(timer);
    }
  }, [panelsHidden, selectedCities]);

  // POI discovery state (same as Morocco demo)
  const [discoveredPOI, setDiscoveredPOI] = useState<PointOfInterest | null>(null);
  const [showPOIDiscovery, setShowPOIDiscovery] = useState(false);

  // POI modal and hover state (same as Morocco demo)
  const [selectedPOIForModal, setSelectedPOIForModal] = useState<PointOfInterest | null>(null);
  const [showPOIModal, setShowPOIModal] = useState(false);
  const [hoveredPOI, setHoveredPOI] = useState<PointOfInterest | null>(null);
  const [showPOIHover, setShowPOIHover] = useState(false);
  const [hoverPosition, setHoverPosition] = useState({ x: 0, y: 0 });

  // Handler functions (cloned from Morocco demo)
  const handleRegionChange = (region: string) => {
    setCurrentRegion(region);
    console.log(`Changed region to ${region}`);
  };

  const handleNumberOfTravelDaysChange = useCallback((days: number) => {
    setNumberOfTravelDays(Math.max(1, Math.min(30, days)));
  }, []);

  const handleTravelPaceChange = useCallback((pace: TravelPace) => {
    setTravelPace(pace);
  }, []);

  const handleJourneyStyleChange = useCallback((style: JourneyStyle) => {
    setJourneyStyle(style);
  }, []);

  const handleTravelInterestToggle = useCallback((interest: TravelInterest) => {
    setTravelInterests(prevInterests => {
      if (prevInterests.includes(interest)) {
        return prevInterests.filter(i => i !== interest);
      }
      return [...prevInterests, interest];
    });
  }, []);

  const handleCityDayAllocationChange = useCallback((cityName: string, days: number) => {
    setCityDayAllocations(prev => ({
      ...prev,
      [cityName]: Math.max(1, Math.min(15, days))
    }));
  }, []);

  const handlePreArrangedJourneySelect = useCallback((journey: PreArrangedJourney) => {
    setSelectedPreArrangedJourney(journey);
    setNumberOfTravelDays(journey.duration);
    setTravelPace(journey.pace);
    setJourneyStyle(journey.style);

    const journeyCities = destinations.filter(dest =>
      journey.cities.includes(dest.name)
    );
    setSelectedCities(journeyCities);

    if (journeyCities.length > 0 && map.current) {
      const firstCity = journeyCities[0];
      if (firstCity.coordinates) {
        map.current.flyTo({
          center: firstCity.coordinates,
          zoom: 12,
          duration: 1500
        });
      }
    }
  }, [destinations]);

  const toggleLeftPanel = useCallback(() => {
    if (isMobile) {
      setShowMobileLeftPanel(!showMobileLeftPanel);
      setShowMobileRightPanel(false);
    } else {
      setIsLeftPanelCollapsed(!isLeftPanelCollapsed);
    }
  }, [isMobile, showMobileLeftPanel, isLeftPanelCollapsed]);

  const toggleRightPanel = useCallback(() => {
    if (isMobile) {
      setShowMobileRightPanel(!showMobileRightPanel);
      setShowMobileLeftPanel(false);
    } else {
      setIsRightPanelCollapsed(!isRightPanelCollapsed);
    }
  }, [isMobile, showMobileRightPanel, isRightPanelCollapsed]);

  const handleSequentialCitySelection = useCallback((destination: Destination) => {
    setSelectedCities(prev => {
      const exists = prev.some(d => d.id === destination.id);

      if (exists) {
        const newDestinations = prev.filter(d => d.id !== destination.id);
        setCityDayAllocations(prevAllocations => {
          const newAllocations = { ...prevAllocations };
          delete newAllocations[destination.name];
          return newAllocations;
        });
        return newDestinations;
      } else {
        const newDestinations = [...prev, destination];

        setCityDayAllocations(prevAllocations => ({
          ...prevAllocations,
          [destination.name]: 2
        }));

        if (map.current && destination.coordinates) {
          map.current.flyTo({
            center: destination.coordinates,
            zoom: 12,
            duration: 1500
          });
        }

        setSelectedDestination(destination);
        return newDestinations;
      }
    });
  }, []);

  const handleCitySelect = (city: Destination) => {
    handleSequentialCitySelection(city);

    setSelectedCities(prev => {
      const newCities = prev.some(c => c.id === city.id) ? prev : [...prev, city];
      const canBegin = newCities.length >= 2 || selectedPreArrangedJourney !== null;
      setCanBeginJourney(canBegin);
      setShowBeginJourneyButton(canBegin);
      setButtonState(canBegin ? 'default' : 'disabled');
      return newCities;
    });
  };

  const handleCityRemove = useCallback((cityToRemove: Destination) => {
    setSelectedCities(prev => {
      const newDestinations = prev.filter(d => d.id !== cityToRemove.id);

      setCityDayAllocations(prevAllocations => {
        const newAllocations = { ...prevAllocations };
        delete newAllocations[cityToRemove.name];
        return newAllocations;
      });

      if (selectedDestination?.id === cityToRemove.id) {
        const lastCity = newDestinations[newDestinations.length - 1];
        setSelectedDestination(lastCity || null);

        if (lastCity && map.current && lastCity.coordinates) {
          map.current.flyTo({
            center: lastCity.coordinates,
            zoom: 12,
            duration: 1000
          });
        } else if (map.current) {
          map.current.flyTo({
            center: DEMO_CENTER,
            zoom: 6,
            duration: 1000
          });
        }
      }

      return newDestinations;
    });
  }, [selectedDestination]);

  const handleCityDeselect = (city: Destination) => {
    handleCityRemove(city);
  };

  const handleStartJourney = async () => {
    if (selectedCities.length < 2) {
      return;
    }

    // Start cinematic mode - hide panels for immersive experience
    setPanelTransition(true);
    setShowAnimationControls(true);
    setAnimationProgress(0);

    // Hide panels with smooth transition
    setTimeout(() => {
      setPanelsHidden(true);
      if (isMobile) {
        setShowMobileLeftPanel(false);
        setShowMobileRightPanel(false);
      }
    }, 100);

    setItineraryPOIs(selectedPOIs);

    if (exploreMapRef.current) {
      // Force map to center on Morocco before starting animation
      if (map.current) {
        console.log('🗺️ Forcing map to center on Morocco before animation');
        map.current.fitBounds(clientData.bounds, {
          padding: { top: 50, bottom: 50, left: 50, right: 50 },
          duration: 1000,
          essential: true
        });

        // Wait for map to finish centering
        await new Promise(resolve => setTimeout(resolve, 1200));
      }

      // Add a small delay to ensure route is ready
      await new Promise(resolve => setTimeout(resolve, 500));

      exploreMapRef.current.beginJourney();
      setJourneyPhase(JourneyPhase.ANIMATING);
      setShowPOIOverlay(false);
    } else {
      setIsAnimating(true);
      setJourneyPhase(JourneyPhase.ANIMATING);
      setShowPOIOverlay(false);
    }
  };

  const handlePauseJourney = () => {
    setIsPaused(!isPaused);
  };

  const handleAnimationComplete = () => {
    console.log('🎬 Animation complete - restoring panels');
    setPanelTransition(true);
    setShowAnimationControls(false);
    setAnimationProgress(100);

    // Restore panels with smooth transition
    setTimeout(() => {
      setPanelsHidden(false);
      setPanelTransition(false);
    }, 100);
  };

  const handleExitCinematicMode = () => {
    console.log('🎬 Exiting cinematic mode manually');
    setPanelTransition(true);
    setShowAnimationControls(false);

    // Stop animation if running
    if (exploreMapRef.current) {
      // Add stop animation method if available
    }

    // Restore panels
    setTimeout(() => {
      setPanelsHidden(false);
      setPanelTransition(false);
      setJourneyPhase(JourneyPhase.PLANNING);
      setIsAnimating(false);
    }, 100);
  };

  const handleSelectPOI = (poi: PointOfInterest) => {
    const normalizedPoi: PointOfInterest = {
      ...poi,
      category: poi.category || 'other' as POICategory,
      images: poi.images || [],
      tags: poi.tags || []
    };

    if (map.current && poi.coordinates) {
      map.current.flyTo({
        center: poi.coordinates,
        zoom: 15,
        duration: 1500,
        essential: true
      });

      setTimeout(() => {
        setHoveredPOI(normalizedPoi);
        const centerX = window.innerWidth / 2;
        const centerY = window.innerHeight / 2;
        setHoverPosition({ x: centerX, y: centerY });
        setShowPOIHover(true);
        setShowPOIModal(false);
      }, 1600);
    }
  };

  const handleAddPOIToTrip = (poi: PointOfInterest) => {
    const normalizedPoi: PointOfInterest = {
      ...poi,
      category: poi.category || 'other' as POICategory,
      images: poi.images || [],
      tags: poi.tags || []
    };

    setSelectedPOIs(current => {
      const isSelected = current.some(p => p.id === normalizedPoi.id);

      if (isSelected) {
        return current.filter(p => p.id !== normalizedPoi.id);
      } else {
        return [...current, normalizedPoi];
      }
    });
  };

  const handlePOIMarkerHover = (poi: PointOfInterest, event: MouseEvent) => {
    setHoveredPOI(poi);
    setHoverPosition({ x: event.clientX, y: event.clientY });
    setShowPOIHover(true);
  };

  const handlePOIMarkerHoverEnd = useCallback(() => {
    setShowPOIHover(false);
    setHoveredPOI(null);
  }, []);

  const handleViewPOIDetails = useCallback((poi: PointOfInterest) => {
    setSelectedPOIForModal(poi);
    setShowPOIModal(true);
    setShowPOIHover(false);
  }, []);

  const handleAddPOIFromModal = useCallback((poi: PointOfInterest) => {
    handleAddPOIToTrip(poi);
    setShowPOIModal(false);
  }, []);

  const handlePOIBooking = useCallback((poi: PointOfInterest) => {
    console.log('Booking POI:', poi.name);
  }, []);
  
  return (
    <div className="enhanced-neutral-demo h-screen theme-neutral">
          {/* TopBar with neutral styling */}
          <TopBar
            currentRegion={currentRegion}
            availableRegions={AVAILABLE_REGIONS}
            onRegionChange={(region) => setCurrentRegion(region)}
            isAnimating={isAnimating}
            isPaused={isPaused}
            journeyProgress={journeyProgress}
            onStartJourney={() => console.log('Start journey')}
            onPauseJourney={() => setIsPaused(!isPaused)}
            selectedCities={selectedCities}
            selectedPOIs={selectedPOIs}
            canBeginJourney={canBeginJourney}
            showBeginJourneyButton={showBeginJourneyButton}
            onBeginJourney={handleStartJourney}
            numberOfDays={Object.values(cityDayAllocations).reduce((sum, days) => sum + days, 0) || 7}
            travelPace={travelPace}
          />

          {/* Main content with same structure as Morocco demo but neutral styling */}
          <div
            className={cn(
              "flex w-screen transition-all duration-500 ease-in-out overflow-hidden gap-0"
            )}
            style={{
              marginTop: '70px',
              height: 'calc(100vh - 70px)', // Explicit height calculation
              maxWidth: '100vw', // Ensure it doesn't exceed viewport
              margin: '70px 0 0 0', // Explicit margin reset
              padding: '0' // Explicit padding reset
            }}
          >

            {/* Mobile Overlay for Left Panel */}
            {isMobile && showMobileLeftPanel && (
              <div
                className="fixed inset-0 bg-black bg-opacity-50 z-20"
                onClick={() => setShowMobileLeftPanel(false)}
              />
            )}

            {/* Left Panel - Enhanced with New Components */}
            <div
              className={cn(
                "h-full transition-all duration-500 ease-in-out flex-shrink-0",
                panelsHidden ? "w-0 opacity-0 pointer-events-none z-20" :
                isMobile ? (
                  showMobileLeftPanel
                    ? "fixed left-0 top-0 w-4/5 sm:w-3/5 md:w-80 opacity-100 pointer-events-auto z-30 transform translate-x-0"
                    : "fixed left-0 top-0 w-4/5 sm:w-3/5 md:w-80 opacity-0 pointer-events-none z-30 transform -translate-x-full"
                ) : (
                  isLeftPanelCollapsed
                    ? "w-0 opacity-0 pointer-events-none z-20"
                    : "opacity-100 z-20"
                )
              )}
              style={isMobile ? { height: '100vh', marginTop: '70px' } : {
                width: panelsHidden ? '0px' : (isLeftPanelCollapsed ? '0px' : '380px'), // Reduced from 480px
                margin: '0',
                padding: '0'
              }}
            >
              {!isMobile && !panelsHidden && (
                <button
                  onClick={toggleLeftPanel}
                  className="absolute -right-6 top-1/2 -translate-y-1/2 bg-white p-1 rounded-r shadow-lg z-50 border border-gray-200"
                  style={{ color: 'var(--theme-primary-color)' }}
                >
                  {isLeftPanelCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
                </button>
              )}
              <LeftPane
                isAnimating={isAnimating}
                onTogglePane={toggleLeftPanel}
                selectedPOIs={selectedPOIs}
                canStartJourney={canBeginJourney}
                minPoisRequired={2}
                allPois={clientData.pois}
                onSelectPOI={handleSelectPOI}
                onAddPOI={handleAddPOIToTrip}
                selectedInterests={travelInterests}
                onInterestToggle={handleTravelInterestToggle}
                isMobile={isMobile}
                availableCities={destinations}
                selectedCities={selectedCities}
                onCitySelect={handleCitySelect}
                onCityDeselect={handleCityDeselect}
                cityDayAllocations={cityDayAllocations}
                onCityDayAllocationChange={handleCityDayAllocationChange}
              />
            </div>

            {/* Map Container */}
            <div
              className="relative transition-all duration-500 ease-in-out overflow-hidden flex-1"
              style={{
                height: '100%',
                margin: '0',
                padding: '0',
                boxSizing: 'border-box',
                minWidth: panelsHidden ? '100vw' : '200px'
              }}
            >
              <div id="explore-map-container" className="w-full h-full overflow-hidden">
                <ExploreMap
                  ref={exploreMapRef}
                  citiesForCountry={destinations}
                  poisForCurrentSelection={clientData.pois}
                  selectedDestinations={selectedCities}
                  selectedPOIs={selectedPOIs}
                  onDestinationSelect={handleCitySelect}
                  onPOISelect={handleSelectPOI}
                  onPOIDeselect={handleSelectPOI}
                  mapCenter={clientData.center}
                  mapZoom={6}
                  maxBounds={clientData.bounds}
                  onCitySelect={handleCitySelect}
                  selectedCity={selectedDestination}
                  isMobile={isMobile}
                  onMapReady={(mapInstance) => {
                    map.current = mapInstance;
                  }}
                  onAnimationComplete={handleAnimationComplete}
                />
              </div>

              {/* City Selection Swatches - Hidden on Mobile and during cinematic mode */}
              {!isMobile && !panelsHidden && (
                <CitySelectionOverlay
                  availableCities={destinations}
                  selectedCities={selectedCities}
                  onCitySelect={handleCitySelect}
                  onCityDeselect={handleCityDeselect}
                  currentRegion={currentRegion}
                  numberOfTravelDays={Object.values(cityDayAllocations).reduce((sum, days) => sum + days, 0) || Math.ceil(selectedCities.length * 2)}
                  onNumberOfTravelDaysChange={() => {}}
                  travelStyle={travelPace}
                  onTravelStyleChange={handleTravelPaceChange}
                  journeyStyle={journeyStyle}
                  onJourneyStyleChange={handleJourneyStyleChange}
                  cityDayAllocations={cityDayAllocations}
                  onCityDayAllocationChange={handleCityDayAllocationChange}
                />
              )}

              {/* POI Hover Card */}
              <POIHoverCard
                poi={hoveredPOI}
                isVisible={showPOIHover}
                position={hoverPosition}
                onClose={handlePOIMarkerHoverEnd}
                onViewDetails={handleViewPOIDetails}
              />

              {/* POI Modal */}
              <POIModal
                poi={selectedPOIForModal}
                isVisible={showPOIModal}
                onClose={() => setShowPOIModal(false)}
                onAddToItinerary={handleAddPOIFromModal}
                onBooking={handlePOIBooking}
                isInItinerary={selectedPOIForModal ? selectedPOIs.some(p => p.id === selectedPOIForModal.id) : false}
              />

              {/* Pre-arranged Journey Links */}
              {!isAnimating && (
                <PreArrangedJourneyLinks
                  journeys={clientData.preArrangedJourneys}
                  onJourneySelect={handlePreArrangedJourneySelect}
                  isMobile={isMobile}
                />
              )}

              {/* Mobile Toggle Buttons */}
              {isMobile && (
                <>
                  {/* Left Panel Toggle Button - POI Explorer */}
                  <button
                    onClick={toggleLeftPanel}
                    className="fixed bottom-6 left-6 w-14 h-14 rounded-full shadow-xl z-40 transition-all duration-300 flex items-center justify-center border-2"
                    style={{
                      backgroundColor: showMobileLeftPanel ? 'var(--theme-primary-color)' : 'white',
                      borderColor: 'var(--theme-primary-color)',
                      color: showMobileLeftPanel ? 'white' : 'var(--theme-primary-color)',
                      boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)',
                      transform: showMobileLeftPanel ? 'scale(1.1)' : 'scale(1)'
                    }}
                  >
                    <MapPin className="h-6 w-6" />
                  </button>

                  {/* Right Panel Toggle Button - Trip Planner */}
                  <button
                    onClick={toggleRightPanel}
                    className="fixed bottom-6 right-6 w-14 h-14 rounded-full shadow-xl z-40 transition-all duration-300 flex items-center justify-center border-2"
                    style={{
                      backgroundColor: showMobileRightPanel ? 'var(--theme-secondary-color)' : 'white',
                      borderColor: 'var(--theme-secondary-color)',
                      color: showMobileRightPanel ? 'white' : 'var(--theme-secondary-color)',
                      boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)',
                      transform: showMobileRightPanel ? 'scale(1.1)' : 'scale(1)'
                    }}
                  >
                    <Calendar className="h-6 w-6" />
                  </button>

                  {/* Mobile Button Labels */}
                  {!showMobileLeftPanel && !showMobileRightPanel && (
                    <>
                      <div
                        className="fixed bottom-20 left-6 text-xs text-center z-30 pointer-events-none"
                        style={{
                          color: 'var(--theme-primary-color)',
                          fontWeight: 600,
                          fontFamily: 'var(--theme-font-body)',
                          textShadow: '0 1px 3px rgba(255, 255, 255, 0.8)'
                        }}
                      >
                        Explore
                      </div>
                      <div
                        className="fixed bottom-20 right-6 text-xs text-center z-30 pointer-events-none"
                        style={{
                          color: 'var(--theme-secondary-color)',
                          fontWeight: 600,
                          fontFamily: 'var(--theme-font-body)',
                          textShadow: '0 1px 3px rgba(255, 255, 255, 0.8)'
                        }}
                      >
                        Plan
                      </div>
                    </>
                  )}
                </>
              )}
            </div>

            {/* Mobile Overlay for Right Panel */}
            {isMobile && showMobileRightPanel && (
              <div
                className="fixed inset-0 bg-black bg-opacity-50 z-20"
                onClick={() => setShowMobileRightPanel(false)}
              />
            )}

            {/* Right Panel - Enhanced with New Components */}
            <div
              className={cn(
                "h-full transition-all duration-500 ease-in-out flex-shrink-0",
                panelsHidden ? "w-0 opacity-0 pointer-events-none z-20" :
                isMobile ? (
                  showMobileRightPanel
                    ? "fixed right-0 top-0 w-4/5 sm:w-3/5 md:w-80 opacity-100 pointer-events-auto z-30 transform translate-x-0"
                    : "fixed right-0 top-0 w-4/5 sm:w-3/5 md:w-80 opacity-0 pointer-events-none z-30 transform translate-x-full"
                ) : (
                  isRightPanelCollapsed
                    ? "w-0 opacity-0 pointer-events-none z-20"
                    : "opacity-100 z-20"
                )
              )}
              style={isMobile ? { height: '100vh', marginTop: '70px' } : {
                width: panelsHidden ? '0px' : (isRightPanelCollapsed ? '0px' : '480px'),
                margin: '0',
                padding: '0'
              }}
            >
              {!isMobile && !panelsHidden && (
                <button
                  onClick={toggleRightPanel}
                  className="absolute -left-6 top-1/2 -translate-y-1/2 bg-white p-1 rounded-l shadow-lg z-50 border border-gray-200"
                  style={{ color: 'var(--theme-secondary-color)' }}
                >
                  {isRightPanelCollapsed ? <ChevronLeft className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                </button>
              )}
              <RightPane
                isAnimating={isAnimating}
                isPaused={isPaused}
                onTogglePane={toggleRightPanel}
                journeyProgress={journeyProgress}
                selectedCities={selectedCities}
                itineraryPOIs={selectedPOIs}
                tripDuration={Object.values(cityDayAllocations).reduce((sum, days) => sum + days, 0) || Math.ceil(selectedCities.length * 2)}
                onTripDurationChange={() => {}}
                onRemovePOI={handleAddPOIToTrip}
                onReorderPOI={(fromIndex, toIndex) => {
                  console.log(`Reordering POI from ${fromIndex} to ${toIndex}`);
                }}
                onRemoveCity={handleCityRemove}
                onExportItinerary={() => {
                  console.log('Exporting itinerary...');
                }}
                cityDayAllocations={cityDayAllocations}
                onCityDayAllocationChange={handleCityDayAllocationChange}
                // AI props
                enableAI={true}
                userInterests={travelInterests.map(interest => interest.name)}
                travelStyle="cultural"
                availablePOIs={clientData.pois}
                onAICitiesSelected={(cities) => {
                  cities.forEach(city => handleCitySelect(city));
                }}
                onAIPOIsSelected={(pois) => {
                  pois.forEach(poi => {
                    const mappedPOI = {
                      id: poi.id,
                      name: poi.name,
                      description: poi.description,
                      category: poi.category as any,
                      tags: poi.tags || [],
                      location: poi.location || '',
                      region: poi.region || '',
                      coordinates: poi.coordinates || [0, 0],
                      position: { lat: poi.coordinates?.[1] || 0, lng: poi.coordinates?.[0] || 0 },
                      price: 0,
                      duration: 60,
                      rating: 4.5,
                      reviews: 0,
                      images: [],
                      openingHours: '',
                      website: '',
                      phone: ''
                    };
                    handleAddPOIToTrip(mappedPOI);
                  });
                }}
                onAIItineraryGenerated={(itinerary) => {
                  console.log('AI Generated Itinerary:', itinerary);
                }}
              />
            </div>
          </div>

          {/* Cinematic Mode Exit Button */}
          {panelsHidden && (
            <div className="fixed top-20 right-4 z-50">
              <button
                onClick={handleExitCinematicMode}
                className="bg-black/70 hover:bg-black/90 text-white px-4 py-2 rounded-lg backdrop-blur-sm transition-all duration-300 flex items-center gap-2 shadow-lg"
                style={{
                  fontFamily: 'var(--font-heading)',
                  fontSize: '0.875rem',
                  fontWeight: '600'
                }}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M18 6 6 18"/>
                  <path d="m6 6 12 12"/>
                </svg>
                Exit Cinematic Mode
              </button>
            </div>
          )}

          {/* Animation Controls */}
          {showAnimationControls && (
            <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50">
              <div className="bg-black/80 backdrop-blur-sm rounded-xl px-6 py-4 shadow-2xl border border-white/20">
                <div className="flex items-center gap-4 text-white">
                  {/* Play/Pause Button */}
                  <button
                    onClick={() => setIsPaused(!isPaused)}
                    className="w-10 h-10 rounded-full bg-white/20 hover:bg-white/30 flex items-center justify-center transition-all duration-200"
                  >
                    {isPaused ? (
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    ) : (
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                      </svg>
                    )}
                  </button>

                  {/* Speed Control */}
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Speed:</span>
                    <select
                      value={animationSpeed}
                      onChange={(e) => setAnimationSpeed(Number(e.target.value))}
                      className="bg-white/20 rounded px-2 py-1 text-sm border border-white/30 focus:outline-none focus:border-white/50"
                    >
                      <option value={0.5}>0.5x</option>
                      <option value={1}>1x</option>
                      <option value={1.5}>1.5x</option>
                      <option value={2}>2x</option>
                    </select>
                  </div>

                  {/* Progress Bar */}
                  <div className="flex items-center gap-2 min-w-[200px]">
                    <span className="text-sm font-medium">Progress:</span>
                    <div className="flex-1 bg-white/20 rounded-full h-2">
                      <div
                        className="bg-white rounded-full h-2 transition-all duration-300"
                        style={{ width: `${animationProgress}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium w-12 text-right">{Math.round(animationProgress)}%</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
  );
};

export default EnhancedNeutralDemo;
