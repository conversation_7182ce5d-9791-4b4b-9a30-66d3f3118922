import React from 'react';
import MoroccanLayout from '../components/layout/MoroccanLayout';
import '../styles/moroccan-theme.css';

const SimpleMorocco = () => {
  return (
    <MoroccanLayout>
      <div 
        style={{
          width: '100%',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'var(--morocco-sand-lightest)'
        }}
      >
        <h1 style={{ 
          fontFamily: 'var(--font-display)',
          color: 'var(--morocco-red)',
          fontSize: '3rem',
          marginBottom: '2rem'
        }}>
          Simple Morocco Test
        </h1>
        <p style={{ 
          fontFamily: 'var(--font-body)',
          color: 'var(--text-secondary)',
          fontSize: '1.5rem'
        }}>
          This is a simplified test page without Mapbox integration
        </p>
      </div>
    </MoroccanLayout>
  );
};

export default SimpleMorocco; 