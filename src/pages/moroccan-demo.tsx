import React, { useState, useEffect, useRef, useCallback } from 'react';
import mapboxgl from 'mapbox-gl';
import ExploreMap from '../components/map/ExploreMap';
// Note: TravelAnimator, POIOverlay, DirectPOIMarkers, POIDiscoveryNotification
// are now handled internally by ExploreMap
// But we still need POIModal and POIHoverCard for external interactions
import POIModal from '../components/map/POIModal';
import POIHoverCard from '../components/map/POIHoverCard';
import ItineraryPanel from '../components/itinerary/ItineraryPanel';
import LeftPane from '../components/layout/LeftPane';
import RightPane from '../components/layout/RightPane';
import TopBar from '../components/layout/TopBar';
// OLD CSS ARCHITECTURE (BACKUP)
// import '../styles/moroccan-theme.css';

// NEW CONSOLIDATED CSS ARCHITECTURE
import '../styles/index-new.css';
import 'mapbox-gl/dist/mapbox-gl.css';
import { Destination, PointOfInterest, POICategory } from '@/types';
import { Position, PositionTuple, toPositionObject, PositionObject } from '../types/Position';
import { JourneyPhase } from '../components/map/animation/types/travelAnimatorTypes';
import { ButtonState } from '../types/TravelAnimatorTypes';
import { TravelPace, JourneyStyle, TravelInterest, PreArrangedJourney } from '../types/ItineraryParameters';
import { regionData } from '@/data/destinations';
import { pointsOfInterest as importedAllPointsOfInterest } from '@/data/pointsOfInterest';
import { convertPOIToPointOfInterest, filterPOIsForCity, normalizeDestination } from '../utils/poiUtils';
import { moroccoPreArrangedJourneys, getRecommendedJourneys } from '../data/preArrangedJourneys';
import { ChevronLeft, ChevronRight, Calendar, MapPin, Sun, Info, Star, Activity } from 'lucide-react';
import CitySelectionOverlay from '../components/map/CitySelectionOverlay';
import { cn } from '@/lib/utils';
import PreArrangedJourneyLinks from '../components/map/PreArrangedJourneyLinks';
import { useSmartUI, UIMode } from '../hooks/useSmartUI';

// Morocco is centered at approximately these coordinates
const MOROCCO_CENTER: [number, number] = [-6.0, 31.8];
const MOROCCO_BOUNDS: [[number, number], [number, number]] = [
  [-17.0, 21.0], // Southwest coordinates
  [0.0, 36.0]    // Northeast coordinates
];

// Available regions for selection (Morocco demo covers these 3 regions)
const AVAILABLE_REGIONS = ['Morocco', 'Portugal', 'Spain'];

// Get Morocco data from the existing data source
const getMoroccoData = () => {
  const moroccoRegionData = regionData['morocco'] || regionData['morocco'];
  if (!moroccoRegionData) {
    console.error('Morocco region data not found');
    return { destinations: [], pointsOfInterest: [] };
  }

  return {
    destinations: moroccoRegionData.destinations.map(dest => normalizeDestination(dest)),
    pointsOfInterest: moroccoRegionData.pointsOfInterest.map(poi => convertPOIToPointOfInterest(poi))
  };
};

const { destinations: MOROCCO_DESTINATIONS, pointsOfInterest: MOROCCO_POIS } = getMoroccoData();

// Debug logging
console.log('Morocco Destinations:', MOROCCO_DESTINATIONS);
console.log('Morocco POIs:', MOROCCO_POIS);

// Sample route through Morocco
const ROUTE_POINTS: PositionTuple[] = [
  [-8.0078, 31.6295], // Marrakech
  [-7.5898, 33.5731], // Casablanca
  [-5.0078, 34.0339], // Fez
  [-5.2636, 35.1689]  // Chefchaouen
];

// Route converted to Position objects for animation
const ROUTE: Position[] = ROUTE_POINTS.map(point => {
  const pos = toPositionObject(point);
  if (!pos) {
    // Fallback if toPositionObject returns null
    return { lng: point[0], lat: point[1] };
  }
  return pos;
});

// This is a simpler demo for the Morocco-themed map interface
const MoroccanDemo: React.FC = () => {
  // Map state
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const markersRef = useRef<mapboxgl.Marker[]>([]);
  const exploreMapRef = useRef<any>(null);
  
  // Journey state
  const [destinations, setDestinations] = useState<Destination[]>(MOROCCO_DESTINATIONS);
  const [route, setRoute] = useState<Position[]>(ROUTE);
  const [journeyPhase, setJourneyPhase] = useState<JourneyPhase>(JourneyPhase.IDLE);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [journeyProgress, setJourneyProgress] = useState(0);
  const [currentRegion, setCurrentRegion] = useState('Morocco');

  // New Journey Parameters State
  const [numberOfTravelDays, setNumberOfTravelDays] = useState<number>(7);
  const [travelPace, setTravelPace] = useState<TravelPace>('balanced-explorer');
  const [journeyStyle, setJourneyStyle] = useState<JourneyStyle>('cultural-deep-dive');
  const [travelInterests, setTravelInterests] = useState<TravelInterest[]>([]);
  const [selectedPreArrangedJourney, setSelectedPreArrangedJourney] = useState<PreArrangedJourney | null>(null);

  // UI State
  const [isLeftPanelCollapsed, setIsLeftPanelCollapsed] = useState(false);
  const [isRightPanelCollapsed, setIsRightPanelCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [showMobileLeftPanel, setShowMobileLeftPanel] = useState(false);
  const [showMobileRightPanel, setShowMobileRightPanel] = useState(false);

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      const isMobileView = window.innerWidth < 768;
      setIsMobile(isMobileView);

      // Close mobile panels when switching to desktop
      if (!isMobileView) {
        setShowMobileLeftPanel(false);
        setShowMobileRightPanel(false);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // POI state
  const [selectedDestination, setSelectedDestination] = useState<Destination | null>(null);
  const [showPOIOverlay, setShowPOIOverlay] = useState(false);
  const [selectedPOIs, setSelectedPOIs] = useState<PointOfInterest[]>([]);
  const [itineraryPOIs, setItineraryPOIs] = useState<PointOfInterest[]>([]);

  // City selection state
  const [selectedCities, setSelectedCities] = useState<Destination[]>([]);
  const [showCityOverlay, setShowCityOverlay] = useState(false);
  const [cityDayAllocations, setCityDayAllocations] = useState<Record<string, number>>({});

  // Journey planning state
  const [canBeginJourney, setCanBeginJourney] = useState(false);
  const [showBeginJourneyButton, setShowBeginJourneyButton] = useState(false);
  const [buttonState, setButtonState] = useState<ButtonState>('disabled');
  const [buttonError, setButtonError] = useState<string | null>(null);

  // POI discovery state
  const [discoveredPOI, setDiscoveredPOI] = useState<PointOfInterest | null>(null);
  const [showPOIDiscovery, setShowPOIDiscovery] = useState(false);

  // POI modal and hover state
  const [selectedPOIForModal, setSelectedPOIForModal] = useState<PointOfInterest | null>(null);
  const [showPOIModal, setShowPOIModal] = useState(false);
  const [hoveredPOI, setHoveredPOI] = useState<PointOfInterest | null>(null);
  const [showPOIHover, setShowPOIHover] = useState(false);
  const [hoverPosition, setHoverPosition] = useState({ x: 0, y: 0 });

  // Smart UI System - Temporarily disabled to fix panel issues
  // const smartUI = useSmartUI({
  //   selectedCities,
  //   selectedPOIs,
  //   isAnimating,
  //   isMobile
  // });

  // Note: Map initialization is now handled by ExploreMap component
  
  // Handle region change
  const handleRegionChange = (region: string) => {
    setCurrentRegion(region);
    // In a real app, we would load region-specific data here
    console.log(`Changed region to ${region}`);
  };

  // New Journey Parameter Handlers
  const handleNumberOfTravelDaysChange = useCallback((days: number) => {
    setNumberOfTravelDays(Math.max(1, Math.min(30, days)));
  }, []);

  const handleTravelPaceChange = useCallback((pace: TravelPace) => {
    setTravelPace(pace);
  }, []);

  const handleJourneyStyleChange = useCallback((style: JourneyStyle) => {
    setJourneyStyle(style);
  }, []);

  const handleTravelInterestToggle = useCallback((interest: TravelInterest) => {
    setTravelInterests(prevInterests => {
      if (prevInterests.includes(interest)) {
        return prevInterests.filter(i => i !== interest);
      }
      return [...prevInterests, interest];
    });
  }, []);

  const handleCityDayAllocationChange = useCallback((cityName: string, days: number) => {
    setCityDayAllocations(prev => ({
      ...prev,
      [cityName]: Math.max(1, Math.min(15, days)) // Allow up to 15 days per city, remove total constraint
    }));
  }, []);

  const handlePreArrangedJourneySelect = useCallback((journey: PreArrangedJourney) => {
    setSelectedPreArrangedJourney(journey);
    setNumberOfTravelDays(journey.duration);
    setTravelPace(journey.pace);
    setJourneyStyle(journey.style);

    // Auto-select cities from the journey
    const journeyCities = destinations.filter(dest =>
      journey.cities.includes(dest.name)
    );
    setSelectedCities(journeyCities);

    // Zoom to first city
    if (journeyCities.length > 0 && map.current) {
      const firstCity = journeyCities[0];
      if (firstCity.coordinates) {
        map.current.flyTo({
          center: firstCity.coordinates,
          zoom: 12,
          duration: 1500
        });
      }
    }
  }, [destinations]);

  // UI Panel Handlers
  const toggleLeftPanel = useCallback(() => {
    if (isMobile) {
      setShowMobileLeftPanel(!showMobileLeftPanel);
      setShowMobileRightPanel(false); // Close right panel when opening left
    } else {
      setIsLeftPanelCollapsed(!isLeftPanelCollapsed);
    }
  }, [isMobile, showMobileLeftPanel, isLeftPanelCollapsed]);

  const toggleRightPanel = useCallback(() => {
    if (isMobile) {
      setShowMobileRightPanel(!showMobileRightPanel);
      setShowMobileLeftPanel(false); // Close left panel when opening right
    } else {
      setIsRightPanelCollapsed(!isRightPanelCollapsed);
    }
  }, [isMobile, showMobileRightPanel, isRightPanelCollapsed]);

  // Find nearby POI during animation
  const findNearbyPOI = (currentPosition: Position, pois: PointOfInterest[]): PointOfInterest | null => {
    const DISCOVERY_RADIUS = 0.05; // Roughly 5km in degrees

    for (const poi of pois) {
      if (poi.coordinates) {
        const distance = Math.sqrt(
          Math.pow(currentPosition.lng - poi.coordinates[0], 2) +
          Math.pow(currentPosition.lat - poi.coordinates[1], 2)
        );

        if (distance <= DISCOVERY_RADIUS) {
          return poi;
        }
      }
    }
    return null;
  };

  // Handle POI discovery notification close
  const handleClosePOIDiscovery = () => {
    setShowPOIDiscovery(false);
    setDiscoveredPOI(null);
  };

  // Handle adding discovered POI to itinerary
  const handleAddDiscoveredPOI = (poi: PointOfInterest) => {
    handleAddPOIToTrip(poi);
    handleClosePOIDiscovery();
  };

  // Sequential city selection logic
  const handleSequentialCitySelection = useCallback((destination: Destination) => {
    setSelectedCities(prev => {
      const exists = prev.some(d => d.id === destination.id);

      if (exists) {
        // Remove city and adjust step
        const newDestinations = prev.filter(d => d.id !== destination.id);
        // Remove city from day allocations
        setCityDayAllocations(prevAllocations => {
          const newAllocations = { ...prevAllocations };
          delete newAllocations[destination.name];
          return newAllocations;
        });
        return newDestinations;
      } else {
        // Add city in sequence
        const newDestinations = [...prev, destination];

        // Add default day allocation for new city
        setCityDayAllocations(prevAllocations => ({
          ...prevAllocations,
          [destination.name]: 2 // Default 2 days per city
        }));

        // Zoom to the newly selected city
        if (map.current && destination.coordinates) {
          map.current.flyTo({
            center: destination.coordinates,
            zoom: 12,
            duration: 1500
          });
        }

        // Filter POIs for the newly selected city
        setSelectedDestination(destination);

        return newDestinations;
      }
    });
  }, []);

  // Handle city selection (legacy compatibility)
  const handleCitySelect = (city: Destination) => {
    handleSequentialCitySelection(city);

    // Update journey readiness
    setSelectedCities(prev => {
      const newCities = prev.some(c => c.id === city.id) ? prev : [...prev, city];
      const canBegin = newCities.length >= 2 || selectedPreArrangedJourney !== null;
      setCanBeginJourney(canBegin);
      setShowBeginJourneyButton(canBegin);
      setButtonState(canBegin ? 'default' : 'disabled');
      return newCities;
    });
  };

  // Handle city removal from itinerary panel
  const handleCityRemove = useCallback((cityToRemove: Destination) => {
    setSelectedCities(prev => {
      const newDestinations = prev.filter(d => d.id !== cityToRemove.id);

      // Remove city from day allocations
      setCityDayAllocations(prevAllocations => {
        const newAllocations = { ...prevAllocations };
        delete newAllocations[cityToRemove.name];
        return newAllocations;
      });

      // If we removed the currently selected city, select the last city or clear
      if (selectedDestination?.id === cityToRemove.id) {
        const lastCity = newDestinations[newDestinations.length - 1];
        setSelectedDestination(lastCity || null);

        // Zoom to last city or reset view
        if (lastCity && map.current && lastCity.coordinates) {
          map.current.flyTo({
            center: lastCity.coordinates,
            zoom: 12,
            duration: 1000
          });
        } else if (map.current) {
          map.current.flyTo({
            center: MOROCCO_CENTER,
            zoom: 6,
            duration: 1000
          });
        }
      }

      return newDestinations;
    });
  }, [selectedDestination]);

  // Handle city deselection (legacy compatibility)
  const handleCityDeselect = (city: Destination) => {
    handleCityRemove(city);
  };

  // Handle city overlay toggle
  const handleToggleCityOverlay = () => {
    setShowCityOverlay(!showCityOverlay);
  };
  
  // Start the journey animation using ExploreMap
  const handleStartJourney = () => {
    console.log('🚀 Begin Journey clicked! Selected cities:', selectedCities.length, 'Selected POIs:', selectedPOIs.length);

    if (selectedCities.length < 2) {
      console.warn('Cannot start journey: Need at least 2 cities selected');
      return;
    }

    // Update itinerary with selected POIs
    setItineraryPOIs(selectedPOIs);

    // Use ExploreMap's Begin Journey functionality
    if (exploreMapRef.current) {
      console.log('Starting journey via ExploreMap...');
      exploreMapRef.current.beginJourney();
      setJourneyPhase(JourneyPhase.ANIMATING);
      setShowPOIOverlay(false);
    } else {
      console.error('ExploreMap ref not available to begin journey.');
      // Fallback to basic animation state
      setIsAnimating(true);
      setJourneyPhase(JourneyPhase.ANIMATING);
      setShowPOIOverlay(false);
    }

    // Log start of journey
    console.log('Starting journey with cities:', selectedCities.map(city => city.name));
    console.log('Starting journey with POIs:', selectedPOIs.map(poi => poi.name));
  };
  
  // Pause/resume the journey animation
  const handlePauseJourney = () => {
    setIsPaused(!isPaused);
  };
  
  // Update journey progress during animation
  const handleProgressUpdate = (progress: number, position: Position) => {
    setJourneyProgress(progress);
    
    // In a real implementation, we would update the vehicle position
    // and handle other animation updates here
  };
  
  // Close POI overlay
  const handleClosePOIOverlay = () => {
    setShowPOIOverlay(false);
    
    // Reset map view
    if (map.current) {
      map.current.flyTo({
        center: MOROCCO_CENTER,
        zoom: 5,
        essential: true,
        duration: 1000
      });
    }
  };
  
  // Handle POI click from left pane - zoom and show small info box
  const handleSelectPOI = (poi: PointOfInterest) => {
    // Ensure the POI has all required fields
    const normalizedPoi: PointOfInterest = {
      ...poi,
      category: poi.category || 'other' as POICategory,
      images: poi.images || [],
      tags: poi.tags || []
    };

    // Fly to POI location on map
    if (map.current && poi.coordinates) {
      map.current.flyTo({
        center: poi.coordinates,
        zoom: 15,
        duration: 1500,
        essential: true
      });

      // Show small hover card/info box after zoom completes
      setTimeout(() => {
        setHoveredPOI(normalizedPoi);
        // Position the hover card in the center of the screen
        const centerX = window.innerWidth / 2;
        const centerY = window.innerHeight / 2;
        setHoverPosition({ x: centerX, y: centerY });
        setShowPOIHover(true);
        setShowPOIModal(false); // Ensure modal is closed
      }, 1600); // Slightly after zoom duration
    }
  };

  // Handle adding POI to trip (from left pane "Add" button or modal)
  const handleAddPOIToTrip = (poi: PointOfInterest) => {
    const normalizedPoi: PointOfInterest = {
      ...poi,
      category: poi.category || 'other' as POICategory,
      images: poi.images || [],
      tags: poi.tags || []
    };

    setSelectedPOIs(current => {
      // Check if POI is already selected
      const isSelected = current.some(p => p.id === normalizedPoi.id);

      // Toggle selection
      if (isSelected) {
        return current.filter(p => p.id !== normalizedPoi.id);
      } else {
        return [...current, normalizedPoi];
      }
    });
  };

  // Handle POI hover from map markers
  const handlePOIMarkerHover = (poi: PointOfInterest, event: MouseEvent) => {
    setHoveredPOI(poi);
    setHoverPosition({ x: event.clientX + 10, y: event.clientY - 10 });
    setShowPOIHover(true);
  };

  // Handle POI hover end
  const handlePOIMarkerHoverEnd = () => {
    setShowPOIHover(false);
    setHoveredPOI(null);
  };

  // Handle adding POI from modal
  const handleAddPOIFromModal = (poi: PointOfInterest) => {
    handleAddPOIToTrip(poi);
    setShowPOIModal(false);
  };

  // Handle POI booking
  const handlePOIBooking = (poi: PointOfInterest) => {
    console.log('Booking POI:', poi.name);
    // Implement booking logic here
  };

  // Handle POI click from map markers - open big modal directly
  const handlePOIMarkerClick = (poi: PointOfInterest) => {
    setSelectedPOIForModal(poi);
    setShowPOIModal(true);
    setShowPOIHover(false); // Hide hover card when modal opens
  };

  // Handle "View Details" click from small hover card - open big modal
  const handleViewPOIDetails = (poi: PointOfInterest) => {
    setSelectedPOIForModal(poi);
    setShowPOIModal(true);
    setShowPOIHover(false); // Hide hover card when modal opens
  };
  
  // Render the component
  return (
    <div className="moroccan-demo h-screen flex flex-col">
      {/* Top Bar */}
      <TopBar
        currentRegion={currentRegion}
        availableRegions={AVAILABLE_REGIONS}
        onRegionChange={handleRegionChange}
        isAnimating={isAnimating}
        onToggleTheme={() => {}}
        onStartJourney={canBeginJourney ? handleStartJourney : undefined}
        onPauseJourney={handlePauseJourney}
        journeyProgress={journeyProgress}

        canBeginJourney={canBeginJourney}
        showBeginJourneyButton={showBeginJourneyButton}
        onBeginJourney={canBeginJourney ? handleStartJourney : undefined}
        numberOfDays={numberOfTravelDays}
        travelPace={travelPace}
        journeyStyle={journeyStyle}
        onNumberOfDaysChange={handleNumberOfTravelDaysChange}
        onTravelPaceChange={handleTravelPaceChange}
        onJourneyStyleChange={handleJourneyStyleChange}
        selectedCities={selectedCities}
        selectedPOIs={selectedPOIs}
        isMobile={isMobile}
        onToggleLeftPanel={toggleLeftPanel}
        onToggleRightPanel={toggleRightPanel}
      />

      {/* Main Content Area */}
      <div className="flex flex-1 relative" style={{ marginTop: '70px' }}>
        {/* Mobile Overlay for Left Panel */}
        {isMobile && showMobileLeftPanel && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-20"
            onClick={() => setShowMobileLeftPanel(false)}
          />
        )}

        {/* Left Panel */}
        <div
          className={cn(
            "h-full transition-all duration-300 ease-in-out",
            isMobile ? (
              showMobileLeftPanel
                ? "fixed left-0 top-0 w-4/5 sm:w-3/5 md:w-80 opacity-100 pointer-events-auto z-30 transform translate-x-0"
                : "fixed left-0 top-0 w-4/5 sm:w-3/5 md:w-80 opacity-0 pointer-events-none z-30 transform -translate-x-full"
            ) : (
              isLeftPanelCollapsed
                ? "relative w-0 opacity-0 pointer-events-none z-20"
                : "relative w-80 opacity-100 z-20"
            )
          )}
          style={isMobile ? { height: '100vh', marginTop: '70px' } : {}}
        >
          {!isMobile && (
            <button
              onClick={toggleLeftPanel}
              className="absolute -right-6 top-1/2 -translate-y-1/2 bg-white p-1 rounded-r shadow-lg z-50 border border-gray-200"
              style={{ color: 'var(--morocco-blue)' }}
            >
              {isLeftPanelCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
            </button>
          )}
          <LeftPane
            isAnimating={isAnimating}
            onTogglePane={toggleLeftPanel}
            selectedPOIs={selectedPOIs}
            canStartJourney={canBeginJourney}
            minPoisRequired={2}
            allPois={MOROCCO_POIS}
            onSelectPOI={handleSelectPOI}
            onAddPOI={handleAddPOIToTrip}
            selectedInterests={travelInterests}
            onInterestToggle={handleTravelInterestToggle}
            isMobile={isMobile}
            availableCities={destinations}
            selectedCities={selectedCities}
            onCitySelect={handleCitySelect}
            onCityDeselect={handleCityDeselect}
            cityDayAllocations={cityDayAllocations}
            onCityDayAllocationChange={handleCityDayAllocationChange}
          />
        </div>

        {/* Map Container */}
        <div className="flex-1 relative">
          <div id="explore-map-container" className="w-full h-full">
            <ExploreMap
            ref={exploreMapRef}
            citiesForCountry={destinations}
            poisForCurrentSelection={MOROCCO_POIS}
            selectedDestinations={selectedCities}
            selectedPOIs={selectedPOIs}
            onDestinationSelect={handleCitySelect}
            onPOISelect={handleSelectPOI}
            onPOIDeselect={handleSelectPOI}
            mapCenter={MOROCCO_CENTER}
            mapZoom={6}
            maxBounds={MOROCCO_BOUNDS}
            onCitySelect={handleCitySelect}
            selectedCity={selectedDestination}
            isMobile={isMobile}
            onMapReady={(mapInstance) => {
              map.current = mapInstance;
              console.log('Map ready via ExploreMap');
            }}
            onAnimationStart={() => {
              console.log('🚀 Animation started - hiding UI elements');
              setIsAnimating(true);
              setJourneyPhase(JourneyPhase.ANIMATING);
              setShowPOIOverlay(false);
              setShowCityOverlay(false);

              // Hide panels during animation
              setIsLeftPanelCollapsed(true);
              setIsRightPanelCollapsed(true);
              setShowMobileLeftPanel(false);
              setShowMobileRightPanel(false);

              // Trigger map resize after panels hide
              setTimeout(() => {
                if (map.current) {
                  map.current.resize();
                  console.log('🗺️ Map resized after panel collapse');
                }
              }, 350); // Wait for transition to complete
            }}
            onAnimationComplete={() => {
              console.log('🏁 Animation completed - restoring UI elements');
              setIsAnimating(false);
              setJourneyPhase(JourneyPhase.IDLE);

              // Restore panels after animation
              setIsLeftPanelCollapsed(false);
              setIsRightPanelCollapsed(false);
            }}
            />
          </div>

          {/* City Selection Swatches - Hidden on Mobile (moved to left pane) */}
          {!isMobile && !isAnimating && (
            <CitySelectionOverlay
              availableCities={destinations}
              selectedCities={selectedCities}
              onCitySelect={handleCitySelect}
              onCityDeselect={handleCityDeselect}
              currentRegion={currentRegion}
              numberOfTravelDays={Object.values(cityDayAllocations).reduce((sum, days) => sum + days, 0) || Math.ceil(selectedCities.length * 2)}
              onNumberOfTravelDaysChange={() => {}} // No longer needed - calculated from city allocations
              travelStyle={travelPace}
              onTravelStyleChange={handleTravelPaceChange}
              journeyStyle={journeyStyle}
              onJourneyStyleChange={handleJourneyStyleChange}
              cityDayAllocations={cityDayAllocations}
              onCityDayAllocationChange={handleCityDayAllocationChange}
            />
          )}

          {/* POI Hover Card */}
          <POIHoverCard
            poi={hoveredPOI}
            isVisible={showPOIHover}
            position={hoverPosition}
            onClose={handlePOIMarkerHoverEnd}
            onViewDetails={handleViewPOIDetails}
          />

          {/* POI Modal */}
          <POIModal
            poi={selectedPOIForModal}
            isVisible={showPOIModal}
            onClose={() => setShowPOIModal(false)}
            onAddToItinerary={handleAddPOIFromModal}
            onBooking={handlePOIBooking}
            isInItinerary={selectedPOIForModal ? selectedPOIs.some(p => p.id === selectedPOIForModal.id) : false}
          />

          {/* Pre-arranged Journey Links */}
          {!isAnimating && (
            <PreArrangedJourneyLinks
              journeys={moroccoPreArrangedJourneys}
              onJourneySelect={handlePreArrangedJourneySelect}
              isMobile={isMobile}
            />
          )}

          {/* Mobile Toggle Buttons */}
          {isMobile && (
            <>
              {/* Left Panel Toggle Button - POI Explorer */}
              <button
                onClick={toggleLeftPanel}
                className="fixed bottom-6 left-6 w-14 h-14 rounded-full shadow-xl z-40 transition-all duration-300 flex items-center justify-center border-2"
                style={{
                  backgroundColor: showMobileLeftPanel ? 'var(--morocco-blue)' : 'white',
                  borderColor: 'var(--morocco-blue)',
                  color: showMobileLeftPanel ? 'white' : 'var(--morocco-blue)',
                  boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)',
                  transform: showMobileLeftPanel ? 'scale(1.1)' : 'scale(1)'
                }}
              >
                <MapPin className="h-6 w-6" />
              </button>

              {/* Right Panel Toggle Button - Trip Planner */}
              <button
                onClick={toggleRightPanel}
                className="fixed bottom-6 right-6 w-14 h-14 rounded-full shadow-xl z-40 transition-all duration-300 flex items-center justify-center border-2"
                style={{
                  backgroundColor: showMobileRightPanel ? 'var(--morocco-red)' : 'white',
                  borderColor: 'var(--morocco-red)',
                  color: showMobileRightPanel ? 'white' : 'var(--morocco-red)',
                  boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)',
                  transform: showMobileRightPanel ? 'scale(1.1)' : 'scale(1)'
                }}
              >
                <Calendar className="h-6 w-6" />
              </button>

              {/* Mobile Button Labels */}
              {!showMobileLeftPanel && !showMobileRightPanel && (
                <>
                  <div
                    className="fixed bottom-20 left-6 text-xs text-center z-30 pointer-events-none"
                    style={{
                      color: 'var(--morocco-blue)',
                      fontWeight: 600,
                      fontFamily: 'var(--font-body)',
                      textShadow: '0 1px 3px rgba(255, 255, 255, 0.8)'
                    }}
                  >
                    Explore
                  </div>
                  <div
                    className="fixed bottom-20 right-6 text-xs text-center z-30 pointer-events-none"
                    style={{
                      color: 'var(--morocco-red)',
                      fontWeight: 600,
                      fontFamily: 'var(--font-body)',
                      textShadow: '0 1px 3px rgba(255, 255, 255, 0.8)'
                    }}
                  >
                    Plan
                  </div>
                </>
              )}
            </>
          )}
        </div>

        {/* Mobile Overlay for Right Panel */}
        {isMobile && showMobileRightPanel && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-20"
            onClick={() => setShowMobileRightPanel(false)}
          />
        )}

        {/* Right Panel - Enhanced with New Components */}
        <div
          className={cn(
            "h-full transition-all duration-300 ease-in-out",
            isMobile ? (
              showMobileRightPanel
                ? "fixed right-0 top-0 w-4/5 sm:w-3/5 md:w-80 opacity-100 pointer-events-auto z-30 transform translate-x-0"
                : "fixed right-0 top-0 w-4/5 sm:w-3/5 md:w-80 opacity-0 pointer-events-none z-30 transform translate-x-full"
            ) : (
              isRightPanelCollapsed
                ? "relative w-0 opacity-0 pointer-events-none z-20"
                : "relative opacity-100 z-20"
            )
          )}
          style={isMobile ? { height: '100vh', marginTop: '70px' } : (isRightPanelCollapsed ? {} : { width: '480px' })}
        >
          {!isMobile && (
            <button
              onClick={toggleRightPanel}
              className="absolute -left-6 top-1/2 -translate-y-1/2 bg-white p-1 rounded-l shadow-lg z-50 border border-gray-200"
              style={{ color: 'var(--morocco-blue)' }}
            >
              {isRightPanelCollapsed ? <ChevronLeft className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </button>
          )}
          <RightPane
            isAnimating={isAnimating}
            isPaused={isPaused}
            onTogglePane={toggleRightPanel}
            journeyProgress={journeyProgress}
            selectedCities={selectedCities}
            itineraryPOIs={selectedPOIs}
            tripDuration={Object.values(cityDayAllocations).reduce((sum, days) => sum + days, 0) || Math.ceil(selectedCities.length * 2)}
            onTripDurationChange={() => {}} // No longer needed - calculated from city allocations
            onRemovePOI={handleAddPOIToTrip}
            onReorderPOI={(fromIndex, toIndex) => {
              console.log(`Reordering POI from ${fromIndex} to ${toIndex}`);
              // Implement POI reordering logic here
            }}
            onRemoveCity={handleCityRemove}
            onExportItinerary={() => {
              console.log('Exporting itinerary...');
              // Implement export functionality here
            }}
            cityDayAllocations={cityDayAllocations}
            onCityDayAllocationChange={handleCityDayAllocationChange}
            // AI props
            enableAI={true}
            userInterests={travelInterests.map(interest => interest.name)}
            travelStyle="cultural"
            availablePOIs={MOROCCO_POIS}
            onAICitiesSelected={(cities) => {
              cities.forEach(city => {
                const mappedCity = destinations.find(dest => dest.id === city.id || dest.name === city.name);
                if (mappedCity) {
                  handleCitySelect(mappedCity);
                }
              });
            }}
            onAIPOIsSelected={(pois) => {
              pois.forEach(poi => {
                const mappedPOI = MOROCCO_POIS.find(p => p.id === poi.id || p.name === poi.name);
                if (mappedPOI) {
                  handleAddPOIToTrip(mappedPOI);
                }
              });
            }}
            onAIItineraryGenerated={(itinerary) => {
              console.log('AI Generated Itinerary:', itinerary);
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default MoroccanDemo;