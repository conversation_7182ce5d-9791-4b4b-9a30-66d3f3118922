/**
 * Neutral Demo Page
 * 
 * Clean, professional demo showcasing the framework without theme-specific branding
 * This will be the main demo page for client presentations
 */

import React, { useState, useEffect, useRef } from 'react';
import { ClientProvider } from '../contexts/ClientContext';
import { ThemeProvider } from '../providers/ThemeProvider';
import NeutralLayout from '../components/layout/NeutralLayout';
import MapComponent from '../components/map/MapComponent';
import { Destination, PointOfInterest } from '../types';

// Sample neutral data for demo (not region-specific)
const DEMO_DESTINATIONS: Destination[] = [
  {
    id: 'demo-city-1',
    name: 'Metropolitan City',
    description: 'A vibrant urban center with rich culture and history',
    position: [-74.006, 40.7128], // New York coordinates as example
    image: '/assets/neutral/cities/metro-city.jpg',
    type: 'city'
  },
  {
    id: 'demo-city-2',
    name: 'Coastal Town',
    description: 'Charming seaside destination with beautiful beaches',
    position: [-118.2437, 34.0522], // Los Angeles coordinates as example
    image: '/assets/neutral/cities/coastal-town.jpg',
    type: 'city'
  },
  {
    id: 'demo-city-3',
    name: 'Mountain Village',
    description: 'Scenic mountain retreat with outdoor adventures',
    position: [-105.2705, 40.0150], // Boulder coordinates as example
    image: '/assets/neutral/cities/mountain-village.jpg',
    type: 'city'
  }
];

const DEMO_POIS: PointOfInterest[] = [
  {
    id: 'demo-poi-1',
    name: 'Historic Landmark',
    description: 'Iconic historical site with guided tours',
    position: [-74.0445, 40.6892],
    type: 'landmark',
    image: '/assets/neutral/pois/historic-landmark.jpg',
    rating: 4.5,
    tags: ['historic', 'cultural', 'guided-tours'],
    categories: ['landmark', 'cultural'],
    duration: 2,
    cost: 25,
    location: 'Metropolitan City'
  },
  {
    id: 'demo-poi-2',
    name: 'Art Museum',
    description: 'World-class art collection and exhibitions',
    position: [-74.0060, 40.7794],
    type: 'activity',
    image: '/assets/neutral/pois/art-museum.jpg',
    rating: 4.7,
    tags: ['art', 'culture', 'indoor'],
    categories: ['activity', 'cultural'],
    duration: 3,
    cost: 30,
    location: 'Metropolitan City'
  },
  {
    id: 'demo-poi-3',
    name: 'Scenic Viewpoint',
    description: 'Breathtaking panoramic views of the landscape',
    position: [-118.3000, 34.1000],
    type: 'nature',
    image: '/assets/neutral/pois/scenic-viewpoint.jpg',
    rating: 4.8,
    tags: ['scenic', 'photography', 'outdoor'],
    categories: ['nature', 'scenic'],
    duration: 1,
    cost: 0,
    location: 'Coastal Town'
  },
  {
    id: 'demo-poi-4',
    name: 'Adventure Park',
    description: 'Outdoor activities and adventure sports',
    position: [-105.2500, 40.0000],
    type: 'activity',
    image: '/assets/neutral/pois/adventure-park.jpg',
    rating: 4.6,
    tags: ['adventure', 'outdoor', 'sports'],
    categories: ['activity', 'adventure'],
    duration: 4,
    cost: 75,
    location: 'Mountain Village'
  }
];

const NeutralDemo: React.FC = () => {
  // State management
  const [selectedCities, setSelectedCities] = useState<Destination[]>([]);
  const [selectedPOIs, setSelectedPOIs] = useState<PointOfInterest[]>([]);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [journeyProgress, setJourneyProgress] = useState(0);
  
  // Map reference
  const mapRef = useRef<any>(null);
  
  // Handle city selection
  const handleCitySelect = (city: Destination) => {
    if (!selectedCities.find(c => c.id === city.id)) {
      setSelectedCities(prev => [...prev, city]);
    }
  };
  
  // Handle city deselection
  const handleCityDeselect = (city: Destination) => {
    setSelectedCities(prev => prev.filter(c => c.id !== city.id));
  };
  
  // Handle POI selection
  const handlePOISelect = (poi: PointOfInterest) => {
    console.log('POI selected:', poi.name);
  };
  
  // Handle adding POI to itinerary
  const handleAddPOI = (poi: PointOfInterest) => {
    if (!selectedPOIs.find(p => p.id === poi.id)) {
      setSelectedPOIs(prev => [...prev, poi]);
    }
  };
  
  // Handle journey start
  const handleStartJourney = () => {
    setIsAnimating(true);
    setIsPaused(false);
    // Journey animation logic would go here
  };
  
  // Handle journey pause
  const handlePauseJourney = () => {
    setIsPaused(!isPaused);
  };
  
  // Handle region change (for demo purposes)
  const handleRegionChange = (region: string) => {
    console.log('Region changed to:', region);
  };
  
  return (
    <ClientProvider initialClientId="neutral">
      <ThemeProvider>
        <div className="neutral-demo h-screen">
          <NeutralLayout
            currentRegion="Global Framework"
            availableRegions={['Global Framework']}
            onRegionChange={handleRegionChange}
            isAnimating={isAnimating}
            isPaused={isPaused}
            journeyProgress={journeyProgress}
            onStartJourney={handleStartJourney}
            onPauseJourney={handlePauseJourney}
            itineraryPOIs={selectedPOIs}
            availableCities={DEMO_DESTINATIONS}
            selectedCities={selectedCities}
            onCitySelect={handleCitySelect}
            onCityDeselect={handleCityDeselect}
            allPOIs={DEMO_POIS}
            onSelectPOI={handlePOISelect}
            onAddPOI={handleAddPOI}
            canBeginJourney={selectedPOIs.length >= 2}
            showBeginJourneyButton={selectedPOIs.length >= 2}
            onBeginJourney={handleStartJourney}
          >
            <MapComponent
              ref={mapRef}
              destinations={DEMO_DESTINATIONS}
              pointsOfInterest={DEMO_POIS}
              selectedCities={selectedCities}
              selectedPOIs={selectedPOIs}
              onCitySelect={handleCitySelect}
              onPOISelect={handlePOISelect}
              isAnimating={isAnimating}
              journeyProgress={journeyProgress}
            />
          </NeutralLayout>
        </div>
      </ThemeProvider>
    </ClientProvider>
  );
};

export default NeutralDemo;
