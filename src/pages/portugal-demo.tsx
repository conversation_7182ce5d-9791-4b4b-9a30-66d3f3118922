import React, { useState, useEffect, useRef, useCallback } from 'react';
import mapboxgl from 'mapbox-gl';
import ExploreMap from '../components/map/ExploreMap';
import POIModal from '../components/map/POIModal';
import POIHoverCard from '../components/map/POIHoverCard';
import ItineraryPanel from '../components/itinerary/ItineraryPanel';
import LeftPane from '../components/layout/LeftPane';
import RightPane from '../components/layout/RightPane';
import TopBar from '../components/layout/TopBar';
import '../styles/index-new.css';
import 'mapbox-gl/dist/mapbox-gl.css';
import { Destination, PointOfInterest, POICategory } from '@/types';
import { Position, PositionTuple, toPositionObject, PositionObject } from '../types/Position';
import { JourneyPhase } from '../components/map/animation/types/travelAnimatorTypes';
import { ButtonState } from '../types/TravelAnimatorTypes';
import { TravelPace, JourneyStyle, TravelInterest, PreArrangedJourney } from '../types/ItineraryParameters';
import { regionData } from '@/data/destinations';
import { pointsOfInterest as importedAllPointsOfInterest } from '@/data/pointsOfInterest';
import { convertPOIToPointOfInterest, filterPOIsForCity, normalizeDestination } from '../utils/poiUtils';
import { ChevronLeft, ChevronRight, Calendar, MapPin, Sun, Info, Star, Activity } from 'lucide-react';
import CitySelectionOverlay from '../components/map/CitySelectionOverlay';
import { cn } from '@/lib/utils';
import PreArrangedJourneyLinks from '../components/map/PreArrangedJourneyLinks';
import { useSmartUI, UIMode } from '../hooks/useSmartUI';

// Portugal is centered at approximately these coordinates
const PORTUGAL_CENTER: [number, number] = [-8.2245, 39.3999];
const PORTUGAL_BOUNDS: [[number, number], [number, number]] = [
  [-10.0, 36.0], // Southwest coordinates
  [-6.0, 43.0]   // Northeast coordinates
];

// Available regions for selection (Portugal demo)
const AVAILABLE_REGIONS = ['Portugal', 'Spain', 'Morocco'];

// Get Portugal data from the existing data source
const getPortugalData = () => {
  const portugalRegionData = regionData['portugal'];
  if (!portugalRegionData) {
    console.error('Portugal region data not found');
    return { destinations: [], pointsOfInterest: [] };
  }

  return {
    destinations: portugalRegionData.destinations || [],
    pointsOfInterest: portugalRegionData.pointsOfInterest || []
  };
};

// Portugal pre-arranged journeys
const portugalPreArrangedJourneys: PreArrangedJourney[] = [
  {
    id: 'portugal-wine-route',
    title: 'Portuguese Wine Route',
    description: 'Discover Portugal\'s finest wine regions from Porto to Douro Valley',
    duration: '7 days',
    destinations: ['Porto', 'Douro Valley', 'Coimbra'],
    highlights: ['Port wine cellars', 'Terraced vineyards', 'Historic university'],
    difficulty: 'Easy',
    season: 'Spring-Fall',
    image: 'https://images.unsplash.com/photo-1596713102803-d0a67f8e2ec2?q=80&w=1000'
  },
  {
    id: 'portugal-coastal-adventure',
    title: 'Coastal Portugal Adventure',
    description: 'From Lisbon\'s charm to Algarve\'s stunning beaches',
    duration: '10 days',
    destinations: ['Lisbon', 'Sintra', 'Cascais', 'Algarve'],
    highlights: ['Pena Palace', 'Benagil Cave', 'Coastal cliffs'],
    difficulty: 'Moderate',
    season: 'Year-round',
    image: 'https://images.unsplash.com/photo-1596627118141-9768b23d121a?q=80&w=1000'
  },
  {
    id: 'portugal-cultural-heritage',
    title: 'Cultural Heritage Tour',
    description: 'Explore Portugal\'s rich history and UNESCO sites',
    duration: '8 days',
    destinations: ['Lisbon', 'Óbidos', 'Coimbra', 'Braga'],
    highlights: ['Belém Tower', 'Medieval walls', 'Ancient university'],
    difficulty: 'Easy',
    season: 'Year-round',
    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?q=80&w=1000'
  }
];

const PortugalDemo: React.FC = () => {
  // Get Portugal data
  const { destinations: portugalDestinations, pointsOfInterest: portugalPOIs } = getPortugalData();

  // State management
  const [selectedDestinations, setSelectedDestinations] = useState<Destination[]>([]);
  const [selectedPOIs, setSelectedPOIs] = useState<PointOfInterest[]>([]);
  const [hoveredPOI, setHoveredPOI] = useState<PointOfInterest | null>(null);
  const [selectedPOI, setSelectedPOI] = useState<PointOfInterest | null>(null);
  const [isLeftPanelCollapsed, setIsLeftPanelCollapsed] = useState(false);
  const [isRightPanelCollapsed, setIsRightPanelCollapsed] = useState(false);
  const [currentJourneyPhase, setCurrentJourneyPhase] = useState<JourneyPhase>('planning');
  const [selectedCity, setSelectedCity] = useState<Destination | null>(null);
  const [showCityPOIs, setShowCityPOIs] = useState(false);
  const [currentAreaPOIs, setCurrentAreaPOIs] = useState<PointOfInterest[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<POICategory[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  // Smart UI hook for responsive behavior
  const { uiMode, isMobile, isTablet } = useSmartUI();

  // Journey parameters
  const [journeyParams, setJourneyParams] = useState({
    pace: 'moderate' as TravelPace,
    style: 'cultural' as JourneyStyle,
    interests: ['culture', 'wine', 'nature'] as TravelInterest[],
    duration: 7
  });

  // Handlers
  const handleDestinationSelect = useCallback((destination: Destination) => {
    setSelectedDestinations(prev => {
      const isAlreadySelected = prev.some(d => d.id === destination.id);
      if (isAlreadySelected) {
        return prev.filter(d => d.id !== destination.id);
      } else {
        return [...prev, destination];
      }
    });
  }, []);

  const handlePOISelect = useCallback((poi: PointOfInterest) => {
    setSelectedPOI(poi);
  }, []);

  const handlePOIToggle = useCallback((poi: PointOfInterest) => {
    setSelectedPOIs(prev => {
      const isAlreadySelected = prev.some(p => p.id === poi.id);
      if (isAlreadySelected) {
        return prev.filter(p => p.id !== poi.id);
      } else {
        return [...prev, poi];
      }
    });
  }, []);

  const handleCitySelect = useCallback((city: Destination) => {
    setSelectedCity(city);
    const cityPOIs = filterPOIsForCity(portugalPOIs, city.name);
    setCurrentAreaPOIs(cityPOIs);
    setShowCityPOIs(true);
  }, [portugalPOIs]);

  const handleShowPOIOverlay = useCallback((destination: Destination) => {
    handleCitySelect(destination);
  }, [handleCitySelect]);

  // Filter POIs based on search and categories
  const filteredPOIs = portugalPOIs.filter(poi => {
    const matchesSearch = !searchQuery || 
      poi.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      poi.description?.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = selectedCategories.length === 0 || 
      selectedCategories.some(cat => poi.type === cat || poi.category === cat);
    
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="h-screen flex flex-col bg-white overflow-hidden">
      {/* Top Bar */}
      <TopBar 
        title="Explore Portugal"
        subtitle="Discover the Soul of Portugal"
        theme="portugal"
        onToggleLeftPanel={() => setIsLeftPanelCollapsed(!isLeftPanelCollapsed)}
        onToggleRightPanel={() => setIsRightPanelCollapsed(!isRightPanelCollapsed)}
        isLeftPanelCollapsed={isLeftPanelCollapsed}
        isRightPanelCollapsed={isRightPanelCollapsed}
        selectedDestinations={selectedDestinations}
        selectedPOIs={selectedPOIs}
        isMobile={isMobile}
      />

      <div className="flex-1 flex overflow-hidden">
        {/* Left Panel */}
        {!isLeftPanelCollapsed && (
          <LeftPane
            destinations={portugalDestinations}
            selectedDestinations={selectedDestinations}
            onDestinationSelect={handleDestinationSelect}
            pois={showCityPOIs ? currentAreaPOIs : filteredPOIs}
            selectedPOIs={selectedPOIs}
            onPOISelect={handlePOIToggle}
            selectedCategories={selectedCategories}
            onCategoryChange={setSelectedCategories}
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            currentCity={selectedCity}
            onBackToDestinations={() => {
              setShowCityPOIs(false);
              setSelectedCity(null);
              setCurrentAreaPOIs([]);
            }}
            theme="portugal"
            isMobile={isMobile}
            isCollapsed={isLeftPanelCollapsed}
            onToggleCollapse={() => setIsLeftPanelCollapsed(!isLeftPanelCollapsed)}
          />
        )}

        {/* Main Map Area */}
        <div className="flex-1 relative">
          <ExploreMap
            citiesForCountry={portugalDestinations}
            poisForCurrentSelection={filteredPOIs}
            selectedDestinations={selectedDestinations}
            selectedPOIs={selectedPOIs}
            onDestinationSelect={handleDestinationSelect}
            onPOISelect={handlePOISelect}
            onPOIDeselect={(poi) => {
              setSelectedPOIs(prev => prev.filter(p => p.id !== poi.id));
            }}
            mapCenter={PORTUGAL_CENTER}
            mapZoom={6}
            onCitySelect={handleCitySelect}
            selectedCity={selectedCity}
            isMobile={isMobile}
            showLeftPanelCommand={isLeftPanelCollapsed}
            onCloseLeftPanel={() => setIsLeftPanelCollapsed(false)}
            isDarkValue={false}
            isLeftPanelCollapsed={isLeftPanelCollapsed}
            toggleLeftPanelCollapse={() => setIsLeftPanelCollapsed(!isLeftPanelCollapsed)}
            onReplaceDestinations={setSelectedDestinations}
            onShowPOIOverlay={handleShowPOIOverlay}
          />

          {/* Pre-arranged Journey Links */}
          <PreArrangedJourneyLinks
            journeys={portugalPreArrangedJourneys}
            onJourneySelect={(journey) => {
              // Auto-select destinations for the journey
              const journeyDestinations = portugalDestinations.filter(dest =>
                journey.destinations.includes(dest.name)
              );
              setSelectedDestinations(journeyDestinations);
            }}
            theme="portugal"
          />
        </div>

        {/* Right Panel */}
        {!isRightPanelCollapsed && (
          <RightPane
            selectedDestinations={selectedDestinations}
            selectedPOIs={selectedPOIs}
            journeyParams={journeyParams}
            onJourneyParamsChange={setJourneyParams}
            currentPhase={currentJourneyPhase}
            onPhaseChange={setCurrentJourneyPhase}
            theme="portugal"
            isMobile={isMobile}
            isCollapsed={isRightPanelCollapsed}
            onToggleCollapse={() => setIsRightPanelCollapsed(!isRightPanelCollapsed)}
            availablePOIs={portugalPOIs}
            onPOISelect={handlePOIToggle}
          />
        )}
      </div>

      {/* POI Modal */}
      {selectedPOI && (
        <POIModal
          poi={selectedPOI}
          isOpen={!!selectedPOI}
          onClose={() => setSelectedPOI(null)}
          onAddToItinerary={() => {
            handlePOIToggle(selectedPOI);
            setSelectedPOI(null);
          }}
          isInItinerary={selectedPOIs.some(p => p.id === selectedPOI.id)}
          theme="portugal"
        />
      )}

      {/* POI Hover Card */}
      {hoveredPOI && (
        <POIHoverCard
          poi={hoveredPOI}
          onClose={() => setHoveredPOI(null)}
          theme="portugal"
        />
      )}
    </div>
  );
};

export default PortugalDemo;
