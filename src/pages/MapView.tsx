import React, { useState, useEffect } from 'react';
import { regionData } from '@/data/destinations';
import { portugalPointsOfInterest } from '@/data/pointsOfInterest';
import { portugalDestinations } from '@/data/destinations';
import ExploreMap from '@/components/map/ExploreMap';
import { Button } from '@/components/ui/button';
import { Search, Filter, ArrowLeft, Plus } from 'lucide-react';
import { Link } from 'react-router-dom';
import { ProgressIndicator, CompactProgressIndicator } from '@/components/ui/progress-indicator';
import { DouroValleyWineOverlay } from '../../docs/examples/ThemeableDiscoveryOverlay-Portugal-DouroValley';
import { onPOIDiscoveryFrameworkEvent, POIDiscoveryFrameworkEvent, POI } from '../components/map/animation/POIDiscoveryFrameworkEvents';
import { Destination, PointOfInterest, normalizeDestination, normalizePOI } from '../types';
import LeftPane from '../components/layout/LeftPane';
import RightPane from '../components/layout/RightPane';

// Set the region here ('portugal' or 'morocco'). In the future, make this dynamic via context or user setting.
const region = 'portugal';

// Steps in the trip planning process
const tripSteps = [
  "Select Towns", 
  "Choose Wineries & Activities", 
  "Select Vehicle", 
  "Review & Submit"
];

console.log('⭐⭐⭐ MapView component loaded - THIS IS WHERE THE NEW UI SHOULD BE VISIBLE ⭐⭐⭐');

const MapView = () => {
  // Use centralized region data
  const destinations = regionData[region].destinations;
  const pointsOfInterest = regionData[region].pointsOfInterest;

  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const [selectedPOIs, setSelectedPOIs] = useState<PointOfInterest[]>([]);
  const [selectedDestinations, setSelectedDestinations] = useState<Destination[]>([]);
  const [routePOIs, setRoutePOIs] = useState<PointOfInterest[]>(pointsOfInterest);
  const [currentStep, setCurrentStep] = useState<number>(1);
  const [isMobile, setIsMobile] = useState<boolean>(false);
  const [event, setEvent] = useState<POIDiscoveryFrameworkEvent | null>(null);

  // Check if we're on mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // Listen for POI/city discovery events (Portugal theme)
  useEffect(() => {
    const unsubscribe = onPOIDiscoveryFrameworkEvent((evt) => {
      setEvent(evt);
    });
    return () => unsubscribe();
  }, []);

  // Toggle destination selection
  const toggleDestination = (destination: Destination) => {
    if (selectedDestinations.some(d => d.id === destination.id)) {
      setSelectedDestinations(selectedDestinations.filter(d => d.id !== destination.id));
    } else {
      setSelectedDestinations([...selectedDestinations, destination]);
      console.log(`Destination added: ${destination.name}`);
    }
  };

  // Add POI to itinerary
  const addToItinerary = (poi: PointOfInterest) => {
    if (!selectedPOIs.some(p => p.id === poi.id)) {
      setSelectedPOIs([...selectedPOIs, poi]);
      console.log(`Added to itinerary: ${poi.name}`);
    }
  };

  // Toggle type filter
  const toggleType = (type: string) => {
    if (selectedTypes.includes(type)) {
      setSelectedTypes(selectedTypes.filter(t => t !== type));
    } else {
      setSelectedTypes([...selectedTypes, type]);
    }
  };

  // Get all available POI types for the filter
  const poiTypes = Array.from(new Set(pointsOfInterest.map(poi => poi.category)));

  // Get points of interest for selected destinations
  const relevantPOIs = selectedDestinations.length > 0
    ? pointsOfInterest.filter(poi => 
        selectedDestinations.some(dest => 
          poi.location && dest.name && (poi.location.includes(dest.name) || dest.name.includes(poi.location))
        )
      )
    : [];

  // Filter points of interest based on search and type filters
  const filteredPOIs = relevantPOIs.filter(poi => {
    const matchesSearch = searchTerm === '' || 
      poi.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
      poi.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = selectedTypes.length === 0 || 
      selectedTypes.includes(poi.category);
    
    return matchesSearch && matchesType;
  });

  // Filter destinations based on search
  const filteredDestinations = destinations.filter(dest => 
    searchTerm === '' || 
    dest.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
    (dest.description && dest.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleDestinationClick = (destination: Destination) => {
    setSelectedDestinations(prev => {
      // If already selected, remove it
      if (prev.some(d => d.id === destination.id)) {
        return prev.filter(d => d.id !== destination.id);
      }
      // Add to selection
      return [...prev, destination];
    });
  };

  const handlePOIClick = (poi: PointOfInterest) => {
    setSelectedPOIs(prev => {
      // If already selected, remove it
      if (prev.some(p => p.id === poi.id)) {
        return prev.filter(p => p.id !== poi.id);
      }
      // Add to selection
      return [...prev, poi];
    });
  };

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(prevStep => prevStep + 1);
    }
  };
  
  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(prevStep => prevStep - 1);
    }
  };

  // Overlay action handlers (Portugal theme)
  const handleClose = () => setEvent(null);
  const handleSavePOI = (poi: POI) => {
    alert(`Saved ${poi.name} to your wine route!`);
  };
  const handleSkipPOI = (poi: POI) => {
    setEvent(null);
  };
  const handleShowDetails = (poi: POI) => {
    alert(`Show details for ${poi.name}`);
  };

  // Modify the getStepContent function for the first step (city selection)
  const getStepContent = () => {
    switch(currentStep) {
      case 1: // Town Selection
        return (
          <div className={`bg-white border-b border-gray-200 py-2 px-4 transition-all ${currentStep === 1 ? 'shadow-md' : ''}`}>
            <div className="max-w-3xl mx-auto">
              <div className="flex justify-between items-center mb-2">
                <h2 className="text-lg font-semibold text-douro-dark">Select Your Douro Valley Towns</h2>
                <div className="relative inline-block">
                  <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search towns..."
                    className="w-64 pl-10 pr-4 py-2 rounded-full bg-gray-50 text-gray-800 placeholder-gray-500 border border-gray-200 focus:outline-none focus:ring-2 focus:ring-douro-wine/30"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              <p className="text-sm text-gray-600 mb-3">Choose the towns you want to visit on your Douro Valley wine adventure.</p>
            </div>
          </div>
        );
      
      case 2: // POI Selection
        return (
          <div className={`bg-white border-b border-gray-200 py-3 px-4 transition-all ${currentStep === 2 ? 'shadow-md' : ''}`}>
            <div className="max-w-3xl mx-auto">
              <h2 className="text-lg font-semibold text-douro-dark mb-2">Choose Wineries & Activities</h2>
              <p className="text-sm text-gray-600 mb-3">Select wineries and experiences in {selectedDestinations.map(d => d.name).join(', ')}.</p>
              
              <div className="relative mb-3">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search for wineries and activities..."
                  className="w-full pl-10 pr-4 py-2 rounded-full bg-gray-50 text-gray-800 placeholder-gray-500 border border-gray-200 focus:outline-none focus:ring-2 focus:ring-douro-wine/30"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              {selectedDestinations.length > 0 ? (
                <>
                  <div className="mb-3">
                    <p className="font-medium text-sm text-gray-600 mb-1">Activity Types:</p>
                    <div className="flex flex-wrap gap-1.5">
                      {poiTypes.map(type => (
                        <Button
                          key={type}
                          variant={selectedTypes.includes(type) ? "default" : "outline"}
                          size="sm"
                          onClick={() => toggleType(type)}
                          className={`text-xs capitalize ${
                            selectedTypes.includes(type) ? 'bg-douro-wine hover:bg-douro-wine/90' : 'border-gray-200'
                          }`}
                        >
                          {type}
                        </Button>
                      ))}
                    </div>
                  </div>
                  
                  {filteredPOIs.length > 0 ? (
                    <p className="text-sm text-gray-600">{filteredPOIs.length} activities available to add</p>
                  ) : (
                    <p className="text-sm text-gray-600">No activities match your filters. Try adjusting your search.</p>
                  )}
                </>
              ) : (
                <div className="text-center py-4">
                  <p className="text-sm text-gray-600">Please select at least one destination first.</p>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="mt-2"
                    onClick={() => setCurrentStep(1)}
                  >
                    Go back to select destinations
                  </Button>
                </div>
              )}
            </div>
          </div>
        );
      
      case 3: // Vehicle Selection
        return (
          <div className={`bg-white border-b border-gray-200 py-3 px-4 transition-all ${currentStep === 3 ? 'shadow-md' : ''}`}>
            <div className="max-w-3xl mx-auto">
              <h2 className="text-lg font-semibold text-douro-dark mb-2">Select Your Vehicle</h2>
              <p className="text-sm text-gray-600 mb-3">Choose the perfect transportation for your journey.</p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {['Economy', 'SUV', 'Luxury', 'Van'].map(vehicle => (
                  <div key={vehicle} className="border rounded-lg p-4 cursor-pointer hover:border-douro-wine hover:bg-douro-wine/5 transition-all">
                    <h3 className="font-medium">{vehicle}</h3>
                    <p className="text-sm text-gray-600">Perfect for {vehicle === 'Economy' ? 'solo travelers or couples' : 
                      vehicle === 'SUV' ? 'small families or off-road adventures' :
                      vehicle === 'Luxury' ? 'comfort and style seekers' : 
                      'large groups up to 8 people'}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );
      
      case 4: // Review & Submit
        return (
          <div className={`bg-white border-b border-gray-200 py-3 px-4 transition-all ${currentStep === 4 ? 'shadow-md' : ''}`}>
            <div className="max-w-3xl mx-auto">
              <h2 className="text-lg font-semibold text-douro-dark mb-2">Review Your Trip</h2>
              <p className="text-sm text-gray-600 mb-3">Verify your selections and submit your request.</p>
              
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <h3 className="font-medium mb-2">Trip Summary</h3>
                <ul className="space-y-2">
                  <li className="flex justify-between text-sm">
                    <span>Destinations:</span>
                    <span className="font-medium">{selectedDestinations.length}</span>
                  </li>
                  <li className="flex justify-between text-sm">
                    <span>Activities:</span>
                    <span className="font-medium">{selectedPOIs.length}</span>
                  </li>
                  <li className="flex justify-between text-sm">
                    <span>Vehicle:</span>
                    <span className="font-medium">SUV</span>
                  </li>
                  <li className="flex justify-between text-sm">
                    <span>Estimated Duration:</span>
                    <span className="font-medium">{selectedDestinations.length * 2} days</span>
                  </li>
                </ul>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                  <input 
                    type="text" 
                    className="w-full p-2 border border-gray-300 rounded-md"
                    placeholder="Your name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  <input 
                    type="email" 
                    className="w-full p-2 border border-gray-300 rounded-md"
                    placeholder="Your email"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                  <input 
                    type="tel" 
                    className="w-full p-2 border border-gray-300 rounded-md"
                    placeholder="Your phone number"
                  />
                </div>
              </div>
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  // Add onStepClick handler to the ProgressIndicator 
  const handleStepClick = (step: number) => {
    // Only allow advancing to steps where we have necessary data
    if (step === 1 || 
        (step === 2 && selectedDestinations.length > 0) || 
        (step === 3 && selectedDestinations.length > 0 && selectedPOIs.length > 0) ||
        (step === 4 && selectedDestinations.length > 0 && selectedPOIs.length > 0)) {
      setCurrentStep(step);
    }
  };

  // Set map center/zoom based on region (example for Portugal Douro Valley)
  let mapCenter: [number, number] = region === 'portugal' ? [-7.7, 41.16] : [-7.5, 31.6];
  if (!Array.isArray(mapCenter) || mapCenter.length !== 2 || typeof mapCenter[0] !== 'number' || typeof mapCenter[1] !== 'number') {
    mapCenter = [-7.7, 41.16]; // fallback
  }
  const mapZoom = region === 'portugal' ? 9 : 6;

  return (
    <div className="map-view-container" style={{ display: 'flex', height: '100vh' }}>
      {/* Debug left pane */}
      <div style={{ width: '320px', backgroundColor: 'lightblue', border: '5px solid red', zIndex: 100, position: 'fixed', left: 0, top: 0, bottom: 0 }}>
        <h2 style={{ padding: '20px', fontSize: '24px', color: 'red' }}>Debug Left Pane</h2>
        <p style={{ padding: '20px', fontSize: '16px' }}>This debug pane should be visible on the left side</p>
      </div>

      <div className="main-content-area" style={{ flex: 1, display: 'flex', flexDirection: 'column', height: '100vh', overflow: 'auto', marginLeft: '320px', marginRight: '320px' }}>
        {/* Portugal Wine Tour Overlay (event-driven, can be toggled by event system) */}
        <DouroValleyWineOverlay
          event={event}
          onClose={handleClose}
          onSavePOI={handleSavePOI}
          onSkipPOI={handleSkipPOI}
          onShowDetails={handleShowDetails}
          theme={{
            background: '#f3f0e7',
            accentColor: '#7b3f00',
            fontFamily: 'Lora, serif',
          }}
        />

        {/* Main content */}
        <div className="h-screen flex flex-col">
          {/* Progress Indicator - visible at top of page */}
          <div className="bg-white border-b p-4 shadow-sm">
            {isMobile ? (
              <div className="flex items-center justify-between">
                <h1 className="text-lg font-semibold">Douro Valley Trip Planner</h1>
                <CompactProgressIndicator 
                  currentStep={currentStep} 
                  totalSteps={4}
                  onStepClick={handleStepClick}
                  allowInteraction={true}
                />
              </div>
            ) : (
              <div className="container mx-auto max-w-5xl">
                <div className="mb-4">
                  <h1 className="text-xl font-bold text-center mb-4">Douro Valley Trip Planner</h1>
                  <ProgressIndicator 
                    currentStep={currentStep} 
                    totalSteps={4} 
                    labels={tripSteps}
                    onStepClick={handleStepClick}
                    allowInteraction={true}
                  />
                </div>
              </div>
            )}
          </div>
          
          {/* Header */}
          <div className="bg-douro-wine text-white py-3 px-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <Link to="/explore">
                  <Button variant="ghost" size="sm" className="text-white hover:bg-white/10 mr-2 p-1 h-8 w-8">
                    <ArrowLeft className="h-5 w-5" />
                  </Button>
                </Link>
                <h1 className="text-xl font-bold">Douro Valley Explorer</h1>
              </div>
              
              {currentStep < 3 && (
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="text-white border-white/20 hover:bg-white/10"
                  onClick={() => setShowFilters(!showFilters)}
                >
                  <Filter className="h-4 w-4 mr-2" /> Filters
                </Button>
              )}
            </div>
          </div>
          
          {/* Step-specific content */}
          {getStepContent()}
          
          {/* Map container with destination tags overlay */}
          <div className="flex-grow relative">
            {/* Map component */}
            <ExploreMap 
              citiesForCountry={destinations.map(normalizeDestination)}
              poisForCurrentSelection={pointsOfInterest.map(normalizePOI)}
              selectedDestinations={selectedDestinations.map(normalizeDestination)}
              selectedPOIs={selectedPOIs.map(normalizePOI)}
              onDestinationSelect={handleDestinationClick}
              onPOISelect={handlePOIClick}
              onPOIDeselect={() => {}}
              mapCenter={mapCenter}
              mapZoom={mapZoom}
            />
          </div>
          
          {/* Bottom Controls */}
          <div className="bg-white border-t p-4">
            <div className="container mx-auto max-w-5xl flex justify-between items-center">
              <div className="text-sm text-gray-600">
                {currentStep === 1 && (
                  <p>Selected {selectedDestinations.length} destinations</p>
                )}
                {currentStep === 2 && (
                  <p>Added {selectedPOIs.length} points of interest</p>
                )}
                {currentStep === 3 && (
                  <p>Choose your preferred vehicle type</p>
                )}
                {currentStep === 4 && (
                  <p>Review your selections and submit your request</p>
                )}
              </div>
              
              <div className="flex gap-3">
                {currentStep > 1 && (
                  <Button variant="outline" onClick={handlePrevious}>
                    Back
                  </Button>
                )}
                <Button 
                  onClick={handleNext} 
                  disabled={currentStep === 4 || (currentStep === 1 && selectedDestinations.length === 0) || (currentStep === 2 && selectedPOIs.length === 0)}
                  className={currentStep === 4 ? 'bg-douro-wine hover:bg-douro-wine/90' : ''}
                >
                  {currentStep < 4 ? "Continue" : "Submit Request"}
                </Button>
              </div>
            </div>
          </div>
          
          {/* Contextual floating buttons */}
          {currentStep === 1 && selectedDestinations.length > 0 && (
            <div className="fixed bottom-20 md:bottom-6 left-1/2 transform -translate-x-1/2 bg-douro-dark text-white py-3 px-6 rounded-full shadow-lg z-20">
              <div className="flex items-center space-x-3">
                <span>{selectedDestinations.length} towns selected</span>
                <Button 
                  className="bg-white text-douro-dark hover:bg-white/90"
                  onClick={handleNext}
                >
                  Choose Activities
                </Button>
              </div>
            </div>
          )}
          
          {currentStep === 2 && selectedPOIs.length > 0 && (
            <div className="fixed bottom-20 md:bottom-6 left-1/2 transform -translate-x-1/2 bg-douro-dark text-white py-3 px-6 rounded-full shadow-lg z-20">
              <div className="flex items-center space-x-3">
                <span>{selectedPOIs.length} activities selected</span>
                <Button 
                  className="bg-white text-douro-dark hover:bg-white/90"
                  onClick={handleNext}
                >
                  Choose Vehicle
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Debug right pane */}
      <div style={{ width: '320px', backgroundColor: 'lightgreen', border: '5px solid blue', zIndex: 100, position: 'fixed', right: 0, top: 0, bottom: 0 }}>
        <h2 style={{ padding: '20px', fontSize: '24px', color: 'blue' }}>Debug Right Pane</h2>
        <p style={{ padding: '20px', fontSize: '16px' }}>This debug pane should be visible on the right side</p>
      </div>
    </div>
  );
};

export default MapView;
