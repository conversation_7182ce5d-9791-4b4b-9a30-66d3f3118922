import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useClient } from '../../contexts/ClientContext';
import DatabaseService from '../../services/database/DatabaseService';
import AdminService, { ClientTheme, ClientSettings } from '../../services/database/AdminService';
import {
  ArrowLeft,
  Palette,
  Eye,
  Save,
  RotateCcw,
  Upload,
  Download,
  Smartphone,
  Monitor,
  Tablet,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface ThemeConfig {
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  backgroundColor: string;
  textColor: string;
  borderColor: string;
  successColor: string;
  warningColor: string;
  errorColor: string;
}

interface BrandingConfig {
  companyName: string;
  tagline: string;
  logo: string;
  favicon: string;
  headerStyle: 'minimal' | 'standard' | 'prominent';
  footerStyle: 'minimal' | 'standard' | 'detailed';
}

const ThemeCustomization: React.FC = () => {
  const navigate = useNavigate();
  const { clientId } = useClient();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');
  
  const [themeConfig, setThemeConfig] = useState<ThemeConfig>({
    primaryColor: '#3B82F6',
    secondaryColor: '#6B7280',
    accentColor: '#F59E0B',
    backgroundColor: '#FFFFFF',
    textColor: '#1F2937',
    borderColor: '#E5E7EB',
    successColor: '#10B981',
    warningColor: '#F59E0B',
    errorColor: '#EF4444'
  });

  const [brandingConfig, setBrandingConfig] = useState<BrandingConfig>({
    companyName: '',
    tagline: '',
    logo: '',
    favicon: '',
    headerStyle: 'standard',
    footerStyle: 'standard'
  });

  const [originalTheme, setOriginalTheme] = useState<ThemeConfig>(themeConfig);
  const [originalBranding, setOriginalBranding] = useState<BrandingConfig>(brandingConfig);

  useEffect(() => {
    loadThemeData();
  }, [clientId]);

  const loadThemeData = async () => {
    try {
      setLoading(true);

      // Get client data to find client ID
      const clientData = await DatabaseService.getClientData(clientId);
      if (!clientData?.client) {
        console.warn('No client data found for:', clientId);
        return;
      }

      // Load theme from new admin system
      const activeTheme = await AdminService.getActiveClientTheme(clientData.client.id);
      const clientSettings = await AdminService.getClientSettings(clientData.client.id);

      if (activeTheme) {
        const loadedTheme = {
          primaryColor: activeTheme.primary_color,
          secondaryColor: activeTheme.secondary_color,
          accentColor: activeTheme.accent_color,
          backgroundColor: activeTheme.background_color,
          textColor: activeTheme.text_color,
          borderColor: activeTheme.border_color,
          successColor: activeTheme.success_color,
          warningColor: activeTheme.warning_color,
          errorColor: activeTheme.error_color
        };

        const loadedBranding = {
          companyName: clientSettings?.company_name || '',
          tagline: clientSettings?.company_tagline || '',
          logo: clientSettings?.logo_url || '',
          favicon: clientSettings?.favicon_url || '',
          headerStyle: activeTheme.header_style,
          footerStyle: activeTheme.footer_style
        };

        setThemeConfig(loadedTheme);
        setBrandingConfig(loadedBranding);
        setOriginalTheme(loadedTheme);
        setOriginalBranding(loadedBranding);
      } else {
        // Fallback to default theme if no theme found
        console.log('No active theme found, using defaults');
      }
    } catch (error) {
      console.error('Error loading theme data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleThemeChange = (key: keyof ThemeConfig, value: string) => {
    setThemeConfig(prev => ({ ...prev, [key]: value }));
  };

  const handleBrandingChange = (key: keyof BrandingConfig, value: string) => {
    setBrandingConfig(prev => ({ ...prev, [key]: value }));
  };

  const handleSave = async () => {
    try {
      setSaving(true);

      // Get client data
      const clientData = await DatabaseService.getClientData(clientId);
      if (!clientData?.client) {
        throw new Error('Client not found');
      }

      // Get current active theme
      const activeTheme = await AdminService.getActiveClientTheme(clientData.client.id);

      if (activeTheme) {
        // Update existing theme
        await AdminService.updateClientTheme(activeTheme.id, {
          primary_color: themeConfig.primaryColor,
          secondary_color: themeConfig.secondaryColor,
          accent_color: themeConfig.accentColor,
          background_color: themeConfig.backgroundColor,
          text_color: themeConfig.textColor,
          border_color: themeConfig.borderColor,
          success_color: themeConfig.successColor,
          warning_color: themeConfig.warningColor,
          error_color: themeConfig.errorColor,
          header_style: brandingConfig.headerStyle,
          footer_style: brandingConfig.footerStyle
        });
      } else {
        // Create new theme
        await AdminService.createClientTheme({
          client_id: clientData.client.id,
          theme_name: 'Default Theme',
          primary_color: themeConfig.primaryColor,
          secondary_color: themeConfig.secondaryColor,
          accent_color: themeConfig.accentColor,
          background_color: themeConfig.backgroundColor,
          text_color: themeConfig.textColor,
          border_color: themeConfig.borderColor,
          success_color: themeConfig.successColor,
          warning_color: themeConfig.warningColor,
          error_color: themeConfig.errorColor,
          font_family: 'Inter, sans-serif',
          heading_font: 'Inter, sans-serif',
          header_style: brandingConfig.headerStyle,
          footer_style: brandingConfig.footerStyle,
          is_active: true,
          is_default: true
        });
      }

      // Update client settings for branding
      await AdminService.updateClientSettings(clientData.client.id, {
        company_name: brandingConfig.companyName,
        company_tagline: brandingConfig.tagline,
        logo_url: brandingConfig.logo,
        favicon_url: brandingConfig.favicon
      });

      // Log the action
      await AdminService.logAdminAction({
        action: 'theme_updated',
        resource_type: 'client_theme',
        resource_id: clientData.client.id,
        new_values: { themeConfig, brandingConfig }
      });

      // Update original configs to reflect saved state
      setOriginalTheme(themeConfig);
      setOriginalBranding(brandingConfig);

      // Show success message
      alert('Theme saved successfully!');
    } catch (error) {
      console.error('Error saving theme:', error);
      alert('Error saving theme. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    setThemeConfig(originalTheme);
    setBrandingConfig(originalBranding);
  };

  const hasChanges = JSON.stringify(themeConfig) !== JSON.stringify(originalTheme) ||
                    JSON.stringify(brandingConfig) !== JSON.stringify(originalBranding);

  const ColorInput: React.FC<{
    label: string;
    value: string;
    onChange: (value: string) => void;
    description?: string;
  }> = ({ label, value, onChange, description }) => (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700">{label}</label>
      <div className="flex items-center space-x-3">
        <div 
          className="w-10 h-10 rounded-md border-2 border-gray-300 cursor-pointer"
          style={{ backgroundColor: value }}
          onClick={() => document.getElementById(`color-${label}`)?.click()}
        />
        <input
          id={`color-${label}`}
          type="color"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="sr-only"
        />
        <input
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="#000000"
        />
      </div>
      {description && <p className="text-xs text-gray-500">{description}</p>}
    </div>
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading theme settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/admin/dashboard')}
                className="p-2 text-gray-600 hover:bg-gray-50 rounded-md transition-colors"
              >
                <ArrowLeft size={20} />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Theme Customization</h1>
                <p className="text-gray-600">Customize colors, branding, and visual appearance</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              {hasChanges && (
                <button
                  onClick={handleReset}
                  className="flex items-center space-x-2 px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                >
                  <RotateCcw size={16} />
                  <span>Reset</span>
                </button>
              )}
              <button
                onClick={handleSave}
                disabled={!hasChanges || saving}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <Save size={16} />
                <span>{saving ? 'Saving...' : 'Save Changes'}</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Theme Configuration */}
          <div className="lg:col-span-1 space-y-6">
            {/* Color Palette */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Palette className="mr-2" size={20} />
                Color Palette
              </h3>
              <div className="space-y-4">
                <ColorInput
                  label="Primary Color"
                  value={themeConfig.primaryColor}
                  onChange={(value) => handleThemeChange('primaryColor', value)}
                  description="Main brand color for buttons and highlights"
                />
                <ColorInput
                  label="Secondary Color"
                  value={themeConfig.secondaryColor}
                  onChange={(value) => handleThemeChange('secondaryColor', value)}
                  description="Secondary elements and text"
                />
                <ColorInput
                  label="Accent Color"
                  value={themeConfig.accentColor}
                  onChange={(value) => handleThemeChange('accentColor', value)}
                  description="Call-to-action and emphasis"
                />
                <ColorInput
                  label="Background Color"
                  value={themeConfig.backgroundColor}
                  onChange={(value) => handleThemeChange('backgroundColor', value)}
                  description="Main background color"
                />
              </div>
            </div>

            {/* Branding */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Branding</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Company Name</label>
                  <input
                    type="text"
                    value={brandingConfig.companyName}
                    onChange={(e) => handleBrandingChange('companyName', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Your Company Name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Tagline</label>
                  <input
                    type="text"
                    value={brandingConfig.tagline}
                    onChange={(e) => handleBrandingChange('tagline', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Your company tagline"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Logo URL</label>
                  <input
                    type="url"
                    value={brandingConfig.logo}
                    onChange={(e) => handleBrandingChange('logo', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="https://example.com/logo.png"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Preview */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Live Preview</h3>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setPreviewMode('desktop')}
                    className={`p-2 rounded-md transition-colors ${
                      previewMode === 'desktop' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'
                    }`}
                  >
                    <Monitor size={20} />
                  </button>
                  <button
                    onClick={() => setPreviewMode('tablet')}
                    className={`p-2 rounded-md transition-colors ${
                      previewMode === 'tablet' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'
                    }`}
                  >
                    <Tablet size={20} />
                  </button>
                  <button
                    onClick={() => setPreviewMode('mobile')}
                    className={`p-2 rounded-md transition-colors ${
                      previewMode === 'mobile' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'
                    }`}
                  >
                    <Smartphone size={20} />
                  </button>
                </div>
              </div>

              {/* Preview Frame */}
              <div className={`mx-auto border-2 border-gray-300 rounded-lg overflow-hidden ${
                previewMode === 'desktop' ? 'w-full h-96' :
                previewMode === 'tablet' ? 'w-3/4 h-80' :
                'w-1/2 h-96'
              }`}>
                <div 
                  className="w-full h-full"
                  style={{
                    backgroundColor: themeConfig.backgroundColor,
                    color: themeConfig.textColor
                  }}
                >
                  {/* Preview Header */}
                  <div 
                    className="p-4 border-b"
                    style={{ 
                      backgroundColor: themeConfig.primaryColor,
                      borderColor: themeConfig.borderColor
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="text-white">
                        <h2 className="text-lg font-bold">{brandingConfig.companyName || 'Company Name'}</h2>
                        <p className="text-sm opacity-90">{brandingConfig.tagline || 'Your tagline here'}</p>
                      </div>
                    </div>
                  </div>

                  {/* Preview Content */}
                  <div className="p-6 space-y-4">
                    <div>
                      <h3 className="text-lg font-semibold mb-2">Sample Content</h3>
                      <p className="text-sm opacity-75">This is how your content will look with the current theme.</p>
                    </div>
                    
                    <div className="flex space-x-3">
                      <button 
                        className="px-4 py-2 rounded-md text-white text-sm font-medium"
                        style={{ backgroundColor: themeConfig.primaryColor }}
                      >
                        Primary Button
                      </button>
                      <button 
                        className="px-4 py-2 rounded-md text-white text-sm font-medium"
                        style={{ backgroundColor: themeConfig.accentColor }}
                      >
                        Accent Button
                      </button>
                    </div>

                    <div 
                      className="p-4 rounded-md border"
                      style={{ 
                        borderColor: themeConfig.borderColor,
                        backgroundColor: `${themeConfig.primaryColor}10`
                      }}
                    >
                      <p className="text-sm">Sample card with themed styling</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThemeCustomization;
