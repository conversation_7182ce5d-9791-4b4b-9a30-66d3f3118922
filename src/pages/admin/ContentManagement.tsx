import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useClient } from '../../contexts/ClientContext';
import DatabaseService from '../../services/database/DatabaseService';
import { 
  ArrowLeft,
  MapPin,
  Building2,
  Route,
  Star,
  Plus,
  Edit,
  Trash2,
  Eye,
  Search,
  Filter
} from 'lucide-react';

interface ContentStats {
  destinations: number;
  pois: number;
  journeys: number;
  reviews: number;
}

interface ContentItem {
  id: string;
  name: string;
  description: string;
  type: 'destination' | 'poi' | 'journey' | 'review';
  status: 'active' | 'draft' | 'archived';
  created_at: string;
  updated_at: string;
}

const ContentManagement: React.FC = () => {
  const navigate = useNavigate();
  const { clientId } = useClient();
  const [stats, setStats] = useState<ContentStats>({
    destinations: 0,
    pois: 0,
    journeys: 0,
    reviews: 0
  });
  const [contentItems, setContentItems] = useState<ContentItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'destinations' | 'pois' | 'journeys' | 'reviews'>('destinations');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'draft' | 'archived'>('all');

  useEffect(() => {
    loadContentData();
  }, [clientId, activeTab]);

  const loadContentData = async () => {
    try {
      setLoading(true);
      const clientData = await DatabaseService.getClientData(clientId);

      if (clientData) {
        setStats({
          destinations: clientData.destinations.length,
          pois: clientData.pois.length,
          journeys: clientData.journeyTemplates.length,
          reviews: 0 // TODO: Add reviews count from database
        });

        // Convert data to ContentItem format based on active tab
        let items: ContentItem[] = [];
        switch (activeTab) {
          case 'destinations':
            items = clientData.destinations.map(dest => ({
              id: dest.id,
              name: dest.name,
              description: dest.description || '',
              type: 'destination' as const,
              status: 'active' as const,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }));
            break;
          case 'pois':
            items = clientData.pois.map(poi => ({
              id: poi.id,
              name: poi.name,
              description: poi.description || '',
              type: 'poi' as const,
              status: 'active' as const,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }));
            break;
          case 'journeys':
            items = clientData.journeyTemplates.map(journey => ({
              id: journey.id,
              name: journey.name,
              description: journey.description,
              type: 'journey' as const,
              status: 'active' as const,
              created_at: journey.created_at,
              updated_at: journey.updated_at
            }));
            break;
          case 'reviews':
            // TODO: Load reviews from database
            items = [];
            break;
        }
        setContentItems(items);
      }
    } catch (error) {
      console.error('Error loading content data:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredItems = contentItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'all' || item.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const StatCard: React.FC<{
    title: string;
    value: number;
    icon: React.ReactNode;
    color: string;
    active: boolean;
    onClick: () => void;
  }> = ({ title, value, icon, color, active, onClick }) => (
    <div 
      className={`bg-white rounded-lg shadow-md p-6 border-l-4 cursor-pointer transition-all ${
        active ? `${color} shadow-lg` : 'border-gray-300 hover:shadow-lg'
      }`}
      onClick={onClick}
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
        </div>
        <div className={active ? 'text-blue-600' : 'text-gray-400'}>
          {icon}
        </div>
      </div>
    </div>
  );

  const ContentItemCard: React.FC<{ item: ContentItem }> = ({ item }) => (
    <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900">{item.name}</h3>
          <p className="text-sm text-gray-600 mt-1 line-clamp-2">{item.description}</p>
        </div>
        <div className="flex items-center space-x-2 ml-4">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
            item.status === 'active' 
              ? 'bg-green-100 text-green-800'
              : item.status === 'draft'
              ? 'bg-yellow-100 text-yellow-800'
              : 'bg-gray-100 text-gray-800'
          }`}>
            {item.status}
          </span>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-500">
          Updated: {new Date(item.updated_at).toLocaleDateString()}
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => {/* TODO: Implement view */}}
            className="p-2 text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
            title="View"
          >
            <Eye size={16} />
          </button>
          <button
            onClick={() => {/* TODO: Implement edit */}}
            className="p-2 text-gray-600 hover:bg-gray-50 rounded-md transition-colors"
            title="Edit"
          >
            <Edit size={16} />
          </button>
          <button
            onClick={() => {/* TODO: Implement delete */}}
            className="p-2 text-red-600 hover:bg-red-50 rounded-md transition-colors"
            title="Delete"
          >
            <Trash2 size={16} />
          </button>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading content...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/admin/dashboard')}
                className="p-2 text-gray-600 hover:bg-gray-50 rounded-md transition-colors"
              >
                <ArrowLeft size={20} />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Content Management</h1>
                <p className="text-gray-600">Manage destinations, POIs, journeys, and reviews</p>
              </div>
            </div>
            <button
              onClick={() => {/* TODO: Implement create new */}}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <Plus size={20} />
              <span>New {activeTab.slice(0, -1)}</span>
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <StatCard
            title="Destinations"
            value={stats.destinations}
            icon={<MapPin size={24} />}
            color="border-blue-500"
            active={activeTab === 'destinations'}
            onClick={() => setActiveTab('destinations')}
          />
          <StatCard
            title="Points of Interest"
            value={stats.pois}
            icon={<Building2 size={24} />}
            color="border-green-500"
            active={activeTab === 'pois'}
            onClick={() => setActiveTab('pois')}
          />
          <StatCard
            title="Journeys"
            value={stats.journeys}
            icon={<Route size={24} />}
            color="border-orange-500"
            active={activeTab === 'journeys'}
            onClick={() => setActiveTab('journeys')}
          />
          <StatCard
            title="Reviews"
            value={stats.reviews}
            icon={<Star size={24} />}
            color="border-purple-500"
            active={activeTab === 'reviews'}
            onClick={() => setActiveTab('reviews')}
          />
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                <input
                  type="text"
                  placeholder={`Search ${activeTab}...`}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Filter size={20} className="text-gray-400" />
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as any)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="draft">Draft</option>
                <option value="archived">Archived</option>
              </select>
            </div>
          </div>
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredItems.map(item => (
            <ContentItemCard key={item.id} item={item} />
          ))}
        </div>

        {filteredItems.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              {activeTab === 'destinations' && <MapPin size={48} className="mx-auto" />}
              {activeTab === 'pois' && <Building2 size={48} className="mx-auto" />}
              {activeTab === 'journeys' && <Route size={48} className="mx-auto" />}
              {activeTab === 'reviews' && <Star size={48} className="mx-auto" />}
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No {activeTab} found
            </h3>
            <p className="text-gray-600 mb-4">
              {searchTerm || filterStatus !== 'all' 
                ? 'Try adjusting your search or filter criteria.'
                : `Get started by creating your first ${activeTab.slice(0, -1)}.`
              }
            </p>
            <button
              onClick={() => {/* TODO: Implement create new */}}
              className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <Plus size={20} />
              <span>Create {activeTab.slice(0, -1)}</span>
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ContentManagement;
