import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DatabaseService from '../../services/database/DatabaseService';
import AdminService, { ClientTheme, ClientSettings } from '../../services/database/AdminService';
import { DatabaseHelper } from '../../lib/supabase';
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  Settings,
  Users,
  MapPin,
  Palette,
  ArrowLeft,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface Client {
  id: string;
  name: string;
  slug: string;
  theme_config: any;
  branding_config: any;
  map_config: any;
  contact_info: any;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

const ClientManagement: React.FC = () => {
  const navigate = useNavigate();
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingClient, setEditingClient] = useState<Client | null>(null);

  useEffect(() => {
    loadClients();
  }, []);

  const loadClients = async () => {
    try {
      setLoading(true);

      // Load all clients from database
      const dbClients = await DatabaseHelper.getAllClients();

      // Enrich with theme and settings data
      const enrichedClients = await Promise.all(
        dbClients.map(async (client) => {
          try {
            const [activeTheme, settings] = await Promise.all([
              AdminService.getActiveClientTheme(client.id),
              AdminService.getClientSettings(client.id)
            ]);

            return {
              id: client.id,
              name: client.name,
              slug: client.slug,
              theme_config: activeTheme ? {
                primaryColor: activeTheme.primary_color,
                secondaryColor: activeTheme.secondary_color
              } : { primaryColor: '#3B82F6', secondaryColor: '#6B7280' },
              branding_config: settings ? {
                companyName: settings.company_name || client.name,
                tagline: settings.company_tagline || ''
              } : { companyName: client.name, tagline: '' },
              map_config: client.map_config || { center: [0, 20], zoom: 6 },
              contact_info: client.contact_info || {},
              is_active: client.is_active,
              created_at: client.created_at,
              updated_at: client.updated_at
            };
          } catch (error) {
            console.warn(`Error loading data for client ${client.slug}:`, error);
            // Return basic client data if enrichment fails
            return {
              id: client.id,
              name: client.name,
              slug: client.slug,
              theme_config: { primaryColor: '#3B82F6', secondaryColor: '#6B7280' },
              branding_config: { companyName: client.name, tagline: '' },
              map_config: { center: [0, 20], zoom: 6 },
              contact_info: {},
              is_active: client.is_active,
              created_at: client.created_at,
              updated_at: client.updated_at
            };
          }
        })
      );

      setClients(enrichedClients);
    } catch (error) {
      console.error('Error loading clients:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateClient = () => {
    setEditingClient(null);
    setShowCreateModal(true);
  };

  const handleEditClient = (client: Client) => {
    setEditingClient(client);
    setShowCreateModal(true);
  };

  const handleDeleteClient = async (clientId: string) => {
    if (window.confirm('Are you sure you want to delete this client? This action cannot be undone.')) {
      try {
        // TODO: Implement DatabaseService.deleteClient(clientId)
        setClients(clients.filter(c => c.id !== clientId));
      } catch (error) {
        console.error('Error deleting client:', error);
      }
    }
  };

  const handleViewClient = (client: Client) => {
    // Navigate to the client's demo page
    const demoRoutes: Record<string, string> = {
      'morocco': '/moroccan-demo',
      'portugal': '/portuguese-demo',
      'global': '/enhanced-neutral-demo',
      'neutral': '/enhanced-neutral-demo'
    };
    
    const route = demoRoutes[client.slug];
    if (route) {
      window.open(route, '_blank');
    }
  };

  const ClientCard: React.FC<{ client: Client }> = ({ client }) => (
    <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
      <div className="flex items-start justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{client.name}</h3>
          <p className="text-sm text-gray-600">/{client.slug}</p>
          <p className="text-sm text-gray-500 mt-1">
            {client.branding_config?.tagline || 'No tagline set'}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
            client.is_active 
              ? 'bg-green-100 text-green-800' 
              : 'bg-red-100 text-red-800'
          }`}>
            {client.is_active ? 'Active' : 'Inactive'}
          </span>
        </div>
      </div>

      <div className="flex items-center space-x-4 mb-4">
        <div 
          className="w-4 h-4 rounded-full border-2 border-gray-300"
          style={{ backgroundColor: client.theme_config?.primaryColor || '#6B7280' }}
        ></div>
        <span className="text-sm text-gray-600">
          Primary: {client.theme_config?.primaryColor || 'Not set'}
        </span>
      </div>

      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-500">
          Created: {new Date(client.created_at).toLocaleDateString()}
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => handleViewClient(client)}
            className="p-2 text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
            title="View Demo"
          >
            <Eye size={16} />
          </button>
          <button
            onClick={() => handleEditClient(client)}
            className="p-2 text-gray-600 hover:bg-gray-50 rounded-md transition-colors"
            title="Edit Client"
          >
            <Edit size={16} />
          </button>
          <button
            onClick={() => navigate(`/admin/clients/${client.id}/settings`)}
            className="p-2 text-gray-600 hover:bg-gray-50 rounded-md transition-colors"
            title="Client Settings"
          >
            <Settings size={16} />
          </button>
          <button
            onClick={() => handleDeleteClient(client.id)}
            className="p-2 text-red-600 hover:bg-red-50 rounded-md transition-colors"
            title="Delete Client"
          >
            <Trash2 size={16} />
          </button>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading clients...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/admin/dashboard')}
                className="p-2 text-gray-600 hover:bg-gray-50 rounded-md transition-colors"
              >
                <ArrowLeft size={20} />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Client Management</h1>
                <p className="text-gray-600">Manage all client accounts and configurations</p>
              </div>
            </div>
            <button
              onClick={handleCreateClient}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <Plus size={20} />
              <span>New Client</span>
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <Users className="text-blue-600" size={24} />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Clients</p>
                <p className="text-2xl font-bold text-gray-900">{clients.length}</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <Users className="text-green-600" size={24} />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Clients</p>
                <p className="text-2xl font-bold text-gray-900">
                  {clients.filter(c => c.is_active).length}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <MapPin className="text-orange-600" size={24} />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Regions</p>
                <p className="text-2xl font-bold text-gray-900">
                  {new Set(clients.map(c => c.slug)).size}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <Palette className="text-purple-600" size={24} />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Custom Themes</p>
                <p className="text-2xl font-bold text-gray-900">
                  {clients.filter(c => c.theme_config?.primaryColor).length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Clients Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {clients.map(client => (
            <ClientCard key={client.id} client={client} />
          ))}
        </div>
      </div>

      {/* Create/Edit Modal - TODO: Implement */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h2 className="text-xl font-bold mb-4">
              {editingClient ? 'Edit Client' : 'Create New Client'}
            </h2>
            <p className="text-gray-600 mb-4">
              Client creation/editing form will be implemented in the next phase.
            </p>
            <div className="flex justify-end space-x-2">
              <button
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                {editingClient ? 'Update' : 'Create'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ClientManagement;
