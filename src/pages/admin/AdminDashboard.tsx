import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useClient } from '../../contexts/ClientContext';
import { useAdminAuth, ADMIN_PERMISSIONS } from '../../contexts/AdminAuthContext';
import DatabaseService from '../../services/database/DatabaseService';
import {
  Users,
  MapPin,
  Building2,
  Settings,
  BarChart3,
  Globe,
  Shield,
  Database,
  Palette,
  FileText,
  LogOut
} from 'lucide-react';

interface AdminStats {
  totalClients: number;
  totalDestinations: number;
  totalPOIs: number;
  totalJourneys: number;
  totalReviews: number;
}

interface AdminUser {
  id: string;
  role: 'super_admin' | 'client_admin';
  clientId?: string;
  permissions: string[];
}

const AdminDashboard: React.FC = () => {
  const { clientId } = useClient();
  const navigate = useNavigate();
  const { user, isSuperAdmin, isClientAdmin, hasPermission, logout } = useAdminAuth();
  const [stats, setStats] = useState<AdminStats>({
    totalClients: 0,
    totalDestinations: 0,
    totalPOIs: 0,
    totalJourneys: 0,
    totalReviews: 0
  });
  const [loading, setLoading] = useState(true);
  const [currentClient, setCurrentClient] = useState<any>(null);

  useEffect(() => {
    if (user) {
      loadAdminData();
    }
  }, [clientId, user]);

  const loadAdminData = async () => {
    try {
      setLoading(true);
      
      // Load basic stats
      const clientData = await DatabaseService.getClientData(clientId);
      if (clientData) {
        setStats({
          totalClients: isSuperAdmin ? 4 : 1,
          totalDestinations: clientData.destinations.length,
          totalPOIs: clientData.pois.length,
          totalJourneys: clientData.journeyTemplates.length,
          totalReviews: 0 // TODO: Add reviews count
        });
        setCurrentClient(clientData);
      }
    } catch (error) {
      console.error('Error loading admin data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Remove determineUserRole function - now handled by AdminAuthContext

  const StatCard: React.FC<{
    title: string;
    value: number;
    icon: React.ReactNode;
    color: string;
    onClick?: () => void;
  }> = ({ title, value, icon, color, onClick }) => (
    <div 
      className={`bg-white rounded-lg shadow-md p-6 border-l-4 ${color} cursor-pointer hover:shadow-lg transition-shadow`}
      onClick={onClick}
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
        </div>
        <div className="text-gray-400">
          {icon}
        </div>
      </div>
    </div>
  );

  const AdminSection: React.FC<{
    title: string;
    description: string;
    icon: React.ReactNode;
    onClick: () => void;
    disabled?: boolean;
  }> = ({ title, description, icon, onClick, disabled = false }) => (
    <div 
      className={`bg-white rounded-lg shadow-md p-6 cursor-pointer hover:shadow-lg transition-shadow ${
        disabled ? 'opacity-50 cursor-not-allowed' : ''
      }`}
      onClick={disabled ? undefined : onClick}
    >
      <div className="flex items-start space-x-4">
        <div className="text-blue-600">
          {icon}
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          <p className="text-gray-600 mt-1">{description}</p>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading admin dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {isSuperAdmin ? 'Super Admin Dashboard' : 'Admin Dashboard'}
              </h1>
              <p className="text-gray-600">
                {isSuperAdmin
                  ? 'Manage all clients and global settings'
                  : `Manage ${currentClient?.client?.name || 'your'} content and settings`
                }
              </p>
              {user && (
                <p className="text-sm text-gray-500 mt-1">
                  Logged in as: {user.email}
                </p>
              )}
            </div>
            <div className="flex items-center space-x-4">
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                isSuperAdmin
                  ? 'bg-purple-100 text-purple-800'
                  : 'bg-blue-100 text-blue-800'
              }`}>
                {isSuperAdmin ? 'Super Admin' : 'Client Admin'}
              </span>
              <button
                onClick={logout}
                className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
              >
                <LogOut size={16} />
                <span>Logout</span>
              </button>
              <button
                onClick={() => navigate('/')}
                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
              >
                Back to App
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          {isSuperAdmin && hasPermission(ADMIN_PERMISSIONS.CLIENTS_VIEW) && (
            <StatCard
              title="Total Clients"
              value={stats.totalClients}
              icon={<Users size={24} />}
              color="border-purple-500"
              onClick={() => navigate('/admin/clients')}
            />
          )}
          <StatCard
            title="Destinations"
            value={stats.totalDestinations}
            icon={<MapPin size={24} />}
            color="border-blue-500"
            onClick={() => navigate('/admin/destinations')}
          />
          <StatCard
            title="Points of Interest"
            value={stats.totalPOIs}
            icon={<Building2 size={24} />}
            color="border-green-500"
            onClick={() => navigate('/admin/pois')}
          />
          <StatCard
            title="Journeys"
            value={stats.totalJourneys}
            icon={<Globe size={24} />}
            color="border-orange-500"
            onClick={() => navigate('/admin/journeys')}
          />
          <StatCard
            title="Reviews"
            value={stats.totalReviews}
            icon={<BarChart3 size={24} />}
            color="border-red-500"
            onClick={() => navigate('/admin/reviews')}
          />
        </div>

        {/* Admin Sections */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Super Admin Only Sections */}
          {isSuperAdmin && (
            <>
              {hasPermission(ADMIN_PERMISSIONS.CLIENTS_VIEW) && (
                <AdminSection
                  title="Client Management"
                  description="Create, edit, and manage client accounts and configurations"
                  icon={<Users size={24} />}
                  onClick={() => navigate('/admin/clients')}
                />
              )}
              {hasPermission(ADMIN_PERMISSIONS.CONTENT_VIEW) && (
                <AdminSection
                  title="Global Content"
                  description="Manage global POI database and journey templates"
                  icon={<Database size={24} />}
                  onClick={() => navigate('/admin/global-content')}
                />
              )}
              {hasPermission(ADMIN_PERMISSIONS.SYSTEM_SETTINGS) && (
                <AdminSection
                  title="System Settings"
                  description="Configure system-wide settings and monitoring"
                  icon={<Settings size={24} />}
                  onClick={() => navigate('/admin/system')}
                />
              )}
            </>
          )}

          {/* Common Admin Sections */}
          <AdminSection
            title="Content Management"
            description="Manage destinations, POIs, journeys, and reviews"
            icon={<FileText size={24} />}
            onClick={() => navigate('/admin/content')}
          />
          <AdminSection
            title="Theme Customization"
            description="Customize colors, branding, and visual appearance"
            icon={<Palette size={24} />}
            onClick={() => navigate('/admin/themes')}
          />
          <AdminSection
            title="Legacy Admin"
            description="Access detailed admin tools and forms"
            icon={<Settings size={24} />}
            onClick={() => navigate('/admin/legacy')}
          />
          <AdminSection
            title="User Management"
            description="Manage user accounts and permissions"
            icon={<Users size={24} />}
            onClick={() => navigate('/admin/legacy?tab=users')}
          />
          <AdminSection
            title="Quote Requests"
            description="View and manage customer quote requests"
            icon={<FileText size={24} />}
            onClick={() => navigate('/admin/legacy?tab=quotes')}
          />
          <AdminSection
            title="Analytics & Reports"
            description="View usage analytics and generate reports"
            icon={<BarChart3 size={24} />}
            onClick={() => navigate('/admin/analytics')}
          />
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
