import React, { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import ExploreMap, { ExploreMapHandles } from '../components/map/ExploreMap';
import { Destination, PointOfInterest, POICategory } from '@/types';
import { regionData } from '@/data/destinations';
import { pointsOfInterest as importedAllPointsOfInterest } from '@/data/pointsOfInterest';
import { convertPOIToPointOfInterest, filterPOIsForCity, normalizeDestination } from '../utils/poiUtils';
import MainHeader from '@/components/MainHeader';
import SearchFilters from '@/components/SearchFilters';
import RightSidebar from '@/components/map/RightSidebar';
import { Button } from '@/components/ui/button';
import { X, Menu, ChevronLeft, ChevronRight } from 'lucide-react';
import VehicleSelector from '@/components/VehicleSelector';
import { vehicles } from '@/data/vehicles';
import { Position, toPositionObject } from '../types/Position';
import { useIsMobile } from '@/hooks/use-mobile';
import AnimationDebugPanel from '../components/map/animation/AnimationDebugPanel';
import { cn } from '@/lib/utils';
import LeftPOIPanel from '../components/map/LeftPOIPanel';
import { TravelPace, JourneyStyle, TravelInterest, PreArrangedJourney } from '@/types/ItineraryParameters';
import { MapProvider } from '@/hooks/useMap';
import { useJourney } from '@/hooks/useJourney';
import { ItineraryProvider } from '@/hooks/useItinerary';
import ItineraryPanel from '@/components/itinerary/ItineraryPanel';
import { moroccoPreArrangedJourneys, getRecommendedJourneys } from '@/data/preArrangedJourneys';

// Define JourneyPhase enum (if not already globally available)
export enum JourneyPhase {
  NOT_STARTED = 'NOT_STARTED',
  INITIAL_CITY_SELECTION = 'INITIAL_CITY_SELECTION', // User is picking first city/cities
  PARAMETER_INPUT = 'PARAMETER_INPUT', // User is setting journey parameters (days, style, interests)
  ITINERARY_GENERATION = 'ITINERARY_GENERATION', // System is generating initial itinerary
  ITINERARY_REVIEW = 'ITINERARY_REVIEW', // User is reviewing/modifying the generated itinerary
  JOURNEY = 'JOURNEY', // Animation/travel is active
  POI_DISCOVERY = 'POI_DISCOVERY', // User is focused on a discovered POI
  COMPLETED = 'COMPLETED', // Ensured initializer is present
  // Add other relevant phases
}

const DEFAULT_REGION = 'morocco';
const REGION_OPTIONS = [
  { label: 'Morocco', value: 'morocco' },
  { label: 'Portugal', value: 'portugal' },
];

// Define runtime array of POI category string values - Updated for Discovery/Exploration
const poiCategoryValues: POICategory[] = [
  'landmark',
  'nature',
  'cultural',
  'adventure',
  'scenic',
  'hidden-gem',
  'photography',
  'local-experience',
  'architecture',
  'viewpoint',
  'activity',
  'other'
];

// Define a more specific type for the raw destination object if possible
// For now, using Partial<Destination> and then ensuring defaults.
const ensureDestinationDefaults = (dest: Partial<Destination & { position: any }>): Destination => ({
  ...normalizeDestination(dest as Destination), // normalizeDestination expects a fuller Destination object
  position: dest.position || { lat: 0, lon: 0 } // Assuming lat/lon might be used if lng/lat isn't present initially
});

const AVAILABLE_TRAVEL_PACES: { value: TravelPace; label: string }[] = [
  { value: 'slow-immersive', label: 'Slow & Immersive' },
  { value: 'balanced-explorer', label: 'Balanced Explorer' },
  { value: 'maximum-discovery', label: 'Maximum Discovery' }
];

const AVAILABLE_JOURNEY_STYLES: { value: JourneyStyle; label: string }[] = [
  { value: 'scenic-routes', label: 'Scenic Routes' },
  { value: 'cultural-deep-dive', label: 'Cultural Deep-dive' },
  { value: 'adventure-seeker', label: 'Adventure Seeker' },
  { value: 'photography-tour', label: 'Photography Tour' },
  { value: 'hidden-gems', label: 'Hidden Gems' },
  { value: 'local-immersion', label: 'Local Immersion' }
];

const AVAILABLE_TRAVEL_INTERESTS: { value: TravelInterest; label: string }[] = [
  { value: 'landmarks', label: 'Historic Landmarks' },
  { value: 'nature', label: 'Natural Wonders' },
  { value: 'culture', label: 'Cultural Sites' },
  { value: 'adventure', label: 'Adventure Activities' },
  { value: 'photography', label: 'Photography' },
  { value: 'architecture', label: 'Architecture' },
  { value: 'local-experience', label: 'Local Experiences' },
  { value: 'hidden-gems', label: 'Hidden Gems' },
  { value: 'scenic-drives', label: 'Scenic Drives' },
  { value: 'viewpoints', label: 'Viewpoints' }
];

// Temporary hardcoded values for debugging map loop
const HARDCODED_MAP_CENTER: [number, number] = [-5, 31]; // Morocco default center
const HARDCODED_MAX_BOUNDS: [[number, number], [number, number]] = [[-18, 27], [0, 36]]; // Morocco default bounds

console.log('HomePage component loaded - You should go to /map to see the new UI');

const HomePage = () => {
  // console.log('[HomePage FULLY SIMPLIFIED FOR DEBUGGING]');

  useEffect(() => {
    console.log('[HomePage MOUNTED]');
    return () => {
      console.log('[HomePage UNMOUNTING]');
    };
  }, []);

  const isMobile = useIsMobile();
  console.log('[HomePage/pages] isMobile:', isMobile, 'window.innerWidth:', typeof window !== 'undefined' ? window.innerWidth : 'SSR');
  const [showMobileSidebar, setShowMobileSidebar] = useState(false);

  const [currentRegion, setCurrentRegion] = useState<string>(DEFAULT_REGION);
  const [selectedDestinations, setSelectedDestinations] = useState<Destination[]>([]);
  const [selectedPOIs, setSelectedPOIs] = useState<PointOfInterest[]>([]);
  const mapRef = useRef<ExploreMapHandles>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [showVehicleSelectionModal, setShowVehicleSelectionModal] = useState(false);
  const [selectedVehicle, setSelectedVehicle] = useState(vehicles[0]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedCity, setSelectedCity] = useState<Destination | null>(null);
  const [showMobileItinerary, setShowMobileItinerary] = useState(false);

  // Enhanced panel collapse state with transition classes
  const [isLeftPanelCollapsed, setIsLeftPanelCollapsed] = useState(false);
  const [isRightPanelCollapsed, setIsRightPanelCollapsed] = useState(false);

  // State to hold the map instance from ExploreMap
  const [homePageMapInstance, setHomePageMapInstance] = useState<mapboxgl.Map | null>(null);

  // State to control visibility of the debug panel
  const [showDebugPanel, setShowDebugPanel] = useState(false); // Initialize to false to hide by default

  // Toggle function for categories (to be passed to SearchFilters as toggleType)
  const toggleCategory = useCallback((category: string) => {
    setSelectedCategories(prev => 
      prev.includes(category) 
        ? prev.filter(c => c !== category) 
        : [...prev, category]
    );
  }, []);

  // Toggle function for destinations (to be passed to SearchFilters as toggleDestination)
  // This specific handler is for the SearchFilters component, which might have different selection logic
  // than the main POI/Destination selection for the map or journey.
  const toggleDestinationForFilter = useCallback((destination: Destination) => {
    setSelectedDestinations(prev => {
      // This logic assumes SearchFilters might want to toggle a single main destination 
      // or manage its own separate list. For now, let's assume it works like a multi-select toggle.
      const isSelected = prev.some(d => d.id === destination.id);
      if (isSelected) {
        return prev.filter(d => d.id !== destination.id);
      }
      return [...prev, destination]; 
    });
  }, []);

  const memoizedRegionData = useMemo(() => {
    const data = regionData[currentRegion as keyof typeof regionData] || regionData[DEFAULT_REGION as keyof typeof regionData];
    return {
      ...data, // Spreads destinations, pointsOfInterest, mapCenter, mapZoom, maxBounds
      destinations: data.destinations.map(ensureDestinationDefaults),
      // Removed nested mapConfiguration, as properties are top-level in data
    };
  }, [currentRegion]);

  // Use regionData for POIs, ensuring all are normalized
  const allPointsOfInterestForRegion = useMemo(() => {
    const regionPOIs = regionData[currentRegion]?.pointsOfInterest || [];
    return regionPOIs.map(poi => convertPOIToPointOfInterest(poi));
  }, [currentRegion]);

  useEffect(() => {
    const ouzoud = allPointsOfInterestForRegion.find(poi => poi.name === 'Ouzoud Waterfalls');
    if (ouzoud) {
      console.log('[HomePage] Ouzoud Waterfalls object in allPointsOfInterestForRegion:', {
        id: ouzoud.id,
        name: ouzoud.name,
        images: ouzoud.images,
        image: (ouzoud as any).image, // Log original image prop if it exists, for debugging
        rawObject: ouzoud
      });
    } else {
      console.log('[HomePage] Ouzoud Waterfalls not found in allPointsOfInterestForRegion.');
    }
  }, [allPointsOfInterestForRegion]);

  const sidebarFilteredPOIs = useMemo(() => {
    let poisToFilter = allPointsOfInterestForRegion;
    if (selectedCity) {
      // If a city is selected, filter POIs for that city first
      // Assuming selectedCity (Destination type) might have its own POI list or requires specific filtering
      poisToFilter = filterPOIsForCity(allPointsOfInterestForRegion, selectedCity, memoizedRegionData.destinations);
    }

    // Apply search term filter
    if (searchTerm) {
      poisToFilter = poisToFilter.filter(poi =>
        poi.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        poi.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    // Apply category filter
    if (selectedCategories.length > 0) {
      poisToFilter = poisToFilter.filter(poi => selectedCategories.includes(poi.category as string));
    }
    return poisToFilter;
  }, [allPointsOfInterestForRegion, searchTerm, selectedCategories, selectedCity, memoizedRegionData.destinations]);

  // Sequential city selection logic
  const handleSequentialCitySelection = useCallback((destination: Destination) => {
    setSelectedDestinations(prev => {
      const exists = prev.some(d => d.id === destination.id);

      if (exists) {
        // Remove city and adjust step
        const newDestinations = prev.filter(d => d.id !== destination.id);
        setCitySelectionStep(newDestinations.length);
        return newDestinations;
      } else {
        // Add city in sequence
        const newDestinations = [...prev, destination];
        setCitySelectionStep(newDestinations.length);

        // Zoom to the newly selected city
        if (mapRef.current) {
          let LngLatArray: [number, number] | undefined;

          if (Array.isArray(destination.position) && destination.position.length === 2) {
            LngLatArray = [destination.position[0], destination.position[1]];
          } else if (typeof destination.position === 'object' && destination.position !== null) {
            const posObj = destination.position as { lng?: number; lat?: number; lon?: number };
            if (typeof posObj.lng === 'number' && typeof posObj.lat === 'number') {
              LngLatArray = [posObj.lng, posObj.lat];
            } else if (typeof posObj.lon === 'number' && typeof posObj.lat === 'number') {
              LngLatArray = [posObj.lon, posObj.lat];
            }
          }

          if (LngLatArray) {
            mapRef.current.flyTo(LngLatArray, 12);
          }
        }

        // Filter POIs for the newly selected city
        setSelectedCity(destination);

        return newDestinations;
      }
    });
  }, []);

  // Legacy toggle function for backward compatibility
  const toggleDestination = useCallback((destination: Destination) => {
    handleSequentialCitySelection(destination);
  }, [handleSequentialCitySelection]);

  // Handle city removal from itinerary panel
  const handleCityRemove = useCallback((cityToRemove: Destination) => {
    setSelectedDestinations(prev => {
      const newDestinations = prev.filter(d => d.id !== cityToRemove.id);
      setCitySelectionStep(newDestinations.length);

      // If we removed the currently selected city, select the last city or clear
      if (selectedCity?.id === cityToRemove.id) {
        const lastCity = newDestinations[newDestinations.length - 1];
        setSelectedCity(lastCity || null);

        // Zoom to last city or reset view
        if (lastCity && mapRef.current) {
          let LngLatArray: [number, number] | undefined;

          if (Array.isArray(lastCity.position) && lastCity.position.length === 2) {
            LngLatArray = [lastCity.position[0], lastCity.position[1]];
          }

          if (LngLatArray) {
            mapRef.current.flyTo(LngLatArray, 12);
          }
        } else if (mapRef.current) {
          mapRef.current.flyTo(memoizedRegionData.mapCenter, memoizedRegionData.mapZoom);
        }
      }

      return newDestinations;
    });
  }, [selectedCity, memoizedRegionData]);

  const togglePOI = useCallback((poi: PointOfInterest) => {
    setSelectedPOIs(prev =>
      prev.find(p => p.id === poi.id)
        ? prev.filter(p => p.id !== poi.id)
        : [...prev, poi]
    );
  }, []);

  const handleSearch = useCallback((term: string) => setSearchTerm(term), []);
  const handleFilterChange = useCallback((categories: string[]) => setSelectedCategories(categories), []);

  const handleRegionChange = useCallback((region: string) => {
    setCurrentRegion(region);
    setSelectedDestinations([]);
    setSelectedPOIs([]);
    setSelectedCity(null);
    mapRef.current?.flyTo(memoizedRegionData.mapCenter, memoizedRegionData.mapZoom);
  }, [memoizedRegionData]);

  const availableRegionsForHeader = useMemo(() => REGION_OPTIONS.map(opt => opt.value), []);

  const handleVehicleSelect = useCallback((vehicleId: string) => {
    const vehicle = vehicles.find(v => v.id === vehicleId);
    if (vehicle) {
      setSelectedVehicle(vehicle);
    }
    setShowVehicleSelectionModal(false);
  }, []);

  const handleCitySelect = useCallback((city: Destination | null) => {
    setSelectedCity(city);
    if (city && mapRef.current) {
      let LngLatArray: [number, number] | undefined;

      if (Array.isArray(city.position) && city.position.length === 2) {
        LngLatArray = [city.position[0], city.position[1]]; 
      } else if (typeof city.position === 'object' && city.position !== null) {
        // Use a more specific cast based on known possible structures of PositionObject
        const posObj = city.position as { lng?: number; lat?: number; lon?: number }; 
        if (typeof posObj.lng === 'number' && typeof posObj.lat === 'number') {
          LngLatArray = [posObj.lng, posObj.lat];
        } else if (typeof posObj.lon === 'number' && typeof posObj.lat === 'number') {
          LngLatArray = [posObj.lon, posObj.lat];
        }
      }

      if (LngLatArray) {
        mapRef.current.flyTo(LngLatArray, 12);
      } else {
        console.warn("Selected city has invalid or unhandled position format for flyTo:", city);
      }
    } else if (mapRef.current) {
      mapRef.current.flyTo(memoizedRegionData.mapCenter, memoizedRegionData.mapZoom);
    }
  }, [memoizedRegionData]);

  // Journey Parameters State - Updated for new framework
  const [numberOfTravelDays, setNumberOfTravelDays] = useState<number>(7);
  const [travelPace, setTravelPace] = useState<TravelPace>('balanced-explorer');
  const [journeyStyle, setJourneyStyle] = useState<JourneyStyle>('cultural-deep-dive');
  const [travelInterests, setTravelInterests] = useState<TravelInterest[]>([]);
  const [selectedPreArrangedJourney, setSelectedPreArrangedJourney] = useState<PreArrangedJourney | null>(null);

  // Sequential city selection state
  const [citySelectionStep, setCitySelectionStep] = useState<number>(0); // 0 = first city, 1 = second city, etc.
  const [isSelectingCity, setIsSelectingCity] = useState<boolean>(false);

  const handleNumberOfTravelDaysChange = useCallback((days: number) => {
    setNumberOfTravelDays(Math.max(1, Math.min(30, days)));
  }, []);

  const handleTravelPaceChange = useCallback((pace: TravelPace) => {
    setTravelPace(pace);
  }, []);

  const handleJourneyStyleChange = useCallback((style: JourneyStyle) => {
    setJourneyStyle(style);
  }, []);

  const handleTravelInterestToggle = useCallback((interest: TravelInterest) => {
    setTravelInterests(prevInterests => {
      if (prevInterests.includes(interest)) {
        return prevInterests.filter(i => i !== interest);
      }
      return [...prevInterests, interest];
    });
  }, []);

  const handlePreArrangedJourneySelect = useCallback((journey: PreArrangedJourney) => {
    setSelectedPreArrangedJourney(journey);
    setNumberOfTravelDays(journey.duration);
    setTravelPace(journey.pace);
    setJourneyStyle(journey.style);

    // Auto-select cities from the journey
    const journeyCities = memoizedRegionData.destinations.filter(dest =>
      journey.cities.includes(dest.name)
    );
    setSelectedDestinations(journeyCities);
    setCitySelectionStep(journeyCities.length);

    // Zoom to first city
    if (journeyCities.length > 0 && mapRef.current) {
      const firstCity = journeyCities[0];
      if (Array.isArray(firstCity.position) && firstCity.position.length === 2) {
        mapRef.current.flyTo([firstCity.position[0], firstCity.position[1]], 12);
      }
    }
  }, [memoizedRegionData.destinations]);

  const replaceSelectedDestinations = useCallback((destinations: Destination[]) => {
    setSelectedDestinations(destinations);
  }, []);

  // Determine current journey phase (assuming this state exists or will be added)
  // For now, let's mock it for the purpose of this fix. 
  // Replace this with actual state logic when JourneyPhase is fully integrated.
  const [currentJourneyPhase, setCurrentJourneyPhase] = useState<JourneyPhase>(JourneyPhase.INITIAL_CITY_SELECTION);
  // const currentJourneyPhase = JourneyPhase.NOT_STARTED; // Replace with actual state

  const isTravelAnimatorActive = useMemo(() => 
    currentJourneyPhase === JourneyPhase.JOURNEY,
    [currentJourneyPhase]
  );

  // New function to replace all selected destinations (for mobile modal)
  const replaceSelectedDestinationsMobile = useCallback((newDestinations: Destination[]) => {
    setSelectedDestinations(newDestinations);
  }, []);

  // State for mobile panel toggle
  const [isMobileView, setIsMobileView] = useState(typeof window !== 'undefined' ? window.innerWidth < 768 : false);
  const [showLeftPanelOnMobile, setShowLeftPanelOnMobile] = useState(false);

  // State for panel widths
  const [leftPanelWidth, setLeftPanelWidth] = useState(0);
  const [rightPanelWidth, setRightPanelWidth] = useState(0);

  // Refs for panel elements
  const leftPanelRef = useRef<HTMLDivElement>(null);
  const rightPanelRef = useRef<HTMLDivElement>(null);

  const handleMobilePanelToggle = useCallback(() => {
    if (showMobileItinerary) {
      setShowMobileItinerary(false);
      setTimeout(() => setShowMobileSidebar(true), 300); // Wait for exit animation
    } else if (showMobileSidebar) {
      setShowMobileSidebar(false);
      setTimeout(() => setShowMobileItinerary(true), 300); // Wait for exit animation
    } else {
      setShowMobileSidebar(true);
    }
  }, [showMobileItinerary, showMobileSidebar]);

  // Panel collapse handlers with smooth transitions
  const toggleLeftPanel = useCallback(() => {
    setIsLeftPanelCollapsed(prev => !prev);
  }, []);

  const toggleRightPanel = useCallback(() => {
    setIsRightPanelCollapsed(prev => !prev);
  }, []);

  const beginJourney = () => {
    if (mapRef.current) {
      mapRef.current.beginJourney();
      setCurrentJourneyPhase(JourneyPhase.JOURNEY); // Update phase
    } else {
      console.error("ExploreMap ref not available to begin journey.");
    }
  };

  // Add local state for showFilters and showSidebar
  const [showSidebar, setShowSidebar] = useState(false);

  return (
    <div className="relative h-screen w-screen overflow-hidden bg-background">
      <MainHeader
        currentRegion={currentRegion}
        availableRegions={availableRegionsForHeader}
        onRegionChange={handleRegionChange}
        showFilters={showFilters}
        setShowFilters={setShowFilters}
        showSidebar={showSidebar}
        setShowSidebar={setShowSidebar}
      />

      {/* Mobile Toggle Buttons */}
      {isMobile && (
        <div className="fixed bottom-4 left-1/2 -translate-x-1/2 z-50 flex gap-2">
          <Button
            variant="secondary"
            size="sm"
            onClick={() => setShowMobileSidebar(prev => !prev)}
            className="shadow-lg"
          >
            {showMobileSidebar ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => setShowMobileItinerary(prev => !prev)}
            className="shadow-lg"
          >
            {showMobileItinerary ? <X className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
          </Button>
        </div>
      )}

      {/* Main Layout */}
      <div className="relative flex h-[calc(100vh-4rem)] w-full">
        {/* Left Panel */}
        <div
          ref={leftPanelRef}
          className={cn(
            "absolute md:relative h-full bg-background transition-all duration-300 ease-in-out z-40 shadow-lg",
            isMobile ? (
              showMobileSidebar ? "left-0 w-4/5 sm:w-3/5 md:w-80" : "-left-full"
            ) : (
              isLeftPanelCollapsed ? "w-0 opacity-0 pointer-events-none" : "w-80 opacity-100"
            )
          )}
        >
          {!isMobile && (
            <button
              onClick={toggleLeftPanel}
              className="absolute -right-6 top-1/2 -translate-y-1/2 bg-background p-1 rounded-r shadow-lg z-50"
            >
              {isLeftPanelCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
            </button>
          )}
          <LeftPOIPanel
            pointsOfInterest={sidebarFilteredPOIs}
            selectedPOIs={selectedPOIs}
            onPOIClick={togglePOI}
            onAddPOI={togglePOI}
            onRemovePOI={togglePOI}
            onClosePanel={() => setShowMobileSidebar(false)}
            isMobile={isMobile}
            isCollapsed={isLeftPanelCollapsed}
          />
        </div>

        {/* Map Container */}
        <div className="flex-1 relative">
          <ExploreMap
            ref={mapRef}
            citiesForCountry={memoizedRegionData.destinations}
            poisForCurrentSelection={allPointsOfInterestForRegion}
            selectedDestinations={selectedDestinations}
            selectedPOIs={selectedPOIs}
            onDestinationSelect={toggleDestination}
            onPOISelect={togglePOI}
            onPOIDeselect={togglePOI}
            mapCenter={HARDCODED_MAP_CENTER}
            mapZoom={memoizedRegionData.mapZoom}
            maxBounds={HARDCODED_MAX_BOUNDS}
            onCitySelect={handleCitySelect}
            selectedCity={selectedCity}
            isMobile={isMobile}
            showLeftPanelCommand={showMobileSidebar}
            onCloseLeftPanel={() => setShowMobileSidebar(false)}
            isLeftPanelCollapsed={isLeftPanelCollapsed}
            onMapReady={setHomePageMapInstance}
          />
        </div>

        {/* Right Panel - New Itinerary Panel */}
        <div
          ref={rightPanelRef}
          className={cn(
            "absolute md:relative h-full transition-all duration-300 ease-in-out z-30",
            isMobile ? (
              showMobileItinerary ? "right-0 w-4/5 sm:w-3/5" : "-right-full"
            ) : (
              isRightPanelCollapsed ? "w-0 opacity-0 pointer-events-none" : "opacity-100"
            )
          )}
        >
          {!isMobile && (
            <button
              onClick={toggleRightPanel}
              className="absolute -left-6 top-1/2 -translate-y-1/2 bg-background p-1 rounded-l shadow-lg z-50"
            >
              {isRightPanelCollapsed ? <ChevronLeft className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </button>
          )}
          <ItineraryPanel
            numberOfDays={numberOfTravelDays}
            travelPace={travelPace}
            journeyStyle={journeyStyle}
            selectedInterests={travelInterests}
            selectedCities={selectedDestinations}
            availableCities={memoizedRegionData.destinations}
            preArrangedJourneys={getRecommendedJourneys(numberOfTravelDays, travelInterests.map(i => i.toString()))}
            onNumberOfDaysChange={handleNumberOfTravelDaysChange}
            onTravelPaceChange={handleTravelPaceChange}
            onJourneyStyleChange={handleJourneyStyleChange}
            onInterestToggle={handleTravelInterestToggle}
            onCitySelect={handleSequentialCitySelection}
            onCityRemove={handleCityRemove}
            onPreArrangedJourneySelect={handlePreArrangedJourneySelect}
            onBeginJourney={beginJourney}
            canBeginJourney={selectedDestinations.length >= 2 || selectedPreArrangedJourney !== null}
            isCollapsed={isRightPanelCollapsed}
          />
        </div>
      </div>

      {/* Vehicle Selection Modal */}
      {showVehicleSelectionModal && (
        <VehicleSelector
          vehicles={vehicles}
          selectedVehicleId={selectedVehicle.id}
          onVehicleSelect={handleVehicleSelect}
        />
      )}

      {/* Debug Tools - Conditionally render DebugPanel */}
      {showDebugPanel && homePageMapInstance && (
        <div className="absolute bottom-0 left-0 right-0 z-50 p-4 bg-gray-800 bg-opacity-90 text-white">
          <AnimationDebugPanel map={homePageMapInstance} />
        </div>
      )}
    </div>
  );
};

export default HomePage;

/*
// ======= ORIGINAL HomePage.tsx CONTENT (FOR REFERENCE - ALL OF THIS IS EFFECTIVELY COMMENTED OUT/REPLACED) =======

// import { regionData } from '@/data/destinations';
// import { pointsOfInterest as importedAllPointsOfInterest } from '@/data/pointsOfInterest';
// import { Position, calculateDistance } from '../types/Position';
// import { convertPOIToPointOfInterest, filterPOIsForCity, normalizeDestination } from '../utils/poiUtils';
// import MainHeader from '@/components/MainHeader';
// import SearchFilters from '@/components/SearchFilters';
// import RightSidebar from '@/components/map/RightSidebar';
// import { Button } from '@/components/ui/button';
// import { X } from 'lucide-react';
// import VehicleSelector from '@/components/VehicleSelector';
// import { vehicles } from '@/data/vehicles';

// const DEFAULT_REGION = 'morocco';
// const REGION_OPTIONS = [
//   { label: 'Morocco', value: 'morocco' },
//   { label: 'Portugal', value: 'portugal' },
// ];

// const ensureDestinationDefaults = (dest: any): Destination => ({ ... });

// const HomePageOriginal = () => {
//   const [currentRegion, setCurrentRegion] = useState<string>(DEFAULT_REGION);
//   const [selectedDestinations, setSelectedDestinations] = useState<Destination[]>([]);
//   const [selectedPOIs, setSelectedPOIs] = useState<PointOfInterest[]>([]);
//   const [mapRef, setMapRef] = useState<any>(null); // Or specific Mapbox type
//   const [searchTerm, setSearchTerm] = useState('');
//   const [selectedCategories, setSelectedCategories] = useState<string[]>([]); // For POI filtering
//   const [showFilters, setShowFilters] = useState(false);
//   const [showVehicleSelectionModal, setShowVehicleSelectionModal] = useState(false);
//   const [selectedVehicle, setSelectedVehicle] = useState(vehicles[0]);
//   const [isLoading, setIsLoading] = useState(false);
//   const [selectedCity, setSelectedCity] = useState<Destination | null>(null);

//   const memoizedRegionData = useMemo(() => regionData[currentRegion] || regionData[DEFAULT_REGION], [currentRegion]);

//   const allPointsOfInterestForRegion = useMemo(() => {
//     const pois = importedAllPointsOfInterest[currentRegion] || [];
//     return pois.map(poi => convertPOIToPointOfInterest(poi));
//   }, [currentRegion]);

//   const sidebarFilteredPOIs = useMemo(() => {
//      // ... (original filtering logic based on searchTerm, selectedCategories, etc.)
//      return allPointsOfInterestForRegion; // Simplified for now
//   }, [allPointsOfInterestForRegion, searchTerm, selectedCategories, selectedCity]);

//   const toggleDestination = useCallback((destination: Destination) => { ... }, [selectedDestinations]);
//   const togglePOI = useCallback((poi: PointOfInterest) => { ... }, [selectedPOIs]);
//   const handleSearch = useCallback((term: string) => setSearchTerm(term), []);
//   const handleFilterChange = useCallback((categories: string[]) => setSelectedCategories(categories), []);
//   const handleRegionChange = useCallback((region: string) => { ... }, []);
//   const handleVehicleSelect = useCallback((vehicleId: string) => { ... }, []);
//   const handleCitySelect = useCallback((city: Destination) => { ... }, []);

//   useEffect(() => { // ... effect for something ... // }, []);

//   return (
//     <div className="flex flex-col h-screen">
//       <MainHeader ... />
//       <div className="flex flex-1 pt-16">
//         <SearchFilters ... />
//         <div className="flex-1 relative">
//           <ExploreMap ... (original props) ... />
//           <RightSidebar ... />
//         </div>
//       </div>
//       {showVehicleSelectionModal && <VehicleSelector ... />}
//     </div>
//   );
// };
*/
