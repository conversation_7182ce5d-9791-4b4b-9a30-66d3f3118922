
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { UserCog, ArrowLeft } from 'lucide-react';
import ProfileForm, { ProfileFormValues } from '@/components/profile/ProfileForm';
import ProfileAvatar from '@/components/profile/ProfileAvatar';

const Profile = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [formValues, setFormValues] = useState<ProfileFormValues>({
    fullName: '',
    email: '',
    avatarUrl: '',
  });
  
  useEffect(() => {
    if (!user) {
      navigate('/auth');
      return;
    }

    const fetchProfile = async () => {
      setIsLoading(true);
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (error) throw error;

        setFormValues({
          fullName: data?.full_name || user?.user_metadata?.full_name || '',
          email: data?.email || user?.email || '',
          avatarUrl: data?.avatar_url || '',
        });
      } catch (error: any) {
        console.error('Error fetching profile:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProfile();
  }, [user, navigate]);

  const getInitials = () => {
    if (formValues.fullName) {
      return formValues.fullName
        .split(' ')
        .map(name => name[0])
        .join('')
        .toUpperCase()
        .substring(0, 2);
    }
    return user?.email?.substring(0, 2).toUpperCase() || 'U';
  };

  return (
    <div className="container max-w-3xl mx-auto py-8 px-4 min-h-screen">
      <Button 
        variant="ghost" 
        className="mb-6 p-0" 
        onClick={() => navigate('/')}
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Home
      </Button>

      <Card>
        <CardHeader className="space-y-1">
          <div className="flex items-center gap-4">
            <UserCog className="h-6 w-6 text-morocco-terracotta" />
            <CardTitle className="text-2xl font-bold">Your Profile</CardTitle>
          </div>
          <CardDescription>
            Update your personal information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-8 items-start">
            <ProfileAvatar 
              avatarUrl={formValues.avatarUrl} 
              getInitials={getInitials} 
            />
            {user && (
              <ProfileForm 
                user={user} 
                initialValues={formValues}
                isLoading={isLoading}
                setIsLoading={setIsLoading}
              />
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Profile;
