/**
 * Morocco Framework Demo
 * 
 * Morocco theme using the Universal Framework
 * This demonstrates how all themes should be structured
 */

import React from 'react';
import UniversalFramework from '@/components/framework/UniversalFramework';
import { regionData } from '../../data/destinations';
import { convertPOIToPointOfInterest, normalizeDestination } from '@/utils/poiUtils';

// Morocco map settings
const MOROCCO_CENTER: [number, number] = [-6.0, 31.8];
const MOROCCO_BOUNDS: [[number, number], [number, number]] = [
  [-17.0, 21.0], // Southwest coordinates
  [0.0, 36.0]    // Northeast coordinates
];

// Get Morocco data
const getMoroccoData = () => {
  const moroccoRegionData = regionData['morocco'];
  if (!moroccoRegionData) {
    console.error('Morocco region data not found');
    return { destinations: [], pointsOfInterest: [] };
  }

  return {
    destinations: moroccoRegionData.destinations.map(dest => normalizeDestination(dest)),
    pointsOfInterest: moroccoRegionData.pointsOfInterest.map(poi => convertPOIToPointOfInterest(poi))
  };
};

const { destinations, pointsOfInterest } = getMoroccoData();

const MoroccoFrameworkDemo: React.FC = () => {
  return (
    <UniversalFramework
      clientId="morocco"
      destinations={destinations}
      pointsOfInterest={pointsOfInterest}
      regionName="Morocco"
      availableRegions={['Morocco', 'Theme Portfolio']}
      mapCenter={MOROCCO_CENTER}
      mapBounds={MOROCCO_BOUNDS}
      customTitle="Come To Morocco"
      customDescription="Discover the magic of Morocco through immersive travel experiences"
      className="morocco-framework-demo"
    />
  );
};

export default MoroccoFrameworkDemo;
