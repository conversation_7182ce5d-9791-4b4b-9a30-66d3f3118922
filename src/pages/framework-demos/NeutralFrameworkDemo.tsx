/**
 * Neutral Framework Demo
 * 
 * Professional neutral theme using the Universal Framework
 * This is the central "skeleton" version for development
 */

import React from 'react';
import UniversalFramework from '@/components/framework/UniversalFramework';
import { Destination, PointOfInterest } from '../../types';

// Sample neutral data for demo (not region-specific)
const DEMO_DESTINATIONS: Destination[] = [
  {
    id: 'demo-city-1',
    name: 'Metropolitan City',
    description: 'A vibrant urban center with rich culture and history',
    position: [-74.006, 40.7128], // New York coordinates as example
    image: '/assets/neutral/cities/metro-city.jpg',
    type: 'city'
  },
  {
    id: 'demo-city-2',
    name: 'Coastal Town',
    description: 'Charming seaside destination with beautiful beaches',
    position: [-118.2437, 34.0522], // Los Angeles coordinates as example
    image: '/assets/neutral/cities/coastal-town.jpg',
    type: 'city'
  },
  {
    id: 'demo-city-3',
    name: 'Mountain Village',
    description: 'Peaceful mountain retreat with stunning views',
    position: [-105.2705, 40.0150], // Boulder, Colorado coordinates
    image: '/assets/neutral/cities/mountain-village.jpg',
    type: 'city'
  },
  {
    id: 'demo-city-4',
    name: 'Historic District',
    description: 'Rich historical heritage and cultural landmarks',
    position: [-71.0589, 42.3601], // Boston coordinates
    image: '/assets/neutral/cities/historic-district.jpg',
    type: 'city'
  }
];

const DEMO_POIS: PointOfInterest[] = [
  {
    id: 'demo-poi-1',
    name: 'Central Museum',
    description: 'World-class museum with extensive collections',
    coordinates: [-74.006, 40.7128],
    category: 'landmark',
    image: '/assets/neutral/pois/museum.jpg',
    tags: ['culture', 'history', 'art'],
    rating: 4.8,
    cost: 'medium'
  },
  {
    id: 'demo-poi-2',
    name: 'Waterfront Park',
    description: 'Beautiful park with scenic water views',
    coordinates: [-118.2437, 34.0522],
    category: 'nature',
    image: '/assets/neutral/pois/park.jpg',
    tags: ['nature', 'relaxation', 'views'],
    rating: 4.6,
    cost: 'low'
  },
  {
    id: 'demo-poi-3',
    name: 'Gourmet Restaurant',
    description: 'Fine dining with locally sourced ingredients',
    coordinates: [-105.2705, 40.0150],
    category: 'restaurant',
    image: '/assets/neutral/pois/restaurant.jpg',
    tags: ['dining', 'local cuisine', 'fine dining'],
    rating: 4.9,
    cost: 'high'
  },
  {
    id: 'demo-poi-4',
    name: 'Adventure Center',
    description: 'Outdoor activities and adventure sports',
    coordinates: [-71.0589, 42.3601],
    category: 'activity',
    image: '/assets/neutral/pois/adventure.jpg',
    tags: ['adventure', 'outdoor', 'sports'],
    rating: 4.7,
    cost: 'medium'
  }
];

const NeutralFrameworkDemo: React.FC = () => {
  return (
    <UniversalFramework
      clientId="neutral"
      destinations={DEMO_DESTINATIONS}
      pointsOfInterest={DEMO_POIS}
      regionName="Global Framework"
      availableRegions={['Global Framework']}
      mapCenter={[-98.5795, 39.8283]} // Center of USA
      customTitle="Travel Framework Demo"
      customDescription="Professional travel planning framework for client presentations"
      className="neutral-framework-demo"
    />
  );
};

export default NeutralFrameworkDemo;
