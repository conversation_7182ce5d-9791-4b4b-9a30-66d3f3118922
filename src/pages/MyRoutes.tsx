
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { fetchUserRoutes, deleteRoute } from '@/utils/supabaseHelpers';
import { SavedRoute } from '@/components/RouteCard';
import RouteList from '@/components/RouteList';

const MyRoutes = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [savedRoutes, setSavedRoutes] = useState<SavedRoute[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!user) {
      navigate('/auth');
      return;
    }

    const loadSavedRoutes = async () => {
      setIsLoading(true);
      try {
        const { data, error } = await fetchUserRoutes(user.id);
        
        if (error) throw error;
        setSavedRoutes(data);
      } catch (error: any) {
        console.error('Error fetching saved routes:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadSavedRoutes();
  }, [user, navigate]);

  const handleDeleteRoute = async (routeId: string) => {
    try {
      const { success, error } = await deleteRoute(routeId);
      
      if (!success) throw error;

      setSavedRoutes(savedRoutes.filter(route => route.id !== routeId));
      console.log("Route deleted successfully");
    } catch (error: any) {
      console.error('Error deleting route:', error);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4 min-h-screen pb-20">
      <Button 
        variant="ghost" 
        className="mb-6 p-0" 
        onClick={() => navigate('/')}
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Home
      </Button>

      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-morocco-terracotta">My Saved Routes</h1>
          <p className="text-muted-foreground">View and manage your saved Morocco travel routes</p>
        </div>
        <Button 
          className="bg-morocco-terracotta hover:bg-morocco-terracotta/90"
          onClick={() => navigate('/')}
        >
          Create New Route
        </Button>
      </div>

      <RouteList 
        routes={savedRoutes}
        isLoading={isLoading}
        onDeleteRoute={handleDeleteRoute}
      />
    </div>
  );
};

export default MyRoutes;
