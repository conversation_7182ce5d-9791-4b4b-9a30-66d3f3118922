/**
 * AI-Powered POI Recommendation Service
 * Provides intelligent suggestions based on user preferences and existing POI data
 */

import { POI, Destination } from '../../types/Destination';

interface UserPreferences {
  interests: string[];
  selectedCities: Destination[];
  tripDuration: number;
  travelStyle: 'luxury' | 'budget' | 'adventure' | 'cultural' | 'romantic';
  groupSize: number;
  budget?: 'low' | 'medium' | 'high';
}

interface POIRecommendation {
  poi: POI;
  score: number;
  reason: string;
  category: string;
  priority: 'high' | 'medium' | 'low';
}

interface RecommendationResponse {
  recommendations: POIRecommendation[];
  totalFound: number;
  categories: string[];
}

export class POIRecommendationService {
  private static instance: POIRecommendationService;
  private openAIApiKey: string;

  private constructor() {
    this.openAIApiKey = import.meta.env.VITE_OPENAI_API_KEY || '';
  }

  public static getInstance(): POIRecommendationService {
    if (!POIRecommendationService.instance) {
      POIRecommendationService.instance = new POIRecommendationService();
    }
    return POIRecommendationService.instance;
  }

  /**
   * Get AI-powered POI recommendations
   */
  public async getRecommendations(
    preferences: UserPreferences,
    availablePOIs: POI[]
  ): Promise<RecommendationResponse> {
    try {
      // First, filter POIs by selected cities
      const cityPOIs = this.filterPOIsByCities(availablePOIs, preferences.selectedCities);
      
      // Create AI prompt for recommendations
      const prompt = this.createRecommendationPrompt(preferences, cityPOIs);
      
      // Call OpenAI API
      const aiResponse = await this.callOpenAI(prompt);
      
      // Parse and score recommendations
      const recommendations = this.parseAIResponse(aiResponse, cityPOIs);
      
      // Sort by score and priority
      const sortedRecommendations = this.sortRecommendations(recommendations);
      
      return {
        recommendations: sortedRecommendations.slice(0, 10), // Top 10
        totalFound: sortedRecommendations.length,
        categories: [...new Set(sortedRecommendations.map(r => r.category))]
      };
    } catch (error) {
      console.error('POI Recommendation Service Error:', error);
      // Fallback to rule-based recommendations
      return this.getFallbackRecommendations(preferences, availablePOIs);
    }
  }

  /**
   * Filter POIs by selected cities
   */
  private filterPOIsByCities(pois: POI[], cities: Destination[]): POI[] {
    const cityIds = cities.map(city => city.id);
    return pois.filter(poi => 
      cityIds.some(cityId => 
        poi.location?.toLowerCase().includes(cityId.toLowerCase()) ||
        poi.region?.toLowerCase().includes(cityId.toLowerCase())
      )
    );
  }

  /**
   * Create AI prompt for recommendations
   */
  private createRecommendationPrompt(preferences: UserPreferences, pois: POI[]): string {
    const poiList = pois.map(poi => 
      `${poi.name}: ${poi.description} (Category: ${poi.category}, Tags: ${poi.tags?.join(', ')})`
    ).join('\n');

    return `
You are a travel expert AI. Based on the user's preferences, recommend the best POIs from the provided list.

User Preferences:
- Interests: ${preferences.interests.join(', ')}
- Travel Style: ${preferences.travelStyle}
- Trip Duration: ${preferences.tripDuration} days
- Group Size: ${preferences.groupSize}
- Cities: ${preferences.selectedCities.map(c => c.name).join(', ')}

Available POIs:
${poiList}

Please recommend the top 10 POIs that best match the user's preferences. For each recommendation, provide:
1. POI name (exact match from the list)
2. Score (1-100)
3. Reason (why it matches their interests)
4. Category
5. Priority (high/medium/low)

Format your response as JSON:
{
  "recommendations": [
    {
      "name": "POI Name",
      "score": 85,
      "reason": "Perfect for history lovers with stunning architecture",
      "category": "Historical",
      "priority": "high"
    }
  ]
}
`;
  }

  /**
   * Call OpenAI API
   */
  private async callOpenAI(prompt: string): Promise<string> {
    if (!this.openAIApiKey) {
      throw new Error('OpenAI API key not configured');
    }

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini', // Cost-effective model
        messages: [
          {
            role: 'system',
            content: 'You are a travel expert AI that provides personalized POI recommendations.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 1500,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0]?.message?.content || '';
  }

  /**
   * Parse AI response and match with actual POIs
   */
  private parseAIResponse(aiResponse: string, availablePOIs: POI[]): POIRecommendation[] {
    try {
      const parsed = JSON.parse(aiResponse);
      const recommendations: POIRecommendation[] = [];

      for (const rec of parsed.recommendations) {
        const matchedPOI = availablePOIs.find(poi => 
          poi.name.toLowerCase() === rec.name.toLowerCase()
        );

        if (matchedPOI) {
          recommendations.push({
            poi: matchedPOI,
            score: rec.score,
            reason: rec.reason,
            category: rec.category,
            priority: rec.priority
          });
        }
      }

      return recommendations;
    } catch (error) {
      console.error('Failed to parse AI response:', error);
      return [];
    }
  }

  /**
   * Sort recommendations by score and priority
   */
  private sortRecommendations(recommendations: POIRecommendation[]): POIRecommendation[] {
    const priorityWeight = { high: 3, medium: 2, low: 1 };
    
    return recommendations.sort((a, b) => {
      const aWeight = a.score + (priorityWeight[a.priority] * 10);
      const bWeight = b.score + (priorityWeight[b.priority] * 10);
      return bWeight - aWeight;
    });
  }

  /**
   * Fallback rule-based recommendations when AI fails
   */
  private getFallbackRecommendations(
    preferences: UserPreferences, 
    availablePOIs: POI[]
  ): RecommendationResponse {
    const cityPOIs = this.filterPOIsByCities(availablePOIs, preferences.selectedCities);
    
    // Simple rule-based scoring
    const recommendations = cityPOIs.map(poi => ({
      poi,
      score: this.calculateRuleBasedScore(poi, preferences),
      reason: `Matches your interest in ${preferences.interests[0]}`,
      category: poi.category || 'General',
      priority: 'medium' as const
    }));

    const sorted = recommendations
      .filter(r => r.score > 50)
      .sort((a, b) => b.score - a.score);

    return {
      recommendations: sorted.slice(0, 10),
      totalFound: sorted.length,
      categories: [...new Set(sorted.map(r => r.category))]
    };
  }

  /**
   * Calculate rule-based score for fallback
   */
  private calculateRuleBasedScore(poi: POI, preferences: UserPreferences): number {
    let score = 50; // Base score

    // Interest matching
    preferences.interests.forEach(interest => {
      if (poi.tags?.some(tag => tag.toLowerCase().includes(interest.toLowerCase()))) {
        score += 20;
      }
      if (poi.description?.toLowerCase().includes(interest.toLowerCase())) {
        score += 15;
      }
    });

    // Travel style matching
    if (preferences.travelStyle === 'luxury' && poi.tags?.includes('luxury')) {
      score += 25;
    }
    if (preferences.travelStyle === 'adventure' && poi.tags?.includes('adventure')) {
      score += 25;
    }

    return Math.min(score, 100);
  }
}

export default POIRecommendationService;
