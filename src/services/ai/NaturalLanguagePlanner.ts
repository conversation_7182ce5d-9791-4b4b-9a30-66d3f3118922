/**
 * Natural Language Trip Planning Service
 * Parses user intent and auto-selects cities/POIs based on natural language input
 */

import { Destination, POI } from '../../types/Destination';

interface TripIntent {
  duration: number;
  travelStyle: string;
  interests: string[];
  groupType: string;
  budget: string;
  season: string;
  cities: string[];
  activities: string[];
}

interface PlanningResult {
  intent: TripIntent;
  suggestedCities: Destination[];
  suggestedPOIs: POI[];
  itinerary: {
    day: number;
    city: string;
    activities: string[];
    pois: POI[];
  }[];
  confidence: number;
}

export class NaturalLanguagePlanner {
  private static instance: NaturalLanguagePlanner;
  private openAIApiKey: string;

  private constructor() {
    this.openAIApiKey = import.meta.env.VITE_OPENAI_API_KEY || '';
  }

  public static getInstance(): NaturalLanguagePlanner {
    if (!NaturalLanguagePlanner.instance) {
      NaturalLanguagePlanner.instance = new NaturalLanguagePlanner();
    }
    return NaturalLanguagePlanner.instance;
  }

  /**
   * Parse natural language input and create trip plan
   */
  public async planTrip(
    userInput: string,
    availableCities: Destination[],
    availablePOIs: POI[]
  ): Promise<PlanningResult> {
    try {
      // Step 1: Extract intent from natural language
      const intent = await this.extractIntent(userInput);
      
      // Step 2: Match cities based on intent
      const suggestedCities = this.matchCities(intent, availableCities);
      
      // Step 3: Match POIs based on intent and cities
      const suggestedPOIs = this.matchPOIs(intent, suggestedCities, availablePOIs);
      
      // Step 4: Create day-by-day itinerary
      const itinerary = await this.createItinerary(intent, suggestedCities, suggestedPOIs);
      
      return {
        intent,
        suggestedCities,
        suggestedPOIs,
        itinerary,
        confidence: this.calculateConfidence(intent, suggestedCities, suggestedPOIs)
      };
    } catch (error) {
      console.error('Natural Language Planning Error:', error);
      throw error;
    }
  }

  /**
   * Extract structured intent from natural language
   */
  private async extractIntent(userInput: string): Promise<TripIntent> {
    const prompt = `
Extract trip planning information from this user request: "${userInput}"

Please identify and extract:
1. Trip duration (number of days)
2. Travel style (luxury, budget, adventure, cultural, romantic, family)
3. Interests/activities (history, nature, food, shopping, nightlife, etc.)
4. Group type (solo, couple, family, friends)
5. Budget level (low, medium, high)
6. Preferred season/time (if mentioned)
7. Specific cities mentioned
8. Specific activities mentioned

Return as JSON:
{
  "duration": 7,
  "travelStyle": "romantic",
  "interests": ["history", "food", "architecture"],
  "groupType": "couple",
  "budget": "medium",
  "season": "spring",
  "cities": ["marrakech", "fez"],
  "activities": ["cooking class", "desert tour"]
}

If information is not specified, use reasonable defaults or empty arrays.
`;

    const response = await this.callOpenAI(prompt);
    
    try {
      return JSON.parse(response);
    } catch (error) {
      // Fallback parsing
      return this.fallbackIntentExtraction(userInput);
    }
  }

  /**
   * Match cities based on extracted intent
   */
  private matchCities(intent: TripIntent, availableCities: Destination[]): Destination[] {
    const matches: Destination[] = [];
    
    // First, add explicitly mentioned cities
    intent.cities.forEach(cityName => {
      const city = availableCities.find(c => 
        c.name.toLowerCase().includes(cityName.toLowerCase()) ||
        c.id.toLowerCase().includes(cityName.toLowerCase())
      );
      if (city && !matches.find(m => m.id === city.id)) {
        matches.push(city);
      }
    });

    // Then, add cities based on interests and travel style
    if (matches.length < 2) {
      const additionalCities = availableCities.filter(city => {
        if (matches.find(m => m.id === city.id)) return false;
        
        // Match based on interests
        const cityDescription = (city.description || '').toLowerCase();
        const cityTags = (city.tags || []).map(tag => tag.toLowerCase());
        
        return intent.interests.some(interest => 
          cityDescription.includes(interest.toLowerCase()) ||
          cityTags.some(tag => tag.includes(interest.toLowerCase()))
        );
      });

      // Add top matches up to desired duration
      const maxCities = Math.min(intent.duration <= 3 ? 2 : intent.duration <= 7 ? 3 : 4, 
                                availableCities.length);
      matches.push(...additionalCities.slice(0, maxCities - matches.length));
    }

    return matches;
  }

  /**
   * Match POIs based on intent and selected cities
   */
  private matchPOIs(intent: TripIntent, cities: Destination[], availablePOIs: POI[]): POI[] {
    const cityIds = cities.map(c => c.id);
    
    // Filter POIs by cities
    const cityPOIs = availablePOIs.filter(poi =>
      cityIds.some(cityId =>
        poi.location?.toLowerCase().includes(cityId.toLowerCase()) ||
        poi.region?.toLowerCase().includes(cityId.toLowerCase())
      )
    );

    // Score POIs based on interests
    const scoredPOIs = cityPOIs.map(poi => ({
      poi,
      score: this.scorePOI(poi, intent)
    }));

    // Sort by score and return top matches
    const maxPOIs = intent.duration * 3; // ~3 POIs per day
    return scoredPOIs
      .sort((a, b) => b.score - a.score)
      .slice(0, maxPOIs)
      .map(item => item.poi);
  }

  /**
   * Score POI based on intent match
   */
  private scorePOI(poi: POI, intent: TripIntent): number {
    let score = 0;

    // Interest matching
    intent.interests.forEach(interest => {
      if (poi.tags?.some(tag => tag.toLowerCase().includes(interest.toLowerCase()))) {
        score += 20;
      }
      if (poi.description?.toLowerCase().includes(interest.toLowerCase())) {
        score += 15;
      }
      if (poi.category?.toLowerCase().includes(interest.toLowerCase())) {
        score += 10;
      }
    });

    // Travel style matching
    const styleKeywords = {
      luxury: ['luxury', 'premium', 'exclusive', 'upscale'],
      budget: ['free', 'cheap', 'budget', 'affordable'],
      adventure: ['adventure', 'hiking', 'climbing', 'extreme'],
      cultural: ['cultural', 'traditional', 'heritage', 'museum'],
      romantic: ['romantic', 'sunset', 'intimate', 'couples']
    };

    const keywords = styleKeywords[intent.travelStyle as keyof typeof styleKeywords] || [];
    keywords.forEach(keyword => {
      if (poi.description?.toLowerCase().includes(keyword) ||
          poi.tags?.some(tag => tag.toLowerCase().includes(keyword))) {
        score += 15;
      }
    });

    return score;
  }

  /**
   * Create day-by-day itinerary
   */
  private async createItinerary(
    intent: TripIntent,
    cities: Destination[],
    pois: POI[]
  ): Promise<PlanningResult['itinerary']> {
    const itinerary: PlanningResult['itinerary'] = [];
    
    // Distribute days across cities
    const daysPerCity = Math.ceil(intent.duration / cities.length);
    
    let currentDay = 1;
    for (const city of cities) {
      const cityPOIs = pois.filter(poi =>
        poi.location?.toLowerCase().includes(city.id.toLowerCase()) ||
        poi.region?.toLowerCase().includes(city.id.toLowerCase())
      );

      const cityDays = Math.min(daysPerCity, intent.duration - currentDay + 1);
      
      for (let day = 0; day < cityDays && currentDay <= intent.duration; day++) {
        const dayPOIs = cityPOIs.slice(day * 2, (day + 1) * 2); // 2 POIs per day
        
        itinerary.push({
          day: currentDay,
          city: city.name,
          activities: this.generateDayActivities(intent, dayPOIs),
          pois: dayPOIs
        });
        
        currentDay++;
      }
    }

    return itinerary;
  }

  /**
   * Generate activities for a day
   */
  private generateDayActivities(intent: TripIntent, pois: POI[]): string[] {
    const activities: string[] = [];
    
    // Add POI-based activities
    pois.forEach(poi => {
      activities.push(`Visit ${poi.name}`);
    });

    // Add interest-based activities
    intent.activities.forEach(activity => {
      if (!activities.some(a => a.toLowerCase().includes(activity.toLowerCase()))) {
        activities.push(activity);
      }
    });

    return activities;
  }

  /**
   * Calculate confidence score
   */
  private calculateConfidence(intent: TripIntent, cities: Destination[], pois: POI[]): number {
    let confidence = 50; // Base confidence

    // Boost confidence based on matches
    if (cities.length >= 2) confidence += 20;
    if (pois.length >= intent.duration) confidence += 20;
    if (intent.cities.length > 0) confidence += 10;

    return Math.min(confidence, 100);
  }

  /**
   * Fallback intent extraction when AI fails
   */
  private fallbackIntentExtraction(userInput: string): TripIntent {
    const input = userInput.toLowerCase();
    
    // Extract duration
    const durationMatch = input.match(/(\d+)\s*(day|week)/);
    const duration = durationMatch ? 
      (durationMatch[2] === 'week' ? parseInt(durationMatch[1]) * 7 : parseInt(durationMatch[1])) : 7;

    // Extract basic interests
    const interests: string[] = [];
    const interestKeywords = ['history', 'culture', 'food', 'nature', 'adventure', 'shopping', 'art'];
    interestKeywords.forEach(keyword => {
      if (input.includes(keyword)) interests.push(keyword);
    });

    return {
      duration,
      travelStyle: 'cultural', // Default
      interests,
      groupType: 'couple', // Default
      budget: 'medium', // Default
      season: '',
      cities: [],
      activities: []
    };
  }

  /**
   * Call OpenAI API
   */
  private async callOpenAI(prompt: string): Promise<string> {
    if (!this.openAIApiKey) {
      throw new Error('OpenAI API key not configured');
    }

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: 'You are a travel planning AI that extracts structured information from natural language.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 800,
        temperature: 0.3,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0]?.message?.content || '';
  }
}

export default NaturalLanguagePlanner;
