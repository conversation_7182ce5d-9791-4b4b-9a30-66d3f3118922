/**
 * Travel Insights Engine
 * 
 * Provides intelligent analysis and recommendations for travel itineraries.
 * Includes balance analysis, time estimates, seasonal considerations,
 * and personalized recommendations based on user preferences.
 */

import { PointOfInterest, Destination } from '@/types';
import { TravelInterest, TravelPace, JourneyStyle } from '@/types/ItineraryParameters';
import * as turf from '@turf/turf';

export interface ItineraryBalance {
  urbanVsRural: number; // 0-100, 50 is balanced
  culturalVsNature: number; // 0-100, 50 is balanced
  activeVsRelaxed: number; // 0-100, 50 is balanced
  budgetDistribution: {
    free: number;
    budget: number; // 0-20 EUR
    moderate: number; // 20-50 EUR
    premium: number; // 50+ EUR
  };
  categoryDistribution: Record<string, number>;
}

export interface TimeEstimates {
  totalDrivingTime: number; // hours
  totalVisitTime: number; // hours
  recommendedDuration: number; // days
  dailyBreakdown: {
    day: number;
    destinations: string[];
    drivingTime: number;
    visitTime: number;
    activities: string[];
  }[];
}

export interface SeasonalRecommendations {
  bestMonths: string[];
  weatherConsiderations: string[];
  seasonalHighlights: string[];
  crowdLevels: 'low' | 'moderate' | 'high';
  pricingImpact: 'budget' | 'standard' | 'premium';
}

export interface PersonalizedRecommendations {
  additionalPOIs: PointOfInterest[];
  routeOptimizations: string[];
  experienceEnhancements: string[];
  budgetTips: string[];
  localInsights: string[];
}

export interface TravelInsights {
  balance: ItineraryBalance;
  timeEstimates: TimeEstimates;
  seasonalRecommendations: SeasonalRecommendations;
  personalizedRecommendations: PersonalizedRecommendations;
  overallScore: number; // 0-100
  improvements: string[];
}

export class TravelInsightsEngine {
  private static instance: TravelInsightsEngine | null = null;

  private constructor() {}

  public static getInstance(): TravelInsightsEngine {
    if (!TravelInsightsEngine.instance) {
      TravelInsightsEngine.instance = new TravelInsightsEngine();
    }
    return TravelInsightsEngine.instance;
  }

  /**
   * Generate comprehensive travel insights for an itinerary
   */
  public generateInsights(
    destinations: Destination[],
    pois: PointOfInterest[],
    preferences: {
      pace: TravelPace;
      style: JourneyStyle;
      interests: TravelInterest[];
      duration?: number;
      budget?: 'budget' | 'moderate' | 'luxury';
    },
    availablePOIs: PointOfInterest[] = []
  ): TravelInsights {
    const balance = this.analyzeBalance(pois);
    const timeEstimates = this.calculateTimeEstimates(destinations, pois, preferences.pace);
    const seasonalRecommendations = this.getSeasonalRecommendations(destinations);
    const personalizedRecommendations = this.generatePersonalizedRecommendations(
      destinations,
      pois,
      preferences,
      availablePOIs
    );

    const overallScore = this.calculateOverallScore(balance, timeEstimates, preferences);
    const improvements = this.generateImprovements(balance, timeEstimates, preferences);

    return {
      balance,
      timeEstimates,
      seasonalRecommendations,
      personalizedRecommendations,
      overallScore,
      improvements
    };
  }

  /**
   * Analyze itinerary balance across multiple dimensions
   */
  private analyzeBalance(pois: PointOfInterest[]): ItineraryBalance {
    if (pois.length === 0) {
      return {
        urbanVsRural: 50,
        culturalVsNature: 50,
        activeVsRelaxed: 50,
        budgetDistribution: { free: 25, budget: 25, moderate: 25, premium: 25 },
        categoryDistribution: {}
      };
    }

    // Urban vs Rural analysis
    const urbanTypes = ['museum', 'monument', 'restaurant', 'shopping', 'landmark'];
    const ruralTypes = ['nature', 'beach', 'mountain', 'park', 'scenic'];
    const urbanCount = pois.filter(poi => urbanTypes.includes(poi.type || '')).length;
    const ruralCount = pois.filter(poi => ruralTypes.includes(poi.type || '')).length;
    const urbanVsRural = pois.length > 0 ? (urbanCount / pois.length) * 100 : 50;

    // Cultural vs Nature analysis
    const culturalTypes = ['museum', 'monument', 'landmark', 'historic', 'religious'];
    const natureTypes = ['nature', 'beach', 'mountain', 'park', 'scenic', 'cave'];
    const culturalCount = pois.filter(poi => 
      culturalTypes.includes(poi.type || '') || 
      poi.tags?.some(tag => culturalTypes.includes(tag))
    ).length;
    const natureCount = pois.filter(poi => 
      natureTypes.includes(poi.type || '') || 
      poi.tags?.some(tag => natureTypes.includes(tag))
    ).length;
    const culturalVsNature = pois.length > 0 ? (culturalCount / pois.length) * 100 : 50;

    // Active vs Relaxed analysis
    const activeTypes = ['adventure', 'hiking', 'sports', 'activity'];
    const relaxedTypes = ['spa', 'beach', 'restaurant', 'scenic'];
    const activeCount = pois.filter(poi => 
      activeTypes.includes(poi.type || '') || 
      poi.tags?.some(tag => activeTypes.includes(tag))
    ).length;
    const relaxedCount = pois.filter(poi => 
      relaxedTypes.includes(poi.type || '') || 
      poi.tags?.some(tag => relaxedTypes.includes(tag))
    ).length;
    const activeVsRelaxed = pois.length > 0 ? (activeCount / pois.length) * 100 : 50;

    // Budget distribution
    const budgetDistribution = {
      free: pois.filter(poi => (poi.cost || 0) === 0).length / pois.length * 100,
      budget: pois.filter(poi => (poi.cost || 0) > 0 && (poi.cost || 0) <= 20).length / pois.length * 100,
      moderate: pois.filter(poi => (poi.cost || 0) > 20 && (poi.cost || 0) <= 50).length / pois.length * 100,
      premium: pois.filter(poi => (poi.cost || 0) > 50).length / pois.length * 100
    };

    // Category distribution
    const categoryDistribution: Record<string, number> = {};
    pois.forEach(poi => {
      const category = poi.category || poi.type || 'other';
      categoryDistribution[category] = (categoryDistribution[category] || 0) + 1;
    });

    return {
      urbanVsRural,
      culturalVsNature,
      activeVsRelaxed,
      budgetDistribution,
      categoryDistribution
    };
  }

  /**
   * Calculate time estimates for the itinerary
   */
  private calculateTimeEstimates(
    destinations: Destination[],
    pois: PointOfInterest[],
    pace: TravelPace
  ): TimeEstimates {
    // Calculate driving time between destinations
    let totalDrivingTime = 0;
    for (let i = 0; i < destinations.length - 1; i++) {
      const from = destinations[i];
      const to = destinations[i + 1];
      const distance = this.calculateDistance(from.coordinates, to.coordinates);
      // Assume average speed of 60 km/h including stops
      totalDrivingTime += distance / 60;
    }

    // Calculate visit time for POIs
    const totalVisitTime = pois.reduce((total, poi) => {
      const baseDuration = poi.duration || 2; // hours
      const paceMultiplier = pace === 'slow' ? 1.5 : pace === 'fast' ? 0.7 : 1;
      return total + (baseDuration * paceMultiplier);
    }, 0);

    // Recommend duration based on total time
    const totalActiveTime = totalDrivingTime + totalVisitTime;
    const recommendedDuration = Math.ceil(totalActiveTime / 8); // 8 active hours per day

    // Create daily breakdown
    const dailyBreakdown = this.createDailyBreakdown(destinations, pois, recommendedDuration);

    return {
      totalDrivingTime,
      totalVisitTime,
      recommendedDuration,
      dailyBreakdown
    };
  }

  /**
   * Get seasonal recommendations
   */
  private getSeasonalRecommendations(destinations: Destination[]): SeasonalRecommendations {
    // This would ideally be based on destination-specific data
    // For now, providing general recommendations
    
    const hasCoastal = destinations.some(dest => 
      dest.tags?.includes('coastal') || dest.tags?.includes('beach')
    );
    
    const hasMountains = destinations.some(dest => 
      dest.tags?.includes('mountain') || dest.tags?.includes('hiking')
    );

    let bestMonths: string[];
    let weatherConsiderations: string[];
    let seasonalHighlights: string[];
    let crowdLevels: 'low' | 'moderate' | 'high';
    let pricingImpact: 'budget' | 'standard' | 'premium';

    if (hasCoastal) {
      bestMonths = ['May', 'June', 'September', 'October'];
      weatherConsiderations = ['Warm temperatures', 'Less crowded beaches', 'Pleasant for walking'];
      seasonalHighlights = ['Beach season', 'Outdoor dining', 'Coastal festivals'];
      crowdLevels = 'moderate';
      pricingImpact = 'standard';
    } else if (hasMountains) {
      bestMonths = ['April', 'May', 'September', 'October'];
      weatherConsiderations = ['Mild temperatures', 'Clear skies', 'Good hiking conditions'];
      seasonalHighlights = ['Spring blooms', 'Fall colors', 'Perfect hiking weather'];
      crowdLevels = 'low';
      pricingImpact = 'budget';
    } else {
      bestMonths = ['March', 'April', 'May', 'September', 'October', 'November'];
      weatherConsiderations = ['Mild temperatures', 'Less rain', 'Comfortable for sightseeing'];
      seasonalHighlights = ['Cultural events', 'Local festivals', 'Perfect weather'];
      crowdLevels = 'moderate';
      pricingImpact = 'standard';
    }

    return {
      bestMonths,
      weatherConsiderations,
      seasonalHighlights,
      crowdLevels,
      pricingImpact
    };
  }

  /**
   * Generate personalized recommendations
   */
  private generatePersonalizedRecommendations(
    destinations: Destination[],
    selectedPOIs: PointOfInterest[],
    preferences: {
      pace: TravelPace;
      style: JourneyStyle;
      interests: TravelInterest[];
      budget?: 'budget' | 'moderate' | 'luxury';
    },
    availablePOIs: PointOfInterest[]
  ): PersonalizedRecommendations {
    // Find additional POIs based on interests
    const additionalPOIs = availablePOIs
      .filter(poi => !selectedPOIs.some(selected => selected.id === poi.id))
      .filter(poi => {
        return preferences.interests.some(interest => 
          poi.tags?.includes(interest) || 
          poi.type === interest ||
          poi.category === interest
        );
      })
      .sort((a, b) => (b.rating || 0) - (a.rating || 0))
      .slice(0, 5);

    // Generate route optimizations
    const routeOptimizations = [
      'Consider visiting POIs in geographical clusters to minimize driving time',
      'Plan overnight stays in central locations to reduce daily travel',
      'Book accommodations near your planned activities'
    ];

    // Generate experience enhancements
    const experienceEnhancements = [];
    if (preferences.interests.includes('culture')) {
      experienceEnhancements.push('Consider hiring local guides for cultural sites');
      experienceEnhancements.push('Look for traditional music or dance performances');
    }
    if (preferences.interests.includes('food')) {
      experienceEnhancements.push('Book cooking classes or food tours');
      experienceEnhancements.push('Try local markets and street food');
    }
    if (preferences.interests.includes('nature')) {
      experienceEnhancements.push('Plan early morning visits for best lighting and fewer crowds');
      experienceEnhancements.push('Consider guided nature walks or wildlife tours');
    }

    // Generate budget tips
    const budgetTips = [];
    if (preferences.budget === 'budget') {
      budgetTips.push('Many museums offer free entry on certain days');
      budgetTips.push('Consider picnics in scenic locations');
      budgetTips.push('Look for local transportation passes');
    } else if (preferences.budget === 'luxury') {
      budgetTips.push('Book premium experiences in advance');
      budgetTips.push('Consider private tours for a more personalized experience');
      budgetTips.push('Look for luxury accommodations with unique character');
    }

    // Generate local insights
    const localInsights = [
      'Learn a few basic phrases in the local language',
      'Respect local customs and dress codes',
      'Try to visit during local festivals for authentic experiences',
      'Ask locals for their favorite hidden spots'
    ];

    return {
      additionalPOIs,
      routeOptimizations,
      experienceEnhancements,
      budgetTips,
      localInsights
    };
  }

  /**
   * Calculate overall itinerary score
   */
  private calculateOverallScore(
    balance: ItineraryBalance,
    timeEstimates: TimeEstimates,
    preferences: any
  ): number {
    let score = 70; // Base score

    // Balance scoring (prefer balanced itineraries)
    const balanceScore = 100 - Math.abs(balance.urbanVsRural - 50) - Math.abs(balance.culturalVsNature - 50);
    score += (balanceScore - 50) * 0.2;

    // Time efficiency scoring
    const dailyHours = (timeEstimates.totalDrivingTime + timeEstimates.totalVisitTime) / timeEstimates.recommendedDuration;
    if (dailyHours >= 6 && dailyHours <= 10) {
      score += 10; // Good daily pace
    } else if (dailyHours > 10) {
      score -= 10; // Too packed
    } else {
      score -= 5; // Too light
    }

    // Ensure score is within bounds
    return Math.max(0, Math.min(100, Math.round(score)));
  }

  /**
   * Generate improvement suggestions
   */
  private generateImprovements(
    balance: ItineraryBalance,
    timeEstimates: TimeEstimates,
    preferences: any
  ): string[] {
    const improvements: string[] = [];

    // Balance improvements
    if (balance.urbanVsRural > 80) {
      improvements.push('Consider adding some nature or outdoor activities for better balance');
    } else if (balance.urbanVsRural < 20) {
      improvements.push('Consider adding some cultural or urban experiences');
    }

    if (balance.culturalVsNature > 80) {
      improvements.push('Add some natural attractions or outdoor activities');
    } else if (balance.culturalVsNature < 20) {
      improvements.push('Include more cultural sites or historical attractions');
    }

    // Time improvements
    const dailyHours = (timeEstimates.totalDrivingTime + timeEstimates.totalVisitTime) / timeEstimates.recommendedDuration;
    if (dailyHours > 10) {
      improvements.push('Your itinerary is quite packed. Consider extending your trip or reducing activities');
    } else if (dailyHours < 6) {
      improvements.push('You have room for more activities. Consider adding more POIs or experiences');
    }

    // Budget improvements
    if (balance.budgetDistribution.premium > 70) {
      improvements.push('Consider mixing in some free or budget-friendly activities');
    } else if (balance.budgetDistribution.free > 80) {
      improvements.push('Consider adding some premium experiences for memorable moments');
    }

    return improvements;
  }

  /**
   * Helper methods
   */
  private calculateDistance(coord1: [number, number], coord2: [number, number]): number {
    const from = turf.point(coord1);
    const to = turf.point(coord2);
    return turf.distance(from, to, { units: 'kilometers' });
  }

  private createDailyBreakdown(
    destinations: Destination[],
    pois: PointOfInterest[],
    totalDays: number
  ): TimeEstimates['dailyBreakdown'] {
    // Simplified daily breakdown - in reality this would be more sophisticated
    const breakdown: TimeEstimates['dailyBreakdown'] = [];
    const poisPerDay = Math.ceil(pois.length / totalDays);

    for (let day = 1; day <= totalDays; day++) {
      const dayPOIs = pois.slice((day - 1) * poisPerDay, day * poisPerDay);
      const dayDestinations = [...new Set(dayPOIs.map(poi => poi.location || 'Unknown'))];
      
      breakdown.push({
        day,
        destinations: dayDestinations,
        drivingTime: 2, // Simplified
        visitTime: dayPOIs.reduce((total, poi) => total + (poi.duration || 2), 0),
        activities: dayPOIs.map(poi => poi.name)
      });
    }

    return breakdown;
  }
}

export default TravelInsightsEngine;
