/**
 * Database Service Layer
 * Provides high-level database operations and data transformation
 */

import { DatabaseHelper, DatabaseClient, DatabaseDestination, DatabasePOI, DatabaseJourney } from '../../lib/supabase';
import { Destination } from '../../types/DestinationTypes';
import { PointOfInterest } from '../../types/POITypes';

// Define POI interface for database service
interface POI {
  id: string;
  name: string;
  description?: string;
  category?: string;
  coordinates?: [number, number];
  location?: string;
  region?: string;
  tags?: string[];
  images?: string[];
}

export class DatabaseService {
  private static instance: DatabaseService;
  private cache: Map<string, any> = new Map();
  private cacheExpiry: Map<string, number> = new Map();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  private constructor() {}

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  /**
   * Get client data by slug with caching and fallback
   */
  public async getClientData(slug: string): Promise<{
    client: DatabaseClient | null;
    destinations: Destination[];
    pois: POI[];
    journeyTemplates: DatabaseJourney[];
  }> {
    const cacheKey = `client_data_${slug}`;

    // Check cache first
    if (this.isCacheValid(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    try {
      // Fetch client
      const client = await DatabaseHelper.getClientBySlug(slug);
      if (!client) {
        // If no client found in database, return empty data structure
        // This allows the app to fall back to mock data
        console.log(`Database client not found: ${slug}. App will use mock data fallback.`);
        return {
          client: null,
          destinations: [],
          pois: [],
          journeyTemplates: []
        };
      }

      // Fetch related data
      const [dbDestinations, dbPOIs, journeyTemplates] = await Promise.all([
        DatabaseHelper.getDestinations(client.id),
        DatabaseHelper.getPOIs(client.id),
        DatabaseHelper.getJourneyTemplates(client.id)
      ]);

      // Transform data to match existing types
      const destinations = this.transformDestinations(dbDestinations);
      const pois = this.transformPOIs(dbPOIs);

      const result = {
        client,
        destinations,
        pois,
        journeyTemplates
      };

      // Cache the result
      this.setCache(cacheKey, result);

      return result;
    } catch (error) {
      console.warn('Database unavailable, app will use mock data fallback:', error);
      // Return empty structure to trigger mock data fallback
      return {
        client: null,
        destinations: [],
        pois: [],
        journeyTemplates: []
      };
    }
  }

  /**
   * Get destinations for a client
   */
  public async getDestinations(clientSlug: string): Promise<Destination[]> {
    const clientData = await this.getClientData(clientSlug);
    return clientData.destinations;
  }

  /**
   * Get POIs for a client
   */
  public async getPOIs(clientSlug: string, destinationId?: string): Promise<POI[]> {
    const clientData = await this.getClientData(clientSlug);
    
    if (destinationId) {
      return clientData.pois.filter(poi => 
        poi.location?.toLowerCase().includes(destinationId.toLowerCase()) ||
        poi.region?.toLowerCase().includes(destinationId.toLowerCase())
      );
    }
    
    return clientData.pois;
  }

  /**
   * Get POIs in the legacy PointOfInterest format
   */
  public async getLegacyPOIs(clientSlug: string): Promise<PointOfInterest[]> {
    const pois = await this.getPOIs(clientSlug);
    return this.transformPOIsToLegacy(pois);
  }

  /**
   * Search POIs by query
   */
  public async searchPOIs(clientSlug: string, query: string): Promise<POI[]> {
    const pois = await this.getPOIs(clientSlug);
    const lowerQuery = query.toLowerCase();
    
    return pois.filter(poi =>
      poi.name.toLowerCase().includes(lowerQuery) ||
      poi.description?.toLowerCase().includes(lowerQuery) ||
      poi.category?.toLowerCase().includes(lowerQuery) ||
      poi.tags?.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  }

  /**
   * Get journey templates
   */
  public async getJourneyTemplates(clientSlug: string): Promise<DatabaseJourney[]> {
    const clientData = await this.getClientData(clientSlug);
    return clientData.journeyTemplates;
  }

  /**
   * Transform database destinations to app format
   */
  private transformDestinations(dbDestinations: DatabaseDestination[]): Destination[] {
    return dbDestinations.map(dest => ({
      id: dest.id,
      name: dest.name,
      description: dest.description,
      coordinates: this.pointToCoordinates(dest.coordinates),
      region: dest.region,
      country: dest.country,
      tags: dest.tags,
      images: dest.images,
      position: {
        lat: this.pointToCoordinates(dest.coordinates)[1],
        lng: this.pointToCoordinates(dest.coordinates)[0]
      }
    }));
  }

  /**
   * Transform database POIs to app format
   */
  private transformPOIs(dbPOIs: DatabasePOI[]): POI[] {
    return dbPOIs.map(poi => ({
      id: poi.id,
      name: poi.name,
      description: poi.description,
      category: poi.category,
      coordinates: this.pointToCoordinates(poi.coordinates),
      location: poi.location,
      region: poi.region,
      tags: poi.tags,
      images: poi.images
    }));
  }

  /**
   * Transform POIs to legacy PointOfInterest format
   */
  private transformPOIsToLegacy(pois: POI[]): PointOfInterest[] {
    return pois.map(poi => ({
      id: poi.id,
      name: poi.name,
      description: poi.description || '',
      category: poi.category as any, // Type assertion for legacy compatibility
      coordinates: poi.coordinates || [0, 0],
      location: poi.location || '',
      region: poi.region || '',
      tags: poi.tags || [],
      position: {
        lat: poi.coordinates?.[1] || 0,
        lng: poi.coordinates?.[0] || 0
      },
      price: 0, // Default values for legacy fields
      duration: 60,
      rating: 4.5,
      reviews: 0,
      images: poi.images || [],
      openingHours: '',
      website: '',
      phone: ''
    }));
  }

  /**
   * Convert PostGIS POINT to [lng, lat] coordinates
   */
  private pointToCoordinates(point: any): [number, number] {
    if (typeof point === 'string') {
      // Parse PostGIS POINT format: "POINT(-7.9811 31.6295)"
      const pointMatch = point.match(/POINT\(([^)]+)\)/);
      if (pointMatch) {
        const [lng, lat] = pointMatch[1].split(' ').map(Number);
        return [lng, lat];
      }

      // Parse coordinate format: "(-7.5898,33.5731)"
      const coordMatch = point.match(/\(([^)]+)\)/);
      if (coordMatch) {
        const [lng, lat] = coordMatch[1].split(',').map(Number);
        return [lng, lat];
      }

      // Parse simple format: "-7.5898,33.5731"
      if (point.includes(',')) {
        const [lng, lat] = point.split(',').map(Number);
        return [lng, lat];
      }
    } else if (point && typeof point === 'object') {
      // Handle object format
      if (point.x !== undefined && point.y !== undefined) {
        return [point.x, point.y];
      }
      if (point.lng !== undefined && point.lat !== undefined) {
        return [point.lng, point.lat];
      }
    }

    console.warn('Unable to parse coordinates:', point);
    return [0, 0];
  }

  /**
   * Cache management
   */
  private isCacheValid(key: string): boolean {
    const expiry = this.cacheExpiry.get(key);
    if (!expiry || Date.now() > expiry) {
      this.cache.delete(key);
      this.cacheExpiry.delete(key);
      return false;
    }
    return this.cache.has(key);
  }

  private setCache(key: string, value: any): void {
    this.cache.set(key, value);
    this.cacheExpiry.set(key, Date.now() + this.CACHE_DURATION);
  }

  /**
   * Clear cache
   */
  public clearCache(): void {
    this.cache.clear();
    this.cacheExpiry.clear();
  }

  /**
   * Health check - test database connection
   */
  public async healthCheck(): Promise<boolean> {
    try {
      const client = await DatabaseHelper.getClientBySlug('morocco');
      return client !== null;
    } catch (error) {
      console.error('Database health check failed:', error);
      return false;
    }
  }
}

export default DatabaseService;
