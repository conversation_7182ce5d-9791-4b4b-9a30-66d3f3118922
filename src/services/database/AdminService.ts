/**
 * Admin Service Layer
 * Provides database operations for admin functionality
 */

import { supabase } from '../../integrations/supabase/client';
import PerformanceOptimizer from '../PerformanceOptimizer';

// =====================================================
// TYPE DEFINITIONS
// =====================================================

export interface ClientTheme {
  id: string;
  client_id: string;
  theme_name: string;
  primary_color: string;
  secondary_color: string;
  accent_color: string;
  background_color: string;
  text_color: string;
  border_color: string;
  success_color: string;
  warning_color: string;
  error_color: string;
  font_family: string;
  heading_font: string;
  header_style: 'minimal' | 'standard' | 'prominent';
  footer_style: 'minimal' | 'standard' | 'detailed';
  custom_css?: string;
  is_active: boolean;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

export interface ClientSettings {
  id: string;
  client_id: string;
  enable_ai: boolean;
  enable_animations: boolean;
  enable_poi_discovery: boolean;
  enable_quote_requests: boolean;
  enable_user_registration: boolean;
  default_map_center?: [number, number];
  default_map_zoom: number;
  default_map_bounds?: any;
  max_destinations: number;
  max_pois_per_destination: number;
  max_journey_duration: number;
  company_name?: string;
  company_tagline?: string;
  logo_url?: string;
  favicon_url?: string;
  contact_email?: string;
  contact_phone?: string;
  contact_address?: string;
  website_url?: string;
  social_media?: any;
  google_analytics_id?: string;
  facebook_pixel_id?: string;
  api_settings?: any;
  created_at: string;
  updated_at: string;
}

export interface UserRole {
  id: string;
  user_id: string;
  role: 'super_admin' | 'client_admin' | 'user';
  client_id?: string;
  permissions: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface AdminPermission {
  id: string;
  permission_name: string;
  permission_description?: string;
  permission_category: string;
  is_system_permission: boolean;
  created_at: string;
}

// =====================================================
// ADMIN SERVICE CLASS
// =====================================================

export class AdminService {
  private static instance: AdminService;
  private performanceOptimizer: PerformanceOptimizer;

  private constructor() {
    this.performanceOptimizer = PerformanceOptimizer.getInstance();
  }

  public static getInstance(): AdminService {
    if (!AdminService.instance) {
      AdminService.instance = new AdminService();
    }
    return AdminService.instance;
  }

  // =====================================================
  // CLIENT THEME OPERATIONS
  // =====================================================

  /**
   * Get all themes for a client
   */
  async getClientThemes(clientId: string): Promise<ClientTheme[]> {
    const cacheKey = `client_themes_${clientId}`;
    const cached = this.performanceOptimizer.getCachedResult(cacheKey);
    if (cached) return cached;

    const { data, error } = await supabase
      .from('client_themes')
      .select('*')
      .eq('client_id', clientId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching client themes:', error);
      throw error;
    }

    const result = data || [];
    this.performanceOptimizer.cacheResult(cacheKey, result, 300000); // 5 minutes
    return result;
  }

  /**
   * Get active theme for a client
   */
  async getActiveClientTheme(clientId: string): Promise<ClientTheme | null> {
    const { data, error } = await supabase
      .from('client_themes')
      .select('*')
      .eq('client_id', clientId)
      .eq('is_active', true)
      .eq('is_default', true)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error fetching active client theme:', error);
      throw error;
    }

    return data;
  }

  /**
   * Create a new theme for a client
   */
  async createClientTheme(theme: Omit<ClientTheme, 'id' | 'created_at' | 'updated_at'>): Promise<ClientTheme> {
    const { data, error } = await supabase
      .from('client_themes')
      .insert([theme])
      .select()
      .single();

    if (error) {
      console.error('Error creating client theme:', error);
      throw error;
    }

    return data;
  }

  /**
   * Update a client theme
   */
  async updateClientTheme(themeId: string, updates: Partial<ClientTheme>): Promise<ClientTheme> {
    const { data, error } = await supabase
      .from('client_themes')
      .update(updates)
      .eq('id', themeId)
      .select()
      .single();

    if (error) {
      console.error('Error updating client theme:', error);
      throw error;
    }

    return data;
  }

  /**
   * Delete a client theme
   */
  async deleteClientTheme(themeId: string): Promise<void> {
    const { error } = await supabase
      .from('client_themes')
      .delete()
      .eq('id', themeId);

    if (error) {
      console.error('Error deleting client theme:', error);
      throw error;
    }
  }

  // =====================================================
  // CLIENT SETTINGS OPERATIONS
  // =====================================================

  /**
   * Get client settings
   */
  async getClientSettings(clientId: string): Promise<ClientSettings | null> {
    const { data, error } = await supabase
      .from('client_settings')
      .select('*')
      .eq('client_id', clientId)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching client settings:', error);
      throw error;
    }

    return data;
  }

  /**
   * Update client settings
   */
  async updateClientSettings(clientId: string, settings: Partial<ClientSettings>): Promise<ClientSettings> {
    const { data, error } = await supabase
      .from('client_settings')
      .upsert([{ client_id: clientId, ...settings }])
      .select()
      .single();

    if (error) {
      console.error('Error updating client settings:', error);
      throw error;
    }

    return data;
  }

  // =====================================================
  // USER ROLE OPERATIONS
  // =====================================================

  /**
   * Get user roles
   */
  async getUserRoles(userId?: string, clientId?: string): Promise<UserRole[]> {
    let query = supabase.from('user_roles').select('*');

    if (userId) {
      query = query.eq('user_id', userId);
    }

    if (clientId) {
      query = query.eq('client_id', clientId);
    }

    const { data, error } = await query.order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching user roles:', error);
      throw error;
    }

    return data || [];
  }

  /**
   * Create user role
   */
  async createUserRole(role: Omit<UserRole, 'id' | 'created_at' | 'updated_at'>): Promise<UserRole> {
    const { data, error } = await supabase
      .from('user_roles')
      .insert([role])
      .select()
      .single();

    if (error) {
      console.error('Error creating user role:', error);
      throw error;
    }

    return data;
  }

  /**
   * Update user role
   */
  async updateUserRole(roleId: string, updates: Partial<UserRole>): Promise<UserRole> {
    const { data, error } = await supabase
      .from('user_roles')
      .update(updates)
      .eq('id', roleId)
      .select()
      .single();

    if (error) {
      console.error('Error updating user role:', error);
      throw error;
    }

    return data;
  }

  /**
   * Delete user role
   */
  async deleteUserRole(roleId: string): Promise<void> {
    const { error } = await supabase
      .from('user_roles')
      .delete()
      .eq('id', roleId);

    if (error) {
      console.error('Error deleting user role:', error);
      throw error;
    }
  }

  // =====================================================
  // PERMISSION OPERATIONS
  // =====================================================

  /**
   * Get all permissions
   */
  async getPermissions(): Promise<AdminPermission[]> {
    const { data, error } = await supabase
      .from('admin_permissions')
      .select('*')
      .order('permission_category', { ascending: true });

    if (error) {
      console.error('Error fetching permissions:', error);
      throw error;
    }

    return data || [];
  }

  /**
   * Get permissions by category
   */
  async getPermissionsByCategory(category: string): Promise<AdminPermission[]> {
    const { data, error } = await supabase
      .from('admin_permissions')
      .select('*')
      .eq('permission_category', category)
      .order('permission_name', { ascending: true });

    if (error) {
      console.error('Error fetching permissions by category:', error);
      throw error;
    }

    return data || [];
  }

  // =====================================================
  // AUDIT LOG OPERATIONS
  // =====================================================

  /**
   * Log admin action
   */
  async logAdminAction(action: {
    user_id?: string;
    client_id?: string;
    action: string;
    resource_type: string;
    resource_id?: string;
    old_values?: any;
    new_values?: any;
    ip_address?: string;
    user_agent?: string;
  }): Promise<void> {
    const { error } = await supabase
      .from('admin_audit_log')
      .insert([action]);

    if (error) {
      console.error('Error logging admin action:', error);
      // Don't throw error for audit log failures
    }
  }

  /**
   * Get audit logs
   */
  async getAuditLogs(filters?: {
    user_id?: string;
    client_id?: string;
    action?: string;
    resource_type?: string;
    limit?: number;
  }): Promise<any[]> {
    let query = supabase
      .from('admin_audit_log')
      .select('*')
      .order('created_at', { ascending: false });

    if (filters?.user_id) {
      query = query.eq('user_id', filters.user_id);
    }

    if (filters?.client_id) {
      query = query.eq('client_id', filters.client_id);
    }

    if (filters?.action) {
      query = query.eq('action', filters.action);
    }

    if (filters?.resource_type) {
      query = query.eq('resource_type', filters.resource_type);
    }

    if (filters?.limit) {
      query = query.limit(filters.limit);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching audit logs:', error);
      throw error;
    }

    return data || [];
  }
}

// Export singleton instance
export default AdminService.getInstance();
