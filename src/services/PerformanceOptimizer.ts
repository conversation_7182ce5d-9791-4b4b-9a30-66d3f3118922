/**
 * Performance Optimization Manager
 * 
 * Handles performance optimizations for animation, database operations,
 * and overall application responsiveness. Includes frame rate monitoring,
 * memory management, and adaptive quality settings.
 */

export interface PerformanceMetrics {
  fps: number;
  frameTime: number;
  memoryUsage: number;
  animationLoad: number;
  databaseLatency: number;
  renderTime: number;
}

export interface PerformanceSettings {
  targetFPS: number;
  maxAnimationObjects: number;
  enableAdaptiveQuality: boolean;
  enableFrameSkipping: boolean;
  enableDatabaseCaching: boolean;
  enableLazyLoading: boolean;
  reducedMotion: boolean;
}

export class PerformanceOptimizer {
  private static instance: PerformanceOptimizer | null = null;
  
  private metrics: PerformanceMetrics = {
    fps: 60,
    frameTime: 16.67,
    memoryUsage: 0,
    animationLoad: 0,
    databaseLatency: 0,
    renderTime: 0
  };

  private settings: PerformanceSettings = {
    targetFPS: 60,
    maxAnimationObjects: 100,
    enableAdaptiveQuality: true,
    enableFrameSkipping: false,
    enableDatabaseCaching: true,
    enableLazyLoading: true,
    reducedMotion: false
  };

  private frameCount = 0;
  private lastFrameTime = 0;
  private fpsHistory: number[] = [];
  private isMonitoring = false;
  private animationFrame: number | null = null;
  private databaseCache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  private readonly CACHE_CLEANUP_INTERVAL = 60000; // 1 minute

  private constructor() {
    this.detectDeviceCapabilities();
    this.startPerformanceMonitoring();
    this.setupCacheCleanup();
  }

  public static getInstance(): PerformanceOptimizer {
    if (!PerformanceOptimizer.instance) {
      PerformanceOptimizer.instance = new PerformanceOptimizer();
    }
    return PerformanceOptimizer.instance;
  }

  // =====================================================
  // DEVICE CAPABILITY DETECTION
  // =====================================================

  /**
   * Detect device capabilities and adjust settings accordingly
   */
  private detectDeviceCapabilities(): void {
    // Check for reduced motion preference
    if (window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      this.settings.reducedMotion = true;
      this.settings.targetFPS = 30;
    }

    // Check device memory (if available)
    const deviceMemory = (navigator as any).deviceMemory;
    if (deviceMemory) {
      if (deviceMemory <= 2) {
        // Low memory device
        this.settings.targetFPS = 30;
        this.settings.maxAnimationObjects = 50;
        this.settings.enableFrameSkipping = true;
      } else if (deviceMemory <= 4) {
        // Medium memory device
        this.settings.targetFPS = 45;
        this.settings.maxAnimationObjects = 75;
      }
    }

    // Check for mobile device
    const isMobile = window.innerWidth <= 768 || 
                    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    
    if (isMobile) {
      this.settings.targetFPS = Math.min(this.settings.targetFPS, 45);
      this.settings.maxAnimationObjects = Math.min(this.settings.maxAnimationObjects, 60);
      this.settings.enableFrameSkipping = true;
    }

    console.log('[Performance] Device capabilities detected:', {
      deviceMemory,
      isMobile,
      reducedMotion: this.settings.reducedMotion,
      targetFPS: this.settings.targetFPS
    });
  }

  // =====================================================
  // PERFORMANCE MONITORING
  // =====================================================

  /**
   * Start performance monitoring
   */
  private startPerformanceMonitoring(): void {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    this.lastFrameTime = performance.now();
    this.monitorFrame();
  }

  /**
   * Monitor frame performance
   */
  private monitorFrame(): void {
    const currentTime = performance.now();
    const deltaTime = currentTime - this.lastFrameTime;
    
    // Calculate FPS
    const fps = 1000 / deltaTime;
    this.fpsHistory.push(fps);
    
    // Keep only last 60 frames for averaging
    if (this.fpsHistory.length > 60) {
      this.fpsHistory.shift();
    }
    
    // Update metrics
    this.metrics.fps = this.fpsHistory.reduce((a, b) => a + b, 0) / this.fpsHistory.length;
    this.metrics.frameTime = deltaTime;
    this.metrics.renderTime = performance.now() - currentTime;
    
    // Check memory usage (if available)
    if ((performance as any).memory) {
      this.metrics.memoryUsage = (performance as any).memory.usedJSHeapSize / 1024 / 1024; // MB
    }
    
    // Adaptive quality adjustment
    if (this.settings.enableAdaptiveQuality) {
      this.adjustQualityBasedOnPerformance();
    }
    
    this.lastFrameTime = currentTime;
    this.frameCount++;
    
    if (this.isMonitoring) {
      this.animationFrame = requestAnimationFrame(() => this.monitorFrame());
    }
  }

  /**
   * Adjust quality settings based on performance
   */
  private adjustQualityBasedOnPerformance(): void {
    const avgFPS = this.metrics.fps;
    const targetFPS = this.settings.targetFPS;
    
    // If FPS is significantly below target, reduce quality
    if (avgFPS < targetFPS * 0.8) {
      if (!this.settings.enableFrameSkipping) {
        this.settings.enableFrameSkipping = true;
        this.emitPerformanceEvent('quality-reduced', { reason: 'low-fps', fps: avgFPS });
      }
      
      // Further reduce animation objects if still struggling
      if (avgFPS < targetFPS * 0.6) {
        this.settings.maxAnimationObjects = Math.max(this.settings.maxAnimationObjects * 0.8, 20);
        this.emitPerformanceEvent('animation-objects-reduced', { 
          newLimit: this.settings.maxAnimationObjects 
        });
      }
    }
    // If FPS is good, gradually increase quality
    else if (avgFPS > targetFPS * 0.95) {
      if (this.settings.enableFrameSkipping && this.frameCount % 300 === 0) { // Check every 5 seconds
        this.settings.enableFrameSkipping = false;
        this.emitPerformanceEvent('quality-improved', { reason: 'good-fps', fps: avgFPS });
      }
    }
  }

  // =====================================================
  // DATABASE CACHING
  // =====================================================

  /**
   * Cache database result
   */
  public cacheResult(key: string, data: any, ttl: number = 300000): void { // 5 minutes default
    if (!this.settings.enableDatabaseCaching) return;
    
    this.databaseCache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  /**
   * Get cached result
   */
  public getCachedResult(key: string): any | null {
    if (!this.settings.enableDatabaseCaching) return null;
    
    const cached = this.databaseCache.get(key);
    if (!cached) return null;
    
    // Check if cache is still valid
    if (Date.now() - cached.timestamp > cached.ttl) {
      this.databaseCache.delete(key);
      return null;
    }
    
    return cached.data;
  }

  /**
   * Setup cache cleanup
   */
  private setupCacheCleanup(): void {
    setInterval(() => {
      const now = Date.now();
      for (const [key, cached] of this.databaseCache.entries()) {
        if (now - cached.timestamp > cached.ttl) {
          this.databaseCache.delete(key);
        }
      }
    }, this.CACHE_CLEANUP_INTERVAL);
  }

  // =====================================================
  // ANIMATION OPTIMIZATION
  // =====================================================

  /**
   * Check if animation should be skipped for performance
   */
  public shouldSkipFrame(): boolean {
    if (!this.settings.enableFrameSkipping) return false;
    
    // Skip every other frame if performance is poor
    return this.metrics.fps < this.settings.targetFPS * 0.7 && this.frameCount % 2 === 0;
  }

  /**
   * Get optimized animation settings
   */
  public getAnimationSettings(): {
    maxObjects: number;
    reducedMotion: boolean;
    targetFPS: number;
    enableInterpolation: boolean;
  } {
    return {
      maxObjects: this.settings.maxAnimationObjects,
      reducedMotion: this.settings.reducedMotion,
      targetFPS: this.settings.targetFPS,
      enableInterpolation: this.metrics.fps > 45
    };
  }

  /**
   * Throttle function calls for performance
   */
  public throttle<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let timeoutId: number | null = null;
    let lastExecTime = 0;
    
    return (...args: Parameters<T>) => {
      const currentTime = Date.now();
      
      if (currentTime - lastExecTime > delay) {
        func(...args);
        lastExecTime = currentTime;
      } else {
        if (timeoutId) clearTimeout(timeoutId);
        timeoutId = window.setTimeout(() => {
          func(...args);
          lastExecTime = Date.now();
        }, delay - (currentTime - lastExecTime));
      }
    };
  }

  /**
   * Debounce function calls for performance
   */
  public debounce<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let timeoutId: number | null = null;
    
    return (...args: Parameters<T>) => {
      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = window.setTimeout(() => func(...args), delay);
    };
  }

  // =====================================================
  // PUBLIC API
  // =====================================================

  /**
   * Get current performance metrics
   */
  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * Get current performance settings
   */
  public getSettings(): PerformanceSettings {
    return { ...this.settings };
  }

  /**
   * Update performance settings
   */
  public updateSettings(newSettings: Partial<PerformanceSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    this.emitPerformanceEvent('settings-updated', newSettings);
  }

  /**
   * Stop performance monitoring
   */
  public stopMonitoring(): void {
    this.isMonitoring = false;
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    }
  }

  /**
   * Clear all caches
   */
  public clearCaches(): void {
    this.databaseCache.clear();
    this.emitPerformanceEvent('caches-cleared', {});
  }

  /**
   * Emit performance event
   */
  private emitPerformanceEvent(type: string, data: any): void {
    const event = new CustomEvent('performance-optimization', {
      detail: { type, data, metrics: this.metrics }
    });
    document.dispatchEvent(event);
  }

  /**
   * Get performance recommendations
   */
  public getRecommendations(): string[] {
    const recommendations: string[] = [];
    
    if (this.metrics.fps < 30) {
      recommendations.push('Consider reducing animation quality for better performance');
    }
    
    if (this.metrics.memoryUsage > 100) {
      recommendations.push('High memory usage detected - consider clearing caches');
    }
    
    if (this.databaseCache.size > 100) {
      recommendations.push('Large cache size - consider reducing cache TTL');
    }
    
    return recommendations;
  }
}

export default PerformanceOptimizer;
