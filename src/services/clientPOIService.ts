/**
 * clientPOIService.ts
 * 
 * Service for managing client-specific POI data
 */

import type { PointOfInterest } from '@/types';
import { ClientConfig } from '../types/ClientTypes';

// Extend the canonical POI type for client-specific data
export interface ClientPointOfInterest extends PointOfInterest {
  clientId: string;
}

/**
 * Service for managing client-specific POI data
 */
export class ClientPOIService {
  private clientId: string;
  private config: ClientConfig | null;

  /**
   * Constructor
   * @param clientId - The client ID
   * @param config - The client configuration
   */
  constructor(clientId: string, config: ClientConfig | null) {
    this.clientId = clientId;
    this.config = config;
  }

  /**
   * Get POIs for the current client
   * @returns Promise resolving to an array of POIs
   */
  async getPOIs(): Promise<ClientPointOfInterest[]> {
    try {
      // In a real implementation, this would fetch from an API
      // For now, we'll simulate loading with a timeout
      return new Promise<ClientPointOfInterest[]>((resolve) => {
        setTimeout(() => {
          // Mock POI data for the client
          // In a real implementation, this would come from an API
          const mockPOIs: ClientPointOfInterest[] = [
            // This would be replaced with actual data from an API
            // For now, we're just returning an empty array
          ];
          
          resolve(mockPOIs.filter(poi => poi.clientId === this.clientId));
        }, 500);
      });
    } catch (error) {
      console.error('Error fetching POIs:', error);
      return [];
    }
  }

  /**
   * Get a single POI by ID
   * @param id - The POI ID
   * @returns Promise resolving to a POI or null if not found
   */
  async getPOIById(id: string): Promise<ClientPointOfInterest | null> {
    try {
      const pois = await this.getPOIs();
      return pois.find(poi => poi.id === id && poi.clientId === this.clientId) || null;
    } catch (error) {
      console.error('Error fetching POI by ID:', error);
      return null;
    }
  }

  /**
   * Filter POIs based on criteria
   * @param filters - The filter criteria
   * @returns Promise resolving to filtered POIs
   */
  async filterPOIs(filters: Record<string, any>): Promise<ClientPointOfInterest[]> {
    try {
      const pois = await this.getPOIs();
      
      return pois.filter(poi => {
        // Ensure POI belongs to current client
        if (poi.clientId !== this.clientId) return false;
        
        // Apply all filters
        return Object.entries(filters).every(([key, value]) => {
          // Skip null or undefined values
          if (value === null || value === undefined) return true;
          
          // Handle array values (e.g., categories, tags)
          if (Array.isArray(value) && Array.isArray(poi[key as keyof PointOfInterest])) {
            const poiValues = poi[key as keyof PointOfInterest] as unknown as any[];
            return value.some(v => poiValues.includes(v));
          }
          
          // Handle string/number equality
          return poi[key as keyof PointOfInterest] === value;
        });
      });
    } catch (error) {
      console.error('Error filtering POIs:', error);
      return [];
    }
  }

  /**
   * Update the client ID
   * @param clientId - The new client ID
   */
  setClientId(clientId: string): void {
    this.clientId = clientId;
  }

  /**
   * Update the client configuration
   * @param config - The new client configuration
   */
  setConfig(config: ClientConfig | null): void {
    this.config = config;
  }
}

/**
 * Create a client POI service instance
 * @param clientId - The client ID
 * @param config - The client configuration
 * @returns A new ClientPOIService instance
 */
export const createClientPOIService = (
  clientId: string,
  config: ClientConfig | null
): ClientPOIService => {
  return new ClientPOIService(clientId, config);
};