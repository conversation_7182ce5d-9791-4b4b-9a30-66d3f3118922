/**
 * Data Service
 * 
 * Unified interface for accessing travel data
 * Supports both mock data (for development) and real database data
 */

import type { 
  Destination, 
  PointOfInterest, 
  PreArrangedJourney 
} from '../types/destinations';

import { regionData } from '../data/destinations';
import { moroccoPreArrangedJourneys } from '../data/preArrangedJourneys';

import { 
  ClientService, 
  RegionService, 
  DestinationService, 
  POIService, 
  JourneyService,
  DataTransformer,
  type DbRegion 
} from './database';

// Configuration
const USE_DATABASE = import.meta.env.VITE_USE_DATABASE === 'true';

export interface ClientData {
  destinations: Destination[];
  pois: PointOfInterest[];
  preArrangedJourneys: PreArrangedJourney[];
  center: [number, number];
  bounds: [[number, number], [number, number]];
}

export interface RegionInfo {
  id?: string;
  name: string;
  center: [number, number];
  bounds: [[number, number], [number, number]];
}

/**
 * Main Data Service Class
 */
export class TravelDataService {
  private static regionCache = new Map<string, DbRegion>();
  
  /**
   * Get available clients/themes
   */
  static async getAvailableClients(): Promise<Array<{ id: string; name: string; slug: string }>> {
    if (USE_DATABASE) {
      try {
        const clients = await ClientService.getAll();
        return clients.map(c => ({
          id: c.id,
          name: c.name,
          slug: c.slug
        }));
      } catch (error) {
        console.warn('Failed to fetch clients from database, falling back to mock data:', error);
      }
    }
    
    // Fallback to mock data
    return [
      { id: 'global', name: 'Global Framework', slug: 'global' },
      { id: 'morocco', name: 'Morocco Travel', slug: 'morocco' },
      { id: 'portugal', name: 'Portugal Discoveries', slug: 'portugal' }
    ];
  }

  /**
   * Get region information for a client
   */
  static async getRegionInfo(clientSlug: string): Promise<RegionInfo | null> {
    if (USE_DATABASE) {
      try {
        // Try to get from cache first
        const cacheKey = `region-${clientSlug}`;
        if (this.regionCache.has(cacheKey)) {
          const cached = this.regionCache.get(cacheKey)!;
          return this.dbRegionToRegionInfo(cached);
        }

        // Fetch from database
        const regions = await RegionService.getByClientSlug(clientSlug);
        if (regions.length > 0) {
          const region = regions[0]; // Take first region for now
          this.regionCache.set(cacheKey, region);
          return this.dbRegionToRegionInfo(region);
        }
      } catch (error) {
        console.warn('Failed to fetch region from database, falling back to mock data:', error);
      }
    }

    // Fallback to mock data
    return this.getMockRegionInfo(clientSlug);
  }

  /**
   * Get complete client data for a specific client
   */
  static async getClientData(clientSlug: string): Promise<ClientData> {
    if (USE_DATABASE) {
      try {
        return await this.getDatabaseClientData(clientSlug);
      } catch (error) {
        console.warn('Failed to fetch client data from database, falling back to mock data:', error);
      }
    }

    // Fallback to mock data
    return this.getMockClientData(clientSlug);
  }

  /**
   * Search destinations
   */
  static async searchDestinations(clientSlug: string, query: string): Promise<Destination[]> {
    if (USE_DATABASE) {
      try {
        const regionInfo = await this.getRegionInfo(clientSlug);
        if (regionInfo?.id) {
          const dbDestinations = await DestinationService.search(regionInfo.id, query);
          return dbDestinations.map(DataTransformer.dbDestinationToDestination);
        }
      } catch (error) {
        console.warn('Failed to search destinations in database, falling back to mock data:', error);
      }
    }

    // Fallback to mock data search
    const clientData = await this.getClientData(clientSlug);
    return clientData.destinations.filter(dest => 
      dest.name.toLowerCase().includes(query.toLowerCase()) ||
      dest.description.toLowerCase().includes(query.toLowerCase())
    );
  }

  /**
   * Search POIs by category or tags
   */
  static async searchPOIs(clientSlug: string, filters: { category?: string; tags?: string[] }): Promise<PointOfInterest[]> {
    if (USE_DATABASE) {
      try {
        const regionInfo = await this.getRegionInfo(clientSlug);
        if (regionInfo?.id) {
          let dbPOIs;
          if (filters.category) {
            dbPOIs = await POIService.getByCategory(regionInfo.id, filters.category);
          } else if (filters.tags && filters.tags.length > 0) {
            dbPOIs = await POIService.searchByTags(regionInfo.id, filters.tags);
          } else {
            dbPOIs = await POIService.getByRegionId(regionInfo.id);
          }
          return dbPOIs.map(DataTransformer.dbPOIToPointOfInterest);
        }
      } catch (error) {
        console.warn('Failed to search POIs in database, falling back to mock data:', error);
      }
    }

    // Fallback to mock data search
    const clientData = await this.getClientData(clientSlug);
    let filteredPOIs = clientData.pois;

    if (filters.category) {
      filteredPOIs = filteredPOIs.filter(poi => poi.category === filters.category);
    }

    if (filters.tags && filters.tags.length > 0) {
      filteredPOIs = filteredPOIs.filter(poi => 
        poi.tags.some(tag => filters.tags!.includes(tag))
      );
    }

    return filteredPOIs;
  }

  // Private helper methods

  private static async getDatabaseClientData(clientSlug: string): Promise<ClientData> {
    const regionInfo = await this.getRegionInfo(clientSlug);
    if (!regionInfo?.id) {
      throw new Error(`Region not found for client: ${clientSlug}`);
    }

    const [dbDestinations, dbPOIs, dbJourneys] = await Promise.all([
      DestinationService.getByRegionId(regionInfo.id),
      POIService.getByRegionId(regionInfo.id),
      JourneyService.getByRegionId(regionInfo.id)
    ]);

    return {
      destinations: dbDestinations.map(DataTransformer.dbDestinationToDestination),
      pois: dbPOIs.map(DataTransformer.dbPOIToPointOfInterest),
      preArrangedJourneys: dbJourneys.map(DataTransformer.dbJourneyToPreArrangedJourney),
      center: regionInfo.center,
      bounds: regionInfo.bounds
    };
  }

  private static getMockClientData(clientSlug: string): ClientData {
    switch (clientSlug) {
      case 'morocco':
        const moroccoData = regionData['morocco'];
        return {
          destinations: moroccoData?.destinations || [],
          pois: moroccoData?.pointsOfInterest || [],
          preArrangedJourneys: moroccoPreArrangedJourneys,
          center: moroccoData?.mapCenter || [-7.09, 31.79] as [number, number],
          bounds: moroccoData?.maxBounds || [[-13.168555, 27.666667], [-1.030511, 35.922284]] as [[number, number], [number, number]]
        };
      case 'portugal':
        const portugalData = regionData['portugal'];
        return {
          destinations: portugalData?.destinations || [],
          pois: portugalData?.pointsOfInterest || [],
          preArrangedJourneys: [], // TODO: Add Portugal journeys
          center: portugalData?.mapCenter || [-8.61, 39.6] as [number, number],
          bounds: portugalData?.maxBounds || [[-9.5, 36.8], [-6.2, 42.1]] as [[number, number], [number, number]]
        };
      default:
        return {
          destinations: [],
          pois: [],
          preArrangedJourneys: [],
          center: [-98.5795, 39.8283] as [number, number],
          bounds: [[-125.0, 25.0], [-65.0, 50.0]] as [[number, number], [number, number]]
        };
    }
  }

  private static getMockRegionInfo(clientSlug: string): RegionInfo | null {
    switch (clientSlug) {
      case 'morocco':
        return {
          name: 'Morocco',
          center: [-7.09, 31.79],
          bounds: [[-13.168555, 27.666667], [-1.030511, 35.922284]]
        };
      case 'portugal':
        return {
          name: 'Portugal',
          center: [-8.61, 39.6],
          bounds: [[-9.5, 36.8], [-6.2, 42.1]]
        };
      default:
        return {
          name: 'Global Framework',
          center: [-98.5795, 39.8283],
          bounds: [[-125.0, 25.0], [-65.0, 50.0]]
        };
    }
  }

  private static dbRegionToRegionInfo(dbRegion: DbRegion): RegionInfo {
    return {
      id: dbRegion.id,
      name: dbRegion.name,
      center: [dbRegion.map_center.x, dbRegion.map_center.y],
      bounds: [
        [dbRegion.map_bounds.x1, dbRegion.map_bounds.y1],
        [dbRegion.map_bounds.x2, dbRegion.map_bounds.y2]
      ]
    };
  }
}

/**
 * Legacy compatibility function
 * Maintains compatibility with existing code
 */
export const getClientData = (clientId: string): ClientData => {
  console.log('🔄 [DataService] Legacy getClientData called, consider using TravelDataService.getClientData()');
  
  // For now, return mock data synchronously to maintain compatibility
  switch (clientId) {
    case 'morocco':
      const moroccoData = regionData['morocco'];
      return {
        destinations: moroccoData?.destinations || [],
        pois: moroccoData?.pointsOfInterest || [],
        preArrangedJourneys: moroccoPreArrangedJourneys,
        center: moroccoData?.mapCenter || [-7.09, 31.79] as [number, number],
        bounds: moroccoData?.maxBounds || [[-13.168555, 27.666667], [-1.030511, 35.922284]] as [[number, number], [number, number]]
      };
    case 'portugal':
      const portugalData = regionData['portugal'];
      return {
        destinations: portugalData?.destinations || [],
        pois: portugalData?.pointsOfInterest || [],
        preArrangedJourneys: [],
        center: portugalData?.mapCenter || [-8.61, 39.6] as [number, number],
        bounds: portugalData?.maxBounds || [[-9.5, 36.8], [-6.2, 42.1]] as [[number, number], [number, number]]
      };
    default:
      return {
        destinations: [],
        pois: [],
        preArrangedJourneys: [],
        center: [-98.5795, 39.8283] as [number, number],
        bounds: [[-125.0, 25.0], [-65.0, 50.0]] as [[number, number], [number, number]]
      };
  }
};
