/**
 * Database Service Layer
 * 
 * Provides a clean interface to interact with Supabase database
 */

import { createClient } from '@supabase/supabase-js';
import type { 
  Destination, 
  PointOfInterest, 
  PreArrangedJourney 
} from '../types/destinations';

// Supabase configuration
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key';

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database types (matching our schema)
export interface DbClient {
  id: string;
  name: string;
  slug: string;
  description?: string;
  theme_config: any;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface DbRegion {
  id: string;
  client_id: string;
  name: string;
  slug: string;
  description?: string;
  map_center: { x: number; y: number };
  map_bounds: { x1: number; y1: number; x2: number; y2: number };
  zoom_level: number;
  country_code?: string;
  timezone?: string;
  currency?: string;
  language?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface DbDestination {
  id: string;
  region_id: string;
  name: string;
  slug: string;
  description?: string;
  short_description?: string;
  coordinates: { x: number; y: number };
  type: string;
  category?: string;
  image_url?: string;
  rating: number;
  average_stay_days: number;
  difficulty_level: string;
  is_active: boolean;
  is_featured: boolean;
  created_at: string;
  updated_at: string;
}

export interface DbPointOfInterest {
  id: string;
  region_id: string;
  destination_id?: string;
  name: string;
  slug: string;
  description?: string;
  short_description?: string;
  coordinates: { x: number; y: number };
  category: string;
  subcategory?: string;
  tags: string[];
  image_url?: string;
  rating: number;
  duration_minutes?: number;
  cost_level: string;
  is_active: boolean;
  is_featured: boolean;
  created_at: string;
  updated_at: string;
}

export interface DbJourney {
  id: string;
  region_id: string;
  title: string;
  slug: string;
  description?: string;
  duration: number;
  difficulty: string;
  pace: string;
  style: string;
  price_from?: number;
  price_to?: number;
  currency: string;
  highlights: string[];
  best_months: number[];
  rating: number;
  is_active: boolean;
  is_featured: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * Client Service
 */
export class ClientService {
  static async getAll(): Promise<DbClient[]> {
    const { data, error } = await supabase
      .from('clients')
      .select('*')
      .eq('is_active', true)
      .order('name');
    
    if (error) throw error;
    return data || [];
  }

  static async getBySlug(slug: string): Promise<DbClient | null> {
    const { data, error } = await supabase
      .from('clients')
      .select('*')
      .eq('slug', slug)
      .eq('is_active', true)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }
    return data;
  }
}

/**
 * Region Service
 */
export class RegionService {
  static async getByClientSlug(clientSlug: string): Promise<DbRegion[]> {
    const { data, error } = await supabase
      .from('regions')
      .select(`
        *,
        clients!inner(slug)
      `)
      .eq('clients.slug', clientSlug)
      .eq('is_active', true)
      .order('display_order');
    
    if (error) throw error;
    return data || [];
  }

  static async getByClientAndRegionSlug(clientSlug: string, regionSlug: string): Promise<DbRegion | null> {
    const { data, error } = await supabase
      .from('regions')
      .select(`
        *,
        clients!inner(slug)
      `)
      .eq('clients.slug', clientSlug)
      .eq('slug', regionSlug)
      .eq('is_active', true)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }
    return data;
  }
}

/**
 * Destination Service
 */
export class DestinationService {
  static async getByRegionId(regionId: string): Promise<DbDestination[]> {
    const { data, error } = await supabase
      .from('destinations')
      .select('*')
      .eq('region_id', regionId)
      .eq('is_active', true)
      .order('popularity_score', { ascending: false });
    
    if (error) throw error;
    return data || [];
  }

  static async getFeatured(regionId: string): Promise<DbDestination[]> {
    const { data, error } = await supabase
      .from('destinations')
      .select('*')
      .eq('region_id', regionId)
      .eq('is_active', true)
      .eq('is_featured', true)
      .order('popularity_score', { ascending: false });
    
    if (error) throw error;
    return data || [];
  }

  static async search(regionId: string, query: string): Promise<DbDestination[]> {
    const { data, error } = await supabase
      .from('destinations')
      .select('*')
      .eq('region_id', regionId)
      .eq('is_active', true)
      .or(`name.ilike.%${query}%,description.ilike.%${query}%,category.ilike.%${query}%`)
      .order('popularity_score', { ascending: false });
    
    if (error) throw error;
    return data || [];
  }
}

/**
 * Points of Interest Service
 */
export class POIService {
  static async getByRegionId(regionId: string): Promise<DbPointOfInterest[]> {
    const { data, error } = await supabase
      .from('points_of_interest')
      .select('*')
      .eq('region_id', regionId)
      .eq('is_active', true)
      .order('popularity_score', { ascending: false });
    
    if (error) throw error;
    return data || [];
  }

  static async getByCategory(regionId: string, category: string): Promise<DbPointOfInterest[]> {
    const { data, error } = await supabase
      .from('points_of_interest')
      .select('*')
      .eq('region_id', regionId)
      .eq('category', category)
      .eq('is_active', true)
      .order('popularity_score', { ascending: false });
    
    if (error) throw error;
    return data || [];
  }

  static async searchByTags(regionId: string, tags: string[]): Promise<DbPointOfInterest[]> {
    const { data, error } = await supabase
      .from('points_of_interest')
      .select('*')
      .eq('region_id', regionId)
      .eq('is_active', true)
      .overlaps('tags', tags)
      .order('popularity_score', { ascending: false });
    
    if (error) throw error;
    return data || [];
  }
}

/**
 * Journey Service
 */
export class JourneyService {
  static async getByRegionId(regionId: string): Promise<DbJourney[]> {
    const { data, error } = await supabase
      .from('journeys')
      .select('*')
      .eq('region_id', regionId)
      .eq('is_active', true)
      .order('popularity_score', { ascending: false });
    
    if (error) throw error;
    return data || [];
  }

  static async getFeatured(regionId: string): Promise<DbJourney[]> {
    const { data, error } = await supabase
      .from('journeys')
      .select('*')
      .eq('region_id', regionId)
      .eq('is_active', true)
      .eq('is_featured', true)
      .order('popularity_score', { ascending: false });
    
    if (error) throw error;
    return data || [];
  }

  static async getWithDetails(journeyId: string) {
    const { data, error } = await supabase
      .from('journeys')
      .select(`
        *,
        journey_destinations(
          *,
          destinations(*)
        ),
        journey_pois(
          *,
          points_of_interest(*)
        ),
        journey_tag_assignments(
          journey_tags(*)
        )
      `)
      .eq('id', journeyId)
      .eq('is_active', true)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }
    return data;
  }
}

/**
 * Data Transformation Utilities
 */
export class DataTransformer {
  static dbDestinationToDestination(dbDest: DbDestination): Destination {
    return {
      id: dbDest.id,
      name: dbDest.name,
      description: dbDest.description || '',
      coordinates: [dbDest.coordinates.x, dbDest.coordinates.y] as [number, number],
      image: dbDest.image_url || '/assets/placeholder-destination.jpg',
      type: dbDest.type as 'city' | 'town' | 'village' | 'landmark'
    };
  }

  static dbPOIToPointOfInterest(dbPOI: DbPointOfInterest): PointOfInterest {
    return {
      id: dbPOI.id,
      name: dbPOI.name,
      description: dbPOI.description || '',
      coordinates: [dbPOI.coordinates.x, dbPOI.coordinates.y] as [number, number],
      category: dbPOI.category,
      image: dbPOI.image_url || '/assets/placeholder-poi.jpg',
      tags: dbPOI.tags,
      rating: dbPOI.rating,
      cost: dbPOI.cost_level as 'free' | 'low' | 'medium' | 'high' | 'luxury'
    };
  }

  static dbJourneyToPreArrangedJourney(dbJourney: DbJourney): PreArrangedJourney {
    return {
      id: dbJourney.id,
      title: dbJourney.title,
      description: dbJourney.description || '',
      duration: dbJourney.duration,
      difficulty: dbJourney.difficulty as 'easy' | 'moderate' | 'challenging',
      pace: dbJourney.pace as 'relaxed' | 'balanced' | 'adventure-seeker',
      style: dbJourney.style,
      cities: [], // Will be populated from journey_destinations
      highlights: dbJourney.highlights,
      imageUrl: '/assets/journeys/placeholder.jpg',
      price: {
        from: dbJourney.price_from || 0,
        currency: dbJourney.currency
      }
    };
  }
}
