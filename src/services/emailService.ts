import { Resend } from 'resend';
import { Destination } from '@/types';
import { Vehicle } from '@/data/vehicles';
import { PointOfInterest } from '@/types';

// Comment out Resend initialization to fix the error
// const resend = new Resend(import.meta.env.VITE_RESEND_API_KEY);

// Types for email data
interface QuoteEmailData {
  fullName: string;
  email: string;
  phone?: string;
  travelDate?: Date;
  numTravelers: number;
  specialRequests?: string;
  destinations: Destination[];
  vehicle: Vehicle | null;
  pointsOfInterest: PointOfInterest[];
  routeStats: {
    totalDistance: number;
    totalDuration: number;
    recommendedDays: number;
  };
}

export const sendQuoteRequestToAdmin = async (data: QuoteEmailData) => {
  try {
    // Format destinations list
    const destinationsList = data.destinations.map((dest, index) => 
      `${index + 1}. ${dest.name}`
    ).join('<br>');
    
    // Format POIs list
    const poiList = data.pointsOfInterest.length > 0 
      ? data.pointsOfInterest.map(poi => `- ${poi.name}`).join('<br>')
      : 'None selected';
    
    // Temporarily commented out to fix Resend API key error
    /* 
    const adminEmail = await resend.emails.send({
      from: 'Morocco Route Artist <<EMAIL>>',
      to: '<EMAIL>', // Change to your admin email
      subject: `New Quote Request from ${data.fullName}`,
      html: `
        <h1>New Quote Request</h1>
        <p><strong>From:</strong> ${data.fullName}</p>
        <p><strong>Email:</strong> ${data.email}</p>
        <p><strong>Phone:</strong> ${data.phone || 'Not provided'}</p>
        <p><strong>Travel Date:</strong> ${data.travelDate ? data.travelDate.toLocaleDateString() : 'Not specified'}</p>
        <p><strong>Number of Travelers:</strong> ${data.numTravelers}</p>
        <p><strong>Special Requests:</strong> ${data.specialRequests || 'None'}</p>
        
        <h2>Itinerary Details</h2>
        <p><strong>Destinations:</strong></p>
        <p>${destinationsList}</p>
        
        <p><strong>Transportation:</strong> ${data.vehicle ? data.vehicle.name + ' (' + data.vehicle.type + ')' : 'Not selected'}</p>
        
        <p><strong>Points of Interest:</strong></p>
        <p>${poiList}</p>
        
        <h2>Route Statistics</h2>
        <p><strong>Total Distance:</strong> ${data.routeStats.totalDistance} km</p>
        <p><strong>Driving Duration:</strong> ${data.routeStats.totalDuration} hours</p>
        <p><strong>Recommended Days:</strong> ${data.routeStats.recommendedDays}</p>
      `,
    });
    
    return adminEmail;
    */
    
    // Return mock success response
    console.log("Quote request would be sent to admin:", data);
    return { id: 'mock-email-id', from: '<EMAIL>' };
    
  } catch (error) {
    console.error('Error sending admin email:', error);
    throw error;
  }
};

export const sendQuoteConfirmationToCustomer = async (data: QuoteEmailData) => {
  try {
    // Format destinations list for customer email
    const destinationsList = data.destinations.map((dest, index) => 
      `${index + 1}. ${dest.name}`
    ).join('<br>');
    
    // Temporarily commented out to fix Resend API key error
    /*
    const customerEmail = await resend.emails.send({
      from: 'Morocco Route Artist <<EMAIL>>',
      to: data.email,
      subject: 'Your Morocco Travel Quote Request Has Been Received',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <img src="https://moroccorouteartist.com/ctm-logo.png" alt="Come to Morocco" style="max-width: 200px; margin-bottom: 20px;" />
          
          <h1 style="color: #e25b45;">Thank You for Your Quote Request!</h1>
          
          <p>Dear ${data.fullName},</p>
          
          <p>We have received your request for a personalized Morocco travel itinerary. Our team of local experts is reviewing your preferences and will create a custom quote for you.</p>
          
          <p>Here's a summary of your request:</p>
          
          <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p><strong>Destinations:</strong></p>
            <p>${destinationsList}</p>
            
            <p><strong>Number of Travelers:</strong> ${data.numTravelers}</p>
            
            <p><strong>Estimated Trip Length:</strong> ${data.routeStats.recommendedDays} days</p>
          </div>
          
          <p>We aim to get back to you within 24-48 hours with your personalized quote. If you have any questions in the meantime, please reply to this email or call us at +212-123-456789.</p>
          
          <p>Thank you for choosing Morocco Route Artist for your Moroccan adventure!</p>
          
          <p>Warm regards,<br>The Morocco Route Artist Team</p>
          
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #777;">
            <p>© 2023 Morocco Route Artist. All rights reserved.</p>
          </div>
        </div>
      `,
    });
    
    return customerEmail;
    */
    
    // Return mock success response
    console.log("Confirmation would be sent to customer:", data);
    return { id: 'mock-email-id', from: '<EMAIL>' };
    
  } catch (error) {
    console.error('Error sending confirmation email:', error);
    throw error;
  }
}; 