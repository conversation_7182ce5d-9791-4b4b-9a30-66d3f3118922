/**
 * AnimationEventEmitter
 * 
 * A specialized event emitter for handling animation system events.
 */

import { AnimationEventData, AnimationEventType, EventCallback } from '../types/AnimationEventTypes';

/**
 * Animation Event Emitter implementation
 * 
 * This class provides a type-safe event handling system for the animation components.
 * It extends the standard event emitter pattern with animation-specific features.
 */
export class AnimationEventEmitter {
  private listeners: Map<AnimationEventType, Array<EventCallback<any>>> = new Map();

  /**
   * Adds an event listener for a specific animation event type
   * 
   * @param eventType The type of event to listen for
   * @param callback The function to call when the event occurs
   * @returns A function to remove this specific listener
   */
  public on<T = AnimationEventData>(eventType: AnimationEventType, callback: EventCallback<T>): () => void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, []);
    }
    
    const listeners = this.listeners.get(eventType)!;
    listeners.push(callback as EventCallback<any>);
    
    // Return a function to remove this specific listener
    return () => {
      const index = listeners.indexOf(callback as EventCallback<any>);
      if (index !== -1) {
        listeners.splice(index, 1);
      }
    };
  }

  /**
   * Removes an event listener for a specific animation event type
   * 
   * @param eventType The type of event
   * @param callback The callback function to remove
   */
  public off<T = AnimationEventData>(eventType: AnimationEventType, callback: EventCallback<T>): void {
    if (!this.listeners.has(eventType)) return;
    
    const listeners = this.listeners.get(eventType)!;
    const index = listeners.indexOf(callback as EventCallback<any>);
    
    if (index !== -1) {
      listeners.splice(index, 1);
    }
  }

  /**
   * Emits an event of the specified type with the provided data
   * 
   * @param eventType The type of event to emit
   * @param data The event data to pass to listeners
   */
  public emit<T extends AnimationEventData>(eventType: AnimationEventType, data: T): void {
    if (!this.listeners.has(eventType)) return;
    
    const listeners = this.listeners.get(eventType)!;
    for (const listener of listeners) {
      try {
        listener(data);
      } catch (error) {
        console.error(`Error in animation event listener for ${eventType}:`, error);
      }
    }
  }

  /**
   * Removes all listeners for a specific event type or all events if no type is specified
   * 
   * @param eventType Optional event type to clear listeners for
   */
  public removeAllListeners(eventType?: AnimationEventType): void {
    if (eventType) {
      this.listeners.delete(eventType);
    } else {
      this.listeners.clear();
    }
  }

  /**
   * Returns the number of listeners for a specific event type
   * 
   * @param eventType The event type to check
   * @returns The number of listeners for this event type
   */
  public listenerCount(eventType: AnimationEventType): number {
    if (!this.listeners.has(eventType)) return 0;
    return this.listeners.get(eventType)!.length;
  }
}

// Export a singleton instance for shared usage
export const sharedEventEmitter = new AnimationEventEmitter(); 