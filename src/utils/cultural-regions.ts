import * as turf from '@turf/turf';

// Define cultural regions of Morocco with special significance for tourism
interface CulturalRegion {
  name: string;
  // Polygon coordinates in [longitude, latitude] format
  coordinates: [number, number][][]; 
  description: string;
  significance: 'high' | 'medium' | 'low';
  recommendedCameraSettings: {
    zoom: number;
    pitch: number;
    duration: number;
  };
}

// Define significant cultural regions
export const CULTURAL_REGIONS: CulturalRegion[] = [
  {
    name: 'Atlas Mountains',
    coordinates: [
      [
        [-8.0, 31.0], // Southwest
        [-8.0, 32.0], // Northwest
        [-5.0, 32.0], // Northeast
        [-5.0, 31.0], // Southeast
        [-8.0, 31.0]  // Close polygon
      ]
    ],
    description: 'Dramatic mountain ranges with traditional Berber villages',
    significance: 'high',
    recommendedCameraSettings: {
      zoom: 6.5,
      pitch: 60, // High pitch for dramatic mountain views
      duration: 4000
    }
  },
  {
    name: 'Sahara Desert Edge',
    coordinates: [
      [
        [-6.0, 29.5], // Southwest
        [-6.0, 30.5], // Northwest
        [-3.5, 30.5], // Northeast
        [-3.5, 29.5], // Southeast
        [-6.0, 29.5]  // Close polygon
      ]
    ],
    description: 'Gateway to the Sahara with desert landscapes and oases',
    significance: 'high',
    recommendedCameraSettings: {
      zoom: 7.0,
      pitch: 45,
      duration: 5000
    }
  },
  {
    name: 'Coastal Atlantic',
    coordinates: [
      [
        [-9.8, 30.0], // Southwest
        [-9.8, 32.5], // Northwest
        [-9.2, 32.5], // Northeast
        [-9.2, 30.0], // Southeast
        [-9.8, 30.0]  // Close polygon
      ]
    ],
    description: 'Historic port cities and Atlantic beaches',
    significance: 'medium',
    recommendedCameraSettings: {
      zoom: 7.5,
      pitch: 30,
      duration: 3000
    }
  },
  {
    name: 'Fes/Meknes Region',
    coordinates: [
      [
        [-5.5, 33.5], // Southwest
        [-5.5, 34.5], // Northwest
        [-4.5, 34.5], // Northeast
        [-4.5, 33.5], // Southeast
        [-5.5, 33.5]  // Close polygon
      ]
    ],
    description: 'Cultural heartland with ancient imperial cities',
    significance: 'high',
    recommendedCameraSettings: {
      zoom: 8.0,
      pitch: 40,
      duration: 4000
    }
  },
  {
    name: 'Marrakech Palm Groves',
    coordinates: [
      [
        [-8.2, 31.5], // Southwest
        [-8.2, 31.7], // Northwest
        [-7.8, 31.7], // Northeast
        [-7.8, 31.5], // Southeast
        [-8.2, 31.5]  // Close polygon
      ]
    ],
    description: 'Iconic palm oases surrounding the Red City',
    significance: 'medium',
    recommendedCameraSettings: {
      zoom: 9.0,
      pitch: 50,
      duration: 3500
    }
  },
  {
    name: 'Chefchaouen Blue City',
    coordinates: [
      [
        [-5.3, 35.15], // Southwest
        [-5.3, 35.25], // Northwest
        [-5.2, 35.25], // Northeast
        [-5.2, 35.15], // Southeast
        [-5.3, 35.15]  // Close polygon
      ]
    ],
    description: 'Famous blue-washed mountain town',
    significance: 'high',
    recommendedCameraSettings: {
      zoom: 10.0,
      pitch: 55,
      duration: 4000
    }
  }
];

// Function to check if a point is within a cultural region
export function findCulturalRegion(point: [number, number]): CulturalRegion | null {
  const pointFeature = turf.point(point);
  
  for (const region of CULTURAL_REGIONS) {
    try {
      // Create a polygon from the region coordinates
      const polygon = turf.polygon(region.coordinates);
      
      // Check if the point is inside the polygon
      if (turf.booleanPointInPolygon(pointFeature, polygon)) {
        return region;
      }
    } catch (error) {
      console.error(`Error checking if point is in ${region.name} region:`, error);
    }
  }
  
  return null;
}

// Function to check if a point is in a significant cultural region
export function isInSignificantCulturalRegion(point: [number, number]): boolean {
  const region = findCulturalRegion(point);
  return region !== null && region.significance === 'high';
}

// Function to determine appropriate camera settings based on current location
export function getCulturalRegionCameraSettings(point: [number, number]) {
  const region = findCulturalRegion(point);
  
  if (region) {
    return {
      ...region.recommendedCameraSettings,
      regionName: region.name,
      description: region.description
    };
  }
  
  // Default camera settings if not in a specific cultural region
  return {
    zoom: 6.0,
    pitch: 0,
    duration: 2000,
    regionName: null,
    description: null
  };
} 