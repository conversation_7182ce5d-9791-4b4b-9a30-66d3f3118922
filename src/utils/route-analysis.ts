import * as turf from '@turf/turf';

// Define terrain types for different regions
export type TerrainType = 'urban' | 'mountain' | 'desert' | 'coastal' | 'standard';

// Define route segment info
export interface RouteSegmentInfo {
  startPoint: [number, number];
  endPoint: [number, number];
  estimatedDriveTime: number; // in minutes
  recommendedStopover: boolean;
  terrain: TerrainType;
  distance: number; // in km
  description: string;
}

// Terrain regions for Morocco
const TERRAIN_REGIONS = [
  {
    name: 'Atlas Mountains',
    type: 'mountain' as TerrainType,
    polygon: [
      [-9.0, 30.5],
      [-9.0, 33.0],
      [-5.0, 33.0],
      [-5.0, 30.5],
      [-9.0, 30.5]
    ],
    speedFactor: 0.6 // 60% of standard speed due to winding roads
  },
  {
    name: 'Sahara Desert',
    type: 'desert' as TerrainType,
    polygon: [
      [-8.0, 28.0],
      [-8.0, 30.0],
      [-3.0, 30.0],
      [-3.0, 28.0],
      [-8.0, 28.0]
    ],
    speedFactor: 0.8 // 80% of standard speed due to desert roads
  },
  {
    name: 'Atlantic Coast',
    type: 'coastal' as TerrainType,
    polygon: [
      [-9.8, 30.0],
      [-9.8, 35.8],
      [-9.0, 35.8],
      [-9.0, 30.0],
      [-9.8, 30.0]
    ],
    speedFactor: 0.9 // 90% of standard speed
  },
  // Urban regions are checked separately based on city coordinates
];

// Average driving speeds in km/h by terrain
const AVERAGE_SPEEDS = {
  urban: 30,     // 30 km/h in cities
  mountain: 40,  // 40 km/h in mountain roads
  desert: 60,    // 60 km/h in desert roads
  coastal: 70,   // 70 km/h on coastal roads
  standard: 80   // 80 km/h on standard highways
};

// Determine terrain type based on coordinates
export function determineTerrainType(point: [number, number], cities: Array<{coordinates: [number, number], name: string}>): TerrainType {
  // Check if point is in urban area (near a city)
  const isUrban = cities.some(city => {
    try {
      const distance = turf.distance(
        turf.point(point),
        turf.point(city.coordinates),
        { units: 'kilometers' }
      );
      return distance < 10; // Consider within 10km of city center as urban
    } catch (error) {
      console.error('Error checking urban terrain:', error);
      return false;
    }
  });
  
  if (isUrban) return 'urban';
  
  // Check against terrain regions
  const pointFeature = turf.point(point);
  
  for (const region of TERRAIN_REGIONS) {
    try {
      const polygon = turf.polygon([[...region.polygon]]);
      if (turf.booleanPointInPolygon(pointFeature, polygon)) {
        return region.type;
      }
    } catch (error) {
      console.error(`Error checking terrain for ${region.name}:`, error);
    }
  }
  
  // Default terrain
  return 'standard';
}

// Generate route segments for time indicators
export function generateRouteSegments(
  routePoints: Array<[number, number]>,
  cities: Array<{coordinates: [number, number], name: string}>
): RouteSegmentInfo[] {
  if (routePoints.length < 2) return [];
  
  const segments: RouteSegmentInfo[] = [];
  const segmentSize = Math.max(Math.floor(routePoints.length / 10), 1);
  
  // Break route into logical segments
  for (let i = 0; i < routePoints.length - 1; i += segmentSize) {
    const nextIndex = Math.min(i + segmentSize, routePoints.length - 1);
    
    // Get segment start and end points
    const startPoint = routePoints[i];
    const endPoint = routePoints[nextIndex];
    
    // Calculate midpoint for terrain determination
    const midpoint = [
      (startPoint[0] + endPoint[0]) / 2,
      (startPoint[1] + endPoint[1]) / 2
    ] as [number, number];
    
    // Determine terrain type
    const terrain = determineTerrainType(midpoint, cities);
    
    // Calculate distance
    const distance = turf.distance(
      turf.point(startPoint),
      turf.point(endPoint),
      { units: 'kilometers' }
    );
    
    // Estimate drive time based on terrain (in minutes)
    const speed = AVERAGE_SPEEDS[terrain];
    const driveTimeMinutes = Math.round(distance / speed * 60);
    
    // Determine if a stopover is recommended (over 2 hours driving)
    const recommendedStopover = driveTimeMinutes > 120;
    
    // Create segment description
    let description = `${Math.round(distance)}km journey through ${terrain} terrain`;
    
    // Add city information if available
    const nearbyCity = findNearbyCityForSegment(startPoint, endPoint, cities, 20);
    if (nearbyCity) {
      description += ` near ${nearbyCity}`;
    }
    
    segments.push({
      startPoint,
      endPoint,
      estimatedDriveTime: driveTimeMinutes,
      recommendedStopover,
      terrain,
      distance,
      description
    });
  }
  
  return segments;
}

// Find a city near the route segment
function findNearbyCityForSegment(
  start: [number, number],
  end: [number, number],
  cities: Array<{coordinates: [number, number], name: string}>,
  maxDistance: number = 20
): string | null {
  // Create a line for the segment
  const line = turf.lineString([start, end]);
  
  for (const city of cities) {
    try {
      // Find the closest point on the line to the city
      const nearestPoint = turf.nearestPointOnLine(line, turf.point(city.coordinates));
      
      // Calculate distance from city to the nearest point on the line
      const distance = turf.distance(
        turf.point(city.coordinates),
        turf.point(nearestPoint.geometry.coordinates),
        { units: 'kilometers' }
      );
      
      if (distance <= maxDistance) {
        return city.name;
      }
    } catch (error) {
      console.error('Error finding nearby city:', error);
    }
  }
  
  return null;
}

// Format drive time for display
export function formatDriveTime(minutes: number): string {
  if (minutes < 60) {
    return `${minutes}min`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (remainingMinutes === 0) {
    return `${hours}h`;
  }
  
  return `${hours}h ${remainingMinutes}min`;
}

// Calculate estimated arrival time based on start time and duration
export function calculateEstimatedArrival(startTime: Date, durationMinutes: number): string {
  const arrivalTime = new Date(startTime.getTime() + durationMinutes * 60000);
  
  // Format as HH:MM
  const hours = arrivalTime.getHours().toString().padStart(2, '0');
  const minutes = arrivalTime.getMinutes().toString().padStart(2, '0');
  
  return `${hours}:${minutes}`;
}

// Calculate if we would be driving during night hours
export function isDrivingAtNight(startTime: Date, durationMinutes: number): boolean {
  const arrivalTime = new Date(startTime.getTime() + durationMinutes * 60000);
  
  // Consider night hours between 9PM (21:00) and 6AM (06:00)
  const startHour = startTime.getHours();
  const arrivalHour = arrivalTime.getHours();
  
  return startHour < 6 || startHour >= 21 || arrivalHour < 6 || arrivalHour >= 21;
} 