import type { PointOfInterest, Destination } from '@/types';
import type { LogLevel } from '@/types/LogLevel';

type LogCategory = 'marker' | 'css' | 'map' | 'route' | 'poi' | 'element';

interface LogEntry {
  timestamp: number;
  level: LogLevel;
  category: LogCategory;
  message: string;
  data?: any;
}

interface ElementState {
  element: HTMLElement;
  initialBounds: DOMRect;
  initialStyles: CSSStyleDeclaration;
  transformHistory: string[];
  positionHistory: { x: number; y: number; timestamp: number }[];
  styleChanges: {
    timestamp: number;
    property: string;
    oldValue: string;
    newValue: string;
  }[];
}

class DebugLogger {
  private static instance: DebugLogger;
  private logs: LogEntry[] = [];
  private maxLogs: number = 1000;
  private enabled: boolean = process.env.NODE_ENV === 'development';
  private mutationObserver: MutationObserver | null = null;
  private resizeObserver: ResizeObserver | null = null;
  private elementStates: Map<string, ElementState> = new Map();
  private markerCreationCount: Map<string, number> = new Map();
  private lastMarkerPositions: Map<string, { x: number; y: number }> = new Map();

  private constructor() {
    // Only set up observers in development mode to avoid performance issues
    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
      console.log('Debug logger initialized in development mode - observers enabled');
      // Set up the observers with a delay to avoid interfering with initial rendering
      setTimeout(() => {
        this.setupMutationObserver();
        this.setupResizeObserver();
      }, 2000); // Delay by 2 seconds to let initial rendering complete
    } else {
      console.log('Debug logger initialized in production mode - observers disabled');
      this.enabled = false;
    }
  }

  static getInstance(): DebugLogger {
    if (!DebugLogger.instance) {
      DebugLogger.instance = new DebugLogger();
    }
    return DebugLogger.instance;
  }

  private setupMutationObserver() {
    if (typeof document === 'undefined' || !this.enabled) return;
    
    // Add throttling for marker element changes
    const throttledMarkerChanges = new Map<string, number>();
    const THROTTLE_INTERVAL = 3000; // Increased to 3 seconds for less aggressive monitoring
    
    this.mutationObserver = new MutationObserver((mutations) => {
      // Skip if too many mutations to avoid performance issues
      if (mutations.length > 100) {
        console.log(`Skipping processing ${mutations.length} mutations to avoid performance issues`);
        return;
      }
      
      mutations.forEach(mutation => {
        if (mutation.target instanceof HTMLElement) {
          // Check if it's a map marker
          if (mutation.target.classList.contains('mapboxgl-marker') || 
              mutation.target.classList.contains('destination-marker')) {
            // Check throttling for this marker
            const markerId = mutation.target.id || 'unknown-marker';
            const now = Date.now();
            const lastLog = throttledMarkerChanges.get(markerId) || 0;
            
            // Only process if we're outside the throttle interval
            if (now - lastLog >= THROTTLE_INTERVAL) {
              throttledMarkerChanges.set(markerId, now);
              this.logElementChange('marker', mutation.target, mutation);
              
              // If position changed, track it
              if (mutation.type === 'attributes' && 
                 (mutation.attributeName === 'style' || mutation.attributeName === 'class')) {
                this.trackMarkerPosition(mutation.target);
              }
            }
          }
          // For POI elements (with throttling)
          else if (mutation.target.classList.contains('poi-marker') || 
                  mutation.target.classList.contains('poi-list-container')) {
            const now = Date.now();
            const lastLog = throttledMarkerChanges.get('poi-elements') || 0;
            
            if (now - lastLog >= THROTTLE_INTERVAL) {
              throttledMarkerChanges.set('poi-elements', now);
              this.logElementChange('poi', mutation.target, mutation);
              
              // Track POI list visibility if applicable
              if (mutation.target.classList.contains('poi-list-container')) {
                this.trackPOIListVisibility(mutation.target);
              }
            }
          }
        }
      });
    });
    
    // Observe only the map container to reduce scope
    const mapContainer = document.querySelector('.mapboxgl-map') || document.getElementById('explore-map-container');
    if (mapContainer) {
      this.mutationObserver.observe(mapContainer, {
        attributes: true,
        childList: true,
        subtree: true,
        attributeOldValue: true,
        characterData: false // Disable character data to reduce overhead
      });
      console.log('MutationObserver set up for map container');
    } else {
      // Fall back to document if map container not found
      console.log('Map container not found, observing document body instead');
      this.mutationObserver.observe(document.body, {
        attributes: true,
        childList: true,
        subtree: true,
        attributeOldValue: true,
        characterData: false
      });
    }
  }

  private setupResizeObserver() {
    this.resizeObserver = new ResizeObserver((entries) => {
      entries.forEach((entry) => {
        const targetEl = entry.target as HTMLElement;
        if (targetEl.classList.contains('destination-marker') ||
            targetEl.classList.contains('route-poi-list') ||
            targetEl.classList.contains('poi-list-container')) {
          this.logElementResize(targetEl, entry);
        }
      });
    });
  }

  private trackMarkerPosition(element: HTMLElement) {
    const markerId = element.id || element.getAttribute('data-marker-id') || 'unknown';
    const rect = element.getBoundingClientRect();
    const currentPos = { x: rect.left, y: rect.top };
    const lastPos = this.lastMarkerPositions.get(markerId);

    if (lastPos && (lastPos.x !== currentPos.x || lastPos.y !== currentPos.y)) {
      this.log('debug', 'marker', 'Marker position changed', {
        markerId,
        from: lastPos,
        to: currentPos,
        delta: {
          x: currentPos.x - lastPos.x,
          y: currentPos.y - lastPos.y
        }
      });
    }

    this.lastMarkerPositions.set(markerId, currentPos);
  }

  private trackPOIListVisibility(element: HTMLElement) {
    const isVisible = window.getComputedStyle(element).display !== 'none';
    const rect = element.getBoundingClientRect();
    
    this.log('debug', 'poi', 'POI list visibility update', {
      isVisible,
      position: {
        top: rect.top,
        left: rect.left,
        bottom: rect.bottom,
        right: rect.right
      },
      dimensions: {
        width: rect.width,
        height: rect.height
      },
      styles: {
        display: window.getComputedStyle(element).display,
        visibility: window.getComputedStyle(element).visibility,
        opacity: window.getComputedStyle(element).opacity,
        transform: window.getComputedStyle(element).transform
      }
    });
  }

  private logElementChange(type: 'marker' | 'poi', element: HTMLElement, mutation: MutationRecord) {
    const elementState = this.getOrCreateElementState(element);
    const currentBounds = element.getBoundingClientRect();
    const currentStyles = window.getComputedStyle(element);
    
    if (mutation.type === 'attributes' && mutation.attributeName) {
      const oldValue = mutation.oldValue || '';
      const newValue = element.getAttribute(mutation.attributeName) || '';
      
      elementState.styleChanges.push({
        timestamp: Date.now(),
        property: mutation.attributeName,
        oldValue,
        newValue
      });
    }

    const changes = {
      id: element.id || element.getAttribute('data-marker-id'),
      class: element.className,
      timestamp: Date.now(),
      mutationType: mutation.type,
      bounds: {
        current: {
          top: currentBounds.top,
          left: currentBounds.left,
          width: currentBounds.width,
          height: currentBounds.height
        },
        initial: {
          top: elementState.initialBounds.top,
          left: elementState.initialBounds.left,
          width: elementState.initialBounds.width,
          height: elementState.initialBounds.height
        }
      },
      styles: {
        position: currentStyles.position,
        transform: currentStyles.transform,
        zIndex: currentStyles.zIndex,
        display: currentStyles.display,
        visibility: currentStyles.visibility,
        opacity: currentStyles.opacity
      },
      styleHistory: elementState.styleChanges.slice(-5) // Keep last 5 style changes
    };

    this.log('debug', 'element', `${type.toUpperCase()} element changed`, changes);
  }

  private logElementResize(element: HTMLElement, entry: ResizeObserverEntry) {
    const bounds = entry.contentRect;
    this.log('debug', 'element', `Element resized: ${element.className}`, {
      id: element.id || element.getAttribute('data-marker-id'),
      bounds: {
        width: bounds.width,
        height: bounds.height,
        top: bounds.top,
        left: bounds.left
      },
      timestamp: Date.now()
    });
  }

  private getOrCreateElementState(element: HTMLElement): ElementState {
    const key = element.id || element.getAttribute('data-marker-id') || element.className;
    if (!this.elementStates.has(key)) {
      this.elementStates.set(key, {
        element,
        initialBounds: element.getBoundingClientRect(),
        initialStyles: window.getComputedStyle(element),
        transformHistory: [],
        positionHistory: [],
        styleChanges: []
      });
    }
    return this.elementStates.get(key)!;
  }

  private log(level: LogLevel, category: LogCategory, message: string, data?: any) {
    if (!this.enabled) return;

    const entry: LogEntry = {
      timestamp: Date.now(),
      level,
      category,
      message,
      data
    };

    this.logs.push(entry);
    if (this.logs.length > this.maxLogs) {
      this.logs.shift();
    }

    console.log(`[${category.toUpperCase()}] ${message}`, data || '');
  }

  public getLogs(options?: {
    level?: LogLevel,
    category?: LogCategory,
    since?: number,
    limit?: number
  }): LogEntry[] {
    let filtered = this.logs;
    
    if (options?.level) {
      filtered = filtered.filter(log => log.level === options.level);
    }
    
    if (options?.category) {
      filtered = filtered.filter(log => log.category === options.category);
    }
    
    const sinceFilterValue = options?.since;
    if (typeof sinceFilterValue === 'number') { // Explicitly check if sinceFilterValue is a number
      filtered = filtered.filter(log => log.timestamp >= sinceFilterValue);
    }

    if (options?.limit) {
      filtered = filtered.slice(-options.limit);
    }
    
    return filtered;
  }

  public getElementHistory(elementId: string) {
    return this.elementStates.get(elementId);
  }

  public cleanup() {
    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
    }
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    this.elementStates.clear();
    this.markerCreationCount.clear();
    this.lastMarkerPositions.clear();
    this.logs = [];
  }

  public logMapEvent(event: string, data?: any) {
    this.log('debug', 'map', `Map event: ${event}`, {
      ...data,
      timestamp: Date.now()
    });
  }

  public logRouteUpdate(destinations: Destination[]) {
    this.log('debug', 'route', 'Route updated', {
      destinationCount: destinations.length,
      coordinates: destinations.map(d => d.coordinates),
      path: destinations.map(d => d.name).join(' → '),
      timestamp: Date.now()
    });
  }

  public logCSSState(element: HTMLElement, context: string) {
    const styles = window.getComputedStyle(element);
    const boundingRect = element.getBoundingClientRect();
    
    this.log('debug', 'css', `CSS State - ${context}`, {
      element: element.className,
      id: element.id || element.getAttribute('data-marker-id'),
      styles: {
        position: styles.position,
        transform: styles.transform,
        zIndex: styles.zIndex,
        display: styles.display,
        visibility: styles.visibility,
        opacity: styles.opacity,
        width: styles.width,
        height: styles.height,
        backgroundColor: styles.backgroundColor
      },
      boundingRect: {
        top: boundingRect.top,
        left: boundingRect.left,
        width: boundingRect.width,
        height: boundingRect.height
      }
    });
  }

  public setupIntersectionObserver(elements: Element[]) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.target instanceof HTMLElement) {
          this.log('debug', 'element', 'Element Visibility Change', {
            target: entry.target.tagName,
            id: entry.target.id || entry.target.getAttribute('data-marker-id'),
            class: entry.target.className,
            isIntersecting: entry.isIntersecting,
            intersectionRatio: entry.intersectionRatio,
            boundingClientRect: entry.boundingClientRect
          });
        }
      });
    }, {
      threshold: [0, 0.5, 1]
    });

    elements.forEach(element => observer.observe(element));
    return observer;
  }

  public logMarkerCreation(markerId: string, coordinates: [number, number], data?: any) {
    const count = (this.markerCreationCount.get(markerId) || 0) + 1;
    this.markerCreationCount.set(markerId, count);
    
    this.log('debug', 'marker', `Creating marker: ${markerId} (creation #${count})`, { 
      coordinates, 
      ...data,
      creationCount: count,
      timestamp: Date.now()
    });
  }

  public logMarkerRemoval(markerId: string) {
    this.log('debug', 'marker', `Removing marker: ${markerId}`, {
      timestamp: Date.now()
    });
    this.lastMarkerPositions.delete(markerId);
  }

  public trackEventListener(element: HTMLElement, eventType: string, handler: EventListener) {
    const elementId = element.id || element.getAttribute('data-marker-id') || 'unknown';
    this.log('debug', 'element', `Event listener added`, {
      element: element.className,
      id: elementId,
      eventType,
      timestamp: Date.now()
    });
    
    // Return the original handler so it works with addEventListener
    return handler;
  }

  public logMarkerPosition(markerId: string, data?: any) {
    this.logs.push({
      category: 'marker',
      message: `Marker position updated: ${markerId}`,
      timestamp: Date.now(),
      level: 'info',
      data
    });
  }

  public logError(message: string, data?: any) {
    this.log('error', 'marker', message, {
      ...data,
      timestamp: Date.now()
    });
  }
}

export const logger = DebugLogger.getInstance();