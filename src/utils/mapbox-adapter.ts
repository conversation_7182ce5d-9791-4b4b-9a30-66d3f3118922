/**
 * MapboxAdapter - A type-safe wrapper for mapbox-gl
 * 
 * This adapter provides access to mapbox-gl functionality while working around
 * TypeScript module import issues that can occur with the library.
 */

// Use proper ES module import instead of require
import mapboxgl from 'mapbox-gl';

// Utility Types
export type MapInstance = any; // Use any to bypass type checking for now
export type LngLat = [number, number];
export type LngLatLike = LngLat | { lng: number; lat: number };
export type LngLatBounds = [LngLatLike, LngLatLike];
export type PaddingOptions = { top?: number; bottom?: number; left?: number; right?: number };

/**
 * Gets the Mapbox GL module
 * @returns The mapboxgl module
 */
export function getMapboxGL(): any {
  return mapboxgl;
}

/**
 * Creates a new Mapbox map instance
 * @param container HTML element or ID to use as container
 * @param options Map initialization options
 * @returns A new map instance
 */
export function createMap(container: string | HTMLElement, options: any): MapInstance {
  return new mapboxgl.Map({
    container,
    ...options
  });
}

/**
 * Removes a map instance
 * @param map Map instance to remove
 */
export function removeMap(map: MapInstance): void {
  if (map && typeof map.remove === 'function') {
    map.remove();
  }
}

/**
 * Creates a marker instance
 * @param options Marker options
 * @returns A new marker instance
 */
export function createMarker(options?: any): any {
  return new mapboxgl.Marker(options);
}

/**
 * Creates a popup instance
 * @param options Popup options
 * @returns A new popup instance
 */
export function createPopup(options?: any): any {
  return new mapboxgl.Popup(options);
}

/**
 * Creates a navigation control
 * @param options Control options
 * @returns A new navigation control
 */
export function createNavigationControl(options?: any): any {
  return new mapboxgl.NavigationControl(options);
}

/**
 * Converts a position to LngLat for mapbox
 * @param position Position object with lng/lat or lon/lat properties
 * @returns LngLat array [lng, lat]
 */
export function toLngLat(position: any): LngLat {
  if (Array.isArray(position) && position.length >= 2) {
    return [position[0], position[1]];
  }
  
  const lng = position.lng || position.lon || position.longitude || 0;
  const lat = position.lat || position.latitude || 0;
  
  return [lng, lat];
}

/**
 * Create a GeoJSON source
 * @param id Source ID
 * @param data GeoJSON data
 * @returns Source object
 */
export function createGeoJSONSource(id: string, data: any): any {
  return {
    id,
    type: 'geojson',
    data
  };
}

/**
 * Create a line layer
 * @param id Layer ID
 * @param source Source name
 * @param paint Paint properties
 * @param layout Layout properties
 * @returns Layer object
 */
export function createLineLayer(id: string, source: string, paint?: any, layout?: any): any {
  return {
    id,
    type: 'line',
    source,
    paint: paint || {},
    layout: layout || {}
  };
}

// Default export for compatibility
export default {
  getMapboxGL,
  createMap,
  removeMap,
  createMarker,
  createPopup,
  createNavigationControl,
  toLngLat,
  createGeoJSONSource,
  createLineLayer
}; 