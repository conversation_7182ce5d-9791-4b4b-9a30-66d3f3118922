/**
 * Returns a promise that resolves when the map is fully ready (both style loaded and map loaded)
 * This provides a reliable way to handle map readiness across components
 * @param map Mapbox GL map instance
 * @param maxWaitTime Maximum time to wait before resolving anyway (ms)
 * @returns Promise that resolves when map is ready
 */
export function whenMapReady(map: mapboxgl.Map, maxWaitTime: number = 5000): Promise<mapboxgl.Map> {
  return new Promise(resolve => {
    if (!map) {
      console.error('Cannot check readiness: Map is null or undefined');
      setTimeout(() => resolve(map), 100); // Resolve with minimal delay
      return;
    }

    // Check if already ready
    try {
      if (map.isStyleLoaded() && map.loaded && map.loaded()) {
        console.log(`✅ [${Date.now()}] Map already fully loaded and ready`);
        resolve(map);
        return;
      }
    } catch (e) {
      console.warn(`⚠️ [${Date.now()}] Error checking map readiness:`, e);
    }
    
    // Setup event listeners for when map becomes ready
    const checkReady = () => {
      try {
        const styleLoaded = map.isStyleLoaded();
        const mapLoaded = map.loaded && map.loaded();
        
        console.log(`🔍 [${Date.now()}] Map readiness check: styleLoaded=${styleLoaded}, mapLoaded=${mapLoaded}`);
        
        if (styleLoaded && mapLoaded) {
          console.log(`✅ [${Date.now()}] Map fully loaded and ready`);
          clearTimeout(failsafeTimeout);
          resolve(map);
        } else {
          // If not ready, check again after a short delay
          setTimeout(checkReady, 100);
        }
      } catch (e) {
        console.warn(`⚠️ [${Date.now()}] Error in map readiness check:`, e);
        // Continue checking despite error
        setTimeout(checkReady, 100);
      }
    };
    
    // Listen for relevant map events
    const onStyleLoad = () => {
      console.log(`🗺️ [${Date.now()}] Map style.load event received`);
      checkReady();
    };
    
    const onMapLoad = () => {
      console.log(`🗺️ [${Date.now()}] Map load event received`);
      checkReady();
    };
    
    // Add event listeners
    map.once('style.load', onStyleLoad);
    map.once('load', onMapLoad);
    
    // Start checking immediately
    checkReady();
    
    // Failsafe timeout - resolve after maxWaitTime regardless
    const failsafeTimeout = setTimeout(() => {
      console.warn(`⚠️ [${Date.now()}] Map readiness timeout reached (${maxWaitTime}ms), continuing anyway`);
      resolve(map);
    }, maxWaitTime);
  });
} 