/**
 * PERFORMANCE MONITOR - SIMPLE PERFORMANCE TRACKING
 * Tracks animation performance and identifies bottlenecks
 */

interface PerformanceMetric {
  name: string;
  duration: number;
  timestamp: number;
  category: 'animation' | 'rendering' | 'data' | 'user-interaction';
}

interface PerformanceThresholds {
  animation: number;    // 16ms for 60fps
  rendering: number;    // 8ms for rendering operations
  data: number;         // 100ms for data operations
  'user-interaction': number; // 50ms for user interactions
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private activeTimers = new Map<string, number>();
  private thresholds: PerformanceThresholds = {
    animation: 16,
    rendering: 8,
    data: 100,
    'user-interaction': 50
  };

  // Track a function execution time
  track<T>(
    name: string, 
    category: PerformanceMetric['category'], 
    fn: () => T
  ): T {
    const start = performance.now();
    
    try {
      const result = fn();
      
      // Handle async functions
      if (result instanceof Promise) {
        return result.then((value) => {
          this.recordMetric(name, category, start);
          return value;
        }).catch((error) => {
          this.recordMetric(name, category, start);
          throw error;
        }) as T;
      }
      
      this.recordMetric(name, category, start);
      return result;
      
    } catch (error) {
      this.recordMetric(name, category, start);
      throw error;
    }
  }

  // Start timing an operation
  start(name: string): void {
    this.activeTimers.set(name, performance.now());
  }

  // End timing an operation
  end(name: string, category: PerformanceMetric['category']): number {
    const startTime = this.activeTimers.get(name);
    if (!startTime) {
      console.warn(`[PerformanceMonitor] No start time found for: ${name}`);
      return 0;
    }

    const duration = performance.now() - startTime;
    this.activeTimers.delete(name);
    
    this.recordMetric(name, category, startTime, duration);
    return duration;
  }

  // Record a metric
  private recordMetric(
    name: string, 
    category: PerformanceMetric['category'], 
    startTime: number, 
    duration?: number
  ): void {
    const finalDuration = duration ?? (performance.now() - startTime);
    
    const metric: PerformanceMetric = {
      name,
      duration: finalDuration,
      timestamp: Date.now(),
      category
    };

    this.metrics.push(metric);

    // Check if duration exceeds threshold
    const threshold = this.thresholds[category];
    if (finalDuration > threshold) {
      console.warn(
        `⚠️ [PerformanceMonitor] Slow ${category}: ${name} took ${finalDuration.toFixed(2)}ms (threshold: ${threshold}ms)`
      );
    }

    // Keep only last 100 metrics to prevent memory leaks
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100);
    }
  }

  // Get performance summary
  getSummary(): {
    totalMetrics: number;
    averageDuration: number;
    slowOperations: PerformanceMetric[];
    byCategory: Record<string, { count: number; avgDuration: number; maxDuration: number }>;
  } {
    if (this.metrics.length === 0) {
      return {
        totalMetrics: 0,
        averageDuration: 0,
        slowOperations: [],
        byCategory: {}
      };
    }

    const totalDuration = this.metrics.reduce((sum, m) => sum + m.duration, 0);
    const averageDuration = totalDuration / this.metrics.length;

    // Find slow operations
    const slowOperations = this.metrics.filter(m => 
      m.duration > this.thresholds[m.category]
    );

    // Group by category
    const byCategory: Record<string, { count: number; avgDuration: number; maxDuration: number }> = {};
    
    for (const metric of this.metrics) {
      if (!byCategory[metric.category]) {
        byCategory[metric.category] = { count: 0, avgDuration: 0, maxDuration: 0 };
      }
      
      const cat = byCategory[metric.category];
      cat.count++;
      cat.avgDuration = (cat.avgDuration * (cat.count - 1) + metric.duration) / cat.count;
      cat.maxDuration = Math.max(cat.maxDuration, metric.duration);
    }

    return {
      totalMetrics: this.metrics.length,
      averageDuration,
      slowOperations,
      byCategory
    };
  }

  // Log performance summary
  logSummary(): void {
    const summary = this.getSummary();
    
    console.group('📊 Performance Summary');
    console.log(`Total operations tracked: ${summary.totalMetrics}`);
    console.log(`Average duration: ${summary.averageDuration.toFixed(2)}ms`);
    console.log(`Slow operations: ${summary.slowOperations.length}`);
    
    if (summary.slowOperations.length > 0) {
      console.group('⚠️ Slow Operations');
      summary.slowOperations.forEach(op => {
        console.log(`${op.name} (${op.category}): ${op.duration.toFixed(2)}ms`);
      });
      console.groupEnd();
    }
    
    console.group('📈 By Category');
    Object.entries(summary.byCategory).forEach(([category, stats]) => {
      console.log(`${category}: ${stats.count} ops, avg: ${stats.avgDuration.toFixed(2)}ms, max: ${stats.maxDuration.toFixed(2)}ms`);
    });
    console.groupEnd();
    
    console.groupEnd();
  }

  // Clear all metrics
  clear(): void {
    this.metrics = [];
    this.activeTimers.clear();
  }

  // Set custom thresholds
  setThresholds(thresholds: Partial<PerformanceThresholds>): void {
    this.thresholds = { ...this.thresholds, ...thresholds };
  }
}

// Create singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Convenience functions
export const trackAnimation = <T>(name: string, fn: () => T): T => 
  performanceMonitor.track(name, 'animation', fn);

export const trackRendering = <T>(name: string, fn: () => T): T => 
  performanceMonitor.track(name, 'rendering', fn);

export const trackData = <T>(name: string, fn: () => T): T => 
  performanceMonitor.track(name, 'data', fn);

export const trackUserInteraction = <T>(name: string, fn: () => T): T => 
  performanceMonitor.track(name, 'user-interaction', fn);

// Global performance monitoring (for debugging)
if (typeof window !== 'undefined') {
  (window as any).__performanceMonitor = performanceMonitor;
}

export default PerformanceMonitor;
