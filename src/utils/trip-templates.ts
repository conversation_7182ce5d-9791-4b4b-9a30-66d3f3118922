import type { PointOfInterest, Destination } from '@/types';

export interface TripTemplate {
  id: string;
  name: string;
  description: string;
  duration: number; // in days
  recommendedCities: string[];
  mustSeePOIs: string[];
  idealFor: string[];
  seasonality: string[];
  pace: 'relaxed' | 'moderate' | 'intense';
  imageUrl?: string;
}

export interface TripRecommendation {
  template: TripTemplate;
  matchScore: number; // 0-100
  reasons: string[];
}

// Define different trip templates based on duration and interests
export const tripTemplates: TripTemplate[] = [
  {
    id: 'imperial-cities-week',
    name: 'Imperial Cities Explorer',
    description: 'Experience Morocco\'s historic imperial cities with their rich culture, impressive architecture and bustling medinas.',
    duration: 7,
    recommendedCities: ['Marrakech', 'Fez', 'Rabat', 'Meknes'],
    mustSeePOIs: [
      'Jamaa el-Fna', 'Bahia Palace', 'Majorelle Garden', // Marrakech
      'Bab Bou Jeloud', 'Al-Qarawiyyin', 'Bou Inania Madrasa', // Fez
      'Hassan Tower', 'Kasbah of the Udayas', // <PERSON>bat
      '<PERSON><PERSON>', 'Mausoleum of Moulay Ismail' // Meknes
    ],
    idealFor: ['culture', 'history', 'architecture', 'shopping'],
    seasonality: ['spring', 'fall'],
    pace: 'moderate',
    imageUrl: '/images/templates/imperial-cities.jpg'
  },
  {
    id: 'coastal-adventure-week',
    name: 'Atlantic Coast Adventure',
    description: 'Explore Morocco\'s magnificent Atlantic coastline with beautiful beaches, historic port cities, and water sports.',
    duration: 7,
    recommendedCities: ['Casablanca', 'Essaouira', 'Agadir', 'El Jadida'],
    mustSeePOIs: [
      'Hassan II Mosque', 'Corniche', // Casablanca
      'Skala du Port', 'Essaouira Medina', // Essaouira
      'Agadir Beach', 'Souk El Had', // Agadir
      'Portuguese Cistern', 'Mazagan' // El Jadida
    ],
    idealFor: ['beaches', 'seafood', 'relaxation', 'water sports'],
    seasonality: ['summer', 'spring'],
    pace: 'relaxed',
    imageUrl: '/images/templates/coastal-adventure.jpg'
  },
  {
    id: 'desert-expedition-week',
    name: 'Sahara Desert Expedition',
    description: 'Journey through Morocco\'s stunning desert landscapes to experience the magic of the Sahara dunes and oases.',
    duration: 7,
    recommendedCities: ['Marrakech', 'Ouarzazate', 'Merzouga', 'Zagora'],
    mustSeePOIs: [
      'Erg Chebbi Dunes', 'Merzouga Desert Camp', // Merzouga
      'Aït Benhaddou', 'Atlas Film Studios', // Ouarzazate
      'Draa Valley', 'Tinfou Dunes', // Zagora
      'Todra Gorge', 'Dades Valley' // Near Merzouga route
    ],
    idealFor: ['adventure', 'photography', 'nature', 'stargazing'],
    seasonality: ['fall', 'winter', 'spring'],
    pace: 'moderate',
    imageUrl: '/images/templates/desert-expedition.jpg'
  },
  {
    id: 'morocco-highlights-10days',
    name: 'Morocco Highlights',
    description: 'The perfect introduction to Morocco combining imperial cities, mountain scenery, and desert experiences.',
    duration: 10,
    recommendedCities: ['Marrakech', 'Fez', 'Chefchaouen', 'Merzouga', 'Ouarzazate'],
    mustSeePOIs: [
      'Jamaa el-Fna', 'Majorelle Garden', // Marrakech
      'Bab Bou Jeloud', 'Al-Qarawiyyin', // Fez
      'Chefchaouen Blue City', 'Ras El Maa', // Chefchaouen
      'Erg Chebbi Dunes', // Merzouga
      'Aït Benhaddou' // Ouarzazate
    ],
    idealFor: ['first-time visitors', 'photography', 'culture', 'adventure'],
    seasonality: ['spring', 'fall'],
    pace: 'intense',
    imageUrl: '/images/templates/morocco-highlights.jpg'
  },
  {
    id: 'north-morocco-5days',
    name: 'Northern Morocco Express',
    description: 'A compact tour of Northern Morocco focusing on the blue city, ancient medinas, and Mediterranean influences.',
    duration: 5,
    recommendedCities: ['Tangier', 'Chefchaouen', 'Tetouan', 'Asilah'],
    mustSeePOIs: [
      'Kasbah Museum', 'Tangier Medina', 'Cape Spartel', // Tangier
      'Plaza Uta el-Hammam', 'Chefchaouen Medina', // Chefchaouen
      'Tetouan Medina', 'Royal Palace of Tetouan', // Tetouan
      'Asilah Ramparts', 'Paradise Beach' // Asilah
    ],
    idealFor: ['short trips', 'photography', 'culture', 'beaches'],
    seasonality: ['spring', 'summer', 'fall'],
    pace: 'moderate',
    imageUrl: '/images/templates/north-morocco.jpg'
  },
  {
    id: 'grand-morocco-14days',
    name: 'Grand Morocco Tour',
    description: 'The ultimate Moroccan experience covering all major regions from the Rif Mountains to the Sahara Desert.',
    duration: 14,
    recommendedCities: ['Casablanca', 'Rabat', 'Fez', 'Chefchaouen', 'Marrakech', 'Essaouira', 'Merzouga', 'Ouarzazate'],
    mustSeePOIs: [
      'Hassan II Mosque', // Casablanca
      'Kasbah of the Udayas', // Rabat
      'Al-Qarawiyyin', 'Bou Inania Madrasa', // Fez
      'Chefchaouen Blue City', // Chefchaouen
      'Jamaa el-Fna', 'Majorelle Garden', // Marrakech
      'Essaouira Medina', // Essaouira
      'Erg Chebbi Dunes', // Merzouga
      'Aït Benhaddou', // Ouarzazate
      'Todra Gorge',
      'Volubilis Roman Ruins'
    ],
    idealFor: ['complete experience', 'luxury', 'culture', 'nature', 'adventure'],
    seasonality: ['spring', 'fall'],
    pace: 'moderate',
    imageUrl: '/images/templates/grand-morocco.jpg'
  },
  {
    id: 'weekend-marrakech',
    name: 'Marrakech Weekend',
    description: 'A quick but fulfilling weekend trip to experience the magic of Marrakech.',
    duration: 3,
    recommendedCities: ['Marrakech'],
    mustSeePOIs: [
      'Jamaa el-Fna', 'Majorelle Garden', 'Bahia Palace', 
      'Koutoubia Mosque', 'Marrakech Medina', 'Saadian Tombs'
    ],
    idealFor: ['short trips', 'city break', 'shopping', 'food'],
    seasonality: ['all year'],
    pace: 'intense',
    imageUrl: '/images/templates/marrakech-weekend.jpg'
  }
];

/**
 * Recommends trip templates based on available days and user interests
 * @param availableDays Number of days available for travel
 * @param userInterests Array of user interests
 * @param selectedCities Array of cities already selected (optional)
 * @returns Array of trip recommendations sorted by match score
 */
export function recommendTrips(
  availableDays: number,
  userInterests: string[] = [],
  selectedCities: string[] = []
): TripRecommendation[] {
  const recommendations: TripRecommendation[] = [];
  
  // Filter templates that fit within available days (or up to 2 days more)
  const possibleTemplates = tripTemplates.filter(
    template => template.duration <= availableDays + 2
  );
  
  for (const template of possibleTemplates) {
    let score = 0;
    const reasons: string[] = [];
    
    // Score based on duration match (highest score for exact match)
    const durationDiff = Math.abs(template.duration - availableDays);
    if (durationDiff === 0) {
      score += 40;
      reasons.push(`Perfect duration match (${availableDays} days)`);
    } else if (durationDiff <= 1) {
      score += 30;
      reasons.push(`Very good duration match (${template.duration} vs ${availableDays} days)`);
    } else if (durationDiff <= 2) {
      score += 20;
      reasons.push(`Good duration match (${template.duration} vs ${availableDays} days)`);
    } else {
      score += 10;
      reasons.push(`Acceptable duration (${template.duration} days)`);
    }
    
    // Score based on interest match
    if (userInterests.length > 0) {
      const matchingInterests = template.idealFor.filter(interest => 
        userInterests.includes(interest)
      );
      
      const interestMatchPercentage = matchingInterests.length / template.idealFor.length;
      const interestScore = Math.round(interestMatchPercentage * 30);
      score += interestScore;
      
      if (matchingInterests.length > 0) {
        reasons.push(`Matches ${matchingInterests.length} of your interests: ${matchingInterests.join(', ')}`);
      }
    } else {
      // If no user interests specified, give a moderate score
      score += 15;
    }
    
    // Score based on already selected cities
    if (selectedCities.length > 0) {
      const matchingCities = template.recommendedCities.filter(city => 
        selectedCities.includes(city)
      );
      
      const cityMatchPercentage = matchingCities.length / selectedCities.length;
      const cityScore = Math.round(cityMatchPercentage * 30);
      score += cityScore;
      
      if (matchingCities.length > 0) {
        reasons.push(`Includes ${matchingCities.length} of your selected cities: ${matchingCities.join(', ')}`);
      }
    } else {
      // If no cities selected, give a moderate score
      score += 15;
    }
    
    // Add seasonality bonus (if current season matches)
    const currentMonth = new Date().getMonth();
    let currentSeason = '';
    
    if (currentMonth >= 2 && currentMonth <= 4) currentSeason = 'spring';
    else if (currentMonth >= 5 && currentMonth <= 7) currentSeason = 'summer';
    else if (currentMonth >= 8 && currentMonth <= 10) currentSeason = 'fall';
    else currentSeason = 'winter';
    
    if (template.seasonality.includes(currentSeason) || template.seasonality.includes('all year')) {
      score += 10;
      reasons.push(`Perfect for current ${currentSeason} season`);
    }
    
    // Cap score at 100
    score = Math.min(score, 100);
    
    recommendations.push({
      template,
      matchScore: score,
      reasons
    });
  }
  
  // Sort by match score (highest first)
  return recommendations.sort((a, b) => b.matchScore - a.matchScore);
}

/**
 * Find must-see POIs from a template based on available POI data
 * @param template The trip template
 * @param allPOIs Array of all available POIs
 * @returns Array of must-see POI objects
 */
export function findMustSeePOIs(
  template: TripTemplate,
  allPOIs: PointOfInterest[]
): PointOfInterest[] {
  const mustSeePOIs: PointOfInterest[] = [];
  
  for (const poiName of template.mustSeePOIs) {
    // Find POIs that match by name (case-insensitive partial match)
    const matchingPOIs = allPOIs.filter(poi => 
      poi.name.toLowerCase().includes(poiName.toLowerCase()) ||
      poiName.toLowerCase().includes(poi.name.toLowerCase())
    );
    
    if (matchingPOIs.length > 0) {
      // Add unique POIs (avoid duplicates)
      for (const poi of matchingPOIs) {
        if (!mustSeePOIs.some(p => p.id === poi.id)) {
          mustSeePOIs.push(poi);
        }
      }
    }
  }
  
  return mustSeePOIs;
}

/**
 * Find cities from a template based on available destination data
 * @param template The trip template
 * @param allDestinations Array of all available destinations
 * @returns Array of destination objects
 */
export function findTemplateDestinations(
  template: TripTemplate,
  allDestinations: Destination[]
): Destination[] {
  const templateDestinations: Destination[] = [];
  
  for (const cityName of template.recommendedCities) {
    // Find cities that match by name (case-insensitive partial match)
    const matchingCities = allDestinations.filter(dest => 
      dest.name.toLowerCase().includes(cityName.toLowerCase()) ||
      cityName.toLowerCase().includes(dest.name.toLowerCase())
    );
    
    if (matchingCities.length > 0) {
      // Add unique cities (avoid duplicates)
      for (const city of matchingCities) {
        if (!templateDestinations.some(d => d.id === city.id)) {
          templateDestinations.push(city);
        }
      }
    }
  }
  
  return templateDestinations;
}

/**
 * Get itinerary balance analysis based on selected cities and POIs
 * @param selectedCities Array of selected cities
 * @param selectedPOIs Array of selected POIs
 * @returns Object with balance analysis
 */
export interface ItineraryBalance {
  culturalScore: number; // 0-100
  adventureScore: number; // 0-100
  relaxationScore: number; // 0-100
  urbanRuralBalance: number; // 0-100 (0 = very urban, 100 = very rural)
  paceAnalysis: string;
  recommendations: string[];
}

export function analyzeItineraryBalance(
  selectedCities: Destination[],
  selectedPOIs: PointOfInterest[]
): ItineraryBalance {
  // Example analysis - in a real application, this would use more sophisticated data
  let culturalScore = 0;
  let adventureScore = 0;
  let relaxationScore = 0;
  let urbanRuralBalance = 50; // Start at neutral balance
  const recommendations: string[] = [];
  
  // Analyze cities
  const imperialCities = ['Marrakech', 'Fez', 'Rabat', 'Meknes'];
  const coastalCities = ['Casablanca', 'Essaouira', 'Agadir', 'Tangier', 'Asilah'];
  const mountainCities = ['Chefchaouen', 'Ifrane', 'Azrou', 'Imlil'];
  const desertCities = ['Merzouga', 'Ouarzazate', 'Zagora', 'Erfoud'];
  
  let imperialCount = 0;
  let coastalCount = 0;
  let mountainCount = 0;
  let desertCount = 0;
  
  for (const city of selectedCities) {
    const cityName = city.name;
    
    if (imperialCities.some(c => cityName.includes(c))) {
      imperialCount++;
      culturalScore += 15;
      urbanRuralBalance -= 10;
    }
    
    if (coastalCities.some(c => cityName.includes(c))) {
      coastalCount++;
      relaxationScore += 10;
      adventureScore += 5;
    }
    
    if (mountainCities.some(c => cityName.includes(c))) {
      mountainCount++;
      adventureScore += 10;
      relaxationScore += 5;
      urbanRuralBalance += 15;
    }
    
    if (desertCities.some(c => cityName.includes(c))) {
      desertCount++;
      adventureScore += 15;
      culturalScore += 5;
      urbanRuralBalance += 20;
    }
  }
  
  // Analyze POIs by categories using tags
  const culturalPOIs = selectedPOIs.filter(poi => 
    poi.tags.some((tag: string) => ['historic', 'museum', 'religious', 'architecture', 'culture', 'heritage'].includes(tag))
  ).length;
  
  const adventurePOIs = selectedPOIs.filter(poi => 
    poi.tags.some((tag: string) => ['nature', 'hiking', 'outdoor', 'mountain', 'desert', 'adventure', 'trek'].includes(tag))
  ).length;
  
  const relaxationPOIs = selectedPOIs.filter(poi => 
    poi.tags.some((tag: string) => ['beach', 'spa', 'garden', 'park', 'relaxation', 'wellness'].includes(tag))
  ).length;
  
  // Add POI analysis to scores
  culturalScore += culturalPOIs * 5;
  adventureScore += adventurePOIs * 5;
  relaxationScore += relaxationPOIs * 5;
  
  // Cap scores at 100
  culturalScore = Math.min(culturalScore, 100);
  adventureScore = Math.min(adventureScore, 100);
  relaxationScore = Math.min(relaxationScore, 100);
  
  // Cap urban/rural balance between 0-100
  urbanRuralBalance = Math.max(0, Math.min(100, urbanRuralBalance));
  
  // Generate pace analysis
  let paceAnalysis = '';
  const totalDestinations = selectedCities.length;
  
  if (totalDestinations <= 2 && selectedPOIs.length <= 5) {
    paceAnalysis = 'Very relaxed pace - plenty of time to explore each location thoroughly';
  } else if (totalDestinations <= 3 && selectedPOIs.length <= 8) {
    paceAnalysis = 'Relaxed pace - good balance of exploration and downtime';
  } else if (totalDestinations <= 5 && selectedPOIs.length <= 12) {
    paceAnalysis = 'Moderate pace - busy but not rushed';
  } else if (totalDestinations <= 7 && selectedPOIs.length <= 15) {
    paceAnalysis = 'Active pace - you\'ll see a lot but have limited time in each place';
  } else {
    paceAnalysis = 'Very intense pace - you\'ll see many places but with limited time at each';
    recommendations.push('Consider reducing the number of destinations for a more relaxed experience');
  }
  
  // Generate recommendations based on balance
  if (culturalScore > 70 && adventureScore < 30) {
    recommendations.push('Consider adding some adventure activities to balance your cultural experiences');
  }
  
  if (adventureScore > 70 && culturalScore < 30) {
    recommendations.push('Consider adding some cultural sites to complement your adventure activities');
  }
  
  if (relaxationScore < 20) {
    recommendations.push('Your itinerary is quite intense - consider adding some relaxation time');
  }
  
  if (imperialCount > 0 && desertCount === 0 && coastalCount === 0 && mountainCount === 0) {
    recommendations.push('Consider adding contrast to your city experiences with a desert, mountain, or coastal destination');
  }
  
  if (urbanRuralBalance < 20) {
    recommendations.push('Your trip is very urban-focused - consider adding some rural or natural areas for contrast');
  }
  
  if (urbanRuralBalance > 80) {
    recommendations.push('Your trip is very rural-focused - consider adding an urban experience for contrast');
  }
  
  return {
    culturalScore,
    adventureScore,
    relaxationScore,
    urbanRuralBalance,
    paceAnalysis,
    recommendations
  };
} 