import { supabase } from '@/integrations/supabase/client';
import type { Destination, PointOfInterest } from '@/types';
import { Vehicle } from '@/data/vehicles';
import { <PERSON><PERSON> } from '@/integrations/supabase/types';

// Helper function to safely parse JSON from Supabase
export function safelyParseJson<T>(data: Json, fallback: T): T {
  if (typeof data === 'string') {
    try {
      return JSON.parse(data) as T;
    } catch (e) {
      console.error('Error parsing JSON:', e);
      return fallback;
    }
  }
  
  return data as unknown as T || fallback;
}

// Save a route to Supabase
export async function saveRoute(
  userId: string,
  name: string,
  destinations: Destination[],
  vehicle: Vehicle | null,
  distance: number,
  duration: number,
  recommendedDays: number,
  pointsOfInterest: PointOfInterest[] = []
) {
  try {
    const { data, error } = await supabase
      .from('saved_routes')
      .insert({
        user_id: userId,
        name,
        destinations: destinations as unknown as <PERSON><PERSON>,
        vehicle: vehicle as unknown as Json,
        distance,
        duration,
        recommended_days: recommendedDays,
        points_of_interest: pointsOfInterest as unknown as <PERSON><PERSON>
      })
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error: any) {
    console.error('Error saving route:', error);
    return { data: null, error };
  }
}

// Submit a quote request to Supabase
export async function submitQuoteRequest(
  userId: string,
  fullName: string,
  email: string,
  phone: string | null,
  travelDate: string | null,
  numTravelers: number,
  specialRequests: string | null,
  routeDetails: {
    destinations: Destination[];
    vehicle: Vehicle | null;
    points_of_interest: PointOfInterest[];
    route_stats: {
      totalDistance: number;
      totalDuration: number;
      recommendedDays: number;
    };
  }
) {
  try {
    const { data, error } = await supabase
      .from('quote_requests')
      .insert({
        user_id: userId,
        full_name: fullName,
        email,
        phone,
        travel_date: travelDate,
        num_travelers: numTravelers,
        special_requests: specialRequests,
        route_details: routeDetails as unknown as Json,
        status: 'pending'
      })
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error: any) {
    console.error('Error submitting quote request:', error);
    return { data: null, error };
  }
}

// Fetch saved routes for a user
export async function fetchUserRoutes(userId: string) {
  try {
    const { data, error } = await supabase
      .from('saved_routes')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    
    // Parse the JSON data correctly
    const parsedData = data?.map(route => {
      return {
        id: route.id,
        name: route.name,
        destinations: safelyParseJson(route.destinations, []),
        vehicle: route.vehicle ? safelyParseJson(route.vehicle, null) : null,
        distance: route.distance || 0,
        duration: route.duration || 0,
        recommended_days: route.recommended_days || 0,
        created_at: route.created_at || new Date().toISOString(),
        points_of_interest: safelyParseJson(route.points_of_interest, [])
      };
    }) || [];

    return { data: parsedData, error: null };
  } catch (error: any) {
    console.error('Error fetching user routes:', error);
    return { data: [], error };
  }
}

// Delete a saved route
export async function deleteRoute(routeId: string) {
  try {
    const { error } = await supabase
      .from('saved_routes')
      .delete()
      .eq('id', routeId);

    if (error) throw error;
    return { success: true, error: null };
  } catch (error: any) {
    console.error('Error deleting route:', error);
    return { success: false, error };
  }
}

// Fetch quote requests for a user
export async function fetchUserQuoteRequests(userId: string) {
  try {
    const { data, error } = await supabase
      .from('quote_requests')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    
    // Parse the route_details JSON
    const parsedData = data?.map(request => {
      return {
        ...request,
        route_details: safelyParseJson(request.route_details, {
          destinations: [],
          vehicle: null,
          points_of_interest: [],
          route_stats: {
            totalDistance: 0,
            totalDuration: 0,
            recommendedDays: 0
          }
        })
      };
    }) || [];

    return { data: parsedData, error: null };
  } catch (error: any) {
    console.error('Error fetching quote requests:', error);
    return { data: [], error };
  }
}
