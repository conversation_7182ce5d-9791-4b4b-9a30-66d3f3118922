/**
 * Unified Route Manager - Single Source of Truth for all map routes
 * This centralizes all route creation and management in the application
 */
import mapboxgl from 'mapbox-gl';
import type { Destination, PointOfInterest } from '@/types';
import { logger } from '@/utils/debugLogger';

// Global route registry
const routeRegistry = new Map<string, {
  source: string;
  layer: string;
  type: 'main-route' | 'poi-connection';
}>();

// Keep track of active map instance - updated by each function call
let activeMapInstance: mapboxgl.Map | null = null;

/**
 * Creates a route between points
 */
export function createRoute(
  map: mapboxgl.Map,
  id: string,
  coordinates: [number, number][],
  options: {
    color?: string;
    width?: number;
    opacity?: number;
    dashArray?: number[];
    type?: 'main-route' | 'poi-connection';
    beforeLayerId?: string;
  } = {}
): boolean {
  // Store active map reference
  activeMapInstance = map;
  
  // Define unique IDs for this route
  const sourceId = `route-source-${id}`;
  const layerId = `route-layer-${id}`;
  
  // Remove existing route if it exists
  removeRoute(id);
  
  const {
    color = '#E27D60',
    width = 4,
    opacity = 0.9,
    dashArray,
    type = 'main-route',
    beforeLayerId = 'waterway-label' // Add before any labels to ensure it's below markers
  } = options;
  
  try {
    // Add new route source
    map.addSource(sourceId, {
      type: 'geojson',
      data: {
        type: 'Feature',
        properties: {},
        geometry: {
          type: 'LineString',
          coordinates
        }
      }
    });
    
    // Add route layer BELOW markers
    const layerConfig: mapboxgl.AnyLayer = {
      id: layerId,
      type: 'line',
      source: sourceId,
      layout: {
        'line-join': 'round',
        'line-cap': 'round',
        visibility: 'visible'
      },
      paint: {
        'line-color': color,
        'line-width': width,
        'line-opacity': opacity
      }
    };
    
    // Add dash array if specified
    if (dashArray) {
      layerConfig.paint = {
        ...layerConfig.paint,
        'line-dasharray': dashArray
      };
    }
    
    // Add layer before specified layer to ensure it's below markers
    if (beforeLayerId && map.getLayer(beforeLayerId)) {
      map.addLayer(layerConfig, beforeLayerId);
    } else {
      // Find the first symbol layer to place route below
      const firstSymbolId = getFirstSymbolLayerId(map);
      if (firstSymbolId) {
        map.addLayer(layerConfig, firstSymbolId);
      } else {
        map.addLayer(layerConfig);
      }
    }
    
    // Register the route
    routeRegistry.set(id, {
      source: sourceId,
      layer: layerId,
      type
    });
    
    logger.logMapEvent('Route created', {
      id,
      type,
      pointCount: coordinates.length
    });
    
    return true;
  } catch (err) {
    logger.logMapEvent('Error creating route', {
      id,
      error: String(err)
    });
    return false;
  }
}

/**
 * Updates an existing route with new coordinates
 */
export function updateRoute(
  map: mapboxgl.Map,
  id: string,
  coordinates: [number, number][]
): boolean {
  // Store active map reference
  activeMapInstance = map;
  
  const route = routeRegistry.get(id);
  if (!route) return false;
  
  try {
    const source = map.getSource(route.source) as mapboxgl.GeoJSONSource;
    if (!source || !source.setData) return false;
    
    source.setData({
      type: 'Feature',
      properties: {},
      geometry: {
        type: 'LineString',
        coordinates
      }
    });
    
    logger.logMapEvent('Route updated', {
      id,
      pointCount: coordinates.length
    });
    
    return true;
  } catch (err) {
    logger.logMapEvent('Error updating route', {
      id,
      error: String(err)
    });
    return false;
  }
}

/**
 * Removes a route from the map and registry
 */
export function removeRoute(id: string): boolean {
  const route = routeRegistry.get(id);
  if (!route) return false;
  
  try {
    // Use the active map instance instead of relying on a global variable
    const map = activeMapInstance;
    if (!map) {
      logger.logMapEvent('Cannot remove route - no active map instance', { id });
      return false;
    }
    
    if (map.getLayer(route.layer)) {
      map.removeLayer(route.layer);
    }
    
    if (map.getSource(route.source)) {
      map.removeSource(route.source);
    }
    
    routeRegistry.delete(id);
    
    logger.logMapEvent('Route removed', { id });
    return true;
  } catch (err) {
    logger.logMapEvent('Error removing route', {
      id,
      error: String(err)
    });
    return false;
  }
}

/**
 * Removes all routes from the map and registry
 */
export function clearAllRoutes(): void {
  if (!activeMapInstance) {
    logger.logMapEvent('Cannot clear routes - no active map instance');
    return;
  }
  
  const routesToRemove = [...routeRegistry.keys()];
  
  // Use array of IDs to avoid modification during iteration
  routesToRemove.forEach(id => {
    removeRoute(id);
  });
  
  routeRegistry.clear();
  logger.logMapEvent('All routes cleared');
}

/**
 * Creates a route between selected destinations
 */
export function createDestinationRoute(
  map: mapboxgl.Map,
  destinations: Destination[],
  options: {
    color?: string;
    width?: number;
    opacity?: number;
  } = {}
): string {
  // Store active map reference
  activeMapInstance = map;
  
  if (!destinations || destinations.length < 2) {
    return '';
  }
  
  // Generate a stable ID based on the destinations
  const routeId = `destination-route-${destinations.map(d => d.id).join('-')}`;
  
  // Get coordinates from selected destinations
  const coordinates = destinations.map(dest => dest.coordinates);
  
  // Create the route
  createRoute(map, routeId, coordinates, {
    ...options,
    type: 'main-route'
  });
  
  return routeId;
}

/**
 * Creates a connection line between a POI and its nearest destination
 */
export function createPOIConnectionLine(
  map: mapboxgl.Map,
  poi: PointOfInterest,
  destination: Destination,
  options: {
    color?: string;
    width?: number;
    opacity?: number;
    dashArray?: number[];
  } = {}
): string {
  // Store active map reference
  activeMapInstance = map;
  
  const lineId = `poi-connection-${poi.id}-${destination.id}`;
  
  // Create the route
  createRoute(map, lineId, [poi.coordinates, destination.coordinates], {
    color: options.color || '#D84727',
    width: options.width || 2,
    opacity: options.opacity || 0.8,
    dashArray: options.dashArray || [0.5, 1.5],
    type: 'poi-connection'
  });
  
  return lineId;
}

/**
 * Helper function to get the first symbol layer in the map style
 */
function getFirstSymbolLayerId(map: mapboxgl.Map): string | undefined {
  const layers = map.getStyle().layers;
  // Find the first symbol layer in the map style
  return layers.find((layer: mapboxgl.AnyLayer) => layer.type === 'symbol')?.id;
}

/**
 * Gets all registered routes
 */
export function getAllRoutes(): Map<string, {
  source: string;
  layer: string;
  type: 'main-route' | 'poi-connection';
}> {
  return new Map(routeRegistry);
}

/**
 * Returns the number of routes currently registered
 */
export function getRouteCount(): number {
  return routeRegistry.size;
} 