/**
 * Utility functions for map handling
 */

/**
 * Injects critical marker CSS to ensure markers are visible
 * This should be called as early as possible in the application lifecycle
 * (e.g., in App.tsx or index.tsx) to ensure styles are applied before map initialization
 */
export function injectMapboxMarkerCSS() {
  // Only run in browser environment
  if (typeof document === 'undefined') return;

  // Check if we've already injected the CSS
  if (document.getElementById('mapbox-marker-css')) return;

  console.log('Injecting critical Mapbox marker CSS');
  
  if (document.head) {
    // Create style element with high priority
    const style = document.createElement('style');
    style.id = 'mapbox-marker-css';
    document.head.insertBefore(style, document.head.firstChild);
    style.innerHTML = `
      /* Critical marker styles that will always be applied */
      .mapboxgl-marker {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        will-change: transform !important;
        visibility: visible !important;
        opacity: 1 !important;
        z-index: 100 !important;
        transform-origin: center !important;
        pointer-events: auto !important;
      }
      
      /* Debug marker for testing */
      .debug-marker {
        z-index: 150 !important;
      }
      
      /* Destination marker with inline styles for reliability */
      .destination-marker {
        width: 36px !important;
        height: 36px !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        cursor: pointer !important;
        position: relative !important;
        z-index: 2000 !important;
        pointer-events: auto !important;
        font-weight: bold !important;
      }
      
      /* Vehicle marker styles - HIGHEST PRIORITY */
      .vehicle-marker {
        width: 40px !important;
        height: 40px !important;
        border-radius: 50% !important;
        background-color: #FF5500 !important;
        border: 4px solid white !important;
        box-shadow: 0 0 15px 5px rgba(255, 85, 0, 0.7) !important;
        z-index: 10000 !important;
        position: absolute !important;
        pointer-events: none !important;
        opacity: 1 !important;
        visibility: visible !important;
        display: block !important;
        transform: translate(-50%, -50%);
      }
      
      /* Pulse animation for vehicle marker - fixed */
      @keyframes pulse {
        0% { transform: translate(-50%, -50%) scale(1); opacity: 0.8; }
        50% { transform: translate(-50%, -50%) scale(1.4); opacity: 0.4; }
        100% { transform: translate(-50%, -50%) scale(1); opacity: 0.8; }
      }
      
      /* Vehicle pulse specifically styled */
      .vehicle-pulse {
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        width: 60px !important;
        height: 60px !important;
        border-radius: 50% !important;
        background-color: rgba(255, 85, 0, 0.5) !important;
        animation: pulse 1.5s infinite !important;
        z-index: 200 !important;
        pointer-events: none !important;
        opacity: 1 !important;
        visibility: visible !important;
      }
      
      /* Make sure all markers are visible regardless of their parent container */
      #root .mapboxgl-marker,
      #root .destination-marker,
      #root .poi-marker,
      #root .vehicle-marker {
        visibility: visible !important;
        opacity: 1 !important;
        pointer-events: auto !important;
        z-index: 100 !important;
      }
      
      /* Vehicle markers get extra-high z-index */
      #root .vehicle-marker,
      #root .mapboxgl-marker.vehicle-marker {
        z-index: 10000 !important;
        visibility: visible !important;
        opacity: 1 !important;
      }
    `;
    console.log('Injected critical Mapbox marker CSS');
  }
}

/**
 * No longer sets up listeners as we use Mapbox's native positioning
 * This function is kept for backward compatibility
 */
export function setupMapRenderListener(map: mapboxgl.Map) {
  // No-op function now that we use Mapbox's native positioning
  console.log('Map render listeners are disabled - using Mapbox native positioning');
}

/**
 * No longer fixes markers as we use Mapbox's native positioning
 * This function is kept for backward compatibility
 */
function fixMarkers(map: mapboxgl.Map) {
  // No-op function now that we use Mapbox's native positioning
  console.log('Marker fixing is disabled - using Mapbox native positioning');
}

/**
 * Sets up global fixes for marker positioning
 */
export function setupGlobalMarkerPositionFixes() {
  // Only run in browser environment
  if (typeof window === 'undefined') return;

  // Fix for markers not showing by ensuring CSS is loaded early
  window.addEventListener('DOMContentLoaded', () => {
    injectMapboxMarkerCSS();
    console.log('Setup global marker position fixes');
  });
  
  // Set a global flag to indicate we've been called
  if (typeof window !== 'undefined') {
    (window as any).__markerFixesInitialized = true;
  }
}

/**
 * Utility for throttling function calls
 * This is still useful for performance optimization
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T, 
  wait: number
): (...args: Parameters<T>) => ReturnType<T> | undefined {
  let timeout: ReturnType<typeof setTimeout> | null = null;
  let previous = 0;
  
  return function(...args: Parameters<T>): ReturnType<T> | undefined {
    const now = Date.now();
    const remaining = wait - (now - previous);
    
    if (remaining <= 0 || remaining > wait) {
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
      }
      previous = now;
      return func(...args);
    } else if (!timeout) {
      timeout = setTimeout(() => {
        previous = Date.now();
        timeout = null;
        func(...args);
      }, remaining);
    }
    return undefined;
  };
}

/**
 * Removes country borders and filters out specific country labels from the map
 * Used to hide political boundaries, especially "Western Sahara"
 */
export const removeCountryBordersAndLabels = (map: mapboxgl.Map, countriesToHide: string[] = ['Western Sahara']): void => {
  if (!map) {
    console.error('Cannot remove country borders: Map is null');
    return;
  }
  
  if (!map.isStyleLoaded()) {
    console.warn('Map style not loaded yet, deferring country border removal');
    
    // Set up a retry mechanism
    const maxRetries = 3;
    let retryCount = 0;
    
    const attemptRemoval = () => {
      if (retryCount >= maxRetries) {
        console.warn(`Failed to remove country borders after ${maxRetries} attempts`);
        return;
      }
      
      if (map && map.isStyleLoaded()) {
        try {
          removeCountryBordersAndLabels(map, countriesToHide);
          console.log('Successfully removed country borders on retry');
        } catch (error) {
          console.error('Error removing borders on retry:', error);
        }
      } else {
        retryCount++;
        setTimeout(attemptRemoval, 500); // Try again in 500ms
      }
    };
    
    // Start the retry process
    setTimeout(attemptRemoval, 500);
    return;
  }

  try {
    // Fix for linter: Object is possibly 'null' or 'undefined'.
    const style = map.getStyle();
    const layers = style && style.layers ? style.layers : [];
    
    // Find and hide all admin boundary layers (borders)
    const boundaryLayers = layers.filter(layer => 
      layer.id.includes('admin') && 
      layer.id.includes('boundary')
    );
    
    console.log(`Found ${boundaryLayers.length} boundary layers to hide`);
    
    boundaryLayers.forEach(layer => {
      try {
        map.setLayoutProperty(layer.id, 'visibility', 'none');
        console.log(`Hidden boundary layer: ${layer.id}`);
      } catch (err) {
        console.warn(`Failed to hide boundary layer ${layer.id}:`, err);
      }
    });

    // Find and filter country label layers
    const labelLayers = layers.filter(layer => 
      layer.id.includes('country') && 
      (layer.id.includes('label') || layer.source === 'composite')
    );
    
    console.log(`Found ${labelLayers.length} label layers to filter`);
    
    labelLayers.forEach(layer => {
      try {
        // Get current filter
        const currentFilter = map.getFilter(layer.id) || ['all'];
        
        // For each country to hide, add a filter condition
        const filters = countriesToHide.map(country => 
          ['!=', ['get', 'name_en'], country]
        );

        // Combine with existing filter
        const newFilter = ['all', ...filters, ...(Array.isArray(currentFilter) ? [currentFilter] : [])];
        
        map.setFilter(layer.id, newFilter);
        console.log(`Filtered labels in layer: ${layer.id}`);
      } catch (err) {
        console.warn(`Failed to filter labels in layer ${layer.id}:`, err);
      }
    });
    
    console.log('Successfully removed country borders and filtered country labels');
  } catch (error) {
    console.error('Error removing country borders and labels:', error);
  }
};