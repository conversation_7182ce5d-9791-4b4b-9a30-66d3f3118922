/**
 * Utility functions for working with Position types
 */

import { Position, PositionObject } from '../types/Position';

/**
 * Converts a Position object to a GeoJSON Position array [longitude, latitude]
 */
export function positionToGeoJSON(position: Position): [number, number] {
  if (Array.isArray(position)) {
    return position;
  }
  
  return [position.lng, position.lat];
}

/**
 * Converts a Position object to a GeoJSON Position array [longitude, latitude]
 * This is an alias for positionToGeoJSON for better semantic clarity
 */
export function positionToCoordinates(position: Position): [number, number] {
  return positionToGeoJSON(position);
}

/**
 * Converts GeoJSON coordinates to a Position object
 */
export function coordinatesToPosition(coordinates: [number, number], name?: string): PositionObject {
  return {
    lng: coordinates[0],
    lat: coordinates[1],
  };
}