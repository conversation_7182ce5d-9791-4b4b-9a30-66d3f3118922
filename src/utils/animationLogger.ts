/**
 * animationLogger.ts
 * Centralized logging utility for animation-related components.
 * This module extends the existing AnimationDebugTools with more animation-specific
 * logging features and standardizes the logging format.
 */

import AnimationDebugTools from '../components/map/animation/AnimationDebugTools';
import type { LogLevel } from '../types/LogLevel';

type AnimationContext = 'vehicle' | 'camera' | 'route' | 'poi' | 'ui' | 'animation' | 'state' | 'marker';
// For backwards compatibility
type ExtendedLogLevel = LogLevel | 'warning';

/**
 * Format timestamp for logs
 */
const getFormattedTimestamp = (): string => {
  return new Date().toISOString();
};

/**
 * Get emoji for animation context
 */
const getContextEmoji = (context: AnimationContext): string => {
  switch (context) {
    case 'vehicle': return '🚗';
    case 'camera': return '🎥';
    case 'route': return '🛣️';
    case 'poi': return '🏞️';
    case 'ui': return '🖥️';
    case 'animation': return '🔄';
    case 'state': return '📊';
    case 'marker': return '📍';
    default: return '🔧';
  }
};

/**
 * Convert 'warning' to 'warn' for compatibility with LogLevel
 */
const normalizeLogLevel = (level: ExtendedLogLevel): LogLevel => {
  return level === 'warning' ? 'warn' : level as LogLevel;
};

/**
 * Generic animation log function
 */
const logAnimation = (
  level: ExtendedLogLevel,
  context: AnimationContext,
  message: string,
  data?: any
): void => {
  // Format with context emoji
  const emoji = getContextEmoji(context);
  const formattedMessage = `${emoji} [${context.toUpperCase()}] ${message}`;
  
  // Use AnimationDebugTools for actual logging
  AnimationDebugTools.log(normalizeLogLevel(level), formattedMessage, data);
};

/**
 * Vehicle-specific logging
 */
const logVehicle = (level: LogLevel, message: string, data?: any): void => {
  logAnimation(level, 'vehicle', message, data);
};

/**
 * Camera-specific logging
 */
const logCamera = (level: LogLevel, message: string, data?: any): void => {
  logAnimation(level, 'camera', message, data);
};

/**
 * Route-specific logging
 */
const logRoute = (level: LogLevel, message: string, data?: any): void => {
  logAnimation(level, 'route', message, data);
};

/**
 * Animation state logging
 */
const logAnimationState = (level: LogLevel, message: string, data?: any): void => {
  logAnimation(level, 'state', message, data);
};

/**
 * UI-related logging
 */
const logUI = (level: LogLevel, message: string, data?: any): void => {
  logAnimation(level, 'ui', message, data);
};

/**
 * POI-related logging
 */
const logPOI = (level: LogLevel, message: string, data?: any): void => {
  logAnimation(level, 'poi', message, data);
};

/**
 * Track animation flow (sequence of operations)
 */
const trackAnimationFlow = (step: string, data?: any): void => {
  logAnimation('info', 'animation', `[FLOW] ${step}`, data);
  
  // Add visual feedback for debugging in development
  if (process.env.NODE_ENV !== 'production') {
    const debugElement = document.getElementById('animation-flow-debug');
    if (!debugElement) {
      const newDebugElement = document.createElement('div');
      newDebugElement.id = 'animation-flow-debug';
      Object.assign(newDebugElement.style, {
        position: 'fixed',
        top: '10px',
        right: '10px',
        width: '300px',
        maxHeight: '400px',
        overflowY: 'auto',
        backgroundColor: 'rgba(0,0,0,0.8)',
        color: 'white',
        padding: '10px',
        fontFamily: 'monospace',
        fontSize: '12px',
        zIndex: '10000',
        borderRadius: '5px'
      });
      document.body.appendChild(newDebugElement);
    }

    const logElement = document.getElementById('animation-flow-debug');
    if (logElement) {
      const entry = document.createElement('div');
      entry.innerHTML = `<span style="color: #4CAF50">${new Date().toISOString().slice(11, 23)}</span> ${step}`;
      logElement.insertBefore(entry, logElement.firstChild);
      
      // Keep only the last 20 entries
      while (logElement.childElementCount > 20) {
        logElement.removeChild(logElement.lastChild as Node);
      }
    }
  }
};

/**
 * Performance tracking functions
 */
const startPerformanceTracking = (): void => {
  AnimationDebugTools.startPerformanceCapture();
};

const endPerformanceTracking = (operation: string): number => {
  return AnimationDebugTools.endPerformanceCapture(operation);
};

// Re-export the existing AnimationDebugTools getLogs and clearLogs functions
const { getLogs, clearLogs } = AnimationDebugTools;

const AnimationLogger = {
  // Generic logging
  log: logAnimation,
  
  // Context-specific logging
  vehicle: logVehicle,
  camera: logCamera,
  route: logRoute,
  state: logAnimationState,
  ui: logUI,
  poi: logPOI,
  
  // Flow tracking
  trackFlow: trackAnimationFlow,
  
  // Performance
  startPerformance: startPerformanceTracking,
  endPerformance: endPerformanceTracking,
  
  // Log retrieval
  getLogs,
  clearLogs
};

export default AnimationLogger; 