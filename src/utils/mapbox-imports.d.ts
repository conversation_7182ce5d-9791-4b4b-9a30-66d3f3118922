/**
 * Type declarations for mapbox-imports utility module
 */

declare module '@/utils/mapbox-imports' {
  import * as mapboxgl from 'mapbox-gl';
  
  // Re-export the mapboxgl namespace
  export default mapboxgl;
  
  // Re-export common types for convenience
  export type Map = mapboxgl.Map;
  export type LngLatLike = mapboxgl.LngLatLike;
  export type LngLatBounds = mapboxgl.LngLatBounds;
  export type MapboxOptions = mapboxgl.MapboxOptions;
  export type GeoJSONSource = mapboxgl.GeoJSONSource;
  export type Layer = mapboxgl.Layer;
  
  // Utility functions
  export function createMap(options: any): mapboxgl.Map;
  export function destroyMap(map: mapboxgl.Map): void;
} 