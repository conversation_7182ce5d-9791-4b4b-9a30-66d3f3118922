import {
  POICategory,
  PointOfInterest,
  Destination,
  isValidCoordinates,
  normalizePOI as canonicalNormalizePOI
} from '@/types';

import {
  Position,
  PositionObject,
  isPositionObject,
  isPositionTuple,
  toPositionObject,
  toPositionTuple,
  calculateDistance
} from '../types/Position';

// import { toKatakana } from 'wanakana';

// Helper function to map string types to POICategory
function mapStringToPOICategory(typeStr: string | undefined): POICategory {
  if (!typeStr) return 'other';
  const lowerType = typeStr.toLowerCase();
  switch (lowerType) {
    case 'restaurant':
    case 'cafe': // map cafe to restaurant for simplicity or add 'cafe' to POICategory
      return 'restaurant';
    case 'hotel':
    case 'accommodation':
    case 'riad':
      return 'hotel';
    case 'attraction':
    case 'landmark':
    case 'monument':
      return 'attraction';
    case 'shopping':
    case 'market':
      return 'shopping';
    case 'entertainment':
      return 'entertainment';
    case 'nature':
    case 'garden':
    case 'park':
    case 'waterfall':
    case 'lake':
    case 'desert': // Added desert as nature
      return 'nature';
    case 'cultural':
    case 'museum':
    case 'historic site':
    case 'madrasa':
    case 'tannery':
      return 'cultural';
    case 'activity': // Broad category, could be refined if more specific types exist
      return 'activity'; // Assuming 'activity' is a valid POICategory, if not, map to 'other' or a suitable one
    default:
      return 'other';
  }
}

/**
 * Converts a POI object (typically from destination data) to a PointOfInterest object.
 * Ensures consistent data structure for map components.
 * @param poi The POI object to convert.
 * @returns A PointOfInterest object.
 */
export function convertPOIToPointOfInterest(poi: any): PointOfInterest {
  // console.log('[convertPOIToPointOfInterest] Input:', poi); // Keep for debugging if needed
  
  // Delegate all normalization to the canonical function from POITypes.ts
  const normalizedPoi = canonicalNormalizePOI(poi);

  // Optional: Add any specific logging or minor adjustments here if needed,
  // but primary normalization should be done by canonicalNormalizePOI.
  // For example, logging for a specific POI if it helps debugging:
  if (normalizedPoi.id === '1' || normalizedPoi.name === 'Casablanca') {
    // console.log('[convertPOIToPointOfInterest after canonical] Output:', JSON.stringify(normalizedPoi));
  }

  return normalizedPoi;
}

/**
 * Filters POIs for a given city by name or proximity.
 * @param pois Array of PointOfInterest
 * @param city The city (Destination) to filter for
 * @param destinations All available destinations (for fallback lookups)
 * @param maxDistance Maximum distance in km for proximity fallback (default 20)
 */
export function filterPOIsForCity(
  pois: PointOfInterest[],
  city: Destination,
  destinations: Destination[],
  maxDistance: number = 20
): PointOfInterest[] {
  // If city is null or undefined, return empty array
  if (!city) return [];
  
  console.log(`[filterPOIsForCity] Starting filtering for ${city.name} with ${pois.length} POIs`);
  
  const cityName = city.name.toLowerCase().trim();
  const cityNameVariations = [
    cityName,
    cityName.replace('marrakech', 'marrakesh'),
    cityName.replace('marrakesh', 'marrakech'),
    cityName.replace('fes', 'fez'),
    cityName.replace('fez', 'fes')
  ];

  // First check for exact city name matches
  const exactMatches = pois.filter(poi => {
    const poiCity = (poi.location || '').toLowerCase().trim();
    return cityNameVariations.some(variation => poiCity === variation);
  });
  
  if (exactMatches.length > 0) {
    console.log(`[filterPOIsForCity] Found ${exactMatches.length} exact city name matches for ${city.name}`);
    return exactMatches;
  }

  // Extract city coordinates
  let cityCoordsObj: PositionObject | null = null;
  
  // Try to get coordinates in priority order
  if (city.position) {
    cityCoordsObj = toPositionObject(city.position);
  } else {
    const found = destinations.find(d => 
      cityNameVariations.some(variation => d.name.toLowerCase().trim() === variation)
    );
    if (found && found.position) {
      cityCoordsObj = toPositionObject(found.position);
    }
  }
  
  if (!cityCoordsObj) {
    console.warn(`[filterPOIsForCity] No valid coordinates for city ${city.name}, returning empty array`);
    return [];
  }

  const cityLat = cityCoordsObj.lat;
  const cityLng = cityCoordsObj.lng;

  // Calculate distance between city and each POI
  const nearbyPOIs = pois.filter(poi => {
    const poiCoordsObj = toPositionObject(poi.position);
    
    if (!poiCoordsObj) {
      console.warn(`[filterPOIsForCity] No valid coordinates for POI ${poi.name || poi.id}`);
      return false;
    }
    
    // Calculate distance using Haversine formula
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = (poiCoordsObj.lat - cityLat) * (Math.PI / 180);
    const dLon = (poiCoordsObj.lng - cityLng) * (Math.PI / 180);
    
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(cityLat * (Math.PI / 180)) * Math.cos(poiCoordsObj.lat * (Math.PI / 180)) * 
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
      
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c; // Distance in kilometers
    
    return distance <= maxDistance;
  });
  
  console.log(`[filterPOIsForCity] Found ${nearbyPOIs.length} POIs near ${city.name} within ${maxDistance}km`);
  
  // If no nearby POIs found, return empty array
  if (nearbyPOIs.length === 0) {
    console.log(`[filterPOIsForCity] No nearby POIs found, returning empty array`);
    return [];
  }
  
  return nearbyPOIs;
}

export function normalizeDestination(dest: any): Destination {
  return {
    ...dest,
    id: dest.id ?? dest.name ?? Math.random().toString(36).slice(2),
    name: dest.name ?? '',
    coordinates: isValidCoordinates(dest.coordinates)
      ? dest.coordinates
      : (dest.position && typeof dest.position.lng === 'number' && typeof dest.position.lat === 'number'
        ? [dest.position.lng, dest.position.lat]
        : [0, 0]),
    position: (dest.position && typeof dest.position.lng === 'number' && typeof dest.position.lat === 'number')
      ? dest.position
      : (isValidCoordinates(dest.coordinates)
        ? { lng: dest.coordinates[0], lat: dest.coordinates[1], name: dest.name }
        : { lng: 0, lat: 0, name: dest.name }),
    distance: typeof dest.distance === 'number' ? String(dest.distance) : dest.distance,
  };
}

// export function normalizePOI(poi: any): PointOfInterest {
//   return {
//     ...poi,
//     id: poi.id ?? poi.name ?? Math.random().toString(36).slice(2),
//     name: poi.name ?? '',
//     coordinates: isValidCoordinates(poi.coordinates)
//       ? poi.coordinates
//       : (poi.position && typeof poi.position.lng === 'number' && typeof poi.position.lat === 'number'
//         ? [poi.position.lng, poi.position.lat]
//         : [0, 0]),
//     position: (poi.position && typeof poi.position.lng === 'number' && typeof poi.position.lat === 'number')
//       ? poi.position
//       : (isValidCoordinates(poi.coordinates)
//         ? { lng: poi.coordinates[0], lat: poi.coordinates[1], name: poi.name }
//         : { lng: 0, lat: 0, name: poi.name }),
//   };
// }

// export function convertRawPOIToPointOfInterest(
//   rawPoi: RawPOI, 
//   categoryOverride?: POICategory
// ): PointOfInterest {
//   const position = toPositionObject(rawPoi.coordinates); 

//   return {
//     id: rawPoi.id.toString(),
//     name: rawPoi.name,
//     position: position, 
//     latitude: position ? position.lat : 0, 
//     longitude: position ? position.lng : 0, 
//     category: categoryOverride || mapStringToPOICategory(rawPoi.tags), 
//     tags: rawPoi.tags,
//     imageUrl: rawPoi.image_url,
//     website: rawPoi.website,
//     phoneNumber: rawPoi.phone_number,
//     address: rawPoi.address,
//     city: rawPoi.city,
//     country: rawPoi.country,
//     rating: typeof rawPoi.rating === 'number' ? rawPoi.rating : 0,
//     openingHours: Array.isArray(rawPoi.opening_hours) ? rawPoi.opening_hours : [],
//     additionalDetails: typeof rawPoi.additional_details === 'object' && rawPoi.additional_details !== null
//         ? rawPoi.additional_details
//         : {},
//     source: rawPoi.source || 'Unknown',
//     icon: rawPoi.icon, 
//     temporarilyClosed: rawPoi.temporarily_closed || false,
//     permanentlyClosed: rawPoi.permanently_closed || false,
//     location: rawPoi.location,
//     activity: mapStringToPOICategory(rawPoi.tags), 
//   };
// }

export function getPositionFromPointOfInterest(poi: PointOfInterest): PositionObject {
  if (poi.position) {
    const posObj = toPositionObject(poi.position);
    if (posObj) return posObj;
  }
  // Fallback if poi.position is not available. Try poi.coordinates if it exists.
  if (Array.isArray(poi.coordinates) && poi.coordinates.length === 2) {
    const posObjFromCoords = toPositionObject(poi.coordinates as [number, number]);
    if (posObjFromCoords) return posObjFromCoords;
  }
  // Removed direct access to poi.latitude and poi.longitude as they are not on PointOfInterest type
  console.warn('[getPositionFromPointOfInterest] POI missing valid position or coordinates:', poi.id, poi.name);
  return { lat: 0, lng: 0 }; // Default fallback
}

/**
 * Calculates the distance between two points of interest.
 *
 * @param poi1 The first point of interest.
 * @param poi2 The second point of interest.
 * @returns The distance in kilometers, or null if positions are invalid.
 */
export function calculateDistanceBetweenPOIs(poi1: PointOfInterest, poi2: PointOfInterest): number | null {
  const pos1 = getPositionFromPointOfInterest(poi1);
  const pos2 = getPositionFromPointOfInterest(poi2);
  // calculateDistance already handles null positions if getPositionFromPointOfInterest returns default {0,0}
  // but it itself returns number | null.
  return calculateDistance(pos1, pos2);
}

/**
 * Finds the closest POI from a list to a given reference POI.
 *
 * @param referencePoi The reference point of interest.
 * @param poiList A list of points of interest to search through.
 * @returns The closest POI from the list, or null if the list is empty.
 */
export function findClosestPOI(referencePoi: PointOfInterest, poiList: PointOfInterest[]): PointOfInterest | null {
  if (!poiList || poiList.length === 0) {
    return null;
  }

  let closestPoi: PointOfInterest | null = null;
  let minDistance = Infinity;

  const referencePos = getPositionFromPointOfInterest(referencePoi);

  for (const poi of poiList) {
    if (poi.id === referencePoi.id) continue;

    const currentPos = getPositionFromPointOfInterest(poi);
    const distance = calculateDistance(referencePos, currentPos);

    if (distance !== null && distance < minDistance) {
      minDistance = distance;
      closestPoi = poi;
    }
  }

  return closestPoi;
}

/**
 * Converts various coordinate formats to a standardized Position object.
 * Useful for ensuring consistent position data throughout the application.
 *
 * @param coords The coordinates to convert, can be POIPosition, [number, number], or GeoJSONPoint.
 * @returns A Position object ({ lat, lng }).
 */
export function ensurePositionObject(coords: Position | [number, number]): PositionObject {
  const posObj = toPositionObject(coords);
  if (posObj) return posObj;
  // Fallback for invalid coords, though toPositionObject should handle basic cases
  console.warn('[ensurePositionObject] Failed to convert coords to PositionObject, returning default:', coords);
  return { lat: 0, lng: 0};
}

// Helper function to safely extract coordinates and convert to Position object
export const getSafePosition = (item: any): PositionObject | null => {
  if (!item) return null;

  if (item.position) {
    const posObj = toPositionObject(item.position);
    if (posObj) return posObj;
  }
  if (item.coordinates) {
    const posObj = toPositionObject(item.coordinates);
    if (posObj) return posObj;
  }
  if (typeof item.latitude === 'number' && typeof item.longitude === 'number') {
    return { lat: item.latitude, lng: item.longitude };
  }
  // Ensure array is [lng, lat] before converting
  if (Array.isArray(item) && item.length === 2 && typeof item[0] === 'number' && typeof item[1] === 'number') {
    const posObj = toPositionObject(item as [number, number]);
    if (posObj) return posObj;
  }
  return null;
};

/**
 * Filters a list of POIs to find those within a given radius of a central point.
 *
 * @param centerPoi The central point of interest.
 * @param allPois A list of all POIs to filter.
 * @param radiusKm The radius in kilometers.
 * @returns A list of POIs within the specified radius.
 */
export function filterPOIsByRadius(
  centerPoi: PointOfInterest,
  allPois: PointOfInterest[],
  radiusKm: number
): PointOfInterest[] {
  const centerPos = getPositionFromPointOfInterest(centerPoi);
  return allPois.filter(poi => {
    if (poi.id === centerPoi.id) return false;
    const poiPos = getPositionFromPointOfInterest(poi);
    const distance = calculateDistance(centerPos, poiPos);
    return distance !== null && distance <= radiusKm;
  });
}

// Example category mapping function (replace with your actual logic)
// const મુખ્યવર્ગ = 'someDefaultCategory'; // Define if used by mapStringToPOICategory, or pass as arg
// function категороизироватьPOI(tags: string[] | undefined, મુખ્યવર્ગ: string): POICategory {
//   // Placeholder implementation
//   if (tags && tags.includes('museum')) return 'museum';
//   return મુખ્યવર્ગ as POICategory;
// }

// Utility for converting POI coordinates to a GeoJSON Point feature
export const poiToGeoJSONFeature = (poi: PointOfInterest): GeoJSON.Feature<GeoJSON.Point> => {
  const positionObj = getPositionFromPointOfInterest(poi);
  return {
    type: 'Feature',
    geometry: {
      type: 'Point',
      coordinates: [positionObj.lng, positionObj.lat],
    },
    properties: {
      id: poi.id,
      name: poi.name,
      category: poi.category,
      // Add any other relevant properties you want in the GeoJSON
    },
  };
};

/**
 * Builds a normalized POI structure: { [country]: { [city]: PointOfInterest[] } }
 * @param pois Array of all POIs
 * @param currentCountryKey The current country key (e.g., "portugal", "morocco")
 * @returns Normalized POI structure
 */
export function buildPOIHierarchy(pois: PointOfInterest[], currentCountryKey: string): Record<string, Record<string, PointOfInterest[]>> {
  const hierarchy: Record<string, Record<string, PointOfInterest[]>> = {};
  console.log(`[buildPOIHierarchy] Starting. Number of POIs: ${pois.length}, Current Country Key: ${currentCountryKey}`);
  const country = currentCountryKey.toLowerCase(); // Use the provided country key

  pois.forEach((poi, index) => {
    const locationString = poi.location || '';
    // City is the primary part of the location string if only city is provided
    const cityRaw = locationString.split(',')[0].trim(); 
    const city = (cityRaw || 'unknown').toLowerCase();

    if (index < 5 || poi.name === 'Kasbah des Oudaias') { // Log first 5 and a specific POI for detailed check
      console.log(`[buildPOIHierarchy] Processing POI #${index}: ${poi.name}`);
      console.log(`  poi.location: "${locationString}"`);
      console.log(`  cityRaw: "${cityRaw}"`);
      console.log(`  Derived city: "${city}", Using country: "${country}"`);
    }

    if (!hierarchy[country]) {
      console.log(`[buildPOIHierarchy] New (or first) country entry for: "${country}"`);
      hierarchy[country] = {};
    }
    if (!hierarchy[country][city]) {
      console.log(`[buildPOIHierarchy] New city added to hierarchy for country "${country}": "${city}"`);
      hierarchy[country][city] = [];
    }
    hierarchy[country][city].push(poi);
  });
  console.log('[buildPOIHierarchy] Finished. Final hierarchy structure:', hierarchy);
  return hierarchy;
}