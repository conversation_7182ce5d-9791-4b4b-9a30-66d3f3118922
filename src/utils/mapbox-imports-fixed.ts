/**
 * Mapbox GL Import Utility (Fixed Version)
 * 
 * This module centralizes mapbox-gl imports and works around TypeScript errors
 * related to the default import of @mapbox/point-geometry within mapbox-gl.
 * 
 * This solves the "can only be default-imported using the 'esModuleInterop' flag" error
 * by providing a properly typed import wrapper.
 */

// Import using namespaced import to avoid esModuleInterop issues
import * as mapboxglImport from "mapbox-gl";

// Create an object to be exported as default
const mapboxgl = mapboxglImport;

// Export the mapboxgl object as default
export default mapboxgl;

// Export key types for use throughout the application
export type Map = mapboxglImport.Map;
export type LngLatLike = mapboxglImport.LngLatLike;
export type LngLatBounds = mapboxglImport.LngLatBounds;
export type Marker = mapboxglImport.Marker;
export type Popup = mapboxglImport.Popup;

/**
 * Creates and returns a mapbox-gl Map instance
 * @param options Map initialization options
 * @returns A new Mapbox map instance
 */
export function createMap(options: any): mapboxglImport.Map {
  return new mapboxglImport.Map(options);
}

/**
 * Safely disposes of a mapbox map instance
 * @param map The map instance to remove
 */
export function destroyMap(map: mapboxglImport.Map | null | undefined): void {
  if (map) {
    map.remove();
  }
}

/**
 * Creates a marker that can be added to the map
 * @param options Marker options
 * @returns A new Mapbox marker
 */
export function createMarker(options?: any): mapboxglImport.Marker {
  return new mapboxglImport.Marker(options);
}

/**
 * Creates a popup that can be added to the map or a marker
 * @param options Popup options
 * @returns A new Mapbox popup
 */
export function createPopup(options?: any): mapboxglImport.Popup {
  return new mapboxglImport.Popup(options);
}
