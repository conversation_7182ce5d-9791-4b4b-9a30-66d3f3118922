/**
 * Mapbox GL Import Utility 
 * 
 * This module centralizes mapbox-gl imports and works around TypeScript errors
 * related to the default import of @mapbox/point-geometry within mapbox-gl.
 * 
 * This solves the "can only be default-imported using the 'esModuleInterop' flag" error
 * by providing a properly typed import wrapper.
 */

// Import using namespaced import to avoid esModuleInterop issues
import * as mapboxglImport from 'mapbox-gl';

// Create a type-safe default export
const mapboxgl = mapboxglImport;

// Re-export as default for consistent usage across the codebase
export default mapboxgl;

// Re-export essential types for type usage
export type Map = mapboxglImport.Map;
export type LngLatLike = mapboxglImport.LngLatLike;
export type LngLatBounds = mapboxglImport.LngLatBounds;
export type Marker = mapboxglImport.Marker;
export type Popup = mapboxglImport.Popup;
export type NavigationControl = mapboxglImport.NavigationControl;
export type GeolocateControl = mapboxglImport.GeolocateControl;
export type GeoJSONSource = mapboxglImport.GeoJSONSource;

// Also export common types that might be needed using type aliases
export type MapOptions = mapboxglImport.MapboxOptions;
export type MapboxEvent<T = string> = {
  type: T;
  target: Map;
  originalEvent: Event;
  point: { x: number; y: number };
  lngLat: { lng: number; lat: number };
};
export type Layer = any; // AnyLayer type
export type Source = any; // AnySourceData type

/**
 * Creates and returns a mapbox-gl Map instance
 * @param options Map initialization options
 * @returns A new Mapbox map instance
 */
export function createMap(options: mapboxglImport.MapboxOptions): mapboxglImport.Map {
  return new mapboxglImport.Map(options);
}

/**
 * Safely disposes of a mapbox map instance
 * @param map The map instance to remove
 */
export function destroyMap(map: mapboxglImport.Map | null | undefined): void {
  if (map) {
    map.remove();
  }
}

/**
 * Creates a marker that can be added to the map
 * @param options Marker options
 * @returns A new Mapbox marker
 */
export function createMarker(options?: any): mapboxglImport.Marker {
  return new mapboxglImport.Marker(options);
}

/**
 * Creates a popup that can be added to the map or a marker
 * @param options Popup options
 * @returns A new Mapbox popup
 */
export function createPopup(options?: any): mapboxglImport.Popup {
  return new mapboxglImport.Popup(options);
}

/**
 * Usage Guidelines:
 * 
 * 1. Import the default export for general mapbox-gl usage:
 *    import mapboxgl from '@/utils/mapbox-imports';
 * 
 * 2. Import specific types when needed:
 *    import { Map, LngLatLike } from '@/utils/mapbox-imports';
 * 
 * 3. Use the helper functions for common operations:
 *    import { createMap, destroyMap } from '@/utils/mapbox-imports';
 * 
 * This approach ensures consistent imports and avoids TypeScript errors.
 */ 