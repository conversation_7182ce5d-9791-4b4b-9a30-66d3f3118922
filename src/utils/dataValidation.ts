/**
 * Data Validation Utilities
 * Ensures data consistency between mock data and database data
 */

import { Destination, POI } from '../types/Destination';
import { PointOfInterest } from '../types/POITypes';

/**
 * Coordinate validation and normalization
 */
export interface ValidatedCoordinates {
  coordinates: [number, number];
  isValid: boolean;
  source: 'coordinates' | 'position' | 'fallback';
}

export function validateAndNormalizeCoordinates(
  data: any,
  fallback: [number, number] = [0, 0]
): ValidatedCoordinates {
  // Try coordinates field first (database format)
  if (data.coordinates && Array.isArray(data.coordinates) && data.coordinates.length >= 2) {
    const [lng, lat] = data.coordinates;
    if (isValidLongitude(lng) && isValidLatitude(lat)) {
      return {
        coordinates: [lng, lat],
        isValid: true,
        source: 'coordinates'
      };
    }
  }

  // Try position field (mock data format)
  if (data.position) {
    if (Array.isArray(data.position) && data.position.length >= 2) {
      const [lng, lat] = data.position;
      if (isValidLongitude(lng) && isValidLatitude(lat)) {
        return {
          coordinates: [lng, lat],
          isValid: true,
          source: 'position'
        };
      }
    } else if (typeof data.position === 'object' && data.position.lng !== undefined && data.position.lat !== undefined) {
      const { lng, lat } = data.position;
      if (isValidLongitude(lng) && isValidLatitude(lat)) {
        return {
          coordinates: [lng, lat],
          isValid: true,
          source: 'position'
        };
      }
    }
  }

  // Try PostGIS POINT format (database string format)
  if (typeof data.coordinates === 'string' && data.coordinates.includes('POINT')) {
    const match = data.coordinates.match(/POINT\(([^)]+)\)/);
    if (match) {
      const [lng, lat] = match[1].split(' ').map(Number);
      if (isValidLongitude(lng) && isValidLatitude(lat)) {
        return {
          coordinates: [lng, lat],
          isValid: true,
          source: 'coordinates'
        };
      }
    }
  }

  console.warn('Invalid coordinates found, using fallback:', data);
  return {
    coordinates: fallback,
    isValid: false,
    source: 'fallback'
  };
}

/**
 * Coordinate validation helpers
 */
export function isValidLongitude(lng: number): boolean {
  return typeof lng === 'number' && !isNaN(lng) && isFinite(lng) && lng >= -180 && lng <= 180;
}

export function isValidLatitude(lat: number): boolean {
  return typeof lat === 'number' && !isNaN(lat) && isFinite(lat) && lat >= -90 && lat <= 90;
}

/**
 * Destination data validation and normalization
 */
export interface ValidatedDestination extends Destination {
  _validation: {
    coordinatesValid: boolean;
    coordinatesSource: string;
    missingFields: string[];
  };
}

export function validateDestination(data: any): ValidatedDestination {
  const coordValidation = validateAndNormalizeCoordinates(data);
  const missingFields: string[] = [];

  // Check required fields
  if (!data.id) missingFields.push('id');
  if (!data.name) missingFields.push('name');

  return {
    id: data.id || `dest-${Date.now()}`,
    name: data.name || 'Unknown Destination',
    description: data.description || '',
    coordinates: coordValidation.coordinates,
    region: data.region || '',
    country: data.country || '',
    tags: Array.isArray(data.tags) ? data.tags : [],
    images: Array.isArray(data.images) ? data.images : [],
    position: {
      lat: coordValidation.coordinates[1],
      lng: coordValidation.coordinates[0]
    },
    _validation: {
      coordinatesValid: coordValidation.isValid,
      coordinatesSource: coordValidation.source,
      missingFields
    }
  };
}

/**
 * POI data validation and normalization
 */
export interface ValidatedPOI extends POI {
  _validation: {
    coordinatesValid: boolean;
    coordinatesSource: string;
    missingFields: string[];
  };
}

export function validatePOI(data: any): ValidatedPOI {
  const coordValidation = validateAndNormalizeCoordinates(data);
  const missingFields: string[] = [];

  // Check required fields
  if (!data.id) missingFields.push('id');
  if (!data.name) missingFields.push('name');

  return {
    id: data.id || `poi-${Date.now()}`,
    name: data.name || 'Unknown POI',
    description: data.description || '',
    category: data.category || 'landmark',
    coordinates: coordValidation.coordinates,
    location: data.location || '',
    region: data.region || '',
    tags: Array.isArray(data.tags) ? data.tags : [],
    images: Array.isArray(data.images) ? data.images : [],
    _validation: {
      coordinatesValid: coordValidation.isValid,
      coordinatesSource: coordValidation.source,
      missingFields
    }
  };
}

/**
 * Convert database POI to PointOfInterest format
 */
export function convertToPointOfInterest(poi: ValidatedPOI): PointOfInterest {
  return {
    id: poi.id,
    name: poi.name,
    description: poi.description,
    category: poi.category as any, // Type assertion for compatibility
    coordinates: poi.coordinates,
    location: poi.location,
    region: poi.region,
    tags: poi.tags,
    position: {
      lat: poi.coordinates[1],
      lng: poi.coordinates[0]
    },
    price: 0, // Default values for legacy fields
    duration: 60,
    rating: 4.5,
    reviews: 0,
    images: poi.images,
    openingHours: '',
    website: '',
    phone: ''
  };
}

/**
 * Batch validation for arrays of data
 */
export function validateDestinations(destinations: any[]): ValidatedDestination[] {
  return destinations.map(dest => validateDestination(dest));
}

export function validatePOIs(pois: any[]): ValidatedPOI[] {
  return pois.map(poi => validatePOI(poi));
}

/**
 * Data quality report
 */
export interface DataQualityReport {
  destinations: {
    total: number;
    validCoordinates: number;
    missingFields: Record<string, number>;
  };
  pois: {
    total: number;
    validCoordinates: number;
    missingFields: Record<string, number>;
  };
  overall: {
    score: number; // 0-100
    issues: string[];
    recommendations: string[];
  };
}

export function generateDataQualityReport(
  destinations: ValidatedDestination[],
  pois: ValidatedPOI[]
): DataQualityReport {
  const destMissingFields: Record<string, number> = {};
  const poiMissingFields: Record<string, number> = {};
  const issues: string[] = [];
  const recommendations: string[] = [];

  // Analyze destinations
  destinations.forEach(dest => {
    dest._validation.missingFields.forEach(field => {
      destMissingFields[field] = (destMissingFields[field] || 0) + 1;
    });
  });

  // Analyze POIs
  pois.forEach(poi => {
    poi._validation.missingFields.forEach(field => {
      poiMissingFields[field] = (poiMissingFields[field] || 0) + 1;
    });
  });

  // Calculate scores
  const destValidCoords = destinations.filter(d => d._validation.coordinatesValid).length;
  const poiValidCoords = pois.filter(p => p._validation.coordinatesValid).length;
  
  const totalItems = destinations.length + pois.length;
  const validCoords = destValidCoords + poiValidCoords;
  const coordScore = totalItems > 0 ? (validCoords / totalItems) * 100 : 100;

  // Generate issues and recommendations
  if (coordScore < 90) {
    issues.push(`${Math.round(100 - coordScore)}% of items have invalid coordinates`);
    recommendations.push('Review coordinate data sources and validation');
  }

  if (Object.keys(destMissingFields).length > 0) {
    issues.push(`Destinations missing fields: ${Object.keys(destMissingFields).join(', ')}`);
    recommendations.push('Ensure all required destination fields are populated');
  }

  if (Object.keys(poiMissingFields).length > 0) {
    issues.push(`POIs missing fields: ${Object.keys(poiMissingFields).join(', ')}`);
    recommendations.push('Ensure all required POI fields are populated');
  }

  return {
    destinations: {
      total: destinations.length,
      validCoordinates: destValidCoords,
      missingFields: destMissingFields
    },
    pois: {
      total: pois.length,
      validCoordinates: poiValidCoords,
      missingFields: poiMissingFields
    },
    overall: {
      score: Math.round(coordScore),
      issues,
      recommendations
    }
  };
}

/**
 * Development helper: Log data quality report
 */
export function logDataQualityReport(report: DataQualityReport): void {
  console.group('📊 Data Quality Report');
  console.log(`Overall Score: ${report.overall.score}/100`);
  console.log(`Destinations: ${report.destinations.total} (${report.destinations.validCoordinates} valid coords)`);
  console.log(`POIs: ${report.pois.total} (${report.pois.validCoordinates} valid coords)`);
  
  if (report.overall.issues.length > 0) {
    console.warn('Issues:', report.overall.issues);
  }
  
  if (report.overall.recommendations.length > 0) {
    console.info('Recommendations:', report.overall.recommendations);
  }
  
  console.groupEnd();
}
