import type { PointOfInterest } from '@/types';

// Importance thresholds
export const HIGH_IMPORTANCE = 0.7;
export const MEDIUM_IMPORTANCE = 0.4;

/**
 * Calculate importance score for a POI based on various factors
 * A higher score means the POI is more important and should be prioritized
 * 
 * @param poi The Point of Interest to score
 * @param userPreferences Optional user preferences for scoring
 * @returns Importance score between 0 and 1
 */
export function calculatePOIImportance(
  poi: PointOfInterest, 
  userPreferences?: {
    preferredCategories?: string[];
    interests?: string[];
  }
): number {
  let score = 0;
  
  // Base score based on category - certain categories might be inherently more important
  // Landmarks are typically more interesting to most people
  switch (poi.category) {
    case 'landmark':
      score += 0.4;
      break;
    case 'activity':
      score += 0.3;
      break;
    case 'restaurant':
      score += 0.2;
      break;
    case 'accommodation':
      score += 0.1;
      break;
    default:
      score += 0.1;
  }
  
  // Cost factor - free or very inexpensive items get a slight boost
  if ((poi.cost ?? Infinity) === 0) {
    score += 0.1; // Free
  } else if ((poi.cost ?? Infinity) < 10) {
    score += 0.05; // Very inexpensive
  }
  
  // Duration factor - shorter activities can be easier to fit in
  if (poi.duration && poi.duration <= 1) {
    score += 0.1; // Quick activities (1 hour or less)
  }
  
  // Tag matching with user interests
  if (userPreferences?.interests && userPreferences.interests.length > 0) {
    const matchingTags = poi.tags.filter((tag: string) =>
      userPreferences.interests!.some(interest => 
        tag.toLowerCase().includes(interest.toLowerCase()) || 
        interest.toLowerCase().includes(tag.toLowerCase())
      )
    );
    
    // Add score based on percentage of matching tags
    if (matchingTags.length > 0) {
      score += 0.2 * (matchingTags.length / Math.max(poi.tags.length, 1));
    }
  }
  
  // Preferred category bonus
  if (userPreferences?.preferredCategories?.includes(poi.category)) {
    score += 0.2;
  }
  
  // Ensure the score is capped between 0 and 1
  return Math.min(Math.max(score, 0), 1);
}

/**
 * Get the importance level category for a POI
 * 
 * @param poi The Point of Interest
 * @param userPreferences Optional user preferences for scoring
 * @returns 'high', 'medium', or 'low' importance
 */
export function getPOIImportanceLevel(
  poi: PointOfInterest,
  userPreferences?: {
    preferredCategories?: string[];
    interests?: string[];
  }
): 'high' | 'medium' | 'low' {
  const score = calculatePOIImportance(poi, userPreferences);
  
  if (score >= HIGH_IMPORTANCE) {
    return 'high';
  } else if (score >= MEDIUM_IMPORTANCE) {
    return 'medium';
  } else {
    return 'low';
  }
} 