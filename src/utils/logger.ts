/**
 * CENTRALIZED LOGGING SYSTEM
 * Production-ready logging with environment-based controls
 */

// Environment-based logging configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isDebugMode = process.env.REACT_APP_DEBUG === 'true';

// Log levels
export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
  VERBOSE = 4
}

// Current log level based on environment
const currentLogLevel = isDevelopment 
  ? (isDebugMode ? LogLevel.VERBOSE : LogLevel.INFO)
  : LogLevel.ERROR;

// Component-specific logging controls
const componentLogging = {
  // Animation System
  AnimationManager: isDevelopment && isDebugMode,
  VehicleManager: isDevelopment && isDebugMode,
  RouteAnimator: isDevelopment && isDebugMode,
  ContextualSpeedController: isDevelopment && isDebugMode,
  ComponentInteractionManager: isDevelopment && isDebugMode,
  
  // Map Components
  ExploreMap: isDevelopment && false, // Too verbose, disable by default
  DirectPOIMarkers: isDevelopment && false, // Too verbose
  POITypes: isDevelopment && false, // Normalization logs
  
  // UI Components
  LeftPane: isDevelopment && false,
  RightPane: isDevelopment && false,
  JourneyButton: isDevelopment && false,
  
  // Core Systems
  MapUtils: isDevelopment && isDebugMode,
  TravelAnimator: isDevelopment && isDebugMode,
  
  // Business Logic
  Destinations: isDevelopment && true, // Keep for business logic
  POIDiscovery: isDevelopment && true, // Keep for business logic
};

/**
 * Centralized logger with component-specific controls
 */
export class Logger {
  private component: string;
  
  constructor(component: string) {
    this.component = component;
  }
  
  private shouldLog(level: LogLevel): boolean {
    if (level > currentLogLevel) return false;
    
    // Check component-specific logging
    const componentEnabled = componentLogging[this.component as keyof typeof componentLogging];
    if (componentEnabled === false) return false;
    
    return true;
  }
  
  private formatMessage(level: string, message: string, ...args: any[]): [string, ...any[]] {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const prefix = `[${timestamp}] ${level} [${this.component}]`;
    return [`${prefix} ${message}`, ...args];
  }
  
  error(message: string, ...args: any[]): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      console.error(...this.formatMessage('ERROR', message, ...args));
    }
  }
  
  warn(message: string, ...args: any[]): void {
    if (this.shouldLog(LogLevel.WARN)) {
      console.warn(...this.formatMessage('WARN', message, ...args));
    }
  }
  
  info(message: string, ...args: any[]): void {
    if (this.shouldLog(LogLevel.INFO)) {
      console.info(...this.formatMessage('INFO', message, ...args));
    }
  }
  
  debug(message: string, ...args: any[]): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      console.log(...this.formatMessage('DEBUG', message, ...args));
    }
  }
  
  verbose(message: string, ...args: any[]): void {
    if (this.shouldLog(LogLevel.VERBOSE)) {
      console.log(...this.formatMessage('VERBOSE', message, ...args));
    }
  }
  
  // Convenience methods for common patterns
  lifecycle(action: string, data?: any): void {
    this.debug(`${action}`, data);
  }
  
  performance(action: string, startTime: number): void {
    const duration = performance.now() - startTime;
    this.debug(`${action} completed in ${duration.toFixed(2)}ms`);
  }
  
  state(stateName: string, value: any): void {
    this.verbose(`State change: ${stateName}`, value);
  }
}

/**
 * Create logger instance for a component
 */
export const createLogger = (component: string): Logger => {
  return new Logger(component);
};

/**
 * Production-safe console replacement
 * Use this to replace direct console.log calls
 */
export const productionConsole = {
  log: (...args: any[]) => {
    if (isDevelopment) console.log(...args);
  },
  warn: (...args: any[]) => {
    console.warn(...args); // Always show warnings
  },
  error: (...args: any[]) => {
    console.error(...args); // Always show errors
  },
  info: (...args: any[]) => {
    if (isDevelopment) console.info(...args);
  },
  debug: (...args: any[]) => {
    if (isDevelopment && isDebugMode) console.debug(...args);
  }
};

/**
 * Performance monitoring utilities
 */
export class PerformanceMonitor {
  private static timers = new Map<string, number>();
  
  static start(label: string): void {
    if (isDevelopment) {
      this.timers.set(label, performance.now());
    }
  }
  
  static end(label: string): number {
    if (!isDevelopment) return 0;
    
    const startTime = this.timers.get(label);
    if (!startTime) return 0;
    
    const duration = performance.now() - startTime;
    this.timers.delete(label);
    
    if (duration > 16) { // Longer than one frame
      console.warn(`Performance: ${label} took ${duration.toFixed(2)}ms`);
    }
    
    return duration;
  }
}

/**
 * Environment configuration
 */
export const LogConfig = {
  isDevelopment,
  isDebugMode,
  currentLogLevel,
  componentLogging,
  
  // Quick toggles for common debugging scenarios
  enableAnimationLogs: () => {
    componentLogging.AnimationManager = true;
    componentLogging.VehicleManager = true;
    componentLogging.RouteAnimator = true;
  },
  
  enableUILogs: () => {
    componentLogging.ExploreMap = true;
    componentLogging.LeftPane = true;
    componentLogging.RightPane = true;
  },
  
  disableVerboseLogs: () => {
    componentLogging.ExploreMap = false;
    componentLogging.DirectPOIMarkers = false;
    componentLogging.POITypes = false;
    componentLogging.LeftPane = false;
    componentLogging.RightPane = false;
  }
};

// Export default logger for quick use
export default createLogger;
