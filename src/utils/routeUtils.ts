import type { Destination } from '@/types/POITypes';
import { Vehicle } from "@/data/vehicles";

// Calculate distance between two points using Haversine formula
export function calculateDistance(
  point1: [number, number],
  point2: [number, number]
): number {
  const R = 6371; // Earth's radius in km
  const dLat = deg2rad(point2[1] - point1[1]);
  const dLon = deg2rad(point2[0] - point1[0]);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(point1[1])) *
      Math.cos(deg2rad(point2[1])) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c; // Distance in km
  return Math.round(distance);
}

function deg2rad(deg: number): number {
  return deg * (Math.PI / 180);
}

// Calculate route details between destinations
export function calculateRoute(
  destinations: Destination[],
  vehicle: Vehicle
): {
  totalDistance: number;
  totalDuration: number;
  recommendedDays: number;
} {
  let totalDistance = 0;
  let totalDuration = 0;

  // Calculate distance between consecutive destinations
  for (let i = 0; i < destinations.length - 1; i++) {
    totalDistance += calculateDistance(
      destinations[i].coordinates,
      destinations[i + 1].coordinates
    );
  }

  // Calculate duration from destination suggestions
  destinations.forEach((destination) => {
    totalDuration += destination.suggestedDuration ?? 0;
  });

  // Calculate recommended days based on distance and activities
  const drivingTime = estimateDrivingTime(totalDistance);
  const recommendedDays = Math.ceil(totalDuration / 24 + drivingTime / 8);

  return {
    totalDistance,
    totalDuration,
    recommendedDays,
  };
}

// Estimate driving time based on distance
export function estimateDrivingTime(distanceKm: number): number {
  // Assuming average speed of 60 km/h on Moroccan roads
  return Math.round(distanceKm / 60 * 10) / 10;
}

// Format duration in days and hours
export function formatDuration(hours: number): string {
  const days = Math.floor(hours / 24);
  const remainingHours = Math.round(hours % 24);
  
  if (days === 0) {
    return `${remainingHours} hour${remainingHours !== 1 ? 's' : ''}`;
  } else if (remainingHours === 0) {
    return `${days} day${days !== 1 ? 's' : ''}`;
  } else {
    return `${days} day${days !== 1 ? 's' : ''} ${remainingHours} hour${remainingHours !== 1 ? 's' : ''}`;
  }
}

// Format currency values with dollar sign
export function formatCurrency(amount: number): string {
  return `$${amount.toFixed(2)}`;
}

// Recommend days to spend at each destination
export function recommendDaysPerDestination(
  destinations: Destination[]
): { destinationId: string; recommendedDays: number }[] {
  return destinations.map(destination => {
    // Convert hours to days, with minimum of 1 day
    const recommendedDays = Math.max(1, Math.ceil((destination.suggestedDuration ?? 0) / 8));
    return {
      destinationId: destination.id,
      recommendedDays
    };
  });
}
