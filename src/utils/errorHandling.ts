/**
 * errorHandling.ts
 * 
 * UNIFIED ERROR HANDLING UTILITIES
 * Provides consistent error handling patterns across the application
 */

// ========================================
// ERROR TYPES AND INTERFACES
// ========================================

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum ErrorCategory {
  NETWORK = 'network',
  VALIDATION = 'validation',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  NOT_FOUND = 'not_found',
  SERVER_ERROR = 'server_error',
  CLIENT_ERROR = 'client_error',
  ANIMATION = 'animation',
  MAP = 'map',
  DATA = 'data',
  UNKNOWN = 'unknown'
}

export interface AppError {
  id: string;
  message: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  timestamp: Date;
  context?: Record<string, any>;
  originalError?: Error;
  stack?: string;
  userMessage?: string;
  actionable?: boolean;
  retryable?: boolean;
}

export interface ErrorHandlerOptions {
  category?: ErrorCategory;
  severity?: ErrorSeverity;
  context?: Record<string, any>;
  userMessage?: string;
  actionable?: boolean;
  retryable?: boolean;
  silent?: boolean;
}

// ========================================
// ERROR CREATION UTILITIES
// ========================================

/**
 * Create a standardized application error
 */
export function createAppError(
  message: string,
  options: ErrorHandlerOptions = {}
): AppError {
  const errorId = `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  return {
    id: errorId,
    message,
    category: options.category || ErrorCategory.UNKNOWN,
    severity: options.severity || ErrorSeverity.MEDIUM,
    timestamp: new Date(),
    context: options.context,
    userMessage: options.userMessage || message,
    actionable: options.actionable ?? true,
    retryable: options.retryable ?? false
  };
}

/**
 * Convert a native Error to AppError
 */
export function toAppError(
  error: Error | unknown,
  options: ErrorHandlerOptions = {}
): AppError {
  const baseError = error instanceof Error ? error : new Error(String(error));
  
  return {
    ...createAppError(baseError.message, options),
    originalError: baseError,
    stack: baseError.stack
  };
}

// ========================================
// ERROR CLASSIFICATION
// ========================================

/**
 * Classify error based on common patterns
 */
export function classifyError(error: Error | unknown): { category: ErrorCategory; severity: ErrorSeverity } {
  const message = error instanceof Error ? error.message.toLowerCase() : String(error).toLowerCase();
  
  // Network errors
  if (message.includes('network') || message.includes('fetch') || message.includes('connection')) {
    return { category: ErrorCategory.NETWORK, severity: ErrorSeverity.MEDIUM };
  }
  
  // Authentication errors
  if (message.includes('unauthorized') || message.includes('authentication') || message.includes('login')) {
    return { category: ErrorCategory.AUTHENTICATION, severity: ErrorSeverity.HIGH };
  }
  
  // Authorization errors
  if (message.includes('forbidden') || message.includes('permission') || message.includes('access denied')) {
    return { category: ErrorCategory.AUTHORIZATION, severity: ErrorSeverity.HIGH };
  }
  
  // Not found errors
  if (message.includes('not found') || message.includes('404')) {
    return { category: ErrorCategory.NOT_FOUND, severity: ErrorSeverity.LOW };
  }
  
  // Server errors
  if (message.includes('server error') || message.includes('500') || message.includes('internal')) {
    return { category: ErrorCategory.SERVER_ERROR, severity: ErrorSeverity.HIGH };
  }
  
  // Animation errors
  if (message.includes('animation') || message.includes('mapbox') || message.includes('vehicle')) {
    return { category: ErrorCategory.ANIMATION, severity: ErrorSeverity.MEDIUM };
  }
  
  // Map errors
  if (message.includes('map') || message.includes('coordinates') || message.includes('bounds')) {
    return { category: ErrorCategory.MAP, severity: ErrorSeverity.MEDIUM };
  }
  
  // Validation errors
  if (message.includes('validation') || message.includes('invalid') || message.includes('required')) {
    return { category: ErrorCategory.VALIDATION, severity: ErrorSeverity.LOW };
  }
  
  // Default classification
  return { category: ErrorCategory.UNKNOWN, severity: ErrorSeverity.MEDIUM };
}

// ========================================
// ERROR HANDLING FUNCTIONS
// ========================================

/**
 * Handle errors with consistent logging and user feedback
 */
export function handleError(
  error: Error | unknown,
  options: ErrorHandlerOptions = {}
): AppError {
  // Create or convert to AppError
  const appError = error instanceof Error 
    ? toAppError(error, options)
    : createAppError(String(error), options);
  
  // Auto-classify if not provided
  if (!options.category || !options.severity) {
    const classification = classifyError(error);
    appError.category = options.category || classification.category;
    appError.severity = options.severity || classification.severity;
  }
  
  // Log error
  if (!options.silent) {
    logError(appError);
  }
  
  // Report to monitoring service
  reportError(appError);
  
  return appError;
}

/**
 * Handle async operations with error handling
 */
export async function handleAsync<T>(
  operation: () => Promise<T>,
  options: ErrorHandlerOptions = {}
): Promise<{ data?: T; error?: AppError }> {
  try {
    const data = await operation();
    return { data };
  } catch (error) {
    const appError = handleError(error, options);
    return { error: appError };
  }
}

/**
 * Handle sync operations with error handling
 */
export function handleSync<T>(
  operation: () => T,
  options: ErrorHandlerOptions = {}
): { data?: T; error?: AppError } {
  try {
    const data = operation();
    return { data };
  } catch (error) {
    const appError = handleError(error, options);
    return { error: appError };
  }
}

// ========================================
// ERROR LOGGING
// ========================================

/**
 * Log error with appropriate level
 */
export function logError(error: AppError): void {
  const logLevel = getLogLevel(error.severity);
  const logMessage = `[${error.category.toUpperCase()}] ${error.message}`;
  
  const logData = {
    id: error.id,
    category: error.category,
    severity: error.severity,
    timestamp: error.timestamp,
    context: error.context,
    stack: error.stack
  };
  
  switch (logLevel) {
    case 'error':
      console.error(logMessage, logData);
      break;
    case 'warn':
      console.warn(logMessage, logData);
      break;
    case 'info':
      console.info(logMessage, logData);
      break;
    default:
      console.log(logMessage, logData);
  }
}

function getLogLevel(severity: ErrorSeverity): 'error' | 'warn' | 'info' | 'log' {
  switch (severity) {
    case ErrorSeverity.CRITICAL:
    case ErrorSeverity.HIGH:
      return 'error';
    case ErrorSeverity.MEDIUM:
      return 'warn';
    case ErrorSeverity.LOW:
      return 'info';
    default:
      return 'log';
  }
}

// ========================================
// ERROR REPORTING
// ========================================

/**
 * Report error to monitoring service
 */
export function reportError(error: AppError): void {
  // In development, just log
  if (process.env.NODE_ENV === 'development') {
    console.group('🚨 Error Report');
    console.table({
      ID: error.id,
      Category: error.category,
      Severity: error.severity,
      Message: error.message,
      Timestamp: error.timestamp.toISOString()
    });
    if (error.context) {
      console.log('Context:', error.context);
    }
    if (error.stack) {
      console.log('Stack:', error.stack);
    }
    console.groupEnd();
    return;
  }
  
  // TODO: Implement actual error reporting service
  // Example integrations:
  // - Sentry: Sentry.captureException(error)
  // - LogRocket: LogRocket.captureException(error)
  // - Custom API: fetch('/api/errors', { method: 'POST', body: JSON.stringify(error) })
  
  console.warn('[ErrorReporting] Error reporting not implemented:', error.id);
}

// ========================================
// USER-FRIENDLY ERROR MESSAGES
// ========================================

/**
 * Get user-friendly error message
 */
export function getUserMessage(error: AppError): string {
  if (error.userMessage) {
    return error.userMessage;
  }
  
  // Default messages by category
  const defaultMessages: Record<ErrorCategory, string> = {
    [ErrorCategory.NETWORK]: 'Unable to connect to the server. Please check your internet connection and try again.',
    [ErrorCategory.AUTHENTICATION]: 'Please log in to continue.',
    [ErrorCategory.AUTHORIZATION]: 'You don\'t have permission to perform this action.',
    [ErrorCategory.NOT_FOUND]: 'The requested resource was not found.',
    [ErrorCategory.SERVER_ERROR]: 'A server error occurred. Please try again later.',
    [ErrorCategory.CLIENT_ERROR]: 'An error occurred. Please try again.',
    [ErrorCategory.VALIDATION]: 'Please check your input and try again.',
    [ErrorCategory.ANIMATION]: 'Animation error occurred. The map experience may be affected.',
    [ErrorCategory.MAP]: 'Map error occurred. Please refresh the page.',
    [ErrorCategory.DATA]: 'Data error occurred. Please try again.',
    [ErrorCategory.UNKNOWN]: 'An unexpected error occurred. Please try again.'
  };
  
  return defaultMessages[error.category] || defaultMessages[ErrorCategory.UNKNOWN];
}

// ========================================
// ERROR RECOVERY UTILITIES
// ========================================

/**
 * Retry operation with exponential backoff
 */
export async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      if (attempt === maxRetries) {
        throw lastError;
      }
      
      // Exponential backoff with jitter
      const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError!;
}

/**
 * Check if error is retryable
 */
export function isRetryable(error: AppError): boolean {
  if (error.retryable !== undefined) {
    return error.retryable;
  }
  
  // Auto-determine based on category
  const retryableCategories = [
    ErrorCategory.NETWORK,
    ErrorCategory.SERVER_ERROR,
    ErrorCategory.ANIMATION,
    ErrorCategory.MAP
  ];
  
  return retryableCategories.includes(error.category);
}
