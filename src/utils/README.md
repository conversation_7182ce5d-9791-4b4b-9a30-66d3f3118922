# Utilities Directory

This directory contains utility functions and classes that provide common functionality across the application. These utilities are designed to be reusable, focused, and maintain a separation of concerns.

## Core Utilities

### Animation Utilities

- **`AnimationEventEmitter.ts`**: A specialized event emitter for handling animation system events with type safety.
- **`AnimationDebugTools.ts`**: Standardized debug logging and monitoring tools for the animation system.
- **`animationLogger.ts`**: Logger specifically designed for animation-related debugging.

### Mapbox Utilities

- **`mapbox-adapter.ts`**: Adapters for Mapbox objects to work with our application types.
- **`mapbox-imports.ts`**: Consolidated Mapbox imports to prevent duplicate imports.

## Usage Guidelines

### Event Handling

Use the `AnimationEventEmitter` for type-safe event handling:

```typescript
import { AnimationEventEmitter } from '../utils/AnimationEventEmitter';
import { AnimationEventType } from '../types/AnimationEventTypes';

// Create an event emitter instance
const eventEmitter = new AnimationEventEmitter();

// Register an event listener
eventEmitter.on(AnimationEventType.START, (event) => {
  console.log('Animation started:', event);
});

// Emit an event
eventEmitter.emit(AnimationEventType.START, {
  eventType: AnimationEventType.START,
  timestamp: Date.now(),
  type: 'start'
});
```

### Debugging

Use `AnimationLogger` for consistent and contextual animation logging:

```typescript
import AnimationLogger from './animationLogger'; // Updated import path

// Log messages with different levels and contexts
// General log with context:
AnimationLogger.log('debug', 'animation', 'Initializing animation system');
AnimationLogger.log('info', 'state', 'Animation progress:', { progress: 0.5 });

// Context-specific logs (recommended):
AnimationLogger.vehicle('warn', 'Vehicle marker update delayed');
AnimationLogger.camera('error', 'Camera failed to track target', { error: 'Target not found' });
AnimationLogger.poi('info', 'POI discovered', { poiName: 'Eiffel Tower' });

// Track animation flow steps
AnimationLogger.trackFlow('Main animation loop started');

// Get log history for debugging
const recentLogs = AnimationLogger.getLogs(); // (getLogs is re-exported by AnimationLogger)
```

## Best Practices

1. **Keep utilities focused**: Each utility file should have a single responsibility
2. **Use type safety**: Leverage TypeScript to provide type safety in utility functions
3. **Provide documentation**: Add JSDoc comments to explain utility functions
4. **Avoid side effects**: Utilities should be pure functions when possible
5. **Unit test utilities**: Since utilities are isolated, they're perfect candidates for unit tests

## Adding New Utilities

When adding new utility files:

1. Follow the naming convention: `camelCaseDescription.ts`
2. Add proper TypeScript types and JSDoc comments
3. Keep utilities small and focused
4. Update this README with a brief description of the utility
5. Consider creating category-specific subdirectories for related utilities

## Traceability

Each utility should maintain clear traceability to user story features through:

- JSDoc comments linking to related user stories
- Feature tags in the utility's header comment
- Documentation of feature dependencies
- Clear mapping of utility functions to specific user requirements

## MapBox TypeScript Integration

### Background

The Mapbox GL library can cause TypeScript issues due to how it imports the `@mapbox/point-geometry` dependency. The issue occurs because:

1. The `mapbox-gl.d.ts` file uses `import Point from '@mapbox/point-geometry'`
2. The `@mapbox/point-geometry` module is declared with `export =`
3. This requires the `esModuleInterop` flag, which may not work correctly with certain build configurations

### Solution: Mapbox Adapter

We created a lightweight adapter (`mapbox-adapter.ts`) that:

1. Uses `require()` to bypass TypeScript's module checking
2. Provides type-safe interfaces for common Mapbox operations
3. Creates a consistent API with proper TypeScript typings
4. Avoids modifying node_modules files (which is problematic for version control and CI)

### Usage Example

```typescript
// Import the adapter
import mapboxAdapter, { MapInstance } from '../utils/mapbox-adapter';

// Get the raw mapboxgl object if needed
const mapboxgl = mapboxAdapter.getMapboxGL();

// Create a map
const map = mapboxAdapter.createMap('map-container', {
  style: 'mapbox://styles/mapbox/streets-v11',
  center: [-74.5, 40],
  zoom: 9
});

// Create a marker
const marker = mapboxAdapter.createMarker()
  .setLngLat([-74.5, 40])
  .addTo(map);

// Function that accepts a MapInstance type
function updateMap(map: MapInstance) {
  // Use the map instance
}
```

### Benefits

- **Clean Imports**: No TypeScript errors from the mapbox-gl module
- **Type Safety**: Proper typings for map functions and properties
- **Consistency**: Standard API for use across the application
- **Maintainability**: No node_modules modifications required 
- **Compatibility**: Works with various TypeScript module settings

### Alternative Approaches (Not Used)

1. **Declaration Override Files**: We initially tried creating declaration files to override the problematic import, but this approach proved unreliable.
   
2. **Direct Node Module Patching**: Modifying the source in node_modules directly would work but is not a maintainable approach.

3. **TSConfig esModuleInterop**: Setting this flag to true in the tsconfig.json file was insufficient because other dependencies are affected.

### Looking Forward

When upgrading Mapbox GL in the future, check if the import issue has been resolved. If it has, we can gradually phase out the adapter.