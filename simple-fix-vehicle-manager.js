#!/usr/bin/env node

/**
 * Simple Fix for VehicleManager.ts
 * 
 * This script only addresses the most critical issues in the VehicleManager.ts file:
 * 1. Ensures _recoveryTimeout has correct type
 * 2. Removes duplicate getVehicleDebugInfo implementation
 * 
 * Usage:
 * node simple-fix-vehicle-manager.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const filePath = path.join(process.cwd(), 'src/components/map/animation/VehicleManager.ts');
const backupPath = `${filePath}.backup`;

// Create backup if it doesn't exist
if (!fs.existsSync(backupPath)) {
  console.log('Creating backup file...');
  fs.copyFileSync(filePath, backupPath);
}

console.log('Reading VehicleManager.ts...');
let content = fs.readFileSync(filePath, 'utf-8');

// Fix 1: Ensure _recoveryTimeout has NodeJS.Timeout | null type
if (content.includes('private _recoveryTimeout:') && !content.includes('private _recoveryTimeout: NodeJS.Timeout | null')) {
  console.log('Fixing _recoveryTimeout property type...');
  content = content.replace(
    /private _recoveryTimeout:.*?=/,
    'private _recoveryTimeout: NodeJS.Timeout | null ='
  );
}

// Fix 2: Find and remove duplicate getVehicleDebugInfo method
// Get both implementations
const privateMethodRegex = /private\s+getVehicleDebugInfo\s*\(\s*\)\s*:.*?{[\s\S]*?}/g;
const publicMethodRegex = /public\s+getVehicleDebugInfo\s*\(\s*\)\s*:.*?{[\s\S]*?}/g;

const privateMatches = content.match(privateMethodRegex);
const publicMatches = content.match(publicMethodRegex);

if (privateMatches && privateMatches.length > 0 && publicMatches && publicMatches.length > 0) {
  console.log('Found duplicate implementations of getVehicleDebugInfo. Removing private implementation...');
  // Replace the private implementation with a comment
  content = content.replace(
    privateMatches[0],
    '// NOTE: Removing the private implementation of getVehicleDebugInfo since it\'s duplicated'
  );
}

// Fix 3: Fix isHTMLElement type guard function if missing
if (!content.includes('private isHTMLElement') && 
    (content.includes('instanceof HTMLElement') || content.includes('element instanceof HTMLElement'))) {
  console.log('Adding isHTMLElement type guard function...');
  // Look for a good place to insert the function, preferably after a method definition
  const insertLocation = content.indexOf('private cleanupAllEmergencyMarkers(): void {');
  if (insertLocation !== -1) {
    const isHTMLElementFunction = `
  /**
   * Type guard for HTMLElement
   */
  private isHTMLElement(value: any): value is HTMLElement {
    return value && typeof value === 'object' && value.nodeType === 1 && typeof value.style === 'object';
  }
`;
    content = content.slice(0, insertLocation) + isHTMLElementFunction + content.slice(insertLocation);
  }
}

// Fix 4: Replace instanceof HTMLElement instances with isHTMLElement
if (content.includes('instanceof HTMLElement')) {
  console.log('Fixing instanceof HTMLElement checks...');
  content = content.replace(
    /(\w+)\s+instanceof\s+HTMLElement/g,
    'this.isHTMLElement($1)'
  );
}

console.log('Writing fixed content to file...');
fs.writeFileSync(filePath, content, 'utf-8');
console.log('VehicleManager.ts has been fixed!'); 