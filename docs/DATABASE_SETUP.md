# Database Setup Guide

This guide will help you set up Supabase as the database backend for the Come to Morocco travel platform.

## Prerequisites

- A Supabase account (free tier available)
- Node.js and npm installed
- Basic understanding of SQL

## Step 1: Create Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign up/login
2. Click "New Project"
3. Choose your organization
4. Enter project details:
   - **Name**: `come-to-morocco`
   - **Database Password**: Choose a strong password
   - **Region**: Choose closest to your users
5. Click "Create new project"
6. Wait for the project to be ready (2-3 minutes)

## Step 2: Get Project Credentials

1. In your Supabase dashboard, go to **Settings** > **API**
2. Copy the following values:
   - **Project URL** (looks like: `https://xxxxx.supabase.co`)
   - **Anon public key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)

## Step 3: Configure Environment Variables

1. Open your `.env` file in the project root
2. Add your Supabase credentials:

```env
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here
```

## Step 4: Run Database Migrations

1. In your Supabase dashboard, go to **SQL Editor**
2. Create a new query
3. Copy and paste the contents of `supabase/migrations/001_initial_schema.sql`
4. Click "Run" to create the database schema
5. Create another new query
6. Copy and paste the contents of `supabase/migrations/002_seed_data.sql`
7. Click "Run" to populate with sample data

## Step 5: Install Dependencies

Run the following command in your project directory:

```bash
npm install
```

This will install the Supabase client library that was added to package.json.

## Step 6: Test the Connection

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Open the browser console and look for any database connection errors

3. The app should now load data from Supabase instead of mock data

## Database Schema Overview

### Core Tables

- **clients**: Multi-tenant client configurations
- **destinations**: Cities and regions
- **pois**: Points of interest (attractions, restaurants, etc.)
- **journeys**: Pre-designed trip templates
- **itineraries**: Day-by-day journey details
- **users**: User profiles and preferences
- **bookings**: Trip bookings and reservations
- **reviews**: User reviews and ratings

### Key Features

- **PostGIS Integration**: Geographic data with spatial queries
- **Row Level Security**: Multi-tenant data isolation
- **Automatic Timestamps**: Created/updated tracking
- **JSON Fields**: Flexible configuration storage
- **Full-text Search**: Efficient content searching

## Data Migration from Mock Data

The database service layer (`src/services/database/DatabaseService.ts`) provides:

- **Automatic Data Transformation**: Converts database format to app format
- **Caching**: 5-minute cache for improved performance
- **Backward Compatibility**: Works with existing component interfaces
- **Error Handling**: Graceful fallbacks and error reporting

## Next Steps

1. **Verify Data**: Check that destinations and POIs appear correctly
2. **Test Features**: Ensure journey planning works with real data
3. **Configure RLS**: Set up proper row-level security policies
4. **Add Authentication**: Implement user authentication with Supabase Auth
5. **Optimize Queries**: Add indexes and optimize for your use case

## Troubleshooting

### Common Issues

1. **Connection Errors**
   - Verify your Supabase URL and key are correct
   - Check that your project is active in Supabase dashboard

2. **Migration Errors**
   - Ensure you have the PostGIS extension enabled
   - Check for syntax errors in SQL files

3. **Data Not Loading**
   - Verify RLS policies allow public read access
   - Check browser console for detailed error messages

4. **Performance Issues**
   - Monitor query performance in Supabase dashboard
   - Consider adding database indexes for frequently queried fields

### Getting Help

- Check the [Supabase Documentation](https://supabase.com/docs)
- Review the database service logs in browser console
- Verify your data in the Supabase Table Editor

## Production Considerations

Before going live:

1. **Security**: Configure proper RLS policies
2. **Backup**: Set up automated backups
3. **Monitoring**: Enable database monitoring
4. **Scaling**: Consider connection pooling for high traffic
5. **CDN**: Use Supabase CDN for image storage
