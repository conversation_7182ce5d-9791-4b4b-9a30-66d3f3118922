# Code Style Guidelines

This document outlines the code style guidelines and best practices for the Morocco Travel application, with particular focus on the animation system.

## General Guidelines

- Follow TypeScript best practices for type safety
- Use interfaces to define component contracts and ensure proper implementation
- Follow the component hierarchy and responsibility boundaries
- Document public methods and interfaces with JSDoc comments
- Use consistent naming conventions across the codebase

## Animation System Guidelines

### Timestamp Standardization

- **Always use `Date.now()` for timestamps instead of `new Date().toISOString()`**
  - `Date.now()` is more performant as it avoids object creation
  - It provides a consistent numeric timestamp in milliseconds
  - Numeric timestamps are easier to compare and perform calculations on

```typescript
// ✅ DO: Use Date.now() for timestamps
const timestamp = Date.now();
this.log('info', `Animation started at ${timestamp}`);

// ❌ DON'T: Use new Date().toISOString()
const timestamp = new Date().toISOString(); // Avoid - creates a Date object and string
```

- Only convert timestamps to ISO strings or formatted dates when displaying to users
- Store and pass timestamps as numbers (`number` type) in all internal operations
- For logging, prefer using numeric timestamps for consistency

### External Library Integration

- **Use the adapter pattern for problematic third-party libraries like mapbox-gl**
  - Import from our adapter files instead of directly from the library
  - Use consistent types from the adapter throughout the application

```typescript
// ✅ DO: Import from our adapter
import mapboxAdapter, { MapInstance } from '@/utils/mapbox-adapter';
const mapboxgl = mapboxAdapter.getMapboxGL();

// ❌ DON'T: Import directly from mapbox-gl
import mapboxgl from 'mapbox-gl'; // Avoid direct imports
```

- Use the `MapInstance` type for function parameters instead of `mapboxgl.Map`
- Prefer adapter utility functions over direct library methods
- Document any library-specific workarounds in code comments

### Component Architecture

- Follow the Manager/Controller pattern for animation systems
- Maintain separation between animation logic and visual rendering
- Implement animation managers as singletons with proper cleanup methods
- Use composition over inheritance for animation functionality

### Animation Loop Best Practices

- Use `requestAnimationFrame` for all animation loops
- Store animation frame IDs in refs and always cancel them on cleanup
- Calculate positions using delta time for consistent speed across devices
- Implement proper pause/resume mechanisms that maintain the animation state

### Error Handling

- Log animation errors with contextual information
- Include visibility checks for critical elements
- Use recovery mechanisms for animation failures

## TypeScript Guidelines

- Define explicit interfaces for all major components
- Use type guards to ensure type safety
- Prefer union types over any
- Define proper return types for all functions
- Use generics appropriately for reusable components
- Use adapter patterns to work around third-party library type issues

## Performance Considerations

- Batch DOM operations when possible
- Use transforms for animations instead of changing position properties
- Minimize React renders during animations
- Use debouncing or throttling for expensive operations
- Cache computation results when appropriate

## Event System

- Use typed events for cross-component communication
- Maintain clear interfaces for event listeners
- Clean up event listeners when components unmount
- Use a consistent event naming convention

## Documentation

- Document component responsibilities clearly
- Include examples of proper component usage
- Maintain up-to-date interface definitions
- Document debugging approaches for complex systems
- Document solutions for external library integration challenges 