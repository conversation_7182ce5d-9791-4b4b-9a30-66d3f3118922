# Portugal Douro Valley Theme Overlay Implementation

**@docs/Portugal-Theme-Implementation.md**

## Purpose
This document describes the implementation and integration of a Portugal-themed (Douro Valley Wine Tour) overlay for the POI/City Discovery Event System. The overlay provides a regionally branded, user-friendly UI for wine tour clients, leveraging the event-driven architecture established in the core project.

## Context
- The overlay is designed for Douro Valley wine tours, using Portuguese wine language, colors, and actions.
- It is built as a ready-to-paste React component, easily swappable for other regions or clients.
- The implementation follows the same event-driven pattern as the Morocco base project, ensuring consistency and extensibility.

## Integration Steps
1. **Import the Overlay**
   ```tsx
   import { DouroValleyWineOverlay } from '../components/overlays/ThemeableDiscoveryOverlay-Portugal-DouroValley';
   import { onPOIDiscoveryFrameworkEvent, POIDiscoveryFrameworkEvent, POI } from '../components/map/animation/POIDiscoveryFrameworkEvents';
   ```
2. **Listen for Events and Show Overlay**
   ```tsx
   const [event, setEvent] = useState<POIDiscoveryFrameworkEvent | null>(null);
   useEffect(() => {
     const unsubscribe = onPOIDiscoveryFrameworkEvent(setEvent);
     return () => unsubscribe();
   }, []);
   ```
3. **Handle User Actions**
   ```tsx
   <DouroValleyWineOverlay
     event={event}
     onClose={() => setEvent(null)}
     onSavePOI={(poi) => {/* save logic */}}
     onSkipPOI={(poi) => setEvent(null)}
     onShowDetails={(poi) => {/* show modal or navigate */}}
     theme={{ background: '#f3f0e7', accentColor: '#7b3f00', fontFamily: 'Lora, serif' }}
   />
   ```

## Customization Options
- **Theme**: Easily override background, accentColor, fontFamily, and button styles.
- **Actions**: Provide custom handlers for saving, skipping, or showing details for POIs.
- **Language**: Adjust button text and overlay content for local flavor.

## Example Usage
```tsx
<DouroValleyWineOverlay
  event={event}
  onClose={handleClose}
  onSavePOI={handleSavePOI}
  onSkipPOI={handleSkipPOI}
  onShowDetails={handleShowDetails}
  theme={{ background: '#f3f0e7', accentColor: '#7b3f00', fontFamily: 'Lora, serif' }}
/>
```

## Branching and Workflow Notes
- The Portugal theme implementation is committed on a dedicated branch (`portugal-douro-valley-theme`).
- The Morocco base project is preserved on the main branch and via a snapshot commit.
- This approach allows for parallel development and easy client-specific customization.

## Extending for Other Regions
- Copy the overlay template and adjust theme, language, and actions for new regions (e.g., Italy, Morocco, USA).
- Maintain a consistent event-driven integration for all overlays.

## Lessons Learned / Best Practices
- Keep overlays stateless and driven by event props for maximum reusability.
- Use strong typing for events and POIs to ensure type safety across themes.
- Document integration steps and customization points for client onboarding.
- Use dedicated branches for each client/region to streamline delivery and maintenance.

---
*For more details, see the event system documentation in `docs/POIDiscoveryFrameworkEvents.md`.* 