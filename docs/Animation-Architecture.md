# Animation Architecture

This document provides a comprehensive overview of the animation architecture in our Morocco travel map application, describing the core components, their interactions, and the principles that guide the system design.

**2024 Rewrite Note:**
- As of the 2024 rewrite, only `AnimationManager`, `VehicleManager`, and `RouteAnimator` are used in the main animation flow.
- `AnimationIntegration.ts` has been deleted.
- `CinematicController`, `OptimizedAnimationIntegration`, `AnimationFrameManager` are present in the codebase but are not currently integrated into the main animation or UI flow. `CameraBehavior.ts` has been deleted.
- Future work may reintroduce some of these modules or similar ones for advanced cinematic camera moves and orchestration.

**2024 Update:** All camera/cinematic logic is now handled by [`EnhancedCameraBehavior.ts`](../src/components/map/utils/EnhancedCameraBehavior.ts) and [`CinematicController.ts`](../src/components/map/animation/CinematicController.ts). Do not use the old `CameraBehavior.ts` (deleted) or camera helpers in `MapHelpers.ts`. All map transitions must use `bearing: 0` (north-up). The vehicle marker rotates, not the map.

## Architecture Overview

The animation system follows a Manager/Controller pattern with strict separation between animation logic and visual rendering. The architecture is designed with modularity, performance, and error resilience in mind, implementing a clear hierarchy of responsibilities and communication channels.

### Core Principles

1. **Separation of Concerns**: Animation logic is strictly separated from UI rendering
2. **Single Source of Truth**: AnimationManager as the central authority for animation state
3. **Manager Pattern**: Core systems implemented as singletons following the manager pattern
4. **Stateless Components**: UI components remain stateless when possible, receiving state from managers
5. **Event-Driven Communication**: Components communicate via events to maintain loose coupling
6. **Error Resilience**: Built-in recovery mechanisms for animation failures
7. **Contextual Awareness**: Animation behavior adapts to geographic and narrative context

## Component Hierarchy

### Core Animation Managers

1. **AnimationManager**
   - **Responsibility**: Central controller for all animations
   - **Key Functions**: Manages animation loop, calculates positions, handles timing
   - **Implementation**: Singleton pattern with requestAnimationFrame-based loop
   - **State Management**: Maintains authoritative animation state (playing, paused, progress)

2. **VehicleManager**
   - **Responsibility**: Handles vehicle marker DOM elements
   - **Key Functions**: Creates, updates, and manages vehicle visibility and styling
   - **Implementation**: Direct DOM manipulation for optimal performance
   - **Error Handling**: Automatic visibility recovery, emergency marker creation
   - **Position Handling**: Uses standardized Position type system for consistent handling

3. **CameraBehavior / EnhancedCameraBehavior**
   - **Responsibility**: Controls map camera positioning and movement
   - **Key Functions**: Manages zoom levels, transitions, vehicle following
   - **Implementation**: Contextual camera behavior based on terrain and POIs
   - **Standards**: Follows strict zoom level standards (e.g., 8 for route travel, 14 for city exploration)

4. **ComponentInteractionManager**
   - **Responsibility**: Coordinates between animation components and UI
   - **Key Functions**: Manages event dispatching, journey phases, component visibility
   - **Implementation**: Event bus pattern with publisher/subscriber model
   - **State Tracking**: Tracks journey phase and animation sequence

### Specialized Controllers

5. **ContextualSpeedController**
   - **Responsibility**: Adjusts animation speed based on context
   - **Key Functions**: Calculates speed multipliers based on terrain, POIs, cities
   - **Implementation**: Context-aware speed adjustments with smooth transitions

6. **POIDiscovery**
   - **Responsibility**: Manages POI detection and interaction during animation (handled by `src/components/map/utils/POIDiscovery.ts`)
   - **Key Functions**: Detects nearby POIs, triggers notifications, manages discovery state
   - **Implementation**: Proximity-based detection with event dispatching
   - **Position Handling**: Uses standard Position type for calculations

7. **CityDriveByManager**
   - **Responsibility**: Manages city approach and exploration experiences
   - **Key Functions**: Detects city proximity, manages camera transitions, triggers UI updates
   - **Implementation**: Stage-based city approach with specialized camera behaviors

8. **RouteAnimator**
   - **Responsibility**: Handles core animation calculations and route traversal
   - **Key Functions**: Calculates positions along route, manages animation timing
   - **Implementation**: Mathematical interpolation between route points

## Integration Components

9. **OptimizedAnimationIntegration**
   - **Responsibility**: Provides simplified API for animation control (currently not integrated)
   - **Key Functions**: Initializes animation components, starts/stops animations, manages cleanup
   - **Implementation**: Facade pattern integrating multiple animation subsystems

10. **AnimationIntegration (Legacy/Deleted)**
    - **Responsibility**: Legacy integration component. This file has been deleted.
    - **Key Functions**: Provided backward compatibility with existing components.
    - **Implementation**: Was an adapter pattern to new animation architecture.

11. **TravelAnimator**
    - **Responsibility**: React component interface for animations
    - **Key Functions**: Renders UI controls, handles user interaction, initializes animation
    - **Implementation**: React component with animation state management

## Communication Flow

The animation system uses a dual communication approach:

1. **Direct Method Calls**: For time-critical operations and immediate state changes
   ```typescript
   vehicleManager.updatePosition(position, bearing);
   ```

2. **Event System**: For loose coupling between components
   ```typescript
   componentInteractionManager.dispatchAnimationEvent(
     AnimationEventType.ANIMATION_PROGRESS,
     { progress, position, bearing }
   );
   ```

3. **React Props**: For UI component updates
   ```tsx
   <ProgressBar progress={animationProgress} />
   ```

4. **Context API**: For deep component trees requiring animation state
   ```tsx
   <AnimationContext.Provider value={{ state, dispatch }}>
     {children}
   </AnimationContext.Provider>
   ```

## Animation Loop Implementation

The core animation loop is implemented in the AnimationManager using requestAnimationFrame:

```typescript
private startAnimationLoop(): void {
  // Store starting time
  this.startTime = performance.now();
  this.lastFrameTime = this.startTime;
  
  // Define animation frame handler
  const animate = (timestamp: number): void => {
    // Calculate time delta and progress
    const now = timestamp;
    const deltaTime = now - this.lastFrameTime;
    this.lastFrameTime = now;
    
    // Skip if paused but keep the loop running
    if (this.isPaused) {
      this.animationFrame = requestAnimationFrame(animate);
      return;
    }
    
    // Calculate overall progress
    const elapsed = now - this.startTime - this.totalPausedTime;
    const progress = Math.min(elapsed / this.duration, 1);
    
    // Update position based on progress
    this.updateAnimationProgress(progress, deltaTime);
    
    // Check for completion
    if (progress >= 1) {
      this.completeAnimation();
      return;
    }
    
    // Continue animation loop
    this.animationFrame = requestAnimationFrame(animate);
  };
  
  // Start the loop
  this.animationFrame = requestAnimationFrame(animate);
}
```

## Position Calculation and Handling

Route positions are calculated using interpolation between route points with the enhanced Position type system:

```typescript
private calculatePosition(progress: number): Position {
  // Handle edge cases
  if (progress <= 0) return this.route[0];
  if (progress >= 1) return this.route[this.route.length - 1];
  
  // Find the appropriate route segments
  const totalDistance = this.routeDistances[this.routeDistances.length - 1];
  const targetDistance = totalDistance * progress;
  
  // Find segment containing the target distance
  let segmentIndex = 0;
  while (segmentIndex < this.routeDistances.length - 1 && 
         this.routeDistances[segmentIndex] < targetDistance) {
    segmentIndex++;
  }
  
  // Calculate position within segment
  const prevDistance = segmentIndex > 0 ? this.routeDistances[segmentIndex - 1] : 0;
  const segmentLength = this.routeDistances[segmentIndex] - prevDistance;
  const segmentProgress = (targetDistance - prevDistance) / segmentLength;
  
  // Interpolate between points
  const p0 = this.route[segmentIndex - 1] || this.route[0];
  const p1 = this.route[segmentIndex];
  
  return [
    p0[0] + (p1[0] - p0[0]) * segmentProgress,
    p0[1] + (p1[1] - p0[1]) * segmentProgress
  ];
}
```

### Position Type System

The animation system uses a standardized Position type system throughout:

- **Core Position Type**: `Position = [number, number]` (longitude, latitude tuple)
- **Alternative Types**: `MapPosition`, `GeoPosition`, `LatLngPosition` for different API needs
- **Type Safety**: Type guards and validation functions ensure position data integrity
- **Conversion Utilities**: Methods to convert between different position formats
- **Error Handling**: Safe conversion functions with fallbacks for invalid positions

### Position Validation

All consumer components now implement proper position validation:

```typescript
// Example from src/components/map/utils/POIDiscovery.ts
private checkDiscovery(vehiclePosition: Position, poi: POI): void {
  if (!vehiclePosition || !poi.coordinates) {
    return; // Early validation prevents runtime errors
  }
  
  const distance = this.calculateDistance(vehiclePosition, poi.coordinates);
  // Further processing...
}
```

### Safe Position Conversion

We've implemented safe conversion utilities to handle different position formats:

```typescript
// Safe conversion with fallback
export function safeToPosition(input: unknown, fallback: Position): Position {
  try {
    return toPosition(input);
  } catch (error) {
    console.warn('Position conversion failed, using fallback', error);
    return fallback;
  }
}
```

## Timestamp Handling

Timestamps are consistently handled as numeric values using `Date.now()` instead of string ISO timestamps:

```typescript
// Before (DEPRECATED)
const timestamp = new Date().toISOString(); // String timestamp - NO LONGER USED

// After (CURRENT STANDARD)
const timestamp = Date.now(); // Numeric timestamp
```

This standardization across all animation components improves:
- **Performance**: Eliminates unnecessary string conversions
- **Type Safety**: Ensures consistent numeric timestamp types for comparison operations
- **Memory Efficiency**: Reduces memory usage by storing numeric values instead of strings
- **Calculation Efficiency**: Makes time-difference calculations more direct and efficient

All animation components, including `AnimationManager`, `POIDiscoveryManager`, and event systems now use this consistent approach to timestamps.

## TypeScript Configuration Improvements

The animation system relies on proper TypeScript configuration to ensure type safety and module interoperability:

```typescript
// tsconfig.json key settings
{
  "compilerOptions": {
    "esModuleInterop": true,
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
```

### Module Declaration Overrides

To address third-party library compatibility issues, we've implemented module declaration overrides:

```typescript
// src/types/module-declarations.d.ts
declare module 'mapbox-gl' {
  import type { Map, LngLatLike, LngLatBounds } from 'mapbox-gl';
  export * from 'mapbox-gl';
  export default mapboxgl;
}
```

These declaration files ensure proper typing for external dependencies while maintaining compatibility with our codebase.

### Import Patterns

For React imports, we've standardized on using the namespace import pattern:

```typescript
// Correct import pattern for React
import * as React from 'react';

// Preferred import pattern for other modules
import { specificExport } from 'module';
```

## Error Handling & Recovery

The animation system implements comprehensive error handling and recovery mechanisms:

```typescript
private attemptAnimationRecovery(): void {
  if (this.isRecoveryAttemptInProgress || this.recoveryAttemptCount >= this.maxRecoveryAttempts) {
    console.error('Animation recovery failed after maximum attempts');
    this.stopAnimation();
    return;
  }
  
  this.isRecoveryAttemptInProgress = true;
  this.recoveryAttemptCount++;
  
  // Log recovery attempt
  AnimationDebugTools.log('warn', 'Attempting animation recovery', {
    attempt: this.recoveryAttemptCount,
    lastKnownProgress: this.progress,
    lastKnownPosition: this.currentPosition
  });
  
  // Attempt to recreate vehicle
  if (this.vehicleManager) {
    try {
      // Force recreate vehicle at last known position
      if (this.currentPosition) {
        this.vehicleManager.attemptRecovery(this.currentPosition, this.currentBearing);
      }
    } catch (error) {
      AnimationDebugTools.log('error', 'Error during vehicle recovery', { error });
    }
  }
  
  // Resume animation from last known state
  setTimeout(() => {
    try {
      this.isPaused = false;
      this.isRecoveryAttemptInProgress = false;
      
      // If animation was completely stuck, restart it
      if (this.progress === this.lastFrameProgress && this.progress > 0) {
        this.stopAnimation();
        setTimeout(() => {
          this.startAnimation(this.route!, this.options);
        }, 500);
      }
    } catch (recoveryError) {
      AnimationDebugTools.log('error', 'Error resuming animation after recovery', { recoveryError });
      this.isRecoveryAttemptInProgress = false;
    }
  }, 1000);
}
```

## Vehicle Marker Visibility

**2024 Rewrite Notice:**
- The vehicle marker system was rewritten from a blank slate. All marker positioning is now controlled by JavaScript using `transform: translate(-50%, -50%) translate3d(x, y, 0) rotate(bearing)`.
- Do **not** use `!important` for `left`, `top`, or `transform` on `.vehicle-marker` in CSS. This will break JS-based positioning.
- The marker is always a child of the map container, with ID `direct-vehicle-marker` and class `vehicle-marker`.
- All marker creation, update, and recovery logic is handled by the singleton `VehicleManager`.
- **Only `AnimationManager`, `VehicleManager`, and `RouteAnimator` are used in the main animation flow.**
- `CinematicController` and related modules are not currently integrated.

The system uses a multi-layered approach to ensure vehicle marker visibility:

1. **React state management**: For component-level visibility tracking
2. **Direct DOM manipulation**: For immediate visibility changes bypassing React rendering
3. **Automatic recovery**: Periodic checks and automatic marker recreation when needed
4. **Visibility enforcement**: Force-showing important markers during critical animations
5. **Marker ID standardization**: Using consistent ID ('direct-vehicle-marker') across components

When visibility issues occur, a recovery hierarchy is triggered:
1. Attempt to update existing marker's visibility property
2. Force recreate the DOM element if visibility update fails
3. Register a background monitoring task to ensure continued visibility

## Contextual Animation

The animation system adapts to the geographical and narrative context:

### Speed Adjustment

```typescript
public updateSpeedContext(context: SpeedContext): number {
  let multiplier = 1.0; // Default speed
  
  // Apply context-based adjustments
  if (context.inCity) {
    multiplier *= SPEEDS.CITY; // 0.5x speed in cities
  }
  
  if (context.nearPOI) {
    multiplier *= SPEEDS.NEAR_POI; // 0.3x speed near POIs
  }
  
  switch (context.terrain) {
    case 'mountain':
      multiplier *= SPEEDS.MOUNTAIN; // 0.8x speed in mountains
      break;
    case 'desert':
      multiplier *= SPEEDS.DESERT; // 1.2x speed in deserts
      break;
    case 'coastal':
      multiplier *= SPEEDS.COASTAL; // 0.7x speed on coastal routes
      break;
  }
  
  // Smooth transition to new speed
  this.targetSpeedMultiplier = multiplier;
  this.smoothlyTransitionSpeed();
  
  return this.currentSpeedMultiplier;
}
```

### Camera Control

```typescript
public followVehicle(position: Position, bearing: number, options: CameraOptions = {}): void {
  if (!this.map) return;
  
  // Determine terrain type
  const terrain = options.terrain || determineTerrainType(position);
  
  // Calculate appropriate zoom level
  let targetZoom = ZOOM_LEVELS.ROUTE_TRAVEL; // Default: 8
  
  if (options.inCity) {
    targetZoom = ZOOM_LEVELS.CITY_EXPLORATION; // 14
  } else if (options.nearPOI) {
    targetZoom = ZOOM_LEVELS.POI_DETAIL; // 16
  } else {
    // Terrain-specific zoom
    switch (terrain) {
      case 'mountain':
        targetZoom = ZOOM_LEVELS.MOUNTAIN_VIEW; // 10
        break;
      case 'coastal':
        targetZoom = ZOOM_LEVELS.COASTAL_VIEW; // 11
        break;
    }
  }
  
  // Calculate look-ahead position for smooth camera following
  const lookAheadPosition = this.calculateLookAheadPosition(position, bearing);
  
  // Apply camera transition
  this.map.easeTo({
    center: lookAheadPosition,
    zoom: targetZoom,
    bearing: options.inCity ? 0 : bearing, // Keep north up in cities
    pitch: this.calculatePitchForTerrain(terrain),
    duration: 500, // Smooth transition
    essential: true // Mark as essential animation
  });
}
```

## Standard Camera Settings

The animation system follows strict standards for camera behavior:

| Context | Zoom Level | Pitch | Bearing | Transition Duration |
|---------|------------|-------|---------|---------------------|
| Route Travel | 8 | 45° | 0° (North) | 500ms |
| City Exploration | 14 | 50° | 0° (North) | 1500ms |
| POI Detail | 16 | 60° | Varies | 1800ms |
| Mountain Terrain | 10 | 60° | 0° (North) | 800ms |
| Coastal Areas | 11 | 45° | 0° (North) | 800ms |
| Desert | 9 | 40° | 0° (North) | 800ms |
| City Drive-By | Variable (10-14) | 50° | Optimal for skyline | 1200-2000ms |

## Performance Optimization

The animation system implements several performance optimizations:

1. **Throttled Updates**: Position events are throttled to reduce UI updates
   ```typescript
   private throttledEmitPositionEvent = throttle((position, bearing) => {
     this.eventEmitter.emit('vehiclePositionChange', { position, bearing });
   }, 100); // Throttle to 10 updates per second
   ```

2. **Batched DOM Updates**: Style changes are batched to minimize reflows
   ```typescript
   private batchedStyleUpdates: Array<StyleUpdate> = [];
   
   private scheduleStyleUpdate(element: HTMLElement, style: Partial<CSSStyleDeclaration>) {
     this.batchedStyleUpdates.push({ element, style });
     
     if (!this.isApplyingUpdates) {
       this.isApplyingUpdates = true;
       requestAnimationFrame(() => this.applyBatchedStyleUpdates());
     }
   }
   ```

3. **Frame Delay**: Animation frames are delayed to maintain smooth movement
   ```typescript
   const FRAME_DELAY_MS = 100; // Reduced from 16ms for smoother updates
   ```

4. **Look-ahead Caching**: Camera positions are calculated with look-ahead to reduce jitter
   ```typescript
   private positionHistory: Array<[number, number]> = [];
   
   private calculateCameraCenter(position, bearing) {
     // Add to history for smoothing
     this.positionHistory.push(position);
     if (this.positionHistory.length > 5) {
       this.positionHistory.shift();
     }
     
     // Calculate smoothed position
     return this.calculateSmoothedPosition();
   }
   ```

## React Integration

The animation system integrates with React through several approaches:

1. **Custom Hooks**: Encapsulate animation logic for React components
   ```typescript
   export const useAnimationProgress = (isAnimating) => {
     const [progress, setProgress] = useState(0);
     
     useEffect(() => {
       if (!isAnimating) return;
       
       const animationManager = AnimationManager.getInstance();
       
       const handleProgress = (newProgress) => {
         setProgress(newProgress);
       };
       
       animationManager.addProgressListener(handleProgress);
       
       return () => {
         animationManager.removeProgressListener(handleProgress);
       };
     }, [isAnimating]);
     
     return progress;
   };
   ```

2. **Animation Context**: Provide animation state to component tree
   ```typescript
   const AnimationContext = createContext(null);
   
   export const AnimationProvider = ({ children }) => {
     const [state, dispatch] = useReducer(animationReducer, initialState);
     
     // Connect to animation manager
     useEffect(() => {
       const animationManager = AnimationManager.getInstance();
       
       const handleProgress = (progress, position) => {
         dispatch({ 
           type: 'UPDATE_PROGRESS', 
           payload: { progress, position } 
         });
       };
       
       animationManager.addProgressListener(handleProgress);
       
       return () => {
         animationManager.removeProgressListener(handleProgress);
       };
     }, []);
     
     return (
       <AnimationContext.Provider value={{ state, dispatch }}>
         {children}
       </AnimationContext.Provider>
     );
   };
   ```

3. **Effect Cleanup**: Ensure proper animation cleanup in React components
   ```typescript
   useEffect(() => {
     // Initialize animation
     const animationManager = AnimationManager.getInstance();
     const vehicleManager = VehicleManager.getInstance();
     
     // Start animation when ready
     if (isReady && routeData) {
       animationManager.startAnimation(routeData, {
         onProgress: handleProgress,
         onComplete: handleComplete
       });
     }
     
     // Clean up on unmount
     return () => {
       animationManager.stopAnimation();
       vehicleManager.showVehicle(false);
     };
   }, [isReady, routeData]);
   ```

## Debugging Support

The animation system includes comprehensive debugging capabilities:

1. **Animation Debug Tools**: Centralized debug utilities
   ```typescript
   AnimationDebugTools.log('Animation started', {
     routePoints: route.length,
     options
   });
   ```

2. **Performance Monitoring**: Track animation performance metrics
   ```typescript
   const metrics = animationPerformanceMonitor.getMetrics();
   console.log(`Animation FPS: ${metrics.fps.toFixed(1)}`);
   ```

3. **Visual Debug Indicators**: Highlight animation elements in debug mode
   ```typescript
   if (this.debugMode) {
     this.addDebugHighlight(this.vehicleMarkerElement);
   }
   ```

4. **Animation Event Logging**: Track animation event sequence
   ```typescript
   private logEventSequence(eventType, data) {
     if (!this.animationSequenceLog) return;
     
     const now = performance.now();
     
     if (!this.sequenceStartTime) {
       this.sequenceStartTime = now;
     }
     
     this.eventSequence.push({
       eventType,
       timestamp: now,
       elapsed: now - this.sequenceStartTime,
       progress: data.progress,
       details: JSON.stringify(data).substring(0, 100)
     });
   }
   ```

## File Structure

The animation system is organized into the following file structure:

```
src/components/map/animation/
├─ AnimationManager.ts         # Core animation loop and state management
├─ VehicleManager.ts           # Vehicle marker management
├─ ComponentInteractionManager.ts # Event bus for animation components
├─ POIDiscoveryManager.ts      # POI detection and interaction
├─ CityDriveByManager.ts       # City approach management
├─ ContextualSpeedController.ts # Speed adjustments based on context
├─ RouteAnimator.ts            # Route animation calculations
├─ AnimationIntegration.ts     # Integration point for React components
├─ OptimizedAnimationIntegration.ts # High-performance integration
├─ AnimationDebugTools.ts      # Debugging utilities
├─ AnimationFrameManager.ts    # Animation frame management
├─ AnimationMonitoring.ts      # Performance monitoring
├─ CinematicController.ts      # Cinematic animation sequences
├─ AnimationUIUtils.ts         # UI integration utilities
├─ utils/                      # Shared utilities
└─ README.md                   # Architecture documentation
```

## Best Practices

When working with the animation system, follow these best practices:

1. **Always Clean Up**: Cancel animation frames and remove event listeners
   ```typescript
   public dispose(): void {
     this.stopAnimation();
     
     if (this.animationFrame !== null) {
       cancelAnimationFrame(this.animationFrame);
       this.animationFrame = null;
     }
     
     this.eventListeners.forEach(listeners => listeners.clear());
     this.eventListeners.clear();
   }
   ```

2. **Use Manager Classes**: Access functionality through manager singletons
   ```typescript
   const animationManager = AnimationManager.getInstance();
   const vehicleManager = VehicleManager.getInstance();
   ```

3. **Handle Errors**: Implement proper error handling and recovery
   ```typescript
   try {
     animationManager.startAnimation(routeData);
   } catch (error) {
     console.error('Animation failed to start', error);
     
     // Attempt recovery
     setTimeout(() => {
       animationManager.stopAnimation();
       animationManager.startAnimation(routeData);
     }, 1000);
   }
   ```

4. **Respect Camera Standards**: Use appropriate zoom levels for each context
   ```typescript
   // For route travel
   map.easeTo({ zoom: 8, pitch: 45, bearing: 0 });
   
   // For city exploration
   map.easeTo({ zoom: 14, pitch: 50, bearing: 0 });
   ```

5. **Optimize for Performance**: Use throttling and batching for smooth animations
   ```typescript
   // Throttle position updates
   const throttledUpdate = throttle((position) => {
     updateVehiclePosition(position);
   }, 100);
   ```

## Conclusion

The animation system provides a robust foundation for creating engaging and performant map animations. By following the architectural principles and utilizing the provided manager classes, developers can create rich animated experiences while maintaining code quality and performance. 