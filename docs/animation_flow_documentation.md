# Animation Flow Documentation

## File: `src/components/map/ExploreMap.tsx`

**Purpose:**
This component is the main interface for users to explore the map, select cities, view routes, and initiate the travel animation. It manages map interactions, POI display, route fetching, and the initial phases of the journey (city selection, countdown).

**Core Functionality & Flow:**

1.  **Initialization & Map Setup:**
    *   Initializes the Mapbox map instance.
    *   Sets up initial camera position and map style.
    *   Loads initial city data and displays city markers.
    *   Manages map readiness state (`isMapReady`, `isStyleLoaded`).

2.  **User Interaction & City Selection:**
    *   Handles map clicks to select cities as origin or destination.
    *   Manages `selectedOrigin` and `selectedDestination` states.
    *   Allows users to clear selections.
    *   Displays selected city information and POIs via `CityPanel`.

3.  **Route Calculation & Display:**
    *   When origin and destination are selected, it triggers route calculation via `fetchRoute` (likely a call to an API or a local utility like `RouteService`).
    *   Stores the fetched `selectedRoute` (an array of `PositionTuple`).
    *   Displays the route on the map using `MapRoute` component.
    *   Manages route loading (`isRouteLoading`) and error states (`routeError`).

4.  **Journey Initiation & Countdown:**
    *   `canBeginJourney`: A derived boolean state that becomes true when `selectedRoute` is valid (has at least 2 points).
    *   `pendingJourneyStart`: A boolean state set to `true` when `canBeginJourney` becomes true and `journeyStarted` is false. This indicates the "Begin Journey" button should be active.
    *   `handleJourneyStart`:
        *   Called when the "Begin Journey" button is clicked.
        *   Sets `countdownVisible` to `true` to show the `CountdownLoadingIndicator`.
        *   Sets `isAnimating` to `true` (this prop is passed to `TravelAnimator` to signal it should prepare for animation).
        *   Sets `pendingJourneyStart` to `false`.
        *   Sets `journeyStarted` to `true`.
    *   `CountdownLoadingIndicator`:
        *   Rendered when `countdownVisible` is `true`.
        *   Takes an `onCountdownComplete` prop, which is `handleCountdownComplete`.
    *   `handleCountdownComplete`:
        *   Called by `CountdownLoadingIndicator` when its internal countdown finishes.
        *   Sets `countdownComplete` to `true`.
        *   Sets `countdownVisible` to `false`.
        *   `animationLogger.log` indicates the countdown is complete and `TravelAnimator` should now take over.

5.  **Passing Control to `TravelAnimator`:**
    *   `TravelAnimator` is rendered conditionally based on `journeyStarted && selectedRoute && selectedRoute.length > 0`.
    *   Key props passed to `TravelAnimator`:
        *   `startAnimationSignal={isAnimating}`: Signals `TravelAnimator` to prepare/start. Based on the refactor, this is `true` when `handleJourneyStart` is called.
        *   `countdownHostComplete={countdownComplete}`: Signals `TravelAnimator` that the countdown in `ExploreMap` is done.
        *   `route={selectedRoute}`: The route for the animation.
        *   `selectedPOIs`: POIs selected by the user.
        *   `animationOptions`: Configuration for the animation.
        *   `onAnimationComplete`: Callback for when `TravelAnimator` finishes.
        *   `onAnimationStop`: Callback for when `TravelAnimator` is stopped.

6.  **Journey Completion & Reset:**
    *   `handleAnimationComplete`:
        *   Called by `TravelAnimator` when the animation finishes.
        *   Resets various states: `isAnimating`, `journeyStarted`, `countdownComplete`, `selectedRoute`, etc., to allow for a new journey.
    *   `handleAnimationStop`: Similar to `handleAnimationComplete` but might be used for user-initiated stops.

**State Management Highlights:**

*   `selectedOrigin`, `selectedDestination`: Store `CityLocation` objects.
*   `selectedRoute`: Stores `PositionTuple[]`.
*   `isRouteLoading`, `routeError`: For route fetching status.
*   `isAnimating`: Boolean, primarily controlled by `handleJourneyStart` and reset on animation completion/stop. Signals `TravelAnimator` to act.
*   `countdownVisible`: Boolean, controls visibility of `CountdownLoadingIndicator`.
*   `countdownComplete`: Boolean, set by `handleCountdownComplete`.
*   `journeyStarted`: Boolean, indicates the overall journey process (including countdown and actual animation) is active. Set by `handleJourneyStart`.
*   `pendingJourneyStart`: Boolean, indicates the "Begin Journey" button should be active.

**Key Imports & Modular Files Used:**

*   `react` (useState, useEffect, useCallback, useMemo, useRef)
*   `mapbox-gl`
*   `@/components/ui/button`
*   `@/components/ui/toast`
*   `@/components/map/common/MapControls`
*   `@/components/map/common/MapRoute`
*   `@/components/map/common/DirectPOIMarkers`
*   `@/components/map/common/CityMarkersLayer`
*   `@/components/map/info-panel/CityPanel`
*   `@/components/map/TravelAnimator`
*   `@/components/map/common/CountdownLoadingIndicator`
*   `@/hooks/useMapboxReadiness`
*   `@/hooks/useMapInteractions`
*   `@/services/RouteService` (presumably for `fetchRoute`)
*   `@/utils/config` (for `MAPBOX_ACCESS_TOKEN`, `MAP_INITIAL_VIEW_STATE`)
*   `@/utils/geoUtils` (for `calculateBoundingBox`)
*   `@/utils/animationLogger`
*   `@/types/POITypes` (City, PointOfInterest, CityLocation, Destination, normalizePOI)
*   `@/types/Position` (Position, PositionTuple)
*   `@/types/MapTypes` (MapStyle, MapViewType)
*   `@/data/cityData`

**Potential Issues/Areas of Concern based on recent debugging:**
*   The interplay between `isAnimating`, `countdownComplete`, and `journeyStarted` needs to be precise to avoid race conditions or incorrect signaling to `TravelAnimator`. The recent refactor aimed to clarify this.
*   Ensuring that `TravelAnimator` only receives the `startAnimationSignal` and `countdownHostComplete` signals *once* and in the correct order is critical.

## File: `src/components/map/TravelAnimator.tsx`

**Purpose:**
This component is responsible for managing and rendering the actual travel animation along a given route. It takes signals from `ExploreMap.tsx` (or a similar parent) to start, handles the animation lifecycle using `useAnimationManager`, updates the vehicle's position via `VehicleManager`, manages POI discovery display during the animation, and provides UI controls.

**Core Functionality & Flow:**

1.  **Props & Initialization:**
    *   Receives `startAnimationSignal` and `countdownHostComplete` from parent to know when to start.
    *   Receives `route` (PositionTuple[]), `selectedPOIs`, `animationOptions`, and callbacks like `onAnimationComplete`, `onProgressUpdate`.
    *   Initializes `VehicleManager` (ref) and `POIDiscoveryManager` (ref).
    *   Uses `useAnimationManager` hook to get animation control functions (`startAnimationHook`, `togglePause`, `stopAnimation`, etc.) and state (`hookIsPaused`, `isAnimating` from hook, `progress` from hook).

2.  **State Management:**
    *   `currentAnimationState`: Local React state (AnimationState enum: IDLE, PREPARING, COUNTDOWN, RUNNING, PAUSED, COMPLETED, ERROR).
    *   `animationStateRef`: Ref to keep `currentAnimationState` in sync for callbacks and effects.
    *   `routeRef`: Stores the current route to be animated.
    *   `loadingMessage`: For displaying messages like "Calculating route..." or "Starting animation...".
    *   `animationErrorMessage`: Stores error messages.
    *   `discoveredPoisForDisplay`: Stores POIs discovered during the animation for the `OnTheWayPOIOverlay`.
    *   `startAnimationTrigger`: Internal signal based on props.
    *   `readyToStartAnimationRef`: Ref indicating if all conditions (props signals, route loaded) are met to start.

3.  **Route Loading (`useEffect` based on `propsDestinations`, `initialRoute`, `map`):**
    *   If `initialRoute` is provided, it's used directly (after de-duping consecutive points).
    *   If `propsDestinations` are provided, it calls `loadRouteData` (from `RouteDataLoader.ts`) to fetch the route.
    *   Sets `loadingMessage` during route calculation.
    *   Stores the loaded (and de-duped) route in `routeRef.current`.
    *   Handles errors during route loading by setting `animationErrorMessage`.

4.  **Signal Handling (`useEffect` based on `startAnimationSignal`, `countdownHostComplete`):**
    *   Monitors `startAnimationSignal` and `countdownHostComplete` props.
    *   When both are `true`:
        *   Sets `startAnimationTrigger` to `true`.
        *   Sets `readyToStartAnimationRef.current` to `true`.
        *   Resets `currentProgress` to 0.
        *   Crucially, it *should not* aggressively reset `currentAnimationState` to `IDLE` if other effects are managing transitions, as this can cause loops. The goal of this effect is to signal readiness.
    *   If `startAnimationSignal` becomes `false` or `countdownHostComplete` is false while animation was active, it calls `handleStop` to stop the animation.

5.  **Main Animation Lifecycle (`useEffect` based on `startAnimationSignal`, `countdownHostComplete`, `currentAnimationState`, `startAnimationHook`, `stopAnimation`):**
    *   This is the core effect that drives state transitions for starting the animation.
    *   **Condition: `startAnimationSignal && countdownHostComplete` is TRUE:**
        *   **If `currentAnimationState === AnimationState.IDLE && routeRef.current` (route is available):**
            *   Transitions local state to `PREPARING`.
            *   Initializes/resets `VehicleManager` and sets its map instance.
            *   (Countdown display is now handled by `ExploreMap`).
        *   **If `currentAnimationState === AnimationState.PREPARING && animationStateRef.current === AnimationState.PREPARING && routeRef.current`:**
            *   Sets `loadingMessage` to "Starting animation...".
            *   Ensures vehicle is visible via `vehicleManagerRef.current?.setVehicleVisibility(true)`.
            *   Calls `startAnimationHook({ coordinates: actualRouteTuples }, propAnimationOptions || {})`.
                *   The `.then()` block logs success/failure of *initiating* the animation. It *does not* set state to RUNNING directly.
                *   The actual transition to `RUNNING` state is expected to come from an event dispatched by `useAnimationManager` / `AnimationManager`.
    *   **Condition: `currentAnimationState !== AnimationState.IDLE && (!startAnimationSignal || !countdownHostComplete)`:**
        *   If animation was running/paused but signals are now off, it stops the animation (`stopAnimation()`) and resets local state to `IDLE`.

6.  **Event Subscription from `useAnimationManager` (`useEffect` based on `addAnimationEventListener`, `removeEventListener`, etc.):**
    *   Subscribes to events from the `animationManager` instance provided by `useAnimationManager`.
    *   `AnimationEventType.PROGRESS_UPDATE`:
        *   Calls `handleHookAnimationProgress` which:
            *   Updates parent component via `onProgressUpdateProp`.
            *   Updates vehicle marker position and bearing using `vehicleManagerRef.current.updateVehiclePosition()`.
            *   Checks for nearby POIs and updates `discoveredPoisForDisplay`.
    *   `AnimationEventType.STATE_CHANGE`:
        *   Updates `currentAnimationState` and `animationStateRef.current`.
        *   If new state is `COMPLETED`, calls `handleHookAnimationComplete`.
        *   If new state is `ERROR`, calls `handleHookAnimationError`.

7.  **Vehicle Management:**
    *   `vehicleManagerRef.current.updateVehiclePosition()`: Called on progress updates.
    *   `vehicleManagerRef.current.setVehicleVisibility()`: Called when starting/stopping or if marker becomes invisible.
    *   Periodic visibility check (`useEffect` based on `currentAnimationState`): Runs an interval to check `isVehicleVisible()` and force visibility if needed while `RUNNING` or `PAUSED`.

8.  **UI Rendering:**
    *   `AnimationControls`: Renders play/pause/stop buttons. Its state (`dynamicButtonState`) is derived from `currentAnimationState`, `loadingMessage`, `startAnimationSignal`, and `countdownHostComplete`.
        *   `onPauseResume` prop is connected to `handlePauseResume`.
        *   `onStop` prop is connected to `handleStop`.
    *   `OnTheWayPOIOverlay`: Displays `discoveredPoisForDisplay`.
    *   `CountdownLoadingIndicator`: Displays `loadingMessage` if present (e.g., "Calculating route...", "Starting animation...").
    *   `ErrorDisplay`: Shows `animationErrorMessage`.

9.  **Cleanup (`useEffect` returning a function, and `handleStop`):**
    *   The main lifecycle `useEffect`'s cleanup function is implicitly handled by its dependencies causing re-runs which might call `stopAnimation`.
    *   `handleStop` ensures `stopAnimation()` from `useAnimationManager` is called and resets local state.
    *   Event listener cleanup is handled in the `useEffect` that subscribes to them.

**Key Imports & Modular Files Used:**

*   `react` (useState, useCallback, useRef, useEffect, useMemo, forwardRef, useImperativeHandle)
*   `mapbox-gl`
*   `./animation/VehicleManager`
*   `../../types/POITypes`
*   `../../types/Position`
*   `./animation/AnimationDebugTools`
*   `./overlays/OnTheWayPOIOverlay`
*   `./components/AnimationControls`
*   `@/utils/animationLogger`
*   `./animation/RouteDataLoader`
*   `./animation/AnimationUtils` (for `showCountdown`, though its direct usage here might be legacy)
*   `@/types/MultiClientAnimationTypes`
*   `@/components/ui/CountdownLoadingIndicator`
*   `@/components/ui/ErrorDisplay`
*   `./TravelAnimator.module.css`
*   `../../hooks/useAnimationManager` (AnimationHookResult)
*   `@turf/turf` (for distance calculations for dynamic duration)
*   `../../types/AnimationEventTypes` (AnimationEventType)
*   `./animation/AnimationManagerWrapper` (AnimationState, AnimationOptions, RouteData, etc.) - *This is a key abstraction layer.*
*   `./animation/POIDiscoveryManager`
*   `./MapComponent` (MapFeatureOptions - though usage seems minimal/indirect)
*   `../../hooks/use-mapbox`
*   `../../types/TravelAnimatorTypes` (ButtonState)

**Potential Issues/Areas of Concern based on recent debugging:**

*   **State Synchronization:** The component uses both its own `currentAnimationState` (React state) and `animationStateRef.current` (ref), plus relies on state from `useAnimationManager` (`hookIsPaused`, `isAnimating`, `progress`). Ensuring these are always perfectly synchronized and that effects trigger based on the *correct source of truth* for that specific effect is crucial and has been a source of bugs.
*   **Effect Dependencies & Execution Order:** The multiple `useEffect` hooks managing different parts of the lifecycle (route loading, signal handling, main animation start, event subscriptions) can interact in complex ways. If dependencies are not perfectly managed, effects can run too often, not often enough, or in an unintended order.
*   **Double Countdown Source:** The "countdown runs twice" symptom strongly suggests that `TravelAnimator` might still have some residual countdown logic or that its state transitions are causing `ExploreMap` to re-trigger its own countdown inadvertently. The primary countdown should be in `ExploreMap`.
*   **Animation Not Starting:** If `startAnimationHook` is called but the animation doesn't visually start, the issue could be:
    *   `AnimationManager` (via `useAnimationManager`) isn't transitioning to a `RUNNING` state internally.
    *   Events from `AnimationManager` are not being correctly received or processed by `TravelAnimator`.
    *   `VehicleManager` is not correctly creating or updating the vehicle marker.
    *   Map camera is not moving.
*   **Clarity of `startAnimationHook`'s Promise:** The promise returned by `startAnimationHook` indicates *initiation success*, not *animation completion*. The state transition to `RUNNING` should be event-driven. The logs now reflect that `TravelAnimator` waits for the `STATE_CHANGE` event to confirm the `RUNNING` state. 

## File: `src/components/map/animation/AnimationManagerWrapper.ts`

**Purpose:**
This class acts as a singleton wrapper and a facade around the core `AnimationManager`. It's intended to be the primary interface for UI components (like `TravelAnimator` via `useAnimationManager` hook) to interact with the animation system. It simplifies the API, manages the lifecycle and dependencies of the `AnimationManager` and other potential subsystems (like `CityDriveByManager`, `POIDiscoveryManager` - though these are currently mock implementations within the wrapper), and re-exports crucial types from `AnimationManager`.

**Core Functionality & Flow:**

1.  **Singleton Pattern:**
    *   Uses a private constructor and `public static getInstance()` method to ensure only one instance exists.

2.  **Initialization (`initialize(map: mapboxgl.Map)`):**
    *   Stores the map instance.
    *   Instantiates `CityDriveByManager` and `POIDiscoveryManager` (currently mocks).
    *   Crucially, it obtains an instance of the main `AnimationManager` using `AnimationManager.getInstance(map, defaultConfig)`.
    *   Subscribes to key events from the `mainAnimationManager` (`STATE_CHANGE`, `PROGRESS_UPDATE`, `ANIMATION_COMPLETE`, `ANIMATION_ERROR`) and forwards/handles them in `handleCoreAnimationEvent`.
    *   Sets `isInitialized` to `true`.

3.  **Core Animation Method Delegation:**
    *   `startAnimation(route, options)`: Delegates directly to `this.mainAnimationManager.startAnimation()`.
    *   `pauseAnimation(isPaused?)`: Delegates to `this.mainAnimationManager.pauseAnimation()`.
    *   `resumeAnimation()`: Calls `this.mainAnimationManager.pauseAnimation(false)` (assuming this is how the core manager resumes).
    *   `stopAnimation()`: Delegates to `this.mainAnimationManager.stopAnimation()`.
    *   Other getter methods (`getAnimationState`, `getCurrentPosition`, `getCurrentBearing`, `getProgress`, `getIsPaused`, `isCurrentlyAnimating`, `getState`) also primarily delegate to `mainAnimationManager` if available, with some fallbacks to its own internal state if the main manager isn't ready.

4.  **Event Handling:**
    *   `addEventListener(eventType, listener)`: 
        *   Primarily delegates to `mainAnimationManager.addEventListener()`.
        *   Has a fallback mechanism to use its internal `eventListeners` map and the `ComponentInteractionManager` if `mainAnimationManager` or its `addEventListener` is not available. This fallback seems like a legacy or parallel system.
    *   `removeEventListener(eventType, listener)`: Similar delegation and fallback logic as `addEventListener`.
    *   `handleCoreAnimationEvent(eventType, eventData)`: 
        *   This private method is the callback for events received from `mainAnimationManager`.
        *   It iterates through listeners registered with the wrapper (via the fallback path) and calls them.
        *   It also updates the wrapper's internal state (`currentAnimationState`, `isAnimating`, `isPaused`, `animationProgress`) based on these core events.

5.  **State Management (Internal to Wrapper):**
    *   `isInitialized`, `isAnimating`, `isPaused`, `animationProgress`, `currentAnimationState`: These are internal state variables of the wrapper. They are primarily updated by `handleCoreAnimationEvent` based on events from `mainAnimationManager`. Some methods also directly set these (e.g., `stopAnimation` sets `isAnimating = false`).
    *   The internal animation loop (`startAnimationLoop`, `animate`) and POI/City checking (`checkForPOIs`, `checkForCities`) seem to be legacy or a parallel implementation that might conflict or be redundant if `mainAnimationManager` is handling the core animation logic and event emission. The presence of `mainAnimationManager` suggests these internal loop/check methods in the wrapper might be unused or deprecated.

6.  **Type Re-exporting:**
    *   Re-exports `AnimationState`, `AnimationOptions`, `RouteData`, `AnimationManagerInterface`, `AnimationEventCallback`, `AnimationEventType`, and `AnimationProgressInfo` from `AnimationManager` (using `Core` prefix aliases internally) so that consumers of the wrapper use the types defined by the core system.

7.  **Dependencies & Other Managers:**
    *   `ComponentInteractionManager`: Used as part of the fallback event system.
    *   `CityDriveByManager`, `POIDiscoveryManager`: Currently mock classes within the wrapper. A full implementation would likely involve separate, more complex modules.

**Key Imports & Modular Files Used:**

*   `mapbox-gl`
*   `@/utils/animationLogger`
*   `../../../types/Position`
*   `@/types/MultiClientAnimationTypes` (various event and option types, some might be superseded by `AnimationManager`'s own types)
*   `../../../types/AnimationEventTypes`
*   `@turf/turf` (for bearing calculation in the internal legacy animation loop)
*   `../../../types` (Destination)
*   `../../../utils/AnimationDebugTools`
*   `./AnimationManager` (This is the CORE dependency: `AnimationManager` class and its associated types like `AnimationState as CoreAnimationState`, etc.)
*   `./ComponentInteractionManager`

**Potential Issues/Areas of Concern:**

*   **Dual Event Systems/Animation Loops:** The wrapper has logic to delegate to `mainAnimationManager` for most operations and event handling. However, it also contains its own internal animation loop (`startAnimationLoop`, `animate`), POI/city checking methods, and an event listener mechanism (`this.eventListeners`) that seems to operate in parallel or as a fallback to `ComponentInteractionManager`. This duality can lead to confusion, redundancy, or conflicts. If `mainAnimationManager` is fully functional and emits all necessary events, the wrapper's internal loop and direct event emission might be unnecessary.
*   **State Synchronization:** The wrapper maintains its own state (`isAnimating`, `isPaused`, etc.) which is updated based on events from `mainAnimationManager`. Ensuring this remains perfectly in sync and that methods use the correct source of truth is important.
*   **Mock Subsystems:** `CityDriveByManager` and `POIDiscoveryManager` are currently stubs. If these are meant to be functional, they would need proper implementations and integration.
*   **Clarity of Responsibility:** The primary role of the wrapper should be to simplify the interface to `AnimationManager` and manage its lifecycle. The extent to which it runs its own animation logic versus purely delegating should be clear. The current structure suggests a transition phase where some older wrapper-based logic might still exist alongside delegation to the newer `AnimationManager`. 

## File: `src/components/map/animation/VehicleManager.ts`

**Purpose:**
This singleton class is solely responsible for managing the visual representation of the vehicle on the map. This includes creating the marker, updating its position and rotation, controlling its visibility, applying styles based on context (like terrain or speed), and implementing robust error recovery and visibility assurance mechanisms.

**Core Functionality & Flow:**

1.  **Singleton Pattern & Initialization:**
    *   `getInstance()`: Provides the single instance of `VehicleManager`.
    *   `initialize(map)`: Can be called to set the map instance, though `setMap(map)` is the primary method for this.
    *   `setMap(map)`: Stores the `mapboxgl.Map` instance. Critically, if a marker was previously created and is supposed to be visible but isn't in the DOM, it attempts to recreate it.

2.  **Vehicle Marker Creation (`createVehicleMarker(options)`):**
    *   This is the current method used for creating the marker.
    *   Takes `VehicleMarkerOptions` (position, bearing, elementId, className, style).
    *   Creates a custom `div` element (`this._customElement`).
    *   Applies initial styles using `applyVehicleStyle` (sets display, dimensions, colors, etc.).
    *   **Crucially, it explicitly sets `display: 'block'`, `width`, `height`, and `boxSizing` on the custom element, then temporarily appends it to `document.body` to measure `offsetWidth` and `offsetHeight`. It removes it immediately after.** This step is vital to ensure the element has dimensions *before* Mapbox uses it.
    *   Creates a `mapboxgl.Marker` instance (`this._markerInstance`), passing the `customElement` to its `element` option.
    *   Sets the marker's LngLat, rotation, and adds it to the map.
    *   Updates internal state: `_lastPosition`, `_lastBearing`, `_isVisible`.
    *   Includes error handling and cleanup for partial creations.

3.  **Position & Bearing Updates (`updateVehiclePosition(position, bearing, options)`):**
    *   If the marker instance or custom element is missing, it attempts to recreate it using `recreateVehicleMarker` (if `_lastPosition` is known).
    *   Validates the incoming `position`.
    *   Updates the `mapboxgl.Marker` instance directly using `_markerInstance.setLngLat()` and `_markerInstance.setRotation()`.
    *   Updates internal `_lastPosition` and `_lastBearing`.
    *   If `options.terrain` or `options.speedMultiplier` are provided, it calls `updateVehicleStyleForTerrain` or `updateVehicleStyleForSpeed` respectively.
    *   Emits `AnimationEventType.VEHICLE_POSITION_UPDATED`.

4.  **Visibility Control:**
    *   `setVehicleVisibility(isVisible, options)`: 
        *   This is the primary method for controlling visibility.
        *   Directly manipulates `this._customElement.style.visibility`, `style.display`, and `style.opacity`.
        *   Supports animated transitions for opacity.
        *   Adds/removes a `force-visible-vehicle` class for debugging/emphasis.
        *   As a backup, it also tries to set `display` and `visibility` on the element returned by `this._markerInstance.getElement()`.
        *   Updates `this._isVisible`.
    *   `showVehicle(isVisible = true)`: A convenience wrapper around `setVehicleVisibility`. If the element doesn't exist but should be visible and a last position is known, it tries to create it first. If regular visibility setting fails for `isVisible=true`, it calls `forceVehicleVisibility`.
    *   `hideVehicle()`: Calls `setVehicleVisibility(false)`.
    *   `isVehicleVisible()`: Checks internal `_isVisible` state, if the Mapbox marker has coordinates, and if the custom element is connected to the DOM.

5.  **Style Updates:**
    *   `applyVehicleStyle(marker: HTMLElement, style: Partial<VehicleStyle>)`: 
        *   Applies various CSS properties to the provided marker element (e.g., `backgroundColor`, `width`, `height`, `opacity`, `borderColor`, `borderWidth`, `zIndex`, `borderRadius`).
        *   Sets `data-terrain` and `data-icon` attributes.
    *   `updateVehicleStyleForTerrain(terrain)`: Applies terrain-specific icon and color using `applyVehicleStyle`.
    *   `updateVehicleStyleForSpeed(speedMultiplier)`: Adds/removes CSS classes (`vehicle-speed-normal`, `vehicle-speed-slow`, etc.) to the custom element and updates a speed indicator child element (🐢/🚀).

6.  **Marker Removal & Reset:**
    *   `removeVehicleMarker()`: Calls `this._markerInstance.remove()` and nullifies `_markerInstance` and `_customElement` references. Sets `_isVisible` to false.
    *   `reset()`: Calls `removeVehicleMarker()` and resets all internal state variables (`_lastPosition`, `_lastBearing`, `_isVisible`, etc.).

7.  **Error Recovery & Visibility Assurance:**
    *   `recreateVehicleMarker(position)`: Calls `createVehicleMarker` to try and rebuild the marker if it's missing. Used by `setMap`, `updateVehiclePosition`, `recoverVehicleVisibility`, and `forceVehicleVisibility`.
    *   `recoverVehicleVisibility()`: Attempts to recover visibility if `isVehicleVisible()` is false. It checks if the marker instance/element exists (recreating if necessary via `recreateVehicleMarker`), re-syncs `_customElement` if disconnected, forces visibility via `setVehicleVisibility(true)`, and re-applies last known position/bearing.
    *   `forceVehicleVisibility()`: A more aggressive last-resort method. Recreates marker if needed, ensures it's added to the map, and applies direct CSS to `_customElement` (`display: 'block'`, `visibility: 'visible'`, `opacity`, `zIndex`).

8.  **DOM & Debug Utilities:**
    *   `getVehicleMarkerElement()`: Returns `this._customElement` if connected, with a fallback to get it from `_markerInstance.getElement()`.
    *   `isVehicleDOM()`: Checks `!!this._customElement?.isConnected`.
    *   `getVehicleDebugInfo()`: Returns a comprehensive object with status, element properties, and computed styles for troubleshooting.
    *   `addDebugEntry()`: Internal logging utility that also uses `AnimationDebugTools.log()`.
    *   `emitEvent()`: Dispatches custom DOM events (e.g., `VEHICLE_POSITION_UPDATED`, `VEHICLE_MARKER_ERROR`).

**Key Imports & Modular Files Used:**

*   `../../../types` (Position)
*   `../../../types/VehicleTypes` (VehicleMarkerOptions, VehicleStyle)
*   `../../../types/AnimationEventTypes` (AnimationEventType)
*   `../../../types/TerrainType` (TerrainType)
*   `./AnimationDebugTools`
*   `mapbox-gl`

**Marker Implementation Strategy:**

The current strategy relies on creating a custom HTML element (`_customElement`) and then passing this element to `new mapboxgl.Marker({ element: this._customElement, ... })`. Mapbox GL JS then takes this custom element and wraps it in its own container, positioning that container on the map.

**Potential Issues/Areas of Concern Based on Recent Debugging:**

*   **Marker Not Appearing:** This has been the primary issue. The multifaceted approach to ensure the custom element has dimensions *before* Mapbox takes it (setting explicit style, temporary DOM append for measurement) is key. If this isn't flawless, Mapbox might not position it correctly or it might be invisible (0x0 size).
*   **Synchronization between `_customElement` and `_markerInstance.getElement()`:** While Mapbox uses the provided `_customElement`, it wraps it. Most direct style manipulations should occur on `_customElement`. Confusion can arise if one attempts to manipulate `_markerInstance.getElement()` expecting it to be identical to `_customElement` after Mapbox has processed it.
*   **CSS Conflicts:** External CSS targeting `.mapboxgl-marker` (Mapbox's own wrapper class) or the custom marker's classes (`vehicle-marker`, `direct-vehicle-marker`) could interfere with the styles applied directly by `VehicleManager`.
*   **Map Readiness:** Although `setMap` is present, ensuring the map is fully loaded (style, sources, etc.) before marker operations are attempted is critical. Race conditions here could lead to errors.
*   **Complexity of Recovery:** The multiple layers of recovery (`recoverVehicleVisibility`, `forceVehicleVisibility`, `recreateVehicleMarker`) are extensive. While good for robustness, they can make debugging the root cause difficult if one of the recovery mechanisms is masking an underlying issue or introducing its own side effects.

## File: `src/components/map/animation/AnimationManager.ts`

**Purpose:**
This singleton class is the core engine for route-based animations. It manages the animation loop, calculates vehicle position and bearing along a route, handles timing (start, stop, pause, resume), adjusts speed based on context (via `ContextualSpeedController`), and dispatches events for progress and state changes. It interacts with `VehicleManager` to update the visual marker and `ComponentInteractionManager` to broadcast events.

**Core Functionality & Flow:**

1.  **Singleton Pattern & Initialization:**
    *   `getInstance(map, config)`: Provides the single instance. Requires map and initial config on first call.
    *   The constructor initializes internal state, `ContextualSpeedController`, and `ComponentInteractionManager`.
    *   `setMap(map)`: Allows updating the map instance post-initialization.

2.  **Contextual Data (`setContextualData(destinations, pois)`):**
    *   Allows providing lists of destinations and POIs that the `AnimationManager` can use for contextual awareness (e.g., in `analyzeContextForPosition`).

3.  **Starting Animation (`async startAnimation(route: RouteData, options: AnimationOptions)`):**
    *   Takes `RouteData` (array of coordinates) and `AnimationOptions` (duration, callbacks, etc.).
    *   **Crucially, it's an `async` function returning `Promise<boolean>` indicating success/failure.**
    *   Resets internal animation state (progress, time, pause state).
    *   Sets `internalState` to `PREPARING`.
    *   Validates the route and map instance.
    *   Awaits `waitForMapStyleLoad()`.
    *   Initializes `vehicleManager` (if not already) and ensures its visibility.
    *   Sets the initial vehicle position to the start of the route.
    *   Sets `internalState` to `RUNNING` (or `COUNTDOWN` if a countdown is implied by options, though this seems unlikely for this manager).
    *   Records start time, resets pause time, and calls `startAnimationLoop()`.
    *   Dispatches `ANIMATION_START` event.
    *   Returns `true` on success, `false` on failure (e.g., invalid route, map not ready).

4.  **Animation Loop (`startAnimationLoop()` -> `animateFrame(timestamp)`):**
    *   Uses `requestAnimationFrame` for the loop.
    *   `animateFrame` is the core update function:
        *   Skips if not animating or paused (but re-requests frame if paused to keep the loop alive).
        *   Calculates `deltaTime` and effective elapsed time considering `totalPausedTime` and `speedMultiplier` from `ContextualSpeedController`.
        *   Calculates current `progress` (0-1) based on `effectiveElapsedTime` and `this.duration`.
        *   Calculates `newPosition` using `calculateCurrentPosition(this.progress)`.
        *   Calculates `currentBearing` using `calculateBearing(previousPosition, newPosition)`.
        *   Calls `updateVehiclePosition(this.currentPosition, this.currentBearing)`.
        *   If `cameraTracking` is enabled, eases the map camera to the `currentPosition` and `currentBearing`.
        *   Calls `progressCallback` (from options) if defined and if progress has changed significantly or enough time has passed.
        *   Dispatches `ANIMATION_PROGRESS` event with detailed info.
        *   If `this.progress >= 1`, calls `completeAnimation()` and returns.
        *   Requests the next animation frame.

5.  **Position & Bearing Calculation:**
    *   `calculateCurrentPosition(progress)`: Performs linear interpolation between points in `this.routePoints` based on `progress`.
    *   `calculateBearing(point1, point2)` (which uses `calculateBearingBetweenPoints`): Calculates bearing between two points.

6.  **Contextual Analysis (`analyzeContextForPosition(position, progress)`):**
    *   Called within `advanceAnimation` (which itself seems to be a more detailed frame update logic, potentially an older or alternative animation step function compared to `animateFrame`).
    *   Determines nearby POIs using `getNearbyPOIs()`.
    *   Determines nearby city using `getNearbyCity()`.
    *   Determines base `terrainType` using `determineTerrainType()`.
    *   Updates `ContextualSpeedController` with `distanceToCity`, `distanceToPOI`, `terrain`, etc.
    *   Dispatches `POI_DISCOVERED` event if a POI comes into proximity.

7.  **Vehicle Update (`updateVehiclePosition(position, bearing)`):**
    *   Calls `this.vehicleManager.updateVehiclePosition()` with position, bearing, and context (terrain, speedMultiplier).

8.  **Pausing/Resuming (`pauseAnimation(isPaused?: boolean)`):**
    *   Toggles `this.isPaused`.
    *   If pausing, records `pausedAtTime` and sets `internalState` to `PAUSED`. Dispatches `ANIMATION_PAUSE`.
    *   If resuming, adds elapsed pause time to `totalPausedTime`, sets `internalState` to `RUNNING`. Dispatches `ANIMATION_RESUME`. Crucially, **restarts the animation frame** by cancelling any existing one and requesting a new one with `animateFrame.bind(this)`.

9.  **Stopping Animation (`stopAnimation()`):**
    *   Calls `cleanupAnimationLoops()` (cancels `animationFrame` and `animationLoopId`).
    *   Clears `healthCheckTimerId`.
    *   Sets `isAnimating` to `false`, `isPaused` to `false`, `internalState` to `IDLE`.
    *   Hides the vehicle using `vehicleManager.hideVehicle()`.
    *   Dispatches `ANIMATION_STOP` event if it was previously running or paused.

10. **Completion (`completeAnimation()`):**
    *   Called when `progress >= 1`.
    *   Positions vehicle at the final point with final bearing.
    *   Sets `internalState` to `COMPLETED`, `animationProgress` to 1.0.
    *   Clears `animationLoopId`.
    *   Dispatches `ANIMATION_COMPLETE` event.

11. **Error Handling & Recovery:**
    *   `attemptAnimationRecovery()`: Tries to recover a stuck animation. Stops current loop, uses `VehicleManager.recoverVehicleVisibility()`, resets some timings, and resumes the loop.
    *   `setupAnimationHealthMonitoring()`: Sets an interval timer to check for stalled position updates or progress. Calls `attemptAnimationRecovery()` if issues are detected.

12. **Event Dispatching (`dispatchAnimationEvent(type, data)`):**
    *   Uses `ComponentInteractionManager.getInstance().emit()` to send out animation events.
    *   Progress events are throttled.

13. **Interfaces & Types:**
    *   Exports `AnimationState` enum, `AnimationManagerInterface`, `RouteData`, `AnimationOptions`, `AnimationProgressInfo`.
    *   Re-exports `AnimationEventType` and `AnimationEventCallback` (which it imports from `../../../types/AnimationEventTypes`).

**Key Dependencies (Imports):**

*   `mapbox-gl`
*   `../types/POITypes` (Destination, PointOfInterest)
*   `../types/Position` (Position, PositionTuple, calculateDistanceUtil, safeToPositionTuple)
*   `./AnimationDebugTools`
*   `./VehicleManager`
*   `./ContextualSpeedController` (TerrainType, POI_DISCOVERY_RADIUS_KM)
*   `./ComponentInteractionManager`
*   `./AnimationDefaults` (DEFAULT_ANIMATION_OPTIONS, FRAME_DELAY_MS, MAX_STYLE_LOAD_WAIT_MS, STYLE_LOAD_CHECK_INTERVAL)
*   `../../../types/AnimationEventTypes` (AnimationEventType, AnimationEventCallback)
*   `@/utils/animationLogger` (AnimationLogger)
*   `@/utils/mapboxgl` (MapboxMap) - *Note: This seems to be a type alias for mapboxgl.Map*

**Modular Files Used:**

*   `VehicleManager`: For all vehicle marker operations.
*   `ContextualSpeedController`: For dynamic speed adjustments.
*   `ComponentInteractionManager`: For broadcasting events to other parts of the application.
*   `AnimationDebugTools`, `AnimationLogger`: For logging.
*   Utility functions from `../types/Position` and `./AnimationDefaults`.
*   Type definitions from `../types/POITypes` and `../../../types/AnimationEventTypes`.

**2024 Rewrite Note:**
- As of the 2024 rewrite, only `AnimationManager`, `VehicleManager`, and `RouteAnimator` are used in the main animation flow.
- `CinematicController`, `AnimationIntegration`, `OptimizedAnimationIntegration`, `AnimationFrameManager`, and `CameraBehavior` are present in the codebase but are not currently integrated into the main animation or UI flow.
- Future work may reintroduce these modules for advanced cinematic camera moves and orchestration.

## VehicleManager (Singleton)

**2024 Rewrite Notice:**
- The vehicle marker system was rewritten from a blank slate. All marker positioning is now controlled by JavaScript using `transform: translate(-50%, -50%) translate3d(x, y, 0) rotate(bearing)`.
- Do **not** use `!important` for `left`, `top`, or `transform` on `.vehicle-marker` in CSS. This will break JS-based positioning.
- The marker is always a child of the map container, with ID `direct-vehicle-marker` and class `vehicle-marker`.
- All marker creation, update, and recovery logic is handled by the singleton `VehicleManager`.
- **Only `AnimationManager`, `VehicleManager`, and `RouteAnimator` are used in the main animation flow.**
- `CinematicController` and related modules are not currently integrated.

> **2024 Update:** All camera/cinematic logic is now handled by [`EnhancedCameraBehavior.ts`](../src/components/map/utils/EnhancedCameraBehavior.ts) and [`CinematicController.ts`](../src/components/map/animation/CinematicController.ts). Do not use `CameraBehavior` or camera helpers in `MapHelpers.ts`. All map transitions must use `bearing: 0` (north-up). The vehicle marker rotates, not the map.