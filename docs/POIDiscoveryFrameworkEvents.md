# POI/City Discovery Framework Event System

## Latest Update (Universal Discovery Framework)

- **Updated POI Categories**: Transitioned from food/restaurant focus to discovery/exploration categories:
  - `landmark`, `nature`, `cultural`, `adventure`, `scenic`, `hidden-gem`, `photography`, `local-experience`, `architecture`, `viewpoint`
- **New Journey Parameters**: Added `TravelPace`, `JourneyStyle`, and enhanced `TravelInterest` types for universal framework
- **Sequential City Selection**: Implemented smart city selection workflow with map zoom and POI filtering
- **Pre-arranged Journeys**: Added comprehensive journey templates with 8 pre-made Morocco experiences
- **Enhanced Itinerary Panel**: Replaced basic sidebar with full-featured itinerary builder supporting custom and pre-arranged journeys
- **Universal Framework Ready**: All components now support multi-client architecture for licensing to tour operators and tourism offices

## Previous Update (HomePage.tsx Refactor)

- Refactored `src/components/HomePage.tsx` to ensure type consistency between `POI` (from destination) and `PointOfInterest` (from poi).
- Added a utility function to convert `POI` to `PointOfInterest` for all region-based POI data.
- Implemented missing handlers: `reorderDestinations`, `handleProceedToVehicle`, and `addToItinerary` using logic from the main HomePage in `pages`.
- Removed unused variables and legacy code (e.g., `getAvailablePOIs`).
- All linter errors related to POI usage, handler presence, and type mismatches are now resolved.
- The code is now robust, production-ready, and easier to maintain for future travel themes and regions.

---

## Purpose

- **Separation of Concerns:** Decouple core discovery logic from UI and theming.
- **Extensibility:** Allow client apps to listen for and react to discovery events in their own way.
- **Strong Typing:** Provide clear, TypeScript-based interfaces for all event types.
- **Framework-Ready:** Support licensing and customization for different travel themes, map areas, and POI needs.

---

## Event System Overview

The event system consists of:
- **Event Types & Interfaces:** Typed definitions for all POI/city discovery events.
- **Registration API:** Functions to register listeners and emit events.
- **Minimal POI Interface:** Extendable for different data needs.

---

## Usage

### 1. Emitting Events (Core Logic)

Import the emitter and use it in your POI/city detection logic:

```typescript
import { emitPOIDiscoveryFrameworkEvent } from 'src/components/map/animation/POIDiscoveryFrameworkEvents';

emitPOIDiscoveryFrameworkEvent({
  type: 'poi-discovered',
  timestamp: Date.now(),
  poi: myPOI,
  position: [lng, lat],
});
```

Supported event types:
- `poi-approaching`
- `poi-discovered`
- `city-approaching`
- `city-entered`
- `batch-poi-discovered`

See the TypeScript interfaces in the file for required fields for each event.

### 2. Listening for Events (Client App/Theme)

Register a listener in your UI or theme code:

```typescript
import { onPOIDiscoveryFrameworkEvent, POIDiscoveryFrameworkEvent } from 'src/components/map/animation/POIDiscoveryFrameworkEvents';

onPOIDiscoveryFrameworkEvent((event: POIDiscoveryFrameworkEvent) => {
  // Show overlay, log analytics, etc.
  if (event.type === 'poi-discovered') {
    // Show POI overlay
  }
});
```

---

## Event Types & Interfaces

```typescript
export type POIDiscoveryEventType =
  | 'poi-approaching'
  | 'poi-discovered'
  | 'city-approaching'
  | 'city-entered'
  | 'batch-poi-discovered';

export interface POI {
  id: string;
  name: string;
  coordinates: { lat: number; lng: number };
  // ...other fields as needed
}

export interface POIApproachingEvent { /* ... */ }
export interface POIDiscoveredEvent { /* ... */ }
export interface CityApproachingEvent { /* ... */ }
export interface CityEnteredEvent { /* ... */ }
export interface BatchPOIDiscoveredEvent { /* ... */ }
```

See the source file for full interface details.

---

## Example: Integrating with a Themeable Overlay

```tsx
import React, { useState, useEffect } from 'react';
import { onPOIDiscoveryFrameworkEvent, POIDiscoveryFrameworkEvent, POI } from 'src/components/map/animation/POIDiscoveryFrameworkEvents';
import { ThemeableDiscoveryOverlay } from 'src/components/ThemeableDiscoveryOverlay';

export const MoroccoDiscoveryUI: React.FC = () => {
  const [activeEvent, setActiveEvent] = useState<POIDiscoveryFrameworkEvent | null>(null);

  const handleSavePOI = (poi: POI) => { /* ... */ };
  const handleSkipPOI = (poi: POI) => { /* ... */ };
  const handleShowDetails = (poi: POI) => { /* ... */ };

  useEffect(() => {
    onPOIDiscoveryFrameworkEvent((event) => {
      setActiveEvent(event);
    });
  }, []);

  return (
    <ThemeableDiscoveryOverlay
      event={activeEvent}
      onClose={() => setActiveEvent(null)}
      onSavePOI={handleSavePOI}
      onSkipPOI={handleSkipPOI}
      onShowDetails={handleShowDetails}
      theme={{ background: '#fffbe6', accentColor: '#b83227' }}
    />
  );
};
```

---

## Best Practices

- **Keep the event system as the single source of truth** for all POI/city discovery events.
- **Extend the POI interface** as needed for your vertical (wine tours, cultural, etc.).
- **Use the event system for analytics, overlays, notifications, and custom logic.**
- **Theme overlays and UI components** via props or context for each client.
- **Document any custom event types or extensions** in your own client docs.

---

## File Location

- Event system: `src/components/map/animation/POIDiscoveryFrameworkEvents.ts`
- Overlay example: `src/components/ThemeableDiscoveryOverlay.tsx`
- This documentation: `docs/POIDiscoveryFrameworkEvents.md`

---

For more advanced usage, see the overlay component and integration examples in the codebase. 