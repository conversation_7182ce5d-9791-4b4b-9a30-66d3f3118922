import React, { useEffect, useState } from 'react';
import { POIDiscoveryFrameworkEvent, POI } from '../../src/components/map/animation/POIDiscoveryFrameworkEvents';

interface ThemeableDiscoveryOverlayProps {
  event: POIDiscoveryFrameworkEvent | null;
  onClose: () => void;
  onSavePOI?: (poi: POI) => void;
  onSkipPOI?: (poi: POI) => void;
  onShowDetails?: (poi: POI) => void;
  theme?: {
    background?: string;
    accentColor?: string;
    fontFamily?: string;
    buttonStyle?: React.CSSProperties;
  };
}

const defaultButtonStyle: React.CSSProperties = {
  padding: '6px 14px',
  borderRadius: 6,
  border: 'none',
  color: '#fff',
  fontWeight: 'bold',
  cursor: 'pointer',
  fontSize: 15,
};

export const MoroccoAtlasOverlay: React.FC<ThemeableDiscoveryOverlayProps> = ({
  event,
  onClose,
  onSavePOI,
  onSkipPOI,
  onShowDetails,
  theme = {},
}) => {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    setVisible(!!event);
  }, [event]);

  const overlayStyle: React.CSSProperties = {
    position: 'fixed',
    top: 80,
    right: 20,
    background: theme.background || '#f8f0e3', // Moroccan sand
    borderRadius: 12,
    boxShadow: '0 4px 20px rgba(180,120,40,0.15)',
    zIndex: 2000,
    padding: 24,
    fontFamily: theme.fontFamily || 'Montserrat, sans-serif',
    color: theme.accentColor || '#e27d60', // Moroccan terracotta
    minWidth: 320,
    maxWidth: 400,
    opacity: visible ? 1 : 0,
    transform: visible ? 'translateY(0)' : 'translateY(-20px)',
    transition: 'opacity 0.4s, transform 0.4s',
    pointerEvents: visible ? 'auto' : 'none',
  };

  if (!event) return null;

  const renderPOIActions = (poi: POI) => (
    <div style={{ marginTop: 12, display: 'flex', gap: 8 }}>
      {onSavePOI && (
        <button
          style={{ ...defaultButtonStyle, ...theme.buttonStyle, background: '#e27d60' }}
          onClick={() => onSavePOI(poi)}
        >
          Add to Journey
        </button>
      )}
      {onShowDetails && (
        <button
          style={{ ...defaultButtonStyle, ...theme.buttonStyle, background: '#2a9d8f' }}
          onClick={() => onShowDetails(poi)}
        >
          Explore Details
        </button>
      )}
      {onSkipPOI && (
        <button
          style={{ ...defaultButtonStyle, ...theme.buttonStyle, background: '#f2c94c' }}
          onClick={() => onSkipPOI(poi)}
        >
          Skip
        </button>
      )}
    </div>
  );

  let content: React.ReactNode = null;
  switch (event.type) {
    case 'poi-approaching':
      content = (
        <>
          <h3 style={{ margin: 0 }}>Approaching: {event.poi.name}</h3>
          <p style={{ margin: '8px 0' }}>Distance: {event.distance.toFixed(1)} km</p>
          {renderPOIActions(event.poi)}
        </>
      );
      break;
    case 'poi-discovered':
      content = (
        <>
          <h3 style={{ margin: 0 }}>Discovered: {event.poi.name}</h3>
          <p style={{ margin: '8px 0' }}>{event.poi.description}</p>
          {renderPOIActions(event.poi)}
          <button style={{ ...defaultButtonStyle, marginTop: 12 }} onClick={onClose}>Close</button>
        </>
      );
      break;
    case 'city-approaching':
      content = (
        <>
          <h3 style={{ margin: 0 }}>Approaching {event.city}</h3>
          <p style={{ margin: '8px 0' }}>Distance: {event.distance.toFixed(1)} km</p>
          <button style={{ ...defaultButtonStyle, marginTop: 12 }} onClick={onClose}>Close</button>
        </>
      );
      break;
    case 'city-entered':
      content = (
        <>
          <h3 style={{ margin: 0 }}>Welcome to {event.city}</h3>
          <button style={{ ...defaultButtonStyle, marginTop: 12 }} onClick={onClose}>Close</button>
        </>
      );
      break;
    case 'batch-poi-discovered':
      content = (
        <>
          <h3 style={{ margin: 0 }}>Atlas Mountains Highlights</h3>
          <ul style={{ margin: '8px 0', paddingLeft: 20 }}>
            {event.pois.map((poi) => (
              <li key={poi.id}>
                <span>{poi.name}</span>
                {renderPOIActions(poi)}
              </li>
            ))}
          </ul>
          <button style={{ ...defaultButtonStyle, marginTop: 12 }} onClick={onClose}>Close</button>
        </>
      );
      break;
    default:
      content = null;
  }

  return <div style={overlayStyle}>{content}</div>;
};

// Usage: Import and use <MoroccoAtlasOverlay ... /> in your Morocco Atlas Mountains tour app.