/**
 * Vehicle Animation Example
 * 
 * This is a simplified example demonstrating the core concepts of vehicle animation
 * on a map using Mapbox GL. It doesn't depend on the specific project implementations.
 */

import mapboxgl from 'mapbox-gl';

// Types
type Position = [number, number]; // [longitude, latitude]
type TerrainType = 'default' | 'city' | 'mountain' | 'desert' | 'coastal';

class VehicleAnimationExample {
  private map: mapboxgl.Map;
  private vehicleMarker: HTMLElement | null = null;
  private vehicleContainer: HTMLElement | null = null;
  private isAnimating: boolean = false;
  private route: Position[] = [];
  private currentRouteIndex: number = 0;
  private animationFrameId: number | null = null;
  
  constructor(containerId: string) {
    // Initialize the map
    this.map = new mapboxgl.Map({
      container: containerId,
      style: 'mapbox://styles/mapbox/streets-v11',
      center: [-5.0, 31.0], // Center on Morocco
      zoom: 6,
      pitch: 45,
      bearing: 0
    });
    
    // Create sample route through Morocco
    this.route = [
      [-5.833954, 35.759465], // Tangier
      [-5.36878, 35.569643],  // Tetouan
      [-5.26795, 35.171704],  // Chefchaouen
      [-4.9998, 34.0531],     // Fez
      [-6.849813, 33.971588], // Rabat
      [-7.589843, 33.573109], // Casablanca
      [-8.008889, 31.630561], // Marrakech
      [-9.598107, 30.427755], // Agadir
      [-5.836954, 29.6585]    // Near Zagora (desert)
    ];
    
    // Set up map event handlers
    this.map.on('load', this.handleMapLoad);
    
    // Set up UI controls
    this.setupControls();
  }
  
  /**
   * Handle map load event
   */
  private handleMapLoad = () => {
    // Create a container for the vehicle marker
    this.createVehicleMarkerContainer();
    
    // Create the vehicle marker
    this.createVehicleMarker();
    
    // Add the route to the map
    this.addRouteToMap();
    
    // Position vehicle at start of route
    if (this.route.length > 0) {
      this.updateVehiclePosition(this.route[0], 0);
    }
  }
  
  /**
   * Create container for vehicle marker
   */
  private createVehicleMarkerContainer = () => {
    // Get the map container
    const mapContainer = this.map.getContainer();
    
    // Create a container for the marker
    this.vehicleContainer = document.createElement('div');
    this.vehicleContainer.className = 'vehicle-container';
    this.vehicleContainer.style.position = 'absolute';
    this.vehicleContainer.style.top = '0';
    this.vehicleContainer.style.left = '0';
    this.vehicleContainer.style.width = '100%';
    this.vehicleContainer.style.height = '100%';
    this.vehicleContainer.style.pointerEvents = 'none';
    this.vehicleContainer.style.zIndex = '10';
    
    // Add to map container
    mapContainer.appendChild(this.vehicleContainer);
  }
  
  /**
   * Create the vehicle marker element
   */
  private createVehicleMarker = () => {
    if (!this.vehicleContainer) return;
    
    // Create marker element
    this.vehicleMarker = document.createElement('div');
    this.vehicleMarker.id = 'vehicle-marker';
    this.vehicleMarker.className = 'vehicle-marker';
    this.vehicleMarker.style.position = 'absolute';
    this.vehicleMarker.style.width = '24px';
    this.vehicleMarker.style.height = '24px';
    this.vehicleMarker.style.marginLeft = '-12px';
    this.vehicleMarker.style.marginTop = '-12px';
    this.vehicleMarker.style.borderRadius = '50%';
    this.vehicleMarker.style.backgroundColor = 'red';
    this.vehicleMarker.style.border = '2px solid white';
    this.vehicleMarker.style.transform = 'translate(-50%, -50%)';
    this.vehicleMarker.style.transition = 'transform 0.2s ease-out';
    
    // Create directional indicator
    const direction = document.createElement('div');
    direction.style.position = 'absolute';
    direction.style.width = '0';
    direction.style.height = '0';
    direction.style.borderLeft = '8px solid transparent';
    direction.style.borderRight = '8px solid transparent';
    direction.style.borderBottom = '16px solid white';
    direction.style.top = '-10px';
    direction.style.left = '4px';
    
    this.vehicleMarker.appendChild(direction);
    this.vehicleContainer.appendChild(this.vehicleMarker);
  }
  
  /**
   * Add the route as a line to the map
   */
  private addRouteToMap = () => {
    if (this.route.length === 0) return;
    
    // Add a source for the route
    this.map.addSource('route', {
      type: 'geojson',
      data: {
        type: 'Feature',
        properties: {},
        geometry: {
          type: 'LineString',
          coordinates: this.route
        }
      }
    });
    
    // Add a line layer for the route
    this.map.addLayer({
      id: 'route',
      type: 'line',
      source: 'route',
      layout: {
        'line-join': 'round',
        'line-cap': 'round'
      },
      paint: {
        'line-color': '#3388ff',
        'line-width': 8,
        'line-opacity': 0.8
      }
    });
  }
  
  /**
   * Set up UI controls
   */
  private setupControls = () => {
    // Create control container
    const controlContainer = document.createElement('div');
    controlContainer.className = 'map-controls';
    controlContainer.style.position = 'absolute';
    controlContainer.style.bottom = '20px';
    controlContainer.style.left = '20px';
    controlContainer.style.zIndex = '20';
    controlContainer.style.backgroundColor = 'white';
    controlContainer.style.padding = '10px';
    controlContainer.style.borderRadius = '4px';
    controlContainer.style.boxShadow = '0 2px 6px rgba(0,0,0,0.3)';
    
    // Create play button
    const playButton = document.createElement('button');
    playButton.id = 'play-button';
    playButton.textContent = 'Play';
    playButton.style.marginRight = '10px';
    playButton.addEventListener('click', () => {
      if (this.isAnimating) {
        this.pauseAnimation();
        playButton.textContent = 'Play';
      } else {
        this.startAnimation();
        playButton.textContent = 'Pause';
      }
    });
    
    // Create reset button
    const resetButton = document.createElement('button');
    resetButton.id = 'reset-button';
    resetButton.textContent = 'Reset';
    resetButton.addEventListener('click', () => {
      this.resetAnimation();
      playButton.textContent = 'Play';
    });
    
    // Add buttons to control container
    controlContainer.appendChild(playButton);
    controlContainer.appendChild(resetButton);
    
    // Add control container to map
    this.map.getContainer().appendChild(controlContainer);
  }
  
  /**
   * Update the vehicle position on the map
   */
  private updateVehiclePosition = (position: Position, bearing: number) => {
    if (!this.vehicleMarker || !this.map) return;
    
    // Convert position to pixel coordinates
    const pixelPosition = this.map.project(position as mapboxgl.LngLatLike);
    
    // Update marker position
    this.vehicleMarker.style.left = `${pixelPosition.x}px`;
    this.vehicleMarker.style.top = `${pixelPosition.y}px`;
    
    // Update marker rotation
    this.vehicleMarker.style.transform = `translate(-50%, -50%) rotate(${bearing}deg)`;
    
    // Update terrain appearance
    const terrain = this.determineTerrainType(position);
    this.updateVehicleAppearance(terrain);
    
    // Update camera to follow vehicle
    this.map.easeTo({
      center: position,
      bearing: bearing,
      duration: 1000
    });
  }
  
  /**
   * Update vehicle appearance based on terrain
   */
  private updateVehicleAppearance = (terrain: TerrainType) => {
    if (!this.vehicleMarker) return;
    
    // Update vehicle color based on terrain
    switch (terrain) {
      case 'city':
        this.vehicleMarker.style.backgroundColor = '#ff4500'; // Orange-red
        break;
      case 'mountain':
        this.vehicleMarker.style.backgroundColor = '#8a2be2'; // Purple
        break;
      case 'desert':
        this.vehicleMarker.style.backgroundColor = '#ffd700'; // Gold
        break;
      case 'coastal':
        this.vehicleMarker.style.backgroundColor = '#00bfff'; // Blue
        break;
      default:
        this.vehicleMarker.style.backgroundColor = '#ff0000'; // Red
    }
  }
  
  /**
   * Determine terrain type based on position
   */
  private determineTerrainType = (position: Position): TerrainType => {
    // For this example, we'll use a simple approximation based on coordinates
    const [lng, lat] = position;
    
    // Near the coast (approximate)
    if (lng < -9 && lat > 30) return 'coastal';
    
    // Mountains (approximate Atlas mountains)
    if (lat > 30 && lat < 33 && lng > -8 && lng < -5) return 'mountain';
    
    // Desert (southern regions)
    if (lat < 30) return 'desert';
    
    // Default to city for major locations
    if (
      (lng > -6 && lng < -5.5 && lat > 33.5 && lat < 34) || // Rabat
      (lng > -7.7 && lng < -7.5 && lat > 33.5 && lat < 33.7) // Casablanca
    ) {
      return 'city';
    }
    
    // Default
    return 'default';
  }
  
  /**
   * Start the animation
   */
  private startAnimation = () => {
    if (this.isAnimating || this.route.length < 2) return;
    
    this.isAnimating = true;
    this.animateAlongRoute();
  }
  
  /**
   * Animate the vehicle along the route
   */
  private animateAlongRoute = () => {
    // Calculate time based on distance and desired speed
    const duration = 2000; // ms per segment
    const startTime = performance.now();
    const startIndex = this.currentRouteIndex;
    const endIndex = (startIndex + 1) % this.route.length;
    
    const startPosition = this.route[startIndex];
    const endPosition = this.route[endIndex];
    
    // Calculate bearing (direction)
    const bearing = this.calculateBearing(startPosition, endPosition);
    
    // Animation frame loop
    const animate = (timestamp: number) => {
      const elapsed = timestamp - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // Interpolate position
      const currentPosition: Position = [
        startPosition[0] + (endPosition[0] - startPosition[0]) * progress,
        startPosition[1] + (endPosition[1] - startPosition[1]) * progress
      ];
      
      // Update vehicle position
      this.updateVehiclePosition(currentPosition, bearing);
      
      // Continue animation if not complete
      if (progress < 1) {
        this.animationFrameId = requestAnimationFrame(animate);
      } else {
        // Move to next segment
        this.currentRouteIndex = endIndex;
        
        // If we haven't reached the end, continue to next segment
        if (this.isAnimating) {
          this.animateAlongRoute();
        }
      }
    };
    
    // Start animation loop
    this.animationFrameId = requestAnimationFrame(animate);
  }
  
  /**
   * Pause the animation
   */
  private pauseAnimation = () => {
    this.isAnimating = false;
    
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }
  
  /**
   * Reset the animation to the beginning
   */
  private resetAnimation = () => {
    this.pauseAnimation();
    this.currentRouteIndex = 0;
    
    if (this.route.length > 0) {
      this.updateVehiclePosition(this.route[0], 0);
    }
  }
  
  /**
   * Calculate bearing between two points
   */
  private calculateBearing = (start: Position, end: Position): number => {
    const startLat = start[1] * Math.PI / 180;
    const startLng = start[0] * Math.PI / 180;
    const endLat = end[1] * Math.PI / 180;
    const endLng = end[0] * Math.PI / 180;
    
    const y = Math.sin(endLng - startLng) * Math.cos(endLat);
    const x = Math.cos(startLat) * Math.sin(endLat) -
              Math.sin(startLat) * Math.cos(endLat) * Math.cos(endLng - startLng);
    
    const bearing = Math.atan2(y, x) * 180 / Math.PI;
    return (bearing + 360) % 360;
  }
  
  /**
   * Clean up resources
   */
  public dispose = () => {
    this.pauseAnimation();
    
    // Remove vehicle marker
    if (this.vehicleMarker && this.vehicleContainer) {
      this.vehicleContainer.removeChild(this.vehicleMarker);
    }
    
    // Remove vehicle container
    const mapContainer = this.map.getContainer();
    if (this.vehicleContainer && mapContainer) {
      mapContainer.removeChild(this.vehicleContainer);
    }
    
    // Clean up map
    this.map.remove();
  }
}

// Example usage:
// const animationExample = new VehicleAnimationExample('map-container');
export default VehicleAnimationExample;