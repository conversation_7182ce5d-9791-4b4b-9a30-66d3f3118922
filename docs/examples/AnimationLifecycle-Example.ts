/**
 * Animation Lifecycle Example
 * 
 * This example demonstrates how to use the AnimationLifecycleManager
 * to create a streamlined animation with proper lifecycle management.
 */

import mapboxgl from 'mapbox-gl';
import AnimationLifecycleManager from '../../src/components/map/animation/AnimationLifecycleManager';
import VehicleController from '../../src/components/map/vehicle/VehicleController';
import { Position } from '../../src/types/Position';
import { AnimationEventType } from '../../src/types/AnimationEventTypes';
import { AnimationPhase } from '../../src/types/AnimationTypes';

class AnimationLifecycleExample {
  private map: mapboxgl.Map;
  private lifecycleManager = AnimationLifecycleManager;
  private vehicleController = VehicleController;
  
  // UI elements
  private progressBar: HTMLElement | null = null;
  private statusText: HTMLElement | null = null;
  private controlButtons: {[key: string]: HTMLButtonElement} = {};
  
  constructor(containerId: string) {
    // Initialize the map
    this.map = new mapboxgl.Map({
      container: containerId,
      style: 'mapbox://styles/mapbox/streets-v11',
      center: [-5.0, 31.0], // Center on Morocco
      zoom: 6,
      pitch: 45,
      bearing: 0
    });
    
    // Setup event handlers
    this.map.on('load', this.handleMapLoad);
    
    // Setup UI
    this.setupUI();
  }
  
  /**
   * Handle map load event
   */
  private handleMapLoad = async () => {
    try {
      // Initialize the lifecycle manager
      const success = await this.lifecycleManager.initialize(this.map);
      this.updateStatus('Initialized');
      
      // Initialize vehicle controller
      await this.vehicleController.initialize(this.map);
      
      // Register components with lifecycle manager
      this.lifecycleManager.registerComponent('vehicle-controller');
      
      // Report components ready
      this.lifecycleManager.reportComponentReady('vehicle-controller');
      
      // Add route to map
      this.addRouteToMap();
      
      // Setup event listeners
      this.setupEventListeners();
      
      // Enable controls
      this.enableControls(['prepare']);
      
    } catch (error) {
      console.error('Error initializing animation:', error);
      this.updateStatus('Error: ' + (error as Error).message);
    }
  }
  
  /**
   * Add sample route to the map
   */
  private addRouteToMap() {
    const route = this.getSampleRoute();
    
    // Add source for route
    this.map.addSource('route', {
      type: 'geojson',
      data: {
        type: 'Feature',
        properties: {},
        geometry: {
          type: 'LineString',
          coordinates: route
        }
      }
    });
    
    // Add route layer
    this.map.addLayer({
      id: 'route',
      type: 'line',
      source: 'route',
      layout: {
        'line-join': 'round',
        'line-cap': 'round'
      },
      paint: {
        'line-color': '#3388ff',
        'line-width': 8,
        'line-opacity': 0.8
      }
    });
  }
  
  /**
   * Get a sample route through Morocco
   */
  private getSampleRoute(): Position[] {
    return [
      [-5.833954, 35.759465], // Tangier
      [-5.36878, 35.569643],  // Tetouan
      [-5.26795, 35.171704],  // Chefchaouen
      [-4.9998, 34.0531],     // Fez
      [-6.849813, 33.971588], // Rabat
      [-7.589843, 33.573109], // Casablanca
      [-8.008889, 31.630561], // Marrakech
      [-9.598107, 30.427755], // Agadir
      [-5.836954, 29.6585]    // Near Zagora (desert)
    ];
  }
  
  /**
   * Setup event listeners for animation lifecycle events
   */
  private setupEventListeners() {
    // Listen for progress updates
    this.lifecycleManager.addEventListener(
      AnimationEventType.PROGRESS_UPDATE,
      (event) => {
        if ('progress' in event) {
          const progress = event.progress as number;
          this.updateProgressBar(progress);
          
          // Update vehicle position if we have route data
          if ('position' in event) {
            const position = event.position as Position;
            const bearing = ('bearing' in event) ? event.bearing as number : 0;
            this.vehicleController.smoothUpdatePosition(position, bearing);
          }
        }
      }
    );
    
    // Listen for phase changes
    this.lifecycleManager.addEventListener(
      AnimationEventType.STATE_CHANGE,
      (event) => {
        if ('phase' in event) {
          this.updateStatus(`Phase: ${event.phase}`);
          this.updateControlButtons(event.phase as string);
        }
      }
    );
    
    // Listen for animation completion
    this.lifecycleManager.addEventListener(
      AnimationEventType.COMPLETE,
      () => {
        this.updateStatus('Animation completed');
        this.enableControls(['prepare', 'reset']);
      }
    );
  }
  
  /**
   * Setup UI elements
   */
  private setupUI() {
    // Create container
    const container = document.createElement('div');
    container.className = 'animation-controls';
    container.style.position = 'absolute';
    container.style.bottom = '20px';
    container.style.left = '20px';
    container.style.padding = '10px';
    container.style.backgroundColor = 'white';
    container.style.borderRadius = '4px';
    container.style.boxShadow = '0 0 10px rgba(0,0,0,0.3)';
    container.style.zIndex = '10';
    container.style.width = '300px';
    
    // Create status text
    this.statusText = document.createElement('div');
    this.statusText.className = 'status';
    this.statusText.style.marginBottom = '10px';
    this.statusText.textContent = 'Initializing...';
    container.appendChild(this.statusText);
    
    // Create progress bar container
    const progressContainer = document.createElement('div');
    progressContainer.className = 'progress-container';
    progressContainer.style.height = '20px';
    progressContainer.style.backgroundColor = '#f0f0f0';
    progressContainer.style.borderRadius = '10px';
    progressContainer.style.overflow = 'hidden';
    progressContainer.style.marginBottom = '10px';
    
    // Create progress bar
    this.progressBar = document.createElement('div');
    this.progressBar.className = 'progress';
    this.progressBar.style.height = '100%';
    this.progressBar.style.width = '0%';
    this.progressBar.style.backgroundColor = '#4CAF50';
    this.progressBar.style.transition = 'width 0.2s ease';
    progressContainer.appendChild(this.progressBar);
    container.appendChild(progressContainer);
    
    // Create buttons container
    const buttonsContainer = document.createElement('div');
    buttonsContainer.className = 'buttons';
    buttonsContainer.style.display = 'flex';
    buttonsContainer.style.gap = '5px';
    
    // Create control buttons
    const buttonConfig = [
      { id: 'prepare', text: 'Prepare', action: this.prepareAnimation },
      { id: 'start', text: 'Start', action: this.startAnimation },
      { id: 'pause', text: 'Pause', action: this.pauseAnimation },
      { id: 'resume', text: 'Resume', action: this.resumeAnimation },
      { id: 'stop', text: 'Stop', action: this.stopAnimation },
      { id: 'reset', text: 'Reset', action: this.resetAnimation }
    ];
    
    // Add buttons
    for (const config of buttonConfig) {
      const button = document.createElement('button');
      button.id = `${config.id}-button`;
      button.textContent = config.text;
      button.style.flex = '1';
      button.style.padding = '8px';
      button.style.border = 'none';
      button.style.borderRadius = '4px';
      button.style.backgroundColor = '#3388ff';
      button.style.color = 'white';
      button.style.cursor = 'pointer';
      button.disabled = true;
      button.addEventListener('click', config.action);
      
      buttonsContainer.appendChild(button);
      this.controlButtons[config.id] = button;
    }
    
    container.appendChild(buttonsContainer);
    
    // Add to document
    document.body.appendChild(container);
  }
  
  /**
   * Update the progress bar
   */
  private updateProgressBar(progress: number) {
    if (this.progressBar) {
      this.progressBar.style.width = `${progress * 100}%`;
    }
  }
  
  /**
   * Update status text
   */
  private updateStatus(status: string) {
    if (this.statusText) {
      this.statusText.textContent = status;
    }
  }
  
  /**
   * Enable specific control buttons
   */
  private enableControls(buttons: string[]) {
    // First disable all buttons
    for (const buttonId in this.controlButtons) {
      this.controlButtons[buttonId].disabled = true;
    }
    
    // Then enable specified buttons
    for (const buttonId of buttons) {
      if (buttonId in this.controlButtons) {
        this.controlButtons[buttonId].disabled = false;
      }
    }
  }
  
  /**
   * Update control buttons based on animation phase
   */
  private updateControlButtons(phase: string) {
    switch (phase) {
      case 'not_started':
      case 'initialized':
        this.enableControls(['prepare']);
        break;
      case 'prepared':
        this.enableControls(['start', 'reset']);
        break;
      case 'playing':
        this.enableControls(['pause', 'stop']);
        break;
      case 'paused':
        this.enableControls(['resume', 'stop', 'reset']);
        break;
      case 'stopped':
      case 'completed':
        this.enableControls(['prepare', 'reset']);
        break;
      default:
        this.enableControls(['reset']);
    }
  }
  
  /**
   * Prepare the animation
   */
  private prepareAnimation = async () => {
    const route = this.getSampleRoute();
    const duration = 30000; // 30 seconds
    
    try {
      const success = await this.lifecycleManager.prepareAnimation(route, duration);
      
      if (success) {
        this.updateStatus('Animation prepared');
        
        // Position vehicle at start of route
        if (route.length > 0) {
          this.vehicleController.setVisible(true);
          this.vehicleController.setPosition(route[0]);
        }
        
        // Focus map on start of route
        this.map.flyTo({
          center: [route[0][0], route[0][1]],
          zoom: 10,
          duration: 1000
        });
        
        this.enableControls(['start', 'reset']);
      } else {
        this.updateStatus('Failed to prepare animation');
      }
    } catch (error) {
      console.error('Error preparing animation:', error);
      this.updateStatus('Error preparing animation');
    }
  }
  
  /**
   * Start the animation
   */
  private startAnimation = async () => {
    try {
      const success = await this.lifecycleManager.startAnimation();
      
      if (success) {
        this.updateStatus('Animation started');
      } else {
        this.updateStatus('Failed to start animation');
      }
    } catch (error) {
      console.error('Error starting animation:', error);
      this.updateStatus('Error starting animation');
    }
  }
  
  /**
   * Pause the animation
   */
  private pauseAnimation = async () => {
    try {
      const success = await this.lifecycleManager.pauseAnimation();
      
      if (success) {
        this.updateStatus('Animation paused');
      } else {
        this.updateStatus('Failed to pause animation');
      }
    } catch (error) {
      console.error('Error pausing animation:', error);
      this.updateStatus('Error pausing animation');
    }
  }
  
  /**
   * Resume the animation
   */
  private resumeAnimation = async () => {
    try {
      const success = await this.lifecycleManager.resumeAnimation();
      
      if (success) {
        this.updateStatus('Animation resumed');
      } else {
        this.updateStatus('Failed to resume animation');
      }
    } catch (error) {
      console.error('Error resuming animation:', error);
      this.updateStatus('Error resuming animation');
    }
  }
  
  /**
   * Stop the animation
   */
  private stopAnimation = async () => {
    try {
      const success = await this.lifecycleManager.stopAnimation();
      
      if (success) {
        this.updateStatus('Animation stopped');
      } else {
        this.updateStatus('Failed to stop animation');
      }
    } catch (error) {
      console.error('Error stopping animation:', error);
      this.updateStatus('Error stopping animation');
    }
  }
  
  /**
   * Reset the animation
   */
  private resetAnimation = async () => {
    try {
      const success = await this.lifecycleManager.resetAnimation();
      
      if (success) {
        this.updateStatus('Animation reset');
        this.updateProgressBar(0);
        
        // Reset vehicle
        this.vehicleController.reset();
        
        this.enableControls(['prepare']);
      } else {
        this.updateStatus('Failed to reset animation');
      }
    } catch (error) {
      console.error('Error resetting animation:', error);
      this.updateStatus('Error resetting animation');
    }
  }
  
  /**
   * Clean up resources
   */
  public dispose() {
    this.lifecycleManager.dispose();
    this.vehicleController.dispose();
    this.map.remove();
  }
}

// Example usage:
// const animationExample = new AnimationLifecycleExample('map-container');
export default AnimationLifecycleExample; 