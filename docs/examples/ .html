<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vehicle Animation Example</title>
  <link href="https://api.mapbox.com/mapbox-gl-js/v2.14.1/mapbox-gl.css" rel="stylesheet">
  <script src="https://api.mapbox.com/mapbox-gl-js/v2.14.1/mapbox-gl.js"></script>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }
    
    .container {
      display: flex;
      flex-direction: column;
      height: 100vh;
    }
    
    .map-container {
      flex: 1;
      position: relative;
    }
    
    #map-container {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 100%;
    }
    
    .controls {
      background: #fff;
      padding: 20px;
      border-top: 1px solid #ddd;
    }
    
    .button-group {
      display: flex;
      gap: 10px;
      margin-bottom: 15px;
    }
    
    button {
      padding: 8px 16px;
      background: #3388ff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
    }
    
    button:hover {
      background: #2266dd;
    }
    
    .slider-container {
      margin-bottom: 15px;
    }
    
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
    }
    
    input[type="range"] {
      width: 100%;
    }
    
    .info-panel {
      margin-top: 10px;
      padding: 10px;
      background: #f5f5f5;
      border-radius: 4px;
    }
    
    .progress-container {
      height: 10px;
      background: #eee;
      border-radius: 5px;
      overflow: hidden;
      margin: 10px 0;
    }
    
    #progress-bar {
      height: 100%;
      background: #3388ff;
      width: 0%;
      transition: width 0.2s ease;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="map-container">
      <div id="map-container"></div>
    </div>
    
    <div class="controls">
      <div class="button-group">
        <button id="play-button">Play</button>
        <button id="reset-button">Reset</button>
      </div>
      
      <div class="slider-container">
        <label for="smoothing-slider">Animation Smoothing: <span id="smoothing-value">0.5</span></label>
        <input type="range" id="smoothing-slider" min="0" max="1" step="0.1" value="0.5">
      </div>
      
      <div class="progress-container">
        <div id="progress-bar"></div>
      </div>
      
      <div class="info-panel">
        <div id="position-display">Position: [0.0000, 0.0000], Bearing: 0.0°</div>
        <div id="terrain-display">Terrain: Default</div>
      </div>
    </div>
  </div>
  
  <!-- This script tag would reference the bundled version of your TypeScript file -->
  <script>
    // This would be replaced with the actual import in a real application
    // For demonstration, we're showing what would happen
    
    document.getElementById('smoothing-slider').addEventListener('input', function() {
      document.getElementById('smoothing-value').textContent = this.value;
    });
    
    // Mock Mapbox token (in a real app, use your own token)
    mapboxgl.accessToken = 'YOUR_MAPBOX_ACCESS_TOKEN';
    
    // The VehicleAnimationExample class would be instantiated here
    // in a real implementation that has bundled the TypeScript
    console.log('In a real implementation, VehicleAnimationExample would be initialized here');
  </script>
</body>
</html>