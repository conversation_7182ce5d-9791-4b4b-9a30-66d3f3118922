# Service Worker & Routing Issues - Fix Documentation

## Issues Identified

### 1. 404 Error on First Load of `/moroccan-demo`
**Problem**: Service worker was caching old `index.html` and serving stale content on first load. Only after force refresh would the new version load.

**Root Cause**: 
- VitePWA plugin was generating a service worker that aggressively cached the index.html
- Old service worker in `public/sw.js` was conflicting with VitePWA-generated service worker
- Service worker was serving cached version before React Router could handle the route

### 2. Vite Dependency Optimization Warnings
**Problem**: Missing chunk files in Vite's dependency optimization cache.
```
The file does not exist at "/Users/<USER>/cometomorocco/node_modules/.vite/deps/chunk-OHWCMTQP.js?v=5fb90538"
The file does not exist at "/Users/<USER>/cometomorocco/node_modules/.vite/deps/chunk-4FOOX3DN.js?v=5fb90538"
```

**Root Cause**: Vite's dependency pre-bundling was creating chunks that weren't properly resolved.

## Solutions Implemented

### 1. Service Worker Configuration Fix

**Updated `vite.config.ts`:**
```typescript
VitePWA({
  registerType: 'autoUpdate',
  workbox: {
    maximumFileSizeToCacheInBytes: 5 * 1024 * 1024, // 5 MB
    // Skip waiting and claim clients immediately for faster updates
    skipWaiting: true,
    clientsClaim: true,
    // Don't cache the index.html to avoid routing issues
    navigateFallback: null,
    // More aggressive cache invalidation
    cleanupOutdatedCaches: true,
    // Exclude problematic routes from caching
    navigateFallbackDenylist: [/^\/moroccan-demo/, /^\/api/],
    runtimeCaching: [
      {
        urlPattern: /^https:\/\/api\.mapbox\.com\/.*/i,
        handler: 'CacheFirst',
        options: {
          cacheName: 'mapbox-cache',
          expiration: {
            maxEntries: 10,
            maxAgeSeconds: 60 * 60 * 24 * 30, // 30 days
          },
        },
      },
    ],
  }
})
```

**Key Changes:**
- `navigateFallback: null` - Prevents service worker from intercepting navigation
- `skipWaiting: true` - Forces immediate activation of new service worker
- `clientsClaim: true` - Takes control of all clients immediately
- `cleanupOutdatedCaches: true` - Removes old caches automatically
- `navigateFallbackDenylist` - Excludes specific routes from caching

### 2. Dependency Optimization Fix

**Updated `vite.config.ts` optimizeDeps:**
```typescript
optimizeDeps: {
  exclude: [
    "chunk-T2WPM7RV",
    "chunk-EURAPOLQ",
    "chunk-OHWCMTQP",
    "chunk-4FOOX3DN"
  ],
  // Force include common dependencies to avoid chunk issues
  include: [
    'react',
    'react-dom',
    'react-router-dom',
    'mapbox-gl',
    '@tanstack/react-query'
  ]
},
```

**Key Changes:**
- Added problematic chunks to exclude list
- Force included common dependencies to prevent chunk issues
- Ensures stable dependency resolution

### 3. Development Cache Clearing

**Updated `src/main.tsx`:**
```typescript
// Clear service worker cache and unregister for development
if ('serviceWorker' in navigator && import.meta.env.DEV) {
  navigator.serviceWorker.getRegistrations().then(function(registrations) {
    for(let registration of registrations) {
      registration.unregister()
        .then(function() { console.log('Service Worker UNREGISTERED:', registration.scope); })
        .catch(function(err) { console.error('Service Worker UNREGISTRATION FAILED:', err); });
    }
  });
  
  // Clear all caches in development
  if ('caches' in window) {
    caches.keys().then(function(names) {
      for (let name of names) {
        caches.delete(name);
      }
    });
  }
}
```

**Key Changes:**
- Only clears service workers in development mode
- Clears all browser caches in development
- Prevents caching issues during development

### 4. Removed Conflicting Service Worker

**Action**: Removed `public/sw.js` file that was conflicting with VitePWA-generated service worker.

## Testing the Fix

### Before Fix:
1. Navigate to `/moroccan-demo` → 404 error
2. Force refresh (Cmd+Shift+R) → Page loads correctly
3. Console shows service worker caching old content

### After Fix:
1. Navigate to `/moroccan-demo` → Page loads correctly immediately
2. No 404 errors on first load
3. Service worker properly handles routing
4. No Vite chunk warnings

## Prevention Measures

### 1. Service Worker Best Practices
- Always use `navigateFallback: null` for SPA routing
- Configure `navigateFallbackDenylist` for API routes
- Use `skipWaiting` and `clientsClaim` for immediate updates
- Enable `cleanupOutdatedCaches` for automatic cleanup

### 2. Development Workflow
- Clear service workers and caches in development mode
- Test routing in incognito mode to avoid cache issues
- Use browser dev tools to disable cache during development

### 3. Vite Configuration
- Exclude problematic chunks from optimization
- Force include common dependencies
- Monitor console for dependency warnings

## Critical Status Assessment

### Question: Is the Vite chunk issue critical?
**Answer: No, but should be monitored.**

**Reasoning:**
- The chunk warnings don't break functionality
- They indicate dependency resolution issues that could become problematic
- The fix (excluding chunks + including common deps) resolves the warnings
- In production, these issues are less likely due to different bundling strategy

**Monitoring:**
- Watch for similar warnings with new dependencies
- Test production builds regularly
- Monitor bundle size and performance

## Files Modified

1. **`vite.config.ts`** - Updated VitePWA and optimizeDeps configuration
2. **`src/main.tsx`** - Added development cache clearing
3. **`public/sw.js`** - Removed (conflicting service worker)
4. **`docs/SERVICE-WORKER-ROUTING-FIX.md`** - This documentation

## Next Steps

1. **Test thoroughly**: Verify `/moroccan-demo` loads correctly on first visit
2. **Monitor performance**: Check if service worker changes affect load times
3. **Production testing**: Ensure fixes work in production environment
4. **Documentation**: Update deployment docs with service worker considerations

The routing issue should now be completely resolved, and the Vite warnings should be eliminated.
