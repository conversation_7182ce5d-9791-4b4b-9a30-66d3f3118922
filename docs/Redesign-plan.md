# Roadmap: The Adaptive Journey Planner & Mobile-First Experience

This document outlines the roadmap for transforming the application into an intelligent, adaptive co-pilot for users, with a strong emphasis on a mobile-first, PWA-driven design. It integrates concepts from our previous discussions and existing UI/UX enhancement documents.

## I. Overarching Goals

1.  **Groundbreaking UX/UI:** Evolve from a passive map animation to an interactive, intelligent journey planner.
2.  **Mobile-First & PWA:** Ensure a seamless, responsive, and app-like experience on all devices, with PWA capabilities for enhanced mobile engagement.
3.  **Adaptive & Personalized Guidance:** Actively help users make choices based on time, interests, and travel style.
4.  **Integration with Core Strengths:** Leverage and enhance existing features like map-driven discovery, animation, and POI data.

## II. Foundational Phase (Mobile Responsiveness, PWA, Core Fixes)

This phase focuses on establishing a solid, responsive foundation and addressing immediate functional and UX needs.

**Task F1: Full Mobile-First Responsive Design**
*   **Description:** Systematically review and refactor all primary components and views (`HomePage`, `ExploreMap`, `MainHeader`, `LeftPOIPanel`, `RightSidebar`, `CityTagsOverlay`, etc.) to be fully responsive, prioritizing the mobile experience.
*   **Details:**
    *   Ensure fluid layouts, appropriate touch targets (min 44px), and adequate spacing.
    *   Leverage Tailwind CSS responsive prefixes (`md:`, `lg:`) and the `useIsMobile` hook consistently.
    *   Adapt complex desktop layouts (e.g., multi-column) to single-column or tabbed interfaces on mobile.
*   **Affected Components:** `HomePage.tsx`, `ExploreMap.tsx`, `MainHeader.tsx`, `LeftPOIPanel.tsx`, `components/map/RightSidebar.tsx`, `components/map/overlays/CityTagsOverlay.tsx`, and their children.
*   **Integration:** Builds upon existing responsive efforts. See `UI-UX-Enhancements.md` (Section 1: Responsive Layout).

**Task F2: Enhance Mobile City Selection for Multi-City Itineraries**
*   **Description:** Modify the `MobileCitySelectionModal` (within `CityTagsOverlay.tsx`) to allow users to select multiple cities for their itinerary.
*   **Details:**
    *   Implement checkboxes or a similar multi-select UI for city list items within the modal.
    *   Add a "Done" or "Confirm Selections" button to the modal.
    *   The main "Select City" button on the map overlay should dynamically update to reflect selections (e.g., "2 Cities Selected" or brief list).
*   **Affected Components:** `components/map/overlays/CityTagsOverlay.tsx` (specifically `MobileCitySelectionModal`).
*   **Integration:** The `onCitySelect` callback (ultimately `HomePage.toggleDestination`) must handle an array of selections or be called iteratively. This ensures `selectedDestinations` state in `HomePage.tsx` is correctly updated for the journey.

**Task F3: Implement Collapsible Desktop Panels**
*   **Description:** Allow users to collapse/expand the left (POI/Filters) and right (Itinerary) panels on desktop to maximize map view.
*   **Details:**
    *   Add persistent collapse/expand UI controls (e.g., arrow icons on panel edges).
    *   Manage panel visibility state in `HomePage.tsx`.
*   **Affected Components:** `HomePage.tsx` (state management), `LeftPOIPanel.tsx`, `components/map/RightSidebar.tsx`.
*   **New Components:** UI controls for collapsing/expanding.

**Task F4: Basic PWA Setup**
*   **Description:** Implement foundational PWA features.
*   **Details:**
    *   Create a `manifest.json` file with app icons, name, start URL, display mode.
    *   Implement a basic service worker for caching essential static assets (shell app).
*   **Affected Components:** Project root, `index.html`.
*   **Integration:** This is a new layer, see general PWA best practices.

**Task F5: Resolve HMR Errors & Stability (Ongoing)**
*   **Description:** Address any outstanding Hot Module Replacement errors or other development stability issues.
*   **Affected Components:** Varies, likely Vite/React setup.

## III. Phase 1: Adaptive Journey Planner - Core Features

This phase implements the core logic and UI for the intelligent itinerary planning.

**Task AJP1: Itinerary Parameters - Initial Setup UI**
*   **Description:** Develop UI (likely in `RightSidebar.tsx` or a new dedicated component) for users to input initial journey parameters.
*   **Details:**
    *   Inputs for: Number of travel days, Travel Style/Pace (e.g., Relaxed, Moderate, Action-Packed), and optionally, broad Interests (History, Nature, etc.).
    *   This will be the first step in defining a multi-day journey.
*   **Affected Components:** `components/map/RightSidebar.tsx` (or new component), `HomePage.tsx` (for state management of these parameters).
*   **Integration:** This input will drive the generation of the "Journey Outline". Relates to concepts in `UI-UX-Flow-Suggestions.md` (Initial Journey Setup).

**Task AJP2: Journey Outline Generation**
*   **Description:** Based on user input (AJP1), the system generates a high-level daily outline.
*   **Details:**
    *   Example: Day 1: City A -> Midpoint B (Suggested stops: 2-3).
    *   Displayed in the right panel. This is a guideline, not yet a rigid schedule.
*   **Affected Components:** `components/map/RightSidebar.tsx`, `HomePage.tsx` (state for the outline).
*   **Integration:** Logic to suggest midpoints and stop counts based on duration/pace.

**Task AJP3: Enhanced POI/Cluster Discovery on Map**
*   **Description:** Improve how POIs and clusters of POIs are presented during map interaction and animation.
*   **Details:**
    *   Visually differentiate single POI markers from "POI Cluster" markers.
    *   Implement "Time Bubbles": Non-intrusive display of estimated duration for a POI/cluster when approached or interacted with.
*   **Affected Components:** `components/map/DirectPOIMarkers.tsx` (or equivalent marker system), `src/components/map/utils/POIDiscovery.ts`.
*   **Integration:** `src/components/map/utils/POIDiscovery.ts` (see `docs/POIDiscoveryFrameworkEvents.md`) will need to source/calculate POI durations and potentially emit new events or richer data for clusters. `Map-Marker-Implementation-Lessons.md` might offer insights.

**Task AJP4: Dynamic Itinerary Builder - "Up Next/Nearby" & POI Cards**
*   **Description:** Implement the core interactive section in the right panel (or left panel on mobile, as per `Redesign-plan.md` "Left Pane Repurposing" discussion).
*   **Details:**
    *   "Up Next / Nearby" section dynamically updates based on animation progress or map view.
    *   POI/Cluster cards display key info (name, photo, duration, tags) and interaction buttons:
        *   "Explore": Pauses animation (via `AnimationManager.ts`), zooms/pans map. Opens detailed POI view (modal or expanded section). Shows time impact on schedule.
        *   "Add to Day [N]": Adds POI/cluster to the `JourneyOutline` for the specified day.
        *   "Skip for Now" / "Save for Later".
*   **Affected Components:** `components/map/RightSidebar.tsx` (or `LeftPOIPanel.tsx` for mobile "Up Next"), `src/components/map/utils/POIDiscovery.ts`, `AnimationManager.ts`, `HomePage.tsx` (state for itinerary).
*   **Integration:** Relies on `src/components/map/utils/POIDiscovery.ts` events (see `docs/POIDiscoveryFrameworkEvents.md`). Animation control as per `docs/Animation-Flow-Documentation.md` and `docs/Animation-Architecture.md`.

**Task AJP5: Daily Schedule View**
*   **Description:** Display the user's planned itinerary with POIs organized by day within the right panel.
*   **Details:**
    *   Visual indication of how "full" a day is (e.g., time bar).
    *   Allow reordering (drag-and-drop if feasible) and removal of POIs.
*   **Affected Components:** `components/map/RightSidebar.tsx`, `HomePage.tsx`.

**Task AJP6: POI Data Enrichment Strategy**
*   **Description:** Define and implement a strategy for ensuring POIs have necessary data, especially estimated `duration` and relevant `tags`/`category`.
*   **Details:** This might involve data schema updates, default value logic, or admin interface changes (out of scope for this immediate plan, but necessary for the feature).
*   **Affected Components:** Data sources (`src/data/`), `POITypes.ts`.

**Task AJP7: Enhanced "City Approaching" Experience**
*   **Description:** Improve UI feedback when approaching major scheduled cities.
*   **Details:**
    *   Notifications: "Approaching [City Name]! This is your stop for Day [N]."
    *   Right panel switches to a "Welcome to [City Name]" view, showing already scheduled POIs for that city and top suggestions.
*   **Affected Components:** `CityDriveByManager.ts` (if still used, or new logic in `AnimationManager.ts`), `components/map/notifications/NotificationPanel.tsx`, `components/map/RightSidebar.tsx`.
*   **Integration:** Builds on existing city approach detection logic. Refer to `docs/Animation-Contextual-Controls.md`.

## IV. Phase 2: Advanced Features & Polish

**Task P2.1: Smart Panel Management (Desktop)**
*   **Description:** Implement context-aware showing/hiding or resizing of desktop panels.
*   **Details:** E.g., auto-collapse left panel during heavy map interaction, expand right panel when itinerary is being actively built.
*   **Affected Components:** `HomePage.tsx`, `LeftPOIPanel.tsx`, `components/map/RightSidebar.tsx`.

**Task P2.2: Advanced PWA Capabilities**
*   **Description:** Implement more robust offline support.
*   **Details:** Service worker caching for itinerary data, map tiles for selected regions (if feasible with Mapbox).
*   **Affected Components:** Service worker, data storage logic.

**Task P2.3: Advanced Animation Controls**
*   **Description:** "Jump to Next Suggested Stop" or "Jump to [POI name on itinerary]" functionality.
*   **Affected Components:** `AnimationManager.ts`, UI controls.
*   **Integration:** Requires `AnimationManager.ts` to understand the itinerary structure. See `docs/TravelAnimator-Enhancements-Roadmap.md`.

**Task P2.4: Schedule-Aware Animation Pacing (Future)**
*   **Description:** `AnimationManager.ts` potentially adjusts animation speed or provides feedback if the user's planned stops exceed available time for a travel segment.
*   **Affected Components:** `AnimationManager.ts`, `ContextualSpeedController.ts`.
*   **Integration:** Deep integration between itinerary state and animation timing.

## V. UI/UX Principles from Existing Docs to Maintain/Incorporate:

*   **Visual Feedback & Micro-interactions:** Continue using for selections, loading states. (from `UI-UX-Enhancements.md`)
*   **Consistent Color System:** Adhere to defined primary, secondary, accent colors. (from `UI-UX-Enhancements.md`)
*   **Progressive Disclosure:** Reveal information and options contextually. (from `UI-UX-Enhancements.md`)
*   **Contextual Rhythm for Camera:** Maintain "breathing zoom patterns" and appropriate camera work for different phases. (from `UI-UX-Enhancements.md` and `docs/MapCameraRules.md`)
*   **Time-Based Considerations:** Make travel times and POI durations transparent. (from `UI-UX-Enhancements.md`)

## VI. Original Content from Previous `Redesign-plan.md` (For Reference - To be integrated or superseded by above tasks)

(This section would ideally contain the original content of Redesign-plan.md if we were appending. Since we are replacing and integrating, the ideas from the original file have been woven into the tasks above, particularly around the "Adaptive Journey Planner" concept, the focus on the "Unsure Traveler," and the iterative UI development for the right panel and POI discovery.)

**Key Original Ideas Integrated:**
*   The core "Adaptive Journey Planner" concept is the central theme of Phase 1 (AJP tasks).
*   Focus on user inputs like travel duration and style (Task AJP1).
*   Interactive route animation with dynamic POI/cluster cards and "Explore" functionality (Task AJP3, AJP4).
*   Time-awareness for POIs ("Time Bubbles," impact on schedule) (Task AJP3, AJP4).
*   Mobile-first considerations and repurposing of the left pane for "Up Next/Nearby" on mobile (Task F1, F2, and influences AJP4 design for mobile).

This revised document now serves as a more structured roadmap.



