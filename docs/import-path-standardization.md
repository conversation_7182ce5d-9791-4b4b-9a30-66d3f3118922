# Import Path Standardization Guide

## Overview

This document outlines the standardization of import paths in the Morocco Travel application to ensure consistency and prevent import-related errors. The primary goal is to establish a single source of truth for each module and ensure all imports reference it correctly.

## Key Issues Identified

1. **Duplicate File Implementations**:
   - We found multiple implementations of the same class/functionality in different directories
   - Example: `POIDiscoveryManager` existed in both:
     - `src/components/map/animation/POIDiscoveryManager.ts`
     - `src/components/map/utils/POIDiscoveryManager.ts`

2. **Inconsistent Import Path Styles**:
   - Some files used aliases (`@/types/Position`)
   - Others used relative paths (`../../../types/poi`)
   - Even within the same file, these styles were mixed

3. **Type Definition Conflicts**:
   - Multiple definitions for the same types in `.ts` and `.d.ts` files
   - Example: `PointOfInterest` defined in both:
     - `src/types/poi.ts`
     - `src/types/poi.d.ts`

## Import Path Standards

### Preferred Import Style

1. **Use Alias Paths**:
   - Use the `@/` alias throughout the project for imports that reach outside the current directory
   - Example: `import { Position } from '@/types/Position';`

2. **Use Relative Paths Only for Same Directory**:
   - Use relative paths only for imports from the same directory
   - Example: `import { ComponentInteractionManager } from './ComponentInteractionManager';`

3. **Use Full File Extensions When Ambiguity Exists**:
   - Include the `.ts` extension when importing from files that have corresponding `.d.ts` files
   - Example: `import { PointOfInterest } from '@/types/poi.ts';`

### Duplicate Files Handling

When duplicate implementations exist:

1. **Mark Secondary Implementation as Deprecated**:
   - Add `@deprecated` JSDoc comment
   - Add re-export of the primary implementation
   - Add console warning
   
   ```typescript
   /**
    * @deprecated This file is deprecated. Please use the version in .../animation/... instead.
    */
   import ActualImplementation from '../path/to/primary';
   export default ActualImplementation;
   ```

2. **Migrate Imports to Primary Implementation**:
   - Identify all files importing the deprecated version
   - Update imports to reference the primary implementation

## Type Definition Best Practices

1. **Single Source of Truth**:
   - Each type should have one authoritative definition location
   - Avoid duplicate definitions across `.ts` and `.d.ts` files

2. **Type Extension vs. Redefinition**:
   - Use interface extension rather than creating duplicate interfaces
   - Example: `export interface ExtendedPointOfInterest extends PointOfInterest {}`

3. **Use Explicit Exports**:
   - Always use named exports for types
   - Avoid default exports for type definitions

## Fixed Files and Changes Made

1. **Deprecated `/utils/POIDiscoveryManager.ts`**:
   - Added deprecation notice
   - Re-exported the implementation from `/animation/POIDiscoveryManager.ts`
   - Added console warning

2. **Updated Import Paths in**:
   - `src/components/map/animation/POIDiscoveryManager.ts`
   - `src/components/map/animation/POIDiscoveryIntegration.ts`
   - `src/components/map/animation/CinematicController.ts`

3. **Standardized Type Imports**:
   - Updated imports to use file extensions for ambiguous types
   - Used proper path aliases (`@/`) for all imports

## Future Development Guidelines

1. **New Files**:
   - Always use path aliases for imports outside the current directory
   - Follow the established directory structure
   - Avoid creating duplicate implementations

2. **Type Definitions**:
   - Place new type definitions in the `src/types` directory
   - Use interface extension to build on existing types
   - Create clear separation between different types

3. **Import Validation**:
   - Periodically run linting to ensure import consistency
   - Address any import-related warnings or errors promptly

## Tools for Checking Import Consistency

Run the following commands to check for import inconsistencies:

```bash
# Find relative imports that could use path aliases
grep -r "from '\.\./\.\./\.\." src/components/

# Find duplicate implementations
find src -name "*POIDiscovery*.ts" | sort

# Check import paths for specific modules
grep -r "import.*POIDiscoveryManager" src
```

---

By following these guidelines, we can maintain a more maintainable and error-resistant codebase with consistent import paths across the application. 