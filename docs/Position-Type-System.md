# Position Type System

## Overview

This document details the comprehensive Position type system implemented in the Come To Morocco application. The system provides a consistent approach to working with geographic coordinates across the entire codebase, enhancing type safety, reducing bugs, and simplifying position-related operations.

The primary goal of this type system is to:
1. Establish a single source of truth for position-related types
2. Provide robust type checking and validation
3. Enable seamless conversion between different position formats
4. Handle edge cases and error conditions gracefully

## Core Position Types

### Position
The fundamental position representation as a longitude/latitude tuple:
```typescript
export type Position = [number, number]; // [longitude, latitude]
```

This is the preferred format for internal use throughout the application.

### MapPosition
Object representation with named properties, primarily used with Mapbox:
```typescript
export interface MapPosition {
  lng: number;
  lat: number;
}
```

### GeoPosition
Format used in GeoJSON and some external APIs:
```typescript
export interface GeoPosition {
  longitude: number;
  latitude: number;
}
```

### LatLngPosition
Variant with swapped property order used in some mapping libraries:
```typescript
export interface LatLngPosition {
  lat: number;
  lng: number;
}
```

### Type Aliases
For backwards compatibility and clarity:
```typescript
export type LngLatPosition = Position;
export type LatLngTuple = Position;
export type AnyPosition = Position | MapPosition | GeoPosition | LatLngPosition;
```

## Type Guards

The system includes type guard functions to safely check position formats:

```typescript
// Check if value is a valid Position tuple
export function isPosition(pos: unknown): pos is Position {
  return (
    Array.isArray(pos) && 
    pos.length === 2 && 
    typeof pos[0] === 'number' && 
    typeof pos[1] === 'number' &&
    !isNaN(pos[0]) && 
    !isNaN(pos[1]) &&
    isFinite(pos[0]) && 
    isFinite(pos[1])
  );
}

// Similar guards for other position types
export function isMapPosition(pos: unknown): pos is MapPosition { /* ... */ }
export function isGeoPosition(pos: unknown): pos is GeoPosition { /* ... */ }
export function isLatLngPosition(pos: unknown): pos is LatLngPosition { /* ... */ }
export function isAnyPosition(pos: unknown): pos is AnyPosition { /* ... */ }
```

These guards provide runtime validation to ensure positions are structurally correct.

## Conversion Utilities

The system offers a comprehensive set of conversion functions:

### Basic Conversion Functions
```typescript
export function positionToMapPosition(pos: Position): MapPosition { /* ... */ }
export function mapPositionToPosition(pos: MapPosition): Position { /* ... */ }
export function positionToGeoPosition(pos: Position): GeoPosition { /* ... */ }
export function geoPositionToPosition(pos: GeoPosition): Position { /* ... */ }
export function positionToLatLngPosition(pos: Position): LatLngPosition { /* ... */ }
export function latLngPositionToPosition(pos: LatLngPosition): Position { /* ... */ }
```

### Universal Conversion Function
Converts any position format to the standard Position tuple:
```typescript
export function toPosition(pos: unknown): Position {
  try {
    if (isPosition(pos)) {
      return pos;
    } else if (isMapPosition(pos)) {
      return [pos.lng, pos.lat];
    } else if (isGeoPosition(pos)) {
      return [pos.longitude, pos.latitude];
    } else if (isLatLngPosition(pos)) {
      return [pos.lng, pos.lat];
    } else if (/* handle additional edge cases */) {
      // ...
    }
    throw new Error(`Invalid position format: ${JSON.stringify(pos)}`);
  } catch (error) {
    console.error('Position conversion error:', error);
    throw new Error(`Failed to convert to Position: ${error instanceof Error ? error.message : String(error)}`);
  }
}
```

### Safe Conversion
For situations where errors should be handled gracefully:
```typescript
export function safeToPosition(pos: unknown, fallback: Position = [0, 0]): Position {
  try {
    return toPosition(pos);
  } catch (error) {
    console.warn('Position conversion failed, using fallback:', error);
    return fallback;
  }
}
```

## Validation Functions

Functions to validate individual coordinates and complete positions:

```typescript
export function isValidLongitude(lng: number): boolean {
  return typeof lng === 'number' && !isNaN(lng) && isFinite(lng) && lng >= -180 && lng <= 180;
}

export function isValidLatitude(lat: number): boolean {
  return typeof lat === 'number' && !isNaN(lat) && isFinite(lat) && lat >= -90 && lat <= 90;
}

export function isValidPosition(pos: Position): boolean {
  return isPosition(pos) && isValidLongitude(pos[0]) && isValidLatitude(pos[1]);
}
```

## Helper Utilities

Additional utilities to support common position-related operations:

```typescript
// Create a validated position
export function createPosition(lng: number, lat: number): Position {
  if (!isValidLongitude(lng)) {
    throw new Error(`Invalid longitude value: ${lng}. Must be between -180 and 180.`);
  }
  if (!isValidLatitude(lat)) {
    throw new Error(`Invalid latitude value: ${lat}. Must be between -90 and 90.`);
  }
  return [lng, lat];
}

// Default position for Morocco (centered in the country)
export const DEFAULT_MOROCCO_POSITION: Position = [-6.0, 31.8];

// Extract position data from objects with unpredictable structure
export function extractPositionFromObject(obj: Record<string, any>): Position | null {
  try {
    // Try various position formats and property names
    // ...
  } catch (error) {
    console.error('Failed to extract position from object:', error);
    return null;
  }
}
```

## Usage Examples

### Basic Usage
```typescript
import { Position, toPosition, isValidPosition } from '../types/Position';

// Create a position
const marrakechPosition: Position = [-7.9811, 31.6295];

// Validate a position
if (isValidPosition(marrakechPosition)) {
  // Safe to use
}

// Convert from any format to Position
const apiPosition = { longitude: -5.833954, latitude: 35.759465 };
const position = toPosition(apiPosition);
```

### Error Handling
```typescript
import { safeToPosition, DEFAULT_MOROCCO_POSITION } from '../types/Position';

function processCoordinates(data: unknown) {
  try {
    // Attempt to convert, falling back to default if necessary
    const position = safeToPosition(data, DEFAULT_MOROCCO_POSITION);
    // Process position...
  } catch (error) {
    console.error('Failed to process coordinates:', error);
    // Handle error...
  }
}
```

### Type Guards for Safety
```typescript
import { isPosition, isMapPosition, toPosition } from '../types/Position';

function handlePositionData(data: unknown) {
  if (isPosition(data)) {
    // TypeScript knows this is a Position tuple
    const [lng, lat] = data;
    return;
  }
  
  if (isMapPosition(data)) {
    // TypeScript knows this is a MapPosition object
    const { lng, lat } = data;
    return;
  }
  
  // Otherwise try to convert or handle error
  try {
    const position = toPosition(data);
    // Process position...
  } catch (error) {
    // Handle conversion error...
  }
}
```

## Benefits of the Position Type System

1. **Type Safety**: Comprehensive type checking prevents common errors
2. **Consistency**: Establishes a single pattern for handling positions
3. **Flexibility**: Easily works with different position formats from various APIs
4. **Error Prevention**: Validation and robust error handling
5. **Developer Experience**: Clear API with descriptive function names
6. **Maintainability**: Centralized position logic reduces code duplication
7. **Performance**: Optimized conversion functions

## Implementation Guidelines

When working with positions in the codebase:

1. Always import position types from `src/types/Position.ts`
2. Use the `Position` type as the primary format for internal operations
3. Use type guards (`isPosition`, etc.) before performing operations on positions
4. When accepting position data from external sources, use `toPosition` or `safeToPosition`
5. Always handle potential errors when working with position data
6. For positions that might be invalid, use validation functions before operations

By following these guidelines, the application maintains consistent and reliable geographic coordinate handling across all components. 