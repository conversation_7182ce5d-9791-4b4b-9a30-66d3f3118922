# Animation Contextual Controls

## Overview

This document outlines the implementation of contextual animation controls in our Morocco Travel application, specifically focusing on how the vehicle animation speed, camera behavior, and user interaction should change based on proximity to points of interest (POIs) and unselected cities. These enhancements create a more engaging user experience and provide additional opportunities for discovery during the journey animation.

## 1. Contextual Speed Control

### Speed Adjustment Rules

The animation speed should dynamically adjust based on the following contextual factors:

| Context | Speed Multiplier | Activation Distance | Purpose |
|---------|------------------|---------------------|---------|
| Default Route | 1.0 | N/A | Base speed for general route travel |
| Near POI | 0.5 | 5 km | Slow down to highlight nearby POI |
| POI Cluster | 0.3 | 3 km | Significant slowdown for multiple POIs |
| Unselected City | 0.4 | 10 km | Slow down when approaching a new city |
| Cultural Region | 0.6 | 3 km | Moderate slowdown in cultural areas |
| Scenic Route | 0.7 | 5 km | Slight slowdown in scenic areas |
| Mountain Terrain | 0.8 | N/A | Slight slowdown in mountainous regions |
| City Center | 0.3 | 2 km | Significant slowdown in dense urban areas |

#### Implementation Requirements

```typescript
// Interface for speed context
interface SpeedContext {
  multiplier: number;
  reason: string;
  location?: [number, number];
  associatedElement?: string; // POI ID or City name
}

// Function to determine current speed context
function determineSpeedContext(
  currentPosition: [number, number],
  route: [number, number][],
  pois: PointOfInterest[],
  cities: Destination[]
): SpeedContext {
  // Default context
  let context: SpeedContext = { 
    multiplier: 1.0, 
    reason: 'default' 
  };

  // Check proximity to POIs
  const nearbyPOIs = findNearbyPOIs(currentPosition, pois, 5);
  if (nearbyPOIs.length > 3) {
    return { 
      multiplier: 0.3, 
      reason: 'poi_cluster',
      associatedElement: `cluster_${nearbyPOIs[0].id}`
    };
  } else if (nearbyPOIs.length > 0) {
    return { 
      multiplier: 0.5, 
      reason: 'near_poi',
      associatedElement: nearbyPOIs[0].id
    };
  }

  // Check proximity to unselected cities
  const nearbyCity = findNearbyUnselectedCity(currentPosition, cities, 10);
  if (nearbyCity) {
    return { 
      multiplier: 0.4, 
      reason: 'unselected_city',
      associatedElement: nearbyCity.name
    };
  }

  // Check for terrain/region type
  const regionType = determineRegionType(currentPosition);
  switch (regionType) {
    case 'cultural':
      return { multiplier: 0.6, reason: 'cultural_region' };
    case 'scenic':
      return { multiplier: 0.7, reason: 'scenic_route' };
    case 'mountain':
      return { multiplier: 0.8, reason: 'mountain_terrain' };
    case 'city_center':
      return { multiplier: 0.3, reason: 'city_center' };
    default:
      return context;
  }
}
```

### Speed Transition Rules

When changing between speed contexts:
- Transitions should be gradual, not sudden
- Use easing functions for natural acceleration/deceleration
- The transition duration should be proportional to the speed change
- Visual cues should be provided when speed changes occur

```typescript
// Apply speed transition
function applySpeedTransition(
  currentSpeed: number, 
  targetSpeed: number,
  reason: string
): void {
  // Calculate transition duration based on delta
  const speedDelta = Math.abs(currentSpeed - targetSpeed);
  const baseDuration = 1000; // 1 second base
  const transitionDuration = baseDuration * speedDelta;
  
  // Create transition with easing
  AnimationManager.getInstance().transitionSpeed(
    targetSpeed, 
    transitionDuration,
    'easeInOutCubic',
    reason
  );
  
  // Show visual indicator for speed change if the delta is significant
  if (speedDelta > 0.3) {
    showSpeedChangeIndicator(targetSpeed < currentSpeed ? 'slowing' : 'accelerating', reason);
  }
}
```

## 2. POI Discovery Integration

### Discovery Mechanics

The system should enable users to discover POIs during the journey through the following mechanisms:

#### Automatic Detection

- **Distance-Based Detection**: Detect POIs within configurable radius during animation
- **Direction-Based Awareness**: Indicate upcoming POIs in the direction of travel
- **Cluster Recognition**: Group nearby POIs into discoverable clusters
- **Importance Weighting**: Prioritize significant POIs over minor ones

#### User Interaction

- **Notification System**: Show non-intrusive notifications when approaching POIs
- **Interactive Pause**: Allow users to pause the journey to explore POIs
- **Exploration Controls**: Provide clear controls to explore or continue
- **Discovery Log**: Maintain a history of discovered and passed POIs

#### Implementation Requirements

```typescript
interface POIDiscoveryOptions {
  automaticPause: boolean;    // Whether to pause automatically
  notificationDuration: number; // How long notifications should display
  detectionRadius: number;     // Base POI detection radius in km
  minImportanceThreshold: number; // Minimum POI importance to trigger notifications
}

// Function to handle POI discovery events
function handlePOIDiscovery(
  poi: PointOfInterest,
  distance: number,
  options: POIDiscoveryOptions,
  onPOIAction: (poi: PointOfInterest, action: 'explore' | 'continue') => void
): void {
  // Show notification with action buttons
  const notification = showPOINotification(poi, distance, {
    title: `Discover ${poi.name}`,
    subtitle: `${distance.toFixed(1)}km away - ${poi.category}`,
    actions: [
      {
        label: 'Explore',
        action: () => onPOIAction(poi, 'explore')
      },
      {
        label: 'Continue',
        action: () => onPOIAction(poi, 'continue')
      }
    ],
    duration: options.automaticPause ? 0 : options.notificationDuration // 0 means don't auto-dismiss
  });
  
  // If automatic pausing is enabled, pause the animation
  if (options.automaticPause) {
    AnimationManager.getInstance().pauseAnimation({
      reason: 'poi_discovery',
      associatedElement: poi.id
    });
    
    // Focus the camera on the POI
    // TODO: Update CameraManager.getInstance() if API has changed.
    // Camera control is now primarily via EnhancedCameraBehavior.ts and CinematicController.ts.
    CameraManager.getInstance().focusOn(poi.coordinates as [number, number], {
      zoom: 14,
      duration: 1500,
      pitch: 45
    });
  }
  
  // Add to discovered POIs list
  addToDiscoveredPOIs(poi);
}
```

## 3. City Drive-By Experience

When approaching or passing through unselected cities, the system should provide a rich experience that encourages exploration:

### Camera Behavior

- **Gradual Zoom**: Increase zoom level gradually as the vehicle approaches the city
- **Contextual Perspective**: Adjust camera pitch and bearing to showcase the city
- **POI Highlighting**: Highlight key POIs in the city with markers
- **Skyline View**: Position camera to capture city skyline when possible

### UI Integration

- **City Information Panel**: Show sidebar with city name, description, and key stats
- **POI List**: Display a scrollable list of top POIs in the city
- **Quick Action Buttons**: Show "Explore City" and "Continue Journey" options
- **Visual Indicators**: Add subtle visual cues on the map showing city boundaries

### Implementation Requirements

```typescript
interface CityDriveByOptions {
  zoomLevel: number;       // Target zoom level for city view
  approachDistance: number; // Distance at which to begin zoom transition (km)
  showTopPOIs: number;     // Number of top POIs to display
  cameraDuration: number;  // Duration of camera transition in ms
}

// Function to handle approaching an unselected city
function handleCityApproach(
  city: Destination,
  distance: number,
  options: CityDriveByOptions,
  onCityAction: (city: Destination, action: 'explore' | 'continue') => void
): void {
  // Calculate zoom level based on distance
  // Closer = higher zoom
  const distanceRatio = Math.min(1, distance / options.approachDistance);
  const dynamicZoom = options.zoomLevel + (1 - distanceRatio) * 2;
  
  // Update camera to focus on city with adaptive zoom
  // TODO: Update CameraManager.getInstance() if API has changed.
  // Camera control is now primarily via EnhancedCameraBehavior.ts and CinematicController.ts.
  CameraManager.getInstance().updateCamera({
    center: city.coordinates as [number, number],
    zoom: dynamicZoom,
    pitch: 50,
    bearing: calculateOptimalBearing(city),
    duration: options.cameraDuration,
    essential: true
  });
  
  // Show city information panel
  UI.showCityPanel({
    city: city,
    topPOIs: getTopPOIsForCity(city, options.showTopPOIs),
    onExplore: () => onCityAction(city, 'explore'),
    onContinue: () => onCityAction(city, 'continue')
  });
  
  // Highlight city boundaries on map
  MapOverlayManager.highlightCityBoundaries(city);
  
  // Show city POI markers
  MapOverlayManager.showCityPOIMarkers(city, options.showTopPOIs);
  
  // Slow down vehicle animation
  AnimationManager.getInstance().setSpeedMultiplier(0.4, {
    reason: 'approaching_city',
    associatedElement: city.name
  });
}
```

## 4. Component Interactions

The animation system needs to communicate effectively with other UI components:

### Core Component Interactions

- **TravelAnimator <-> POIDiscoveryManager**: Animation progress triggers POI discovery checks
- **AnimationManager <-> CameraManager**: Speed changes trigger camera behavior changes
- **VehicleManager <-> AnimationManager**: Vehicle visibility is monitored during animation
- **POIDiscoveryManager <-> CityInfoPanel**: Discovered POIs update the sidebar display
- **AnimationManager <-> UI Controls**: Animation state changes update button states

### Communication Methods

- **Event System**: Key state changes dispatch custom events
- **Context API**: Animation states are available through React context
- **Direct Function Calls**: Performance-critical operations use direct calls
- **Custom Hooks**: Components use hooks to access animation capabilities

### Implementation Requirements

```typescript
// Custom event types
interface AnimationEvents {
  'animation:speed-change': {
    newSpeed: number;
    reason: string;
  };
  'animation:poi-discovered': {
    poi: PointOfInterest;
    distance: number;
  };
  'animation:city-approach': {
    city: Destination;
    distance: number;
  };
  'animation:pause': {
    reason: string;
    timestamp: number;
  };
  'animation:resume': {
    reason: string;
    timestamp: number;
  };
}

// Event dispatcher for animation events
const dispatchAnimationEvent = <K extends keyof AnimationEvents>(
  eventName: K,
  detail: AnimationEvents[K]
) => {
  const event = new CustomEvent(eventName, { detail });
  document.dispatchEvent(event);
};

// Event listeners in components
useEffect(() => {
  const handlePOIDiscovery = (event: CustomEvent) => {
    const { poi, distance } = event.detail;
    // Update UI accordingly
    setPOINotification({ poi, distance });
  };
  
  document.addEventListener(
    'animation:poi-discovered',
    handlePOIDiscovery as EventListener
  );
  
  return () => {
    document.removeEventListener(
      'animation:poi-discovered',
      handlePOIDiscovery as EventListener
    );
  };
}, []);
```

## 5. Vehicle Visibility & Identification

### Marker Consistency

- **ID Standardization**: Always use `direct-vehicle-marker` as the marker ID
- **Visibility Checks**: Implement multi-level checks for vehicle visibility
- **Emergency Recovery**: Use fallback marker creation as a last resort
- **Style Consistency**: Maintain consistent visual style across all marker states

### Implementation Requirements

```typescript
// Vehicle manager visibility check
function checkAndFixVehicleVisibility(
  position: [number, number],
  bearing: number
): boolean {
  const vehicleMarker = document.getElementById('direct-vehicle-marker');
  
  if (!vehicleMarker || 
      vehicleMarker.style.display === 'none' || 
      vehicleMarker.style.visibility === 'hidden') {
    console.warn(`⚠️ [${new Date().toISOString()}] Vehicle not visible, attempting to fix`);
    
    // Try to fix by forcing visibility
    forceVehicleVisibility(position, bearing);
    
    // Re-check after fix attempt
    setTimeout(() => {
      const markerAfterFix = document.getElementById('direct-vehicle-marker');
      if (!markerAfterFix || 
          markerAfterFix.style.display === 'none' || 
          markerAfterFix.style.visibility === 'hidden') {
        console.error(`❌ [${new Date().toISOString()}] Vehicle still not visible after fix attempt`);
        
        // Last resort: create emergency marker
        createEmergencyMarker(position, bearing);
      }
    }, 100);
    
    return false;
  }
  
  return true;
}
```

## 6. Camera Transition Specifications

### Camera Transitions for Contextual Animation

- **Route Travel**: Zoom level 8, pitch 45°, bearing north-oriented (0°)
- **City Exploration**: Zoom level 14, pitch 50°, bearing city-dependent
- **POI Detail**: Zoom level 16, pitch 60°, bearing POI-oriented
- **Drive-By Experience**: Zoom levels 10-14 based on distance, pitch 45-60°
- **Cinematic Overview**: Dynamic zoom and bearing for dramatic effect

### Transition Parameters

- **Duration**: Use at least 500ms for small changes, 1500-2000ms for large changes
- **Easing**: Use `easeInOutCubic` for smooth transitions
- **Essential Flag**: Mark critical animations as `essential: true`
- **Priority**: Camera changes from user interaction take precedence
- **Interruption**: Only interrupt transitions for user-initiated actions

### Implementation Requirements

```typescript
interface CameraTransitionOptions {
  zoom: number;
  pitch: number;
  bearing: number;
  duration: number;
  essential: boolean;
  easing?: 'linear' | 'easeInOut' | 'easeInOutCubic';
  priority?: 'low' | 'medium' | 'high' | 'critical';
}

interface CameraPresets {
  routeTravel: CameraTransitionOptions;
  cityExploration: CameraTransitionOptions;
  poiDetail: CameraTransitionOptions;
  dramaticOverview: CameraTransitionOptions;
  driveBySlow: CameraTransitionOptions;
  driveByFast: CameraTransitionOptions;
}

// Presets for different camera contexts
const CAMERA_PRESETS: CameraPresets = {
  routeTravel: {
    zoom: 8,
    pitch: 45,
    bearing: 0,
    duration: 1000,
    essential: true,
    easing: 'easeInOutCubic',
    priority: 'medium'
  },
  cityExploration: {
    zoom: 14,
    pitch: 50,
    bearing: 0, // Will be adjusted based on city
    duration: 1500,
    essential: true,
    easing: 'easeInOutCubic',
    priority: 'medium'
  },
  poiDetail: {
    zoom: 16,
    pitch: 60,
    bearing: 30, // Will be adjusted based on POI
    duration: 1800,
    essential: true,
    easing: 'easeInOutCubic',
    priority: 'high'
  },
  dramaticOverview: {
    zoom: 6,
    pitch: 60,
    bearing: 0,
    duration: 2500,
    essential: true,
    easing: 'easeInOutCubic',
    priority: 'low'
  },
  driveBySlow: {
    zoom: 12,
    pitch: 55,
    bearing: 0, // Will be adjusted
    duration: 2000,
    essential: false,
    easing: 'easeInOutCubic',
    priority: 'medium'
  },
  driveByFast: {
    zoom: 10,
    pitch: 45,
    bearing: 0, // Will be adjusted
    duration: 1200,
    essential: false,
    easing: 'easeInOutCubic',
    priority: 'low'
  }
};
```

## 7. Debug & Monitoring Tools

### Debug Logging Requirements

- **Consistent Logging**: Use `AnimationDebugTools.log()` for all animation events
- **Error Reporting**: Full error context when animation issues occur
- **Performance Tracking**: Log frame rate and operation durations
- **State Transitions**: Log all significant state changes with timestamps
- **Speed Changes**: Log speed changes with reasons and timestamp

### Debug Visualization

- **Animation State**: Visualize animation status on screen during development
- **Speed Indicator**: Show current animation speed multiplier
- **Proximity Alerts**: Visualize detection radius for POIs and cities
- **Performance Graphs**: Show frame rate and animation performance over time
- **POI Discovery**: Highlight discovered and upcoming POIs

### Implementation Requirements

```typescript
// Animation debug tools enhancements
interface DebugLogOptions {
  level: 'info' | 'warning' | 'error' | 'performance';
  context?: string;
  timestamp?: number;
  data?: any;
}

// Consistent logging
AnimationDebugTools.log = (message: string, options?: DebugLogOptions) => {
  const timestamp = options?.timestamp || Date.now();
  const dateTime = new Date(timestamp).toISOString();
  const level = options?.level || 'info';
  const context = options?.context || 'animation';
  
  // Format log entry
  const formattedMessage = `[${dateTime}] [${level.toUpperCase()}] [${context}] ${message}`;
  
  // Add to debug log
  debugLogs.push({
    message,
    timestamp,
    level,
    context,
    data: options?.data
  });
  
  // Keep log size manageable
  if (debugLogs.length > MAX_DEBUG_LOG_ENTRIES) {
    debugLogs.shift();
  }
  
  // Log to console in development mode
  if (process.env.NODE_ENV === 'development') {
    switch (level) {
      case 'warning':
        console.warn(formattedMessage, options?.data || '');
        break;
      case 'error':
        console.error(formattedMessage, options?.data || '');
        break;
      case 'performance':
        console.info(`🔍 ${formattedMessage}`, options?.data || '');
        break;
      default:
        console.log(formattedMessage, options?.data || '');
    }
  }
  
  // Dispatch event for debug UI
  dispatchDebugEvent(level, message, options?.data);
};
```

## Implementation Timeline

1. **Phase 1 (Immediate):**
   - Standardize vehicle marker IDs
   - Implement consistent logging with AnimationDebugTools.log()
   - Create contextual speed control framework

2. **Phase 2 (Short-term):**
   - Implement POI discovery notification system
   - Add camera transition presets
   - Enhance vehicle visibility recovery mechanisms

3. **Phase 3 (Medium-term):**
   - Develop city drive-by experience
   - Implement component interaction events
   - Create debug visualization tools

4. **Phase 4 (Long-term):**
   - Refine interactive pause mechanisms
   - Enhance performance optimization
   - Implement full contextual animation awareness 