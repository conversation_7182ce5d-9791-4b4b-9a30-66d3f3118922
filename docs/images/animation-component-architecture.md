# Animation Component Architecture

```mermaid
graph TD
    %% Components
    AI[AnimationIntegration] --> AM[AnimationManager]
    AI --> CC[CameraController]
    AI --> VC[VehicleController]
    AI --> RA[RouteAnimator]
    AI --> PD[POIDiscoveryManager]
    
    VC --> VM[VehicleManager]
    VM --> VD[VehicleDOM]
    
    AM --> VC
    AM --> CC
    AM --> RA
    AM --> PD
    
    CC --> CM[CameraManager]
    
    %% Component Responsibilities
    subgraph "Presentation Layer"
        RA
        CC
        VC
    end
    
    subgraph "Business Logic Layer"
        AM
        VM
        CM
        PD
    end
    
    subgraph "DOM Manipulation Layer"
        VD
    end
    
    %% Legend
    classDef interface fill:#f9f,stroke:#333,stroke-width:2px;
    classDef component fill:#bbf,stroke:#333,stroke-width:1px;
    
    %% Apply styles
    class AI,AM,VC,CC,RA,VM,VD,CM,PD component;
```

## Component Responsibilities

### Integration Layer
- **AnimationIntegration**: Coordinates all animation components and provides a unified interface to React components

### Presentation Layer
- **RouteAnimator**: Handles the animation of routes on the map
- **CameraController**: Controls camera movements during animation
- **VehicleController**: Manages vehicle animations and interactions

### Business Logic Layer
- **AnimationManager**: Core animation loop and state management
- **VehicleManager**: Manages vehicle state and delegates DOM operations
- **CameraManager**: Manages camera state and map view updates
- **POIDiscoveryManager**: Handles POI discovery during animation

### DOM Manipulation Layer
- **VehicleDOM**: Low-level DOM operations for vehicle markers

## Data Flow

```mermaid
sequenceDiagram
    participant Component as React Component
    participant AI as AnimationIntegration
    participant VC as VehicleController
    participant VM as VehicleManager
    participant VD as VehicleDOM
    
    Component->>AI: startAnimation()
    AI->>VC: initializeAnimation()
    VC->>VM: createVehicle()
    VM->>VD: createMarkerElement()
    VD-->>VM: markerElement
    
    Component->>AI: updatePosition(pos)
    AI->>VC: smoothUpdatePosition(pos)
    VC->>VM: updatePosition(calculatedPos)
    VM->>VD: updateElementPosition(element, pos)
    
    VD-->>VM: positionUpdated
    VM-->>VC: positionUpdated
    VC-->>AI: vehiclePositionChanged
    AI-->>Component: onPositionUpdate
```

## Component Interactions

| Component | Depends On | Responsible For |
|-----------|------------|-----------------|
| AnimationIntegration | AnimationManager, VehicleController, CameraController | Coordinating the animation system |
| VehicleController | VehicleManager | Animation and user interaction for the vehicle |
| VehicleManager | VehicleDOM | Vehicle state management |
| VehicleDOM | None | DOM manipulation for vehicle marker |
| CameraController | CameraManager | Camera animations and transitions |
| RouteAnimator | AnimationManager | Route animation calculations |
| POIDiscoveryManager | VehicleManager | Discovering POIs along route | 