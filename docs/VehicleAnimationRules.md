# Vehicle Animation Rules

**2024 Rewrite Notice:**
- The vehicle marker system was rewritten from a blank slate. All marker positioning is now controlled by JavaScript using `transform: translate(-50%, -50%) translate3d(x, y, 0) rotate(bearing)`.
- **Do not use `!important` for `left`, `top`, or `transform` on `.vehicle-marker` in any CSS.** This will break JS-based positioning.
- The marker is always a child of the map container, with ID `direct-vehicle-marker` and class `vehicle-marker`.
- All marker creation, update, and recovery logic is handled by the singleton `VehicleManager`.

**2024 Rewrite Note:**
- As of the 2024 rewrite, only `AnimationManager`, `VehicleManager`, and `RouteAnimator` are used in the main animation flow.
- `CinematicController`, `AnimationIntegration`, `OptimizedAnimationIntegration`, `AnimationFrameManager`, and `CameraBehavior` are present in the codebase but are not currently integrated into the main animation or UI flow.
- Future work may reintroduce these modules for advanced cinematic camera moves and orchestration.

**2024 Update:** All camera/cinematic logic is now handled by [`EnhancedCameraBehavior.ts`](../src/components/map/utils/EnhancedCameraBehavior.ts) and [`CinematicController.ts`](../src/components/map/animation/CinematicController.ts). Do not use `CameraBehavior` or camera helpers in `MapHelpers.ts`. All map transitions must use `bearing: 0` (north-up). The vehicle marker rotates, not the map.

## Vehicle Marker Architecture

### DOM-Based Vehicle Implementation

1. **Direct DOM Manipulation (JS-Only for Positioning)**
   - Use only JS to set the marker's position via `transform`.
   - Example:
   ```typescript
   const vehicleMarker = document.getElementById('direct-vehicle-marker');
   if (vehicleMarker) {
     vehicleMarker.style.transform = `translate(-50%, -50%) translate3d(${x}px, ${y}px, 0) rotate(${bearing}deg)`;
   }
   ```

2. **Element Hierarchy**
   - Vehicle marker must be a direct child of the map container.
   - Maintain proper z-index for visibility.

3. **Marker Visibility Management**
   - Use JS to set `display`, `visibility`, and `opacity`.
   - Example:
   ```typescript
       vehicleMarker.style.display = 'block';
       vehicleMarker.style.visibility = 'visible';
       vehicleMarker.style.opacity = '1';
   ```

4. **CSS Warning**
   - Do **not** use CSS like:
   ```css
   .vehicle-marker {
     left: 0 !important;
     top: 0 !important;
     transform: translate(-50%, -50%) !important;
     }
   ```
   - This will override JS positioning and break animation.

## Animation Techniques

### Direct Animation Approach

1. **Point-Index-Based Animation**
   - Use route point index incrementing rather than time-based calculations
   - Advance vehicle position based on direct point index increments
   - Example:
   ```typescript
   const animateVehicle = () => {
     try {
       // Increment point index directly
       currentPointIndexRef.current += 1;
       
       // Get current position from route points
       const currentPosition = routePoints[currentPointIndexRef.current];
       
       // Update vehicle position
       updateVehiclePosition(currentPosition);
       
       // Force visibility for reliability
       forceVehicleVisibility();
       
     } catch (error) {
       AnimationDebugTools.log('Animation error:', error);
     }
     
     // Continue animation if not complete
     if (currentPointIndexRef.current < routePoints.length - 1) {
       animationFrameIdRef.current = requestAnimationFrame(animateVehicle);
     } else {
       completeAnimation();
     }
   };
   ```

2. **Animation Frame Timing**
   - Implement frame delay of 100ms for natural movement speed
   - Use `requestAnimationFrame` for the main animation loop
   - Store timestamp for managing delay between frames
   - Example:
   ```typescript
   const FRAME_DELAY_MS = 100;
   const lastFrameTimeRef = useRef<number>(0);
   
   const animateWithDelay = (timestamp: number) => {
     if (timestamp - lastFrameTimeRef.current >= FRAME_DELAY_MS) {
       // Animation logic
       currentPointIndexRef.current += 1;
       updateVehiclePosition(routePoints[currentPointIndexRef.current]);
       
       // Update timestamp
       lastFrameTimeRef.current = timestamp;
     }
     
     // Continue animation
     animationFrameIdRef.current = requestAnimationFrame(animateWithDelay);
   };
   ```

### Position Calculation

1. **Coordinates to Pixels**
   - Use map projection to convert coordinates to pixel positions
   - Update translation using CSS transform
   - Example:
   ```typescript
   const updateVehiclePosition = (coordinates) => {
     const map = mapRef.current;
     if (!map) return;
     
     // Convert coordinates to pixel position
     const pixelPosition = map.project(coordinates);
     
     // Apply position with transform for better performance
     const vehicleMarker = document.getElementById('direct-vehicle-marker');
     if (vehicleMarker) {
       vehicleMarker.style.transform = `translate(${pixelPosition.x}px, ${pixelPosition.y}px)`;
     }
   };
   ```

2. **Route Point Processing**
   - Process route points to ensure suitable density for smooth animation
   - Add interpolation when points are too far apart
   - Example:
   ```typescript
   const processRoutePoints = (originalPoints) => {
     const processedPoints = [];
     
     for (let i = 0; i < originalPoints.length - 1; i++) {
       const current = originalPoints[i];
       const next = originalPoints[i + 1];
       
       // Add the current point
       processedPoints.push(current);
       
       // Calculate distance between points
       const distance = calculateDistance(current, next);
       
       // If distance is too large, add interpolated points
       if (distance > MAX_POINT_DISTANCE) {
         const pointsToAdd = Math.floor(distance / IDEAL_POINT_DISTANCE);
         
         for (let j = 1; j <= pointsToAdd; j++) {
           const fraction = j / (pointsToAdd + 1);
           
           // Linear interpolation between points
           const interpolatedPoint = [
             current[0] + (next[0] - current[0]) * fraction,
             current[1] + (next[1] - current[1]) * fraction
           ];
           
           processedPoints.push(interpolatedPoint);
         }
       }
     }
     
     // Add the last point
     processedPoints.push(originalPoints[originalPoints.length - 1]);
     
     return processedPoints;
   };
   ```

## Animation State Management

### Animation Control

1. **State Initialization**
   - Reset all animation state before starting new animations
   - Initialize refs with starting values
   - Example:
   ```typescript
   const initializeAnimationState = () => {
     // Reset point index
     currentPointIndexRef.current = 0;
     
     // Reset timestamp
     lastFrameTimeRef.current = 0;
     
     // Set animation flag
     isAnimatingRef.current = true;
     
     // Clear existing animation frame if any
     if (animationFrameIdRef.current !== null) {
       cancelAnimationFrame(animationFrameIdRef.current);
       animationFrameIdRef.current = null;
     }
   };
   ```

2. **Animation Control Functions**
   - Implement clean start, pause, resume, and stop functions
   - Ensure all state is properly reset between animations
   - Example:
   ```typescript
   const startAnimation = () => {
     // Initialize animation state
     initializeAnimationState();
     
     // Ensure vehicle is visible
     forceVehicleVisibility();
     
     // Start animation loop
     animationFrameIdRef.current = requestAnimationFrame(animateWithDelay);
     
     // Set React state for UI updates
     setIsAnimating(true);
   };
   
   const pauseAnimation = () => {
     // Stop animation frame
     if (animationFrameIdRef.current !== null) {
       cancelAnimationFrame(animationFrameIdRef.current);
       animationFrameIdRef.current = null;
     }
     
     // Update animation flag
     isAnimatingRef.current = false;
     
     // Update React state for UI
     setIsAnimating(false);
   };
   
   const resumeAnimation = () => {
     // Update animation flag
     isAnimatingRef.current = true;
     
     // Restart animation loop
     animationFrameIdRef.current = requestAnimationFrame(animateWithDelay);
     
     // Update React state for UI
     setIsAnimating(true);
   };
   
   const stopAnimation = () => {
     // Stop animation frame
     if (animationFrameIdRef.current !== null) {
       cancelAnimationFrame(animationFrameIdRef.current);
       animationFrameIdRef.current = null;
     }
     
     // Reset animation state
     currentPointIndexRef.current = 0;
     isAnimatingRef.current = false;
     
     // Update React state for UI
     setIsAnimating(false);
   };
   ```

### Animation Monitoring

1. **Stall Detection**
   - Implement monitoring system to detect and fix stalled animations
   - Track point index changes to identify stalls
   - Example:
   ```typescript
   const monitorAnimation = () => {
     // Store previous values for comparison
     const prevPointIndex = monitorStateRef.current.pointIndex;
     
     // Update monitoring state with current values
     monitorStateRef.current = {
       pointIndex: currentPointIndexRef.current,
       timestamp: Date.now(),
       isAnimating: isAnimatingRef.current
     };
     
     // Check for stalled animation
     if (isAnimatingRef.current && 
         prevPointIndex === currentPointIndexRef.current &&
         currentPointIndexRef.current < routePoints.length - 1) {
       
       // Log the stall
       AnimationDebugTools.log('Animation stalled, attempting recovery');
       
       // Recovery action: force move to next point
       currentPointIndexRef.current += 1;
       updateVehiclePosition(routePoints[currentPointIndexRef.current]);
       forceVehicleVisibility();
     }
     
     // Continue monitoring
     monitorTimeoutRef.current = setTimeout(monitorAnimation, 1000);
   };
   ```

2. **Error Recovery**
   - Implement automatic recovery from animation errors
   - Use try/catch blocks in animation frames
   - Example:
   ```typescript
   const animateFrameWithRecovery = (timestamp) => {
     try {
       // Normal animation logic
       if (timestamp - lastFrameTimeRef.current >= FRAME_DELAY_MS) {
         currentPointIndexRef.current += 1;
         updateVehiclePosition(routePoints[currentPointIndexRef.current]);
         lastFrameTimeRef.current = timestamp;
       }
     } catch (error) {
       // Error recovery
       AnimationDebugTools.log('Animation error, attempting recovery:', error);
       
       // Try to fix by skipping problematic point
       currentPointIndexRef.current += 1;
       
       // Check if we've reached the end
       if (currentPointIndexRef.current >= routePoints.length) {
         completeAnimation();
         return;
       }
       
       // Try to continue from next point
       try {
         updateVehiclePosition(routePoints[currentPointIndexRef.current]);
       } catch (secondError) {
         // If recovery fails, stop animation
         AnimationDebugTools.log('Recovery failed, stopping animation:', secondError);
         stopAnimation();
         return;
       }
     }
     
     // Continue animation if not complete
     if (currentPointIndexRef.current < routePoints.length - 1 && isAnimatingRef.current) {
       animationFrameIdRef.current = requestAnimationFrame(animateFrameWithRecovery);
     } else {
       completeAnimation();
     }
   };
   ```

## Vehicle Appearance

### Visual Properties

1. **Size and Scale**
   - Vehicle size should be appropriate for current zoom level
   - Use consistent scale: 30x30px base size
   - Example:
   ```typescript
   const updateVehicleScale = (zoomLevel) => {
     const vehicleMarker = document.getElementById('direct-vehicle-marker');
     if (!vehicleMarker) return;
     
     // Base size
     const baseSize = 30;
     
     // Scale factor based on zoom
     let scaleFactor = 1;
     if (zoomLevel <= 8) {
       scaleFactor = 0.8;  // Smaller at lower zoom
     } else if (zoomLevel >= 14) {
       scaleFactor = 1.2;  // Larger at higher zoom
     }
     
     // Apply scale
     const size = baseSize * scaleFactor;
     vehicleMarker.style.width = `${size}px`;
     vehicleMarker.style.height = `${size}px`;
   };
   ```

2. **Rotation**
   - Rotate vehicle icon to match direction of travel
   - Calculate bearing from consecutive points
   - Example:
   ```typescript
   const updateVehicleRotation = (currentIndex, routePoints) => {
     // Need at least two points to calculate direction
     if (currentIndex < 1 || currentIndex >= routePoints.length) return;
     
     const vehicleMarker = document.getElementById('direct-vehicle-marker');
     if (!vehicleMarker) return;
     
     // Get current and previous points
     const currentPoint = routePoints[currentIndex];
     const prevPoint = routePoints[currentIndex - 1];
     
     // Calculate bearing between points (in degrees)
     const bearing = calculateBearing(prevPoint, currentPoint);
     
     // Apply rotation transform
     vehicleMarker.style.transform += ` rotate(${bearing}deg)`;
   };
   
   const calculateBearing = (start, end) => {
     const startLat = toRadians(start[1]);
     const startLng = toRadians(start[0]);
     const endLat = toRadians(end[1]);
     const endLng = toRadians(end[0]);
     
     const dLng = endLng - startLng;
     
     const y = Math.sin(dLng) * Math.cos(endLat);
     const x = Math.cos(startLat) * Math.sin(endLat) -
               Math.sin(startLat) * Math.cos(endLat) * Math.cos(dLng);
     
     let bearing = toDegrees(Math.atan2(y, x));
     bearing = (bearing + 360) % 360;
     
     return bearing;
   };
   ```

### Animation Effects

1. **Movement Transitions**
   - Add slight transition effect for smoother movement
   - Use transition property for position changes
   - Example:
   ```typescript
   const setupVehicleTransitions = () => {
     const vehicleMarker = document.getElementById('direct-vehicle-marker');
     if (!vehicleMarker) return;
     
     // Add transition for smoother movement
     // Note: Keep transition short to maintain responsiveness
     vehicleMarker.style.transition = 'transform 100ms ease-out';
   };
   ```

2. **Vehicle Indicators**
   - Add subtle indicators for vehicle state (moving, paused)
   - Use CSS classes for different states
   - Example:
   ```typescript
   const updateVehicleState = (isMoving) => {
     const vehicleMarker = document.getElementById('direct-vehicle-marker');
     if (!vehicleMarker) return;
     
     // Remove existing state classes
     vehicleMarker.classList.remove('vehicle-moving', 'vehicle-paused');
     
     // Add appropriate class
     if (isMoving) {
       vehicleMarker.classList.add('vehicle-moving');
     } else {
       vehicleMarker.classList.add('vehicle-paused');
     }
   };
   ```

## Performance Optimization

1. **Efficient DOM Updates**
   - Minimize DOM manipulations to essential properties
   - Batch style updates when possible
   - Use transform for position updates (instead of left/top)
   - Example:
   ```typescript
   // Efficient position update with single style change
   const updateVehiclePositionEfficient = (pixelX, pixelY, rotation) => {
     const vehicleMarker = document.getElementById('direct-vehicle-marker');
     if (!vehicleMarker) return;
     
     // Combine translation and rotation in single transform
     vehicleMarker.style.transform = 
       `translate(${pixelX}px, ${pixelY}px) rotate(${rotation}deg)`;
   };
   ```

2. **Animation Performance**
   - Keep animation frame functions lightweight
   - Defer non-critical updates outside the animation loop
   - Use performance monitoring during development
   - Example:
   ```typescript
   // Performance monitoring wrapper
   const withPerformanceTracking = (fn, label) => {
     return (...args) => {
       if (process.env.NODE_ENV === 'development') {
         const startTime = performance.now();
         const result = fn(...args);
         const endTime = performance.now();
         
         const duration = endTime - startTime;
         if (duration > 16) {  // Frame budget (60fps)
           AnimationDebugTools.log(`Performance warning: ${label} took ${duration.toFixed(2)}ms`);
         }
         
         return result;
       } else {
         return fn(...args);
       }
     };
   };
   
   // Apply to animation frame function
   const animateFrameOptimized = withPerformanceTracking(
     (timestamp) => {
       // Optimized animation logic
     },
     'Animation frame'
   );
   ```

## Component Integration

1. **Lifecycle Management**
   - Implement proper cleanup in component unmount
   - Cancel all animations and clear timeouts
   - Remove DOM elements created during animation
   - Example:
   ```typescript
   useEffect(() => {
     // Setup animation components
     
     return () => {
       // Cancel animation frame
       if (animationFrameIdRef.current !== null) {
         cancelAnimationFrame(animationFrameIdRef.current);
         animationFrameIdRef.current = null;
       }
       
       // Clear monitoring timeout
       if (monitorTimeoutRef.current !== null) {
         clearTimeout(monitorTimeoutRef.current);
         monitorTimeoutRef.current = null;
       }
       
       // Remove vehicle marker if it exists
       const vehicleMarker = document.getElementById('direct-vehicle-marker');
       if (vehicleMarker && vehicleMarker.parentNode) {
         vehicleMarker.parentNode.removeChild(vehicleMarker);
       }
     };
   }, []);
   ```

2. **Animation State Props**
   - Provide clear props for controlling animation from parent
   - Implement prop change handlers for external control
   - Example:
   ```tsx
   interface VehicleAnimatorProps {
     isPlaying: boolean;
     routePoints: [number, number][];
     speed: number;
     onComplete: () => void;
     onProgressChange?: (progress: number) => void;
   }
   
   const VehicleAnimator: React.FC<VehicleAnimatorProps> = ({
     isPlaying,
     routePoints,
     speed,
     onComplete,
     onProgressChange
   }) => {
     // Implementation that responds to prop changes
     
     // Effect to handle isPlaying changes
     useEffect(() => {
       if (isPlaying && !isAnimatingRef.current) {
         startAnimation();
       } else if (!isPlaying && isAnimatingRef.current) {
         pauseAnimation();
       }
     }, [isPlaying]);
     
     // Rest of component
   };
   