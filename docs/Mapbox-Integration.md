# Mapbox GL Integration

This document outlines the approach we've taken to integrate Mapbox GL with our TypeScript application, addressing common integration challenges and providing guidelines for consistent usage.

## Integration Challenges

When integrating the Mapbox GL library with TypeScript, we encountered several challenges:

1. **Module Import Issues**: The `mapbox-gl` library imports the `@mapbox/point-geometry` dependency using default imports, but the dependency is exported using `export =` syntax, which requires the `esModuleInterop` flag.

2. **Type Declaration Conflicts**: Despite having `esModuleInterop` enabled in our tsconfig.json, TypeScript still reports errors when importing the library directly.

3. **Consistency Across Codebase**: Different components were importing and using Mapbox GL in inconsistent ways, leading to maintenance challenges.

## Solution: The Adapter Pattern

We've implemented an adapter pattern to address these challenges:

1. Created a centralized adapter file (`src/utils/mapbox-adapter.ts`) that:
   - Uses `require()` to bypass TypeScript's module checking
   - Provides type-safe interfaces for common Mapbox operations
   - Creates a consistent API with proper TypeScript typings

2. Defined consistent types (`MapInstance`, etc.) to use throughout the application

3. Added utility functions for common Mapbox operations

## Usage Guidelines

### Importing Mapbox GL

```typescript
// Always import from our adapter instead of directly from mapbox-gl
import mapboxAdapter, { MapInstance } from '@/utils/mapbox-adapter';

// If you need the raw mapboxgl object
const mapboxgl = mapboxAdapter.getMapboxGL();
```

### Function Parameters

```typescript
// Use MapInstance instead of mapboxgl.Map for function parameters
function myMapFunction(map: MapInstance) {
  // Function implementation
}
```

### Creating Map Instances

```typescript
// Use the adapter's createMap function
const map = mapboxAdapter.createMap('map-container', {
  style: 'mapbox://styles/mapbox/streets-v11',
  center: [-74.5, 40],
  zoom: 9
});
```

### Other Common Operations

```typescript
// Creating markers
const marker = mapboxAdapter.createMarker()
  .setLngLat([-74.5, 40])
  .addTo(map);

// Creating popups
const popup = mapboxAdapter.createPopup()
  .setLngLat([-74.5, 40])
  .setHTML('<h3>Hello World!</h3>')
  .addTo(map);
```

## Benefits

- **Clean Imports**: No TypeScript errors from the mapbox-gl module
- **Type Safety**: Proper typings for map functions and properties
- **Consistency**: Standard API for use across the application
- **Maintainability**: No node_modules modifications required
- **Compatibility**: Works with various TypeScript module settings

## Migration

When migrating existing code to use the adapter:

1. Replace direct imports with adapter imports
2. Update function parameter types from `mapboxgl.Map` to `MapInstance`
3. Use adapter utility functions where appropriate
4. Add comments explaining the change if necessary

## Future Considerations

When upgrading Mapbox GL in the future:

1. Check if the import issue has been resolved in newer versions
2. Test the adapter with the new version before updating the entire codebase
3. If the issue is resolved, consider a gradual migration back to direct imports
4. Update the adapter to support any new features in the library 