# Morocco Travel App - User Story

## Meet <PERSON>: Planning a Trip to Morocco

<PERSON> is a 32-year-old travel enthusiast who loves exploring new cultures. She's planning her first trip to Morocco but has limited knowledge about the country beyond a few famous cities like Marrakech and Casablanca. She wants an authentic experience that balances cultural immersion, natural beauty, and practical logistics.

## Initial Exploration

Sarah opens the Morocco Travel App on her laptop. She's greeted with a beautiful map of Morocco and a prompt to select her interests. She selects:

- Cultural experiences
- Historical sites
- Local cuisine
- Photography opportunities
- Moderate hiking

After selecting her interests and indicating she has 8 days for her trip, the app suggests a preliminary route: Casablanca → Rabat → Fes → Chefchaouen → Marrakech.

## Planning the Journey

As <PERSON> looks at the suggested route, she notices color-coded POI clusters on the map. She clicks on the Marrakech cluster and sees it contains 15 points of interest categorized by type:

- Cultural sites (5)
- Historical landmarks (4)
- Restaurants & cafés (3)
- Shopping experiences (2)
- Viewpoints (1)

She uses the category filters to focus on cultural and historical sites first, then adds a few restaurants to her saved list. She's impressed by how easily she can filter POIs by her interests.

## Starting the Journey Animation

Eager to visualize her trip, Sarah clicks "Simulate Journey" to see how her route will unfold. The app smoothly zooms out to provide a country-wide view of Morocco, with her route highlighted.

The vehicle animation begins as a small car icon starting in Casablanca. She notices:

1. **Contextual Rhythm:** The animation isn't moving at a uniform pace - it's dynamically adapting to the surroundings:
   - It slows down when approaching cities
   - It speeds up slightly when crossing larger desert stretches
   - The camera occasionally zooms out to show broader context then zooms back in for details

2. **Progressive Awareness:** As the vehicle approaches the outskirts of Rabat:
   - A subtle direction indicator appears pointing toward an upcoming POI
   - A notification slides in: "Approaching: Hassan Tower (3.2 km away)"
   - The notification includes a small "Details" button which she clicks to learn more about this historical minaret

3. **Visual Enhancements:** Sarah notices several visual cues enriching her experience:
   - The vehicle has a subtle pulsing effect making it easy to track
   - Important landmarks have gentle highlighting animations
   - Notifications fade in and out smoothly without disrupting the flow
   - When approaching cultural regions, the map adjusts its pitch to provide a more immersive view

## Discovery Experience

As the vehicle animation continues toward Fes, Sarah experiences:

1. **Batch POI Discoveries:** Rather than being overwhelmed with individual notifications for each POI:
   - Upon entering the Fes region, a notification appears: "Entering Fes - 8 points of interest nearby"
   - A compact summary shows the top 3 POIs categorized by her interests
   - A "View All" button allows her to explore the complete list

2. **Adaptive Detection:** She notices the app intelligently highlights POIs:
   - In dense areas like medinas, only the most relevant POIs to her interests are highlighted
   - In sparser regions, more POIs are shown with a wider detection radius
   - High-importance POIs matching her specific interests receive special visual treatment

3. **Importance Scoring:** Sarah appreciates that the POIs shown align well with her interests:
   - Cultural sites and historical landmarks are prominently featured
   - Restaurant recommendations focus on authentic Moroccan cuisine (matching her interests)
   - Photography viewpoints are highlighted when passing scenic areas

## Interactive Exploration

While watching the animation, Sarah:

1. **Pauses for Exploration:** When the vehicle approaches Chefchaouen (the blue city):
   - She sees a notification about this unique location
   - She clicks "Explore" to pause the animation
   - The camera smoothly transitions to show the city in more detail
   - A cluster of POIs appears, and she can click on individual points

2. **Contextual Information:** While exploring Chefchaouen:
   - The app shows estimated time needed to explore the highlighted POIs
   - A legend indicates which streets are pedestrian-only
   - Hover tooltips on POIs show opening hours and brief descriptions

3. **Camera Control:** Sarah enjoys the flexibility to:
   - Manually zoom in to see street-level details
   - Pan around to explore nearby areas
   - Reset to the suggested viewpoint with a single click
   - Toggle between different viewing modes (focus, nearby, context)

## Travel Planning Insights

As the journey animation concludes in Marrakech, Sarah receives valuable insights:

1. **Time-Based Recommendations:**
   - The app shows estimated driving times between destinations
   - It suggests where overnight stays make the most sense
   - It highlights segments that might be challenging in specific weather or traffic conditions

2. **Balanced Itinerary Feedback:**
   - A balance indicator shows her itinerary is well-rounded but slightly heavy on urban experiences
   - It suggests adding a desert excursion for better balance
   - It highlights that her food experiences are concentrated in cities, suggesting some rural culinary stops

3. **Alternative Routes:**
   - When zoomed out, she can see three alternative route options:
     - A "Scenic" route that takes longer but offers more natural beauty
     - A "Fast" route optimized for efficiency
     - A "Cultural" route that maximizes exposure to different regions

## Final Planning

With her journey animation complete, Sarah makes final adjustments:

1. She adds a day trip to the Atlas Mountains based on the app's suggestion
2. She adjusts her time in Casablanca slightly, allocating more time to Fes based on the POI density
3. She saves her customized itinerary with notes on specific POIs she wants to prioritize

The app generates a day-by-day breakdown of her trip with:
- Driving directions and estimated times
- Recommendations for when to visit each POI based on crowds and lighting (for photography)
- Suggestions for local experiences based on her interest profile

## Outcome

Sarah feels confident and excited about her upcoming Morocco trip. Thanks to the Morocco Travel App, she has:

1. **Discovered hidden gems** beyond the typical tourist spots
2. **Created a balanced itinerary** that reflects her personal interests
3. **Gained contextual understanding** of how the destinations relate to each other
4. **Set realistic expectations** for travel times and regional differences
5. **Prepared effectively** with practical insights and local knowledge

The app's immersive, educational animation experience has given her a preview of her journey that static maps or text descriptions could never provide, making her feel more connected to her destination before even arriving. 