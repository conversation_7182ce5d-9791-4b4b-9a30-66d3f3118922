# Project Structure: Morocco Travel Application
## Universal Framework for Travel Planning

**Version**: 2.0
**Last Updated**: January 29, 2025

## Overview

This document outlines the modular architecture of the Morocco Travel Application, which has evolved into a universal framework for licensing to tour operators, travel agencies, and tourism boards. The application provides an interactive travel planning experience featuring animated routes, point of interest discovery, immersive map visualization, and comprehensive trip planning tools.

## Core Modules

### Map Animation System

The animation system has been modularized into specialized components:

```
src/components/map/
├── animation/                    # Animation modules
│   ├── RouteAnimator.ts          # Core animation calculations
│   ├── RouteDataLoader.ts        # Route data loading and fallbacks
│   ├── AnimationDebugTools.tsx   # Debug UI components and utilities
│   ├── AnimationController.tsx   # Animation coordination
│   ├── AnimationStyles.tsx       # Animation-related styles
│   ├── AnimationUtils.ts         # Shared utility functions
│   ├── AwarenessIndicators.ts    # POI awareness indicators
│   ├── ContextualRhythm.ts       # Camera behavior during travel
│   └── SpeedController.ts        # Vehicle speed management
├── vehicle/
│   ├── DirectVehicleMarker.tsx   # Vehicle marker component
│   └── VehicleMarker.tsx         # Legacy vehicle marker
```

### Map Rendering System

The map rendering system handles the visual components of the map:

```
src/components/map/
├── layers/
│   └── RouteLayer.tsx            # Route line rendering
├── markers/
│   ├── DestinationMarker.tsx     # City/destination markers
│   ├── MarkerManager.tsx         # Marker coordination
│   └── POIMarker.tsx             # Point of interest markers
├── clusters/
│   ├── ClusterGenerator.ts       # Clustering algorithm
│   ├── ClusterMarkers.tsx        # Cluster rendering
│   └── ClusterModal.tsx          # Cluster interaction
├── overlays/
│   ├── CityTagsOverlay.tsx       # City label overlays
│   ├── FeedbackOverlay.tsx       # User feedback system
│   ├── POINotification.tsx       # POI discovery notifications
│   └── POIOverlay.tsx            # POI information display
├── controls/
│   └── MapControls.tsx           # Map control components
├── utils/
│   ├── EnhancedCameraBehavior.ts # Camera movement logic
│   ├── MapHelpers.ts             # Map utility functions (constants, non-behavioral)
│   ├── NotificationManager.tsx   # User notification system
│   ├── POIDiscovery.tsx          # POI discovery logic
│   ├── RoutePoiDiscovery.ts      # Route-based POI discovery
│   └── types.ts                  # Shared type definitions
```

### Explore Features

The explore section allows users to browse and select destinations:

```
src/components/explore/
├── ExploreFilters.tsx            # Filter UI for destinations/POIs
├── ExploreFooter.tsx             # Footer for explore view
├── ExplorePOIList.tsx            # List of points of interest
└── ExploreSidebar.tsx            # Sidebar for exploration
```

### Home View

Home-related components for the landing experience:

```
src/components/home/
├── DestinationsList.tsx          # List of destinations
├── MapSection.tsx                # Map display for home view
└── RouteFooter.tsx               # Footer with route controls
```

### Journey Experience

Journey components for the travel experience:

```
src/components/journey/
└── JourneySidebar.tsx            # Sidebar during journey
```

### Trip Planning System

Comprehensive trip planning and itinerary management:

```
src/components/layout/
├── LeftPane.tsx                  # POI discovery and filtering
├── RightPane.tsx                 # Trip planning and itinerary management
└── TopBar.tsx                    # Navigation and journey controls

src/components/itinerary/
├── ItineraryPanel.tsx            # Day-by-day itinerary display
├── UnifiedTripPlanner.tsx        # Comprehensive trip planning interface
└── TripOverview.tsx              # Trip statistics and overview

src/components/map/
├── CitySelectionOverlay.tsx      # City selection with day allocation
├── JourneyParametersForm.tsx     # Travel preferences and style
└── PreArrangedJourneyLinks.tsx   # Pre-built journey templates
```

### Smart UI System

Adaptive user interface that responds to user workflow:

```
src/hooks/
└── useSmartUI.ts                 # Smart UI state management and transitions
```

### Shared Components

```
src/components/
├── ExploreMap.tsx                # Main map implementation
├── MapComponent.tsx              # Base map component
├── TravelAnimator.tsx            # Core travel animation
├── map/RouteConnector.tsx        # Route visualization (now located in map directory)
└── VehicleSelector.tsx           # Vehicle selection UI
```

## Major Feature Sets

### POI (Points of Interest) System

The POI system manages discovery and display of interesting locations:

- **Discovery Logic**: `src/components/map/utils/POIDiscovery.tsx`
- **Route-Based Discovery**: `src/components/map/utils/RoutePoiDiscovery.ts` 
- **Clustering**: `src/components/map/clusters/ClusterGenerator.ts`
- **Notifications**: `src/components/map/overlays/POINotification.tsx`
- **Display**: `src/components/map/overlays/POIOverlay.tsx`

### Route Animation System

The route animation system creates a dynamic travel experience:

- **Animation Core**: `src/components/map/animation/RouteAnimator.ts`
- **Data Loading**: `src/components/map/animation/RouteDataLoader.ts`
- **Camera Behavior**: `src/components/map/utils/EnhancedCameraBehavior.ts`
- **Coordination**: `src/components/map/animation/AnimationController.tsx`

### Map Rendering Components

The map rendering system visualizes geographic data:

- **Base Map**: `src/components/ExploreMap.tsx` 
- **Route Layer**: `src/components/map/layers/RouteLayer.tsx`
- **Markers**: `src/components/map/markers/MarkerManager.tsx`
- **Destination Markers**: `src/components/map/DirectDestinationMarkers.tsx`
- **POI Markers**: `src/components/map/DirectPOIMarkers.tsx`

### Admin System

Admin components for managing application data:

```
src/components/admin/
├── AdminDestinations.tsx         # Destination management
├── AdminPOIs.tsx                 # POI management
├── AdminQuotes.tsx               # Quote management
└── AdminUsers.tsx                # User management
```

## Data Models

Key data structures that support the application:

```
src/types/
├── POITypes.ts                   # Canonical source for PointOfInterest, Destination, etc.
├── clusters.ts                   # Cluster data models
├── cultural-regions.ts           # Cultural region definitions
├── destination.ts                # Alias/Re-exporter for Destination from POITypes.ts
└── poi.ts                        # Alias/Re-exporter for PointOfInterest from POITypes.ts
```

## Utility Functions

Shared utility functions:

```
src/utils/
├── cultural-regions.ts           # Cultural region utilities
├── mapUtils.ts                   # Map utility functions
├── markerManager.ts              # Marker management
├── poi-importance.ts             # POI scoring algorithm
├── route-analysis.ts             # Route analysis tools
└── routeUtils.ts                 # Route utility functions
```

## Hooks

Custom React hooks for reusable logic:

```
src/hooks/
├── use-animation.tsx             # Animation hooks
├── use-mapbox.tsx                # Mapbox integration
├── useDestinationManagement.tsx  # Destination state management
├── useMapControls.ts             # Map control logic
└── usePOIManagement.tsx          # POI state management
```

## Architecture Decisions

### Modular Design

The application follows a modular architecture with these advantages:

1. **Separation of Concerns**: Each module has a specific responsibility
2. **Reusability**: Components can be reused across different contexts
3. **Maintainability**: Easier to debug and maintain smaller files
4. **Testability**: Modules can be tested independently

### Key Design Patterns

1. **Component Composition**: Building complex UIs from simple components
2. **Custom Hooks**: Extracting reusable stateful logic
3. **Render Props**: Sharing rendering logic between components
4. **Context API**: Managing global state (auth, theme, etc.)
5. **React Refs**: Managing imperative DOM operations and animation

### Animation Architecture

The animation system is built on these principles:

1. **Decoupled Logic**: Animation calculations separate from rendering
2. **Frame-based Animation**: Using requestAnimationFrame for smooth animations
3. **State Management**: Combination of React state and refs
4. **Fallback Mechanisms**: Multiple strategies for route data
5. **Debug Tooling**: Built-in debugging capabilities

## Development Workflow

When working with the codebase:

1. **Animation Changes**: Focus on modules in `src/components/map/animation/`
2. **Map Rendering**: Modify appropriate map layer components
3. **UI Enhancements**: Work with relevant UI components
4. **Debug Issues**: Use the AnimationDebugTools components and browser console 