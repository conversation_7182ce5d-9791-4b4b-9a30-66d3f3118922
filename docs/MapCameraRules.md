# Map Camera Controls - Architecture Rules

This document establishes the standards for camera movement and behavior within our Morocco Travel application. Following these guidelines ensures a consistent and smooth user experience during map interactions.

## Code Consolidation (April 2024)

**Previous camera utilities in `MapHelpers.ts` are deprecated.**

**Use this pattern for all camera transitions:**
```typescript
import { EnhancedCameraBehavior } from '../src/components/map/utils/EnhancedCameraBehavior';
// ...
EnhancedCameraBehavior.getInstance().followVehicle(center, 0, { forceUpdate: true });
```

**For advanced cinematic sequences:**
```typescript
import CinematicController from '../src/components/map/animation/CinematicController';
// ...
CinematicController.getInstance().startCinematic(...);
```

Camera control logic is primarily handled by `EnhancedCameraBehavior.ts` and `CinematicController.ts`.
The `MapHelpers.ts` file may still contain camera-related constants (like `CAMERA_SETTINGS`) or non-behavioral utility functions, but direct camera manipulation functions from it are deprecated. The `MapHelpers.tsx` file has been removed.

### Key Advantages:
- Single source of truth for camera utilities
- Proper separation of animation logic from React components
- Consistent implementation of camera standards
- Improved type safety and maintainability
- Elimination of duplicate implementations

### Implementation Examples:

```typescript
// Using camera settings constants
import { CAMERA_SETTINGS } from '../utils/MapHelpers';

// Apply context-specific camera settings
map.easeTo({
  ...CAMERA_SETTINGS.CITY_APPROACH,
  center: currentPosition,
  essential: true
});
```

## Zoom Level Standards

### Defined Zoom Levels

These zoom levels MUST be used consistently across the application:

```typescript
export const ZOOM_LEVELS = {
  COUNTRY_OVERVIEW: 5,    // Full country view
  ROUTE_TRAVEL: 8,        // Journey animation
  REGION_VIEW: 10,        // Regional exploration
  CITY_EXPLORATION: 14,   // City-level exploration
  POI_DETAIL: 16          // Point of interest detail
};
```

### Context-Specific Zoom Usage

1. **Route Travel Animation**
   - Always use zoom level 8 during route travel animations
   - This zoom level provides enough geographic context while showing movement
   - Example: `map.flyTo({ zoom: ZOOM_LEVELS.ROUTE_TRAVEL })`

2. **City Exploration**
   - Use zoom level 14 when entering a city or displaying city POIs
   - This zoom shows street-level detail while maintaining broader context
   - Example: `map.easeTo({ zoom: ZOOM_LEVELS.CITY_EXPLORATION })`

3. **POI Detail View**
   - Use zoom level 16 when focusing on a specific POI
   - Provides close-up detail of the location and immediate surroundings
   - Example: `map.flyTo({ zoom: ZOOM_LEVELS.POI_DETAIL })`

## Camera Orientation Rules

### Bearing Standards

1. **North-Facing Orientation**
   - During route travel, ALWAYS maintain fixed north orientation (bearing: 0)
   - Helps users maintain consistent spatial awareness during journey
   - Example: `map.easeTo({ bearing: 0 })`

2. **POI-Specific Bearings**
   - When highlighting specific POIs, bearings MAY be adjusted for better views
   - Should be cleared with transition back to north when resuming travel
   - Example: `map.rotateTo(0, { duration: 1000 })`

### Pitch Standards

1. **3D Perspective**
   - Maintain 45° pitch for standard 3D perspective
   - Provides depth without obscuring map details
   - Example: `map.setPitch(45)`

2. **Pitch Transitions**
   - Use smooth transitions when changing pitch
   - Reset to 45° when resuming journey
   - Example: `map.easeTo({ pitch: 45, duration: 800 })`

## Camera Movement Dynamics

### Transition Timing

1. **Duration Standards**
   - Short movements: 500ms minimum duration
   - Long-distance transitions: 1500-2000ms
   - Dramatic zoom changes: 1000-1500ms
   - Example: `map.flyTo({ center: newCenter, duration: 1500 })`

2. **Easing Functions**
   - Use cubic easing for most camera movements
   - Use quadratic easing for subtle movements
   - Example: 
   ```typescript
   map.easeTo({
     center: newCenter,
     easing: (t) => easeInOutCubic(t),
     duration: 1000
   });
   ```

### Essential Animations

1. **Animation Protection**
   - Mark critical camera animations as `essential: true`
   - Prevents user interactions from canceling animations
   - Example: `map.flyTo({ center: nextPoint, essential: true })`

2. **Animation Interruption**
   - Allow non-critical animations to be interrupted
   - Provide smooth transitions between interrupted states
   - Example: `map.stop() // Stops current camera animation`

## Progressive Zoom Pattern

1. **Context-First Approach**
   - Begin with wider context and progressively zoom in
   - Example sequence:
     1. Start at COUNTRY_OVERVIEW (zoom 5)
     2. Transition to ROUTE_TRAVEL (zoom 8)
     3. Progress to CITY_EXPLORATION (zoom 14) when appropriate
     4. Zoom to POI_DETAIL (zoom 16) only when highlighting specific features

2. **Two-Step Zooming**
   - For dramatic zoom changes, use a two-step approach
   - Example:
   ```typescript
   // Two-step zoom implementation
   const twoStepZoom = (map, finalZoom, finalCenter) => {
     // Step 1: Initial transition to intermediate zoom
     map.flyTo({
       center: finalCenter,
       zoom: Math.floor((map.getZoom() + finalZoom) / 2),
       duration: 1000,
       onComplete: () => {
         // Step 2: Final transition to target zoom
         map.flyTo({
           center: finalCenter,
           zoom: finalZoom,
           duration: 800
         });
       }
     });
   };
   ```

## Camera Behavior during Animation

1. **Camera Tracking**
   - During animation, camera should track vehicle with slight look-ahead
   - Example:
   ```typescript
   // Calculate look-ahead point
   const calculateLookAheadPoint = (currentIndex, routePoints, lookAheadFactor = 5) => {
     const lookAheadIndex = Math.min(currentIndex + lookAheadFactor, routePoints.length - 1);
     return routePoints[lookAheadIndex];
   };
   
   // Apply in animation frame
   map.easeTo({
     center: calculateLookAheadPoint(currentPointIndex, routePoints),
     zoom: ZOOM_LEVELS.ROUTE_TRAVEL,
     duration: 300
   });
   ```

2. **Animation Speed Coordination**
   - Camera movement speed should match vehicle animation speed
   - For faster animations, increase camera transition duration
   - Example:
   ```typescript
   const cameraDuration = baseSpeed * speedMultiplier;
   map.easeTo({
     center: nextPoint,
     duration: cameraDuration
   });
   ```

## Performance Considerations

1. **Optimization Techniques**
   - Limit camera updates to essential changes
   - Batch camera movements when possible
   - Use `requestAnimationFrame` for smooth camera animations

2. **Device-Specific Adjustments**
   - Detect device performance capabilities
   - Reduce animation complexity on lower-end devices
   - Example:
   ```typescript
   // Simplified implementation for performance detection
   const isLowPerformanceDevice = () => {
     return window.navigator.hardwareConcurrency < 4;
   };
   
   // Apply different camera behavior based on device
   const cameraTransitionDuration = isLowPerformanceDevice() 
     ? 300  // Shorter, simpler transitions for low-end devices
     : 1000;  // Full transitions for high-end devices
   ```

## Camera Debugging

1. **Logging Camera States**
   - Log camera transitions for debugging
   - Include zoom, center, bearing, and pitch in logs
   - Example:
   ```typescript
   const logCameraState = (map, label = 'Camera state') => {
     animationLogger.log(label, {
       zoom: map.getZoom(),
       center: map.getCenter(),
       bearing: map.getBearing(),
       pitch: map.getPitch()
     });
   };
   ```

2. **Visual Debug Indicators**
   - Implement visual indicators for camera target points
   - Show look-ahead points during development
   - Example: 
   ```tsx
   // Debug marker component
   {process.env.NODE_ENV === 'development' && (
     <div 
       className="camera-target-debug"
       style={{
         position: 'absolute',
         left: cameraTargetPixel.x,
         top: cameraTargetPixel.y,
         width: '10px',
         height: '10px',
         background: 'red',
         borderRadius: '50%',
         zIndex: 1000
       }}
     />
   )}
   ```

## Camera State Management

1. **Camera State Storage**
   - Store camera state in refs, not React state
   - Example:
   ```typescript
   const cameraCenterRef = useRef(initialCenter);
   const cameraZoomRef = useRef(ZOOM_LEVELS.COUNTRY_OVERVIEW);
   
   // Update refs directly for camera operations
   const updateCameraState = (center, zoom) => {
     cameraCenterRef.current = center;
     cameraZoomRef.current = zoom;
   };
   ```

2. **Camera State Recovery**
   - Implement camera state recovery mechanisms
   - Restore preferred camera view after interruptions
   - Example:
   ```typescript
   // Store preferred camera state
   const storePreferredCameraState = () => {
     localStorage.setItem('preferredCameraState', JSON.stringify({
       center: map.getCenter(),
       zoom: map.getZoom(),
       timestamp: Date.now()
     }));
   };
   
   // Restore camera state if recent
   const restorePreferredCameraState = () => {
     try {
       const storedState = JSON.parse(localStorage.getItem('preferredCameraState'));
       if (storedState && Date.now() - storedState.timestamp < 3600000) {
         map.flyTo({
           center: storedState.center,
           zoom: storedState.zoom,
           duration: 1000
         });
         return true;
       }
     } catch (e) {
       console.error('Failed to restore camera state', e);
     }
     return false;
   };
   ``` 