# Client/Region Onboarding Checklist

This checklist guides you through onboarding a new client, region, or theme for the travel experience platform. Follow each step to ensure a smooth, production-ready rollout.

---

## 1. Data & Configuration
- [ ] **Create Client Config:**
  - Copy and update a config in `src/config/clientConfigs.ts`.
  - Set `id`, `name`, `theme`, `mapSettings`, `poiSettings`, and `featureFlags`.
- [ ] **Add Region Data:**
  - Add destinations and POIs to `regionData` and `pointsOfInterest` files.
  - Ensure each POI/destination has a `clientId` and correct location/city/country fields.
- [ ] **Validate Data Types:**
  - Use `PointOfInterest` and `Destination` types from `src/types/POITypes.ts`.
  - Run type checks to catch schema mismatches.

## 2. Theming & Branding
- [ ] **Create/Update Theme:**
  - Add a new theme in `src/config/themes/` (see `morocco.ts`, `portugal.ts`).
  - Set colors, fonts, logos, and UI element styles.
  - Register the theme in `themes.ts` and `clientConfigs.ts`.
- [ ] **Test Theme Switching:**
  - Use the `ThemeProvider` and `ClientProvider` to verify correct theme application.

## 3. UI & Overlay Modules
- [ ] **Configure Overlays:**
  - Enable/disable overlays (e.g., POI discovery, city panels) in the client config.
  - Add new overlays as React components if needed.
  - Reference overlays in config for pluggability.
- [ ] **Localize UI Text:**
  - Add translations/localized text for the new region/client.
  - Update UI components to use localization where needed.

## 4. Animation & Map Behavior
- [ ] **Review Animation Managers:**
  - Ensure vehicle, camera, and route managers are compatible with new region data.
  - Update camera rules and journey phases in config if needed.
- [ ] **Test POI/City Discovery:**
  - Verify event-driven overlays and notifications work for the new region.

## 5. Integration & Deployment
- [ ] **Set Up Routing:**
  - Update routing (e.g., `App.tsx`, `App.multiclient.tsx`) to support the new client/region.
  - Test switching via URL, context, or selector.
- [ ] **Choose Deployment Model:**
  - Decide on white-label, multi-tenant, or widget deployment.
  - Update build/deploy scripts as needed.

## 6. QA & Testing
- [ ] **Integration Tests:**
  - Add tests for region switching, overlay rendering, and event handling.
- [ ] **Manual QA:**
  - Verify all POIs, destinations, overlays, and theming in the new region.
  - Check for missing data, broken links, or UI glitches.
- [ ] **Performance & Security:**
  - Test performance with large data sets.
  - Validate client data isolation and access control.

## 7. Documentation
- [ ] **Update Docs:**
  - Document any customizations or new overlays in the `docs/` folder.
  - Add onboarding notes for future maintainers.

---

**References:**
- See `docs/Multi-Client-POI-Architecture.md`, `Framework-Offering.md`, `Modularization-Progress.md`, and `Portugal-Theme-Implementation.md` for detailed examples and best practices.
- For overlays and event-driven UI, see `POIDiscoveryFrameworkEvents.md` and `UI-UX-Enhancements.md`.

_Last updated: 2024-06-09_ 