# RoutePoint Interface Documentation

## Overview

The `RoutePoint` interface is a crucial data structure representing points along a route with position information and contextual metadata. It serves as the foundation for animation, navigation, and contextual processing in the application.

## Interface Definition

The `RoutePoint` interface is defined in `src/types/RoutePoint.ts` and includes the following properties:

```typescript
interface RoutePoint {
  // Geographic position as [longitude, latitude]
  position: Position;
  
  // Distance along the route in meters
  distance: number;
  
  // Bearing (direction) in degrees at this point
  bearing: number;
  
  // Terrain type at this location (optional)
  terrain?: TerrainType;
  
  // Elevation in meters (optional)
  elevation?: number;
  
  // Nearest city or location name (optional)
  nearestLocation?: string;
  
  // Nearby points of interest with distances (optional)
  pois?: { id: string; distance: number; }[];
  
  // Index of this point in the route array
  index: number;

  // Additional metadata (optional)
  metadata?: Record<string, any>;
}
```

## RoutePoint Adapter System

The application uses a flexible adapter system (`RoutePointAdapter.ts`) to handle different route point formats and ensure interoperability between components. This adapter system provides:

1. **Type Guards** for validating route point objects:
   - `isDetailedRoutePoint(point: any): boolean` - Checks if an object matches the full `RoutePoint` interface
   - `isSimpleRoutePoint(point: any): boolean` - Checks if an object is a simplified route point with just lat/lng

2. **Conversion Functions** for transforming between formats:
   - `convertSimpleToDetailedRoutePoint()` - Converts simple lat/lng objects to full RoutePoint objects
   - `convertDetailedToSimpleRoutePoint()` - Extracts just lat/lng from full RoutePoint objects
   - `createRoutePointFromPosition()` - Creates a RoutePoint from a basic Position

3. **Safe Access Methods**:
   - `getRoutePointPosition()` - Safely extracts Position from any route point format
   - `safeGetRoutePointPosition()` - More robust version with additional fallback strategies

## Integration with VehicleManager

The VehicleManager doesn't directly use the RoutePoint interface but receives position updates derived from RoutePoints:

```typescript
// VehicleManager.ts methods
public updateVehiclePosition(position: Position, bearing: number = 0): boolean
public updatePosition(position: Position, bearing: number = 0, terrain: TerrainType = TerrainType.DEFAULT): void
```

The VehicleManager is responsible for:
- Rendering the vehicle marker on the map
- Updating its position and orientation
- Applying appropriate styling based on terrain context
- Handling visibility and recovery of the marker

## Integration with AnimationManager

The AnimationManager uses RoutePoints (through various formats) to:

1. **Calculate Vehicle Positions**: The AnimationManager interpolates between route points to determine the vehicle's precise position at any given moment during the animation.

2. **Determine Contextual Information**: It analyzes the current and upcoming route points to determine:
   - Terrain type
   - Proximity to cities/POIs
   - Appropriate speed adjustments
   - Camera behavior

3. **Animation Control Flow**: Route points are used as waypoints for animation progress calculation and timing adjustments.

```typescript
// From AnimationManager.ts
public prepareRoute(routePoints: Position[], options?: { duration?: number; onComplete?: () => void }): boolean
private calculateCurrentPosition(progress: number): Position
```

## Integration with POIDiscoveryManager

While the POIDiscoveryManager doesn't directly use the RoutePoint interface, it receives position updates derived from the current RoutePoint during animation:

```typescript
// POIDiscoveryManager.ts
public updatePosition(position: [number, number]): POI[]
```

The POIDiscoveryManager is responsible for:
- Detecting when the vehicle is near points of interest
- Triggering discovery events
- Managing vehicle slowdown near POIs
- Tracking discovered POIs

## Integration with RouteAnimator

The RouteAnimator module provides core animation calculation functions that process route points:

```typescript
// RouteAnimator.ts
export const calculateAnimationState = (
  timestamp: number,
  routePoints: Array<[number, number]>,
  refs: RouteAnimatorRefs,
  speedContext: SpeedContext
): AnimationState
```

## Development Guidelines

When working with RoutePoints:

1. **Use Type Guards**: Always validate route point objects using the type guards from RoutePointAdapter before accessing properties.

2. **Handle Optionals**: Many RoutePoint properties are optional - use safe access patterns when working with them.

3. **Position Standardization**: Use the adapter functions to standardize positions regardless of the input format.

4. **Terrain Context**: When available, leverage the terrain information for contextual styling and behavior.

5. **Extended Metadata**: Use the metadata field for application-specific data that doesn't fit in the standard properties.

## Common Patterns

1. **Position Updates**:
   ```typescript
   const position = getRoutePointPosition(routePoint);
   vehicleManager.updatePosition(position, routePoint.bearing, routePoint.terrain);
   ```

2. **Contextual Processing**:
   ```typescript
   // Determine speed adjustments based on terrain
   const speedFactor = routePoint.terrain 
     ? SPEED_MODIFIERS[routePoint.terrain]
     : SPEED_MODIFIERS.DEFAULT;
   ```

3. **Safe Property Access**:
   ```typescript
   // Example of safe access to optional properties
   const elevation = routePoint.elevation ?? 0;
   const nearestLocation = routePoint.nearestLocation || 'Unknown';
   ```
