# Animation System Architecture

## Component Roles and Responsibilities

### Vehicle Position Management

**2024 Rewrite Notice:**
- The vehicle marker system was rewritten from a blank slate. All marker positioning is now controlled by JavaScript using `transform: translate(-50%, -50%) translate3d(x, y, 0) rotate(bearing)`.
- Do **not** use `!important` for `left`, `top`, or `transform` on `.vehicle-marker` in CSS. This will break JS-based positioning.
- The marker is always a child of the map container, with ID `direct-vehicle-marker` and class `vehicle-marker`.
- All marker creation, update, and recovery logic is handled by the singleton `VehicleManager`.

1. **VehicleManager (`VehicleManager.ts`)**
   - **Primary Role**: Core DOM-based vehicle marker management
   - **When to Use**: For direct vehicle marker manipulation and visibility control
   - **Interface**:
   ```typescript
   interface VehicleManagerInterface {
     updateVehiclePosition(position: [number, number], bearing: number): void;
     updateVehicleVisibility(visible: boolean): boolean;
     forceVehicleVisibility(position?: [number, number], bearing?: number): HTMLElement | null;
     isVehicleVisible(): boolean;
   }
   ```

2. **SmoothVehicleController**
   - **Primary Role**: Smooth transitions and interpolation
   - **When to Use**: When requiring smooth vehicle movement between points
   - **Interface**:
   ```typescript
   interface SmoothVehicleControllerInterface {
     updatePosition(position: [number, number], bearing: number): void;
     setInterpolationFactor(factor: number): void;
     setSmoothingEnabled(enabled: boolean): void;
   }
   ```

3. **EmergencyVehicle**
   - **Primary Role**: Fallback positioning system
   - **When to Use**: During system recovery or when normal updates fail
   - **Interface**:
   ```typescript
   interface EmergencyVehicleInterface {
     updateDirectVehiclePosition(position: [number, number]): void;
     forceRecoveryMode(): void;
     resetToNormalOperation(): void;
   }
   ```

### Animation Frame Management

1. **AnimationManager (`AnimationManager.ts`)**
   - **Primary Role**: Core animation timing and state management
   - **When to Use**: For managing the main animation loop and timing
   - **Interface**:
   ```typescript
   interface AnimationManagerInterface {
     startAnimation(config: AnimationConfig): void;
     stopAnimation(): void;
     isCurrentlyAnimating(): boolean;
     getProgress(): number;
   }
   ```

2. **RouteAnimationController**
   - **Primary Role**: Route-specific animation behaviors
   - **When to Use**: When animating along predefined routes
   - **Interface**:
   ```typescript
   interface RouteAnimationControllerInterface {
     prepareRoute(route: RouteData): void;
     animateFrame(timestamp: number): void;
     updateSpeed(multiplier: number): void;
   }
   ```

3. **SimpleRouteAnimator**
   - **Primary Role**: Basic animation functionality
   - **When to Use**: For simple point-to-point animations without complex effects
   - **Interface**:
   ```typescript
   interface SimpleRouteAnimatorInterface {
     animationLoop(): void;
     setRoute(points: [number, number][]): void;
     setDuration(ms: number): void;
   }
   ```

### Camera Control System

1. **VehicleController**
   - **Primary Role**: Basic vehicle following camera
   - **When to Use**: For simple vehicle tracking without effects
   - **Interface**:
   ```typescript
   interface VehicleControllerInterface {
     centerMapOnVehicle(position: [number, number]): void;
     setZoomLevel(zoom: number): void;
     setBearing(bearing: number): void;
   }
   ```

2. **CinematicController**
   - **Primary Role**: Complex cinematic camera effects
   - **When to Use**: For dramatic camera movements with terrain awareness
   - **Interface**:
   ```typescript
   interface CinematicControllerInterface {
     updateCamera(position: [number, number], bearing: number): void;
     setTerrainMode(enabled: boolean): void;
     adjustForPOI(poi: PointOfInterest): void;
   }
   ```

## Component Interaction Flow

1. **Normal Operation Flow**:
   ```
   AnimationManager
   ↓
   RouteAnimationController
   ↓
   SmoothVehicleController
   ↓
   VehicleManager
   ```

2. **Emergency Recovery Flow**:
   ```
   AnimationManager
   ↓
   EmergencyVehicle
   ↓
   VehicleManager (Direct Mode)
   ```

3. **Cinematic Sequence Flow**:
   ```
   CinematicController
   ↓
   RouteAnimationController
   ↓
   SmoothVehicleController
   ↓
   VehicleManager
   ```

## Best Practices

1. **Error Handling**:
   - Always implement fallback mechanisms
   - Use EmergencyVehicle for recovery
   - Log all state transitions

2. **Performance**:
   - Batch DOM updates
   - Use requestAnimationFrame
   - Implement proper cleanup

3. **State Management**:
   - Maintain single source of truth
   - Use proper event system
   - Implement proper cleanup 

# Code Consolidation

> **2024 Update:** All camera/cinematic logic is now handled by [`EnhancedCameraBehavior.ts`](../src/components/map/utils/EnhancedCameraBehavior.ts) and [`CinematicController.ts`](../src/components/map/animation/CinematicController.ts). Do not use `CameraBehavior` or camera helpers in `MapHelpers.ts`. All map transitions must use `bearing: 0` (north-up). The vehicle marker rotates, not the map.

// ... existing content ...
// Remove or update any mention of CameraBehavior, MapHelpers camera utilities, or direct map rotation. Update code examples to use EnhancedCameraBehavior or CinematicController as above.
// ... existing content ... 