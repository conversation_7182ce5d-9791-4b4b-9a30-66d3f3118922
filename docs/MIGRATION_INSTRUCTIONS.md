# Database Migration Instructions

## 🎯 **Overview**

This guide provides step-by-step instructions to set up your Supabase database with comprehensive Morocco travel data. Follow these instructions sequentially to ensure proper database setup.

## 📋 **Prerequisites**

- ✅ Supabase account (free tier available)
- ✅ Node.js and npm installed
- ✅ Basic understanding of SQL
- ✅ Project environment variables ready

## 🚀 **Step-by-Step Migration Process**

### **Step 1: Create Supabase Project**

1. **Go to Supabase Dashboard**
   - Visit [supabase.com](https://supabase.com)
   - Sign up or log in to your account

2. **Create New Project**
   - Click "New Project"
   - Choose your organization
   - Enter project details:
     - **Name**: `come-to-morocco-db`
     - **Database Password**: Choose a strong password (save it!)
     - **Region**: Choose closest to your users (e.g., Europe West, US East)
   - Click "Create new project"

3. **Wait for Project Setup**
   - Project creation takes 2-3 minutes
   - You'll see a progress indicator
   - Don't proceed until status shows "Project is ready"

### **Step 2: Get Project Credentials**

1. **Navigate to Settings**
   - In your Supabase dashboard, go to **Settings** → **API**

2. **Copy Required Values**
   ```
   Project URL: https://your-project-id.supabase.co
   Anon public key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   ```

3. **Update Environment Variables**
   - Open your `.env` file in the project root
   - Add your Supabase credentials:
   ```env
   # Supabase Configuration
   VITE_SUPABASE_URL=https://your-project-id.supabase.co
   VITE_SUPABASE_ANON_KEY=your-anon-key-here
   ```

### **Step 3: Enable PostGIS Extension**

1. **Go to SQL Editor**
   - In Supabase dashboard: **SQL Editor** → **New query**

2. **Enable PostGIS**
   ```sql
   -- Enable PostGIS extension for geographic data
   CREATE EXTENSION IF NOT EXISTS "postgis";
   ```

3. **Run the Query**
   - Click "Run" button
   - Verify success message appears

### **Step 4: Run Schema Migration**

1. **Create New Query in SQL Editor**
   - Click "New query" in SQL Editor

2. **Copy Schema Migration**
   - Open `supabase/migrations/001_initial_schema.sql`
   - Copy the **entire file content**
   - Paste into the SQL Editor

3. **Execute Schema Migration**
   - Click "Run" button
   - **Wait for completion** (may take 30-60 seconds)
   - Verify success message: "Success. No rows returned"

4. **Verify Tables Created**
   - Go to **Table Editor** in Supabase dashboard
   - You should see these tables:
     - ✅ `clients`
     - ✅ `destinations`
     - ✅ `pois`
     - ✅ `journeys`
     - ✅ `itineraries`
     - ✅ `users`
     - ✅ `bookings`
     - ✅ `reviews`

### **Step 5: Run Seed Data Migration**

1. **Create Another New Query**
   - Click "New query" in SQL Editor

2. **Copy Seed Data**
   - Open `supabase/migrations/002_seed_data.sql`
   - Copy the **entire file content**
   - Paste into the SQL Editor

3. **Execute Seed Migration**
   - Click "Run" button
   - **Wait for completion** (may take 1-2 minutes)
   - Verify success message appears

4. **Verify Data Inserted**
   - Go to **Table Editor**
   - Check each table has data:
     - `clients`: 3 rows (Morocco, Portugal, Global)
     - `destinations`: 9 rows (Morocco cities)
     - `pois`: 15+ rows (attractions and landmarks)
     - `journeys`: 6 rows (journey templates)

### **Step 6: Configure Row Level Security (Optional)**

1. **Review RLS Policies**
   - Go to **Authentication** → **Policies**
   - Basic policies are already created
   - For production, you may want to customize these

2. **Test Public Access**
   - The current policies allow public read access
   - This is suitable for the demo application

### **Step 7: Install Dependencies**

1. **Install Supabase Client**
   ```bash
   npm install
   ```
   - This installs the Supabase client library already added to package.json

2. **Verify Installation**
   ```bash
   npm list @supabase/supabase-js
   ```
   - Should show version 2.49.4 or similar

### **Step 8: Test Database Connection**

1. **Start Development Server**
   ```bash
   npm run dev
   ```

2. **Open Application**
   - Navigate to `http://localhost:3000/enhanced-neutral-demo`

3. **Check Console Logs**
   - Open browser developer tools
   - Look for these success messages:
   ```
   ✅ [getClientData] Using database data for client: morocco
   ✅ [EnhancedNeutralDemo] Client data loaded: {destinations: 9, pois: 15, journeys: 6}
   📊 Data Quality Report: Overall Score: 100/100
   ```

4. **Verify Map Data**
   - Cities should appear on the map
   - Route generation should work
   - Animation should function properly

## 🔍 **Troubleshooting**

### **Common Issues & Solutions**

#### **1. PostGIS Extension Error**
```
ERROR: extension "postgis" is not available
```
**Solution**: Some Supabase regions don't support PostGIS. Try:
- Switch to a different region when creating the project
- Use EU West or US East regions which typically support PostGIS

#### **2. Migration Timeout**
```
Query timeout after 60 seconds
```
**Solution**: 
- Break the migration into smaller chunks
- Run schema first, then seed data in batches
- Increase timeout in Supabase settings

#### **3. Connection Errors**
```
Failed to connect to database
```
**Solution**:
- Verify your Supabase URL and key are correct
- Check that your project is active (not paused)
- Ensure environment variables are properly set

#### **4. Data Not Loading**
```
Using mock data fallback for client: morocco
```
**Solution**:
- Verify seed data was inserted successfully
- Check RLS policies allow public read access
- Confirm client slug matches ('morocco')

#### **5. Coordinate Issues**
```
Map center is outside Morocco bounds
```
**Solution**:
- Verify PostGIS POINT data was inserted correctly
- Check coordinate format in database (should be POINT(lng lat))
- Ensure data validation utilities are working

### **Verification Checklist**

Before proceeding, ensure:
- ✅ PostGIS extension is enabled
- ✅ All 8 tables are created
- ✅ Sample data is inserted in all tables
- ✅ Environment variables are set
- ✅ Application connects to database
- ✅ Morocco data loads correctly
- ✅ Map displays cities and routes
- ✅ Animation system works

## 🎉 **Success Indicators**

When everything is working correctly, you should see:

1. **Database Connection**
   ```
   ✅ [getClientData] Using database data for client: morocco
   ```

2. **Data Quality**
   ```
   📊 Data Quality Report: Overall Score: 100/100
   ```

3. **Map Functionality**
   - 9 Morocco cities visible on map
   - Route generation between cities works
   - Vehicle animation follows routes
   - POI data loads correctly

4. **Performance**
   - Fast data loading (< 2 seconds)
   - Smooth map interactions
   - No console errors

## 🔄 **Next Steps**

After successful migration:

1. **Test All Features**
   - Try different city combinations
   - Test journey planning
   - Verify AI integration works

2. **Add More Data** (Optional)
   - Add Portugal destinations
   - Create custom journey templates
   - Add more POIs and attractions

3. **Production Setup**
   - Configure proper RLS policies
   - Set up database backups
   - Monitor performance metrics

4. **Admin Portal**
   - Use the admin portal to manage data
   - Add new destinations and POIs
   - Create custom journey templates

---

**🎯 Total Setup Time**: 15-30 minutes
**🎯 Difficulty Level**: Beginner-friendly
**🎯 Result**: Production-ready database with comprehensive Morocco travel data

If you encounter any issues, refer to the troubleshooting section or check the Supabase documentation.
