# Vehicle Component Migration Guide

**2024 Rewrite Notice:**
- The vehicle marker and animation system was rewritten from a blank slate in 2024.
- All marker positioning is now handled by JS using `transform: translate(-50%, -50%) translate3d(x, y, 0) rotate(bearing)`.
- Do **not** use `!important` for `left`, `top`, or `transform` on `.vehicle-marker` in CSS. This will break JS-based positioning.
- The marker is always a child of the map container, with ID `direct-vehicle-marker` and class `vehicle-marker`.
- All marker creation, update, and recovery logic is handled by the singleton `VehicleManager`.

**2024 Rewrite Note:**
- As of the 2024 rewrite, only `AnimationManager`, `VehicleManager`, and `RouteAnimator` are used in the main animation flow.
- `CinematicController`, `AnimationIntegration`, `OptimizedAnimationIntegration`, `AnimationFrameManager`, and `CameraBehavior` are present in the codebase but are not currently integrated into the main animation or UI flow.
- Future work may reintroduce these modules for advanced cinematic camera moves and orchestration.

**2024 Update:** All camera/cinematic logic is now handled by [`EnhancedCameraBehavior.ts`](../src/components/map/utils/EnhancedCameraBehavior.ts) and [`CinematicController.ts`](../src/components/map/animation/CinematicController.ts). Do not use `CameraBehavior` or camera helpers in `MapHelpers.ts`. All map transitions must use `bearing: 0` (north-up). The vehicle marker rotates, not the map.

## Overview of Changes

We're restructuring the vehicle animation system to follow better architectural patterns:

1. **VehicleDOM**: A new component focused solely on DOM manipulation
2. **VehicleManager**: Refactored to focus on state management and coordination
3. **VehicleController**: Enhanced to handle animation and user interaction

This separation improves:
- Testability by reducing singleton dependencies
- Maintainability through focused components
- Performance by optimizing DOM operations
- Extensibility with cleaner interfaces

## Migration Steps

### Phase 0: Remove Conflicting CSS

- Remove any CSS rules that set `left`, `top`, or `transform` with `!important` for `.vehicle-marker`.
- Example (to remove):
```css
.vehicle-marker {
  left: 0 !important;
  top: 0 !important;
  transform: translate(-50%, -50%) !important;
}
```

### Phase 1: Using the Refactored Components with Minimal Changes

To minimize disruption, we've created a backward-compatible implementation. Update imports as follows:

#### Before:

```typescript
import VehicleManager from '../components/map/animation/VehicleManager';
import { updateVehiclePosition } from '../components/map/animation/VehicleController';
```

#### After:

```typescript
import VehicleManager from '../components/map/animation/RefactoredVehicleManager';
import VehicleController from '../components/map/animation/VehicleController';
```

#### Update Method Calls:

| Old API | New API |
|---------|---------|
| `VehicleManager.updateVehiclePosition(pos, bearing)` | `VehicleManager.updatePosition(pos, bearing)` |
| `VehicleManager.showVehicle(true)` | `VehicleManager.setVisible(true)` |
| `VehicleManager.isVehicleVisible()` | `VehicleManager.isVisible()` |
| `VehicleManager.applyVehicleStyle(style)` | `VehicleManager.applyStyle(style)` |
| `updateVehiclePosition(pos, bearing)` | `VehicleController.smoothUpdatePosition(pos, bearing)` |
| `forceVehicleVisibility()` | `VehicleManager.recoverVisibility()` |

### Phase 2: Updating Component Interactions

#### AnimationIntegration Changes

```typescript
// Before
this.vehicleManager.createVehicleMarker(position);
this.vehicleManager.updateVehiclePosition(position, bearing);

// After
await this.vehicleController.initialize(this.map);
this.vehicleController.smoothUpdatePosition(position, bearing);
```

#### Event Subscriptions

```typescript
// Before
this.vehicleManager.addEventListener('positionChange', this.handlePositionChange);

// After
this.vehicleManager.addEventListener(
  AnimationEventType.VEHICLE_POSITION_CHANGE, 
  this.handlePositionChange
);
```

### Phase 3: Animation Improvements

Take advantage of the enhanced animation capabilities:

```typescript
// Simple position update with smoothing
vehicleController.smoothUpdatePosition(position, bearing);

// Custom animation with easing
await vehicleController.animateToPosition(position, bearing, {
  duration: 2000,
  easing: 'easeInOut',
  onComplete: () => console.log('Animation complete'),
  onFrame: (progress) => console.log(`Animation progress: ${progress * 100}%`)
});

// Visibility with fade animation
vehicleController.setVisible(true, 500); // 500ms fade-in
```

### Phase 4: Refactoring Direct DOM References

Replace direct DOM manipulation with VehicleDOM:

#### Before:

```typescript
const marker = document.getElementById('vehicle-marker');
marker.style.transform = `translate(${x}px, ${y}px) rotate(${bearing}deg)`;
```

#### After:

```typescript
vehicleController.smoothUpdatePosition(position, bearing);
```

### Phase 5: Complete Migration

After testing with the refactored components, complete the migration by:

1. Rename `RefactoredVehicleManager.ts` to `VehicleManager.ts` (replacing the old file)
2. Update all imports to use the new component paths
3. Remove any deprecated utility functions

## API Changes

### VehicleManager

| Old Method | New Method | Notes |
|------------|------------|-------|
| `initialize(config)` | `initialize(map)` | Simplified parameters |
| `updateVehiclePosition(pos, bearing)` | `updatePosition(pos, bearing)` | More consistent naming |
| `showVehicle(true)` | `setVisible(true)` | More consistent naming |
| `applyVehicleStyle(style)` | `applyStyle(style)` | More consistent naming |
| `isVehicleVisible()` | `isVisible()` | More consistent naming |
| `getCurrentPosition()` | `getCurrentPosition()` | Unchanged |
| `checkVehicleVisibility()` | `recoverVisibility()` | Focus on action |
| `enableVisibilityMonitoring(true)` | `startVisibilityMonitoring()` | Split into two methods |
| - | `stopVisibilityMonitoring()` | New method |
| `destroy()` | `dispose()` | More consistent naming |

### VehicleController

| Old Pattern | New Method | Notes |
|-------------|------------|-------|
| Utility functions | Class methods | Proper encapsulation |
| `updateVehiclePosition(pos)` | `smoothUpdatePosition(pos)` | Clarified purpose |
| - | `animateToPosition(pos, bearing, options)` | New feature |
| - | `stopAnimations()` | New feature |
| - | `setSmoothingOptions(options)` | New configuration options |
| - | `setVisible(visible, fadeDuration)` | Fade animation support |

## Testing

Unit tests have been updated to cover the new architecture. When migrating your code:

1. Update test imports to use the new components
2. Use the factory methods for dependency injection in tests:
   ```typescript
   const mockVehicleDOM = new MockVehicleDOM();
   const vehicleManager = RefactoredVehicleManager.createInstance(mockVehicleDOM);
   const vehicleController = VehicleController.createInstance(vehicleManager);
   ```
3. Test each layer independently:
   - VehicleDOM: Test DOM operations
   - VehicleManager: Test state management
   - VehicleController: Test animations and coordination

## Troubleshooting

### Visibility Issues

If vehicle markers aren't appearing:

1. Check if the VehicleManager is properly initialized: `vehicleManager.initialize(map)`
2. Verify visibility state: `vehicleManager.isVisible()`
3. Try recovery: `vehicleManager.recoverVisibility()`

### Animation Glitches

If animations aren't smooth:

1. Adjust smoothing: `vehicleController.setSmoothingOptions({ factor: 0.5 })`
2. Ensure vehicle is visible: `vehicleManager.setVisible(true)`
3. Check for animation conflicts: `vehicleController.stopAnimations()`

## Timeline

- **Current**: Use RefactoredVehicleManager and VehicleController alongside existing components
- **Next 2 Weeks**: Update application code to use new APIs
- **Next Month**: Complete migration and remove old components

## Further Support

For questions about the migration, contact the Animation Systems team or open an issue with "Vehicle Migration" in the title.