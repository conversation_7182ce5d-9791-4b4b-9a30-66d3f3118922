# Animation Architecture Rules

This document establishes the architectural guidelines and rules for implementing and extending the map animation system in our Morocco Travel application. All new code should adhere to these principles to maintain consistency, reliability, and performance.

**2024 Rewrite Note:**
- As of the 2024 rewrite, only `AnimationManager`, `VehicleManager`, and `RouteAnimator` are used in the main animation flow.
- `CinematicController`, `AnimationIntegration`, `OptimizedAnimationIntegration`, `AnimationFrameManager`, and `CameraBehavior` are present in the codebase but are not currently integrated into the main animation or UI flow.
- Future work may reintroduce these modules for advanced cinematic camera moves and orchestration.

## Animation System Core Principles

### State Management Pattern

1. **Ref-Based Animation State**
   - Store all animation-critical state in refs (`useRef`) not React state (`useState`)
   - Example: `const animationFrameIdRef = useRef<number | null>(null);`
   - Example: `const currentPointIndexRef = useRef<number>(0);`

2. **React State for UI Only**
   - Use React state exclusively for UI updates and rendering
   - Keep animation logic decoupled from <PERSON><PERSON>'s render cycle
   - Example: `const [isAnimating, setIsAnimating] = useState(false);`

3. **Controlled Prop Flow**
   - Animation components should accept clearly defined props
   - Parent components control animation through props, not direct manipulation
   - Example: `<TravelAnimator isPlaying={isPlaying} speed={speed} onComplete={handleComplete} />`

### Animation Frame Management

1. **Direct Animation Loop**
   - All animations must use `requestAnimationFrame` for the main loop
   - Never use `setTimeout` or `setInterval` for primary animation timing
   - Always store frame IDs in refs for proper cleanup
   
   ```typescript
   // CORRECT IMPLEMENTATION
   const frameIdRef = useRef<number | null>(null);
   
   const animateFrame = () => {
     // Animation logic here
     frameIdRef.current = requestAnimationFrame(animateFrame);
   };
   
   const startAnimation = () => {
     if (frameIdRef.current === null) {
       frameIdRef.current = requestAnimationFrame(animateFrame);
     }
   };
   
   const stopAnimation = () => {
     if (frameIdRef.current !== null) {
       cancelAnimationFrame(frameIdRef.current);
       frameIdRef.current = null;
     }
   };
   ```

2. **Frame Delay Implementation**
   - Use index-based progression instead of time-based calculations
   - Add intentional delay between frames for smoother vehicle movement
   - Target 100ms between position updates for a natural progression speed

   ```typescript
   const FRAME_DELAY_MS = 100;
   const lastFrameTimeRef = useRef<number>(0);
   
   const animateWithDelay = (timestamp: number) => {
     if (timestamp - lastFrameTimeRef.current >= FRAME_DELAY_MS) {
       // Update animation state
       currentPointIndexRef.current += 1;
       // Update vehicle position
       updateVehiclePosition(routePoints[currentPointIndexRef.current]);
       lastFrameTimeRef.current = timestamp;
     }
     frameIdRef.current = requestAnimationFrame(animateWithDelay);
   };
   ```

3. **Cleanup Requirements**
   - Always implement proper cleanup in useEffect returns
   - Cancel animation frames on component unmount and state changes
   - Reset animation state completely when animations restart

   ```typescript
   useEffect(() => {
     // Setup animation
     
     return () => {
       // This must be implemented for all animations
       if (frameIdRef.current !== null) {
         cancelAnimationFrame(frameIdRef.current);
         frameIdRef.current = null;
       }
     };
   }, [dependencies]);
   ```

### Error Handling & Recovery

1. **Animation Error Protection**
   - Wrap all animation frame logic in try/catch blocks
   - Log errors through `AnimationDebugTools.log()`
   - Implement recovery mechanisms for failed frames

   ```typescript
   const animateFrame = () => {
     try {
       // Animation logic
     } catch (error) {
       AnimationDebugTools.log('Animation error:', error);
       // Recovery logic
       resetAnimationState();
       // Optionally restart
       startAnimationWithDelay(500);
     }
     frameIdRef.current = requestAnimationFrame(animateFrame);
   };
   ```

2. **State Monitoring**
   - Implement animation state monitors to detect and repair stalled animations
   - Use timeout-based checks to verify animation progress
   - Add recovery mechanisms for detected failures

   ```typescript
   // Monitor implementation
   const monitorAnimation = () => {
     const previousPointIndex = monitorPointIndexRef.current;
     monitorPointIndexRef.current = currentPointIndexRef.current;
     
     // If animation is supposed to be running but point index hasn't changed
     if (isAnimatingRef.current && 
         previousPointIndex === currentPointIndexRef.current &&
         previousPointIndex !== routePoints.length - 1) {
       AnimationDebugTools.log('Animation stalled, attempting recovery');
       // Recovery logic
       resetAndRestartAnimation();
     }
     
     // Continue monitoring
     setTimeout(monitorAnimation, 1000);
   };
   ```

### Module Consolidation

1. **Utility File Consolidation**
   - Animation logic is now consolidated into specialized utility files
   - Follow the "single source of truth" principle for common utilities
   - Prefer importing from `.ts` files rather than `.tsx` files for utilities
   
   ```typescript
   // CORRECT
   import { calculateBearing } from '../utils/MapHelpers';
   
   // INCORRECT - transitional only
   import { calculateBearing } from '../utils/MapHelpers.tsx';
   ```
   
2. **Component vs. Utility Separation**
   - React components (.tsx) should focus on rendering and user interaction
   - Animation logic should be in specialized utility files (.ts)
   - This improves testability and reduces component complexity

   ```typescript
   // Utility file (MapHelpers.ts)
   export const calculateBearing = (point1: [number, number], point2: [number, number]): number => {
     // Calculation logic
   };
   
   // Component file (TravelAnimator.tsx)
   import { calculateBearing } from '../utils/MapHelpers';
   
   const handleVehicleMovement = () => {
     const bearing = calculateBearing(currentPoint, nextPoint);
     // Use the bearing in component logic
   };
   ```

3. **Consolidated File Structure**
   
   ```
   src/components/map/
   ├── utils/
   │   ├── MapHelpers.ts        # All map calculation utilities
   │   ├── CameraBehavior.ts    # Camera control functions
   │   ├── types.ts             # Common type definitions
   ├── animation/
   │   ├── AnimationManager.ts  # Animation state management
   │   ├── RouteAnimator.ts     # Route animation logic
   │   ├── VehicleManager.ts    # Vehicle marker management
   ```

### TypeScript Configuration Standards

1. **Required Configuration**
   - Ensure `esModuleInterop` is set to `true` in tsconfig.json
   - Configure path aliases using `baseUrl` and `paths` for cleaner imports
   - Use strict type checking with `strict: true`

   ```typescript
   // tsconfig.json
   {
     "compilerOptions": {
       "target": "ES2020",
       "useDefineForClassFields": true,
       "lib": ["ES2020", "DOM", "DOM.Iterable"],
       "module": "ESNext",
       "skipLibCheck": true,
       "esModuleInterop": true,
       "allowSyntheticDefaultImports": true,
       "baseUrl": ".",
       "paths": {
         "@/*": ["./src/*"]
       },
       "strict": true
     }
   }
   ```

2. **Module Declaration Overrides**
   - Use declaration files for third-party library compatibility issues
   - Place these declarations in `src/types/module-declarations.d.ts`

   ```typescript
   // src/types/module-declarations.d.ts
   declare module 'mapbox-gl' {
     import type { Map, LngLatLike, LngLatBounds } from 'mapbox-gl';
     export * from 'mapbox-gl';
     export default mapboxgl;
   }
   ```

3. **Import Patterns**
   - For React, use the namespace import: `import * as React from 'react';`
   - For other modules, use named imports: `import { Something } from 'module';`
   - For path aliases, prefer explicit paths over aliases when importing within the same directory
   
   ```typescript
   // CORRECT
   import * as React from 'react';
   import { useCallback, useState } from 'react';
   import { AnimationManager } from '../animation/AnimationManager';
   
   // AVOID in same directory
   import { someUtil } from '@/components/map/utils/someUtil';
   
   // PREFER in same directory
   import { someUtil } from './someUtil';
   ```

### Timestamp Standardization

1. **Numeric Timestamps Only**
   - Always use `Date.now()` for timestamps (returns milliseconds since epoch)
   - Never use string-based timestamps like `new Date().toISOString()`
   - Store timestamp references in numeric variables
   
   ```typescript
   // CORRECT
   const timestamp = Date.now();
   AnimationDebugTools.log('Animation started', { timestamp });
   
   // INCORRECT
   const timestamp = new Date().toISOString(); // DO NOT USE
   ```

2. **Timestamp Calculations**
   - Use direct numeric operations for timestamp differences
   - Store last update times in refs for animation timing

   ```typescript
   const lastUpdateTimeRef = useRef<number>(Date.now());
   
   const calculateTimeDelta = () => {
     const now = Date.now();
     const delta = now - lastUpdateTimeRef.current;
     lastUpdateTimeRef.current = now;
     return delta;
   };
   ```

3. **Timestamp Storage**
   - Use numeric timestamps in all event payloads and logs
   - When displaying timestamps to users, convert to locale format
   
   ```typescript
   // For logging/events (internal)
   dispatchEvent({
     type: 'ANIMATION_START',
     timestamp: Date.now()
   });
   
   // For display (only at UI boundary)
   const formatTimestamp = (timestamp: number) => {
     return new Date(timestamp).toLocaleTimeString();
   };
   ```

## Animation Integration Modules

1. **Standard Animation Integration**
   - `AnimationIntegration.ts` provides unified animation coordination
   - Centralizes animation start/stop/monitoring logic
   - Handles journey phase transitions and event dispatching
   - Implements error recovery for animation failures

2. **Optimized Animation Integration**
   - `OptimizedAnimationIntegration.ts` provides enhanced performance features
   - Supports animation performance monitoring
   - Implements adaptive speed control based on terrain and context
   - Adds animation health monitoring with fallbacks
   - Used for high-performance animation sequences

3. **Integration Interfaces**
   ```typescript
   // Common interfaces between standard and optimized integration
   interface AnimationIntegrationState {
     isAnimating: boolean;
     progress: number;
     position: [number, number] | null;
     bearing: number;
     speed: number;
     terrain: string;
     inCity: boolean;
     cityName?: string;
     isPaused: boolean;
   }

   interface AnimationIntegrationOptions {
     map: mapboxgl.Map;
     routePoints: Array<[number, number]>;
     onProgress?: (progress: number, position: [number, number], bearing: number) => void;
     onComplete?: () => void;
     onError?: (error: Error) => void;
     duration?: number;
     debug?: boolean;
     initialPosition?: [number, number];
     initialBearing?: number;
   }
   ```

## Map Camera Rules

1. **Zoom Level Standards**
   - Route travel: Always use zoom level 8
   - City exploration: Always use zoom level 14
   - POI detail view: Always use zoom level 16

   ```typescript
   const ZOOM_LEVELS = {
     ROUTE_TRAVEL: 8,
     CITY_EXPLORATION: 14,
     POI_DETAIL: 16
   };
   
   // Example usage
   map.flyTo({
     center: currentPoint,
     zoom: ZOOM_LEVELS.ROUTE_TRAVEL,
     pitch: 45,
     bearing: 0,
     essential: true
   });
   ```

2. **Camera Orientation**
   - During route travel, maintain fixed north orientation (bearing: 0)
   - Keep pitch at 45° for 3D perspective during all camera transitions
   - Use appropriate easing for smooth camera movements

   ```typescript
   // Camera movement implementation
   map.easeTo({
     center: [longitude, latitude],
     zoom: ZOOM_LEVELS.ROUTE_TRAVEL,
     pitch: 45,
     bearing: 0,
     duration: 500,
     easing: (t) => easeInOutCubic(t),
     essential: true
   });
   ```

3. **Transition Timing**
   - Use duration of at least 500ms for all camera transitions
   - Implement easing functions for smoother camera movement
   - Mark camera animations as `essential: true` to prevent interruption

## Vehicle Marker Management

**2024 Rewrite Notice:**
- The vehicle marker system was rewritten from a blank slate. All marker positioning is now controlled by JavaScript using `transform: translate(-50%, -50%) translate3d(x, y, 0) rotate(bearing)`.
- Do **not** use `!important` for `left`, `top`, or `transform` on `.vehicle-marker` in CSS. This will break JS-based positioning.
- The marker is always a child of the map container, with ID `direct-vehicle-marker` and class `vehicle-marker`.
- All marker creation, update, and recovery logic is handled by the singleton `VehicleManager`.
- **Only `AnimationManager`, `VehicleManager`, and `RouteAnimator` are used in the main animation flow.**
- `CinematicController` and related modules are not currently integrated.

1. **Direct DOM Manipulation**
   - Always check for marker existence before manipulation
   - Prefer direct DOM manipulation for vehicle visibility concerns
   - Use a combination of React state and DOM manipulation for critical visibility
   
   ```typescript
   const forceVehicleVisibility = () => {
     const vehicleMarker = document.getElementById('direct-vehicle-marker');
     if (vehicleMarker) {
       // Force visibility with both approaches
       vehicleMarker.style.display = 'block';
       vehicleMarker.style.visibility = 'visible';
       // Update React state to maintain synchronized state
       setShowVehicle(true);
     }
   };
   ```

2. **Marker Recreation Safety**
   - Implement defensive checks for marker existence
   - Add recovery mechanisms for missing markers
   - Include clear logging for marker-related operations
   
   ```typescript
   const ensureMarkerExists = () => {
     const existingMarker = document.getElementById('direct-vehicle-marker');
     if (!existingMarker) {
       AnimationDebugTools.log('Vehicle marker missing, recreating');
       createVehicleMarker(position, bearing);
       return true;
     }
     return false;
   };
   ```

## Vehicle Animation Rules

1. **Vehicle Speed Control**
   - Modify speed based on context (city, mountain, desert)
   - Apply easing for smooth acceleration and deceleration
   - Coordinate speed with camera transitions
   
   ```typescript
   const getSpeedModifier = (context: string): number => {
     switch (context) {
       case 'city': return 0.5; // Slower in cities
       case 'mountain': return 0.8; // Slightly slower in mountains
       case 'desert': return 1.2; // Faster in open desert
       default: return 1.0;
     }
   };
   ```

2. **Position Updates**
   - Use current and next point to interpolate intermediate positions
   - Calculate bearing dynamically between points
   - Apply appropriate terrain-specific behaviors
   
   ```typescript
   const updateVehiclePosition = (progress: number) => {
     const index = Math.floor(progress * (routePoints.length - 1));
     const remaining = progress * (routePoints.length - 1) - index;
     
     if (index < routePoints.length - 1) {
       const current = routePoints[index];
       const next = routePoints[index + 1];
       const position = interpolatePosition(current, next, remaining);
       const bearing = calculateBearing(current, next);
       
       vehicleMarker.setLngLat(position);
       vehicleMarker.setBearing(bearing);
     }
   };
   ```

## Animation Debugging Requirements

1. **Logging Standards**
   - Use `AnimationDebugTools.log()` for consistent logging format
   - Include specific state information in all animation logs
   - Add log markers for animation start/stop/recovery events

   ```typescript
   // Log format example
   AnimationDebugTools.log(
     'Animation state:',
     {
       pointIndex: currentPointIndexRef.current,
       totalPoints: routePoints.length,
       position: currentPosition,
       isAnimating: isAnimatingRef.current,
       timestamp: Date.now()
     }
   );
   ```

2. **Debug UI Components**
   - Implement debug panels with animation state visualization
   - Add control buttons for manual animation testing
   - Display current vehicle position and animation state
   
   ```tsx
   // Debug UI example
   {process.env.NODE_ENV === 'development' && (
     <AnimationDebugPanel
       isAnimating={isAnimatingRef.current}
       pointIndex={currentPointIndexRef.current}
       totalPoints={routePoints.length}
       onReset={resetAnimation}
       onForceStart={startAnimation}
       onForceStop={stopAnimation}
     />
   )}
   ```

## Performance Optimization Rules

1. **Animation Frame Execution**
   - Keep animation frame logic minimal and focused
   - Avoid DOM operations in tight animation loops
   - Implement point indexing approach rather than time-based calculations
   
2. **DOM Manipulation Efficiency**
   - Batch DOM operations when possible
   - Create helper functions for common DOM operations
   - Minimize direct style manipulations to essential properties only

3. **React State Updates**
   - Avoid triggering React state updates within animation frames
   - Use React state updates only for UI changes, not animation control
   - Implement debouncing for state updates from animation frames

## Module Organization Standards

1. **Animation Logic Separation**
   - Keep animation logic separate from React components
   - Create specialized utility files for animation calculations
   - Maintain clear separation between animation and UI concerns

2. **Component Responsibilities**
   - `TravelAnimator.tsx`: Coordinates animation and UI
   - `RouteAnimator.ts`: Handles animation calculations and progress
   - `VehicleController.ts`: Manages vehicle marker DOM manipulation
   - `CameraBehavior.ts`: Controls camera positioning and movements
   - `AnimationDebugTools.tsx`: Provides debugging utilities

3. **Type Definitions**
   - Create explicit types for all animation properties
   - Define consistent interfaces for animation components
   - Use TypeScript generics for reusable animation patterns

## Architecture Extension Guidelines

When adding new animation features:

1. **Consistency First**
   - Maintain consistent patterns with existing animation code
   - Follow the established module structure and responsibilities
   - Use the same state management approach (refs for animation, state for UI)

2. **Feature Integration**
   - Extend existing modules rather than creating new ones
   - Add new capabilities through clear interfaces
   - Document all animation extensions in code comments

3. **Testing Requirements**
   - Include debug capabilities for all new animation features
   - Add monitoring and recovery for new animation states
   - Test animations across different device performance profiles 

## Enhanced State Management Patterns

### Context-Based State Management

1. **TravelAnimator Context**
   ```typescript
   // Define context type
   interface TravelAnimatorContextType {
     state: TravelAnimatorState;
     dispatch: React.Dispatch<TravelAnimatorAction>;
   }

   // Create context
   const TravelAnimatorContext = React.createContext<TravelAnimatorContextType>({
     state: initialState,
     dispatch: () => null
   });

   // Provider implementation
   const TravelAnimatorProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
     const [state, dispatch] = useReducer(travelAnimatorReducer, initialState);
     return (
       <TravelAnimatorContext.Provider value={{ state, dispatch }}>
         {children}
       </TravelAnimatorContext.Provider>
     );
   };
   ```

2. **State Reducer Pattern**
   ```typescript
   // Define state type
   interface TravelAnimatorState {
     isAnimating: boolean;
     journeyPhase: JourneyPhase | null;
   }

   // Define action types
   type TravelAnimatorAction = 
     | { type: 'START_ANIMATION' }
     | { type: 'STOP_ANIMATION' }
     | { type: 'SET_JOURNEY_PHASE'; payload: JourneyPhase };

   // Implement reducer
   const travelAnimatorReducer = (
     state: TravelAnimatorState, 
     action: TravelAnimatorAction
   ): TravelAnimatorState => {
     switch (action.type) {
       case 'START_ANIMATION':
         return { ...state, isAnimating: true };
       case 'STOP_ANIMATION':
         return { ...state, isAnimating: false };
       case 'SET_JOURNEY_PHASE':
         return { ...state, journeyPhase: action.payload };
       default:
         return state;
     }
   };
   ```

### Performance Monitoring Implementation

1. **Animation Performance Tracking**
   ```typescript
   interface PerformanceMetrics {
     frameRate: number;
     frameDelay: number;
     memoryUsage: number;
     stateUpdateFrequency: number;
   }

   class AnimationPerformanceMonitor {
     private metrics: PerformanceMetrics;
     private history: PerformanceLogEntry[];

     public trackFrame(timestamp: number): void {
       // Update metrics
       this.updateFrameRate(timestamp);
       this.updateStateMetrics();
       this.logPerformance();
     }

     private logPerformance(): void {
       AnimationDebugTools.log('Performance metrics:', this.metrics);
     }
   }
   ```

2. **Debug Tools Integration**
   ```typescript
   interface DebugPanelProps {
     metrics: PerformanceMetrics;
     animationState: TravelAnimatorState;
     onReset: () => void;
   }

   const AnimationDebugPanel: React.FC<DebugPanelProps> = ({
     metrics,
     animationState,
     onReset
   }) => {
     // Debug panel implementation
   };
   ```

### State Synchronization Rules

1. **External State Sync**
   ```typescript
   // Sync external state with context
   useEffect(() => {
     if (externalIsAnimating !== state.isAnimating) {
       dispatch({ 
         type: externalIsAnimating ? 'START_ANIMATION' : 'STOP_ANIMATION' 
       });
     }
   }, [externalIsAnimating, state.isAnimating]);
   ```

2. **Phase Transition Management**
   ```typescript
   const handlePhaseTransition = (newPhase: JourneyPhase) => {
     // Log transition
     AnimationDebugTools.log('Phase transition:', {
       from: state.journeyPhase,
       to: newPhase
     });

     // Update state
     dispatch({ type: 'SET_JOURNEY_PHASE', payload: newPhase });
   };
   ``` 

## Implementation Status Overview

The following section provides the current implementation status of our animation architecture rules:

### Core Animation Rules
- ✅ Ref-based animation state
- ✅ Direct animation frame system with requestAnimationFrame
- ✅ Frame delay implementation (100ms)
- ✅ Animation error protection
- ✅ State monitoring
- 🟡 Utility file consolidation (in progress)

### State Management Rules
- ✅ Context-based state management
- ✅ Reducer pattern for complex state
- ✅ State synchronization mechanisms
- 🟡 State persistence (in progress)
- 🟡 Advanced performance optimization (in progress)

### Camera Control Rules
- ✅ Zoom level standards
- ✅ Basic camera transitions
- 🟡 Contextual rhythm (partially implemented)
- 🟡 User-controlled zoom options (planned)

### Debug Support Rules
- ✅ Debug panel implementation
- ✅ Performance monitoring
- ✅ State transition logging
- 🟡 Advanced visualization tools (in progress)

## Current Implementation Details

### State Synchronization Implementation

Our current implementation effectively synchronizes external state with context state:

```typescript
// Sync external state with context state
useEffect(() => {
  if (externalIsAnimating !== state.isAnimating) {
    dispatch({ 
      type: externalIsAnimating ? 'START_ANIMATION' : 'STOP_ANIMATION' 
    });
  }
}, [externalIsAnimating, state.isAnimating]);

useEffect(() => {
  if (externalJourneyPhase && externalJourneyPhase !== state.journeyPhase) {
    dispatch({ 
      type: 'SET_JOURNEY_PHASE', 
      payload: externalJourneyPhase 
    });
  }
}, [externalJourneyPhase, state.journeyPhase]);
```

### Performance Monitoring Implementation

We've implemented a robust performance monitoring system:

```typescript
// Current implementation in AnimationPerformanceMonitor.ts
export class AnimationPerformanceMonitor {
  private static instance: AnimationPerformanceMonitor;
  private metrics: PerformanceMetrics;
  private history: PerformanceLogEntry[];
  private isRunning: boolean;
  private animationStateProvider?: () => AnimationState;

  constructor() {
    this.metrics = {
      frameRate: 0,
      frameDelay: 0,
      memoryUsage: 0,
      cpuUsage: 0,
      stateUpdateCount: 0,
      lastUpdateTimestamp: 0
    };
    this.history = [];
    this.isRunning = false;
  }

  public static getInstance(): AnimationPerformanceMonitor {
    if (!AnimationPerformanceMonitor.instance) {
      AnimationPerformanceMonitor.instance = new AnimationPerformanceMonitor();
    }
    return AnimationPerformanceMonitor.instance;
  }

  public start(animationStateProvider?: () => AnimationState): () => void {
    this.isRunning = true;
    this.animationStateProvider = animationStateProvider;
    this.startMonitoring();
    
    // Return cleanup function
    return () => this.stop();
  }

  public stop(): void {
    this.isRunning = false;
    this.logPerformanceSummary();
  }
}
```

## Priority Areas for Implementation

To fully align with our architecture rules, the following areas require implementation focus:

### 1. State Persistence Enhancement
- Complete journey state snapshot system implementation
- Add recovery mechanisms for interrupted animations
- Create state comparison tools for debugging

### 2. Camera Behavior Completion
- Finish contextual rhythm implementation
- Add user-controlled zoom options
- Implement terrain-aware camera settings

### 3. Utility File Consolidation
- Complete migration to TypeScript utility files
- Remove transitional re-export files
- Ensure single source of truth for all utilities 

## UI Component Separation

1. **POI Interaction Components**
   - Two distinct components provide different interfaces at different journey phases:
     1. **POIOverlay.tsx**
        - Initial city exploration before animation
        - Displays a vertical list of POIs for selection
        - Contains "Add to Itinerary" functionality
        - Used in the `selecting_pois` journey phase
     
     2. **NotificationPanel.tsx**
        - In-journey discovery interface during animation
        - Category-based filtering
        - Integration with journey animation
        - Used in the `journey` phase
   
   - **Implementation in ExploreMap.tsx**
     ```typescript
     // Control POIOverlay visibility (pre-animation)
     const [showPOIOverlay, setShowPOIOverlay] = useState(false);
     
     // Control NotificationPanel visibility (during animation)
     const [showNotificationPanel, setShowNotificationPanel] = useState(false);
     
     // In JSX, conditionally render the appropriate component
     // POIOverlay is shown when selecting POIs BEFORE animation
     <POIOverlay
       isVisible={showPOIOverlay && selectedDestination !== null && !isAnimating}
       destination={selectedDestination}
       pois={getDestinationPOIs(selectedDestination)}
       selectedPOIs={selectedPOIs}
       onClose={handlePOIOverlayClose}
       onSelectPOI={handlePOIClick}
     />
     
     // NotificationPanel is used during animation in the TravelAnimator
     {isAnimating && (
       <TravelAnimator
         // ...other props
         map={map}
         // TravelAnimator will manage the NotificationPanel internally
       />
     )}
     ```

2. **Modular Animation Architecture**
   - Animation logic is now separated into specialized modules:
     - `src/components/map/animation/`
       - `RouteAnimator.ts` - Core animation calculations
       - `RouteDataLoader.ts` - Route data loading and fallbacks
       - `VehicleController.ts` - Vehicle marker management
       - `AnimationUtils.ts` - UI effects and notifications
       - `CinematicController.ts` - Cinematic sequence management
       - `AnimationDebugTools.tsx` - Debug visualization
       - `VehicleManager.ts` - Vehicle marker DOM element management
       - `AnimationManager.ts` - Animation state and control
       - `ComponentInteractionManager.ts` - Cross-component event coordination
       - `DebugHelper.ts` - Animation debugging utilities

   - `src/components/map/utils/`
       - `PerformanceUtils.ts` - Performance optimization utilities (debounce, throttle)
       - `MapHelpers.ts` - Map utility functions
       - `CameraBehavior.ts` - Camera control utilities
       - `POIDiscovery.ts` - POI detection utilities
       - `MapInstance.ts` - Map instance management
   
   - **Core TravelAnimator Component**
     - Coordinates journey phases and animation state
     - Acts as an orchestrator for the specialized modules
     - Maintains animation state via refs
     - Handles event delegation and prop flow

## Import Path Guidelines

When importing utilities and modules, always use the correct relative path:

```typescript
// For importing from utils directory (from TravelAnimator.tsx)
import { debounce } from './utils/PerformanceUtils';

// For importing animation modules (from TravelAnimator.tsx)
import { VehicleManager } from './animation/VehicleManager';

// For importing utilities from another component
import { getMapInstance } from '../utils/MapInstance';
```

Using incorrect import paths (like `../utils/PerformanceUtils` when you should use `./utils/PerformanceUtils`) will cause build errors.
   
   - **Implementation Pattern**
     ```typescript
     // In TravelAnimator.tsx
     import { calculateAnimationState } from './animation/RouteAnimator';
     import { loadRouteData } from './animation/RouteDataLoader';
     import { forceVehicleVisibility } from './animation/VehicleController';
     import { showCountdown } from './animation/AnimationUtils';
     
     // Use the modules in the component
     const TravelAnimator: React.FC<TravelAnimatorProps> = (props) => {
       // Setup animation state with refs
       const vehicleRef = useRef<mapboxgl.Marker | null>(null);
       const routeRef = useRef<Array<[number, number]>>([]);
       const animationFrameRef = useRef<number | null>(null);
       
       // Orchestrate the animation process
       useEffect(() => {
         if (!props.isAnimating) return;
         
         // Load route data
         const routeData = loadRouteData(props.route);
         
         // Setup animation
         const cleanup = setupAnimation({
           map: props.map,
           route: routeData,
           onProgress: props.onProgressUpdate,
         });
         
         return cleanup;
       }, [props.isAnimating, props.route]);
       
       // Render appropriate UI
       return (
         <div className="travel-animator">
           {/* Vehicle marker */}
           {/* Debug tools */}
           {/* NotificationPanel when appropriate */}
         </div>
       );
     };
     ``` 