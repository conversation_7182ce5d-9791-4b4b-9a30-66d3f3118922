#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to patch Mapbox GL type declarations to work with TypeScript configuration
 * 
 * This script modifies the mapbox-gl.d.ts file to fix the default import issue with point-geometry
 * Run with: node scripts/typescript-fix.js
 */

import { promises as fs, existsSync, mkdirSync } from 'fs';
import { join } from 'path';

const MAPBOX_DTS_PATHS = [
  './node_modules/mapbox-gl/dist/mapbox-gl.d.ts',
  './node_modules/@types/mapbox-gl/index.d.ts'
];

async function patchMapboxDeclaration() {
  for (const dtsPath of MAPBOX_DTS_PATHS) {
    try {
      if (existsSync(dtsPath)) {
        console.log(`Found Mapbox declaration file at ${dtsPath}`);
        let content = await fs.readFile(dtsPath, 'utf8');
        
        // Replace the problematic import
        const originalImport = `import Point from '@mapbox/point-geometry';`;
        const newImport = `// @ts-ignore - Patched by typescript-fix.js\nimport * as _Point from '@mapbox/point-geometry';\n// @ts-ignore - Type fix\nconst Point = _Point;\n`;
        
        if (content.includes(originalImport)) {
          content = content.replace(originalImport, newImport);
          await fs.writeFile(dtsPath, content, 'utf8');
          console.log(`✅ Successfully patched ${dtsPath}`);
        } else {
          console.log(`⚠️ Could not find the expected import pattern in ${dtsPath}`);
        }
      }
    } catch (err) {
      console.error(`❌ Error when patching ${dtsPath}:`, err);
    }
  }
}

// Create our custom declaration file for point-geometry
async function createPointGeometryDeclaration() {
  const dir = './src/types';
  if (!existsSync(dir)) {
    mkdirSync(dir, { recursive: true });
  }
  
  const declPath = join(dir, 'point-geometry.d.ts');
  const content = `
/**
 * Type declaration for @mapbox/point-geometry
 * Added to fix TypeScript import issues
 */
declare module '@mapbox/point-geometry' {
  export default class Point {
    x: number;
    y: number;
    
    constructor(x: number, y: number);
    
    clone(): Point;
    add(p: Point): Point;
    sub(p: Point): Point;
    mult(k: number): Point;
    div(k: number): Point;
    rotate(a: number): Point;
    matMult(m: number[]): Point;
    distSqr(p: Point): number;
    dist(p: Point): number;
    mag(): number;
    equals(p: Point): boolean;
    toString(): string;
    convert(a: any): Point;
  }
}
`;
  
  await fs.writeFile(declPath, content, 'utf8');
  console.log(`✅ Created custom declaration file at ${declPath}`);
}

// Add ignore comments to mapbox-imports.ts
async function updateMapboxImports() {
  const importPath = './src/utils/mapbox-imports.ts';
  if (existsSync(importPath)) {
    try {
      let content = await fs.readFile(importPath, 'utf8');
      
      // Make sure we have the @ts-ignore comments
      if (!content.includes('@ts-ignore')) {
        content = content.replace(
          "import * as _mapboxgl from 'mapbox-gl';",
          "// @ts-ignore - Allow import despite module issues\nimport * as _mapboxgl from 'mapbox-gl';"
        );
        content = content.replace(
          "const mapboxgl = require('mapbox-gl');",
          "// @ts-ignore - Allow require without type issues\nconst mapboxgl = require('mapbox-gl');"
        );
        
        await fs.writeFile(importPath, content, 'utf8');
        console.log(`✅ Updated ${importPath} with @ts-ignore comments`);
      } else {
        console.log(`ℹ️ ${importPath} already has @ts-ignore comments`);
      }
    } catch (err) {
      console.error(`❌ Error when updating ${importPath}:`, err);
    }
  } else {
    console.log(`⚠️ Could not find ${importPath}`);
  }
}

// Run all fixes
async function runFixes() {
  console.log('🔄 Starting TypeScript fixes...');
  await patchMapboxDeclaration();
  await createPointGeometryDeclaration();
  await updateMapboxImports();
  console.log('✅ TypeScript fixes completed');
}

runFixes().catch(err => {
  console.error('🔴 Error running fixes:', err);
}); 