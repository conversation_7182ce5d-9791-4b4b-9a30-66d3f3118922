#!/usr/bin/env node

/**
 * Test Visual Enhancements Script
 * 
 * Validates all visual enhancements and fixes implemented
 */

import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.dirname(__dirname);

// ========================================
// TEST FUNCTIONS
// ========================================

function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m',    // Cyan
    success: '\x1b[32m', // Green
    warning: '\x1b[33m', // Yellow
    error: '\x1b[31m',   // Red
    reset: '\x1b[0m'     // Reset
  };
  
  const icon = {
    info: 'ℹ️',
    success: '✅',
    warning: '⚠️',
    error: '❌'
  };
  
  console.log(`${colors[type]}${icon[type]} ${message}${colors.reset}`);
}

async function testCinematicModeImplementation() {
  log('Testing cinematic mode panel hiding implementation...');
  
  try {
    const filePath = path.join(projectRoot, 'src/pages/enhanced-neutral-demo.tsx');
    const content = await fs.readFile(filePath, 'utf8');
    
    const checks = [
      { pattern: 'const [panelsHidden, setPanelsHidden] = useState(false)', description: 'Panel hidden state' },
      { pattern: 'const [panelTransition, setPanelTransition] = useState(false)', description: 'Panel transition state' },
      { pattern: 'setPanelsHidden(true)', description: 'Panel hiding logic' },
      { pattern: 'duration-500', description: 'Smooth transition duration' },
      { pattern: 'transform translate-x-full', description: 'Panel slide animation' },
      { pattern: 'handleExitCinematicMode', description: 'Exit cinematic mode function' }
    ];
    
    let passedChecks = 0;
    for (const check of checks) {
      if (content.includes(check.pattern)) {
        log(`✓ ${check.description}`, 'success');
        passedChecks++;
      } else {
        log(`✗ Missing: ${check.description}`, 'error');
      }
    }
    
    const success = passedChecks === checks.length;
    if (success) {
      log('Cinematic mode implementation complete', 'success');
    } else {
      log(`Cinematic mode incomplete: ${passedChecks}/${checks.length} checks passed`, 'warning');
    }
    
    return success;
  } catch (error) {
    log(`Cinematic mode test failed: ${error.message}`, 'error');
    return false;
  }
}

async function testExitCinematicButton() {
  log('Testing Exit Cinematic Mode button...');
  
  try {
    const filePath = path.join(projectRoot, 'src/pages/enhanced-neutral-demo.tsx');
    const content = await fs.readFile(filePath, 'utf8');
    
    const checks = [
      { pattern: '{panelsHidden && (', description: 'Conditional button rendering' },
      { pattern: 'Exit Cinematic Mode', description: 'Button text' },
      { pattern: 'fixed top-20 right-4 z-50', description: 'Button positioning' },
      { pattern: 'bg-black/70 hover:bg-black/90', description: 'Button styling' },
      { pattern: 'onClick={handleExitCinematicMode}', description: 'Button click handler' }
    ];
    
    let passedChecks = 0;
    for (const check of checks) {
      if (content.includes(check.pattern)) {
        log(`✓ ${check.description}`, 'success');
        passedChecks++;
      } else {
        log(`✗ Missing: ${check.description}`, 'error');
      }
    }
    
    const success = passedChecks === checks.length;
    if (success) {
      log('Exit cinematic button implementation complete', 'success');
    } else {
      log(`Exit button incomplete: ${passedChecks}/${checks.length} checks passed`, 'warning');
    }
    
    return success;
  } catch (error) {
    log(`Exit button test failed: ${error.message}`, 'error');
    return false;
  }
}

async function testThemeSwitchingFix() {
  log('Testing theme switching fix...');
  
  try {
    const filePath = path.join(projectRoot, 'src/pages/enhanced-neutral-demo.tsx');
    const content = await fs.readFile(filePath, 'utf8');
    
    // Check that ClientProvider wrapper is removed
    if (!content.includes('<ClientProvider initialClientId="neutral">')) {
      log('✓ Local ClientProvider wrapper removed', 'success');
      
      // Check for debug logging
      if (content.includes('console.log(\'🎨 [EnhancedNeutralDemo] Client changed to:\', clientId)')) {
        log('✓ Client change debug logging added', 'success');
        
        // Check for clientData useMemo
        if (content.includes('const clientData = useMemo(() => {')) {
          log('✓ Client data memoization with logging', 'success');
          return true;
        } else {
          log('✗ Client data memoization not found', 'error');
          return false;
        }
      } else {
        log('✗ Debug logging not found', 'error');
        return false;
      }
    } else {
      log('✗ Local ClientProvider wrapper still present', 'error');
      return false;
    }
  } catch (error) {
    log(`Theme switching test failed: ${error.message}`, 'error');
    return false;
  }
}

async function testAnimationCompleteCallback() {
  log('Testing animation complete callback integration...');
  
  try {
    const enhancedNeutralPath = path.join(projectRoot, 'src/pages/enhanced-neutral-demo.tsx');
    const exploreMapPath = path.join(projectRoot, 'src/components/map/ExploreMap.tsx');
    
    const enhancedContent = await fs.readFile(enhancedNeutralPath, 'utf8');
    const exploreMapContent = await fs.readFile(exploreMapPath, 'utf8');
    
    const checks = [
      { content: enhancedContent, pattern: 'onAnimationComplete={handleAnimationComplete}', description: 'Parent passes callback to ExploreMap' },
      { content: exploreMapContent, pattern: 'onAnimationComplete?: () => void', description: 'ExploreMap interface includes callback' },
      { content: exploreMapContent, pattern: 'if (onAnimationComplete) {', description: 'ExploreMap calls parent callback' },
      { content: enhancedContent, pattern: 'const handleAnimationComplete = () => {', description: 'Parent animation complete handler' }
    ];
    
    let passedChecks = 0;
    for (const check of checks) {
      if (check.content.includes(check.pattern)) {
        log(`✓ ${check.description}`, 'success');
        passedChecks++;
      } else {
        log(`✗ Missing: ${check.description}`, 'error');
      }
    }
    
    const success = passedChecks === checks.length;
    if (success) {
      log('Animation complete callback integration complete', 'success');
    } else {
      log(`Callback integration incomplete: ${passedChecks}/${checks.length} checks passed`, 'warning');
    }
    
    return success;
  } catch (error) {
    log(`Animation callback test failed: ${error.message}`, 'error');
    return false;
  }
}

async function testVehicleMarkerEnhancements() {
  log('Testing vehicle marker visibility enhancements...');
  
  try {
    const filePath = path.join(projectRoot, 'src/components/map/animation/VehicleManager.ts');
    const content = await fs.readFile(filePath, 'utf8');
    
    const checks = [
      { pattern: 'backgroundColor: \'#ff3b30\'', description: 'Bright red vehicle color' },
      { pattern: 'width: \'50px\'', description: 'Large vehicle size' },
      { pattern: 'zIndex: \'99999\'', description: 'Ultra high z-index' },
      { pattern: 'border: \'5px solid yellow\'', description: 'Bold yellow border' },
      { pattern: 'boxShadow: \'0 0 30px rgba(255, 255, 0, 0.8)\'', description: 'Glow effect' },
      { pattern: 'debugLabel.textContent = \'VEHICLE\'', description: 'Debug label' }
    ];
    
    let passedChecks = 0;
    for (const check of checks) {
      if (content.includes(check.pattern)) {
        log(`✓ ${check.description}`, 'success');
        passedChecks++;
      } else {
        log(`✗ Missing: ${check.description}`, 'error');
      }
    }
    
    const success = passedChecks === checks.length;
    if (success) {
      log('Vehicle marker enhancements verified', 'success');
    } else {
      log(`Vehicle marker incomplete: ${passedChecks}/${checks.length} checks passed`, 'warning');
    }
    
    return success;
  } catch (error) {
    log(`Vehicle marker test failed: ${error.message}`, 'error');
    return false;
  }
}

async function testResponsiveDesign() {
  log('Testing responsive design enhancements...');
  
  try {
    const filePath = path.join(projectRoot, 'src/pages/enhanced-neutral-demo.tsx');
    const content = await fs.readFile(filePath, 'utf8');
    
    const checks = [
      { pattern: 'isMobile ?', description: 'Mobile-specific logic' },
      { pattern: 'showMobileLeftPanel', description: 'Mobile left panel state' },
      { pattern: 'showMobileRightPanel', description: 'Mobile right panel state' },
      { pattern: 'setShowMobileLeftPanel(false)', description: 'Mobile panel hiding in cinematic mode' },
      { pattern: 'w-4/5 sm:w-3/5 md:w-80', description: 'Responsive panel widths' }
    ];
    
    let passedChecks = 0;
    for (const check of checks) {
      if (content.includes(check.pattern)) {
        log(`✓ ${check.description}`, 'success');
        passedChecks++;
      } else {
        log(`✗ Missing: ${check.description}`, 'error');
      }
    }
    
    const success = passedChecks === checks.length;
    if (success) {
      log('Responsive design enhancements verified', 'success');
    } else {
      log(`Responsive design incomplete: ${passedChecks}/${checks.length} checks passed`, 'warning');
    }
    
    return success;
  } catch (error) {
    log(`Responsive design test failed: ${error.message}`, 'error');
    return false;
  }
}

// ========================================
// MAIN EXECUTION
// ========================================

async function main() {
  console.log('🎨 Testing Visual Enhancements Implementation...\n');
  
  const tests = [
    { name: 'Cinematic Mode Implementation', test: testCinematicModeImplementation },
    { name: 'Exit Cinematic Button', test: testExitCinematicButton },
    { name: 'Theme Switching Fix', test: testThemeSwitchingFix },
    { name: 'Animation Complete Callback', test: testAnimationCompleteCallback },
    { name: 'Vehicle Marker Enhancements', test: testVehicleMarkerEnhancements },
    { name: 'Responsive Design', test: testResponsiveDesign }
  ];
  
  const results = [];
  let passedTests = 0;
  
  for (const { name, test } of tests) {
    console.log(`\n📋 ${name}`);
    
    try {
      const result = await test();
      results.push({ name, passed: result });
      
      if (result) {
        passedTests++;
      }
    } catch (error) {
      log(`Test failed with error: ${error.message}`, 'error');
      results.push({ name, passed: false, error: error.message });
    }
  }
  
  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 VISUAL ENHANCEMENTS TEST RESULTS');
  console.log('='.repeat(60));
  
  results.forEach(result => {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${result.name}`);
    if (result.error) {
      console.log(`     Error: ${result.error}`);
    }
  });
  
  console.log('\n' + '='.repeat(60));
  console.log(`📈 RESULTS: ${passedTests}/${tests.length} visual enhancement tests passed`);
  
  const successRate = (passedTests / tests.length) * 100;
  if (successRate === 100) {
    log(`🎉 ALL VISUAL ENHANCEMENTS PERFECT! (${successRate.toFixed(1)}% success rate)`, 'success');
    console.log('\n🎬 Visual Enhancement Status: FULLY IMPLEMENTED');
    console.log('✅ Cinematic mode with smooth panel hiding');
    console.log('✅ Exit cinematic mode button');
    console.log('✅ Theme switching with proper data updates');
    console.log('✅ Animation complete callbacks');
    console.log('✅ Enhanced vehicle marker visibility');
    console.log('✅ Responsive design optimizations');
    console.log('\n🚀 Users can now enjoy the perfect visual experience!');
  } else if (successRate >= 80) {
    log(`Most visual enhancements working (${successRate.toFixed(1)}% success rate)`, 'warning');
  } else {
    log(`Visual enhancement issues remain (${successRate.toFixed(1)}% success rate)`, 'error');
  }
  
  console.log('\n🌐 Test the enhancements at: http://localhost:8080/');
  console.log('📱 Testing checklist:');
  console.log('   1. Select 2+ cities → Click "Begin Journey"');
  console.log('   2. Verify panels slide out smoothly');
  console.log('   3. Check "Exit Cinematic Mode" button appears');
  console.log('   4. Test theme switching in dropdown');
  console.log('   5. Verify vehicle marker visibility');
  console.log('   6. Test on mobile/tablet devices');
  
  // Exit with appropriate code
  process.exit(successRate === 100 ? 0 : 1);
}

// Run if called directly
main().catch(error => {
  console.error('❌ Visual enhancement testing failed:', error);
  process.exit(1);
});

export { main };
