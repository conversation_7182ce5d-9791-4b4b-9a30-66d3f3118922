#!/bin/bash

# fix-project-structure.sh
# Script to fix organizational issues in the project structure

echo "Starting project structure fix..."
echo "-------------------------------"

# Create backup of current state
echo "Creating backup of the current project state..."
BACKUP_DIR="project_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p $BACKUP_DIR
cp -r src $BACKUP_DIR/
echo "Backup created at $BACKUP_DIR"
echo "-------------------------------"

# 1. Move ExploreMap.tsx to the correct location
echo "Moving ExploreMap.tsx to the correct location in src/components/map/..."
if [ -f "src/components/ExploreMap.tsx" ]; then
  mkdir -p src/components/map
  mv src/components/ExploreMap.tsx src/components/map/
  echo "Moved ExploreMap.tsx successfully"
else
  echo "ExploreMap.tsx not found in src/components/"
fi
echo "-------------------------------"

# 2. Fix import paths in files that import ExploreMap
echo "Updating imports in files that reference ExploreMap..."
find src -type f -name "*.tsx" -o -name "*.ts" | xargs grep -l "@/components/ExploreMap" | while read file; do
  echo "Updating imports in: $file"
  sed -i '' 's|@/components/ExploreMap|@/components/map/ExploreMap|g' "$file"
done
echo "-------------------------------"

# 3. Consistency in MapComponent placement
echo "Checking MapComponent.tsx placement..."
if [ -f "src/components/MapComponent.tsx" ]; then
  echo "Moving MapComponent.tsx to map directory..."
  mkdir -p src/components/map
  mv src/components/MapComponent.tsx src/components/map/
  echo "Moved MapComponent.tsx successfully"
  
  # Update imports referencing MapComponent
  echo "Updating imports for MapComponent..."
  find src -type f -name "*.tsx" -o -name "*.ts" | xargs grep -l "@/components/MapComponent" | while read file; do
    echo "Updating imports in: $file"
    sed -i '' 's|@/components/MapComponent|@/components/map/MapComponent|g' "$file"
  done
  
  # Update relative imports to MapComponent
  find src -type f -name "*.tsx" -o -name "*.ts" | xargs grep -l -E "import .* from ['\"]./MapComponent['\"]" | while read file; do
    echo "Updating relative imports in: $file"
    sed -i '' 's|from \(['"'"'"]\)\./MapComponent\1|from \1@/components/map/MapComponent\1|g' "$file"
  done
else
  echo "MapComponent.tsx already in the correct location or not found"
fi
echo "-------------------------------"

# 4. Fix VehicleManager initialization in ExploreMap.tsx
echo "Fixing VehicleManager initialization in ExploreMap.tsx..."
if [ -f "src/components/map/ExploreMap.tsx" ]; then
  sed -i '' 's|vehicleManagerRef.current = VehicleManager.getInstance().initialize({ map });|await VehicleManager.getInstance().initialize({ map });\n                      vehicleManagerRef.current = VehicleManager.getInstance();|g' "src/components/map/ExploreMap.tsx"
  echo "Updated VehicleManager initialization"
else
  echo "ExploreMap.tsx not found in the expected location"
fi
echo "-------------------------------"

# 5. Fix import inconsistencies in TravelAnimator.tsx
echo "Fixing import inconsistencies in TravelAnimator.tsx..."
if [ -f "src/components/map/TravelAnimator.tsx" ]; then
  # Standardize DebugHelper import to ES module format
  sed -i '' 's|import DebugHelper from \(['"'"'"]\)./animation/DebugHelper\1;|import * as DebugHelper from \1./animation/DebugHelper\1;|g' "src/components/map/TravelAnimator.tsx"
  echo "Standardized DebugHelper import"
else
  echo "TravelAnimator.tsx not found in the expected location"
fi
echo "-------------------------------"

# 6. Ensure AnimationDebugPanel gets map prop
echo "Ensuring AnimationDebugPanel receives map prop in TravelAnimator.tsx..."
if [ -f "src/components/map/TravelAnimator.tsx" ]; then
  sed -i '' 's|{process.env.NODE_ENV !== \(['"'"'"]\)production\1 && (|{process.env.NODE_ENV !== \1production\1 \&\& map \&\& (|g' "src/components/map/TravelAnimator.tsx"
  echo "Updated AnimationDebugPanel rendering condition"
else
  echo "TravelAnimator.tsx not found in the expected location"
fi
echo "-------------------------------"

# 7. Update all relative imports to use absolute paths
echo "Converting problematic relative imports to absolute imports..."
# This is a complex operation that would need to be carefully implemented
# For demonstration, we'll just fix a few common patterns
find src/components -type f -name "*.tsx" -o -name "*.ts" | while read file; do
  # Convert relative imports within components directory to absolute imports
  sed -i '' 's|from \(['"'"'"]\)\.\./\.\./components/|from \1@/components/|g' "$file"
  sed -i '' 's|from \(['"'"'"]\)\.\./\.\./hooks/|from \1@/hooks/|g' "$file"
  sed -i '' 's|from \(['"'"'"]\)\.\./\.\./types/|from \1@/types/|g' "$file"
done
echo "-------------------------------"

# 8. Fix the createTestVehicle function in DebugHelper.ts
echo "Fixing createTestVehicle function in DebugHelper.ts..."
if [ -f "src/components/map/animation/DebugHelper.ts" ]; then
  cat > src/components/map/animation/DebugHelper.ts.new << 'EOL'
// Find the createTestVehicle function and replace it with a fixed version
function fixCreateTestVehicle(content) {
  // This is a simplified approach - in reality, you'd need more sophisticated parsing
  return content.replace(
    /export const createTestVehicle = \(\): void => {[\s\S]*?};/,
    `export const createTestVehicle = (): void => {
  console.log(\`🐞 [\${new Date().toISOString()}] Creating test vehicle in Marrakech\`);
  
  const vehicleManager = VehicleManager.getInstance();
  const map = getMapInstance();
  
  // Check if map is available
  if (!map) {
    console.error(\`❌ [\${new Date().toISOString()}] Cannot create test vehicle: Map not available\`);
    return;
  }
  
  // Default position in Marrakech, Morocco
  const marrakechPosition: [number, number] = [-7.9811, 31.6295];
  
  try {
    // Force vehicle visibility which creates the marker if it doesn't exist
    const marker = vehicleManager.forceVehicleVisibility();
    
    if (marker) {
      console.log(\`✅ [\${new Date().toISOString()}] Test vehicle created successfully\`);
      
      // Update the vehicle position to Marrakech
      vehicleManager.updateVehiclePosition(marrakechPosition, 0);
      
      // Check if the vehicle is visible after a short delay
      setTimeout(() => {
        if (!vehicleManager.checkVehicleVisibility()) {
          console.log(\`⚠️ [\${new Date().toISOString()}] Vehicle not visible, forcing visibility\`);
          vehicleManager.forceVehicleVisibility();
        }
      }, 500);
    } else {
      console.error(\`❌ [\${new Date().toISOString()}] Failed to create test vehicle marker\`);
    }
  } catch (error) {
    console.error(\`❌ [\${new Date().toISOString()}] Error creating test vehicle:\`, error);
  }
};`
  );
}

// Read the current file content
const fs = require('fs');
const content = fs.readFileSync('src/components/map/animation/DebugHelper.ts', 'utf8');
const updatedContent = fixCreateTestVehicle(content);
fs.writeFileSync('src/components/map/animation/DebugHelper.ts', updatedContent, 'utf8');
EOL

  # Now run the script to fix DebugHelper.ts
  node src/components/map/animation/DebugHelper.ts.new
  rm src/components/map/animation/DebugHelper.ts.new
  echo "Updated createTestVehicle function"
else
  echo "DebugHelper.ts not found in the expected location"
fi
echo "-------------------------------"

# 9. Fix showVehiclePosition in AnimationDebugPanel.tsx
echo "Fixing showVehiclePosition in AnimationDebugPanel.tsx..."
if [ -f "src/components/map/animation/AnimationDebugPanel.tsx" ]; then
  cat > src/components/map/animation/AnimationDebugPanel.tsx.fix.js << 'EOL'
// Read the original file
const fs = require('fs');
const path = require('path');
const filePath = 'src/components/map/animation/AnimationDebugPanel.tsx';
let content = fs.readFileSync(filePath, 'utf8');

// Find the showVehiclePosition function and replace it
const functionPattern = /const showVehiclePosition = \(\) => \{[\s\S]*?};/;
const replacementFunction = `const showVehiclePosition = () => {
    try {
      console.log(\`ℹ️ [\${new Date().toISOString()}] Showing vehicle position\`);
      
      const vehicleManager = VehicleManager.getInstance();
      if (!vehicleManager) {
        const errorMsg = "VehicleManager not available";
        console.error(\`❌ [\${new Date().toISOString()}] \${errorMsg}\`);
        setState(prev => ({
          ...prev,
          errors: [...prev.errors, \`\${new Date().toISOString()} - \${errorMsg}\`].slice(-5)
        }));
        return;
      }
      
      // Ensure the vehicle is visible first
      vehicleManager.forceVehicleVisibility();
      
      // Get the vehicle position
      const position = vehicleManager.getVehiclePosition();
      
      if (!position.lngLat && !position.pixel) {
        const errorMsg = "Vehicle position is not available";
        console.error(\`❌ [\${new Date().toISOString()}] \${errorMsg}\`);
        setState(prev => ({
          ...prev,
          errors: [...prev.errors, \`\${new Date().toISOString()} - \${errorMsg}\`].slice(-5)
        }));
      } else {
        console.log(\`✅ [\${new Date().toISOString()}] Vehicle position:\`, position);
        setState(prev => ({
          ...prev,
          position: position.lngLat ? [position.lngLat.lng, position.lngLat.lat] : null,
          vehicleVisible: true,
          errors: [...prev.errors, \`\${new Date().toISOString()} - Position: \${position.lngLat ? \`[\${position.lngLat.lng.toFixed(5)}, \${position.lngLat.lat.toFixed(5)}]\` : 'Unknown'}\`].slice(-5)
        }));
      }
    } catch (error) {
      const errorMsg = \`Error showing vehicle position: \${error}\`;
      console.error(\`❌ [\${new Date().toISOString()}] \${errorMsg}\`);
      setState(prev => ({
        ...prev,
        errors: [...prev.errors, \`\${new Date().toISOString()} - \${errorMsg}\`].slice(-5)
      }));
    }
  };`;

content = content.replace(functionPattern, replacementFunction);
fs.writeFileSync(filePath, content, 'utf8');
console.log('AnimationDebugPanel.tsx updated successfully');
EOL

  # Run the Node.js script to fix the file
  node src/components/map/animation/AnimationDebugPanel.tsx.fix.js
  rm src/components/map/animation/AnimationDebugPanel.tsx.fix.js
  echo "Updated showVehiclePosition function"
else
  echo "AnimationDebugPanel.tsx not found in the expected location"
fi
echo "-------------------------------"

echo "Project structure fixes completed!"
echo "Please check the changes and test the application."
echo "Backup of the original files is available at $BACKUP_DIR" 