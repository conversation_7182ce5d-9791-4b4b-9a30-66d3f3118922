/**
 * This script injects environment variables into HTML files in the public directory
 * 
 * It specifically looks for tokens like "<%- process.env.VARIABLE_NAME %>" and
 * replaces them with the actual value from the environment.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get the project root directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');
const publicDir = path.join(projectRoot, 'public');

// Function to walk through directory and find all HTML files
const findHtmlFiles = (dir) => {
  const results = [];
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      results.push(...findHtmlFiles(filePath));
    } else if (file.endsWith('.html') || file.endsWith('.js')) {
      results.push(filePath);
    }
  }
  
  return results;
};

// Function to inject environment variables into file content
const injectEnvVars = (content) => {
  // Replace all <%- process.env.VAR_NAME %> with the actual environment variable
  return content.replace(/<%-(.*?)process\.env\.([A-Za-z0-9_]+)(.*?)%>/g, (match, prefix, varName, suffix) => {
    const envValue = process.env[varName] || '';
    if (!envValue) {
      console.warn(`Warning: Environment variable ${varName} not found`);
    }
    return envValue;
  });
};

// Main function to process all files
const processFiles = () => {
  console.log('Injecting environment variables into HTML/JS files...');
  
  const htmlFiles = findHtmlFiles(publicDir);
  let modifiedCount = 0;
  
  for (const filePath of htmlFiles) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const modifiedContent = injectEnvVars(content);
      
      if (content !== modifiedContent) {
        fs.writeFileSync(filePath, modifiedContent, 'utf8');
        modifiedCount++;
        console.log(`Updated: ${path.relative(projectRoot, filePath)}`);
      }
    } catch (error) {
      console.error(`Error processing ${filePath}:`, error);
    }
  }
  
  console.log(`Finished processing ${htmlFiles.length} files. Modified ${modifiedCount} files.`);
};

// Run the script
processFiles(); 