#!/usr/bin/env node

/**
 * LOGGING CLEANUP SCRIPT
 * Removes excessive console.log statements for production readiness
 */

const fs = require('fs');
const path = require('path');

// Files with excessive logging that need cleanup
const filesToClean = [
  'src/components/map/ExploreMap.tsx',
  'src/components/layout/LeftPane.tsx', 
  'src/components/layout/RightPane.tsx',
  'src/components/map/DirectPOIMarkers.tsx',
  'src/types/POITypes.ts',
  'src/components/map/JourneyButton.tsx',
  'src/pages/moroccan-demo.tsx',
  'src/utils/mapUtils.ts'
];

// Patterns to remove or replace
const cleanupPatterns = [
  // Remove verbose component lifecycle logs
  {
    pattern: /console\.log\(\s*['"`]\[.*?\]\s*.*?component.*?rendered['"`]\s*\);?\s*$/gm,
    replacement: ''
  },
  
  // Remove excessive state logging
  {
    pattern: /console\.log\(\s*['"`]\[.*?\]\s*.*?updated:['"`]\s*,.*?\);?\s*$/gm,
    replacement: ''
  },
  
  // Remove POI normalization logs (keep errors)
  {
    pattern: /console\.log\(\s*['"`]\[normalizePOI\].*?['"`]\s*,.*?\);?\s*$/gm,
    replacement: ''
  },
  
  // Remove map readiness logs
  {
    pattern: /console\.log\(\s*['"`].*?Map.*?ready.*?['"`]\s*\);?\s*$/gm,
    replacement: ''
  },
  
  // Remove render logs
  {
    pattern: /console\.log\(\s*['"`].*?Render.*?['"`]\s*,.*?\);?\s*$/gm,
    replacement: ''
  },
  
  // Convert excessive console.log to conditional logging
  {
    pattern: /console\.log\(\s*['"`]🔍.*?['"`]/g,
    replacement: 'process.env.NODE_ENV === "development" && console.log("🔍'
  }
];

// Backup directory
const backupDir = 'backups/pre-log-cleanup';

function createBackup(filePath) {
  const backupPath = path.join(backupDir, filePath);
  const backupDirPath = path.dirname(backupPath);
  
  // Create backup directory if it doesn't exist
  if (!fs.existsSync(backupDirPath)) {
    fs.mkdirSync(backupDirPath, { recursive: true });
  }
  
  // Copy original file to backup
  if (fs.existsSync(filePath)) {
    fs.copyFileSync(filePath, backupPath);
    console.log(`✅ Backed up: ${filePath} -> ${backupPath}`);
  }
}

function cleanupFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  File not found: ${filePath}`);
    return;
  }
  
  // Create backup first
  createBackup(filePath);
  
  // Read file content
  let content = fs.readFileSync(filePath, 'utf8');
  const originalLength = content.length;
  
  // Apply cleanup patterns
  let changesCount = 0;
  cleanupPatterns.forEach(({ pattern, replacement }) => {
    const matches = content.match(pattern);
    if (matches) {
      changesCount += matches.length;
      content = content.replace(pattern, replacement);
    }
  });
  
  // Remove empty lines left by removed console.logs
  content = content.replace(/^\s*\n/gm, '');
  
  // Write cleaned content back
  fs.writeFileSync(filePath, content, 'utf8');
  
  const newLength = content.length;
  const reduction = originalLength - newLength;
  
  console.log(`🧹 Cleaned: ${filePath}`);
  console.log(`   - Removed ${changesCount} log statements`);
  console.log(`   - Reduced size by ${reduction} characters`);
}

function main() {
  console.log('🚀 Starting logging cleanup...\n');
  
  // Create backup directory
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  let totalFiles = 0;
  let totalChanges = 0;
  
  // Clean each file
  filesToClean.forEach(filePath => {
    try {
      cleanupFile(filePath);
      totalFiles++;
    } catch (error) {
      console.error(`❌ Error cleaning ${filePath}:`, error.message);
    }
  });
  
  console.log(`\n✅ Cleanup complete!`);
  console.log(`   - Processed ${totalFiles} files`);
  console.log(`   - Backups saved to: ${backupDir}`);
  console.log(`   - To restore: cp ${backupDir}/[file] [original-location]`);
  
  // Create environment setup reminder
  const envReminder = `
# LOGGING CONFIGURATION
# Add to your .env file for development logging:

# Enable debug mode (shows all logs)
REACT_APP_DEBUG=true

# Disable debug mode for production-like testing
REACT_APP_DEBUG=false

# The new logging system will automatically:
# - Show only errors in production
# - Show info logs in development
# - Show verbose logs only when REACT_APP_DEBUG=true
`;
  
  fs.writeFileSync('.env.logging-example', envReminder);
  console.log(`\n📝 Created .env.logging-example with configuration options`);
}

if (require.main === module) {
  main();
}

module.exports = { cleanupFile, cleanupPatterns };
