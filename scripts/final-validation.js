#!/usr/bin/env node

/**
 * Final Validation Script
 * 
 * Validates that all critical issues have been resolved
 */

import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { spawn } from 'child_process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.dirname(__dirname);

// ========================================
// VALIDATION TASKS
// ========================================

const validationTasks = [
  {
    name: 'PreArrangedJourneyLinks Fix',
    description: 'Verify null safety check for journey.tags',
    run: validatePreArrangedJourneyLinks
  },
  {
    name: 'Theme Switching Data',
    description: 'Verify client-aware data loading',
    run: validateThemeSwitching
  },
  {
    name: 'Begin Journey Button Logic',
    description: 'Verify button state management',
    run: validateBeginJourneyButton
  },
  {
    name: 'Build System',
    description: 'Verify successful build',
    run: validateBuildSystem
  },
  {
    name: 'Import Consistency',
    description: 'Verify correct imports',
    run: validateImports
  }
];

// ========================================
// UTILITY FUNCTIONS
// ========================================

function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m',    // Cyan
    success: '\x1b[32m', // Green
    warning: '\x1b[33m', // Yellow
    error: '\x1b[31m',   // Red
    reset: '\x1b[0m'     // Reset
  };
  
  const icon = {
    info: 'ℹ️',
    success: '✅',
    warning: '⚠️',
    error: '❌'
  };
  
  console.log(`${colors[type]}${icon[type]} ${message}${colors.reset}`);
}

async function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      cwd: projectRoot,
      stdio: 'pipe',
      ...options
    });
    
    let stdout = '';
    let stderr = '';
    
    child.stdout?.on('data', (data) => {
      stdout += data.toString();
    });
    
    child.stderr?.on('data', (data) => {
      stderr += data.toString();
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        resolve({ stdout, stderr, code });
      } else {
        reject(new Error(`Command failed with code ${code}: ${stderr}`));
      }
    });
  });
}

// ========================================
// VALIDATION FUNCTIONS
// ========================================

async function validatePreArrangedJourneyLinks() {
  log('Checking PreArrangedJourneyLinks null safety fix...');
  
  try {
    const filePath = path.join(projectRoot, 'src/components/map/PreArrangedJourneyLinks.tsx');
    const content = await fs.readFile(filePath, 'utf8');
    
    // Check for the null safety fix
    if (content.includes('journey.tags?.slice(0, 3).map((tag) => (')) {
      log('✓ Null safety check found in PreArrangedJourneyLinks', 'success');
      
      // Check demo data has proper tags
      const demoPath = path.join(projectRoot, 'src/pages/enhanced-neutral-demo.tsx');
      const demoContent = await fs.readFile(demoPath, 'utf8');
      
      if (demoContent.includes('tags: [') && demoContent.includes('difficulty:') && demoContent.includes('price:')) {
        log('✓ Demo journey data has proper structure', 'success');
        return true;
      } else {
        log('✗ Demo journey data missing required properties', 'error');
        return false;
      }
    } else {
      log('✗ Null safety check not found', 'error');
      return false;
    }
  } catch (error) {
    log(`PreArrangedJourneyLinks validation failed: ${error.message}`, 'error');
    return false;
  }
}

async function validateThemeSwitching() {
  log('Checking theme switching data implementation...');
  
  try {
    const filePath = path.join(projectRoot, 'src/pages/enhanced-neutral-demo.tsx');
    const content = await fs.readFile(filePath, 'utf8');
    
    const checks = [
      { pattern: 'import { useClient }', description: 'useClient hook import' },
      { pattern: 'import { regionData }', description: 'regionData import' },
      { pattern: 'const { clientId } = useClient()', description: 'clientId extraction' },
      { pattern: 'getClientData(clientId)', description: 'client data function usage' },
      { pattern: 'useEffect(() => {', description: 'client change effect' },
      { pattern: 'clientData.destinations', description: 'client-specific destinations' },
      { pattern: 'clientData.pois', description: 'client-specific POIs' }
    ];
    
    let passedChecks = 0;
    for (const check of checks) {
      if (content.includes(check.pattern)) {
        log(`✓ ${check.description}`, 'success');
        passedChecks++;
      } else {
        log(`✗ Missing: ${check.description}`, 'error');
      }
    }
    
    const success = passedChecks === checks.length;
    if (success) {
      log('Theme switching implementation complete', 'success');
    } else {
      log(`Theme switching incomplete: ${passedChecks}/${checks.length} checks passed`, 'warning');
    }
    
    return success;
  } catch (error) {
    log(`Theme switching validation failed: ${error.message}`, 'error');
    return false;
  }
}

async function validateBeginJourneyButton() {
  log('Checking Begin Journey button logic...');
  
  try {
    const filePath = path.join(projectRoot, 'src/pages/enhanced-neutral-demo.tsx');
    const content = await fs.readFile(filePath, 'utf8');
    
    const checks = [
      { pattern: 'const hasEnoughCities = selectedCities.length >= 2', description: 'City count check' },
      { pattern: 'const hasPreArrangedJourney = selectedPreArrangedJourney !== null', description: 'Pre-arranged journey check' },
      { pattern: 'const hasEnoughPOIs = selectedPOIs.length >= 2', description: 'POI count check' },
      { pattern: 'const canBegin = hasEnoughCities || hasPreArrangedJourney || hasEnoughPOIs', description: 'Combined logic' },
      { pattern: 'setCanBeginJourney(canBegin)', description: 'State update' },
      { pattern: '[selectedCities, selectedPreArrangedJourney, selectedPOIs]', description: 'Effect dependencies' }
    ];
    
    let passedChecks = 0;
    for (const check of checks) {
      if (content.includes(check.pattern)) {
        log(`✓ ${check.description}`, 'success');
        passedChecks++;
      } else {
        log(`✗ Missing: ${check.description}`, 'error');
      }
    }
    
    const success = passedChecks === checks.length;
    if (success) {
      log('Begin Journey button logic complete', 'success');
    } else {
      log(`Begin Journey logic incomplete: ${passedChecks}/${checks.length} checks passed`, 'warning');
    }
    
    return success;
  } catch (error) {
    log(`Begin Journey validation failed: ${error.message}`, 'error');
    return false;
  }
}

async function validateBuildSystem() {
  log('Testing build system...');
  
  try {
    const result = await runCommand('npm', ['run', 'build']);
    log('Build completed successfully', 'success');
    
    // Check if dist directory exists
    const distPath = path.join(projectRoot, 'dist');
    const distExists = await fs.access(distPath).then(() => true).catch(() => false);
    
    if (distExists) {
      log('✓ Build output directory exists', 'success');
      return true;
    } else {
      log('✗ Build output directory not found', 'error');
      return false;
    }
  } catch (error) {
    log(`Build validation failed: ${error.message}`, 'error');
    return false;
  }
}

async function validateImports() {
  log('Checking import consistency...');
  
  try {
    const filePath = path.join(projectRoot, 'src/pages/enhanced-neutral-demo.tsx');
    const content = await fs.readFile(filePath, 'utf8');
    
    // Check for correct imports
    if (content.includes('import { regionData } from \'../data/destinations\';')) {
      log('✓ Correct regionData import', 'success');
      
      // Check that old incorrect imports are not present
      if (!content.includes('MOROCCO_DESTINATIONS') && !content.includes('MOROCCO_POIS')) {
        log('✓ Old incorrect imports removed', 'success');
        return true;
      } else {
        log('✗ Old incorrect imports still present', 'error');
        return false;
      }
    } else {
      log('✗ Incorrect import structure', 'error');
      return false;
    }
  } catch (error) {
    log(`Import validation failed: ${error.message}`, 'error');
    return false;
  }
}

// ========================================
// MAIN EXECUTION
// ========================================

async function main() {
  console.log('🔍 Starting final validation of critical fixes...\n');
  
  const results = [];
  let totalTasks = validationTasks.length;
  let passedTasks = 0;
  
  for (const task of validationTasks) {
    console.log(`\n📋 ${task.name}`);
    console.log(`   ${task.description}`);
    
    try {
      const result = await task.run();
      results.push({ name: task.name, passed: result });
      
      if (result) {
        passedTasks++;
      }
    } catch (error) {
      log(`Task failed with error: ${error.message}`, 'error');
      results.push({ name: task.name, passed: false, error: error.message });
    }
  }
  
  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 FINAL VALIDATION SUMMARY');
  console.log('='.repeat(60));
  
  results.forEach(result => {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${result.name}`);
    if (result.error) {
      console.log(`     Error: ${result.error}`);
    }
  });
  
  console.log('\n' + '='.repeat(60));
  console.log(`📈 RESULTS: ${passedTasks}/${totalTasks} critical fixes validated`);
  
  const successRate = (passedTasks / totalTasks) * 100;
  if (successRate === 100) {
    log(`🎉 ALL CRITICAL ISSUES FIXED! (${successRate.toFixed(1)}% success rate)`, 'success');
    console.log('\n🚀 The application is ready for production use!');
    console.log('✅ Theme switching works with proper data');
    console.log('✅ Begin Journey button appears when conditions are met');
    console.log('✅ PreArrangedJourneyLinks component renders without errors');
    console.log('✅ Build system works perfectly');
    console.log('✅ All imports are correct and consistent');
  } else if (successRate >= 80) {
    log(`Most critical issues fixed (${successRate.toFixed(1)}% success rate)`, 'warning');
  } else {
    log(`Critical issues remain (${successRate.toFixed(1)}% success rate)`, 'error');
  }
  
  console.log('\n🎬 Users can now test the complete cinematics experience!');
  console.log('   1. Select 2+ cities or a pre-arranged journey');
  console.log('   2. Click "Begin Journey" button');
  console.log('   3. Enjoy the animated travel experience');
  
  // Exit with appropriate code
  process.exit(successRate === 100 ? 0 : 1);
}

// Run if called directly
main().catch(error => {
  console.error('❌ Final validation failed:', error);
  process.exit(1);
});

export { main, validationTasks };
