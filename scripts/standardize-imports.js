#!/usr/bin/env node

/**
 * Import Path Standardization Script
 *
 * Automatically fixes inconsistent import paths across the codebase
 * Converts relative imports to use @ alias where appropriate
 */

import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Import patterns to standardize
const IMPORT_PATTERNS = [
  // Convert relative imports to absolute imports
  {
    pattern: /from ['"]\.\.\/\.\.\/types\/([^'"]+)['"]/g,
    replacement: "from '@/types/$1'"
  },
  {
    pattern: /from ['"]\.\.\/\.\.\/components\/([^'"]+)['"]/g,
    replacement: "from '@/components/$1'"
  },
  {
    pattern: /from ['"]\.\.\/\.\.\/hooks\/([^'"]+)['"]/g,
    replacement: "from '@/hooks/$1'"
  },
  {
    pattern: /from ['"]\.\.\/\.\.\/utils\/([^'"]+)['"]/g,
    replacement: "from '@/utils/$1'"
  },
  {
    pattern: /from ['"]\.\.\/\.\.\/contexts\/([^'"]+)['"]/g,
    replacement: "from '@/contexts/$1'"
  },
  {
    pattern: /from ['"]\.\.\/\.\.\/config\/([^'"]+)['"]/g,
    replacement: "from '@/config/$1'"
  },
  
  // Fix three-level relative imports
  {
    pattern: /from ['"]\.\.\/\.\.\/\.\.\/types\/([^'"]+)['"]/g,
    replacement: "from '@/types/$1'"
  },
  {
    pattern: /from ['"]\.\.\/\.\.\/\.\.\/components\/([^'"]+)['"]/g,
    replacement: "from '@/components/$1'"
  },
  {
    pattern: /from ['"]\.\.\/\.\.\/\.\.\/hooks\/([^'"]+)['"]/g,
    replacement: "from '@/hooks/$1'"
  },
  
  // Standardize specific problematic imports
  {
    pattern: /from ['"]\.\/animation\/utils\/types['"]/g,
    replacement: "from '@/types'"
  },
  {
    pattern: /from ['"]\.\.\/types\/POITypes['"]/g,
    replacement: "from '@/types'"
  },
  {
    pattern: /from ['"]\.\.\/\.\.\/types\/POITypes['"]/g,
    replacement: "from '@/types'"
  },
  
  // Fix animation-specific imports
  {
    pattern: /from ['"]\.\/animation\/([^'"]+)['"]/g,
    replacement: "from './animation/$1'"
  },
  
  // Standardize React imports
  {
    pattern: /import React from ['"]react['"]/g,
    replacement: "import React from 'react'"
  },
  {
    pattern: /import \* as React from ['"]react['"]/g,
    replacement: "import React from 'react'"
  }
];

// Files to process
const EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];
const IGNORE_DIRS = ['node_modules', 'dist', 'build', '.git'];

/**
 * Get all files to process
 */
async function getAllFiles(dir, files = []) {
  const entries = await fs.readdir(dir, { withFileTypes: true });
  
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    
    if (entry.isDirectory() && !IGNORE_DIRS.includes(entry.name)) {
      await getAllFiles(fullPath, files);
    } else if (entry.isFile() && EXTENSIONS.includes(path.extname(entry.name))) {
      files.push(fullPath);
    }
  }
  
  return files;
}

/**
 * Process a single file
 */
async function processFile(filePath) {
  try {
    let content = await fs.readFile(filePath, 'utf8');
    let modified = false;
    const changes = [];
    
    // Apply each import pattern
    for (const { pattern, replacement } of IMPORT_PATTERNS) {
      const originalContent = content;
      content = content.replace(pattern, replacement);
      
      if (content !== originalContent) {
        modified = true;
        const matches = originalContent.match(pattern);
        if (matches) {
          changes.push({
            pattern: pattern.toString(),
            matches: matches.length,
            replacement
          });
        }
      }
    }
    
    // Write back if modified
    if (modified) {
      await fs.writeFile(filePath, content, 'utf8');
      console.log(`✅ Updated: ${filePath}`);
      changes.forEach(change => {
        console.log(`   - ${change.matches} replacements: ${change.pattern}`);
      });
      return { file: filePath, changes };
    }
    
    return null;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return null;
  }
}

/**
 * Main execution
 */
async function main() {
  console.log('🚀 Starting import path standardization...\n');

  const startTime = Date.now();
  const srcDir = path.join(path.dirname(__dirname), 'src');
  
  try {
    // Get all files to process
    console.log('📁 Scanning files...');
    const files = await getAllFiles(srcDir);
    console.log(`Found ${files.length} files to process\n`);
    
    // Process files
    console.log('🔄 Processing files...');
    const results = [];
    
    for (const file of files) {
      const result = await processFile(file);
      if (result) {
        results.push(result);
      }
    }
    
    // Summary
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    console.log('\n📊 Summary:');
    console.log(`   - Files processed: ${files.length}`);
    console.log(`   - Files modified: ${results.length}`);
    console.log(`   - Total changes: ${results.reduce((sum, r) => sum + r.changes.length, 0)}`);
    console.log(`   - Duration: ${duration}s`);
    
    if (results.length > 0) {
      console.log('\n📝 Modified files:');
      results.forEach(result => {
        console.log(`   - ${result.file}`);
      });
    }
    
    console.log('\n✅ Import standardization completed!');
    
  } catch (error) {
    console.error('❌ Fatal error:', error);
    process.exit(1);
  }
}

// Run if called directly
main();

export { main, processFile, getAllFiles };
