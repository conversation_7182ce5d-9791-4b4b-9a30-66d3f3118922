#!/usr/bin/env node

/**
 * Mobile Functionality Test Script
 * 
 * Tests all mobile-specific features and CTAs
 */

import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.dirname(__dirname);

function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m',    // Cyan
    success: '\x1b[32m', // Green
    warning: '\x1b[33m', // Yellow
    error: '\x1b[31m',   // Red
    reset: '\x1b[0m'     // Reset
  };
  
  const icon = {
    info: 'ℹ️',
    success: '✅',
    warning: '⚠️',
    error: '❌'
  };
  
  console.log(`${colors[type]}${icon[type]} ${message}${colors.reset}`);
}

async function testMobilePanelManagement() {
  log('Testing mobile panel management...');
  
  try {
    const filePath = path.join(projectRoot, 'src/pages/enhanced-neutral-demo.tsx');
    const content = await fs.readFile(filePath, 'utf8');
    
    const checks = [
      { pattern: 'const [showMobileLeftPanel, setShowMobileLeftPanel] = useState(false)', description: 'Mobile left panel state' },
      { pattern: 'const [showMobileRightPanel, setShowMobileRightPanel] = useState(false)', description: 'Mobile right panel state' },
      { pattern: 'const toggleLeftPanel = () => {', description: 'Left panel toggle function' },
      { pattern: 'const toggleRightPanel = () => {', description: 'Right panel toggle function' },
      { pattern: 'setShowMobileLeftPanel(!showMobileLeftPanel)', description: 'Left panel toggle logic' },
      { pattern: 'setShowMobileRightPanel(!showMobileRightPanel)', description: 'Right panel toggle logic' }
    ];
    
    let passedChecks = 0;
    for (const check of checks) {
      if (content.includes(check.pattern)) {
        log(`✓ ${check.description}`, 'success');
        passedChecks++;
      } else {
        log(`✗ Missing: ${check.description}`, 'error');
      }
    }
    
    return passedChecks >= 5; // Allow some flexibility
  } catch (error) {
    log(`Mobile panel test failed: ${error.message}`, 'error');
    return false;
  }
}

async function testMobileButtons() {
  log('Testing mobile floating buttons...');
  
  try {
    const filePath = path.join(projectRoot, 'src/pages/enhanced-neutral-demo.tsx');
    const content = await fs.readFile(filePath, 'utf8');
    
    const checks = [
      { pattern: '{isMobile && (', description: 'Mobile-specific rendering' },
      { pattern: 'fixed bottom-6 left-6', description: 'Left floating button positioning' },
      { pattern: 'fixed bottom-6 right-6', description: 'Right floating button positioning' },
      { pattern: 'w-14 h-14 rounded-full', description: 'Circular button styling' },
      { pattern: 'onClick={toggleLeftPanel}', description: 'Left button click handler' },
      { pattern: 'onClick={toggleRightPanel}', description: 'Right button click handler' },
      { pattern: 'Explore', description: 'Left button label' },
      { pattern: 'Plan', description: 'Right button label' }
    ];
    
    let passedChecks = 0;
    for (const check of checks) {
      if (content.includes(check.pattern)) {
        log(`✓ ${check.description}`, 'success');
        passedChecks++;
      } else {
        log(`✗ Missing: ${check.description}`, 'error');
      }
    }
    
    return passedChecks >= 6;
  } catch (error) {
    log(`Mobile buttons test failed: ${error.message}`, 'error');
    return false;
  }
}

async function testMobileOverlays() {
  log('Testing mobile overlay functionality...');
  
  try {
    const filePath = path.join(projectRoot, 'src/pages/enhanced-neutral-demo.tsx');
    const content = await fs.readFile(filePath, 'utf8');
    
    const checks = [
      { pattern: 'fixed inset-0 bg-black bg-opacity-50', description: 'Overlay background' },
      { pattern: 'onClick={() => setShowMobileLeftPanel(false)}', description: 'Left overlay dismiss' },
      { pattern: 'onClick={() => setShowMobileRightPanel(false)}', description: 'Right overlay dismiss' },
      { pattern: 'z-20', description: 'Overlay z-index' },
      { pattern: 'z-30', description: 'Panel z-index above overlay' }
    ];
    
    let passedChecks = 0;
    for (const check of checks) {
      if (content.includes(check.pattern)) {
        log(`✓ ${check.description}`, 'success');
        passedChecks++;
      } else {
        log(`✗ Missing: ${check.description}`, 'error');
      }
    }
    
    return passedChecks >= 4;
  } catch (error) {
    log(`Mobile overlays test failed: ${error.message}`, 'error');
    return false;
  }
}

async function testMobileCinematicMode() {
  log('Testing mobile cinematic mode...');
  
  try {
    const filePath = path.join(projectRoot, 'src/pages/enhanced-neutral-demo.tsx');
    const content = await fs.readFile(filePath, 'utf8');
    
    const checks = [
      { pattern: 'setShowMobileLeftPanel(false)', description: 'Hide left panel in cinematic mode' },
      { pattern: 'setShowMobileRightPanel(false)', description: 'Hide right panel in cinematic mode' },
      { pattern: 'if (isMobile) {', description: 'Mobile-specific cinematic logic' },
      { pattern: 'panelsHidden', description: 'Panel hidden state for mobile' }
    ];
    
    let passedChecks = 0;
    for (const check of checks) {
      if (content.includes(check.pattern)) {
        log(`✓ ${check.description}`, 'success');
        passedChecks++;
      } else {
        log(`✗ Missing: ${check.description}`, 'error');
      }
    }
    
    return passedChecks >= 3;
  } catch (error) {
    log(`Mobile cinematic test failed: ${error.message}`, 'error');
    return false;
  }
}

async function testMobileAnimationControls() {
  log('Testing mobile animation controls...');
  
  try {
    const filePath = path.join(projectRoot, 'src/pages/enhanced-neutral-demo.tsx');
    const content = await fs.readFile(filePath, 'utf8');
    
    const checks = [
      { pattern: 'fixed bottom-6 left-1/2 transform -translate-x-1/2', description: 'Centered animation controls' },
      { pattern: 'bg-black/80 backdrop-blur-sm', description: 'Mobile-friendly control styling' },
      { pattern: 'rounded-xl px-6 py-4', description: 'Touch-friendly control sizing' },
      { pattern: 'w-10 h-10', description: 'Touch-friendly button size' },
      { pattern: 'min-w-[200px]', description: 'Adequate progress bar width' }
    ];
    
    let passedChecks = 0;
    for (const check of checks) {
      if (content.includes(check.pattern)) {
        log(`✓ ${check.description}`, 'success');
        passedChecks++;
      } else {
        log(`✗ Missing: ${check.description}`, 'error');
      }
    }
    
    return passedChecks >= 4;
  } catch (error) {
    log(`Mobile animation controls test failed: ${error.message}`, 'error');
    return false;
  }
}

async function testMobileResponsiveDesign() {
  log('Testing mobile responsive design...');
  
  try {
    const filePath = path.join(projectRoot, 'src/pages/enhanced-neutral-demo.tsx');
    const content = await fs.readFile(filePath, 'utf8');
    
    const checks = [
      { pattern: 'w-4/5 sm:w-3/5 md:w-80', description: 'Responsive panel widths' },
      { pattern: 'height: \'100vh\'', description: 'Full height mobile panels' },
      { pattern: 'marginTop: \'70px\'', description: 'Top bar spacing' },
      { pattern: 'transform translate-x-0', description: 'Panel slide animations' },
      { pattern: 'transform -translate-x-full', description: 'Panel hide animations' }
    ];
    
    let passedChecks = 0;
    for (const check of checks) {
      if (content.includes(check.pattern)) {
        log(`✓ ${check.description}`, 'success');
        passedChecks++;
      } else {
        log(`✗ Missing: ${check.description}`, 'error');
      }
    }
    
    return passedChecks >= 4;
  } catch (error) {
    log(`Mobile responsive test failed: ${error.message}`, 'error');
    return false;
  }
}

async function main() {
  console.log('📱 Testing Mobile Functionality...\n');
  
  const tests = [
    { name: 'Mobile Panel Management', test: testMobilePanelManagement },
    { name: 'Mobile Floating Buttons', test: testMobileButtons },
    { name: 'Mobile Overlays', test: testMobileOverlays },
    { name: 'Mobile Cinematic Mode', test: testMobileCinematicMode },
    { name: 'Mobile Animation Controls', test: testMobileAnimationControls },
    { name: 'Mobile Responsive Design', test: testMobileResponsiveDesign }
  ];
  
  const results = [];
  let passedTests = 0;
  
  for (const { name, test } of tests) {
    console.log(`\n📋 ${name}`);
    
    try {
      const result = await test();
      results.push({ name, passed: result });
      
      if (result) {
        passedTests++;
      }
    } catch (error) {
      log(`Test failed with error: ${error.message}`, 'error');
      results.push({ name, passed: false, error: error.message });
    }
  }
  
  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 MOBILE FUNCTIONALITY TEST RESULTS');
  console.log('='.repeat(60));
  
  results.forEach(result => {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${result.name}`);
    if (result.error) {
      console.log(`     Error: ${result.error}`);
    }
  });
  
  console.log('\n' + '='.repeat(60));
  console.log(`📈 RESULTS: ${passedTests}/${tests.length} mobile functionality tests passed`);
  
  const successRate = (passedTests / tests.length) * 100;
  if (successRate === 100) {
    log(`🎉 ALL MOBILE FEATURES PERFECT! (${successRate.toFixed(1)}% success rate)`, 'success');
    console.log('\n📱 Mobile Status: FULLY OPTIMIZED');
    console.log('✅ Panel management with floating buttons');
    console.log('✅ Touch-friendly overlays and dismissal');
    console.log('✅ Cinematic mode for mobile devices');
    console.log('✅ Responsive animation controls');
    console.log('✅ Adaptive design across screen sizes');
    console.log('\n🚀 Mobile experience is production-ready!');
  } else if (successRate >= 80) {
    log(`Most mobile features working (${successRate.toFixed(1)}% success rate)`, 'warning');
  } else {
    log(`Mobile functionality issues remain (${successRate.toFixed(1)}% success rate)`, 'error');
  }
  
  console.log('\n📱 Mobile Testing Checklist:');
  console.log('   1. Test on iPhone/Android devices');
  console.log('   2. Verify floating button accessibility');
  console.log('   3. Check panel slide animations');
  console.log('   4. Test cinematic mode on mobile');
  console.log('   5. Verify animation controls are touch-friendly');
  console.log('   6. Test landscape/portrait orientations');
  
  // Exit with appropriate code
  process.exit(successRate === 100 ? 0 : 1);
}

// Run if called directly
main().catch(error => {
  console.error('❌ Mobile functionality testing failed:', error);
  process.exit(1);
});

export { main };
