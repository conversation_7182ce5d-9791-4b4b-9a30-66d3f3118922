const { test, expect } = require('@playwright/test');

test('Debug and fix vehicle marker positioning', async ({ page }) => {
  // Set up logging
  page.on('console', msg => {
    console.log(`BROWSER LOG: ${msg.text()}`);
  });
  
  page.on('pageerror', error => {
    console.error(`PAGE ERROR: ${error.message}`);
  });

  // Navigate to the application
  await page.goto('http://localhost:8080/');
  console.log('Navigated to application');
  
  // Wait for map to load
  await page.waitForSelector('.mapboxgl-canvas', { state: 'visible', timeout: 30000 });
  console.log('Map loaded');
  
  // Inject diagnostic and repair code
  await page.evaluate(() => {
    // Store original functions for later examination
    const originalFunctions = {};
    
    // Capture errors
    window.errorLogs = [];
    window.addEventListener('error', (event) => {
      window.errorLogs.push({
        message: event.message,
        source: event.filename,
        lineno: event.lineno,
        timestamp: new Date().toISOString()
      });
    });
    
    // Helper function to debug map elements
    window.debugMapElements = () => {
      console.log('Debugging map elements...');
      const mapContainer = document.querySelector('.mapboxgl-map');
      if (!mapContainer) {
        console.error('Map container not found!');
        return;
      }
      
      console.log('Map dimensions:', {
        width: mapContainer.offsetWidth,
        height: mapContainer.offsetHeight,
        style: window.getComputedStyle(mapContainer)
      });
      
      // Check for direct vehicle marker
      const vehicleMarker = document.getElementById('direct-vehicle-marker');
      if (vehicleMarker) {
        console.log('Vehicle marker found:', {
          position: {
            left: vehicleMarker.style.left,
            top: vehicleMarker.style.top,
            transform: vehicleMarker.style.transform
          },
          dimensions: {
            width: vehicleMarker.offsetWidth,
            height: vehicleMarker.offsetHeight
          },
          visibility: vehicleMarker.style.visibility,
          display: vehicleMarker.style.display,
          parent: vehicleMarker.parentElement ? vehicleMarker.parentElement.tagName : 'none'
        });
      } else {
        console.error('Vehicle marker not found!');
        
        // Look for any other markers
        const markers = document.querySelectorAll('.marker');
        console.log(`Found ${markers.length} generic markers`);
        
        markers.forEach((marker, i) => {
          console.log(`Marker ${i}:`, {
            id: marker.id,
            class: marker.className,
            position: {
              left: marker.style.left,
              top: marker.style.top
            }
          });
        });
      }
    };
    
    // Function to attempt to fix the vehicle marker
    window.fixVehicleMarker = () => {
      const vehicleMarker = document.getElementById('direct-vehicle-marker');
      if (!vehicleMarker) {
        console.error('Cannot fix - vehicle marker not found!');
        return false;
      }
      
      console.log('Attempting to fix vehicle marker...');
      
      // Backup original styles
      const originalStyles = {
        position: vehicleMarker.style.position,
        left: vehicleMarker.style.left,
        top: vehicleMarker.style.top,
        transform: vehicleMarker.style.transform,
        zIndex: vehicleMarker.style.zIndex,
        visibility: vehicleMarker.style.visibility,
        opacity: vehicleMarker.style.opacity
      };
      
      // Set position to absolute
      vehicleMarker.style.position = 'absolute';
      
      // Ensure high z-index
      vehicleMarker.style.zIndex = '1000';
      
      // Ensure visibility
      vehicleMarker.style.visibility = 'visible';
      vehicleMarker.style.opacity = '1';
      
      // Check if the marker has moved from origin point (0,0)
      if (vehicleMarker.style.left === '0px' && vehicleMarker.style.top === '0px') {
        console.log('Marker stuck at origin, attempting to reposition...');
        
        // Try to get map container dimensions
        const mapContainer = document.querySelector('.mapboxgl-map');
        if (mapContainer) {
          // Position in the middle of the map
          vehicleMarker.style.left = `${mapContainer.offsetWidth / 2}px`;
          vehicleMarker.style.top = `${mapContainer.offsetHeight / 2}px`;
          console.log('Repositioned marker to center of map');
        }
      }
      
      // Check if marker is using transform for positioning
      if (vehicleMarker.style.transform && vehicleMarker.style.transform.includes('translate')) {
        console.log('Marker uses transform for positioning');
        
        // Extract translation values
        const match = vehicleMarker.style.transform.match(/translate\(([^,]+),\s*([^)]+)\)/);
        if (match) {
          const x = match[1];
          const y = match[2];
          
          console.log(`Transform values: x=${x}, y=${y}`);
          
          // If using %, convert to pixels
          if (x.includes('%') || y.includes('%')) {
            console.log('Converting % to px values...');
            
            const mapContainer = document.querySelector('.mapboxgl-map');
            if (mapContainer) {
              const mapWidth = mapContainer.offsetWidth;
              const mapHeight = mapContainer.offsetHeight;
              
              const xPercent = parseFloat(x);
              const yPercent = parseFloat(y);
              
              if (!isNaN(xPercent) && !isNaN(yPercent)) {
                vehicleMarker.style.left = `${(mapWidth * xPercent / 100)}px`;
                vehicleMarker.style.top = `${(mapHeight * yPercent / 100)}px`;
                vehicleMarker.style.transform = 'none';
                
                console.log('Converted transform to absolute positioning');
              }
            }
          }
        }
      }
      
      // Add visual marker to identify it more easily
      vehicleMarker.style.outline = '3px solid red';
      vehicleMarker.style.boxShadow = '0 0 10px #ff0000';
      
      // Log results of fix
      console.log('Fix applied. New styles:', {
        position: vehicleMarker.style.position,
        left: vehicleMarker.style.left,
        top: vehicleMarker.style.top,
        transform: vehicleMarker.style.transform,
        zIndex: vehicleMarker.style.zIndex
      });
      
      // Return original styles for reference
      return {
        fixed: true,
        originalStyles,
        newStyles: {
          position: vehicleMarker.style.position,
          left: vehicleMarker.style.left,
          top: vehicleMarker.style.top,
          transform: vehicleMarker.style.transform,
          zIndex: vehicleMarker.style.zIndex
        }
      };
    };
    
    // Create a monitoring function to track marker changes
    window.startMarkerMonitoring = () => {
      window.markerPositions = [];
      
      window.markerMonitorInterval = setInterval(() => {
        const vehicleMarker = document.getElementById('direct-vehicle-marker');
        if (vehicleMarker) {
          const position = {
            time: new Date().toISOString(),
            left: vehicleMarker.style.left,
            top: vehicleMarker.style.top,
            transform: vehicleMarker.style.transform,
            visible: vehicleMarker.style.visibility === 'visible' && 
                    vehicleMarker.style.display !== 'none' &&
                    parseFloat(vehicleMarker.style.opacity || 1) > 0
          };
          
          window.markerPositions.push(position);
          
          // Check if position has changed from previous
          const previousPosition = window.markerPositions.length > 1 ? 
            window.markerPositions[window.markerPositions.length - 2] : null;
            
          if (previousPosition && 
              previousPosition.left === position.left && 
              previousPosition.top === position.top &&
              previousPosition.transform === position.transform) {
            console.log('WARNING: Marker position not changing!', position);
          }
        } else {
          console.log('Marker not found during monitoring');
        }
      }, 1000);
      
      return 'Marker monitoring started';
    };
    
    window.stopMarkerMonitoring = () => {
      if (window.markerMonitorInterval) {
        clearInterval(window.markerMonitorInterval);
        return {
          stopped: true,
          positions: window.markerPositions || []
        };
      }
      return { stopped: false };
    };
    
    console.log('Diagnostic and repair functions injected');
  });
  
  // Debug map elements initially
  await page.evaluate(() => window.debugMapElements());
  
  // Start marker monitoring
  await page.evaluate(() => window.startMarkerMonitoring());
  
  // Try to find and click the Continue Journey button
  try {
    // First try to find specific continue journey button
    let continueButton = await page.$('button:has-text("Continue Journey")');
    
    if (!continueButton) {
      // Try more generic buttons
      continueButton = await page.$('button:has-text("Continue"), button:has-text("Start"), button:has-text("Begin")');
    }
    
    if (continueButton) {
      await continueButton.click();
      console.log('Clicked continue journey button');
      
      // Take a screenshot after starting
      await page.screenshot({ path: 'tests/journey-started.png' });
    } else {
      console.log('No continue journey button found');
      
      // Take a screenshot to see what's on screen
      await page.screenshot({ path: 'tests/no-continue-button.png' });
    }
  } catch (error) {
    console.error('Error clicking continue button:', error);
  }
  
  // Wait for animation to potentially start
  await page.waitForTimeout(5000);
  
  // Debug map elements again after starting
  await page.evaluate(() => window.debugMapElements());
  
  // Check if the vehicle marker exists
  const markerExists = await page.evaluate(() => {
    return !!document.getElementById('direct-vehicle-marker');
  });
  
  if (markerExists) {
    console.log('Vehicle marker found. Taking screenshot before fix...');
    await page.screenshot({ path: 'tests/before-fix.png' });
    
    // Apply fix to vehicle marker
    const fixResult = await page.evaluate(() => window.fixVehicleMarker());
    console.log('Fix result:', fixResult);
    
    // Take screenshot after fix
    await page.screenshot({ path: 'tests/after-fix.png' });
    
    // Monitor after fix
    await page.waitForTimeout(15000);
    
    // Debug map elements again
    await page.evaluate(() => window.debugMapElements());
    
    // Take final screenshot
    await page.screenshot({ path: 'tests/final-state.png' });
  } else {
    console.log('Vehicle marker not found. Searching DOM for potential markers...');
    
    // Look for any elements that might be the marker
    const potentialMarkers = await page.evaluate(() => {
      // Look for elements with names or classes that might be the vehicle marker
      const candidates = [];
      
      // Check for elements with "vehicle" in class, id, or name
      document.querySelectorAll('*').forEach(el => {
        if (
          (el.id && (el.id.includes('vehicle') || el.id.includes('marker'))) ||
          (el.className && (el.className.includes('vehicle') || el.className.includes('marker'))) ||
          (el.getAttribute('name') && (el.getAttribute('name').includes('vehicle') || el.getAttribute('name').includes('marker')))
        ) {
          candidates.push({
            tagName: el.tagName,
            id: el.id,
            className: el.className,
            position: {
              left: el.style.left,
              top: el.style.top,
              transform: el.style.transform
            },
            isVisible: el.style.display !== 'none' && el.style.visibility !== 'hidden',
            bbox: el.getBoundingClientRect()
          });
        }
      });
      
      return candidates;
    });
    
    console.log('Potential vehicle markers found:', potentialMarkers);
    
    // Try to create a new marker if none found
    if (potentialMarkers.length === 0) {
      console.log('No potential markers found. Creating a dummy marker for testing...');
      
      await page.evaluate(() => {
        const map = document.querySelector('.mapboxgl-map');
        if (map) {
          // Create a new marker
          const newMarker = document.createElement('div');
          newMarker.id = 'direct-vehicle-marker';
          newMarker.className = 'marker vehicle-marker';
          newMarker.style.position = 'absolute';
          newMarker.style.width = '30px';
          newMarker.style.height = '30px';
          newMarker.style.backgroundColor = 'red';
          newMarker.style.borderRadius = '50%';
          newMarker.style.zIndex = '1000';
          newMarker.style.left = '50%';
          newMarker.style.top = '50%';
          newMarker.style.transform = 'translate(-50%, -50%)';
          
          // Add to map
          map.appendChild(newMarker);
          
          console.log('Created test marker');
          return true;
        }
        return false;
      });
      
      // Take screenshot with dummy marker
      await page.screenshot({ path: 'tests/dummy-marker.png' });
    }
  }
  
  // Stop monitoring and collect data
  const monitoringData = await page.evaluate(() => window.stopMarkerMonitoring());
  console.log(`Monitoring stopped. Collected ${monitoringData.positions?.length || 0} position points`);
  
  // Get any errors that occurred
  const errors = await page.evaluate(() => window.errorLogs || []);
  if (errors.length > 0) {
    console.log('Errors detected during test:', errors);
  }
  
  console.log('Debug test complete');
}); 