const { test, expect } = require('@playwright/test');

test('Analyze vehicle bearing and animation issues', async ({ page }) => {
  // Set up logging
  page.on('console', msg => {
    const text = msg.text();
    // Filter for bearing-related logs
    if (text.includes('bearing') || text.includes('rotation') || text.includes('vehicle position update')) {
      console.log(`BEARING LOG: ${text}`);
    } else {
      console.log(`BROWSER LOG: ${text}`);
    }
  });
  
  page.on('pageerror', error => {
    console.error(`PAGE ERROR: ${error.message}`);
  });

  // Navigate to the application
  await page.goto('http://localhost:8080/');
  console.log('Navigated to application');
  
  // Wait for map to load
  await page.waitForSelector('.mapboxgl-canvas', { state: 'visible', timeout: 30000 });
  console.log('Map loaded');
  
  // Take an initial screenshot
  await page.screenshot({ path: 'tests/initial-state.png' });
  
  // Inject monitoring code
  await page.evaluate(() => {
    // Store all bearing-related logs
    window.bearingLogs = [];
    
    // Original console.log
    const originalConsoleLog = console.log;
    
    // Override console.log to capture bearing logs
    console.log = function(...args) {
      // Call original console.log
      originalConsoleLog.apply(console, args);
      
      // Check if log is related to bearing
      const logStr = args.join(' ');
      if (logStr.includes('bearing') || logStr.includes('rotation') || logStr.includes('vehicle position update')) {
        window.bearingLogs.push({
          timestamp: new Date().toISOString(),
          message: logStr
        });
      }
    };
    
    // Function to debug vehicle bearing
    window.analyzeBearing = () => {
      const vehicleMarker = document.getElementById('direct-vehicle-marker');
      if (!vehicleMarker) {
        console.error('Vehicle marker not found for bearing analysis');
        return null;
      }
      
      // Get computed style
      const style = window.getComputedStyle(vehicleMarker);
      
      // Extract transform properties
      const transform = style.transform || style.webkitTransform;
      
      // Try to parse rotation from transform
      let rotation = null;
      if (transform && transform !== 'none') {
        const matches = transform.match(/rotate(?:Z|3d\(0,\s*0,\s*1,)?\(([^)]+)\)/);
        if (matches && matches.length > 1) {
          rotation = matches[1];
          console.log(`Detected rotation: ${rotation}`);
        }
      }
      
      // Check for custom data attributes that might store bearing
      const dataAttrs = {};
      Array.from(vehicleMarker.attributes).forEach(attr => {
        if (attr.name.startsWith('data-')) {
          dataAttrs[attr.name] = attr.value;
        }
      });
      
      return {
        id: vehicleMarker.id,
        className: vehicleMarker.className,
        transform,
        rotation,
        dataAttributes: dataAttrs,
        style: {
          transform: vehicleMarker.style.transform,
          transition: vehicleMarker.style.transition,
          willChange: vehicleMarker.style.willChange
        }
      };
    };
    
    // Monkey patch any DirectVehicleMarker rendering
    window.monitorComponentProps = () => {
      // Check if React DevTools extension is available
      if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
        let vehicleMarkerInstance = null;
        
        // Store logs of prop updates
        window.propUpdateLogs = [];
        
        // Find component instance
        try {
          window.__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberRoot = (function(originalFunction) {
            return function(...args) {
              const fiberRoot = args[1];
              
              // Walk the fiber tree to find DirectVehicleMarker component
              const findComponent = (fiber) => {
                if (!fiber) return null;
                
                // Check if this is the DirectVehicleMarker component
                if (
                  fiber.type && 
                  fiber.type.name === 'DirectVehicleMarker' || 
                  (fiber.elementType && fiber.elementType.name === 'DirectVehicleMarker')
                ) {
                  return fiber;
                }
                
                // Check children
                if (fiber.child) {
                  const result = findComponent(fiber.child);
                  if (result) return result;
                }
                
                // Check siblings
                if (fiber.sibling) {
                  const result = findComponent(fiber.sibling);
                  if (result) return result;
                }
                
                return null;
              };
              
              const vehicleMarkerFiber = findComponent(fiberRoot.current);
              
              if (vehicleMarkerFiber) {
                vehicleMarkerInstance = vehicleMarkerFiber;
                
                // Log props
                const props = vehicleMarkerFiber.memoizedProps;
                window.propUpdateLogs.push({
                  timestamp: new Date().toISOString(),
                  bearing: props.bearing,
                  position: props.position,
                  isTracking: props.isTracking,
                });
                
                console.log('DirectVehicleMarker props update:', {
                  bearing: props.bearing,
                  position: props.position,
                });
              }
              
              return originalFunction.apply(this, args);
            };
          })(window.__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberRoot);
          
          console.log('React component monitoring enabled');
        } catch (error) {
          console.error('Error setting up React component monitoring:', error);
        }
      } else {
        console.log('React DevTools hook not found - cannot monitor component props directly');
      }
    };
    
    // Start monitoring for bearing changes via DOM mutations
    window.startBearingMonitoring = () => {
      window.bearingValues = [];
      
      // Create a mutation observer to watch for style changes
      const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
          if (mutation.type === 'attributes' && 
              (mutation.attributeName === 'style' || 
               mutation.attributeName === 'transform' || 
               mutation.attributeName.includes('data-'))) {
            
            const element = mutation.target;
            
            // Check if this is the vehicle marker
            if (element.id === 'direct-vehicle-marker' || 
                (element.className && element.className.includes('vehicle'))) {
              
              // Analyze current bearing
              const analysis = window.analyzeBearing();
              
              if (analysis) {
                window.bearingValues.push({
                  timestamp: new Date().toISOString(),
                  ...analysis
                });
                
                console.log('Vehicle marker style change detected:', {
                  transform: analysis.transform,
                  rotation: analysis.rotation
                });
              }
            }
          }
        });
      });
      
      // Start observing
      observer.observe(document.body, {
        attributes: true,
        attributeFilter: ['style', 'transform', 'class'],
        attributeOldValue: true,
        subtree: true
      });
      
      // Store observer reference
      window.bearingObserver = observer;
      
      console.log('Bearing monitoring started');
      return 'Bearing monitoring started';
    };
    
    // Apply experimental fixes to test various bearing implementations
    window.testBearingImplementations = () => {
      const vehicleMarker = document.getElementById('direct-vehicle-marker');
      if (!vehicleMarker) {
        console.error('Vehicle marker not found for bearing tests');
        return false;
      }
      
      // Save original styles
      const originalStyles = {
        transform: vehicleMarker.style.transform,
        transition: vehicleMarker.style.transition
      };
      
      // Test different bearing implementations
      const implementations = [
        {
          name: 'CSS transform with transition',
          apply: () => {
            vehicleMarker.style.transform = 'rotate(45deg)';
            vehicleMarker.style.transition = 'transform 1s ease-out';
            
            // After 2 seconds, update to a new bearing
            setTimeout(() => {
              vehicleMarker.style.transform = 'rotate(90deg)';
            }, 2000);
          }
        },
        {
          name: 'Hardware accelerated transform',
          apply: () => {
            vehicleMarker.style.willChange = 'transform';
            vehicleMarker.style.transform = 'rotate3d(0, 0, 1, 135deg)';
            vehicleMarker.style.transition = 'transform 1s ease-out';
            
            // After 2 seconds, update to a new bearing
            setTimeout(() => {
              vehicleMarker.style.transform = 'rotate3d(0, 0, 1, 180deg)';
            }, 2000);
          }
        },
        {
          name: 'Transform origin test',
          apply: () => {
            vehicleMarker.style.transformOrigin = 'center center';
            vehicleMarker.style.transform = 'rotate(225deg)';
            vehicleMarker.style.transition = 'transform 1s ease-out';
            
            // After 2 seconds, update to a new bearing
            setTimeout(() => {
              vehicleMarker.style.transform = 'rotate(270deg)';
            }, 2000);
          }
        },
        {
          name: 'Normalized bearing test',
          apply: () => {
            // Normalize a bearing value (e.g., 370 degrees -> 10 degrees)
            const normalizedBearing = (370 % 360);
            vehicleMarker.style.transform = `rotate(${normalizedBearing}deg)`;
            vehicleMarker.style.transition = 'transform 1s ease-out';
            
            console.log(`Applied normalized bearing: ${normalizedBearing}deg (from 370deg)`);
            
            // After 2 seconds, try a negative bearing
            setTimeout(() => {
              const negativeBearing = -90;
              const normalizedNegative = ((negativeBearing % 360) + 360) % 360;
              vehicleMarker.style.transform = `rotate(${normalizedNegative}deg)`;
              console.log(`Applied normalized negative bearing: ${normalizedNegative}deg (from ${negativeBearing}deg)`);
            }, 2000);
          }
        }
      ];
      
      // Initialize tracking
      window.implementationResults = [];
      
      // Run tests sequentially
      const runImplementation = (index) => {
        if (index >= implementations.length) {
          // Restore original styles when done
          vehicleMarker.style.transform = originalStyles.transform;
          vehicleMarker.style.transition = originalStyles.transition;
          vehicleMarker.style.willChange = 'auto';
          
          console.log('Finished testing all bearing implementations');
          return;
        }
        
        const implementation = implementations[index];
        console.log(`Testing bearing implementation: ${implementation.name}`);
        
        // Apply this implementation
        implementation.apply();
        
        // Capture initial state after applying
        const initialState = window.analyzeBearing();
        
        // Wait 3 seconds, then capture final state
        setTimeout(() => {
          const finalState = window.analyzeBearing();
          
          // Store results
          window.implementationResults.push({
            name: implementation.name,
            initialState,
            finalState,
            success: initialState && finalState && initialState.rotation !== finalState.rotation
          });
          
          console.log(`Completed test: ${implementation.name}`, {
            success: initialState && finalState && initialState.rotation !== finalState.rotation,
            initialRotation: initialState?.rotation,
            finalRotation: finalState?.rotation
          });
          
          // Run next implementation after a delay
          setTimeout(() => runImplementation(index + 1), 1000);
        }, 3000);
      };
      
      // Start running implementations
      runImplementation(0);
      
      return 'Started bearing implementation tests';
    };
    
    // Stop bearing monitoring
    window.stopBearingMonitoring = () => {
      if (window.bearingObserver) {
        window.bearingObserver.disconnect();
        console.log('Bearing monitoring stopped');
        
        return {
          bearingLogs: window.bearingLogs || [],
          bearingValues: window.bearingValues || [],
          propUpdateLogs: window.propUpdateLogs || [],
          implementationResults: window.implementationResults || []
        };
      }
      
      return { stopped: false };
    };
    
    console.log('Bearing analysis functions injected');
  });
  
  // Start monitoring
  await page.evaluate(() => {
    window.monitorComponentProps();
    window.startBearingMonitoring();
  });
  
  // Try to find and click the Continue Journey button
  try {
    // First try to find specific continue journey button
    let continueButton = await page.$('button:has-text("Continue Journey")');
    
    if (!continueButton) {
      // Try more generic buttons
      continueButton = await page.$('button:has-text("Continue"), button:has-text("Start"), button:has-text("Begin")');
    }
    
    if (continueButton) {
      await continueButton.click();
      console.log('Clicked continue journey button');
      
      // Take a screenshot after starting
      await page.screenshot({ path: 'tests/journey-started-bearing.png' });
    } else {
      console.log('No continue journey button found');
      
      // Take a screenshot to see what's on screen
      await page.screenshot({ path: 'tests/no-continue-button-bearing.png' });
    }
  } catch (error) {
    console.error('Error clicking continue button:', error);
  }
  
  // Wait for animation to potentially start
  await page.waitForTimeout(5000);
  
  // Take a screenshot after waiting
  await page.screenshot({ path: 'tests/after-wait.png' });
  
  // Get initial bearing
  const initialBearing = await page.evaluate(() => window.analyzeBearing());
  console.log('Initial bearing analysis:', initialBearing);
  
  // Run bearing tests
  await page.evaluate(() => window.testBearingImplementations());
  
  // Wait for the bearing tests to complete (about 16 seconds total)
  await page.waitForTimeout(16000);
  
  // Take screenshots after tests
  await page.screenshot({ path: 'tests/after-bearing-tests.png' });
  
  // Stop monitoring and get results
  const monitoringData = await page.evaluate(() => window.stopBearingMonitoring());
  
  // Analyze the logs
  if (monitoringData.bearingLogs && monitoringData.bearingLogs.length > 0) {
    console.log(`Collected ${monitoringData.bearingLogs.length} bearing logs`);
    
    // Find logs specifically about normalizedBearing
    const normalizedBearingLogs = monitoringData.bearingLogs.filter(log => 
      log.message.includes('normalizedBearing'));
    
    if (normalizedBearingLogs.length > 0) {
      console.log(`Found ${normalizedBearingLogs.length} logs mentioning normalizedBearing`);
      console.log('Sample normalizedBearing logs:', normalizedBearingLogs.slice(0, 3));
    } else {
      console.log('No logs found mentioning normalizedBearing');
    }
    
    // Find logs with bearing but not normalizedBearing
    const bearingOnlyLogs = monitoringData.bearingLogs.filter(log => 
      log.message.includes('bearing') && !log.message.includes('normalizedBearing'));
      
    if (bearingOnlyLogs.length > 0) {
      console.log(`Found ${bearingOnlyLogs.length} logs mentioning bearing but not normalizedBearing`);
      console.log('Sample bearing logs:', bearingOnlyLogs.slice(0, 3));
    }
  }
  
  // Analyze test results
  if (monitoringData.implementationResults && monitoringData.implementationResults.length > 0) {
    console.log('Bearing implementation test results:');
    
    monitoringData.implementationResults.forEach(result => {
      console.log(`- ${result.name}: ${result.success ? 'SUCCESS' : 'FAILED'}`);
      console.log(`  Initial rotation: ${result.initialState?.rotation}, Final rotation: ${result.finalState?.rotation}`);
    });
    
    // Find the most successful implementation
    const successfulImplementations = monitoringData.implementationResults.filter(r => r.success);
    if (successfulImplementations.length > 0) {
      console.log('Recommended implementation:', successfulImplementations[0].name);
    }
  }
  
  // Analyze component props
  if (monitoringData.propUpdateLogs && monitoringData.propUpdateLogs.length > 0) {
    console.log(`Collected ${monitoringData.propUpdateLogs.length} component prop updates`);
    
    // Check bearing prop values
    const bearingProps = monitoringData.propUpdateLogs.map(log => log.bearing);
    const uniqueBearings = [...new Set(bearingProps)];
    
    console.log('Unique bearing values from props:', uniqueBearings);
    
    // Check if bearing values changed
    if (uniqueBearings.length > 1) {
      console.log('Bearing props changed during monitoring');
    } else {
      console.log('Bearing props did not change during monitoring');
    }
  }
  
  console.log('Bearing analysis test complete');
}); 