const { test, expect } = require('@playwright/test');

test('Record vehicle movement on Marrakech to Tangier route', async ({ page }) => {
  // Enable verbose console logging
  page.on('console', msg => {
    console.log(`BROWSER LOG: ${msg.text()}`);
  });

  // Log network requests for debugging
  page.on('request', request => {
    console.log(`>> Request: ${request.method()} ${request.url()}`);
  });

  // Log any errors that occur
  page.on('pageerror', error => {
    console.error(`PAGE ERROR: ${error.message}`);
  });

  // Navigate to the application
  await page.goto('http://localhost:8080/');
  console.log('Navigated to application');
  
  // Wait for page to load completely
  await page.waitForLoadState('networkidle');
  
  // Take a screenshot of the initial state
  await page.screenshot({ path: 'tests/initial-state.png' });
  
  // Wait for map to load (assuming there's some element that indicates map is ready)
  await page.waitForSelector('.mapboxgl-canvas', { state: 'visible', timeout: 30000 });
  console.log('Map loaded');
  
  // Choose Marrakech to Tangier route (adjust selector based on your UI)
  // This is a generic approach - you'll need to adapt to your specific UI
  try {
    // Look for route selection UI elements
    const routeButtons = await page.$$('.route-selection-button, button:has-text("Marrakech"), button:has-text("Tangier")');
    
    if (routeButtons.length > 0) {
      console.log(`Found ${routeButtons.length} route buttons`);
      await routeButtons[0].click();
      console.log('Clicked route button');
    } else {
      // If no specific route buttons, try to find a generic start journey button
      await page.click('button:has-text("Start"), button:has-text("Journey"), button:has-text("Continue")');
      console.log('Clicked generic journey button');
    }
  } catch (error) {
    console.error('Error selecting route:', error);
    // Take screenshot of the error state
    await page.screenshot({ path: 'tests/route-selection-error.png' });
  }
  
  // Wait for animation to start (adjust timing as needed)
  console.log('Waiting for animation to start...');
  await page.waitForTimeout(5000);
  
  // Take a screenshot after animation starts
  await page.screenshot({ path: 'tests/animation-started.png' });
  
  // Look for the vehicle marker DOM element
  const vehicleMarkerInfo = await page.evaluate(() => {
    const markerEl = document.getElementById('direct-vehicle-marker');
    if (markerEl) {
      return {
        found: true,
        position: {
          left: markerEl.style.left,
          top: markerEl.style.top,
          transform: markerEl.style.transform,
          display: markerEl.style.display,
          visibility: markerEl.style.visibility,
          zIndex: markerEl.style.zIndex,
        }
      };
    }
    return { found: false };
  });
  
  console.log('Vehicle marker DOM info:', vehicleMarkerInfo);
  
  // Record the animation for 30 seconds
  console.log('Recording animation for 30 seconds...');
  for (let i = 0; i < 6; i++) {
    await page.waitForTimeout(5000); // Take a screenshot every 5 seconds
    await page.screenshot({ path: `tests/animation-${i}.png` });
    
    // Check vehicle marker position
    const position = await page.evaluate(() => {
      const marker = document.getElementById('direct-vehicle-marker');
      return marker ? { left: marker.style.left, top: marker.style.top } : null;
    });
    
    console.log(`Vehicle position at ${i*5}s:`, position);
  }
  
  // Get final console logs
  const logs = await page.evaluate(() => {
    return {
      errors: window.console.error.calls,
      logs: window.console.log.calls
    };
  }).catch(e => console.log('Could not retrieve console logs:', e));
  
  console.log('Recording completed');
  
  // Final screenshot
  await page.screenshot({ path: 'tests/final-state.png' });
}); 