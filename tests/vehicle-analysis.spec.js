import { test, expect } from '@playwright/test';
import fs from 'fs';
import path from 'path';

// Increase the test timeout to 120 seconds to handle longer observation periods
test.setTimeout(120000);

test('Vehicle marker position analysis', async ({ page }) => {
  // Create results directory if it doesn't exist
  const resultsDir = 'test-results';
  if (!fs.existsSync(resultsDir)) {
    fs.mkdirSync(resultsDir, { recursive: true });
  }
  
  // Setup listener for console messages to capture vehicle positions
  const consoleMessages = [];
  const vehiclePositions = [];
  
  page.on('console', message => {
    const text = message.text();
    consoleMessages.push({
      type: message.type(),
      text: text,
      timestamp: new Date().toISOString()
    });
    
    // Print city selection related logs to the console for debugging
    if (text.includes('city') || text.includes('destination') || text.includes('select')) {
      console.log(`APP LOG: ${text}`);
    }
    
    // Enhanced position tracking - look for different position-related log patterns
    if (text.includes('Vehicle visibility check') || 
        text.includes('Vehicle position:') || 
        text.includes('marker position') ||
        text.includes('Current position:')) {
      try {
        // Try to extract coordinates using regex if available
        const coordMatch = text.match(/lat[:\s]+(-?\d+\.\d+)[\s,]+lon[:\s]+(-?\d+\.\d+)/i) || 
                          text.match(/\[(-?\d+\.\d+),\s*(-?\d+\.\d+)\]/);
        
        const position = {
          timestamp: new Date().toISOString(),
          message: text,
          rawData: text
        };
        
        if (coordMatch) {
          position.lat = parseFloat(coordMatch[1]);
          position.lng = parseFloat(coordMatch[2]);
        }
        
        vehiclePositions.push(position);
      } catch (err) {
        console.log('Error parsing vehicle position:', err);
      }
    }
  });
  
  // Navigate to the application with increased timeout
  try {
    await page.goto('http://localhost:8086', { timeout: 60000 });
    console.log('Navigated to application successfully');
  } catch (error) {
    console.error('Failed to navigate to application:', error);
    // Still continue the test to collect any available data
  }
  
  // Take initial screenshot 
  await page.screenshot({ path: path.join(resultsDir, 'initial-page.png') });
  console.log('Initial screenshot captured');
  
  // Wait for the app to fully load and initialize
  await page.waitForTimeout(5000);
  console.log('Waited for initial app load');
  
  // Check if map is present
  const mapSelectors = ['.mapboxgl-canvas-container', '.mapboxgl-map', '.map', '#map'];
  let mapFound = false;
  
  for (const selector of mapSelectors) {
    try {
      const isVisible = await page.isVisible(selector, { timeout: 5000 });
      if (isVisible) {
        console.log(`Found map using selector: ${selector}`);
        mapFound = true;
        break;
    }
  } catch (error) {
      // Continue to next selector
    }
  }
  
  if (!mapFound) {
    console.log('Map not found with known selectors');
  }
  
  // Select destinations first to make the Explore button appear
  await selectCityDestinations(page, resultsDir);

  // Debug HTML structure
  await page.screenshot({ path: path.join(resultsDir, 'before-button-search.png') });
  const html = await page.content();
  fs.writeFileSync(path.join(resultsDir, 'page-structure.html'), html);
  
  // Enhanced function to find journey button with better selectors
  const findJourneyButton = async () => {
    console.log('Looking for journey button...');
    
    // Capture what buttons are present for debugging
    const allButtons = await page.$$('button, [role="button"], .button, a.btn');
    console.log(`Found ${allButtons.length} button-like elements`);

    // Log button texts for debugging
    for (let i = 0; i < Math.min(allButtons.length, 10); i++) {
      try {
        const text = await allButtons[i].innerText();
        const className = await allButtons[i].getAttribute('class');
        console.log(`Button ${i+1}: "${text}" (class: ${className})`);
      } catch (e) {
        // Ignore errors
      }
    }
    
    // Try different text variations - UPDATED to include "explore" instead of "Begin Adventure"
    const buttonTexts = [
      'explore', 'ex→plore', 'travelz.ai', 'Explore',
      'Begin', 'Start', 'Go', 'Journey', 'Adventure'
    ];
    
    // Try with exact and partial text matching
    for (const text of buttonTexts) {
      // Try text selectors with lower confidence first
      try {
        console.log(`Looking for button with text: "${text}"`);
        
        // Try with getByText - non-strict to find any element containing the text
        const buttonText = page.getByText(text, { exact: false });
        if (await buttonText.isVisible({ timeout: 1000 })) {
          console.log(`Found element with text: "${text}"`);
          return { element: buttonText, text };
        }
        
        // Try CSS selectors
        for (const selector of [
          `button:has-text("${text}")`,
          `[role="button"]:has-text("${text}")`,
          `.button:has-text("${text}")`,
          `.explore-button`,
          `.journey-button`,
          `.controls-button`,
          `text="${text}"`,
          `a:has-text("${text}")`,
          `div:has-text("${text}")`,
          `span:has-text("${text}")`
        ]) {
          try {
            const element = page.locator(selector, { strict: false });
            const count = await element.count();
            if (count > 0) {
              console.log(`Found ${count} elements with selector: ${selector}`);
              const isVisible = await element.first().isVisible();
              if (isVisible) {
                console.log(`Element with selector "${selector}" is visible`);
                return { element: element.first(), text, selector };
              }
            }
          } catch (e) {
            // Move to next selector
          }
        }
      } catch (e) {
        console.log(`Error searching for "${text}" button:`, e.message);
      }
    }
    
    // If no button found with text, try looking for all buttons and inspect them
    try {
      const allVisibleButtons = await page.$$('button:visible, [role="button"]:visible, .button:visible');
      console.log(`Found ${allVisibleButtons.length} visible button-like elements`);
      
      if (allVisibleButtons.length > 0) {
        // Return the first visible button as a fallback
        const buttonText = await allVisibleButtons[0].innerText();
        console.log(`Using first visible button with text "${buttonText}" as fallback`);
        return { element: allVisibleButtons[0], text: buttonText };
      }
    } catch (e) {
      console.log('Error finding visible buttons:', e.message);
    }
    
    return { element: null };
  };
  
  // Take a screenshot to see what might be blocking clicks
  await page.screenshot({ path: path.join(resultsDir, 'before-button-click.png'), fullPage: true });
  
  // Try to find and click the journey button
  console.log('Searching for journey button...');
  const { element, text } = await findJourneyButton();
  
  let buttonClicked = false;
  let journeyStarted = false;
  if (element) {
    console.log(`Found button with text: "${text}"`);
    
    // Get button position for screenshot highlighting
    try {
      const box = await element.boundingBox();
      if (box) {
        // Take screenshot with highlighted button area
        await page.screenshot({ 
          path: path.join(resultsDir, 'button-highlighted.png')
        });
        console.log(`Button found at position: x=${box.x}, y=${box.y}, width=${box.width}, height=${box.height}`);
      }
    } catch (e) {
      console.log('Could not get button bounding box:', e.message);
    }
    
    // Click the button with multiple attempts and force if needed
    for (let attempt = 1; attempt <= 3 && !buttonClicked; attempt++) {
      try {
        console.log(`Button click attempt ${attempt}...`);
        
        if (attempt === 1) {
          // First try normal click
          await element.click({ timeout: 5000 });
        } else {
          // Try with force and increased timeout
          await element.click({ force: true, timeout: 10000 });
        }
        
        console.log(`Successfully clicked button on attempt ${attempt}`);
        buttonClicked = true;
        journeyStarted = true;
        
        // Take screenshot after click
        await page.screenshot({ path: path.join(resultsDir, `after-button-click-${attempt}.png`) });
      } catch (error) {
        console.error(`Failed to click button on attempt ${attempt}:`, error.message);
        
        // If clicking failed, try to debug what's in the way
        try {
          const point = await element.boundingBox();
          if (point) {
            const x = point.x + point.width / 2;
            const y = point.y + point.height / 2;
            
            // Use evaluate to find what element is at that position
            const elementAtPoint = await page.evaluate(({ x, y }) => {
              const el = document.elementFromPoint(x, y);
              return el ? {
                tagName: el.tagName,
                id: el.id,
                className: el.className,
                innerHTML: el.innerHTML.substring(0, 100)
              } : null;
            }, { x, y });
            
            console.log('Element at button position:', elementAtPoint);
          }
        } catch (e) {
          console.log('Error getting element at position:', e.message);
        }
        
        // If last attempt, try JS click
        if (attempt === 3 && !buttonClicked) {
          try {
            console.log('Trying JavaScript click as last resort');
            await element.evaluate(el => el.click());
            console.log('JavaScript click successful');
            buttonClicked = true;
            journeyStarted = true;
            await page.screenshot({ path: path.join(resultsDir, 'after-js-click.png') });
          } catch (jsError) {
            console.error('JavaScript click failed:', jsError.message);
          }
        }
      }
    }
  } else {
    console.log('No journey button found, will observe passively');
  }
  
  // Observe for a period of time to capture vehicle movement
  console.log('Observing vehicle movement...');
  
  // If the journey started, we'll observe longer for better data collection
  // Using a shorter observation time to avoid timeout issues
  const totalObservationTime = journeyStarted ? 60000 : 30000; // 60 seconds if journey started, otherwise 30
  const checkInterval = 5000; // Check every 5 seconds
  const checks = totalObservationTime / checkInterval;
  
  for (let i = 0; i < checks; i++) {
    await page.waitForTimeout(checkInterval);
    console.log(`Observation check ${i+1}/${checks}`);
    
    // Look for vehicle marker
    try {
      const vehicleMarkerSelectors = [
        '.vehicle-marker', 
        '[data-testid="vehicle-marker"]',
        '[class*="vehicle"]',
        'div[id="direct-vehicle-marker"]',
        '#direct-vehicle-marker'
      ];
      
      for (const selector of vehicleMarkerSelectors) {
        try {
          const isVisible = await page.isVisible(selector, { timeout: 1000 });
          if (isVisible) {
            console.log(`Vehicle marker found with selector: ${selector}`);
            await page.screenshot({ path: path.join(resultsDir, `vehicle-marker-found-${i+1}.png`) });
            break;
          }
        } catch (e) {
          // Continue to next selector
        }
      }
    } catch (error) {
      console.log('Error checking for vehicle marker:', error.message);
    }
  }
  
  // Take final screenshot
  await page.screenshot({ path: path.join(resultsDir, 'final-state.png') });
  
  // Process collected data
  console.log(`Captured ${vehiclePositions.length} vehicle position updates`);
  console.log(`Collected ${consoleMessages.length} total console messages`);
  
  // Filter relevant logs
  const relevantLogs = consoleMessages.filter(msg => 
    msg.text.includes('vehicle') || 
    msg.text.includes('animation') || 
    msg.text.includes('route') ||
    msg.text.includes('marker') ||
    msg.text.includes('city') ||
    msg.text.includes('destination')
  );
  
  // Calculate metrics
  let totalDistance = 0;
  let avgSpeed = 0;
  
  if (vehiclePositions.length > 1) {
    // Calculate actual distance if coordinates are available
    const positionsWithCoords = vehiclePositions.filter(p => p.lat && p.lng);
    
    if (positionsWithCoords.length > 1) {
      // Calculate distance between each consecutive point
      for (let i = 1; i < positionsWithCoords.length; i++) {
        const prev = positionsWithCoords[i-1];
        const curr = positionsWithCoords[i];
        
        // Haversine formula for distance calculation
        const R = 6371; // Earth radius in km
        const dLat = (curr.lat - prev.lat) * Math.PI / 180;
        const dLng = (curr.lng - prev.lng) * Math.PI / 180;
        const a = 
          Math.sin(dLat/2) * Math.sin(dLat/2) +
          Math.cos(prev.lat * Math.PI / 180) * Math.cos(curr.lat * Math.PI / 180) * 
          Math.sin(dLng/2) * Math.sin(dLng/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        totalDistance += R * c;
      }
      
      // Calculate average speed
      const firstTimestamp = new Date(positionsWithCoords[0].timestamp).getTime();
      const lastTimestamp = new Date(positionsWithCoords[positionsWithCoords.length-1].timestamp).getTime();
      const duration = (lastTimestamp - firstTimestamp) / 3600000; // hours
      
      if (duration > 0) {
        avgSpeed = totalDistance / duration;
      }
    } else {
      // Fallback to estimation if no coordinates
      totalDistance = vehiclePositions.length * 0.05; // Simple estimation
      avgSpeed = totalDistance / (totalObservationTime / 3600000); // km/h
    }
  }
  
  // Write test results
  const results = {
    positions: vehiclePositions,
    relevantLogs,
    metrics: {
      totalPositions: vehiclePositions.length,
      positionsWithCoordinates: vehiclePositions.filter(p => p.lat && p.lng).length,
      totalDistance: totalDistance.toFixed(2) + ' km',
      averageSpeed: avgSpeed.toFixed(2) + ' km/h'
    },
    testSummary: {
      mapFound,
      buttonFound: element !== null,
      buttonText: text || null,
      buttonClicked,
      journeyStarted,
      totalObservationTime: `${totalObservationTime/1000} seconds`,
      timestamp: new Date().toISOString()
    }
  };
  
  fs.writeFileSync(
    path.join(resultsDir, 'vehicle-analysis.json'),
    JSON.stringify(results, null, 2)
  );
  
  // Final HTML for debugging
  const finalHtml = await page.content();
  fs.writeFileSync(path.join(resultsDir, 'page-final.html'), finalHtml);
  
  // Simple assertion
  expect(true).toBeTruthy();
});

// Helper function to select destinations by clicking city buttons directly
async function selectCityDestinations(page, resultsDir) {
  console.log('Attempting to select destinations by clicking city buttons...');
  
  try {
    // Wait for buttons to be available
    await page.waitForTimeout(2000);
    
    // First try to find city buttons by text
    const cityNames = ['Marrakech', 'Fes', 'Casablanca', 'Chefchaouen', 'Tangier'];
    let citiesSelected = 0;
    
    for (const cityName of cityNames) {
      if (citiesSelected >= 2) break; // We only need 2 cities
      
      try {
        // Take a screenshot before attempting to click
        await page.screenshot({ path: path.join(resultsDir, `before-${cityName}-click.png`) });
        
        // Try with text - non-strict to avoid multiple element errors
        const buttonText = page.getByText(cityName, { exact: false });
        
        if (await buttonText.isVisible({ timeout: 1000 })) {
          await buttonText.first().click({ force: true });
          console.log(`Selected ${cityName} by clicking city text`);
          citiesSelected++;
          await page.waitForTimeout(500);
          await page.screenshot({ path: path.join(resultsDir, `after-${cityName}-click.png`) });
          continue;
        }
        
        // Try with button selector
        const buttonSelector = page.locator(`button:has-text("${cityName}")`, { strict: false });
        if (await buttonSelector.isVisible({ timeout: 1000 })) {
          await buttonSelector.first().click({ force: true });
          console.log(`Selected ${cityName} by clicking city selector`);
          citiesSelected++;
          await page.waitForTimeout(500);
          await page.screenshot({ path: path.join(resultsDir, `after-${cityName}-click.png`) });
          continue;
        }
        
        // Last resort - try any element with this city name
        const anyElement = page.locator(`:text("${cityName}")`, { strict: false });
        if (await anyElement.isVisible({ timeout: 1000 })) {
          await anyElement.first().click({ force: true });
          console.log(`Selected ${cityName} by clicking any element with text`);
          citiesSelected++;
          await page.waitForTimeout(500);
          await page.screenshot({ path: path.join(resultsDir, `after-${cityName}-click.png`) });
        }
      } catch (e) {
        console.log(`Failed to select ${cityName}: ${e.message}`);
      }
    }
    
    if (citiesSelected === 0) {
      // Fall back to the older method if we couldn't select any cities with buttons
      await selectDestinations(page, resultsDir);
  } else {
      console.log(`Successfully selected ${citiesSelected} destinations using city buttons`);
    }
    
    // Wait for the UI to update
    await page.waitForTimeout(3000);
    
  } catch (error) {
    console.error('Error during city destination selection:', error.message);
    // Fall back to map markers if button selection fails
    await selectDestinations(page, resultsDir);
  }
}

// Original helper function to select destinations by map markers
async function selectDestinations(page, resultsDir) {
  console.log('Attempting to select destinations from map markers...');
  
  try {
    // Look for destination markers
    const destinationSelectors = [
      '.city-marker', 
      '[data-testid="city-marker"]',
      '[class*="destination"]',
      '.mapboxgl-marker'
    ];
    
    for (const selector of destinationSelectors) {
      const markers = await page.$$(selector);
      console.log(`Found ${markers.length} potential destination markers with selector: ${selector}`);
      
      // If we found markers, try to click on at least 2 of them
      if (markers.length >= 2) {
        // Take a screenshot before selecting
        await page.screenshot({ path: path.join(resultsDir, 'before-destination-selection.png') });
        
        // Click on the first marker
        try {
          await markers[0].click({ force: true });
          console.log('Clicked on first destination marker');
          await page.waitForTimeout(1000);
          await page.screenshot({ path: path.join(resultsDir, 'after-first-destination.png') });
        } catch (e) {
          console.log('Failed to click first destination:', e.message);
        }
        
        // Click on the second marker
        try {
          await markers[Math.min(1, markers.length - 1)].click({ force: true });
          console.log('Clicked on second destination marker');
          await page.waitForTimeout(1000);
          await page.screenshot({ path: path.join(resultsDir, 'after-second-destination.png') });
        } catch (e) {
          console.log('Failed to click second destination:', e.message);
        }
        
        console.log('Destination selection completed');
        break;
      }
    }
    
    // Wait for the UI to update after destination selection
    await page.waitForTimeout(3000);
    
  } catch (error) {
    console.error('Error during destination selection:', error.message);
  }
} 