import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";
import { VitePWA } from "vite-plugin-pwa";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current working directory.
  // Set the third parameter to '' to load all env regardless of the `VITE_` prefix.
  const env = loadEnv(mode, process.cwd(), '');

  return {
    server: {
      host: "::",
      port: 8080,
    },
    plugins: [
      react(),
      mode === 'development' &&
      componentTagger(),
      VitePWA({
        registerType: 'autoUpdate',
        includeAssets: ['favicon.ico', 'apple-touch-icon.png', 'mask-icon.svg'],
        manifest: {
          name: 'CometoMorocco Travel App',
          short_name: 'CometoMorocco',
          description: 'Interactive travel experiences for Morocco.',
          theme_color: '#ffffff',
          background_color: '#ffffff',
          display: 'standalone',
          scope: '/',
          start_url: '/',
          icons: [
            {
              src: 'pwa-192x192.png',
              sizes: '192x192',
              type: 'image/png',
            },
            {
              src: 'pwa-512x512.png',
              sizes: '512x512',
              type: 'image/png',
            },
            {
              src: 'pwa-512x512.png',
              sizes: '512x512',
              type: 'image/png',
              purpose: 'any maskable',
            },
          ],
        },
        workbox: {
          maximumFileSizeToCacheInBytes: 5 * 1024 * 1024, // 5 MB
          // Skip waiting and claim clients immediately for faster updates
          skipWaiting: true,
          clientsClaim: true,
          // Don't cache the index.html to avoid routing issues
          navigateFallback: null,
          // More aggressive cache invalidation
          cleanupOutdatedCaches: true,
          // Exclude problematic routes from caching
          navigateFallbackDenylist: [/^\/moroccan-demo/, /^\/api/],
          runtimeCaching: [
            {
              urlPattern: /^https:\/\/api\.mapbox\.com\/.*/i,
              handler: 'CacheFirst',
              options: {
                cacheName: 'mapbox-cache',
                expiration: {
                  maxEntries: 10,
                  maxAgeSeconds: 60 * 60 * 24 * 30, // 30 days
                },
              },
            },
          ],
        }
      }),
    ].filter(Boolean),
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
        "@/types": path.resolve(__dirname, "./src/types"),
        "@/components": path.resolve(__dirname, "./src/components"),
        "@/utils": path.resolve(__dirname, "./src/utils"),
        "@/config": path.resolve(__dirname, "./src/config")
      },
    },
    define: {
      // Do not override environment variables, let them be read naturally
      // from the .env file without replacing them
    },
    optimizeDeps: {
      exclude: [
        // Add problematic dependencies here
        "chunk-T2WPM7RV",
        "chunk-EURAPOLQ",
        "chunk-OHWCMTQP",
        "chunk-4FOOX3DN"
      ],
      // Force include common dependencies to avoid chunk issues
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        'mapbox-gl',
        '@tanstack/react-query'
      ]
    },
    build: {
      target: 'es2020',
      sourcemap: true,
      rollupOptions: {
        output: {
          manualChunks: (id) => {
            // Vendor chunks for large dependencies
            if (id.includes('node_modules')) {
              if (id.includes('react') || id.includes('react-dom')) {
                return 'react-vendor';
              }
              if (id.includes('mapbox-gl')) {
                return 'mapbox-vendor';
              }
              if (id.includes('@radix-ui')) {
                return 'ui-vendor';
              }
              if (id.includes('lucide-react') || id.includes('react-icons')) {
                return 'icons-vendor';
              }
              if (id.includes('@tanstack/react-query')) {
                return 'query-vendor';
              }
              // Other vendor dependencies
              return 'vendor';
            }

            // Application chunks
            if (id.includes('src/components/map/animation')) {
              return 'animation';
            }
            if (id.includes('src/components/ui')) {
              return 'ui-components';
            }
            if (id.includes('src/hooks')) {
              return 'hooks';
            }
            if (id.includes('src/contexts')) {
              return 'contexts';
            }
            if (id.includes('src/utils')) {
              return 'utils';
            }

            // Don't create separate chunks for types (they're compile-time only)
            // Don't create separate chunks for small files
          }
        }
      }
    }
  };
});
